# Autoresize Input - 自动调整大小输入框

## 概述

`autoresize_input.js` 实现了一个自动调整大小的输入框组件，能够根据内容长度自动调整输入框的宽度或高度。该组件在 Odoo 邮件系统中用于提供更好的用户输入体验，特别适用于需要动态调整大小的文本输入场景。

## 文件信息
- **路径**: `/mail/static/src/core/common/autoresize_input.js`
- **行数**: 63
- **模块**: `@mail/core/common/autoresize_input`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架组件和钩子
'@web/core/utils/autoresize'   // 自动调整大小工具
```

## 组件定义

### AutoresizeInput 类

继承自 OWL Component 的自动调整大小输入框组件：

```javascript
class AutoresizeInput extends Component {
    static template = "mail.AutoresizeInput";
}
```

## Props 配置

### Props 定义

```javascript
static props = {
    autofocus: { type: Boolean, optional: true },    // 自动聚焦
    className: { type: String, optional: true },     // CSS 类名
    enabled: { optional: true },                     // 是否启用
    onValidate: { type: Function, optional: true },  // 验证回调
    placeholder: { type: String, optional: true },   // 占位符文本
    value: { type: String, optional: true },         // 输入值
};
```

### 默认属性

```javascript
static defaultProps = {
    autofocus: false,      // 默认不自动聚焦
    className: "",         // 默认无额外类名
    enabled: true,         // 默认启用
    onValidate: () => {},  // 默认空验证函数
    placeholder: "",       // 默认无占位符
};
```

### Props 详细说明

- **`autofocus`**: 组件挂载后是否自动聚焦到输入框
- **`className`**: 添加到输入框的额外 CSS 类名
- **`enabled`**: 控制输入框是否可用（禁用状态）
- **`onValidate`**: 输入验证完成时的回调函数
- **`placeholder`**: 输入框的占位符文本
- **`value`**: 输入框的当前值

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 状态管理
    this.state = useState({
        value: this.props.value,
    });
    
    // DOM 引用
    this.inputRef = useRef("input");
    
    // Props 变化监听
    onWillUpdateProps((nextProps) => {
        if (this.props.value !== nextProps.value) {
            this.state.value = nextProps.value;
        }
    });
    
    // 自动调整大小功能
    useAutoresize(this.inputRef);
    
    // 挂载后处理
    onMounted(() => {
        if (this.props.autofocus) {
            this.inputRef.el.focus();
            this.inputRef.el.setSelectionRange(-1, -1);
        }
    });
}
```

### 核心功能

#### 1. 状态管理
```javascript
this.state = useState({
    value: this.props.value,
});
```
- 使用响应式状态管理输入值
- 初始值来自 props

#### 2. 自动调整大小
```javascript
useAutoresize(this.inputRef);
```
- 使用 Web 核心的自动调整大小工具
- 根据内容自动调整输入框尺寸

#### 3. Props 同步
```javascript
onWillUpdateProps((nextProps) => {
    if (this.props.value !== nextProps.value) {
        this.state.value = nextProps.value;
    }
});
```
- 监听 props 变化
- 同步外部值变化到内部状态

#### 4. 自动聚焦
```javascript
onMounted(() => {
    if (this.props.autofocus) {
        this.inputRef.el.focus();
        this.inputRef.el.setSelectionRange(-1, -1);
    }
});
```
- 组件挂载后自动聚焦（如果启用）
- 设置光标位置到末尾

## 事件处理

### onKeydownInput() - 键盘事件处理

```javascript
onKeydownInput(ev) {
    switch (ev.key) {
        case "Enter":
            this.inputRef.el.blur();
            break;
        case "Escape":
            this.state.value = this.props.value;
            this.inputRef.el.blur();
            break;
    }
}
```

#### 键盘快捷键

- **Enter 键**: 
  - 失去焦点，完成输入
  - 通常触发验证或保存操作

- **Escape 键**:
  - 恢复到原始值（取消编辑）
  - 失去焦点，退出编辑模式

## 使用场景

### 1. 内联编辑
```xml
<AutoresizeInput 
    value="t-att-value"
    onValidate="() => this.saveValue()"
    placeholder="点击编辑..."
    autofocus="true"
/>
```

### 2. 动态标题编辑
```xml
<AutoresizeInput 
    value="thread.name"
    className="thread-title-input"
    placeholder="输入线程标题"
    onValidate="(value) => this.updateThreadName(value)"
/>
```

### 3. 快速输入框
```xml
<AutoresizeInput 
    value="state.searchTerm"
    placeholder="搜索..."
    enabled="!state.loading"
    className="search-input"
/>
```

## 设计模式

### 1. 受控组件模式 (Controlled Component)
- 通过 props 接收外部值
- 通过回调函数通知值变化

### 2. 组合模式 (Composition Pattern)
- 使用 `useAutoresize` Hook 组合自动调整功能
- 分离关注点，提高复用性

### 3. 事件驱动模式 (Event-Driven Pattern)
- 通过键盘事件驱动状态变化
- 支持标准的编辑交互模式

## 特性优势

### 1. 自适应尺寸
- 根据内容长度自动调整大小
- 提供更好的视觉体验

### 2. 键盘友好
- 支持 Enter 确认和 Escape 取消
- 符合用户操作习惯

### 3. 灵活配置
- 丰富的 props 配置选项
- 适应不同使用场景

### 4. 状态同步
- 自动同步外部状态变化
- 保持数据一致性

## 技术实现

### 自动调整大小原理

`useAutoresize` Hook 的工作原理：
1. 监听输入框的 `input` 事件
2. 创建隐藏的测量元素
3. 复制输入框的样式到测量元素
4. 设置测量元素的内容为当前输入值
5. 获取测量元素的尺寸
6. 将尺寸应用到输入框

### 焦点管理

```javascript
// 自动聚焦并设置光标位置
this.inputRef.el.focus();
this.inputRef.el.setSelectionRange(-1, -1);
```

### 状态恢复

```javascript
// Escape 键恢复原始值
case "Escape":
    this.state.value = this.props.value;
    this.inputRef.el.blur();
    break;
```

## 注意事项

1. **性能考虑**: 频繁的尺寸计算可能影响性能，建议节流处理
2. **样式继承**: 确保测量元素正确继承输入框样式
3. **浏览器兼容**: 某些老版本浏览器可能不支持部分 API
4. **内存泄漏**: 及时清理事件监听器和 DOM 引用

## 扩展建议

1. **最小/最大尺寸**: 添加尺寸限制配置
2. **动画效果**: 为尺寸变化添加平滑动画
3. **多行支持**: 扩展支持 textarea 的自动调整
4. **验证状态**: 添加输入验证和错误状态显示
5. **国际化**: 支持不同语言的文本测量

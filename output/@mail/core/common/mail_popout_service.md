# Mail Popout Service - 邮件弹出窗口服务

## 概述

`mail_popout_service.js` 实现了 Odoo 邮件系统中的弹出窗口服务，允许将邮件界面元素弹出到独立的浏览器窗口中。该服务提供了窗口管理、元素迁移、状态同步等功能，为用户提供更灵活的多窗口邮件体验，是现代邮件应用中重要的用户界面增强功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/mail_popout_service.js`
- **行数**: 95
- **模块**: `@mail/core/common/mail_popout_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'    // 服务注册表
'@odoo/owl'            // OWL 框架（响应式）
```

## 服务定义

### mailPopoutService 对象

```javascript
const mailPopoutService = {
    dependencies: ["ui"],
    start(env, { ui }) {
        // 服务实现
    },
};
```

### 服务依赖

- **`ui`**: UI 服务，用于触发界面事件

## 核心变量

### 内部状态

```javascript
const anchor = document.createElement("div");    // 锚点元素，标记原始位置
let externalWindow;                              // 外部弹出窗口引用
let currentReplacement;                          // 当前被移动的元素
```

### 变量说明

- **`anchor`**: DOM 锚点，用于标记元素的原始位置
- **`externalWindow`**: 弹出窗口的 Window 对象引用
- **`currentReplacement`**: 当前在弹出窗口中的元素

## 核心功能

### 1. 重置功能

```javascript
function reset() {
    // 将元素从外部窗口移回原位置
    if (externalWindow?.document?.body?.firstChild && anchor.isConnected) {
        anchor.after(externalWindow.document.body.firstChild);
    }
    
    // 清空外部窗口内容
    if (externalWindow) {
        externalWindow.document.body = externalWindow.document.createElement("body");
    }
    
    // 重置当前替换元素
    currentReplacement = null;
}
```

**重置流程**:
1. 检查外部窗口和锚点的有效性
2. 将元素从外部窗口移回原始位置
3. 清空外部窗口的内容
4. 重置内部状态

### 2. 窗口关闭检测

```javascript
async function pollClosedWindow() {
    while (externalWindow) {
        await new Promise((r) => setTimeout(r, 1000));
        if (externalWindow.closed) {
            reset();
            externalWindow = null;
            ui.bus.trigger("resize");
        }
    }
}
```

**检测机制**:
- 每秒检查一次窗口状态
- 检测到窗口关闭时自动重置
- 触发主窗口的 resize 事件
- 清理窗口引用

### 3. 弹出功能

```javascript
function popout(element, triggerResize = true) {
    // 创建或获取外部窗口
    if (!externalWindow || externalWindow.closed) {
        externalWindow = window.open("about:blank", "_blank", "popup=yes");
        
        // 监听主窗口关闭事件
        window.addEventListener("beforeunload", () => {
            if (externalWindow && !externalWindow.closed) {
                externalWindow.close();
            }
        });
        
        // 开始轮询窗口状态
        pollClosedWindow();
        
        // 复制样式到外部窗口
        externalWindow.document.write(window.document.head.outerHTML);
    }
    
    // 处理元素迁移
    if (element !== currentReplacement) {
        reset();
        if (element) {
            currentReplacement = element;
            element.after(anchor);                    // 在原位置放置锚点
            externalWindow.document.body.append(element);  // 移动到外部窗口
        }
    }
    
    // 触发 resize 事件
    if (triggerResize) {
        ui.bus.trigger("resize");
    }
    
    return externalWindow;
}
```

**弹出流程**:
1. 检查并创建外部窗口
2. 设置窗口关闭监听
3. 复制样式到外部窗口
4. 处理元素迁移
5. 触发界面更新事件

## 服务接口

### 响应式接口

```javascript
return reactive({
    get externalWindow() {
        return externalWindow && externalWindow.closed ? null : externalWindow;
    },
    popout,
    reset,
});
```

### 接口说明

- **`externalWindow`**: 获取当前有效的外部窗口
- **`popout(element, triggerResize)`**: 弹出元素到外部窗口
- **`reset()`**: 重置服务状态

## 使用场景

### 1. 聊天窗口弹出

```javascript
// 将聊天窗口弹出到独立窗口
const popoutChatWindow = (chatWindowElement) => {
    const popoutService = env.services['mail.popout'];
    const externalWindow = popoutService.popout(chatWindowElement);
    
    // 设置窗口标题
    externalWindow.document.title = '聊天窗口';
    
    // 监听窗口事件
    externalWindow.addEventListener('focus', () => {
        console.log('弹出窗口获得焦点');
    });
};
```

### 2. 消息编辑器弹出

```javascript
// 弹出消息编辑器
const popoutComposer = (composerElement) => {
    const popoutService = env.services['mail.popout'];
    
    // 弹出编辑器
    const externalWindow = popoutService.popout(composerElement);
    
    // 设置窗口属性
    externalWindow.document.title = '消息编辑器';
    externalWindow.resizeTo(800, 600);
    externalWindow.moveTo(100, 100);
};
```

### 3. 附件查看器弹出

```javascript
// 弹出附件查看器
const popoutAttachmentViewer = (viewerElement) => {
    const popoutService = env.services['mail.popout'];
    
    const externalWindow = popoutService.popout(viewerElement, false);
    
    // 全屏显示
    externalWindow.document.title = '附件查看器';
    externalWindow.focus();
};
```

### 4. 多窗口管理

```javascript
// 管理多个弹出窗口
class PopoutManager {
    constructor() {
        this.popoutService = env.services['mail.popout'];
        this.activePopouts = new Map();
    }
    
    popout(id, element, options = {}) {
        // 如果已有弹出，先关闭
        if (this.activePopouts.has(id)) {
            this.close(id);
        }
        
        const externalWindow = this.popoutService.popout(element);
        this.activePopouts.set(id, {
            window: externalWindow,
            element: element,
            options: options
        });
        
        return externalWindow;
    }
    
    close(id) {
        const popout = this.activePopouts.get(id);
        if (popout) {
            this.popoutService.reset();
            this.activePopouts.delete(id);
        }
    }
    
    closeAll() {
        this.popoutService.reset();
        this.activePopouts.clear();
    }
}
```

## 窗口管理

### 1. 窗口创建配置

```javascript
// 自定义窗口创建
const createCustomPopout = (element, config = {}) => {
    const {
        width = 800,
        height = 600,
        left = 100,
        top = 100,
        resizable = true,
        scrollbars = true
    } = config;
    
    const features = [
        'popup=yes',
        `width=${width}`,
        `height=${height}`,
        `left=${left}`,
        `top=${top}`,
        `resizable=${resizable ? 'yes' : 'no'}`,
        `scrollbars=${scrollbars ? 'yes' : 'no'}`
    ].join(',');
    
    // 修改服务以支持自定义配置
    const externalWindow = window.open("about:blank", "_blank", features);
    return externalWindow;
};
```

### 2. 窗口状态监控

```javascript
// 监控窗口状态
const monitorPopoutWindow = (externalWindow) => {
    const monitor = {
        isActive: !externalWindow.closed,
        lastActivity: Date.now(),
        
        checkStatus() {
            this.isActive = !externalWindow.closed;
            if (this.isActive) {
                this.lastActivity = Date.now();
            }
            return this.isActive;
        },
        
        getInactiveTime() {
            return Date.now() - this.lastActivity;
        }
    };
    
    // 定期检查
    const interval = setInterval(() => {
        if (!monitor.checkStatus()) {
            clearInterval(interval);
        }
    }, 1000);
    
    return monitor;
};
```

### 3. 窗口通信

```javascript
// 主窗口与弹出窗口通信
const setupWindowCommunication = (externalWindow) => {
    // 从主窗口发送消息到弹出窗口
    const sendToPopout = (message) => {
        if (externalWindow && !externalWindow.closed) {
            externalWindow.postMessage(message, '*');
        }
    };
    
    // 监听弹出窗口的消息
    const handlePopoutMessage = (event) => {
        if (event.source === externalWindow) {
            console.log('收到弹出窗口消息:', event.data);
        }
    };
    
    window.addEventListener('message', handlePopoutMessage);
    
    return {
        send: sendToPopout,
        cleanup: () => {
            window.removeEventListener('message', handlePopoutMessage);
        }
    };
};
```

## 样式同步

### 1. 样式复制

```javascript
// 复制样式到弹出窗口
const copyStylesToPopout = (externalWindow) => {
    // 复制所有样式表
    const styleSheets = Array.from(document.styleSheets);
    
    styleSheets.forEach(sheet => {
        try {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = sheet.href;
            externalWindow.document.head.appendChild(link);
        } catch (e) {
            // 处理跨域样式表
            console.warn('无法复制样式表:', sheet.href);
        }
    });
    
    // 复制内联样式
    const inlineStyles = document.querySelectorAll('style');
    inlineStyles.forEach(style => {
        const newStyle = externalWindow.document.createElement('style');
        newStyle.textContent = style.textContent;
        externalWindow.document.head.appendChild(newStyle);
    });
};
```

### 2. 动态样式更新

```javascript
// 监听主窗口样式变化并同步到弹出窗口
const syncDynamicStyles = (externalWindow) => {
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'childList' && 
                mutation.target === document.head) {
                
                mutation.addedNodes.forEach(node => {
                    if (node.tagName === 'STYLE' || 
                        (node.tagName === 'LINK' && node.rel === 'stylesheet')) {
                        
                        const clone = node.cloneNode(true);
                        externalWindow.document.head.appendChild(clone);
                    }
                });
            }
        });
    });
    
    observer.observe(document.head, {
        childList: true,
        subtree: true
    });
    
    return observer;
};
```

## 性能优化

### 1. 窗口复用

```javascript
// 复用弹出窗口
const reusePopoutWindow = (() => {
    let cachedWindow = null;
    
    return (element) => {
        if (cachedWindow && !cachedWindow.closed) {
            // 清空现有内容
            cachedWindow.document.body.innerHTML = '';
            // 添加新元素
            cachedWindow.document.body.appendChild(element);
            return cachedWindow;
        } else {
            // 创建新窗口
            cachedWindow = popoutService.popout(element);
            return cachedWindow;
        }
    };
})();
```

### 2. 内存清理

```javascript
// 清理弹出窗口资源
const cleanupPopoutResources = () => {
    // 清理事件监听器
    const listeners = getPopoutListeners();
    listeners.forEach(listener => {
        window.removeEventListener(listener.type, listener.handler);
    });
    
    // 清理定时器
    const timers = getPopoutTimers();
    timers.forEach(timer => {
        clearInterval(timer);
        clearTimeout(timer);
    });
    
    // 清理观察器
    const observers = getPopoutObservers();
    observers.forEach(observer => {
        observer.disconnect();
    });
};
```

## 错误处理

### 1. 窗口创建失败

```javascript
// 处理窗口创建失败
const safePopout = (element) => {
    try {
        const externalWindow = popoutService.popout(element);
        
        if (!externalWindow) {
            throw new Error('弹出窗口创建失败');
        }
        
        return externalWindow;
    } catch (error) {
        console.error('弹出窗口错误:', error);
        
        // 回退到模态对话框
        showModalDialog(element);
        return null;
    }
};
```

### 2. 元素迁移错误

```javascript
// 安全的元素迁移
const safeMoveElement = (element, targetWindow) => {
    try {
        // 检查元素是否仍在DOM中
        if (!element.isConnected) {
            throw new Error('元素已从DOM中移除');
        }
        
        // 检查目标窗口是否有效
        if (!targetWindow || targetWindow.closed) {
            throw new Error('目标窗口无效');
        }
        
        // 执行迁移
        targetWindow.document.body.appendChild(element);
        
    } catch (error) {
        console.error('元素迁移失败:', error);
        // 恢复元素到原位置
        restoreElementPosition(element);
    }
};
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 确保只有一个弹出窗口服务实例
- 管理全局的弹出窗口状态

### 2. 观察者模式 (Observer Pattern)
- 监听窗口关闭事件
- 响应窗口状态变化

### 3. 代理模式 (Proxy Pattern)
- 响应式接口代理窗口状态
- 透明的状态访问

## 注意事项

1. **浏览器兼容性**: 确保弹出窗口功能的浏览器支持
2. **弹窗拦截**: 处理浏览器的弹窗拦截机制
3. **内存泄漏**: 及时清理窗口引用和事件监听器
4. **用户体验**: 提供清晰的弹出窗口操作反馈

## 扩展建议

1. **多窗口支持**: 支持同时管理多个弹出窗口
2. **窗口持久化**: 保存和恢复窗口状态
3. **自定义配置**: 支持更多窗口创建选项
4. **窗口通信**: 增强主窗口与弹出窗口的通信
5. **移动端适配**: 在移动设备上提供替代方案

该服务为邮件系统提供了灵活的多窗口体验，增强了用户界面的可用性和工作效率。

# Settings Model - 设置模型

## 概述

`settings_model.js` 定义了 Odoo 邮件系统中的设置模型类，用于管理用户的各种偏好设置。该模型涵盖了通知设置、音频视频设置、RTC通话设置等功能，支持本地存储和服务器同步，是用户个性化体验的核心数据结构。

## 文件信息
- **路径**: `/mail/static/src/core/common/settings_model.js`
- **行数**: 348
- **模块**: `@mail/core/common/settings_model`

## 依赖关系

```javascript
// Web 核心依赖
'@web/core/l10n/translation'    // 国际化
'@web/core/utils/strings'       // 字符串工具
'@web/core/browser/browser'     // 浏览器工具
'@web/core/utils/timing'        // 时间工具
'@web/core/network/rpc'         // RPC 网络请求

// 邮件系统依赖
'@mail/core/common/record'      // Record 基类
```

## 类定义

### Settings 类

```javascript
class Settings extends Record {
    id;  // 设置ID
    
    setup() {
        super.setup();
        // 初始化防抖保存
        // 检查浏览器支持
        // 加载本地设置
    }
}
```

## 核心设置分类

### 1. 通知设置

#### 频道通知级别

```javascript
channel_notifications = Record.attr("mentions", {
    compute() {
        return this.channel_notifications === false ? "mentions" : this.channel_notifications;
    },
});
```

**通知级别**:
- `"all"`: 所有消息
- `"mentions"`: 仅提及
- `"no_notif"`: 无通知

#### 静音设置

```javascript
mute_until_dt = Record.attr(false, { type: "datetime" });
```

**功能**:
- 设置静音截止时间
- 支持临时静音功能

#### 通知选项

```javascript
get NOTIFICATIONS() {
    return [
        {
            label: "all",
            name: _t("All Messages"),
        },
        {
            label: "mentions", 
            name: _t("Mentions Only"),
        },
        {
            label: "no_notif",
            name: _t("Nothing"),
        },
    ];
}
```

### 2. 音频设置

#### 音频设备

```javascript
audioInputDeviceId = "";        // 音频输入设备ID
```

#### 语音激活

```javascript
use_push_to_talk = false;       // 是否使用按键通话
voice_active_duration = 200;    // 语音激活持续时间
voiceActivationThreshold = 0.05; // 语音激活阈值 [0, 1]
push_to_talk_key;               // 按键通话快捷键
isRegisteringKey = false;       // 是否正在注册快捷键
```

#### 音量设置

```javascript
volumes = Record.many("Volume");           // 音量设置集合
volumeSettingsTimeouts = new Map();       // 音量设置超时
```

#### 音频约束

```javascript
get audioConstraints() {
    const constraints = {
        echoCancellation: true,     // 回声消除
        noiseSuppression: true,     // 噪声抑制
    };
    if (this.audioInputDeviceId) {
        constraints.deviceId = this.audioInputDeviceId;
    }
    return constraints;
}
```

### 3. 视频设置

#### 背景效果

```javascript
backgroundBlurAmount = 10;      // 背景模糊程度
edgeBlurAmount = 10;           // 边缘模糊程度
useBlur = false;               // 是否使用模糊效果
```

#### 显示设置

```javascript
showOnlyVideo = false;         // 是否只显示视频
```

#### 浏览器支持检测

```javascript
hasCanvasFilterSupport = typeof document.createElement("canvas")
    .getContext("2d").filter !== "undefined";
```

### 4. RTC设置

```javascript
logRtc = false;               // 是否记录RTC日志
```

## 静音选项

### 静音时长选项

```javascript
get MUTES() {
    return [
        {
            label: "15_mins",
            value: 15,
            name: _t("For 15 minutes"),
        },
        {
            label: "1_hour", 
            value: 60,
            name: _t("For 1 hour"),
        },
        {
            label: "3_hours",
            value: 180, 
            name: _t("For 3 hours"),
        },
        {
            label: "8_hours",
            value: 480,
            name: _t("For 8 hours"),
        },
        {
            label: "24_hours",
            value: 1440,
            name: _t("For 24 hours"),
        },
        {
            label: "forever",
            value: -1,
            name: _t("Until I turn it back on"),
        },
    ];
}
```

## 本地存储管理

### 1. 加载本地设置

```javascript
_loadLocalSettings() {
    // 从本地存储加载设置
    const voiceThreshold = browser.localStorage.getItem("mail_user_setting_voice_threshold");
    if (voiceThreshold) {
        this.voiceActivationThreshold = parseFloat(voiceThreshold);
    }
    
    // 加载其他本地设置...
}
```

### 2. 保存语音阈值

```javascript
saveVoiceThresholdDebounce = debounce(() => {
    browser.localStorage.setItem(
        "mail_user_setting_voice_threshold",
        this.voiceActivationThreshold.toString()
    );
}, 2000);
```

**防抖保存**:
- 2秒延迟保存
- 避免频繁写入本地存储
- 提高性能

### 3. 设置同步

```javascript
// 同步设置到服务器
async syncToServer() {
    await rpc('/mail/settings/update', {
        channel_notifications: this.channel_notifications,
        mute_until_dt: this.mute_until_dt,
        use_push_to_talk: this.use_push_to_talk,
        // 其他设置...
    });
}
```

## 使用场景

### 1. 通知设置界面

```javascript
// 渲染通知设置选项
const renderNotificationSettings = (settings) => {
    return settings.NOTIFICATIONS.map(option => ({
        label: option.label,
        name: option.name,
        selected: settings.channel_notifications === option.label,
        onChange: () => {
            settings.channel_notifications = option.label;
            settings.syncToServer();
        }
    }));
};
```

### 2. 音频设备选择

```javascript
// 获取可用音频设备
const getAudioDevices = async () => {
    const devices = await navigator.mediaDevices.enumerateDevices();
    return devices.filter(device => device.kind === 'audioinput');
};

// 设置音频设备
const setAudioDevice = (deviceId) => {
    settings.audioInputDeviceId = deviceId;
    settings.saveToLocal();
};
```

### 3. 语音激活设置

```javascript
// 设置语音激活阈值
const setVoiceThreshold = (threshold) => {
    settings.voiceActivationThreshold = threshold;
    settings.saveVoiceThresholdDebounce();
};

// 切换按键通话模式
const togglePushToTalk = () => {
    settings.use_push_to_talk = !settings.use_push_to_talk;
    settings.syncToServer();
};
```

### 4. 静音功能

```javascript
// 设置静音
const muteFor = (minutes) => {
    if (minutes === -1) {
        // 永久静音
        settings.mute_until_dt = null;
    } else {
        // 临时静音
        const muteUntil = new Date();
        muteUntil.setMinutes(muteUntil.getMinutes() + minutes);
        settings.mute_until_dt = muteUntil;
    }
    settings.syncToServer();
};

// 取消静音
const unmute = () => {
    settings.mute_until_dt = false;
    settings.syncToServer();
};
```

## 音量管理

### 1. 用户音量设置

```javascript
// 设置特定用户的音量
const setUserVolume = (userId, volume) => {
    let volumeRecord = settings.volumes.find(v => v.userId === userId);
    
    if (!volumeRecord) {
        volumeRecord = store.Volume.insert({
            userId: userId,
            volume: volume
        });
        settings.volumes.add(volumeRecord);
    } else {
        volumeRecord.volume = volume;
    }
    
    // 防抖保存
    clearTimeout(settings.volumeSettingsTimeouts.get(userId));
    settings.volumeSettingsTimeouts.set(userId, setTimeout(() => {
        settings.syncVolumeToServer(userId, volume);
    }, 1000));
};
```

### 2. 获取用户音量

```javascript
const getUserVolume = (userId) => {
    const volumeRecord = settings.volumes.find(v => v.userId === userId);
    return volumeRecord ? volumeRecord.volume : 1.0; // 默认音量
};
```

## 视频效果设置

### 1. 背景模糊

```javascript
// 启用背景模糊
const enableBackgroundBlur = (amount = 10) => {
    if (settings.hasCanvasFilterSupport) {
        settings.useBlur = true;
        settings.backgroundBlurAmount = amount;
        settings.syncToServer();
    } else {
        console.warn('Canvas filter not supported');
    }
};

// 禁用背景模糊
const disableBackgroundBlur = () => {
    settings.useBlur = false;
    settings.syncToServer();
};
```

### 2. 视频显示模式

```javascript
// 切换仅视频模式
const toggleVideoOnlyMode = () => {
    settings.showOnlyVideo = !settings.showOnlyVideo;
    settings.syncToServer();
};
```

## 快捷键管理

### 1. 注册按键通话快捷键

```javascript
// 开始注册快捷键
const startKeyRegistration = () => {
    settings.isRegisteringKey = true;
    
    const handleKeyDown = (event) => {
        if (settings.isRegisteringKey) {
            settings.push_to_talk_key = {
                code: event.code,
                key: event.key,
                ctrlKey: event.ctrlKey,
                altKey: event.altKey,
                shiftKey: event.shiftKey
            };
            settings.isRegisteringKey = false;
            document.removeEventListener('keydown', handleKeyDown);
            settings.syncToServer();
        }
    };
    
    document.addEventListener('keydown', handleKeyDown);
};
```

### 2. 检查快捷键匹配

```javascript
const isKeyMatch = (event, keyConfig) => {
    return event.code === keyConfig.code &&
           event.ctrlKey === keyConfig.ctrlKey &&
           event.altKey === keyConfig.altKey &&
           event.shiftKey === keyConfig.shiftKey;
};
```

## 设置验证

### 1. 音频设置验证

```javascript
const validateAudioSettings = (settings) => {
    const errors = [];
    
    if (settings.voiceActivationThreshold < 0 || settings.voiceActivationThreshold > 1) {
        errors.push('Voice activation threshold must be between 0 and 1');
    }
    
    if (settings.voice_active_duration < 0) {
        errors.push('Voice active duration must be positive');
    }
    
    return errors;
};
```

### 2. 视频设置验证

```javascript
const validateVideoSettings = (settings) => {
    const errors = [];
    
    if (settings.backgroundBlurAmount < 0 || settings.backgroundBlurAmount > 100) {
        errors.push('Background blur amount must be between 0 and 100');
    }
    
    return errors;
};
```

## 性能优化

### 1. 防抖保存

```javascript
// 防抖保存机制
const createDebouncedSave = (key, delay = 1000) => {
    return debounce((value) => {
        browser.localStorage.setItem(key, JSON.stringify(value));
    }, delay);
};
```

### 2. 批量更新

```javascript
// 批量更新设置
const batchUpdateSettings = (updates) => {
    Object.assign(settings, updates);
    settings.syncToServer();
};
```

### 3. 缓存机制

```javascript
// 缓存计算结果
let cachedAudioConstraints = null;
let lastAudioDeviceId = null;

get audioConstraints() {
    if (this.audioInputDeviceId !== lastAudioDeviceId) {
        cachedAudioConstraints = null;
        lastAudioDeviceId = this.audioInputDeviceId;
    }
    
    if (!cachedAudioConstraints) {
        cachedAudioConstraints = this.calculateAudioConstraints();
    }
    
    return cachedAudioConstraints;
}
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 全局唯一的设置实例
- 统一的设置管理入口

### 2. 观察者模式 (Observer Pattern)
- 监听设置变化
- 自动同步到服务器和本地存储

### 3. 策略模式 (Strategy Pattern)
- 不同设置类型的不同处理策略
- 本地存储 vs 服务器同步的策略

## 注意事项

1. **数据一致性**: 确保本地和服务器设置同步
2. **浏览器兼容性**: 检查API支持情况
3. **性能考虑**: 避免频繁的存储操作
4. **用户体验**: 提供即时的设置反馈

## 扩展建议

1. **设置导入导出**: 支持设置的备份和恢复
2. **设置模板**: 提供预设的设置模板
3. **高级音频**: 支持更多音频处理选项
4. **自定义主题**: 支持界面主题设置
5. **设置同步**: 跨设备的设置同步

该模型为邮件系统提供了完整的用户设置管理功能，支持丰富的个性化选项和高级的音视频配置，是用户体验定制的核心组件。

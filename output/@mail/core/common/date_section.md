# Date Section - 日期分段组件

## 概述

`date_section.js` 实现了日期分段显示组件，用于在 Odoo 邮件系统中按日期对消息或内容进行分组显示。该组件提供了清晰的时间分隔线，帮助用户更好地理解消息的时间顺序，并针对移动端进行了优化。

## 文件信息
- **路径**: `/mail/static/src/core/common/date_section.js`
- **行数**: 22
- **模块**: `@mail/core/common/date_section`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                              // OWL 框架组件基类
'@web/core/browser/feature_detection'    // 浏览器特性检测
```

## 组件定义

### DateSection 类

继承自 OWL Component 的日期分段组件：

```javascript
class DateSection extends Component {
    static template = "mail.DateSection";
    static props = ["date", "className?"];
}
```

## Props 配置

### Props 接口

```typescript
interface Props {
    date: string;        // 日期字符串（必需）
    className?: string;  // 自定义CSS类名（可选）
}
```

### Props 定义

```javascript
static props = [
    "date",       // 要显示的日期
    "className?"  // 可选的CSS类名
];
```

### Props 详细说明

- **`date`** (必需):
  - 类型: 字符串
  - 用途: 指定要显示的日期
  - 格式: 通常为 ISO 日期格式或本地化日期字符串
  - 示例: `"2024-01-15"`, `"Today"`, `"Yesterday"`

- **`className`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"compact-date"`, `"highlighted-date"`

## 核心功能

### 移动端检测

```javascript
get isMobileOS() {
    return isMobileOS();
}
```

**功能说明**:
- 检测当前设备是否为移动操作系统
- 用于在模板中应用不同的样式或布局
- 提供响应式的用户体验

## 使用场景

### 1. 消息列表分组

```xml
<div class="message-list">
    <t t-foreach="messagesByDate" t-as="dateGroup">
        <DateSection date="dateGroup.date" className="message-date-separator"/>
        <t t-foreach="dateGroup.messages" t-as="message">
            <Message message="message"/>
        </t>
    </t>
</div>
```

### 2. 聊天历史分段

```xml
<div class="chat-history">
    <t t-foreach="chatHistory" t-as="dayGroup">
        <DateSection date="dayGroup.date"/>
        <div class="day-messages">
            <t t-foreach="dayGroup.messages" t-as="message">
                <ChatMessage message="message"/>
            </t>
        </div>
    </t>
</div>
```

### 3. 活动时间线

```xml
<div class="activity-timeline">
    <t t-foreach="activities" t-as="activity">
        <DateSection 
            t-if="activity.isNewDate" 
            date="activity.date"
            className="timeline-date"
        />
        <ActivityItem activity="activity"/>
    </t>
</div>
```

### 4. 邮件归档视图

```xml
<div class="mail-archive">
    <t t-foreach="mailsByMonth" t-as="monthGroup">
        <DateSection 
            date="monthGroup.monthYear" 
            className="archive-month-header"
        />
        <div class="month-mails">
            <t t-foreach="monthGroup.mails" t-as="mail">
                <MailItem mail="mail"/>
            </t>
        </div>
    </t>
</div>
```

## 模板结构

典型的日期分段模板结构：

```xml
<div t-att-class="'o-mail-DateSection ' + (props.className or '')"
     t-att-data-mobile="isMobileOS">
    <div class="o-mail-DateSection-line"/>
    <div class="o-mail-DateSection-text">
        <span t-esc="props.date"/>
    </div>
    <div class="o-mail-DateSection-line"/>
</div>
```

## 样式设计

### 基础样式

```css
.o-mail-DateSection {
    display: flex;
    align-items: center;
    margin: 20px 0;
    position: relative;
}

.o-mail-DateSection-line {
    flex: 1;
    height: 1px;
    background-color: #e0e0e0;
}

.o-mail-DateSection-text {
    padding: 0 15px;
    background-color: white;
    color: #666;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    white-space: nowrap;
}
```

### 移动端样式

```css
.o-mail-DateSection[data-mobile="true"] {
    margin: 15px 0;
}

.o-mail-DateSection[data-mobile="true"] .o-mail-DateSection-text {
    padding: 0 10px;
    font-size: 11px;
}

.o-mail-DateSection[data-mobile="true"] .o-mail-DateSection-line {
    height: 0.5px;
}
```

### 主题变体

```css
/* 深色主题 */
.o-dark-theme .o-mail-DateSection-line {
    background-color: #404040;
}

.o-dark-theme .o-mail-DateSection-text {
    background-color: #2a2a2a;
    color: #ccc;
}

/* 紧凑模式 */
.o-mail-DateSection.compact-date {
    margin: 10px 0;
}

.o-mail-DateSection.compact-date .o-mail-DateSection-text {
    font-size: 10px;
    padding: 0 8px;
}
```

## 日期格式化

### 智能日期显示

```javascript
// 在父组件中格式化日期
function formatDateForSection(date) {
    const today = new Date();
    const messageDate = new Date(date);
    const diffDays = Math.floor((today - messageDate) / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
        return _t("Today");
    } else if (diffDays === 1) {
        return _t("Yesterday");
    } else if (diffDays < 7) {
        return messageDate.toLocaleDateString(undefined, { weekday: 'long' });
    } else {
        return messageDate.toLocaleDateString();
    }
}
```

### 国际化支持

```javascript
// 本地化日期格式
function getLocalizedDate(date, locale) {
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    
    return new Date(date).toLocaleDateString(locale, options);
}
```

## 响应式设计

### 断点适配

```css
/* 平板设备 */
@media (max-width: 1024px) {
    .o-mail-DateSection {
        margin: 16px 0;
    }
    
    .o-mail-DateSection-text {
        font-size: 11px;
        padding: 0 12px;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    .o-mail-DateSection {
        margin: 12px 0;
    }
    
    .o-mail-DateSection-text {
        font-size: 10px;
        padding: 0 8px;
    }
}
```

### 触摸友好

```css
@media (pointer: coarse) {
    .o-mail-DateSection {
        margin: 18px 0;
        min-height: 24px;
    }
}
```

## 可访问性

### 语义化标记

```xml
<div class="o-mail-DateSection" 
     role="separator" 
     t-att-aria-label="'Messages from ' + props.date">
    <div class="o-mail-DateSection-line" aria-hidden="true"/>
    <div class="o-mail-DateSection-text">
        <time t-att-datetime="props.date" t-esc="props.date"/>
    </div>
    <div class="o-mail-DateSection-line" aria-hidden="true"/>
</div>
```

### 屏幕阅读器支持

```css
.o-mail-DateSection[role="separator"] {
    /* 确保屏幕阅读器能正确识别分隔符 */
}

.o-mail-DateSection-text time {
    /* 为时间元素提供适当的样式 */
}
```

## 性能优化

### 虚拟化支持

```javascript
// 在大量数据中使用虚拟化
function shouldShowDateSection(currentMessage, previousMessage) {
    if (!previousMessage) return true;
    
    const currentDate = new Date(currentMessage.date).toDateString();
    const previousDate = new Date(previousMessage.date).toDateString();
    
    return currentDate !== previousDate;
}
```

### 记忆化

```javascript
// 缓存日期格式化结果
const dateFormatCache = new Map();

function getCachedFormattedDate(date) {
    if (dateFormatCache.has(date)) {
        return dateFormatCache.get(date);
    }
    
    const formatted = formatDateForSection(date);
    dateFormatCache.set(date, formatted);
    return formatted;
}
```

## 扩展建议

1. **动画效果**: 为日期分段添加淡入动画
2. **交互功能**: 点击日期跳转到特定时间
3. **自定义格式**: 支持自定义日期显示格式
4. **时区支持**: 处理不同时区的日期显示
5. **批量操作**: 支持按日期批量选择消息

## 注意事项

1. **时区处理**: 确保日期在不同时区下的正确显示
2. **性能影响**: 在大量消息中避免过度渲染
3. **国际化**: 支持不同语言的日期格式
4. **缓存策略**: 合理缓存格式化结果

该组件虽然简单，但在提升用户体验方面起到重要作用，帮助用户更好地理解和导航时间相关的内容。

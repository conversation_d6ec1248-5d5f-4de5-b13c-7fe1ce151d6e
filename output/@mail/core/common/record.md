# Record - 记录基类

## 概述

`record.js` 是 Odoo 邮件系统中的核心模块，它重新导出了 `@mail/model/export` 模块中的所有功能，主要包括 `Record` 基类。Record 类是整个邮件系统数据模型的基础，提供了响应式数据管理、关系字段处理、生命周期管理等核心功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/record.js`
- **行数**: 7
- **模块**: `@mail/core/common/record`

## 依赖关系

```javascript
// 核心依赖
'@mail/model/export'  // 导出邮件模型系统的所有核心功能
```

## 导出内容

该模块重新导出了以下核心组件：

### 1. Record 基类
- **来源**: `@mail/model/record`
- **功能**: 所有数据模型的基类

### 2. Store 类
- **来源**: `@mail/model/store`
- **功能**: 数据存储和状态管理

### 3. makeStore 函数
- **来源**: `@mail/model/make_store`
- **功能**: 创建存储实例

### 4. 工具函数
- **来源**: `@mail/model/misc`
- **功能**: AND, OR 等逻辑操作符

## Record 基类详解

### 核心特性

Record 类是一个响应式数据模型基类，具有以下特性：

```javascript
class Record {
    static id;              // 主键字段名
    static records = {};    // 静态记录集合
    static store;           // 关联的存储实例
    static env;             // Odoo 环境对象
}
```

### 静态方法

#### 1. 记录获取和创建

```javascript
// 获取现有记录
static get(data) {
    const Model = toRaw(this);
    return this.records[Model.localId(data)];
}

// 创建新记录
static new(data, ids) {
    const Model = toRaw(this);
    const store = Model._rawStore;
    return store.MAKE_UPDATE(function RecordNew() {
        const recordProxy = new Model.Class();
        // ... 初始化逻辑
    });
}

// 插入记录（获取或创建）
static insert(data) {
    return super.insert(...arguments);
}

// 预插入记录
static preinsert(data) {
    // 处理关系字段的预插入
    return Model.get.call(ModelFullProxy, data) ?? Model.new(data, ids);
}
```

#### 2. 更新管理

```javascript
// 批量更新操作
static MAKE_UPDATE(fn) {
    return this.store.MAKE_UPDATE(...arguments);
}

// 字段变化监听
static onChange(record, name, cb) {
    return this.store.onChange(...arguments);
}
```

### 实例方法

#### 1. 数据操作

```javascript
// 更新记录数据
update(data) {
    const record = toRaw(this)._raw;
    const store = record._rawStore;
    return store.MAKE_UPDATE(function recordUpdate() {
        if (typeof data === "object" && data !== null) {
            store._.updateFields(record, data);
        } else {
            store._.updateFields(record, { [record.Model.id]: data });
        }
    });
}

// 删除记录
delete() {
    const record = toRaw(this)._raw;
    const store = record._rawStore;
    return store.MAKE_UPDATE(function recordDelete() {
        store._.ADD_QUEUE("delete", record);
    });
}
```

#### 2. 状态检查

```javascript
// 检查记录是否存在
exists() {
    return !this._[IS_DELETED_SYM];
}

// 记录相等性比较
eq(record) {
    return toRaw(this)._raw === toRaw(record)?._raw;
}

// 记录不等性比较
notEq(record) {
    return !this.eq(record);
}
```

#### 3. 集合操作

```javascript
// 检查记录是否在集合中
in(collection) {
    if (!collection) {
        return false;
    }
    return collection.some((record) => toRaw(record)._raw.eq(this));
}

// 检查记录是否不在集合中
notIn(collection) {
    return !this.in(collection);
}
```

#### 4. 数据序列化

```javascript
// 转换为普通数据对象
toData() {
    const recordProxy = this;
    const record = toRaw(recordProxy)._raw;
    const Model = record.Model;
    const data = { ...recordProxy };
    
    for (const name of Model._.fields.keys()) {
        if (isMany(Model, name)) {
            // 处理一对多关系
            data[name] = record._proxyInternal[name].map((recordProxy) => {
                const record = toRaw(recordProxy)._raw;
                return record.toIdData.call(record._proxyInternal);
            });
        } else if (isOne(Model, name)) {
            // 处理一对一关系
            const otherRecord = toRaw(record._proxyInternal[name])?._raw;
            data[name] = otherRecord?.toIdData.call(otherRecord._proxyInternal);
        } else {
            // 处理普通属性
            const value = recordProxy[name];
            if (Model._.fieldsType.get(name) === "datetime" && value) {
                data[name] = serializeDateTime(value);
            } else if (Model._.fieldsType.get(name) === "date" && value) {
                data[name] = serializeDate(value);
            } else {
                data[name] = value;
            }
        }
    }
    
    // 清理内部属性
    delete data._;
    delete data._fieldsValue;
    delete data._proxy;
    delete data._proxyInternal;
    delete data._raw;
    delete data.Model;
    
    return data;
}
```

## 关系字段定义

### 字段类型

Record 类提供了多种字段类型定义方法：

```javascript
// 普通属性字段
static attr(defaultValue, options = {}) {
    // 定义普通属性字段
}

// 一对一关系
static one(modelName, options = {}) {
    // 定义一对一关系字段
}

// 一对多关系
static many(modelName, options = {}) {
    // 定义一对多关系字段
}
```

### 使用示例

```javascript
class Message extends Record {
    static id = "id";
    static records = {};
    
    // 普通属性
    body = Record.attr("", { html: true });
    subject = Record.attr("");
    date = Record.attr(undefined, { type: "datetime" });
    
    // 关系字段
    author = Record.one("Persona");
    thread = Record.one("Thread");
    attachments = Record.many("Attachment", { inverse: "message" });
    
    // 计算属性
    displayName = Record.attr("", {
        compute() {
            return this.subject || this.body.substring(0, 50);
        }
    });
}
```

## 响应式系统

### 自动更新

Record 类基于 OWL 的响应式系统，提供自动的 UI 更新：

```javascript
// 字段变化自动触发 UI 更新
message.body = "新的消息内容";  // 自动更新相关组件

// 关系字段变化也会触发更新
message.attachments.push(newAttachment);  // 自动更新附件列表
```

### 变化监听

```javascript
// 监听字段变化
Record.onChange(message, "body", () => {
    console.log("消息内容已更改");
});

// 监听多个字段
Record.onChange(message, ["body", "subject"], () => {
    console.log("消息标题或内容已更改");
});
```

## 生命周期管理

### 记录创建

```javascript
// 创建新记录
const message = store.Message.insert({
    body: "Hello World",
    author: currentUser,
    thread: currentThread
});
```

### 记录更新

```javascript
// 更新记录
message.update({
    body: "Updated content",
    subject: "New subject"
});
```

### 记录删除

```javascript
// 软删除（标记为删除）
message.delete();

// 检查是否已删除
if (!message.exists()) {
    console.log("记录已删除");
}
```

## 使用场景

### 1. 数据模型定义

```javascript
class Thread extends Record {
    static id = "id";
    static records = {};
    
    name = Record.attr("");
    messages = Record.many("Message", { inverse: "thread" });
    participants = Record.many("Persona");
    
    get lastMessage() {
        return this.messages[this.messages.length - 1];
    }
}
```

### 2. 数据查询和操作

```javascript
// 获取记录
const thread = store.Thread.get({ id: 123 });

// 创建记录
const newMessage = store.Message.insert({
    body: "新消息",
    thread: thread,
    author: currentUser
});

// 更新记录
thread.update({ name: "新的线程名称" });
```

### 3. 关系管理

```javascript
// 添加关系
thread.participants.push(newParticipant);

// 移除关系
thread.participants.delete(oldParticipant);

// 查询关系
const hasParticipant = newParticipant.in(thread.participants);
```

## 性能优化

### 1. 批量操作

```javascript
// 使用 MAKE_UPDATE 进行批量操作
store.MAKE_UPDATE(() => {
    message1.update({ body: "内容1" });
    message2.update({ body: "内容2" });
    message3.update({ body: "内容3" });
});
```

### 2. 懒加载

```javascript
// 计算属性支持懒加载
displayText = Record.attr("", {
    compute() {
        // 只在需要时计算
        return this.processContent();
    }
});
```

## 注意事项

1. **响应式更新**: 所有字段变化都会触发响应式更新
2. **内存管理**: 及时删除不需要的记录避免内存泄漏
3. **关系一致性**: 确保关系字段的双向一致性
4. **批量操作**: 使用 MAKE_UPDATE 优化性能

## 扩展建议

1. **验证机制**: 添加字段验证和约束
2. **序列化优化**: 优化大对象的序列化性能
3. **缓存策略**: 实现智能的数据缓存
4. **事务支持**: 添加事务回滚机制

Record 类是整个邮件系统的数据基础，为所有业务模型提供了统一的数据管理和响应式更新能力。

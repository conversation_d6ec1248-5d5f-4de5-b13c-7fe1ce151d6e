# Suggestion Hook - 建议钩子

## 概述

`suggestion_hook.js` 实现了 Odoo 邮件系统中的智能建议功能钩子，用于在用户输入时提供实时的建议选项。该钩子支持多种类型的建议，包括用户提及（@）、频道提及（#）、表情符号（:）和预设回复等，为用户提供便捷的输入体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/suggestion_hook.js`
- **行数**: 233
- **模块**: `@mail/core/common/suggestion_hook`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/hooks'    // 邮件系统钩子
'@odoo/owl'                   // OWL 框架
'@web/core/utils/hooks'       // Web 核心钩子
```

## 核心类

### UseSuggestion 类

建议功能的核心实现类：

```javascript
class UseSuggestion {
    constructor(comp) {
        this.comp = comp;
        // 设置效果监听器
        // 设置状态管理
        // 初始化搜索参数
    }
}
```

## 核心属性

### 组件引用

```javascript
comp;                    // 组件实例
get composer() {         // 编辑器对象
    return this.comp.props.composer;
}
get thread() {           // 线程对象
    return this.composer.thread || this.composer.message.thread;
}
```

### 服务和工具

```javascript
sequential = useSequential();                    // 顺序执行工具
suggestionService = useService("mail.suggestion"); // 建议服务
```

### 状态管理

```javascript
state = useState({
    count: 0,           // 搜索计数器
    items: undefined,   // 建议项目
    isFetching: false,  // 是否正在获取
});
```

### 搜索参数

```javascript
search = {
    delimiter: undefined,  // 分隔符（@, #, : 等）
    position: undefined,   // 分隔符位置
    term: "",             // 搜索词
};
lastFetchedSearch;        // 上次获取的搜索参数
```

## 核心功能

### 1. 自动检测

#### 检测逻辑

```javascript
detect() {
    const { start, end } = this.composer.selection;
    const text = this.composer.text;
    
    // 避免干扰多字符选择
    if (start !== end) {
        this.clearSearch();
        return;
    }
    
    const candidatePositions = [];
    let numberOfSpaces = 0;
    
    // 考虑光标位置前的字符
    for (let index = start - 1; index >= 0; --index) {
        if (/\s/.test(text[index])) {
            numberOfSpaces++;
            if (numberOfSpaces === 2) {
                // 在第二个空格后停止考虑
                // 因为大多数合作伙伴有两个词的名字
                break;
            }
        }
        candidatePositions.push(index);
    }
    
    // 如果当前分隔符仍然有效，保持它
    if (this.search.position !== undefined && this.search.position < start) {
        candidatePositions.push(this.search.position);
    }
    
    const supportedDelimiters = this.suggestionService.getSupportedDelimiters(this.thread);
    
    for (const candidatePosition of candidatePositions) {
        if (candidatePosition < 0 || candidatePosition >= text.length) {
            continue;
        }
        
        const candidateChar = text[candidatePosition];
        
        // 检查是否为支持的分隔符
        if (!supportedDelimiters.find(
            ([delimiter, allowedPosition]) =>
                delimiter === candidateChar &&
                (allowedPosition === undefined || allowedPosition === candidatePosition)
        )) {
            continue;
        }
        
        // 检查分隔符前的字符
        const charBeforeCandidate = text[candidatePosition - 1];
        if (charBeforeCandidate && !/\s/.test(charBeforeCandidate)) {
            continue;
        }
        
        // 设置搜索参数
        Object.assign(this.search, {
            delimiter: candidateChar,
            position: candidatePosition,
            term: text.substring(candidatePosition + 1, start),
        });
        
        this.state.count++;
        return;
    }
    
    this.clearSearch();
}
```

**检测特性**:
- 支持多种分隔符（@, #, : 等）
- 智能边界检测
- 避免误触发
- 考虑上下文环境

### 2. 建议获取

#### 异步获取

```javascript
useEffect(
    (delimiter, position, term) => {
        this.update();
        
        if (this.search.position === undefined || !this.search.delimiter) {
            return; // 没有需要获取的内容
        }
        
        if (this.composer.store.self.type !== "partner") {
            return; // 访客无法访问获取建议方法
        }
        
        this.sequential(async () => {
            // 检查搜索参数是否仍然有效
            if (this.search.delimiter !== delimiter ||
                this.search.position !== position ||
                this.search.term !== term) {
                return; // 忽略过时的调用
            }
            
            // 优化：如果上次搜索无结果且当前搜索更具体，跳过
            if (this.lastFetchedSearch?.count === 0 &&
                (!this.search.delimiter || this.isSearchMoreSpecificThanLastFetch)) {
                return;
            }
            
            this.state.isFetching = true;
            
            try {
                await this.suggestionService.fetchSuggestions(this.search, {
                    thread: this.thread,
                });
            } catch {
                this.lastFetchedSearch = null;
            } finally {
                this.state.isFetching = false;
            }
            
            if (status(comp) === "destroyed") {
                return;
            }
            
            this.update();
            this.lastFetchedSearch = {
                ...this.search,
                count: this.state.items?.suggestions.length ?? 0,
            };
            
            // 如果没有建议结果，清除搜索
            if (this.search.delimiter === delimiter &&
                this.search.position === position &&
                this.search.term === term &&
                !this.state.items?.suggestions.length) {
                this.clearSearch();
            }
        });
    },
    () => [this.search.delimiter, this.search.position, this.search.term]
);
```

### 3. 建议更新

#### 本地搜索

```javascript
update() {
    if (!this.search.delimiter) {
        return;
    }
    
    const { type, suggestions } = this.suggestionService.searchSuggestions(this.search, {
        thread: this.thread,
        sort: true,
    });
    
    if (!suggestions.length) {
        this.state.items = undefined;
        return;
    }
    
    // 任意限制以避免一次显示太多元素
    // 理想情况下应该引入加载更多机制
    const limit = 8;
    suggestions.length = Math.min(suggestions.length, limit);
    this.state.items = { type, suggestions };
}
```

### 4. 建议插入

#### 插入选项

```javascript
insert(option) {
    const position = this.composer.selection.start;
    const text = this.composer.text;
    let before = text.substring(0, this.search.position + 1);
    let after = text.substring(position, text.length);
    
    // 表情符号特殊处理
    if (this.search.delimiter === ":") {
        before = text.substring(0, this.search.position);
        after = text.substring(position, text.length);
    }
    
    // 处理合作伙伴提及
    if (option.partner) {
        this.composer.mentionedPartners.add({
            id: option.partner.id,
            type: "partner",
        });
    }
    
    // 处理频道提及
    if (option.thread) {
        this.composer.mentionedChannels.add({
            model: "discuss.channel",
            id: option.thread.id,
        });
    }
    
    // 处理预设回复
    if (option.cannedResponse) {
        this.composer.cannedResponses.push(option.cannedResponse);
    }
    
    // 更新文本和光标位置
    this.clearSearch();
    this.composer.text = before + option.label + " " + after;
    this.composer.selection.start = before.length + option.label.length + 1;
    this.composer.selection.end = before.length + option.label.length + 1;
    this.composer.forceCursorMove = true;
}
```

## 优化机制

### 1. 搜索优化

#### 更具体搜索检查

```javascript
get isSearchMoreSpecificThanLastFetch() {
    return (
        this.lastFetchedSearch.delimiter === this.search.delimiter &&
        this.search.term.startsWith(this.lastFetchedSearch.term) &&
        this.lastFetchedSearch.position >= this.search.position
    );
}
```

**优化逻辑**:
- 如果当前搜索是上次搜索的更具体版本
- 且上次搜索无结果
- 则跳过当前搜索

### 2. 顺序执行

```javascript
sequential = useSequential();
```

**功能**:
- 确保异步操作按顺序执行
- 避免竞态条件
- 取消过时的请求

### 3. 结果限制

```javascript
const limit = 8;
suggestions.length = Math.min(suggestions.length, limit);
```

**目的**:
- 限制显示的建议数量
- 提高渲染性能
- 改善用户体验

## 清理功能

### 1. 清除原始提及

```javascript
clearRawMentions() {
    this.composer.mentionedChannels.length = 0;
    this.composer.mentionedPartners.length = 0;
}
```

### 2. 清除预设回复

```javascript
clearCannedResponses() {
    this.composer.cannedResponses = [];
}
```

### 3. 清除搜索

```javascript
clearSearch() {
    Object.assign(this.search, {
        delimiter: undefined,
        position: undefined,
        term: "",
    });
    this.state.items = undefined;
}
```

## 支持的建议类型

### 1. 用户提及 (@)

```javascript
// @username 格式
// 触发用户建议列表
// 插入时添加到 mentionedPartners
```

### 2. 频道提及 (#)

```javascript
// #channel 格式
// 触发频道建议列表
// 插入时添加到 mentionedChannels
```

### 3. 表情符号 (:)

```javascript
// :emoji: 格式
// 触发表情符号建议
// 插入时替换为表情符号
```

### 4. 预设回复

```javascript
// 特定分隔符触发
// 显示预设回复模板
// 插入时添加到 cannedResponses
```

## useSuggestion 钩子

### 钩子函数

```javascript
function useSuggestion() {
    return new UseSuggestion(useComponent());
}
```

**功能**:
- 创建建议功能实例
- 绑定到当前组件
- 返回建议管理对象

## 使用场景

### 1. 消息编辑器

```javascript
// 在编辑器组件中
const suggestion = useSuggestion();

// 建议列表显示
if (suggestion.state.items) {
    showSuggestionList(suggestion.state.items);
}

// 选择建议
const selectSuggestion = (option) => {
    suggestion.insert(option);
};
```

### 2. 聊天输入框

```javascript
// 聊天组件中的建议
const suggestion = useSuggestion();

// 监听建议状态
useEffect(() => {
    if (suggestion.state.items) {
        openSuggestionPopup();
    } else {
        closeSuggestionPopup();
    }
}, () => [suggestion.state.items]);
```

### 3. 自定义建议

```javascript
// 扩展建议类型
const suggestion = useSuggestion();

// 自定义插入逻辑
const insertCustomSuggestion = (option) => {
    // 自定义处理
    suggestion.insert(option);
};
```

## 性能优化

### 1. 防抖机制

- 使用 `useSequential` 避免频繁请求
- 智能跳过无效搜索
- 缓存搜索结果

### 2. 结果限制

- 限制显示数量提高性能
- 避免过多DOM元素
- 考虑实现分页加载

### 3. 状态管理

- 使用响应式状态更新
- 及时清理无用状态
- 优化重新渲染

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听文本和光标变化
- 自动触发建议检测

### 2. 策略模式 (Strategy Pattern)
- 根据分隔符类型采用不同策略
- 支持多种建议类型

### 3. 状态模式 (State Pattern)
- 根据搜索状态显示不同界面
- 管理获取和显示状态

## 注意事项

1. **性能考虑**: 避免频繁的网络请求
2. **用户体验**: 提供即时的视觉反馈
3. **权限控制**: 检查用户权限
4. **边界处理**: 正确处理文本边界

## 扩展建议

1. **更多建议类型**: 支持文件、链接等建议
2. **智能排序**: 基于使用频率排序
3. **缓存机制**: 改进建议缓存策略
4. **自定义分隔符**: 允许自定义触发字符
5. **键盘导航**: 改进键盘导航体验

该钩子为用户提供了智能的输入建议功能，大大提升了消息编辑的效率和用户体验。

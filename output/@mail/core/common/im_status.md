# IM Status - 即时消息状态组件

## 概述

`im_status.js` 实现了即时消息状态显示组件，用于在 Odoo 邮件系统中显示用户的在线状态。该组件支持多种状态显示（在线、离线、忙碌等），并集成了打字状态指示器，为用户提供实时的状态信息。

## 文件信息
- **路径**: `/mail/static/src/core/common/im_status.js`
- **行数**: 19
- **模块**: `@mail/core/common/im_status`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架组件基类
'@mail/discuss/typing/common/typing'  // 打字状态组件
```

## 组件定义

### ImStatus 类

继承自 OWL Component 的即时消息状态组件：

```javascript
class ImStatus extends Component {
    static props = ["persona?", "className?", "style?", "member?", "size?"];
    static template = "mail.ImStatus";
    static defaultProps = { className: "", style: "", size: "lg" };
    static components = { Typing };
}
```

### 子组件

- **`Typing`**: 打字状态指示器组件，显示用户正在输入的状态

## Props 配置

### Props 定义

```javascript
static props = [
    "persona?",    // 用户角色对象（可选）
    "className?",  // 自定义CSS类名（可选）
    "style?",      // 自定义样式（可选）
    "member?",     // 成员对象（可选）
    "size?"        // 尺寸大小（可选）
];
```

### 默认属性

```javascript
static defaultProps = {
    className: "",  // 默认无额外类名
    style: "",      // 默认无自定义样式
    size: "lg"      // 默认大尺寸
};
```

### Props 详细说明

- **`persona`** (可选):
  - 类型: Persona 对象
  - 用途: 直接指定要显示状态的用户角色
  - 优先级: 高于 member.persona

- **`className`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"status-indicator"`, `"compact-status"`

- **`style`** (可选):
  - 类型: 字符串
  - 用途: 添加内联样式
  - 示例: `"margin-left: 5px;"`

- **`member`** (可选):
  - 类型: Member 对象
  - 用途: 通过成员对象获取用户角色
  - 备用: 当没有直接提供 persona 时使用

- **`size`** (可选):
  - 类型: 字符串
  - 用途: 控制状态指示器的尺寸
  - 可选值: `"sm"`, `"md"`, `"lg"`, `"xl"`
  - 默认值: `"lg"`

## 核心功能

### persona 计算属性

```javascript
get persona() {
    return this.props.persona ?? this.props.member?.persona;
}
```

**功能说明**:
- 优先使用直接传入的 `persona` 属性
- 如果没有 `persona`，则从 `member` 对象中获取
- 使用空值合并操作符 (`??`) 确保类型安全

## 状态类型

### 常见的IM状态

1. **在线 (Online)**
   - 用户当前活跃在线
   - 通常显示为绿色圆点

2. **离开 (Away)**
   - 用户暂时离开
   - 通常显示为黄色圆点

3. **忙碌 (Busy)**
   - 用户忙碌中，不希望被打扰
   - 通常显示为红色圆点

4. **离线 (Offline)**
   - 用户不在线
   - 通常显示为灰色圆点或不显示

5. **正在输入 (Typing)**
   - 用户正在输入消息
   - 通过 Typing 组件显示动画效果

## 使用场景

### 1. 聊天参与者列表
```xml
<div class="participant-list">
    <t t-foreach="participants" t-as="participant">
        <div class="participant-item">
            <ImStatus member="participant" size="sm"/>
            <span t-esc="participant.display_name"/>
        </div>
    </t>
</div>
```

### 2. 消息发送者状态
```xml
<div class="message-header">
    <ImStatus persona="message.author" className="author-status"/>
    <span t-esc="message.author.name"/>
</div>
```

### 3. 联系人列表
```xml
<div class="contact-list">
    <t t-foreach="contacts" t-as="contact">
        <div class="contact-item">
            <ImStatus persona="contact.persona" size="md"/>
            <span t-esc="contact.name"/>
        </div>
    </t>
</div>
```

### 4. 聊天窗口标题栏
```xml
<div class="chat-header">
    <h3 t-esc="thread.displayName"/>
    <ImStatus 
        persona="thread.correspondent" 
        className="header-status"
        size="lg"
    />
</div>
```

## 尺寸变体

### 尺寸配置

```javascript
// 不同尺寸的状态指示器
const sizeMap = {
    "sm": "8px",   // 小尺寸
    "md": "12px",  // 中等尺寸  
    "lg": "16px",  // 大尺寸（默认）
    "xl": "20px"   // 超大尺寸
};
```

### 样式示例

```css
.o-mail-ImStatus {
    position: relative;
    display: inline-block;
}

.o-mail-ImStatus-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid white;
    position: absolute;
    bottom: 0;
    right: 0;
}

/* 尺寸变体 */
.o-mail-ImStatus.size-sm .o-mail-ImStatus-indicator {
    width: 8px;
    height: 8px;
}

.o-mail-ImStatus.size-md .o-mail-ImStatus-indicator {
    width: 12px;
    height: 12px;
}

.o-mail-ImStatus.size-xl .o-mail-ImStatus-indicator {
    width: 20px;
    height: 20px;
}
```

## 状态颜色

### 状态颜色映射

```css
/* 在线状态 - 绿色 */
.o-mail-ImStatus-indicator.online {
    background-color: #28a745;
}

/* 离开状态 - 黄色 */
.o-mail-ImStatus-indicator.away {
    background-color: #ffc107;
}

/* 忙碌状态 - 红色 */
.o-mail-ImStatus-indicator.busy {
    background-color: #dc3545;
}

/* 离线状态 - 灰色 */
.o-mail-ImStatus-indicator.offline {
    background-color: #6c757d;
}

/* 隐身状态 - 透明 */
.o-mail-ImStatus-indicator.invisible {
    background-color: transparent;
    border-color: #6c757d;
}
```

## 打字状态集成

### Typing 组件集成

```xml
<!-- 在模板中集成打字状态 -->
<div class="o-mail-ImStatus">
    <div t-if="persona.im_status !== 'typing'" 
         t-att-class="'o-mail-ImStatus-indicator ' + persona.im_status"/>
    <Typing t-if="persona.im_status === 'typing'" 
            persona="persona"/>
</div>
```

### 打字状态优先级

1. 当用户正在输入时，显示 Typing 组件
2. 其他时候显示常规的在线状态指示器
3. 打字状态具有最高显示优先级

## 响应式设计

### 移动端适配

```css
@media (max-width: 768px) {
    .o-mail-ImStatus-indicator {
        width: 12px;
        height: 12px;
    }
    
    .o-mail-ImStatus.size-lg .o-mail-ImStatus-indicator {
        width: 14px;
        height: 14px;
    }
}
```

### 高分辨率支持

```css
@media (-webkit-min-device-pixel-ratio: 2) {
    .o-mail-ImStatus-indicator {
        border-width: 1px;
    }
}
```

## 可访问性

### 无障碍支持

```xml
<div class="o-mail-ImStatus" 
     t-att-aria-label="persona.im_status_text"
     role="img">
    <div t-att-class="'o-mail-ImStatus-indicator ' + persona.im_status"
         t-att-title="persona.im_status_text"/>
</div>
```

### 屏幕阅读器支持

```javascript
// 状态文本映射
const statusTextMap = {
    'online': _t('Online'),
    'away': _t('Away'),
    'busy': _t('Busy'),
    'offline': _t('Offline'),
    'typing': _t('Typing...')
};
```

## 性能优化

### 状态缓存

```javascript
// 缓存状态计算结果
const statusCache = new Map();

function getPersonaStatus(persona) {
    const cacheKey = `${persona.id}_${persona.last_seen}`;
    if (statusCache.has(cacheKey)) {
        return statusCache.get(cacheKey);
    }
    
    const status = calculateStatus(persona);
    statusCache.set(cacheKey, status);
    return status;
}
```

### 批量更新

```javascript
// 批量更新多个状态
function updateMultipleStatuses(personas) {
    const updates = personas.map(persona => ({
        id: persona.id,
        status: getPersonaStatus(persona)
    }));
    
    // 批量应用更新
    applyStatusUpdates(updates);
}
```

## 扩展建议

1. **自定义状态**: 支持自定义状态类型和颜色
2. **动画效果**: 为状态变化添加平滑过渡动画
3. **状态历史**: 记录和显示状态变化历史
4. **批量操作**: 支持批量设置多个用户状态
5. **主题支持**: 适配不同的UI主题

## 注意事项

1. **实时更新**: 确保状态信息的实时性
2. **网络状态**: 处理网络断开时的状态显示
3. **隐私设置**: 尊重用户的隐私设置
4. **性能影响**: 避免频繁的状态查询影响性能

该组件是即时通讯系统中的重要组成部分，为用户提供了直观的状态信息，增强了沟通的效率和用户体验。

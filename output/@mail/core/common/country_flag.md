# Country Flag - 国家旗帜组件

## 概述

`country_flag.js` 实现了一个简洁的国家旗帜显示组件，用于在 Odoo 邮件系统中显示用户或联系人的国家旗帜图标。该组件提供了标准化的国家标识显示方式，增强了用户界面的国际化体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/country_flag.js`
- **行数**: 16
- **模块**: `@mail/core/common/country_flag`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'  // OWL 框架组件基类
```

## 组件定义

### CountryFlag 类

继承自 OWL Component 的国家旗帜组件：

```javascript
class CountryFlag extends Component {
    static props = ["country", "class?"];
    static template = "mail.CountryFlag";
}
```

## Props 配置

### Props 定义

```javascript
static props = [
    "country",  // 国家对象或国家代码
    "class?"    // 可选的CSS类名
];
```

### Props 详细说明

- **`country`** (必需): 
  - 类型: 国家对象或国家代码字符串
  - 用途: 指定要显示的国家旗帜
  - 示例: `{ code: "US", name: "United States" }` 或 `"US"`

- **`class`** (可选):
  - 类型: 字符串
  - 用途: 添加自定义CSS类名
  - 示例: `"small-flag"`, `"rounded-flag"`

## 使用场景

### 1. 用户资料显示
```xml
<CountryFlag country="user.country" class="user-flag" />
```

### 2. 联系人列表
```xml
<t t-foreach="contacts" t-as="contact">
    <div class="contact-item">
        <span t-esc="contact.name"/>
        <CountryFlag country="contact.country" class="contact-flag"/>
    </div>
</t>
```

### 3. 聊天参与者标识
```xml
<div class="chat-participant">
    <CountryFlag country="participant.country"/>
    <span t-esc="participant.name"/>
</div>
```

### 4. 消息发送者标识
```xml
<div class="message-author">
    <CountryFlag country="message.author.country" class="author-flag"/>
    <span t-esc="message.author.name"/>
</div>
```

## 模板结构

虽然具体的模板实现在 `mail.CountryFlag` 模板中，但通常包含以下结构：

```xml
<!-- 典型的国家旗帜模板结构 -->
<span t-att-class="'o-mail-CountryFlag ' + (props.class or '')">
    <img 
        t-if="country" 
        t-att-src="'/base/static/img/country_flags/' + country.code.toLowerCase() + '.png'"
        t-att-alt="country.name"
        t-att-title="country.name"
        class="o-mail-CountryFlag-image"
    />
</span>
```

## 设计特点

### 1. 极简设计
- 组件代码非常简洁，只有核心功能
- 专注于单一职责：显示国家旗帜

### 2. 灵活配置
- 支持传入国家对象或国家代码
- 可选的CSS类名支持自定义样式

### 3. 标准化显示
- 统一的旗帜显示格式
- 一致的用户体验

### 4. 可访问性
- 支持alt和title属性
- 提供无障碍访问支持

## 技术实现

### 1. 图片资源管理
```javascript
// 通常旗帜图片存储在标准路径
const flagImagePath = `/base/static/img/country_flags/${countryCode.toLowerCase()}.png`;
```

### 2. 国家代码标准
- 使用 ISO 3166-1 alpha-2 国家代码
- 例如: US (美国), CN (中国), GB (英国)

### 3. 回退处理
```xml
<!-- 当没有国家信息时的回退显示 -->
<span t-if="!country" class="o-mail-CountryFlag-placeholder">
    <i class="fa fa-globe" title="Unknown Country"/>
</span>
```

## 样式定制

### 1. 基础样式
```css
.o-mail-CountryFlag {
    display: inline-block;
    vertical-align: middle;
}

.o-mail-CountryFlag-image {
    width: 16px;
    height: 12px;
    border: 1px solid #ddd;
}
```

### 2. 尺寸变体
```css
.o-mail-CountryFlag.small-flag .o-mail-CountryFlag-image {
    width: 12px;
    height: 9px;
}

.o-mail-CountryFlag.large-flag .o-mail-CountryFlag-image {
    width: 24px;
    height: 18px;
}
```

### 3. 圆角样式
```css
.o-mail-CountryFlag.rounded-flag .o-mail-CountryFlag-image {
    border-radius: 2px;
}
```

## 使用示例

### 1. 基础使用
```xml
<CountryFlag country="record.country"/>
```

### 2. 带自定义样式
```xml
<CountryFlag country="user.country" class="small-flag rounded-flag"/>
```

### 3. 条件显示
```xml
<CountryFlag t-if="contact.country" country="contact.country"/>
```

### 4. 在列表中使用
```xml
<t t-foreach="participants" t-as="participant">
    <div class="participant-item">
        <CountryFlag 
            country="participant.country" 
            class="participant-flag"
        />
        <span t-esc="participant.display_name"/>
    </div>
</t>
```

## 国际化考虑

### 1. 国家名称本地化
```javascript
// 在模板中使用翻译
t-att-title="_t(country.name)"
```

### 2. 文化敏感性
- 确保旗帜图片的政治正确性
- 处理争议地区的显示问题

### 3. RTL语言支持
```css
.o-rtl .o-mail-CountryFlag {
    margin-left: 4px;
    margin-right: 0;
}
```

## 性能优化

### 1. 图片预加载
```javascript
// 预加载常用国家旗帜
const commonCountries = ['US', 'CN', 'GB', 'DE', 'FR'];
commonCountries.forEach(code => {
    const img = new Image();
    img.src = `/base/static/img/country_flags/${code.toLowerCase()}.png`;
});
```

### 2. 懒加载
```xml
<!-- 使用懒加载属性 -->
<img loading="lazy" t-att-src="flagImagePath"/>
```

## 扩展建议

### 1. 动态加载
- 支持从CDN加载旗帜图片
- 实现图片缓存机制

### 2. 矢量图标支持
- 使用SVG格式的旗帜图标
- 支持高分辨率显示

### 3. 交互功能
- 点击旗帜显示国家信息
- 悬停显示详细信息

### 4. 主题支持
- 支持深色/浅色主题
- 自适应颜色方案

## 注意事项

1. **图片资源**: 确保所有国家旗帜图片都已正确部署
2. **加载失败**: 处理图片加载失败的情况
3. **版权问题**: 确保旗帜图片的使用符合版权要求
4. **更新维护**: 及时更新国家信息和旗帜图片

## 相关组件

- **ImStatus**: 显示用户在线状态
- **PersonaModel**: 用户信息模型
- **CountryModel**: 国家信息模型

该组件虽然简单，但在国际化应用中起到重要的视觉标识作用，提升了用户界面的专业性和友好性。

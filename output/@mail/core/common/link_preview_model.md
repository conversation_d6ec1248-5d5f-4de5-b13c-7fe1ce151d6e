# Link Preview Model - 链接预览模型

## 概述

`link_preview_model.js` 定义了 Odoo 邮件系统中的链接预览模型类，用于管理消息中链接的预览信息。该模型处理 Open Graph 元数据，支持图片、视频和卡片等多种预览类型，为用户提供丰富的链接预览体验，是现代消息系统中重要的媒体展示功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/link_preview_model.js`
- **行数**: 61
- **模块**: `@mail/core/common/link_preview_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类
```

## 类定义

### LinkPreview 类

```javascript
class LinkPreview extends Record {
    static id = "id";           // 主键字段
    static records = {};        // 静态记录集合
}
```

## 核心属性

### 基础属性

```javascript
id: number;                     // 链接预览ID
source_url: string;             // 源链接URL
```

### 消息关系

```javascript
message = Record.one("Message", { inverse: "linkPreviews" });
```

**关系说明**:
- 与消息模型的一对一关系
- 反向关系：消息的 `linkPreviews` 集合
- 确定预览属于哪条消息

### Open Graph 属性

#### 基础 OG 属性

```javascript
og_title: string;               // OG标题
og_description: string;         // OG描述
og_type: string;                // OG类型 (website, video, etc.)
og_site_name: string;           // OG站点名称
```

#### 图片相关属性

```javascript
og_image: string;               // OG图片URL
og_mimetype: string;            // OG MIME类型
image_mimetype: string;         // 图片MIME类型
```

### 属性详细说明

#### 1. source_url (源链接)
- **类型**: `string`
- **用途**: 原始链接地址
- **示例**: `"https://example.com/article"`

#### 2. og_title (标题)
- **类型**: `string`
- **用途**: Open Graph 标题
- **示例**: `"Amazing Article Title"`

#### 3. og_description (描述)
- **类型**: `string`
- **用途**: Open Graph 描述
- **示例**: `"This is an amazing article about..."`

#### 4. og_type (类型)
- **类型**: `string`
- **用途**: Open Graph 内容类型
- **可能值**: `"website"`, `"video"`, `"article"`, `"music"` 等

#### 5. og_image (图片)
- **类型**: `string`
- **用途**: Open Graph 图片URL
- **示例**: `"https://example.com/image.jpg"`

## 计算属性

### 1. imageUrl - 图片URL

```javascript
get imageUrl() {
    return this.og_image ? this.og_image : this.source_url;
}
```

**获取逻辑**:
1. **优先使用**: Open Graph 图片URL
2. **备用方案**: 源链接URL（可能本身就是图片）
3. **用途**: 统一的图片显示接口

### 2. isImage - 图片类型检查

```javascript
get isImage() {
    return Boolean(this.image_mimetype || this.og_mimetype === "image/gif");
}
```

**判断条件**:
- 有图片MIME类型
- 或者 OG MIME类型为 GIF
- 返回布尔值表示是否为图片类型

### 3. isVideo - 视频类型检查

```javascript
get isVideo() {
    return Boolean(!this.isImage && this.og_type && this.og_type.startsWith("video"));
}
```

**判断逻辑**:
1. **排除图片**: 首先确保不是图片类型
2. **检查类型**: OG类型存在且以 "video" 开头
3. **返回结果**: 布尔值表示是否为视频类型

### 4. isCard - 卡片类型检查

```javascript
get isCard() {
    return !this.isImage && !this.isVideo;
}
```

**判断逻辑**:
- 既不是图片也不是视频
- 默认显示为卡片类型
- 包括网站、文章等普通链接

## 预览类型

### 1. 图片预览

```javascript
// 图片类型的链接预览
const imagePreview = {
    id: 1,
    source_url: "https://example.com/image.jpg",
    image_mimetype: "image/jpeg",
    og_image: "https://example.com/image.jpg",
    og_title: "Beautiful Image"
};

console.log(imagePreview.isImage);  // true
console.log(imagePreview.isVideo);  // false
console.log(imagePreview.isCard);   // false
```

### 2. 视频预览

```javascript
// 视频类型的链接预览
const videoPreview = {
    id: 2,
    source_url: "https://youtube.com/watch?v=123",
    og_type: "video.other",
    og_title: "Amazing Video",
    og_description: "Watch this amazing video",
    og_image: "https://img.youtube.com/vi/123/maxresdefault.jpg"
};

console.log(videoPreview.isImage);  // false
console.log(videoPreview.isVideo);  // true
console.log(videoPreview.isCard);   // false
```

### 3. 卡片预览

```javascript
// 卡片类型的链接预览
const cardPreview = {
    id: 3,
    source_url: "https://example.com/article",
    og_type: "article",
    og_title: "Interesting Article",
    og_description: "Read this interesting article",
    og_site_name: "Example News",
    og_image: "https://example.com/article-thumb.jpg"
};

console.log(cardPreview.isImage);  // false
console.log(cardPreview.isVideo);  // false
console.log(cardPreview.isCard);   // true
```

## 使用场景

### 1. 消息中的链接预览

```javascript
// 在消息组件中显示链接预览
const renderLinkPreviews = (message) => {
    return message.linkPreviews.map(preview => {
        if (preview.isImage) {
            return renderImagePreview(preview);
        } else if (preview.isVideo) {
            return renderVideoPreview(preview);
        } else if (preview.isCard) {
            return renderCardPreview(preview);
        }
    });
};
```

### 2. 图片预览渲染

```javascript
const renderImagePreview = (preview) => {
    return `
        <div class="link-preview image-preview">
            <img src="${preview.imageUrl}" alt="${preview.og_title || 'Image'}" />
            ${preview.og_title ? `<div class="title">${preview.og_title}</div>` : ''}
        </div>
    `;
};
```

### 3. 视频预览渲染

```javascript
const renderVideoPreview = (preview) => {
    return `
        <div class="link-preview video-preview">
            <div class="video-thumbnail">
                <img src="${preview.og_image}" alt="${preview.og_title}" />
                <div class="play-button">▶</div>
            </div>
            <div class="video-info">
                <h3>${preview.og_title}</h3>
                <p>${preview.og_description}</p>
                <span class="site-name">${preview.og_site_name}</span>
            </div>
        </div>
    `;
};
```

### 4. 卡片预览渲染

```javascript
const renderCardPreview = (preview) => {
    return `
        <div class="link-preview card-preview">
            ${preview.og_image ? `
                <div class="card-image">
                    <img src="${preview.og_image}" alt="${preview.og_title}" />
                </div>
            ` : ''}
            <div class="card-content">
                <h3>${preview.og_title}</h3>
                <p>${preview.og_description}</p>
                <span class="site-name">${preview.og_site_name}</span>
                <a href="${preview.source_url}" target="_blank" class="source-link">
                    ${preview.source_url}
                </a>
            </div>
        </div>
    `;
};
```

## 数据处理

### 1. 创建链接预览

```javascript
// 从 Open Graph 数据创建预览
const createLinkPreview = (ogData, sourceUrl, messageId) => {
    return store.LinkPreview.insert({
        source_url: sourceUrl,
        message: messageId,
        og_title: ogData.title,
        og_description: ogData.description,
        og_type: ogData.type,
        og_image: ogData.image,
        og_site_name: ogData.site_name,
        og_mimetype: ogData.image_type
    });
};
```

### 2. 更新预览信息

```javascript
// 更新链接预览数据
const updateLinkPreview = (previewId, newData) => {
    const preview = store.LinkPreview.get(previewId);
    if (preview) {
        Object.assign(preview, newData);
    }
};
```

### 3. 删除预览

```javascript
// 删除链接预览
const deleteLinkPreview = (previewId) => {
    const preview = store.LinkPreview.get(previewId);
    if (preview) {
        preview.delete();
    }
};
```

## Open Graph 支持

### 标准 OG 标签

```html
<!-- 网页中的 Open Graph 标签 -->
<meta property="og:title" content="Page Title" />
<meta property="og:description" content="Page Description" />
<meta property="og:type" content="website" />
<meta property="og:image" content="https://example.com/image.jpg" />
<meta property="og:url" content="https://example.com/page" />
<meta property="og:site_name" content="Site Name" />
```

### 视频特定标签

```html
<!-- 视频相关的 OG 标签 -->
<meta property="og:type" content="video.other" />
<meta property="og:video" content="https://example.com/video.mp4" />
<meta property="og:video:type" content="video/mp4" />
<meta property="og:video:width" content="1280" />
<meta property="og:video:height" content="720" />
```

### 图片特定标签

```html
<!-- 图片相关的 OG 标签 -->
<meta property="og:image:type" content="image/jpeg" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta property="og:image:alt" content="Image description" />
```

## 错误处理

### 1. 缺失数据处理

```javascript
// 安全访问预览属性
const getPreviewTitle = (preview) => {
    return preview.og_title || preview.source_url || 'Untitled';
};

const getPreviewDescription = (preview) => {
    return preview.og_description || 'No description available';
};
```

### 2. 图片加载失败

```javascript
// 图片加载失败的备用方案
const handleImageError = (preview) => {
    // 移除损坏的图片URL
    preview.og_image = null;
    
    // 如果源URL也是图片，尝试使用
    if (preview.source_url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
        return preview.source_url;
    }
    
    // 返回默认占位图
    return '/web/static/img/placeholder.png';
};
```

### 3. 类型检测失败

```javascript
// 类型检测的备用逻辑
const detectPreviewType = (preview) => {
    // 基于URL扩展名检测
    const url = preview.source_url.toLowerCase();
    
    if (url.match(/\.(jpg|jpeg|png|gif|webp)$/)) {
        return 'image';
    }
    
    if (url.match(/\.(mp4|webm|ogg|avi)$/)) {
        return 'video';
    }
    
    // 基于域名检测
    if (url.includes('youtube.com') || url.includes('vimeo.com')) {
        return 'video';
    }
    
    return 'card';
};
```

## 性能优化

### 1. 图片懒加载

```javascript
// 实现图片懒加载
const lazyLoadPreviewImage = (preview) => {
    const img = new Image();
    img.onload = () => {
        // 图片加载成功，显示预览
        showPreview(preview);
    };
    img.onerror = () => {
        // 图片加载失败，使用备用方案
        preview.og_image = null;
    };
    img.src = preview.imageUrl;
};
```

### 2. 预览缓存

```javascript
// 缓存预览数据
const previewCache = new Map();

const getCachedPreview = (url) => {
    if (previewCache.has(url)) {
        return previewCache.get(url);
    }
    
    // 获取新的预览数据
    const preview = fetchPreviewData(url);
    previewCache.set(url, preview);
    return preview;
};
```

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 根据预览类型采用不同的渲染策略
- 图片、视频、卡片的不同处理方式

### 2. 工厂模式 (Factory Pattern)
- 根据 Open Graph 数据创建预览对象
- 统一的预览创建接口

### 3. 适配器模式 (Adapter Pattern)
- 将不同来源的元数据适配为统一格式
- 处理不同网站的 OG 标签差异

## 注意事项

1. **隐私保护**: 注意链接预览可能泄露用户浏览信息
2. **性能影响**: 大量预览可能影响页面加载性能
3. **内容安全**: 验证预览内容的安全性
4. **版权问题**: 注意预览内容的版权和使用权限

## 扩展建议

1. **更多媒体类型**: 支持音频、文档等更多类型
2. **自定义预览**: 允许用户自定义预览样式
3. **预览编辑**: 支持编辑预览信息
4. **批量处理**: 支持批量生成和管理预览
5. **智能识别**: 使用AI改进内容识别和分类

该模型为邮件系统提供了丰富的链接预览功能，增强了消息的视觉效果和用户体验，是现代通讯应用的重要特性。

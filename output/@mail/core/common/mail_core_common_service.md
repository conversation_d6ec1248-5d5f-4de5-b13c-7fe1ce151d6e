# Mail Core Common Service - 邮件核心通用服务

## 概述

`mail_core_common_service.js` 实现了 Odoo 邮件系统的核心通用服务，负责处理邮件系统的基础事件监听和数据同步。该服务作为邮件系统与总线服务之间的桥梁，监听服务器端的实时通知并更新本地数据状态，确保邮件系统的数据一致性。

## 文件信息
- **路径**: `/mail/static/src/core/common/mail_core_common_service.js`
- **行数**: 77
- **模块**: `@mail/core/common/mail_core_common_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'           // OWL 框架（reactive）
'@web/core/registry'  // 服务注册表
```

## 类定义

### MailCoreCommon 类

邮件核心通用服务的主要实现类：

```javascript
class MailCoreCommon {
    /**
     * @param {import("@web/env").OdooEnv} env
     * @param {Partial<import("services").Services>} services
     */
    constructor(env, services) {
        this.env = env;
        this.busService = services.bus_service;
        this.store = services["mail.store"];
    }
}
```

### 构造函数参数

- **`env`**: Odoo 环境对象，提供全局上下文
- **`services`**: 服务集合，包含依赖的服务实例

### 核心属性

```javascript
this.env;           // Odoo 环境对象
this.busService;    // 总线服务实例
this.store;         // 邮件存储服务实例
```

## 核心功能

### setup() 方法

服务初始化方法，设置所有必要的事件监听器：

```javascript
setup() {
    // 设置各种总线事件监听器
    this.busService.subscribe("ir.attachment/delete", ...);
    this.busService.subscribe("mail.message/delete", ...);
    this.busService.subscribe("mail.message/toggle_star", ...);
    this.busService.subscribe("res.users.settings", ...);
    this.busService.subscribe("mail.record/insert", ...);
}
```

## 事件监听器

### 1. 附件删除事件

```javascript
this.busService.subscribe("ir.attachment/delete", (payload) => {
    const { id: attachmentId, message: messageData } = payload;
    
    // 如果有消息数据，先更新消息
    if (messageData) {
        this.store.Message.insert(messageData);
    }
    
    // 删除附件
    const attachment = this.store.Attachment.get(attachmentId);
    attachment?.delete();
});
```

**功能说明**:
- 监听服务器端的附件删除事件
- 同步更新相关消息数据
- 从本地存储中删除附件记录
- 使用可选链操作符避免空引用错误

**事件流程**:
1. 服务器端删除附件
2. 发送总线通知
3. 客户端接收通知
4. 更新本地消息数据（如果有）
5. 删除本地附件记录

### 2. 消息删除事件

```javascript
this.busService.subscribe("mail.message/delete", (payload, { id: notifId }) => {
    for (const messageId of payload.message_ids) {
        const message = this.store.Message.get(messageId);
        if (!message) {
            continue;
        }
        
        // 触发本地删除事件
        this.env.bus.trigger("mail.message/delete", { message, notifId });
        
        // 删除消息
        message.delete();
    }
});
```

**功能说明**:
- 处理批量消息删除
- 触发本地事件通知其他组件
- 从本地存储中删除消息记录
- 跳过不存在的消息

**事件流程**:
1. 服务器端删除消息
2. 发送包含消息ID列表的通知
3. 客户端遍历处理每个消息
4. 触发本地删除事件
5. 删除本地消息记录

### 3. 消息标星切换事件

```javascript
this.busService.subscribe("mail.message/toggle_star", (payload, metadata) =>
    this._handleNotificationToggleStar(payload, metadata)
);

_handleNotificationToggleStar(payload, metadata) {
    const { message_ids: messageIds, starred } = payload;
    this.store.Message.insert(messageIds.map((id) => ({ id, starred })));
}
```

**功能说明**:
- 处理消息标星状态的批量更新
- 支持同时更新多个消息的标星状态
- 使用 insert 方法更新现有消息记录

**数据格式**:
```javascript
// payload 结构
{
    message_ids: [123, 456, 789],  // 消息ID数组
    starred: true                  // 新的标星状态
}
```

### 4. 用户设置更新事件

```javascript
this.busService.subscribe("res.users.settings", (payload) => {
    if (payload) {
        this.store.settings.update(payload);
    }
});
```

**功能说明**:
- 监听用户设置变更
- 实时同步用户偏好设置
- 确保多端设置一致性

**设置类型**:
- 通知偏好
- 界面设置
- 邮件配置
- 其他用户偏好

### 5. 记录插入事件

```javascript
this.busService.subscribe("mail.record/insert", (payload) => {
    this.store.insert(payload, { html: true });
});
```

**功能说明**:
- 处理通用的记录插入事件
- 支持HTML内容的插入
- 用于同步各种类型的邮件记录

**插入选项**:
- `{ html: true }`: 表示内容包含HTML格式

## 服务注册

### 服务配置

```javascript
const mailCoreCommon = {
    dependencies: ["bus_service", "mail.store"],
    
    /**
     * @param {import("@web/env").OdooEnv} env
     * @param {Partial<import("services").Services>} services
     */
    start(env, services) {
        const mailCoreCommon = reactive(new MailCoreCommon(env, services));
        mailCoreCommon.setup();
        return mailCoreCommon;
    },
};

registry.category("services").add("mail.core.common", mailCoreCommon);
```

### 服务特性

1. **依赖声明**: 明确声明对总线服务和邮件存储的依赖
2. **响应式**: 使用 `reactive` 包装服务实例
3. **自动初始化**: 在 start 方法中自动调用 setup
4. **注册**: 注册到服务注册表中

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听总线事件并响应
- 实现松耦合的事件处理

### 2. 中介者模式 (Mediator Pattern)
- 作为总线服务和邮件存储之间的中介
- 协调不同组件间的通信

### 3. 适配器模式 (Adapter Pattern)
- 将总线事件适配为邮件系统的数据操作
- 转换不同格式的数据

## 事件处理流程

### 典型事件流程

```mermaid
sequenceDiagram
    participant Server as 服务器
    participant Bus as 总线服务
    participant Service as 邮件核心服务
    participant Store as 邮件存储
    participant UI as 用户界面

    Server->>Bus: 发送事件通知
    Bus->>Service: 触发事件监听器
    Service->>Store: 更新本地数据
    Store->>UI: 触发响应式更新
```

### 数据同步机制

1. **服务器变更**: 服务器端数据发生变化
2. **事件发送**: 通过总线发送变更通知
3. **客户端接收**: 邮件核心服务接收事件
4. **数据更新**: 更新本地数据存储
5. **界面刷新**: 响应式系统自动更新UI

## 错误处理

### 安全操作

```javascript
// 使用可选链避免空引用
attachment?.delete();

// 检查数据存在性
if (!message) {
    continue;
}

// 条件检查
if (payload) {
    this.store.settings.update(payload);
}
```

### 容错机制

- 跳过不存在的记录
- 使用可选链操作符
- 条件检查避免错误

## 性能优化

### 1. 批量处理

```javascript
// 批量更新消息标星状态
this.store.Message.insert(messageIds.map((id) => ({ id, starred })));
```

### 2. 事件过滤

```javascript
// 只处理存在的消息
if (!message) {
    continue;
}
```

### 3. 响应式优化

```javascript
// 使用 reactive 包装提供响应式能力
const mailCoreCommon = reactive(new MailCoreCommon(env, services));
```

## 使用场景

### 1. 实时数据同步

- 多用户协作时的数据同步
- 跨设备的状态一致性
- 服务器端变更的实时反映

### 2. 事件驱动更新

- 附件删除后的界面更新
- 消息状态变更的同步
- 用户设置的实时应用

### 3. 系统集成

- 与其他 Odoo 模块的集成
- 第三方系统的事件处理
- 插件和扩展的事件支持

## 注意事项

1. **事件顺序**: 确保事件处理的正确顺序
2. **数据一致性**: 维护本地和服务器数据的一致性
3. **错误恢复**: 处理网络中断等异常情况
4. **内存管理**: 及时清理不再需要的事件监听器

## 扩展建议

1. **事件缓存**: 实现离线时的事件缓存机制
2. **重试机制**: 添加失败事件的重试逻辑
3. **事件过滤**: 支持更细粒度的事件过滤
4. **性能监控**: 添加事件处理性能监控
5. **调试工具**: 提供事件调试和日志功能

该服务是邮件系统实时性的核心保障，确保了用户在多端使用时的数据一致性和实时性体验。

# Message Reactions Model - 消息反应模型

## 概述

`message_reactions_model.js` 定义了 Odoo 邮件系统中的消息反应模型类，用于管理用户对消息的表情反应。该模型处理表情符号的添加、移除、计数和用户列表，支持多用户对同一消息的不同表情反应，是现代聊天系统中重要的情感表达功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_reactions_model.js`
- **行数**: 50
- **模块**: `@mail/core/common/message_reactions_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // Record 基类（AND 操作符）
'@web/core/network/rpc'        // RPC 网络请求
```

## 类定义

### MessageReactions 类

```javascript
class MessageReactions extends Record {
    static id = AND("message", "content");  // 复合主键：消息 + 表情内容
    static records = {};                    // 静态记录集合
}
```

### 复合主键

使用 `AND("message", "content")` 作为主键，表示：
- 同一条消息的同一个表情只有一个反应记录
- 不同消息或不同表情可以有独立的反应记录
- 确保反应的唯一性

## 核心属性

### 基础属性

```javascript
content: string;                // 表情符号内容
count: number;                  // 反应数量
sequence: number;               // 显示顺序
```

### 关系属性

```javascript
personas = Record.many("Persona");     // 反应的用户列表
message = Record.one("Message");       // 关联的消息
```

### 属性详细说明

#### 1. content (表情内容)
- **类型**: `string`
- **用途**: 表情符号的Unicode字符
- **示例**: `"👍"`, `"❤️"`, `"😂"`, `"😮"`

#### 2. count (反应数量)
- **类型**: `number`
- **用途**: 该表情的反应总数
- **计算**: 通常等于 `personas.length`

#### 3. sequence (显示顺序)
- **类型**: `number`
- **用途**: 表情在消息中的显示顺序
- **排序**: 数值越小越靠前显示

#### 4. personas (反应用户)
- **类型**: Persona 模型的多对多关系
- **用途**: 添加了该表情反应的用户列表
- **功能**: 支持查看谁添加了反应

#### 5. message (关联消息)
- **类型**: Message 模型的一对一关系
- **用途**: 被反应的消息对象
- **功能**: 确定反应属于哪条消息

## 核心方法

### remove() - 移除反应

```javascript
async remove() {
    this.store.insert(
        await rpc(
            "/mail/message/reaction",
            {
                action: "remove",
                content: this.content,
                message_id: this.message.id,
                ...this.message.thread.rpcParams,
            },
            { silent: true }
        )
    );
}
```

**移除流程**:
1. **服务器调用**: 调用 `/mail/message/reaction` 端点
2. **参数传递**:
   - `action`: "remove" 操作类型
   - `content`: 要移除的表情内容
   - `message_id`: 消息ID
   - `...rpcParams`: 线程相关参数
3. **静默模式**: 使用 `silent: true` 避免显示加载状态
4. **数据更新**: 将服务器返回的数据插入本地存储

## 使用场景

### 1. 显示消息反应

```javascript
// 在消息组件中显示反应
const renderMessageReactions = (message) => {
    return message.reactions.map(reaction => ({
        content: reaction.content,
        count: reaction.count,
        users: reaction.personas.map(p => p.name),
        hasCurrentUser: reaction.personas.some(p => p.eq(store.self))
    }));
};
```

### 2. 添加表情反应

```javascript
// 添加新的表情反应
const addReaction = async (message, emoji) => {
    try {
        const response = await rpc("/mail/message/reaction", {
            action: "add",
            content: emoji,
            message_id: message.id,
            ...message.thread.rpcParams
        });
        
        // 更新本地数据
        store.insert(response);
        
    } catch (error) {
        console.error('添加反应失败:', error);
    }
};
```

### 3. 切换反应状态

```javascript
// 切换用户的反应状态
const toggleReaction = async (message, emoji) => {
    const existingReaction = message.reactions.find(r => r.content === emoji);
    const currentUser = store.self;
    
    if (existingReaction && existingReaction.personas.includes(currentUser)) {
        // 用户已经添加了这个反应，移除它
        if (existingReaction.count === 1) {
            // 如果只有当前用户，移除整个反应
            await existingReaction.remove();
        } else {
            // 否则只移除当前用户
            await removeUserReaction(existingReaction, currentUser);
        }
    } else {
        // 用户还没有添加这个反应，添加它
        await addReaction(message, emoji);
    }
};
```

### 4. 反应统计

```javascript
// 统计消息的反应信息
const getReactionStats = (message) => {
    const reactions = message.reactions;
    
    return {
        totalReactions: reactions.reduce((sum, r) => sum + r.count, 0),
        uniqueEmojis: reactions.length,
        mostPopular: reactions.sort((a, b) => b.count - a.count)[0],
        userReactions: reactions.filter(r => 
            r.personas.some(p => p.eq(store.self))
        )
    };
};
```

## 反应管理

### 1. 反应排序

```javascript
// 按序列号排序反应
const sortReactions = (reactions) => {
    return reactions.sort((a, b) => a.sequence - b.sequence);
};

// 按受欢迎程度排序
const sortByPopularity = (reactions) => {
    return reactions.sort((a, b) => b.count - a.count);
};
```

### 2. 反应过滤

```javascript
// 获取当前用户的反应
const getCurrentUserReactions = (message) => {
    return message.reactions.filter(reaction =>
        reaction.personas.some(persona => persona.eq(store.self))
    );
};

// 获取特定表情的反应
const getReactionByEmoji = (message, emoji) => {
    return message.reactions.find(reaction => reaction.content === emoji);
};
```

### 3. 反应验证

```javascript
// 验证表情符号是否有效
const isValidEmoji = (content) => {
    // 简单的表情符号验证
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
    return emojiRegex.test(content);
};

// 检查用户是否可以添加反应
const canAddReaction = (message, user) => {
    return message.thread.hasReadAccess && !message.isTransient;
};
```

## 数据同步

### 1. 服务器同步

```javascript
// 同步反应数据到服务器
const syncReactionToServer = async (action, messageId, content, threadParams) => {
    return await rpc("/mail/message/reaction", {
        action: action,           // "add" 或 "remove"
        content: content,         // 表情符号
        message_id: messageId,    // 消息ID
        ...threadParams          // 线程参数
    }, { silent: true });
};
```

### 2. 实时更新

```javascript
// 监听反应变化
const watchReactionChanges = (message) => {
    message.reactions.forEach(reaction => {
        reaction.addEventListener('change', (event) => {
            if (event.field === 'count') {
                updateReactionDisplay(reaction);
            }
        });
    });
};
```

### 3. 批量操作

```javascript
// 批量处理反应操作
const batchReactionOperations = async (operations) => {
    const promises = operations.map(op => 
        syncReactionToServer(op.action, op.messageId, op.content, op.threadParams)
    );
    
    try {
        const results = await Promise.all(promises);
        results.forEach(result => store.insert(result));
    } catch (error) {
        console.error('批量反应操作失败:', error);
    }
};
```

## UI 集成

### 1. 反应按钮

```javascript
// 反应按钮组件
const ReactionButton = ({ reaction, onToggle }) => {
    const hasCurrentUser = reaction.personas.some(p => p.eq(store.self));
    
    return `
        <button 
            class="reaction-btn ${hasCurrentUser ? 'active' : ''}"
            onclick="${() => onToggle(reaction.content)}"
            title="${reaction.personas.map(p => p.name).join(', ')}"
        >
            <span class="emoji">${reaction.content}</span>
            <span class="count">${reaction.count}</span>
        </button>
    `;
};
```

### 2. 反应选择器

```javascript
// 表情选择器集成
const openEmojiPicker = (message) => {
    showEmojiPicker({
        onSelect: (emoji) => {
            toggleReaction(message, emoji);
        },
        position: 'bottom'
    });
};
```

### 3. 反应列表

```javascript
// 反应用户列表
const ReactionUserList = ({ reaction }) => {
    return `
        <div class="reaction-users">
            <div class="emoji-large">${reaction.content}</div>
            <div class="user-list">
                ${reaction.personas.map(persona => `
                    <div class="user-item">
                        <img src="${persona.avatarUrl}" alt="${persona.name}" />
                        <span>${persona.name}</span>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
};
```

## 性能优化

### 1. 反应缓存

```javascript
// 缓存反应数据
const reactionCache = new Map();

const getCachedReactions = (messageId) => {
    if (reactionCache.has(messageId)) {
        return reactionCache.get(messageId);
    }
    
    const reactions = message.reactions;
    reactionCache.set(messageId, reactions);
    return reactions;
};
```

### 2. 防抖操作

```javascript
// 防抖反应操作
const debouncedToggleReaction = debounce((message, emoji) => {
    toggleReaction(message, emoji);
}, 300);
```

### 3. 虚拟化

```javascript
// 大量反应时的虚拟化显示
const VirtualizedReactionList = ({ reactions, maxVisible = 10 }) => {
    const visibleReactions = reactions.slice(0, maxVisible);
    const hiddenCount = reactions.length - maxVisible;
    
    return {
        visible: visibleReactions,
        hasMore: hiddenCount > 0,
        moreCount: hiddenCount
    };
};
```

## 设计模式

### 1. 聚合模式 (Aggregate Pattern)
- 将同一表情的多个用户反应聚合为一个记录
- 提供统一的反应视图

### 2. 命令模式 (Command Pattern)
- 反应的添加和移除操作封装为命令
- 支持撤销和重做

### 3. 观察者模式 (Observer Pattern)
- 监听反应数据变化
- 自动更新UI显示

## 注意事项

1. **数据一致性**: 确保反应数量与用户列表一致
2. **权限控制**: 检查用户是否有权限添加反应
3. **性能考虑**: 大量反应时的渲染性能
4. **用户体验**: 提供即时的视觉反馈

## 扩展建议

1. **自定义表情**: 支持自定义表情符号
2. **反应动画**: 添加反应的动画效果
3. **反应历史**: 记录反应的历史变化
4. **反应统计**: 提供反应的统计分析
5. **批量反应**: 支持批量添加/移除反应

该模型为邮件系统提供了完整的消息反应功能，增强了用户间的情感交流和互动体验，是现代通讯应用的重要特性。

# Sound Effects Service - 音效服务

## 概述

`sound_effects_service.js` 实现了 Odoo 邮件系统中的音效服务，为各种邮件和通话事件提供音频反馈。该服务管理多种音效文件，支持音量控制、循环播放、格式自适应等功能，增强了用户的交互体验，是现代通讯应用中重要的用户反馈机制。

## 文件信息
- **路径**: `/mail/static/src/core/common/sound_effects_service.js`
- **行数**: 99
- **模块**: `@mail/core/common/sound_effects_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/browser'    // 浏览器工具
'@web/core/registry'           // 服务注册表
'@web/core/utils/urls'         // URL 工具
```

## 类定义

### SoundEffects 类

```javascript
class SoundEffects {
    constructor(env) {
        this.soundEffects = {
            // 音效配置对象
        };
    }
}
```

## 音效配置

### 内置音效列表

```javascript
this.soundEffects = {
    "channel-join": { 
        defaultVolume: 0.3, 
        path: "/mail/static/src/audio/channel_01_in" 
    },
    "channel-leave": { 
        path: "/mail/static/src/audio/channel_04_out" 
    },
    "deafen": { 
        defaultVolume: 0.15, 
        path: "/mail/static/src/audio/deafen_new_01" 
    },
    "incoming-call": { 
        defaultVolume: 0.15, 
        path: "/mail/static/src/audio/call_02_in_" 
    },
    "member-leave": { 
        defaultVolume: 0.5, 
        path: "/mail/static/src/audio/channel_01_out" 
    },
    "mute": { 
        defaultVolume: 0.2, 
        path: "/mail/static/src/audio/mute_1" 
    },
    "new-message": { 
        path: "/mail/static/src/audio/dm_02" 
    },
    "push-to-talk-on": { 
        defaultVolume: 0.05, 
        path: "/mail/static/src/audio/ptt_push_1" 
    },
    "push-to-talk-off": {
        defaultVolume: 0.05,
        path: "/mail/static/src/audio/ptt_release_1",
    },
    "screen-sharing": { 
        defaultVolume: 0.5, 
        path: "/mail/static/src/audio/share_02" 
    },
    "undeafen": { 
        defaultVolume: 0.15, 
        path: "/mail/static/src/audio/undeafen_new_01" 
    },
    "unmute": { 
        defaultVolume: 0.2, 
        path: "/mail/static/src/audio/unmute_1" 
    },
};
```

### 音效分类

#### 1. 频道相关音效
- **`channel-join`**: 加入频道音效 (音量: 0.3)
- **`channel-leave`**: 离开频道音效 (默认音量)
- **`member-leave`**: 成员离开音效 (音量: 0.5)

#### 2. 通话相关音效
- **`incoming-call`**: 来电音效 (音量: 0.15)
- **`mute`**: 静音音效 (音量: 0.2)
- **`unmute`**: 取消静音音效 (音量: 0.2)
- **`deafen`**: 屏蔽音效 (音量: 0.15)
- **`undeafen`**: 取消屏蔽音效 (音量: 0.15)

#### 3. 交互相关音效
- **`new-message`**: 新消息音效 (默认音量)
- **`push-to-talk-on`**: 按键通话开启音效 (音量: 0.05)
- **`push-to-talk-off`**: 按键通话关闭音效 (音量: 0.05)
- **`screen-sharing`**: 屏幕共享音效 (音量: 0.5)

## 核心方法

### 1. 播放音效

```javascript
play(soundEffectName, { loop = false, volume } = {}) {
    // 检查浏览器音频支持
    if (typeof browser.Audio === "undefined") {
        return;
    }
    
    // 获取音效配置
    const soundEffect = this.soundEffects[soundEffectName];
    if (!soundEffect) {
        return;
    }
    
    // 创建音频对象（懒加载）
    if (!soundEffect.audio) {
        const audio = new browser.Audio();
        const ext = audio.canPlayType("audio/ogg; codecs=vorbis") ? ".ogg" : ".mp3";
        audio.src = url(soundEffect.path + ext);
        soundEffect.audio = audio;
    }
    
    // 停止当前播放
    if (!soundEffect.audio.paused) {
        soundEffect.audio.pause();
    }
    
    // 设置播放参数
    soundEffect.audio.currentTime = 0;
    soundEffect.audio.loop = loop;
    soundEffect.audio.volume = volume ?? soundEffect.defaultVolume ?? 1;
    
    // 播放音效
    Promise.resolve(soundEffect.audio.play()).catch(() => {});
}
```

**播放流程**:
1. 检查浏览器音频支持
2. 验证音效名称的有效性
3. 懒加载音频文件（支持格式自适应）
4. 停止当前播放并重置
5. 设置播放参数（循环、音量）
6. 开始播放并处理错误

### 2. 停止音效

```javascript
stop(soundEffectName) {
    const soundEffect = this.soundEffects[soundEffectName];
    if (soundEffect) {
        // 停止指定音效
        if (soundEffect.audio) {
            soundEffect.audio.pause();
            soundEffect.audio.currentTime = 0;
        }
    } else {
        // 停止所有音效
        for (const soundEffect of Object.values(this.soundEffects)) {
            if (soundEffect.audio) {
                soundEffect.audio.pause();
                soundEffect.audio.currentTime = 0;
            }
        }
    }
}
```

**停止逻辑**:
- **指定音效**: 停止特定的音效播放
- **全部音效**: 如果不指定名称，停止所有音效
- **重置状态**: 将播放位置重置到开头

## 使用场景

### 1. 消息通知音效

```javascript
// 新消息到达时播放音效
const playNewMessageSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('new-message');
};

// 自定义音量的新消息音效
const playNewMessageSoundCustom = (volume = 0.8) => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('new-message', { volume });
};
```

### 2. 通话状态音效

```javascript
// 静音/取消静音音效
const toggleMuteSound = (isMuted) => {
    const soundService = env.services['mail.sound_effects'];
    
    if (isMuted) {
        soundService.play('mute');
    } else {
        soundService.play('unmute');
    }
};

// 来电音效（循环播放）
const playIncomingCallSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('incoming-call', { loop: true });
};

// 停止来电音效
const stopIncomingCallSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.stop('incoming-call');
};
```

### 3. 频道操作音效

```javascript
// 加入频道音效
const playChannelJoinSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('channel-join');
};

// 离开频道音效
const playChannelLeaveSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('channel-leave');
};

// 成员离开音效
const playMemberLeaveSound = () => {
    const soundService = env.services['mail.sound_effects'];
    soundService.play('member-leave');
};
```

### 4. 按键通话音效

```javascript
// 按键通话音效管理
class PushToTalkSounds {
    constructor(soundService) {
        this.soundService = soundService;
    }
    
    onPushToTalkStart() {
        this.soundService.play('push-to-talk-on');
    }
    
    onPushToTalkEnd() {
        this.soundService.play('push-to-talk-off');
    }
}

// 使用示例
const pttSounds = new PushToTalkSounds(env.services['mail.sound_effects']);

// 监听按键事件
document.addEventListener('keydown', (event) => {
    if (event.code === 'Space' && !event.repeat) {
        pttSounds.onPushToTalkStart();
    }
});

document.addEventListener('keyup', (event) => {
    if (event.code === 'Space') {
        pttSounds.onPushToTalkEnd();
    }
});
```

## 音频格式支持

### 1. 格式检测

```javascript
// 自动检测支持的音频格式
const detectAudioFormat = (audio) => {
    if (audio.canPlayType("audio/ogg; codecs=vorbis")) {
        return ".ogg";
    } else {
        return ".mp3";
    }
};
```

### 2. 格式优先级

```javascript
// 音频格式优先级
const AUDIO_FORMATS = [
    { ext: ".ogg", mime: "audio/ogg; codecs=vorbis" },
    { ext: ".mp3", mime: "audio/mpeg" },
    { ext: ".wav", mime: "audio/wav" }
];

const getBestAudioFormat = (audio) => {
    for (const format of AUDIO_FORMATS) {
        if (audio.canPlayType(format.mime)) {
            return format.ext;
        }
    }
    return ".mp3"; // 默认格式
};
```

## 音效管理

### 1. 音效预加载

```javascript
// 预加载常用音效
const preloadSoundEffects = (soundService) => {
    const commonSounds = [
        'new-message',
        'mute',
        'unmute',
        'channel-join'
    ];
    
    commonSounds.forEach(soundName => {
        // 触发懒加载但不播放
        const soundEffect = soundService.soundEffects[soundName];
        if (soundEffect && !soundEffect.audio) {
            const audio = new Audio();
            const ext = audio.canPlayType("audio/ogg; codecs=vorbis") ? ".ogg" : ".mp3";
            audio.src = url(soundEffect.path + ext);
            audio.preload = 'auto';
            soundEffect.audio = audio;
        }
    });
};
```

### 2. 音效缓存管理

```javascript
// 音效缓存管理
class SoundEffectCache {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = 10;
    }
    
    get(soundName) {
        return this.cache.get(soundName);
    }
    
    set(soundName, audio) {
        if (this.cache.size >= this.maxCacheSize) {
            // 移除最旧的缓存
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(soundName, audio);
    }
    
    clear() {
        this.cache.clear();
    }
}
```

### 3. 音量控制

```javascript
// 全局音量控制
class VolumeController {
    constructor() {
        this.masterVolume = 1.0;
        this.categoryVolumes = {
            notification: 0.8,
            call: 0.6,
            interaction: 0.4
        };
    }
    
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
    }
    
    setCategoryVolume(category, volume) {
        this.categoryVolumes[category] = Math.max(0, Math.min(1, volume));
    }
    
    calculateVolume(soundName, baseVolume) {
        const category = this.getSoundCategory(soundName);
        const categoryVolume = this.categoryVolumes[category] || 1;
        return baseVolume * categoryVolume * this.masterVolume;
    }
    
    getSoundCategory(soundName) {
        if (soundName.includes('message')) return 'notification';
        if (soundName.includes('call') || soundName.includes('mute')) return 'call';
        return 'interaction';
    }
}
```

## 用户偏好设置

### 1. 音效开关

```javascript
// 音效偏好设置
class SoundPreferences {
    constructor() {
        this.enabled = this.loadPreference('soundEnabled', true);
        this.volume = this.loadPreference('soundVolume', 0.8);
        this.enabledSounds = this.loadPreference('enabledSounds', {});
    }
    
    loadPreference(key, defaultValue) {
        const stored = localStorage.getItem(`mail_sound_${key}`);
        return stored ? JSON.parse(stored) : defaultValue;
    }
    
    savePreference(key, value) {
        localStorage.setItem(`mail_sound_${key}`, JSON.stringify(value));
    }
    
    isSoundEnabled(soundName) {
        return this.enabled && (this.enabledSounds[soundName] !== false);
    }
    
    setSoundEnabled(soundName, enabled) {
        this.enabledSounds[soundName] = enabled;
        this.savePreference('enabledSounds', this.enabledSounds);
    }
}
```

### 2. 增强的播放方法

```javascript
// 带偏好设置的播放方法
const playWithPreferences = (soundService, preferences, soundName, options = {}) => {
    if (!preferences.isSoundEnabled(soundName)) {
        return;
    }
    
    const volume = options.volume || (preferences.volume * (soundService.soundEffects[soundName].defaultVolume || 1));
    
    soundService.play(soundName, {
        ...options,
        volume
    });
};
```

## 错误处理

### 1. 播放错误处理

```javascript
// 增强的播放方法
const safePlay = async (audio) => {
    try {
        await audio.play();
    } catch (error) {
        if (error.name === 'NotAllowedError') {
            console.warn('音频播放被阻止，需要用户交互');
        } else if (error.name === 'NotSupportedError') {
            console.warn('音频格式不支持');
        } else {
            console.error('音频播放失败:', error);
        }
    }
};
```

### 2. 音频加载错误

```javascript
// 音频加载错误处理
const loadAudioWithFallback = (soundEffect) => {
    const audio = new Audio();
    
    audio.addEventListener('error', () => {
        console.warn(`音频加载失败: ${soundEffect.path}`);
        // 尝试备用格式
        const currentExt = audio.src.includes('.ogg') ? '.ogg' : '.mp3';
        const fallbackExt = currentExt === '.ogg' ? '.mp3' : '.ogg';
        audio.src = url(soundEffect.path + fallbackExt);
    });
    
    return audio;
};
```

## 性能优化

### 1. 懒加载策略

```javascript
// 智能懒加载
const smartLazyLoad = (soundEffect, priority = 'low') => {
    if (priority === 'high') {
        // 立即加载
        loadAudio(soundEffect);
    } else {
        // 延迟加载
        setTimeout(() => loadAudio(soundEffect), 1000);
    }
};
```

### 2. 内存管理

```javascript
// 音频对象清理
const cleanupAudioObjects = (soundEffects) => {
    Object.values(soundEffects).forEach(soundEffect => {
        if (soundEffect.audio) {
            soundEffect.audio.pause();
            soundEffect.audio.src = '';
            soundEffect.audio = null;
        }
    });
};
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 全局唯一的音效服务实例
- 统一的音效管理

### 2. 工厂模式 (Factory Pattern)
- 动态创建音频对象
- 格式自适应

### 3. 策略模式 (Strategy Pattern)
- 不同音效的不同播放策略
- 格式选择策略

## 注意事项

1. **浏览器限制**: 处理自动播放策略限制
2. **性能影响**: 避免同时播放过多音效
3. **用户偏好**: 尊重用户的音效设置
4. **网络优化**: 合理管理音频文件的加载

## 扩展建议

1. **自定义音效**: 支持用户上传自定义音效
2. **音效主题**: 提供不同的音效主题包
3. **空间音效**: 支持3D空间音效
4. **音效可视化**: 提供音效播放的可视化反馈
5. **智能音量**: 根据环境自动调节音量

该服务为邮件系统提供了丰富的音频反馈功能，增强了用户的交互体验和应用的专业性。

# Picker - 选择器组件

## 概述

`picker.js` 实现了 Odoo 邮件系统中的选择器组件和钩子，主要用于表情符号和GIF的选择。该组件提供了响应式的用户界面，在大屏幕上使用弹出框模式，在移动端使用键盘模式，为用户提供便捷的内容选择体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/picker.js`
- **行数**: 171
- **模块**: `@mail/core/common/picker`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架
'@web/core/browser/browser'           // 浏览器工具
'@web/core/utils/misc'                // 工具函数
'@web/core/popover/popover_hook'      // 弹出框钩子
'@web/core/utils/hooks'               // Web 核心钩子
'@mail/core/common/picker_content'    // 选择器内容组件
'@mail/utils/common/hooks'            // 邮件系统钩子
```

## 选择器类型

### PICKERS 常量

```javascript
const PICKERS = {
    NONE: "none",    // 无选择器
    EMOJI: "emoji",  // 表情符号选择器
    GIF: "gif",      // GIF 选择器
};
```

## usePicker 钩子

### 钩子函数

```javascript
function usePicker(setting) {
    const storeScroll = {
        scrollValue: 0,
        set: (value) => (storeScroll.scrollValue = value),
        get: () => storeScroll.scrollValue,
    };
    
    return useState({
        PICKERS,                    // 选择器类型常量
        anchor: setting.anchor,     // 锚点元素
        buttons: setting.buttons,   // 触发按钮
        close: setting.close,       // 关闭回调
        pickers: setting.pickers,   // 选择器配置
        position: setting.position, // 弹出位置
        state: {
            picker: PICKERS.NONE,   // 当前选择器类型
            searchTerm: "",         // 搜索词
        },
        storeScroll,               // 滚动状态存储
    });
}
```

### 配置参数

#### setting 对象

```typescript
interface PickerSetting {
    anchor?: HTMLElement;                    // 锚点元素
    buttons: HTMLElement[];                  // 触发按钮数组
    close?: () => void;                     // 关闭回调函数
    pickers: Record<string, (str: string) => void>;  // 选择器回调映射
    position?: string;                      // 弹出位置
}
```

### 滚动状态管理

```javascript
const storeScroll = {
    scrollValue: 0,                         // 当前滚动位置
    set: (value) => (storeScroll.scrollValue = value),  // 设置滚动位置
    get: () => storeScroll.scrollValue,     // 获取滚动位置
};
```

**功能说明**:
- 保存选择器的滚动位置
- 在选择器重新打开时恢复位置
- 提供更好的用户体验

## Picker 组件

### 组件定义

```javascript
class Picker extends Component {
    static components = {
        PickerContent,  // 选择器内容组件
    };
    static template = "mail.Picker";
}
```

### Props 配置

```javascript
static props = [
    "PICKERS",      // 选择器类型常量
    "anchor?",      // 锚点元素（可选）
    "buttons",      // 触发按钮数组
    "close?",       // 关闭回调（可选）
    "state",        // 选择器状态
    "pickers",      // 选择器回调映射
    "position?",    // 弹出位置（可选）
    "storeScroll",  // 滚动状态存储
    "fixed?",       // 是否固定位置（可选）
];
```

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.ui = useState(useService("ui"));
    this.popover = usePopover(PickerContent, this.popoverSettings);
    
    // 全局点击监听
    useExternalListener(browser, "click", async (ev) => {
        if (this.props.state.picker === this.props.PICKERS.NONE) {
            return;
        }
        await new Promise(setTimeout); // 等待事件冒泡处理
        if (!this.isEventHandledByPicker(ev)) {
            this.close();
        }
    }, true);
    
    // 按钮点击监听
    for (const button of this.props.buttons) {
        useLazyExternalListener(
            () => button.el,
            "click",
            async (ev) => this.toggle(this.props.anchor?.el ?? button.el, ev)
        );
    }
}
```

### 2. 弹出框配置

```javascript
get popoverSettings() {
    return {
        position: this.props.position,      // 弹出位置
        fixedPosition: this.props.fixed,    // 是否固定位置
        onClose: () => this.close(),        // 关闭回调
        closeOnClickAway: false,            // 不自动关闭（手动控制）
        animation: false,                   // 禁用动画
        arrow: false,                       // 不显示箭头
    };
}
```

### 3. 内容属性

```javascript
get contentProps() {
    const pickers = {};
    for (const [name, fn] of Object.entries(this.props.pickers)) {
        pickers[name] = (str, resetOnSelect) => {
            fn(str);                        // 执行选择回调
            if (resetOnSelect) {
                this.close();               // 选择后关闭
            }
        };
    }
    
    return {
        PICKERS: this.props.PICKERS,
        close: () => this.close(),
        pickers,
        state: this.props.state,
        storeScroll: this.props.storeScroll,
    };
}
```

### 4. 事件处理检测

```javascript
isEventHandledByPicker(ev) {
    return (
        isEventHandled(ev, "Composer.onClickAddEmoji") ||
        isEventHandled(ev, "PickerContent.onClick")
    );
}
```

**功能说明**:
- 检测事件是否被选择器相关组件处理
- 避免误关闭选择器
- 支持事件标记机制

### 5. 切换逻辑

```javascript
async toggle(el, ev) {
    await new Promise(setTimeout); // 等待事件冒泡处理
    
    if (this.ui.isSmall) {
        // 小屏幕：切换键盘选择器
        if (this.props.state.picker === this.props.PICKERS.NONE) {
            this.props.state.picker = this.props.PICKERS.EMOJI;
        } else {
            this.props.state.picker = this.props.PICKERS.NONE;
        }
        return;
    }
    
    // 大屏幕：切换弹出框
    if (isEventHandled(ev, "Composer.onClickAddEmoji")) {
        if (this.popover.isOpen) {
            if (this.props.state.picker === this.props.PICKERS.EMOJI) {
                this.props.state.picker = this.props.PICKERS.NONE;
                this.popover.close();
                return;
            }
            this.props.state.picker = this.props.PICKERS.EMOJI;
        } else {
            this.props.state.picker = this.props.PICKERS.EMOJI;
            this.popover.open(el, this.contentProps);
        }
    }
}
```

### 6. 关闭功能

```javascript
close() {
    this.props.close?.();                           // 执行外部关闭回调
    this.popover.close();                           // 关闭弹出框
    this.props.state.picker = this.props.PICKERS.NONE;  // 重置选择器状态
    this.props.state.searchTerm = "";               // 清空搜索词
}
```

## 响应式设计

### 双模式支持

#### 大屏幕模式（弹出框）
- 使用 `usePopover` 钩子
- 在触发按钮附近显示弹出框
- 支持位置定制和固定定位

#### 小屏幕模式（键盘）
- 直接在组件位置显示选择器
- 替代虚拟键盘的位置
- 提供触摸友好的界面

### 自动切换

```javascript
if (this.ui.isSmall) {
    // 移动端逻辑
} else {
    // 桌面端逻辑
}
```

## 使用场景

### 1. 消息编辑器中的表情选择

```javascript
// 在编辑器组件中使用
const picker = usePicker({
    buttons: [emojiButton],
    pickers: {
        emoji: (emoji) => this.addEmoji(emoji)
    },
    position: "top-end",
    close: () => {
        if (!this.ui.isSmall) {
            this.composer.autofocus++;
        }
    }
});
```

### 2. 聊天窗口中的GIF选择

```javascript
const picker = usePicker({
    buttons: [gifButton],
    pickers: {
        gif: (gifUrl) => this.sendGif(gifUrl)
    },
    position: "bottom-start"
});
```

### 3. 多类型选择器

```javascript
const picker = usePicker({
    buttons: [emojiButton, gifButton],
    pickers: {
        emoji: (emoji) => this.addEmoji(emoji),
        gif: (gifUrl) => this.sendGif(gifUrl)
    },
    anchor: containerElement
});
```

## 状态管理

### 选择器状态

```javascript
state: {
    picker: PICKERS.NONE,  // 当前活跃的选择器类型
    searchTerm: "",        // 搜索关键词
}
```

### 滚动位置保持

```javascript
// 保存滚动位置
storeScroll.set(scrollTop);

// 恢复滚动位置
element.scrollTop = storeScroll.get();
```

## 事件处理

### 全局点击处理

```javascript
// 点击外部区域关闭选择器
useExternalListener(browser, "click", async (ev) => {
    if (!this.isEventHandledByPicker(ev)) {
        this.close();
    }
}, true);
```

### 按钮点击处理

```javascript
// 懒加载按钮监听器
useLazyExternalListener(
    () => button.el,
    "click",
    async (ev) => this.toggle(anchor, ev)
);
```

## 性能优化

### 1. 懒加载监听器

```javascript
// 只在按钮元素存在时添加监听器
useLazyExternalListener(() => button.el, "click", handler);
```

### 2. 事件防抖

```javascript
// 等待事件冒泡完成
await new Promise(setTimeout);
```

### 3. 条件渲染

```javascript
// 只在需要时渲染选择器内容
if (this.props.state.picker !== this.props.PICKERS.NONE) {
    // 渲染选择器内容
}
```

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 根据屏幕尺寸选择不同的显示策略
- 弹出框模式 vs 键盘模式

### 2. 观察者模式 (Observer Pattern)
- 监听全局点击事件
- 响应状态变化更新UI

### 3. 工厂模式 (Factory Pattern)
- `usePicker` 钩子作为选择器实例工厂
- 根据配置创建不同的选择器

### 4. 命令模式 (Command Pattern)
- 选择器回调函数封装具体操作
- 支持不同类型的选择行为

## 注意事项

1. **事件处理**: 正确处理事件冒泡和标记
2. **内存管理**: 及时清理事件监听器
3. **响应式适配**: 确保在不同屏幕尺寸下正常工作
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **更多选择器类型**: 支持贴纸、文件等选择器
2. **自定义位置**: 更灵活的弹出位置配置
3. **动画效果**: 添加打开/关闭动画
4. **搜索优化**: 改进搜索算法和用户体验
5. **缓存机制**: 缓存常用的选择器内容

该组件为用户提供了直观便捷的内容选择体验，是邮件系统中重要的交互组件。

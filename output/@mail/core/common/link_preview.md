# Odoo 链接预览组件 (Link Preview) 学习资料

## 文件概述

**文件路径**: `output/@mail/core/common/link_preview.js`  
**原始路径**: `/mail/static/src/core/common/link_preview.js`  
**模块类型**: 核心公共组件 - 链接预览显示  
**代码行数**: 39 行  
**依赖关系**: 
- `@mail/core/common/link_preview_confirm_delete` - 链接预览删除确认组件
- `@odoo/owl` - OWL 框架
- `@web/core/utils/hooks` - 核心钩子

## 模块功能

链接预览组件是 Odoo 邮件系统中的链接预览显示组件。该模块提供了：
- 链接预览卡片显示
- 链接点击处理
- 预览删除功能
- 外部链接安全打开
- 用户交互管理
- 对话框服务集成

这个组件用于在消息中显示链接的预览信息，包括标题、描述、图片等，提升用户的链接浏览体验。

## 链接预览组件架构

### 核心组件结构
```
Link Preview Component
├── 预览显示引擎
│   ├── 预览卡片渲染
│   ├── 元数据显示
│   ├── 图片预览
│   └── 链接信息
├── 交互管理系统
│   ├── 点击处理
│   ├── 外部链接打开
│   ├── 安全检查
│   └── 用户反馈
├── 删除功能
│   ├── 删除按钮
│   ├── 确认对话框
│   ├── 删除处理
│   └── 状态更新
└── 服务集成
    ├── 对话框服务
    ├── 通知服务
    ├── 安全服务
    └── 分析服务
```

### 组件定义
```javascript
// 链接预览组件
class LinkPreview extends Component {
    static template = "mail.LinkPreview";
    static props = [
        "linkPreview",    // 链接预览数据
        "deletable"       // 是否可删除
    ];
    static components = {};
    
    setup() {
        this.dialogService = useService("dialog");
    }
    
    onClick() {
        // 点击处理：在新窗口中打开链接
        window.open(this.props.linkPreview.source_url, "_blank", "noreferrer");
    }
    
    onClickDelete() {
        // 删除点击处理：显示确认对话框
        this.dialogService.add(LinkPreviewConfirmDelete, {
            linkPreview: this.props.linkPreview,
        });
    }
}
```

## 核心功能详解

### 1. 链接预览数据管理器
```javascript
// 链接预览数据管理器
class LinkPreviewDataManager {
    constructor() {
        this.previewCache = new Map();
        this.metadataExtractor = new MetadataExtractor();
        this.securityValidator = new SecurityValidator();
        this.setupDataManagement();
    }
    
    setupDataManagement() {
        // 设置数据管理
        this.supportedDomains = new Set([
            'youtube.com', 'youtu.be',
            'twitter.com', 'x.com',
            'github.com',
            'linkedin.com',
            'facebook.com',
            'instagram.com',
            'medium.com',
            'stackoverflow.com'
        ]);
        
        this.previewTypes = {
            'video': {
                domains: ['youtube.com', 'youtu.be', 'vimeo.com'],
                template: 'VideoPreview',
                extractors: ['title', 'description', 'thumbnail', 'duration']
            },
            'social': {
                domains: ['twitter.com', 'x.com', 'facebook.com', 'instagram.com'],
                template: 'SocialPreview',
                extractors: ['title', 'description', 'author', 'avatar']
            },
            'article': {
                domains: ['medium.com', 'dev.to', 'hashnode.com'],
                template: 'ArticlePreview',
                extractors: ['title', 'description', 'author', 'publishDate', 'readTime']
            },
            'code': {
                domains: ['github.com', 'gitlab.com', 'bitbucket.org'],
                template: 'CodePreview',
                extractors: ['title', 'description', 'language', 'stars', 'forks']
            },
            'generic': {
                domains: ['*'],
                template: 'GenericPreview',
                extractors: ['title', 'description', 'image', 'siteName']
            }
        };
        
        this.cacheConfig = {
            maxSize: 100,
            maxAge: 24 * 60 * 60 * 1000, // 24小时
            cleanupInterval: 60 * 60 * 1000 // 1小时清理一次
        };
        
        this.setupCacheCleanup();
    }
    
    async generatePreview(url) {
        // 生成链接预览
        try {
            // 检查缓存
            const cached = this.getFromCache(url);
            if (cached) {
                return cached;
            }
            
            // 验证URL安全性
            if (!this.securityValidator.isUrlSafe(url)) {
                throw new Error('不安全的URL');
            }
            
            // 提取元数据
            const metadata = await this.metadataExtractor.extract(url);
            
            // 确定预览类型
            const previewType = this.determinePreviewType(url);
            
            // 生成预览数据
            const preview = this.buildPreviewData(metadata, previewType, url);
            
            // 缓存结果
            this.addToCache(url, preview);
            
            return preview;
            
        } catch (error) {
            console.error('生成链接预览失败:', error);
            return this.createErrorPreview(url, error);
        }
    }
    
    determinePreviewType(url) {
        // 确定预览类型
        try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname.toLowerCase();
            
            for (const [type, config] of Object.entries(this.previewTypes)) {
                if (config.domains.includes(domain) || config.domains.includes('*')) {
                    return type;
                }
            }
            
            return 'generic';
            
        } catch (error) {
            return 'generic';
        }
    }
    
    buildPreviewData(metadata, previewType, url) {
        // 构建预览数据
        const config = this.previewTypes[previewType];
        const preview = {
            id: this.generatePreviewId(url),
            source_url: url,
            preview_type: previewType,
            template: config.template,
            created_at: new Date().toISOString(),
            metadata: {}
        };
        
        // 提取指定的元数据字段
        config.extractors.forEach(field => {
            if (metadata[field]) {
                preview.metadata[field] = metadata[field];
            }
        });
        
        // 添加通用字段
        preview.title = metadata.title || this.extractTitleFromUrl(url);
        preview.description = metadata.description || '';
        preview.image_url = metadata.image || metadata.thumbnail || '';
        preview.site_name = metadata.siteName || this.extractDomainFromUrl(url);
        
        return preview;
    }
    
    generatePreviewId(url) {
        // 生成预览ID
        return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
    }
    
    extractTitleFromUrl(url) {
        // 从URL提取标题
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            
            // 移除文件扩展名和特殊字符
            const title = pathname
                .split('/')
                .pop()
                .replace(/\.[^/.]+$/, '')
                .replace(/[-_]/g, ' ')
                .replace(/\b\w/g, l => l.toUpperCase());
            
            return title || urlObj.hostname;
            
        } catch (error) {
            return 'Link Preview';
        }
    }
    
    extractDomainFromUrl(url) {
        // 从URL提取域名
        try {
            const urlObj = new URL(url);
            return urlObj.hostname;
        } catch (error) {
            return 'Unknown';
        }
    }
    
    createErrorPreview(url, error) {
        // 创建错误预览
        return {
            id: this.generatePreviewId(url),
            source_url: url,
            preview_type: 'error',
            template: 'ErrorPreview',
            title: 'Link Preview Error',
            description: `无法生成预览: ${error.message}`,
            image_url: '',
            site_name: this.extractDomainFromUrl(url),
            error: true,
            created_at: new Date().toISOString()
        };
    }
    
    getFromCache(url) {
        // 从缓存获取
        const cached = this.previewCache.get(url);
        
        if (cached && this.isCacheValid(cached)) {
            return cached.data;
        }
        
        return null;
    }
    
    addToCache(url, preview) {
        // 添加到缓存
        if (this.previewCache.size >= this.cacheConfig.maxSize) {
            this.cleanupCache();
        }
        
        this.previewCache.set(url, {
            data: preview,
            timestamp: Date.now()
        });
    }
    
    isCacheValid(cached) {
        // 检查缓存是否有效
        const age = Date.now() - cached.timestamp;
        return age < this.cacheConfig.maxAge;
    }
    
    setupCacheCleanup() {
        // 设置缓存清理
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    cleanupCache() {
        // 清理缓存
        const now = Date.now();
        const maxAge = this.cacheConfig.maxAge;
        
        for (const [url, cached] of this.previewCache.entries()) {
            if (now - cached.timestamp > maxAge) {
                this.previewCache.delete(url);
            }
        }
    }
    
    getCacheStats() {
        // 获取缓存统计
        return {
            size: this.previewCache.size,
            maxSize: this.cacheConfig.maxSize,
            hitRate: this.calculateHitRate()
        };
    }
    
    calculateHitRate() {
        // 计算缓存命中率
        // 这里需要实际的统计数据
        return 0.75; // 示例值
    }
}
```

### 2. 元数据提取器
```javascript
// 元数据提取器
class MetadataExtractor {
    constructor() {
        this.extractors = new Map();
        this.setupExtractors();
    }
    
    setupExtractors() {
        // 设置提取器
        this.extractors.set('og', this.extractOpenGraph.bind(this));
        this.extractors.set('twitter', this.extractTwitterCard.bind(this));
        this.extractors.set('schema', this.extractSchemaOrg.bind(this));
        this.extractors.set('basic', this.extractBasicMeta.bind(this));
        
        this.fallbackExtractors = [
            this.extractFromTitle.bind(this),
            this.extractFromHeadings.bind(this),
            this.extractFromContent.bind(this)
        ];
    }
    
    async extract(url) {
        // 提取元数据
        try {
            // 获取页面内容
            const html = await this.fetchPageContent(url);
            
            // 解析HTML
            const doc = this.parseHTML(html);
            
            // 提取各种元数据
            const metadata = {};
            
            for (const [type, extractor] of this.extractors) {
                try {
                    const extracted = extractor(doc);
                    Object.assign(metadata, extracted);
                } catch (error) {
                    console.warn(`${type} 元数据提取失败:`, error);
                }
            }
            
            // 如果主要字段缺失，使用回退提取器
            if (!metadata.title || !metadata.description) {
                for (const fallback of this.fallbackExtractors) {
                    try {
                        const fallbackData = fallback(doc);
                        Object.assign(metadata, fallbackData);
                        
                        if (metadata.title && metadata.description) {
                            break;
                        }
                    } catch (error) {
                        console.warn('回退提取器失败:', error);
                    }
                }
            }
            
            return metadata;
            
        } catch (error) {
            console.error('元数据提取失败:', error);
            throw error;
        }
    }
    
    async fetchPageContent(url) {
        // 获取页面内容
        // 注意：在实际实现中，这应该通过后端代理来完成
        // 以避免CORS问题和安全问题
        
        const response = await fetch(`/web/proxy/fetch?url=${encodeURIComponent(url)}`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.text();
    }
    
    parseHTML(html) {
        // 解析HTML
        const parser = new DOMParser();
        return parser.parseFromString(html, 'text/html');
    }
    
    extractOpenGraph(doc) {
        // 提取 Open Graph 元数据
        const ogData = {};
        const ogTags = doc.querySelectorAll('meta[property^="og:"]');
        
        ogTags.forEach(tag => {
            const property = tag.getAttribute('property');
            const content = tag.getAttribute('content');
            
            if (property && content) {
                const key = property.replace('og:', '');
                ogData[key] = content;
            }
        });
        
        return {
            title: ogData.title,
            description: ogData.description,
            image: ogData.image,
            url: ogData.url,
            siteName: ogData.site_name,
            type: ogData.type
        };
    }
    
    extractTwitterCard(doc) {
        // 提取 Twitter Card 元数据
        const twitterData = {};
        const twitterTags = doc.querySelectorAll('meta[name^="twitter:"]');
        
        twitterTags.forEach(tag => {
            const name = tag.getAttribute('name');
            const content = tag.getAttribute('content');
            
            if (name && content) {
                const key = name.replace('twitter:', '');
                twitterData[key] = content;
            }
        });
        
        return {
            title: twitterData.title,
            description: twitterData.description,
            image: twitterData.image,
            card: twitterData.card,
            site: twitterData.site,
            creator: twitterData.creator
        };
    }
    
    extractSchemaOrg(doc) {
        // 提取 Schema.org 结构化数据
        const schemaData = {};
        const scripts = doc.querySelectorAll('script[type="application/ld+json"]');
        
        scripts.forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                
                if (data['@type']) {
                    schemaData.type = data['@type'];
                    schemaData.title = data.name || data.headline;
                    schemaData.description = data.description;
                    schemaData.image = data.image;
                    schemaData.author = data.author?.name;
                    schemaData.publishDate = data.datePublished;
                }
            } catch (error) {
                console.warn('Schema.org 数据解析失败:', error);
            }
        });
        
        return schemaData;
    }
    
    extractBasicMeta(doc) {
        // 提取基本元数据
        const basicData = {};
        
        // 标题
        const titleTag = doc.querySelector('title');
        if (titleTag) {
            basicData.title = titleTag.textContent.trim();
        }
        
        // 描述
        const descTag = doc.querySelector('meta[name="description"]');
        if (descTag) {
            basicData.description = descTag.getAttribute('content');
        }
        
        // 关键词
        const keywordsTag = doc.querySelector('meta[name="keywords"]');
        if (keywordsTag) {
            basicData.keywords = keywordsTag.getAttribute('content');
        }
        
        // 作者
        const authorTag = doc.querySelector('meta[name="author"]');
        if (authorTag) {
            basicData.author = authorTag.getAttribute('content');
        }
        
        return basicData;
    }
    
    extractFromTitle(doc) {
        // 从标题提取
        const title = doc.querySelector('title')?.textContent?.trim();
        return { title: title };
    }
    
    extractFromHeadings(doc) {
        // 从标题标签提取
        const h1 = doc.querySelector('h1')?.textContent?.trim();
        const h2 = doc.querySelector('h2')?.textContent?.trim();
        
        return {
            title: h1 || h2,
            description: h2 && h1 ? h2 : undefined
        };
    }
    
    extractFromContent(doc) {
        // 从内容提取
        const paragraphs = doc.querySelectorAll('p');
        let description = '';
        
        for (const p of paragraphs) {
            const text = p.textContent?.trim();
            if (text && text.length > 50) {
                description = text.substring(0, 200) + '...';
                break;
            }
        }
        
        return { description: description };
    }
}
```

### 3. 安全验证器
```javascript
// 安全验证器
class SecurityValidator {
    constructor() {
        this.blockedDomains = new Set();
        this.allowedProtocols = new Set(['http:', 'https:']);
        this.setupSecurity();
    }
    
    setupSecurity() {
        // 设置安全规则
        this.blockedDomains.add('malicious-site.com');
        this.blockedDomains.add('phishing-site.com');
        
        this.suspiciousPatterns = [
            /bit\.ly/i,
            /tinyurl/i,
            /t\.co/i,
            /goo\.gl/i
        ];
        
        this.dangerousExtensions = new Set([
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js'
        ]);
    }
    
    isUrlSafe(url) {
        // 检查URL是否安全
        try {
            const urlObj = new URL(url);
            
            // 检查协议
            if (!this.allowedProtocols.has(urlObj.protocol)) {
                return false;
            }
            
            // 检查域名
            if (this.blockedDomains.has(urlObj.hostname)) {
                return false;
            }
            
            // 检查可疑模式
            if (this.suspiciousPatterns.some(pattern => pattern.test(url))) {
                return false;
            }
            
            // 检查危险扩展名
            const pathname = urlObj.pathname.toLowerCase();
            if (Array.from(this.dangerousExtensions).some(ext => pathname.endsWith(ext))) {
                return false;
            }
            
            return true;
            
        } catch (error) {
            return false;
        }
    }
    
    sanitizeUrl(url) {
        // 清理URL
        try {
            const urlObj = new URL(url);
            
            // 移除危险参数
            urlObj.searchParams.delete('javascript');
            urlObj.searchParams.delete('vbscript');
            
            return urlObj.toString();
            
        } catch (error) {
            return url;
        }
    }
}
```

## 使用示例

### 1. 基本链接预览使用
```javascript
// 使用链接预览组件
class MessageWithLinks extends Component {
    static template = xml`
        <div class="message">
            <div class="message-content">
                <t t-esc="message.body"/>
            </div>
            <div class="link-previews" t-if="linkPreviews.length">
                <LinkPreview 
                    t-foreach="linkPreviews" 
                    t-as="preview" 
                    t-key="preview.id"
                    linkPreview="preview"
                    deletable="canDeletePreviews"/>
            </div>
        </div>
    `;
    
    static components = { LinkPreview };
    
    setup() {
        this.message = this.props.message;
        this.linkPreviews = this.message.linkPreviews || [];
        this.canDeletePreviews = this.props.canDelete || false;
    }
}
```

### 2. 高级链接预览管理
```javascript
// 高级链接预览管理
class AdvancedLinkPreviewManager extends Component {
    setup() {
        this.dataManager = new LinkPreviewDataManager();
        this.state = useState({
            previews: [],
            loading: false,
            error: null
        });
        
        this.setupLinkPreviewManagement();
    }
    
    async generatePreviewsForMessage(message) {
        // 为消息生成链接预览
        this.state.loading = true;
        this.state.error = null;
        
        try {
            const urls = this.extractUrlsFromMessage(message.body);
            const previews = [];
            
            for (const url of urls) {
                try {
                    const preview = await this.dataManager.generatePreview(url);
                    previews.push(preview);
                } catch (error) {
                    console.warn('链接预览生成失败:', url, error);
                }
            }
            
            this.state.previews = previews;
            
        } catch (error) {
            this.state.error = error.message;
        } finally {
            this.state.loading = false;
        }
    }
    
    extractUrlsFromMessage(text) {
        // 从消息中提取URL
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        const matches = text.match(urlRegex);
        return matches || [];
    }
    
    async deletePreview(preview) {
        // 删除预览
        try {
            await this.env.services.orm.unlink('mail.link.preview', [preview.id]);
            
            this.state.previews = this.state.previews.filter(p => p.id !== preview.id);
            
            this.env.services.notification.add('链接预览已删除', {
                type: 'success'
            });
            
        } catch (error) {
            this.env.services.notification.add('删除失败', {
                type: 'danger'
            });
        }
    }
}
```

## 链接预览组件特性

### 1. 安全链接处理
链接预览组件提供了安全的链接处理：
- **安全验证**: 验证URL的安全性
- **外部打开**: 在新窗口中打开链接
- **noreferrer**: 防止引用信息泄露
- **域名检查**: 检查可疑域名

### 2. 丰富的预览类型
```javascript
// 支持的预览类型
const previewTypes = {
    video: '视频预览',
    social: '社交媒体预览',
    article: '文章预览',
    code: '代码仓库预览',
    generic: '通用预览'
};
```

### 3. 用户交互优化
- **点击打开**: 点击预览卡片打开链接
- **删除功能**: 可选的预览删除功能
- **确认对话框**: 删除前的确认机制
- **响应式设计**: 适配不同设备

### 4. 性能优化
- **缓存机制**: 预览数据的智能缓存
- **懒加载**: 按需生成预览
- **批量处理**: 支持批量预览生成
- **错误处理**: 完善的错误处理机制

## 最佳实践

### 1. 组件使用
```javascript
// ✅ 推荐：正确的链接预览使用
<LinkPreview 
    linkPreview="preview"
    deletable="canDelete"/>
```

### 2. 安全处理
```javascript
// ✅ 推荐：安全的链接打开
onClick() {
    window.open(this.props.linkPreview.source_url, "_blank", "noreferrer");
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    const preview = await this.generatePreview(url);
} catch (error) {
    console.error('预览生成失败:', error);
    return this.createErrorPreview(url, error);
}
```

### 4. 性能优化
```javascript
// ✅ 推荐：使用缓存优化性能
const cached = this.getFromCache(url);
if (cached) {
    return cached;
}
```

## 总结

Odoo 链接预览组件提供了安全、高效的链接预览功能：

**核心优势**:
- **安全性**: 完善的URL安全验证机制
- **易用性**: 简单的点击交互和删除功能
- **性能**: 智能缓存和懒加载优化
- **扩展性**: 支持多种预览类型和自定义
- **用户体验**: 丰富的预览信息和响应式设计

**适用场景**:
- 消息链接预览
- 邮件链接预览
- 社交媒体链接
- 文档链接预览
- 任何需要链接预览的场景

**设计优势**:
- 组件化架构
- 安全优先设计
- 性能优化集成
- 用户体验优化

这个链接预览组件为 Odoo 邮件系统提供了专业的链接预览能力，在保证安全性的同时，为用户提供了丰富的链接浏览体验。

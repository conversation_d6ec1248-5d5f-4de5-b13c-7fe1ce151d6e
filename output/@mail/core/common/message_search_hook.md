# Message Search Hook - 消息搜索钩子

## 概述

`message_search_hook.js` 实现了 Odoo 邮件系统中的消息搜索功能钩子，提供了消息内容的搜索、高亮显示、分页加载等功能。该钩子支持多关键词搜索、智能高亮、XPath查询等高级特性，为用户提供强大的消息搜索体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_search_hook.js`
- **行数**: 124
- **模块**: `@mail/core/common/message_search_hook`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/hooks'    // 邮件系统钩子
'@odoo/owl'                   // OWL 框架
'@web/core/utils/hooks'       // Web 核心钩子
'@web/core/utils/strings'     // 字符串工具
```

## 核心常量

### 高亮样式类

```javascript
const HIGHLIGHT_CLASS = "o-mail-Message-searchHighlight";
```

**用途**:
- 定义搜索结果高亮的CSS类名
- 用于标记匹配的搜索词
- 提供统一的高亮样式

## 核心函数

### searchHighlight() - 搜索高亮

高级的搜索高亮函数，支持HTML内容的智能高亮：

```javascript
function searchHighlight(searchTerm, target) {
    if (!searchTerm) {
        return target;
    }
    
    const htmlDoc = new DOMParser().parseFromString(target, "text/html");
    
    for (const term of searchTerm.split(" ")) {
        const regexp = new RegExp(`(${escapeRegExp(term)})`, "gi");
        
        // 特殊处理单引号字符
        const split = term.toLowerCase().split("'");
        let lowercase = split.map((s) => `'${s}'`).join(', "\'", ');
        let uppercase = lowercase.toUpperCase();
        
        if (split.length > 1) {
            lowercase = `concat(${lowercase})`;
            uppercase = `concat(${uppercase})`;
        }
        
        // 使用XPath查询匹配的文本节点
        const matchs = htmlDoc.evaluate(
            `//*[text()[contains(translate(., ${uppercase}, ${lowercase}), ${lowercase})]]`,
            htmlDoc,
            null,
            XPathResult.ORDERED_NODE_SNAPSHOT_TYPE
        );
        
        // 处理每个匹配的元素
        for (let i = 0; i < matchs.snapshotLength; i++) {
            const element = matchs.snapshotItem(i);
            const newNode = [];
            
            for (const node of element.childNodes) {
                const match = node.textContent.match(regexp);
                
                if (node.nodeType === Node.TEXT_NODE && match?.length > 0) {
                    let curIndex = 0;
                    
                    // 为每个匹配项创建高亮span
                    for (const match of node.textContent.matchAll(regexp)) {
                        // 匹配前的文本
                        const start = htmlDoc.createTextNode(
                            node.textContent.slice(curIndex, match.index)
                        );
                        newNode.push(start);
                        
                        // 高亮的匹配文本
                        const span = htmlDoc.createElement("span");
                        span.setAttribute("class", HIGHLIGHT_CLASS);
                        span.textContent = match[0];
                        newNode.push(span);
                        
                        curIndex = match.index + match[0].length;
                    }
                    
                    // 匹配后的文本
                    const end = htmlDoc.createTextNode(node.textContent.slice(curIndex));
                    newNode.push(end);
                } else {
                    newNode.push(node);
                }
            }
            
            element.replaceChildren(...newNode);
        }
    }
    
    return markup(htmlDoc.body.innerHTML);
}
```

### 高亮算法特性

#### 1. 多关键词支持
- 按空格分割搜索词
- 每个关键词独立高亮
- 支持部分匹配

#### 2. 大小写不敏感
- 使用正则表达式的 `gi` 标志
- XPath查询中使用 `translate()` 函数
- 智能处理大小写转换

#### 3. 特殊字符处理
```javascript
// 特殊处理单引号字符
const split = term.toLowerCase().split("'");
let lowercase = split.map((s) => `'${s}'`).join(', "\'", ');
```

#### 4. XPath查询
```javascript
// 等效于对所有搜索字符执行 .toLowerCase()
`//*[text()[contains(translate(., ${uppercase}, ${lowercase}), ${lowercase})]]`
```

#### 5. DOM操作优化
- 使用 `DOMParser` 解析HTML
- 精确的文本节点替换
- 保持原有的DOM结构

## useMessageSearch 钩子

### 钩子函数

```javascript
function useMessageSearch(thread) {
    const store = useService("mail.store");
    const sequential = useSequential();
    
    const state = useState({
        // 状态属性和方法
    });
    
    onWillUnmount(() => {
        state.clear();
    });
    
    return state;
}
```

### 状态属性

```javascript
const state = useState({
    thread,                    // 搜索的线程
    count: 0,                 // 搜索结果总数
    loadMore: false,          // 是否可以加载更多
    messages: [],             // 搜索结果消息列表
    searchTerm: undefined,    // 搜索关键词
    searched: false,          // 是否已执行搜索
    searching: false,         // 是否正在搜索
});
```

### 核心方法

#### search() - 执行搜索

```javascript
async search(before = false) {
    if (this.searchTerm) {
        this.searching = true;
        
        const data = await sequential(() =>
            store.search(this.searchTerm, this.thread, before)
        );
        
        if (!data) {
            return;
        }
        
        const { count, loadMore, messages } = data;
        this.searched = true;
        this.searching = false;
        this.count = count;
        this.loadMore = loadMore;
        
        if (before) {
            this.messages.push(...messages);  // 追加到现有结果
        } else {
            this.messages = messages;          // 替换搜索结果
        }
    } else {
        this.clear();
    }
}
```

**搜索流程**:
1. 检查搜索词是否存在
2. 设置搜索状态为进行中
3. 调用存储服务执行搜索
4. 处理搜索结果
5. 更新状态和消息列表

**参数说明**:
- `before`: 是否加载更早的结果（分页）

#### clear() - 清除搜索

```javascript
clear() {
    this.messages = [];
    this.searched = false;
    this.searching = false;
    this.searchTerm = undefined;
}
```

**清除操作**:
- 清空消息列表
- 重置搜索状态
- 清除搜索关键词

#### highlight() - 高亮方法

```javascript
highlight: (target) => searchHighlight(state.searchTerm, target)
```

**功能**:
- 使用当前搜索词高亮目标文本
- 返回包含高亮标记的HTML
- 用于在UI中显示搜索结果

## 使用场景

### 1. 消息列表搜索

```javascript
// 在消息列表组件中
const messageSearch = useMessageSearch(thread);

// 执行搜索
const handleSearch = async (searchTerm) => {
    messageSearch.searchTerm = searchTerm;
    await messageSearch.search();
};

// 显示搜索结果
if (messageSearch.searched) {
    renderSearchResults(messageSearch.messages);
}
```

### 2. 搜索结果高亮

```javascript
// 在消息组件中高亮搜索词
const messageSearch = useMessageSearch(thread);

// 高亮消息内容
const highlightedContent = messageSearch.highlight(message.body);
```

### 3. 分页加载

```javascript
// 加载更多搜索结果
const loadMoreResults = async () => {
    if (messageSearch.loadMore) {
        await messageSearch.search(true);  // before = true
    }
};
```

### 4. 搜索状态管理

```javascript
// 显示搜索状态
if (messageSearch.searching) {
    showLoadingIndicator();
} else if (messageSearch.searched && messageSearch.count === 0) {
    showNoResultsMessage();
}
```

## 高级特性

### 1. 顺序执行

```javascript
const sequential = useSequential();
```

**功能**:
- 确保搜索请求按顺序执行
- 避免竞态条件
- 取消过时的搜索请求

### 2. 生命周期管理

```javascript
onWillUnmount(() => {
    state.clear();
});
```

**功能**:
- 组件卸载时自动清理搜索状态
- 防止内存泄漏
- 确保状态一致性

### 3. 响应式状态

```javascript
const state = useState({...});
```

**功能**:
- 提供响应式的搜索状态
- 自动触发UI更新
- 与OWL框架集成

## 性能优化

### 1. 搜索防抖

```javascript
// 建议在使用时添加防抖
const debouncedSearch = debounce(messageSearch.search, 300);
```

### 2. 结果缓存

```javascript
// 存储服务可能实现搜索结果缓存
// 避免重复的网络请求
```

### 3. DOM操作优化

```javascript
// 使用 DOMParser 而不是 innerHTML
// 精确的节点替换而不是全量重建
```

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 不同的高亮策略（XPath vs 正则表达式）
- 可扩展的搜索算法

### 2. 观察者模式 (Observer Pattern)
- 响应式状态更新
- 自动触发UI重新渲染

### 3. 命令模式 (Command Pattern)
- 搜索操作的封装
- 支持撤销和重做

## 注意事项

1. **性能考虑**: 大量文本的高亮可能影响性能
2. **XPath兼容性**: 确保浏览器支持XPath查询
3. **HTML安全**: 注意XSS防护
4. **内存管理**: 及时清理搜索状态

## 扩展建议

1. **搜索历史**: 保存用户搜索历史
2. **高级搜索**: 支持正则表达式、日期范围等
3. **搜索建议**: 提供搜索词自动补全
4. **结果排序**: 支持按相关性、时间等排序
5. **导出功能**: 支持搜索结果导出

该钩子为邮件系统提供了强大的搜索功能，支持复杂的文本匹配和高亮显示，大大提升了用户查找信息的效率。

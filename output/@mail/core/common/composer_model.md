# Composer Model - 编辑器模型

## 概述

`composer_model.js` 定义了 Odoo 邮件系统中的编辑器模型类，用于管理消息编辑器的状态和数据。该模型处理文本内容、附件、提及、预设回复、光标位置等信息，是消息编辑功能的核心数据结构，为编辑器组件提供完整的状态管理支持。

## 文件信息
- **路径**: `/mail/static/src/core/common/composer_model.js`
- **行数**: 53
- **模块**: `@mail/core/common/composer_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类（OR 操作符）
```

## 类定义

### Composer 类

```javascript
class Composer extends Record {
    static id = OR("thread", "message");  // 复合主键：线程或消息
    static records = {};                  // 静态记录集合
}
```

### 复合主键

使用 `OR("thread", "message")` 作为主键，表示编辑器可以关联到：
- **线程** (`thread`): 新消息编辑器
- **消息** (`message`): 编辑现有消息

这种设计允许同一个编辑器模型支持两种不同的使用场景。

## 核心属性

### 内容相关

```javascript
text = "";                                    // 编辑器文本内容
attachments = Record.many("Attachment");      // 附件集合
```

### 关联对象

```javascript
message = Record.one("Message");              // 关联的消息（编辑模式）
thread = Record.one("Thread");                // 关联的线程（新消息模式）
```

### 提及和引用

```javascript
mentionedPartners = Record.many("Persona");   // 提及的用户
mentionedChannels = Record.many("Thread");    // 提及的频道
cannedResponses = Record.many("mail.canned.response");  // 使用的预设回复
```

### 光标和选择

```javascript
selection = {
    start: 0,                                 // 选择开始位置
    end: 0,                                   // 选择结束位置
    direction: "none",                        // 选择方向
};
```

**选择方向类型**:
- `"forward"`: 向前选择
- `"backward"`: 向后选择  
- `"none"`: 无方向（光标位置）

### 状态控制

```javascript
forceCursorMove: boolean;                     // 强制光标移动
isFocused = false;                           // 是否获得焦点
autofocus = 0;                               // 自动焦点计数器
```

## 核心方法

### clear() - 清空编辑器

```javascript
clear() {
    this.attachments.length = 0;             // 清空附件
    this.text = "";                          // 清空文本
    Object.assign(this.selection, {          // 重置选择
        start: 0,
        end: 0,
        direction: "none",
    });
}
```

**清空操作**:
1. 移除所有附件
2. 清空文本内容
3. 重置光标位置到开头
4. 重置选择方向

## 使用场景

### 1. 新消息编辑器

```javascript
// 创建新消息编辑器
const composer = store.Composer.insert({
    thread: currentThread,
    text: "",
    selection: { start: 0, end: 0, direction: "none" }
});

// 添加文本
composer.text = "Hello, world!";

// 添加附件
composer.attachments.add(attachment);
```

### 2. 编辑现有消息

```javascript
// 创建消息编辑器
const composer = store.Composer.insert({
    message: existingMessage,
    text: existingMessage.body,
    mentionedPartners: existingMessage.recipients
});

// 修改内容
composer.text = "Updated message content";
```

### 3. 提及用户和频道

```javascript
// 添加用户提及
composer.mentionedPartners.add(user);

// 添加频道提及
composer.mentionedChannels.add(channel);

// 文本中包含提及标记
composer.text = "Hello @john, please check #general channel";
```

### 4. 使用预设回复

```javascript
// 添加预设回复
composer.cannedResponses.add(cannedResponse);

// 插入预设回复内容
composer.text += cannedResponse.substitution;
```

## 光标和选择管理

### 设置光标位置

```javascript
// 设置光标到文本末尾
composer.selection.start = composer.text.length;
composer.selection.end = composer.text.length;
composer.selection.direction = "none";
```

### 选择文本范围

```javascript
// 选择特定范围的文本
composer.selection.start = 5;
composer.selection.end = 15;
composer.selection.direction = "forward";
```

### 强制光标移动

```javascript
// 强制光标移动（用于触发UI更新）
composer.forceCursorMove = true;
```

## 焦点管理

### 设置焦点

```javascript
// 设置编辑器焦点
composer.isFocused = true;
composer.autofocus++;  // 触发自动焦点
```

### 失去焦点

```javascript
// 编辑器失去焦点
composer.isFocused = false;
```

## 数据验证

### 检查是否有内容

```javascript
const hasContent = (composer) => {
    return composer.text.trim().length > 0 || 
           composer.attachments.length > 0;
};
```

### 检查是否有提及

```javascript
const hasMentions = (composer) => {
    return composer.mentionedPartners.length > 0 || 
           composer.mentionedChannels.length > 0;
};
```

### 检查是否为编辑模式

```javascript
const isEditMode = (composer) => {
    return Boolean(composer.message);
};
```

## 内容处理

### 插入文本

```javascript
const insertText = (composer, text, position) => {
    const before = composer.text.substring(0, position);
    const after = composer.text.substring(position);
    composer.text = before + text + after;
    
    // 更新光标位置
    composer.selection.start = position + text.length;
    composer.selection.end = position + text.length;
};
```

### 替换选中文本

```javascript
const replaceSelection = (composer, newText) => {
    const { start, end } = composer.selection;
    const before = composer.text.substring(0, start);
    const after = composer.text.substring(end);
    composer.text = before + newText + after;
    
    // 更新光标位置
    composer.selection.start = start + newText.length;
    composer.selection.end = start + newText.length;
};
```

### 添加附件

```javascript
const addAttachment = (composer, file) => {
    const attachment = store.Attachment.insert({
        filename: file.name,
        mimetype: file.type,
        filesize: file.size
    });
    
    composer.attachments.add(attachment);
    return attachment;
};
```

## 状态同步

### 与UI组件同步

```javascript
// 编辑器组件监听模型变化
useEffect(() => {
    // 同步文本内容
    textareaRef.current.value = composer.text;
    
    // 同步光标位置
    if (composer.forceCursorMove) {
        textareaRef.current.setSelectionRange(
            composer.selection.start,
            composer.selection.end
        );
        composer.forceCursorMove = false;
    }
}, () => [composer.text, composer.selection, composer.forceCursorMove]);
```

### 与服务器同步

```javascript
// 保存草稿
const saveDraft = async (composer) => {
    if (composer.thread) {
        await rpc('/mail/thread/save_draft', {
            thread_id: composer.thread.id,
            text: composer.text,
            attachment_ids: composer.attachments.map(a => a.id)
        });
    }
};
```

## 性能优化

### 1. 防抖保存

```javascript
// 防抖保存草稿
const debouncedSaveDraft = debounce((composer) => {
    saveDraft(composer);
}, 1000);

// 文本变化时触发
composer.text = newText;
debouncedSaveDraft(composer);
```

### 2. 批量更新

```javascript
// 批量更新编辑器状态
const updateComposer = (composer, updates) => {
    Object.assign(composer, updates);
    
    // 触发单次重新渲染
    composer.autofocus++;
};
```

### 3. 选择性清理

```javascript
// 只清理必要的内容
const partialClear = (composer, options = {}) => {
    if (options.clearText) {
        composer.text = "";
    }
    if (options.clearAttachments) {
        composer.attachments.length = 0;
    }
    if (options.resetCursor) {
        Object.assign(composer.selection, {
            start: 0, end: 0, direction: "none"
        });
    }
};
```

## 设计模式

### 1. 状态模式 (State Pattern)
- 编辑模式 vs 新消息模式
- 不同状态下的不同行为

### 2. 观察者模式 (Observer Pattern)
- 监听文本和附件变化
- 自动保存草稿

### 3. 命令模式 (Command Pattern)
- 文本编辑操作的封装
- 支持撤销和重做

## 扩展功能

### 1. 撤销/重做

```javascript
// 扩展编辑器支持撤销重做
class ComposerWithHistory extends Composer {
    history = [];
    historyIndex = -1;
    
    saveState() {
        this.history = this.history.slice(0, this.historyIndex + 1);
        this.history.push({
            text: this.text,
            selection: { ...this.selection }
        });
        this.historyIndex++;
    }
    
    undo() {
        if (this.historyIndex > 0) {
            this.historyIndex--;
            const state = this.history[this.historyIndex];
            this.text = state.text;
            this.selection = { ...state.selection };
        }
    }
    
    redo() {
        if (this.historyIndex < this.history.length - 1) {
            this.historyIndex++;
            const state = this.history[this.historyIndex];
            this.text = state.text;
            this.selection = { ...state.selection };
        }
    }
}
```

### 2. 自动保存

```javascript
// 自动保存功能
const setupAutoSave = (composer) => {
    let saveTimer;
    
    const autoSave = () => {
        clearTimeout(saveTimer);
        saveTimer = setTimeout(() => {
            saveDraft(composer);
        }, 2000);
    };
    
    // 监听内容变化
    composer.addEventListener('textChange', autoSave);
    composer.addEventListener('attachmentChange', autoSave);
};
```

## 注意事项

1. **内存管理**: 及时清理不再使用的编辑器实例
2. **状态一致性**: 确保模型状态与UI状态同步
3. **性能考虑**: 避免频繁的状态更新
4. **数据验证**: 验证用户输入的有效性

## 扩展建议

1. **富文本支持**: 扩展支持富文本编辑
2. **协作编辑**: 支持多人协作编辑
3. **模板系统**: 支持消息模板
4. **自动补全**: 改进提及和建议功能
5. **离线支持**: 支持离线编辑和同步

该模型为消息编辑器提供了完整的状态管理，是邮件系统中用户内容创建的核心数据结构。

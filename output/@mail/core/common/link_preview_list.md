# Link Preview List - 链接预览列表

## 概述

`link_preview_list.js` 实现了 Odoo 邮件系统中的链接预览列表组件，用于批量显示多个链接预览。该组件是一个简洁的容器组件，负责渲染链接预览的集合，支持统一的删除权限控制，是消息中链接预览展示的核心组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/link_preview_list.js`
- **行数**: 28
- **模块**: `@mail/core/common/link_preview_list`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/link_preview'  // 链接预览组件
'@odoo/owl'                       // OWL 框架
```

## 组件定义

### LinkPreviewList 类

```javascript
class LinkPreviewList extends Component {
    static template = "mail.LinkPreviewList";
    static props = ["linkPreviews", "deletable?"];
    static defaultProps = {
        deletable: false,
    };
    static components = { LinkPreview };
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    linkPreviews: import("models").LinkPreview[];  // 链接预览数组
    deletable?: boolean;                           // 是否可删除（可选）
}
```

### Props 详细说明

- **`linkPreviews`** (必需):
  - 类型: LinkPreview 模型数组
  - 用途: 要显示的链接预览列表
  - 功能: 提供所有需要渲染的预览数据

- **`deletable`** (可选):
  - 类型: 布尔值
  - 用途: 控制所有预览是否可删除
  - 默认值: `false`
  - 传递: 会传递给每个子预览组件

### 默认属性

```javascript
static defaultProps = {
    deletable: false,
};
```

**默认行为**:
- 默认情况下预览不可删除
- 确保安全的只读显示模式

## 使用场景

### 1. 消息中的预览列表

```javascript
// 在消息组件中显示所有链接预览
<div class="message-content">
    <div class="message-text">{messageText}</div>
    <LinkPreviewList 
        linkPreviews={message.linkPreviews}
        deletable={message.canEdit}
    />
</div>
```

### 2. 编辑器中的预览列表

```javascript
// 在消息编辑器中显示预览
<div class="composer-content">
    <textarea class="composer-input">{composerText}</textarea>
    <LinkPreviewList 
        linkPreviews={composer.linkPreviews}
        deletable={true}
    />
</div>
```

### 3. 只读模式预览

```javascript
// 只读模式下的预览列表
<LinkPreviewList 
    linkPreviews={thread.allLinkPreviews}
    deletable={false}
/>
```

### 4. 管理界面预览

```javascript
// 管理界面中的预览列表
<div class="admin-preview-section">
    <h3>消息中的链接预览</h3>
    <LinkPreviewList 
        linkPreviews={selectedMessage.linkPreviews}
        deletable={currentUser.isAdmin}
    />
</div>
```

## 渲染逻辑

### 1. 基础渲染

```javascript
// 模板中的基础渲染逻辑
// mail.LinkPreviewList 模板
<div class="link-preview-list">
    <t t-foreach="props.linkPreviews" t-as="linkPreview" t-key="linkPreview.id">
        <LinkPreview 
            linkPreview="linkPreview"
            deletable="props.deletable"
        />
    </t>
</div>
```

### 2. 条件渲染

```javascript
// 只有当有预览时才渲染列表
const renderPreviewList = (linkPreviews, deletable) => {
    if (!linkPreviews || linkPreviews.length === 0) {
        return null;
    }
    
    return (
        <LinkPreviewList 
            linkPreviews={linkPreviews}
            deletable={deletable}
        />
    );
};
```

### 3. 分组渲染

```javascript
// 按类型分组显示预览
const renderGroupedPreviews = (linkPreviews, deletable) => {
    const grouped = groupPreviewsByType(linkPreviews);
    
    return Object.entries(grouped).map(([type, previews]) => (
        <div class={`preview-group preview-group-${type}`}>
            <h4>{getTypeLabel(type)}</h4>
            <LinkPreviewList 
                linkPreviews={previews}
                deletable={deletable}
            />
        </div>
    ));
};
```

### 4. 响应式渲染

```javascript
// 响应式布局的预览列表
const renderResponsivePreviews = (linkPreviews, deletable, screenSize) => {
    const maxVisible = screenSize === 'mobile' ? 2 : 4;
    const visiblePreviews = linkPreviews.slice(0, maxVisible);
    const hiddenCount = linkPreviews.length - maxVisible;
    
    return (
        <div class="responsive-preview-list">
            <LinkPreviewList 
                linkPreviews={visiblePreviews}
                deletable={deletable}
            />
            {hiddenCount > 0 && (
                <div class="more-previews">
                    还有 {hiddenCount} 个预览...
                </div>
            )}
        </div>
    );
};
```

## 预览管理

### 1. 预览过滤

```javascript
// 过滤有效的预览
const filterValidPreviews = (linkPreviews) => {
    return linkPreviews.filter(preview => 
        preview && 
        preview.source_url && 
        !preview.isHidden
    );
};
```

### 2. 预览排序

```javascript
// 按创建时间排序预览
const sortPreviewsByTime = (linkPreviews) => {
    return [...linkPreviews].sort((a, b) => 
        new Date(a.create_date) - new Date(b.create_date)
    );
};

// 按类型排序预览
const sortPreviewsByType = (linkPreviews) => {
    const typeOrder = { image: 1, video: 2, card: 3 };
    
    return [...linkPreviews].sort((a, b) => {
        const aType = a.isImage ? 'image' : a.isVideo ? 'video' : 'card';
        const bType = b.isImage ? 'image' : b.isVideo ? 'video' : 'card';
        
        return typeOrder[aType] - typeOrder[bType];
    });
};
```

### 3. 预览限制

```javascript
// 限制显示的预览数量
const limitPreviews = (linkPreviews, maxCount = 5) => {
    if (linkPreviews.length <= maxCount) {
        return { previews: linkPreviews, hasMore: false };
    }
    
    return {
        previews: linkPreviews.slice(0, maxCount),
        hasMore: true,
        hiddenCount: linkPreviews.length - maxCount
    };
};
```

## 样式和布局

### 1. 基础样式

```css
.link-preview-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin: 8px 0;
}

.link-preview-list .link-preview {
    margin: 0; /* 重置单个预览的边距 */
}
```

### 2. 网格布局

```css
.link-preview-list.grid-layout {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
}

@media (max-width: 768px) {
    .link-preview-list.grid-layout {
        grid-template-columns: 1fr;
    }
}
```

### 3. 水平布局

```css
.link-preview-list.horizontal {
    flex-direction: row;
    overflow-x: auto;
    padding: 8px 0;
}

.link-preview-list.horizontal .link-preview {
    flex-shrink: 0;
    width: 300px;
}
```

### 4. 紧凑布局

```css
.link-preview-list.compact {
    gap: 4px;
}

.link-preview-list.compact .link-preview {
    transform: scale(0.9);
    transform-origin: left top;
}
```

## 交互功能

### 1. 批量操作

```javascript
// 批量删除所有预览
const deleteAllPreviews = (linkPreviews) => {
    const previewIds = linkPreviews.map(p => p.id);
    
    showConfirmDialog({
        title: '删除所有预览',
        message: `确定要删除所有 ${linkPreviews.length} 个链接预览吗？`,
        onConfirm: () => {
            rpc('/mail/link_preview/hide', {
                link_preview_ids: previewIds
            });
        }
    });
};
```

### 2. 选择模式

```javascript
// 支持多选的预览列表
const SelectableLinkPreviewList = ({ linkPreviews, onSelectionChange }) => {
    const [selectedIds, setSelectedIds] = useState(new Set());
    
    const toggleSelection = (previewId) => {
        const newSelection = new Set(selectedIds);
        if (newSelection.has(previewId)) {
            newSelection.delete(previewId);
        } else {
            newSelection.add(previewId);
        }
        setSelectedIds(newSelection);
        onSelectionChange(Array.from(newSelection));
    };
    
    return (
        <div class="selectable-preview-list">
            {linkPreviews.map(preview => (
                <div 
                    class={`preview-item ${selectedIds.has(preview.id) ? 'selected' : ''}`}
                    onClick={() => toggleSelection(preview.id)}
                >
                    <LinkPreview linkPreview={preview} deletable={false} />
                    <input 
                        type="checkbox" 
                        checked={selectedIds.has(preview.id)}
                        onChange={() => toggleSelection(preview.id)}
                    />
                </div>
            ))}
        </div>
    );
};
```

### 3. 拖拽排序

```javascript
// 支持拖拽重排序的预览列表
const DraggableLinkPreviewList = ({ linkPreviews, onReorder }) => {
    const handleDragStart = (e, index) => {
        e.dataTransfer.setData('text/plain', index);
    };
    
    const handleDrop = (e, dropIndex) => {
        e.preventDefault();
        const dragIndex = parseInt(e.dataTransfer.getData('text/plain'));
        
        if (dragIndex !== dropIndex) {
            const newOrder = [...linkPreviews];
            const [draggedItem] = newOrder.splice(dragIndex, 1);
            newOrder.splice(dropIndex, 0, draggedItem);
            onReorder(newOrder);
        }
    };
    
    return (
        <div class="draggable-preview-list">
            {linkPreviews.map((preview, index) => (
                <div 
                    draggable
                    onDragStart={(e) => handleDragStart(e, index)}
                    onDrop={(e) => handleDrop(e, index)}
                    onDragOver={(e) => e.preventDefault()}
                >
                    <LinkPreview linkPreview={preview} />
                </div>
            ))}
        </div>
    );
};
```

## 性能优化

### 1. 虚拟化

```javascript
// 大量预览时使用虚拟化
const VirtualizedLinkPreviewList = ({ linkPreviews, deletable }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
    const visiblePreviews = linkPreviews.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-preview-list">
            <LinkPreviewList 
                linkPreviews={visiblePreviews}
                deletable={deletable}
            />
        </div>
    );
};
```

### 2. 懒加载

```javascript
// 懒加载预览内容
const LazyLinkPreviewList = ({ linkPreviews, deletable }) => {
    const [loadedCount, setLoadedCount] = useState(3);
    const visiblePreviews = linkPreviews.slice(0, loadedCount);
    
    const loadMore = () => {
        setLoadedCount(prev => Math.min(prev + 3, linkPreviews.length));
    };
    
    return (
        <div class="lazy-preview-list">
            <LinkPreviewList 
                linkPreviews={visiblePreviews}
                deletable={deletable}
            />
            {loadedCount < linkPreviews.length && (
                <button onClick={loadMore}>
                    加载更多 ({linkPreviews.length - loadedCount} 个)
                </button>
            )}
        </div>
    );
};
```

### 3. 缓存优化

```javascript
// 缓存渲染结果
const memoizedLinkPreviewList = memo(({ linkPreviews, deletable }) => {
    return (
        <LinkPreviewList 
            linkPreviews={linkPreviews}
            deletable={deletable}
        />
    );
}, (prevProps, nextProps) => {
    return (
        prevProps.deletable === nextProps.deletable &&
        prevProps.linkPreviews.length === nextProps.linkPreviews.length &&
        prevProps.linkPreviews.every((preview, index) => 
            preview.id === nextProps.linkPreviews[index]?.id
        )
    );
});
```

## 可访问性

### 1. 语义化标记

```xml
<!-- 语义化的预览列表 -->
<section class="link-preview-list" role="list" aria-label="链接预览">
    <t t-foreach="props.linkPreviews" t-as="linkPreview">
        <article role="listitem">
            <LinkPreview 
                linkPreview="linkPreview"
                deletable="props.deletable"
            />
        </article>
    </t>
</section>
```

### 2. 键盘导航

```javascript
// 支持键盘导航
const handleKeyNavigation = (event, currentIndex, totalCount) => {
    switch (event.key) {
        case 'ArrowDown':
            focusPreview(Math.min(currentIndex + 1, totalCount - 1));
            break;
        case 'ArrowUp':
            focusPreview(Math.max(currentIndex - 1, 0));
            break;
        case 'Home':
            focusPreview(0);
            break;
        case 'End':
            focusPreview(totalCount - 1);
            break;
    }
};
```

### 3. 屏幕阅读器支持

```javascript
// 为屏幕阅读器提供描述
const getListDescription = (linkPreviews) => {
    const count = linkPreviews.length;
    const types = [...new Set(linkPreviews.map(p => 
        p.isImage ? '图片' : p.isVideo ? '视频' : '网页'
    ))];
    
    return `包含 ${count} 个链接预览：${types.join('、')}`;
};
```

## 设计模式

### 1. 容器模式 (Container Pattern)
- 作为预览组件的容器
- 统一管理子组件的属性

### 2. 组合模式 (Composition Pattern)
- 组合多个链接预览组件
- 提供统一的列表接口

### 3. 迭代器模式 (Iterator Pattern)
- 遍历和渲染预览列表
- 支持不同的遍历策略

## 注意事项

1. **性能考虑**: 大量预览时的渲染性能
2. **内存管理**: 避免内存泄漏
3. **用户体验**: 提供流畅的滚动和交互
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **分页支持**: 支持分页显示大量预览
2. **搜索过滤**: 支持搜索和过滤预览
3. **自定义布局**: 支持多种布局模式
4. **批量操作**: 支持批量管理预览
5. **动画效果**: 添加列表变化的动画

该组件为链接预览的批量显示提供了简洁而灵活的解决方案，是消息系统中重要的展示组件。

# Emoji Picker Mobile - 移动端表情选择器

## 概述

`emoji_picker_mobile.js` 实现了专为移动端设计的表情符号选择器组件。该组件将标准的表情选择器包装在一个对话框中，提供了适合移动设备的全屏显示体验，解决了移动端屏幕空间有限的问题。

## 文件信息
- **路径**: `/mail/static/src/core/common/emoji_picker_mobile.js`
- **行数**: 23
- **模块**: `@mail/core/common/emoji_picker_mobile`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架
'@web/core/dialog/dialog'             // 对话框组件
'@web/core/emoji_picker/emoji_picker' // 表情选择器组件
```

## 组件定义

### EmojiPickerMobile 类

```javascript
class EmojiPickerMobile extends Component {
    static components = { 
        Dialog,      // 对话框组件
        EmojiPicker  // 表情选择器组件
    };
    static props = EMOJI_PICKER_PROPS;  // 继承标准表情选择器的Props
}
```

## Props 配置

### 继承的Props

```javascript
static props = EMOJI_PICKER_PROPS;
```

该组件直接继承了标准 `EmojiPicker` 组件的所有 Props，包括：

- **`onSelect`**: 表情选择回调函数
- **`categories`**: 表情分类配置
- **`searchEnabled`**: 是否启用搜索功能
- **`recentEnabled`**: 是否启用最近使用功能
- **`skinToneEnabled`**: 是否启用肤色选择
- **其他标准表情选择器属性**

## 模板结构

### 内联模板

```javascript
static template = xml`
    <Dialog 
        size="'lg'" 
        header="false" 
        footer="false" 
        contentClass="'o-discuss-mobileContextMenu d-flex position-absolute bottom-0 rounded-0 h-50 bg-100'"
    >
        <EmojiPicker t-props="props"/>
    </Dialog>
`;
```

### 模板分析

#### Dialog 配置
- **`size="'lg'"`**: 大尺寸对话框
- **`header="false"`**: 不显示头部
- **`footer="false"`**: 不显示底部
- **`contentClass`**: 自定义内容样式类

#### 样式类说明
- **`o-discuss-mobileContextMenu`**: 移动端上下文菜单样式
- **`d-flex`**: Flexbox 布局
- **`position-absolute`**: 绝对定位
- **`bottom-0`**: 底部对齐
- **`rounded-0`**: 无圆角
- **`h-50`**: 高度为50%
- **`bg-100`**: 背景色

## 设计特点

### 1. 移动端优化

#### 全屏体验
- 使用大尺寸对话框占据更多屏幕空间
- 底部对齐，符合移动端操作习惯
- 高度设置为50%，平衡显示和操作空间

#### 简化界面
- 移除对话框头部和底部
- 专注于表情选择功能
- 减少不必要的UI元素

### 2. 响应式设计

#### 位置策略
```css
/* 对应的CSS样式 */
.o-discuss-mobileContextMenu {
    position: absolute;
    bottom: 0;
    height: 50%;
    border-radius: 0;
}
```

#### 触摸友好
- 大尺寸的表情按钮
- 适合手指操作的间距
- 流畅的滚动体验

## 使用场景

### 1. 移动端聊天

```javascript
// 在移动端聊天中使用
if (this.env.services.ui.isSmall) {
    // 显示移动端表情选择器
    this.dialog.add(EmojiPickerMobile, {
        onSelect: (emoji) => this.addEmoji(emoji)
    });
} else {
    // 显示桌面端表情选择器
    this.showDesktopEmojiPicker();
}
```

### 2. 消息编辑器集成

```xml
<!-- 在移动端编辑器中 -->
<button t-if="ui.isSmall" 
        t-on-click="() => this.openMobileEmojiPicker()"
        class="btn btn-link">
    😀
</button>
```

### 3. 响应式表情选择

```javascript
// 响应式表情选择器
openEmojiPicker() {
    const EmojiComponent = this.ui.isSmall ? 
                          EmojiPickerMobile : 
                          EmojiPickerDesktop;
    
    this.dialog.add(EmojiComponent, {
        onSelect: this.handleEmojiSelect.bind(this)
    });
}
```

## 与标准表情选择器的对比

### 标准表情选择器
- 适用于桌面端
- 通常作为弹出框显示
- 相对较小的显示区域
- 支持悬停交互

### 移动端表情选择器
- 专为移动端优化
- 全屏对话框显示
- 更大的显示和操作区域
- 触摸友好的交互

## 技术实现

### 1. 组件包装

```javascript
// 简单的包装器模式
class EmojiPickerMobile extends Component {
    // 直接继承标准组件的Props
    static props = EMOJI_PICKER_PROPS;
    
    // 在对话框中包装标准组件
    static template = xml`
        <Dialog ...>
            <EmojiPicker t-props="props"/>
        </Dialog>
    `;
}
```

### 2. Props 透传

```xml
<!-- 所有Props都透传给内部的EmojiPicker -->
<EmojiPicker t-props="props"/>
```

### 3. 样式定制

```javascript
// 通过contentClass定制对话框样式
contentClass="'o-discuss-mobileContextMenu d-flex position-absolute bottom-0 rounded-0 h-50 bg-100'"
```

## 样式系统

### CSS 类组合

```css
/* 移动端上下文菜单基础样式 */
.o-discuss-mobileContextMenu {
    /* 移动端特定样式 */
}

/* Bootstrap 工具类 */
.d-flex          /* Flexbox 布局 */
.position-absolute /* 绝对定位 */
.bottom-0        /* 底部对齐 */
.rounded-0       /* 无圆角 */
.h-50           /* 高度50% */
.bg-100         /* 背景色 */
```

### 响应式考虑

```css
/* 移动端优化 */
@media (max-width: 768px) {
    .o-discuss-mobileContextMenu {
        /* 移动端特定调整 */
    }
}
```

## 性能优化

### 1. 组件复用

```javascript
// 复用标准表情选择器的所有功能
static components = { Dialog, EmojiPicker };
```

### 2. 懒加载

```javascript
// 只在需要时加载移动端表情选择器
const EmojiPickerMobile = lazy(() => 
    import('@mail/core/common/emoji_picker_mobile')
);
```

### 3. 内存管理

```javascript
// 对话框关闭时自动清理
// Dialog 组件会自动处理生命周期
```

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 将标准表情选择器适配到移动端环境
- 通过对话框包装改变显示方式

### 2. 装饰器模式 (Decorator Pattern)
- 为标准表情选择器添加移动端特性
- 不修改原组件的功能

### 3. 策略模式 (Strategy Pattern)
- 根据设备类型选择不同的显示策略
- 移动端 vs 桌面端的不同实现

## 可访问性

### 触摸优化

- 大尺寸的触摸目标
- 适当的间距避免误触
- 流畅的滚动和选择体验

### 键盘导航

- 继承标准表情选择器的键盘支持
- 在移动端虚拟键盘环境下工作

## 注意事项

1. **屏幕适配**: 确保在不同尺寸的移动设备上正常显示
2. **性能考虑**: 大量表情符号的渲染性能
3. **用户体验**: 快速响应的触摸交互
4. **兼容性**: 与不同移动浏览器的兼容性

## 扩展建议

1. **手势支持**: 添加滑动手势导航
2. **搜索优化**: 优化移动端的搜索体验
3. **分类导航**: 改进分类切换的移动端体验
4. **自定义表情**: 支持自定义表情符号
5. **最近使用**: 优化最近使用表情的显示

## 使用示例

### 基础使用

```javascript
// 在移动端显示表情选择器
this.dialog.add(EmojiPickerMobile, {
    onSelect: (emoji) => {
        console.log('选择的表情:', emoji);
        this.insertEmoji(emoji);
    }
});
```

### 集成到编辑器

```javascript
// 编辑器中的表情按钮
onEmojiButtonClick() {
    if (this.ui.isSmall) {
        this.dialog.add(EmojiPickerMobile, {
            onSelect: (emoji) => this.composer.addEmoji(emoji)
        });
    }
}
```

该组件虽然简单，但为移动端用户提供了优化的表情选择体验，是响应式设计的良好实践。

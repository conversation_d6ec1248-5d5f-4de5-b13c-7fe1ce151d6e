# Attachment Upload Service - 附件上传服务

## 概述

`attachment_upload_service.js` 实现了 Odoo 邮件系统中的附件上传服务，负责管理文件上传的完整生命周期，包括上传进度跟踪、错误处理、状态管理等功能。该服务作为 Odoo 服务注册系统的一部分，提供了统一的附件上传接口。

## 文件信息
- **路径**: `/mail/static/src/core/common/attachment_upload_service.js`
- **行数**: 204
- **模块**: `@mail/core/common/attachment_upload_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // EventBus 事件总线
'@web/core/l10n/translation'   // 国际化翻译
'@web/core/registry'           // 服务注册表
'@web/core/utils/concurrency'  // 并发工具（Deferred）
```

## 服务架构

### AttachmentUploadService 类

核心的附件上传服务类，管理所有上传相关的操作：

```javascript
class AttachmentUploadService {
    constructor(env, services) {
        this.setup(env, services);
    }
}
```

#### 核心属性

```javascript
// 服务依赖
this.env                        // Odoo 环境对象
this.fileUploadService         // 文件上传服务
this.store                     // 邮件存储服务
this.notificationService       // 通知服务

// 状态管理
this.nextId = -1               // 临时ID生成器（负数）
this.abortByAttachmentId       // Map: 附件ID -> 中止函数
this.deferredByAttachmentId    // Map: 附件ID -> Deferred对象
this.uploadingAttachmentIds    // Set: 正在上传的附件ID集合
this.targetsByTmpId           // Map: 临时ID -> {composer, thread}
this._fileUploadBus           // 内部事件总线
```

## 核心功能

### 1. 事件监听机制

服务通过监听文件上传服务的事件来管理上传流程：

#### FILE_UPLOAD_ADDED 事件
```javascript
this.fileUploadService.bus.addEventListener("FILE_UPLOAD_ADDED", ({ detail: { upload } }) => {
    const tmpId = parseInt(upload.data.get("temporary_id"));
    if (!this.uploadingAttachmentIds.has(tmpId)) return;
    
    const { thread, composer } = this.targetsByTmpId.get(tmpId);
    const tmpUrl = upload.data.get("tmp_url");
    this.abortByAttachmentId.set(tmpId, upload.xhr.abort.bind(upload.xhr));
    
    const attachment = this.store.Attachment.insert(
        this._makeAttachmentData(upload, tmpId, composer ? undefined : thread, tmpUrl)
    );
    composer?.attachments.push(attachment);
});
```

**功能说明**:
- 文件开始上传时触发
- 创建临时附件记录
- 设置中止函数
- 将附件添加到编辑器（如果存在）

#### FILE_UPLOAD_LOADED 事件
```javascript
this.fileUploadService.bus.addEventListener("FILE_UPLOAD_LOADED", ({ detail: { upload } }) => {
    // 处理上传完成的逻辑
    const response = JSON.parse(upload.xhr.response);
    if (response.error) {
        this.notificationService.add(response.error, { type: "danger" });
        return;
    }
    this._processLoaded(thread, composer, attachmentData, tmpId, def);
});
```

**错误处理**:
- **413 状态码**: 文件过大错误
- **非200状态码**: 服务器错误
- **响应错误**: 业务逻辑错误

#### FILE_UPLOAD_ERROR 事件
```javascript
this.fileUploadService.bus.addEventListener("FILE_UPLOAD_ERROR", ({ detail: { upload } }) => {
    const tmpId = parseInt(upload.data.get("temporary_id"));
    this.deferredByAttachmentId.get(tmpId).resolve();
    this._cleanupUploading(tmpId);
});
```

### 2. 上传流程管理

#### upload() - 主要上传方法
```javascript
async upload(thread, composer, file, options) {
    const tmpId = this.nextId--;
    const tmpURL = URL.createObjectURL(file);
    return this._upload(thread, composer, file, options, tmpId, tmpURL);
}
```

#### _upload() - 内部上传实现
```javascript
async _upload(thread, composer, file, options, tmpId, tmpURL) {
    this.targetsByTmpId.set(tmpId, { composer, thread });
    this.uploadingAttachmentIds.add(tmpId);
    
    await this.fileUploadService.upload(this.getUploadURL(thread), [file], {
        buildFormData: (formData) => {
            this._buildFormData(formData, tmpURL, thread, composer, tmpId, options);
        },
    });
    
    const uploadDoneDeferred = new Deferred();
    this.deferredByAttachmentId.set(tmpId, uploadDoneDeferred);
    return uploadDoneDeferred;
}
```

**上传流程**:
1. 生成临时ID和临时URL
2. 注册上传目标信息
3. 调用文件上传服务
4. 返回 Deferred 对象用于异步处理

### 3. 表单数据构建

#### _buildFormData() - 构建上传表单
```javascript
_buildFormData(formData, tmpURL, thread, composer, tmpId, options) {
    formData.append("thread_id", thread.id);
    formData.append("tmp_url", tmpURL);
    formData.append("thread_model", thread.model);
    formData.append("is_pending", Boolean(composer));
    formData.append("temporary_id", tmpId);
    if (options?.activity) {
        formData.append("activity_id", options.activity.id);
    }
    return formData;
}
```

**表单字段说明**:
- `thread_id`: 线程ID
- `tmp_url`: 临时文件URL
- `thread_model`: 线程模型名称
- `is_pending`: 是否为待发送状态（编辑器中）
- `temporary_id`: 临时ID
- `activity_id`: 活动ID（可选）

### 4. 附件数据创建

#### _makeAttachmentData() - 创建附件数据
```javascript
_makeAttachmentData(upload, tmpId, thread, tmpUrl) {
    return {
        filename: upload.title,
        id: tmpId,
        mimetype: upload.type,
        name: upload.title,
        thread,
        extension: upload.title.split(".").pop(),
        uploading: true,
        tmpUrl,
    };
}
```

### 5. 上传完成处理

#### _processLoaded() - 处理上传完成
```javascript
_processLoaded(thread, composer, { data }, tmpId, def) {
    const { Attachment } = this.store.insert(data);
    const [attachment] = Attachment;
    
    if (composer) {
        const index = composer.attachments.findIndex(({ id }) => id === tmpId);
        if (index >= 0) {
            composer.attachments[index] = attachment;
        } else {
            composer.attachments.push(attachment);
        }
    }
    
    def.resolve(attachment);
    this._fileUploadBus.trigger("UPLOAD", thread);
    this._cleanupUploading(tmpId);
}
```

**处理步骤**:
1. 插入服务器返回的附件数据
2. 更新编辑器中的临时附件
3. 解析 Deferred 对象
4. 触发上传完成事件
5. 清理临时数据

### 6. 资源清理

#### _cleanupUploading() - 清理上传状态
```javascript
_cleanupUploading(tmpId) {
    this.abortByAttachmentId.delete(tmpId);
    this.deferredByAttachmentId.delete(tmpId);
    this.uploadingAttachmentIds.delete(tmpId);
    this.targetsByTmpId.delete(tmpId);
    this.store.Attachment.get(tmpId).remove();
}
```

### 7. 取消上传

#### unlink() - 取消/删除附件
```javascript
async unlink(attachment) {
    if (this.uploadingAttachmentIds.has(attachment.id)) {
        const deferred = this.deferredByAttachmentId.get(attachment.id);
        const abort = this.abortByAttachmentId.get(attachment.id);
        this._cleanupUploading(attachment.id);
        deferred.resolve();
        abort();
        return;
    }
    await attachment.remove();
}
```

### 8. 事件监听

#### onFileUploaded() - 监听上传完成
```javascript
onFileUploaded(thread, onFileUploaded) {
    this._fileUploadBus.addEventListener("UPLOAD", ({ detail }) => {
        if (thread.eq(detail)) {
            onFileUploaded();
        }
    });
}
```

## 服务注册

```javascript
const attachmentUploadService = {
    dependencies: ["file_upload", "mail.store", "notification"],
    start(env, services) {
        return new AttachmentUploadService(env, services);
    },
};

registry.category("services").add("mail.attachment_upload", attachmentUploadService);
```

## 设计模式

### 1. 服务模式 (Service Pattern)
- 作为 Odoo 服务系统的一部分注册
- 提供统一的附件上传接口

### 2. 事件驱动模式 (Event-Driven Pattern)
- 通过事件总线监听上传状态变化
- 解耦上传逻辑和UI更新

### 3. 异步模式 (Async Pattern)
- 使用 Deferred 对象管理异步上传
- 支持上传取消和错误处理

### 4. 状态管理模式
- 集中管理所有上传状态
- 提供完整的生命周期管理

## 使用场景

1. **邮件编辑器**: 在撰写邮件时上传附件
2. **聊天窗口**: 在聊天中发送文件
3. **批量上传**: 同时上传多个文件
4. **拖拽上传**: 支持拖拽文件上传

## 注意事项

1. **内存管理**: 及时清理临时URL和状态数据
2. **错误处理**: 完善的错误分类和用户提示
3. **并发控制**: 支持多文件同时上传
4. **状态同步**: 确保UI状态与上传状态一致

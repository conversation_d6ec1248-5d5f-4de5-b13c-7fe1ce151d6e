# Follower Model - 关注者模型

## 概述

`follower_model.js` 定义了 Odoo 邮件系统中的关注者模型类，用于管理线程的关注者信息。该模型处理用户对特定线程的关注关系，包括关注状态、权限检查、取消关注等功能，是邮件系统订阅机制的核心数据结构。

## 文件信息
- **路径**: `/mail/static/src/core/common/follower_model.js`
- **行数**: 52
- **模块**: `@mail/core/common/follower_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // Record 基类
```

## 类定义

### Follower 类

```javascript
class Follower extends Record {
    static id = "id";           // 主键字段
    static records = {};        // 静态记录集合
}
```

## 核心属性

### 基础属性

```javascript
id: number;                     // 关注者ID
is_active: boolean;             // 是否活跃状态
```

### 关系属性

```javascript
thread = Record.one("Thread");     // 关联的线程
partner = Record.one("Persona");   // 关联的用户角色
```

### 属性详细说明

#### 1. id (关注者ID)
- **类型**: `number`
- **用途**: 唯一标识关注者记录
- **示例**: `123`, `456`

#### 2. is_active (活跃状态)
- **类型**: `boolean`
- **用途**: 表示关注者是否处于活跃状态
- **默认**: 通常为 `true`

#### 3. thread (关联线程)
- **类型**: Thread 模型的一对一关系
- **用途**: 被关注的线程对象
- **功能**: 确定关注的目标

#### 4. partner (关联用户)
- **类型**: Persona 模型的一对一关系
- **用途**: 关注者的用户信息
- **功能**: 标识谁在关注

## 计算属性

### isEditable - 可编辑性检查

```javascript
get isEditable() {
    const hasWriteAccess = this.thread ? this.thread.hasWriteAccess : false;
    return this.partner.eq(this.store.self) ? this.thread.hasReadAccess : hasWriteAccess;
}
```

**权限逻辑**:
1. **自己的关注**: 如果关注者是当前用户自己
   - 只需要线程的读取权限即可编辑
   - 用户可以管理自己的关注状态

2. **他人的关注**: 如果关注者是其他用户
   - 需要线程的写入权限才能编辑
   - 只有管理员或有权限的用户可以管理他人关注

**使用场景**:
- 显示/隐藏取消关注按钮
- 控制关注者列表的编辑权限
- 权限验证

## 核心方法

### 1. remove() - 取消关注

```javascript
async remove() {
    await this.store.env.services.orm.call(this.thread.model, "message_unsubscribe", [
        [this.thread.id],
        [this.partner.id],
    ]);
    this.delete();
}
```

**取消关注流程**:
1. **服务器调用**: 调用后端的 `message_unsubscribe` 方法
2. **参数传递**: 
   - 线程ID数组: `[this.thread.id]`
   - 用户ID数组: `[this.partner.id]`
3. **本地删除**: 从本地记录中删除关注者记录

**异步处理**:
- 使用 `async/await` 确保服务器操作完成
- 先更新服务器，再更新本地状态

### 2. removeRecipient() - 移除收件人

```javascript
removeRecipient() {
    this.thread.recipients.delete(this);
}
```

**功能**:
- 从线程的收件人列表中移除当前关注者
- 本地操作，不涉及服务器调用
- 用于临时的UI状态更新

## 使用场景

### 1. 显示关注者列表

```javascript
// 获取线程的所有关注者
const followers = thread.followers;

// 渲染关注者列表
const renderFollowers = (followers) => {
    return followers.map(follower => ({
        id: follower.id,
        name: follower.partner.name,
        avatar: follower.partner.avatarUrl,
        isEditable: follower.isEditable,
        isActive: follower.is_active
    }));
};
```

### 2. 关注者权限管理

```javascript
// 检查用户是否可以管理关注者
const canManageFollower = (follower, currentUser) => {
    return follower.isEditable;
};

// 显示管理按钮
if (canManageFollower(follower, store.self)) {
    showUnfollowButton(follower);
}
```

### 3. 取消关注操作

```javascript
// 取消关注确认对话框
const confirmUnfollow = async (follower) => {
    const confirmed = await showConfirmDialog({
        title: "取消关注",
        message: `确定要取消关注 ${follower.partner.name} 吗？`
    });
    
    if (confirmed) {
        try {
            await follower.remove();
            showSuccessMessage("已取消关注");
        } catch (error) {
            showErrorMessage("取消关注失败");
        }
    }
};
```

### 4. 关注状态检查

```javascript
// 检查用户是否关注了线程
const isUserFollowing = (thread, user) => {
    return thread.followers.some(follower => 
        follower.partner.eq(user) && follower.is_active
    );
};

// 切换关注状态
const toggleFollow = async (thread, user) => {
    const follower = thread.followers.find(f => f.partner.eq(user));
    
    if (follower) {
        await follower.remove();  // 取消关注
    } else {
        await thread.follow(user);  // 添加关注
    }
};
```

## 关注者管理

### 1. 批量取消关注

```javascript
// 批量取消关注
const batchUnfollow = async (followers) => {
    const promises = followers.map(follower => follower.remove());
    
    try {
        await Promise.all(promises);
        showSuccessMessage(`已取消 ${followers.length} 个关注`);
    } catch (error) {
        showErrorMessage("批量取消关注失败");
    }
};
```

### 2. 关注者搜索

```javascript
// 搜索关注者
const searchFollowers = (followers, query) => {
    const lowerQuery = query.toLowerCase();
    
    return followers.filter(follower =>
        follower.partner.name.toLowerCase().includes(lowerQuery) ||
        follower.partner.email?.toLowerCase().includes(lowerQuery)
    );
};
```

### 3. 关注者分组

```javascript
// 按状态分组关注者
const groupFollowersByStatus = (followers) => {
    return {
        active: followers.filter(f => f.is_active),
        inactive: followers.filter(f => !f.is_active)
    };
};

// 按权限分组关注者
const groupFollowersByPermission = (followers) => {
    return {
        editable: followers.filter(f => f.isEditable),
        readonly: followers.filter(f => !f.isEditable)
    };
};
```

## 权限系统

### 1. 权限检查

```javascript
// 详细的权限检查
const checkFollowerPermissions = (follower, currentUser) => {
    const permissions = {
        canView: true,  // 所有人都可以查看关注者
        canEdit: follower.isEditable,
        canRemove: follower.isEditable,
        isSelf: follower.partner.eq(currentUser)
    };
    
    return permissions;
};
```

### 2. 权限验证

```javascript
// 操作前的权限验证
const validateFollowerOperation = (follower, operation, currentUser) => {
    const permissions = checkFollowerPermissions(follower, currentUser);
    
    switch (operation) {
        case 'remove':
            if (!permissions.canRemove) {
                throw new Error("没有权限取消此关注");
            }
            break;
        case 'edit':
            if (!permissions.canEdit) {
                throw new Error("没有权限编辑此关注");
            }
            break;
        default:
            break;
    }
};
```

## 数据同步

### 1. 服务器同步

```javascript
// 同步关注者状态到服务器
const syncFollowerToServer = async (follower) => {
    try {
        await store.env.services.orm.write(
            'mail.followers',
            [follower.id],
            {
                is_active: follower.is_active
            }
        );
    } catch (error) {
        console.error('同步关注者状态失败:', error);
    }
};
```

### 2. 实时更新

```javascript
// 监听关注者变化
const watchFollowerChanges = (thread) => {
    thread.followers.forEach(follower => {
        follower.addEventListener('change', (event) => {
            if (event.field === 'is_active') {
                syncFollowerToServer(follower);
            }
        });
    });
};
```

## 性能优化

### 1. 关注者缓存

```javascript
// 缓存关注者查询结果
const followerCache = new Map();

const getCachedFollowers = (threadId) => {
    if (followerCache.has(threadId)) {
        return followerCache.get(threadId);
    }
    
    const followers = thread.followers;
    followerCache.set(threadId, followers);
    return followers;
};
```

### 2. 批量操作优化

```javascript
// 优化批量关注者操作
const optimizedBatchUnfollow = async (followers) => {
    // 按线程分组
    const groupedByThread = followers.reduce((groups, follower) => {
        const threadId = follower.thread.id;
        if (!groups[threadId]) {
            groups[threadId] = [];
        }
        groups[threadId].push(follower);
        return groups;
    }, {});
    
    // 按线程批量处理
    const promises = Object.entries(groupedByThread).map(([threadId, threadFollowers]) => {
        const thread = store.Thread.get(threadId);
        const partnerIds = threadFollowers.map(f => f.partner.id);
        
        return store.env.services.orm.call(
            thread.model,
            "message_unsubscribe",
            [[thread.id], partnerIds]
        );
    });
    
    await Promise.all(promises);
    
    // 本地删除
    followers.forEach(follower => follower.delete());
};
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 关注者订阅线程更新
- 自动接收相关通知

### 2. 策略模式 (Strategy Pattern)
- 根据用户权限采用不同的编辑策略
- 自己 vs 他人的不同权限处理

### 3. 命令模式 (Command Pattern)
- 取消关注操作的封装
- 支持撤销和重做

## 注意事项

1. **权限控制**: 严格检查关注者的编辑权限
2. **数据一致性**: 确保本地和服务器状态同步
3. **错误处理**: 妥善处理网络错误和权限错误
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **关注类型**: 支持不同类型的关注（全部、提及等）
2. **通知设置**: 允许关注者自定义通知偏好
3. **关注历史**: 记录关注和取消关注的历史
4. **智能推荐**: 推荐相关的关注对象
5. **批量管理**: 提供更强大的批量管理功能

该模型为邮件系统提供了完整的关注者管理功能，支持灵活的权限控制和高效的订阅机制。

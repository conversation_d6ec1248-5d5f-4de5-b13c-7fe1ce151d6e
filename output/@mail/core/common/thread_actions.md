# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 邮件系统中的线程操作功能，定义了线程的各种操作行为（如折叠聊天窗口、重命名线程、关闭窗口、搜索消息等）。该模块使用注册表模式管理所有线程操作，并提供了一个钩子函数供组件使用，支持操作的分组、排序和状态管理。

## 文件信息
- **路径**: `/mail/static/src/core/common/thread_actions.js`
- **行数**: 256
- **模块**: `@mail/core/common/thread_actions`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/l10n/translation'                   // 国际化
'@web/core/registry'                           // 注册表
'@mail/core/common/search_messages_panel'      // 搜索消息面板
```

## 核心组件

### threadActionsRegistry

线程操作注册表，管理所有可用的线程操作：

```javascript
const threadActionsRegistry = registry.category("mail.thread/actions");
```

## 注册的线程操作

### 1. 折叠聊天窗口 (fold-chat-window)

```javascript
threadActionsRegistry.add("fold-chat-window", {
    condition(component) {
        return (
            !component.ui.isSmall &&
            component.props.chatWindow &&
            component.props.chatWindow.thread
        );
    },
    icon: "fa fa-fw fa-minus",
    name(component) {
        return !component.props.chatWindow?.isOpen ? _t("Open") : _t("Fold");
    },
    open(component) {
        component.toggleFold();
    },
    displayActive(component) {
        return !component.props.chatWindow?.isOpen;
    },
    sequence: 99,
    sequenceQuick: 20,
});
```

**功能说明**:
- **条件**: 非小屏幕且有聊天窗口和线程
- **图标**: 减号图标
- **名称**: 根据窗口状态显示"打开"或"折叠"
- **操作**: 切换窗口折叠状态
- **活跃显示**: 窗口关闭时显示为活跃状态

### 2. 重命名线程 (rename-thread)

```javascript
threadActionsRegistry.add("rename-thread", {
    condition(component) {
        return (
            component.thread &&
            component.props.chatWindow?.isOpen &&
            (component.thread.is_editable || component.thread.channel_type === "chat")
        );
    },
    icon: "fa fa-fw fa-pencil",
    name: _t("Rename Thread"),
    open(component) {
        component.state.editingName = true;
    },
    sequence: 30,
    sequenceGroup: 20,
});
```

**功能说明**:
- **条件**: 线程存在、窗口打开且线程可编辑或为聊天类型
- **图标**: 铅笔图标
- **操作**: 进入重命名编辑模式
- **分组**: 属于第20组操作

### 3. 关闭窗口 (close)

```javascript
threadActionsRegistry.add("close", {
    condition(component) {
        return component.props.chatWindow;
    },
    icon: "oi fa-fw oi-close",
    name: _t("Close Chat Window (ESC)"),
    open(component) {
        component.close();
    },
    sequence: 100,
    sequenceQuick: 10,
});
```

**功能说明**:
- **条件**: 存在聊天窗口
- **图标**: 关闭图标
- **操作**: 关闭聊天窗口
- **快捷序列**: 快捷操作中的第10位

### 4. 搜索消息 (search-messages)

```javascript
threadActionsRegistry.add("search-messages", {
    component: SearchMessagesPanel,
    condition(component) {
        return (
            ["discuss.channel", "mail.box"].includes(component.thread?.model) &&
            (!component.props.chatWindow || component.props.chatWindow.isOpen)
        );
    },
    panelOuterClass: "o-mail-SearchMessagesPanel bg-inherit",
    icon: "oi oi-fw oi-search",
    iconLarge: "oi oi-fw fa-lg oi-search",
    name: _t("Search Messages"),
    nameActive: _t("Close Search"),
    sequence: 20,
    sequenceGroup: 20,
    setup(action) {
        useSubEnv({
            searchMenu: {
                open: () => action.open(),
                close: () => {
                    if (action.isActive) {
                        action.close();
                    }
                },
            },
        });
    },
    toggle: true,
});
```

**功能说明**:
- **组件**: 使用 SearchMessagesPanel 组件
- **条件**: 讨论频道或邮箱模型，且窗口打开
- **面板样式**: 自定义面板外层样式
- **图标**: 搜索图标（普通和大尺寸）
- **切换**: 支持开关切换
- **环境设置**: 提供搜索菜单的环境变量

## 操作转换函数

### transformAction()

将注册表中的操作定义转换为组件可用的操作对象：

```javascript
function transformAction(component, id, action) {
    return {
        // 关闭操作
        close() {
            if (this.toggle) {
                component.threadActions.activeAction = component.threadActions.actionStack.pop();
            }
            action.close?.(component, this);
        },
        
        // 组件相关属性
        component: action.component,
        get componentCondition() {
            return this.isActive && this.component && this.condition && !this.popover;
        },
        get componentProps() {
            return action.componentProps?.(this, component);
        },
        
        // 条件和状态
        get condition() {
            return action.condition(component);
        },
        get disabledCondition() {
            return action.disabledCondition?.(component);
        },
        
        // 图标和显示
        get icon() {
            return typeof action.icon === "function" ? action.icon(component) : action.icon;
        },
        get iconLarge() {
            return typeof action.iconLarge === "function"
                ? action.iconLarge(component)
                : action.iconLarge ?? action.icon;
        },
        
        // 标识和状态
        id,
        get isActive() {
            return id === component.threadActions.activeAction?.id;
        },
        get name() {
            const res = this.isActive && action.nameActive ? action.nameActive : action.name;
            return typeof res === "function" ? res(component) : res;
        },
        
        // 操作方法
        onSelect({ keepPrevious } = {}) {
            if (this.toggle && this.isActive) {
                this.close();
            } else {
                this.open({ keepPrevious });
            }
        },
        
        open({ keepPrevious } = {}) {
            if (this.toggle) {
                if (component.threadActions.activeAction && keepPrevious) {
                    component.threadActions.actionStack.push(component.threadActions.activeAction);
                }
                component.threadActions.activeAction = this;
            }
            action.open?.(component, this);
        },
        
        // 样式和排序
        get panelOuterClass() {
            return typeof action.panelOuterClass === "function"
                ? action.panelOuterClass(component)
                : action.panelOuterClass;
        },
        
        popover: null,
        
        get sequence() {
            return typeof action.sequence === "function"
                ? action.sequence(component)
                : action.sequence;
        },
        get sequenceGroup() {
            return typeof action.sequenceGroup === "function"
                ? action.sequenceGroup(component)
                : action.sequenceGroup;
        },
        get sequenceQuick() {
            return typeof action.sequenceQuick === "function"
                ? action.sequenceQuick(component)
                : action.sequenceQuick;
        },
        
        // 其他属性
        setup: action.setup,
        text: action.text,
        toggle: action.toggle,
    };
}
```

## useThreadActions 钩子

### 钩子函数

```javascript
function useThreadActions() {
    const component = useComponent();
    const transformedActions = threadActionsRegistry
        .getEntries()
        .map(([id, action]) => transformAction(component, id, action));
    
    // 执行操作设置
    for (const action of transformedActions) {
        if (action.setup) {
            action.setup(action);
        }
    }
    
    const state = useState({
        get actions() {
            return transformedActions
                .filter((action) => action.condition)
                .sort((a1, a2) => a1.sequence - a2.sequence);
        },
        
        get partition() {
            const actions = transformedActions.filter((action) => action.condition);
            
            // 快捷操作
            const quick = actions
                .filter((a) => a.sequenceQuick)
                .sort((a1, a2) => a1.sequenceQuick - a2.sequenceQuick);
            
            // 分组操作
            const grouped = actions.filter((a) => a.sequenceGroup);
            const groups = {};
            for (const a of grouped) {
                if (!(a.sequenceGroup in groups)) {
                    groups[a.sequenceGroup] = [];
                }
                groups[a.sequenceGroup].push(a);
            }
            
            const sortedGroups = Object.entries(groups).sort(
                ([groupId1], [groupId2]) => groupId1 - groupId2
            );
            for (const [, actions] of sortedGroups) {
                actions.sort((a1, a2) => a1.sequence - a2.sequence);
            }
            const group = sortedGroups.map(([groupId, actions]) => actions);
            
            // 其他操作
            const other = actions
                .filter((a) => !a.sequenceQuick & !a.sequenceGroup)
                .sort((a1, a2) => a1.sequence - a2.sequence);
            
            return { quick, group, other };
        },
        
        actionStack: [],      // 操作栈
        activeAction: null,   // 当前活跃操作
    });
    
    return state;
}
```

### 钩子特性

#### 1. 动态过滤和排序
- 根据条件动态过滤可用操作
- 按 sequence 属性自动排序
- 响应式更新操作列表

#### 2. 操作分区
- **快捷操作** (`quick`): 有 `sequenceQuick` 的操作
- **分组操作** (`group`): 有 `sequenceGroup` 的操作，按组分类
- **其他操作** (`other`): 不属于快捷或分组的操作

#### 3. 状态管理
- **操作栈** (`actionStack`): 支持操作的嵌套和返回
- **活跃操作** (`activeAction`): 当前激活的操作
- **切换支持**: 支持操作的开关切换

## 操作属性

### 基础属性

- **`condition`**: 显示条件函数
- **`icon`**: 图标（字符串或函数）
- **`iconLarge`**: 大图标（可选）
- **`name`**: 操作名称（字符串或函数）
- **`nameActive`**: 激活时的名称（可选）
- **`open`**: 打开操作的处理函数
- **`close`**: 关闭操作的处理函数（可选）

### 排序属性

- **`sequence`**: 基本排序序号
- **`sequenceQuick`**: 快捷操作排序序号
- **`sequenceGroup`**: 分组操作的组号

### 扩展属性

- **`component`**: 关联的组件
- **`componentProps`**: 组件属性函数
- **`panelOuterClass`**: 面板外层样式
- **`setup`**: 设置函数
- **`toggle`**: 是否为切换操作
- **`text`**: 按钮文本

## 使用场景

### 1. 聊天窗口操作

```javascript
// 在聊天窗口组件中
const threadActions = useThreadActions();

// 渲染快捷操作
threadActions.partition.quick.forEach(action => {
    if (action.condition) {
        renderQuickAction(action);
    }
});
```

### 2. 自定义操作

```javascript
// 添加自定义线程操作
threadActionsRegistry.add("custom-action", {
    condition: (component) => component.thread?.canCustomAction,
    icon: "fa-custom",
    name: _t("Custom Action"),
    open: (component) => component.handleCustomAction(),
    sequence: 200,
});
```

### 3. 操作分组显示

```javascript
// 按分组显示操作
const { quick, group, other } = threadActions.partition;

// 快捷操作区域
renderQuickActions(quick);

// 分组操作区域
group.forEach(groupActions => {
    renderActionGroup(groupActions);
});

// 其他操作区域
renderOtherActions(other);
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 使用注册表管理所有线程操作
- 支持动态添加和移除操作

### 2. 策略模式 (Strategy Pattern)
- 每个操作都是一个策略
- 根据条件选择可用的策略

### 3. 状态模式 (State Pattern)
- 操作的激活/非激活状态
- 切换操作的开关状态

### 4. 命令模式 (Command Pattern)
- 每个操作封装为命令对象
- 支持操作栈和状态管理

## 性能优化

### 1. 懒加载计算

```javascript
// 使用 getter 懒加载计算操作列表
get actions() {
    return transformedActions
        .filter((action) => action.condition)
        .sort((a1, a2) => a1.sequence - a2.sequence);
}
```

### 2. 条件缓存

```javascript
// 使用 getter 缓存条件计算结果
get condition() {
    return action.condition(component);
}
```

### 3. 分区优化

```javascript
// 一次性计算所有分区，避免重复过滤
get partition() {
    const actions = transformedActions.filter((action) => action.condition);
    // ... 分区逻辑
}
```

## 注意事项

1. **权限检查**: 确保操作的权限检查正确
2. **状态同步**: 保持操作状态与组件状态一致
3. **内存管理**: 及时清理操作栈和状态
4. **国际化**: 所有文本都需要国际化处理

## 扩展建议

1. **操作历史**: 记录操作历史和撤销功能
2. **快捷键**: 为操作添加键盘快捷键
3. **权限细化**: 更细粒度的权限控制
4. **操作预览**: 提供操作效果预览
5. **批量操作**: 支持批量线程操作

该模块为线程提供了完整的操作功能，支持灵活的操作管理和用户交互，是线程界面的重要组成部分。

# Message Model - 消息模型

## 概述

`message_model.js` 定义了 Odoo 邮件系统中的消息模型类，是整个邮件系统的核心数据结构。该模型管理消息的内容、状态、关系、权限等所有方面，支持消息的创建、编辑、删除、已读状态跟踪、反应管理等功能。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_model.js`
- **行数**: 507
- **模块**: `@mail/core/common/message_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'        // Record 基类
'@mail/utils/common/format'       // 格式化工具
'@web/core/network/rpc'           // RPC 网络请求
'@web/core/browser/browser'       // 浏览器工具
'@web/core/l10n/translation'      // 国际化
'@web/core/user'                  // 用户服务
'@web/core/utils/urls'            // URL 工具
'@web/core/browser/router'        // 路由工具
'@odoo/owl'                       // OWL 框架
```

## 类定义

### Message 类

```javascript
class Message extends Record {
    static id = "id";        // 主键字段
    static records = {};     // 静态记录集合
}
```

## 核心属性

### 基础属性

```javascript
// 基本标识
id: number | string;         // 消息ID
subject: string;             // 主题
default_subject: string;     // 默认主题
body: string;               // 消息正文（HTML格式）

// 时间属性
date: DateTime;             // 消息日期
create_date: DateTime;      // 创建日期
write_date: DateTime;       // 修改日期
scheduledDatetime: DateTime; // 计划发送时间

// 状态属性
is_discussion: boolean;     // 是否为讨论
is_note: boolean;          // 是否为笔记
is_transient: boolean;     // 是否为临时消息
needaction: boolean;       // 是否需要操作
starred: boolean;          // 是否已标星
```

### 关系字段

#### 作者和收件人
```javascript
author = Record.one("Persona");           // 消息作者
recipients = Record.many("Persona");      // 收件人列表
```

#### 线程关系
```javascript
thread = Record.one("Thread");            // 所属线程

threadAsNeedaction = Record.one("Thread", {
    compute() {
        if (this.needaction) {
            return this.thread;
        }
    },
});

threadAsNewest = Record.one("Thread");     // 作为最新消息的线程
threadAsFirstUnread = Record.one("Thread", { inverse: "firstUnreadMessage" });
```

#### 附件和预览
```javascript
attachment_ids = Record.many("Attachment", { inverse: "message" });
linkPreviews = Record.many("LinkPreview", { 
    inverse: "message", 
    onDelete: (r) => r.delete() 
});
```

#### 反应和通知
```javascript
reactions = Record.many("MessageReactions", {
    inverse: "message",
    sort: (r1, r2) => r1.sequence - r2.sequence,
});

notifications = Record.many("Notification", { inverse: "message" });
```

#### 编辑器关系
```javascript
composer = Record.one("Composer", { 
    inverse: "message", 
    onDelete: (r) => r.delete() 
});
```

#### 父消息关系
```javascript
parentMessage = Record.one("Message");  // 回复的父消息
```

## 计算属性

### 编辑状态

#### 是否已编辑
```javascript
edited = Record.attr(false, {
    compute() {
        return Boolean(
            new DOMParser()
                .parseFromString(this.body, "text/html")
                .querySelector(".o-mail-Message-edited")
        );
    },
});
```

#### 编辑权限
```javascript
get allowsEdition() {
    return this.store.self.isAdmin || this.isSelfAuthored;
}
```

### 已读状态管理

#### 所有人已读
```javascript
hasEveryoneSeen = Record.attr(false, {
    compute() {
        return this.thread?.membersThatCanSeen.every((m) => m.hasSeen(this));
    },
});
```

#### 有人已读
```javascript
hasSomeoneSeen = Record.attr(false, {
    compute() {
        return this.thread?.membersThatCanSeen
            .filter(({ persona }) => !persona.eq(this.author))
            .some((m) => m.hasSeen(this));
    },
});
```

#### 自己已读
```javascript
isReadBySelf = Record.attr(false, {
    compute() {
        return (
            this.thread?.selfMember?.seen_message_id?.id >= this.id &&
            this.thread?.selfMember?.new_message_separator > this.id
        );
    },
});
```

#### 有人已获取
```javascript
hasSomeoneFetched = Record.attr(false, {
    compute() {
        if (!this.thread) {
            return false;
        }
        const otherFetched = this.thread.channelMembers.filter(
            (m) => m.persona.notEq(this.author) && m.fetched_message_id?.id >= this.id
        );
        return otherFetched.length > 0;
    },
});
```

#### 早于最后已读消息
```javascript
isMessagePreviousToLastSelfMessageSeenByEveryone = Record.attr(false, {
    compute() {
        if (!this.thread?.lastSelfMessageSeenByEveryone) {
            return false;
        }
        return this.id < this.thread.lastSelfMessageSeenByEveryone.id;
    },
});
```

### 内容分析

#### 是否包含链接
```javascript
hasLink = Record.attr(false, {
    compute() {
        if (this.isBodyEmpty) {
            return false;
        }
        const div = document.createElement("div");
        div.innerHTML = this.body;
        return Boolean(div.querySelector("a:not([data-oe-model])"));
    },
});
```

#### 是否只包含表情符号
```javascript
onlyEmojis = Record.attr(false, {
    compute() {
        const div = document.createElement("div");
        div.innerHTML = this.body;
        const bodyWithoutTags = div.textContent;
        const withoutEmojis = bodyWithoutTags.replace(EMOJI_REGEX, "");
        return bodyWithoutTags.length > 0 && withoutEmojis.trim().length === 0;
    },
});
```

## 特殊属性

### 消息类型
```javascript
message_type: string;        // 消息类型
notificationType: string;    // 通知类型
subtype_description: string; // 子类型描述
```

### 翻译支持
```javascript
translationValue: string;    // 翻译值
translationSource: string;   // 翻译源
translationErrors: string;   // 翻译错误
```

### 跟踪和失败处理
```javascript
trackingValues: Object[];    // 跟踪值数组

// 消息发送失败时的重试回调
postFailRedo: (() => {}) | undefined;
```

## 更新方法

### 数据更新处理
```javascript
update(data) {
    super.update(data);
    if (this.isNotification && !this.notificationType) {
        const parser = new DOMParser();
        const htmlBody = parser.parseFromString(this.body, "text/html");
        this.notificationType = htmlBody.querySelector(".o_mail_notification")?.dataset.oeType;
    }
}
```

**功能说明**:
- 调用父类更新方法
- 自动解析通知类型
- 从HTML内容中提取通知元数据

## 消息状态

### 消息类型分类

1. **讨论消息** (`is_discussion: true`)
   - 正常的对话消息
   - 可以被回复和反应

2. **笔记消息** (`is_note: true`)
   - 内部笔记
   - 通常不对外部用户可见

3. **临时消息** (`is_transient: true`)
   - 临时显示的消息
   - 通常用于状态提示

4. **通知消息** (`message_type: "notification"`)
   - 系统通知
   - 自动生成的消息

### 已读状态层次

1. **获取** (Fetched): 消息已从服务器获取
2. **已读** (Seen): 用户已查看消息
3. **所有人已读**: 所有相关成员都已查看

## 使用场景

### 1. 创建新消息
```javascript
const message = store.Message.insert({
    body: "Hello World",
    author: currentUser,
    thread: currentThread,
    date: DateTime.now(),
});
```

### 2. 回复消息
```javascript
const replyMessage = store.Message.insert({
    body: "Reply content",
    author: currentUser,
    thread: originalMessage.thread,
    parentMessage: originalMessage,
});
```

### 3. 检查已读状态
```javascript
if (message.hasEveryoneSeen) {
    console.log("所有人都已读");
} else if (message.hasSomeoneSeen) {
    console.log("有人已读");
}
```

### 4. 编辑权限检查
```javascript
if (message.allowsEdition) {
    // 显示编辑按钮
    showEditButton();
}
```

## 性能优化

### 1. 计算属性缓存
- 使用 Record.attr 的 compute 功能
- 自动缓存复杂计算结果
- 依赖变化时自动重新计算

### 2. DOM 操作优化
- 在计算属性中创建临时 DOM 元素
- 避免在渲染过程中重复解析 HTML

### 3. 关系字段优化
- 使用 inverse 属性维护双向关系
- 自动排序减少手动排序开销

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 计算属性自动响应依赖变化
- 已读状态的自动更新

### 2. 策略模式 (Strategy Pattern)
- 根据消息类型采用不同的处理策略
- 不同的显示和交互逻辑

### 3. 装饰器模式 (Decorator Pattern)
- 通过计算属性为基础数据添加额外功能
- 不修改原始数据结构

## 注意事项

1. **HTML 安全**: 消息正文包含 HTML，需要注意 XSS 防护
2. **性能考虑**: 大量消息时需要虚拟化处理
3. **状态一致性**: 确保已读状态在多端同步
4. **内存管理**: 及时清理不再使用的消息对象

## 扩展建议

1. **消息加密**: 支持端到端加密消息
2. **消息撤回**: 实现消息撤回功能
3. **消息引用**: 支持引用其他消息
4. **富文本增强**: 支持更丰富的格式化选项
5. **离线支持**: 实现离线消息缓存和同步

该模型是邮件系统的数据核心，为所有消息相关功能提供了完整的数据结构和业务逻辑支持。

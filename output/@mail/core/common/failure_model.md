# Failure Model - 失败模型

## 概述

`failure_model.js` 定义了 Odoo 邮件系统中的失败模型类，用于管理邮件发送失败的情况。该模型聚合相关的失败通知，提供失败信息的统一视图，帮助用户了解和处理邮件发送过程中的错误，是邮件系统错误处理和用户反馈的重要组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/failure_model.js`
- **行数**: 84
- **模块**: `@mail/core/common/failure_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // Record 基类
'@odoo/owl'                    // OWL 框架（markRaw）
'@web/core/l10n/translation'   // 国际化
```

## 类定义

### Failure 类

```javascript
class Failure extends Record {
    static nextId = markRaw({ value: 1 });  // 下一个ID生成器
    static id = "id";                       // 主键字段
    static records = {};                    // 静态记录集合
}
```

### ID 生成机制

```javascript
static nextId = markRaw({ value: 1 });
```

**特性**:
- 使用 `markRaw` 标记为非响应式对象
- 自动递增的ID生成器
- 确保每个失败记录有唯一标识

## 核心属性

### 通知关系

```javascript
notifications = Record.many("Notification", {
    onUpdate() {
        if (this.notifications.length === 0) {
            this.delete();  // 无通知时自动删除失败记录
        } else {
            this.store.failures.add(this);  // 有通知时添加到失败集合
        }
    },
});
```

**自动管理机制**:
- **无通知时**: 自动删除失败记录
- **有通知时**: 自动添加到存储的失败集合
- **响应式更新**: 通知变化时自动触发更新

### 最后消息

```javascript
lastMessage = Record.one("Message", {
    compute() {
        let lastMsg = this.notifications[0]?.message;
        for (const notification of this.notifications) {
            if (lastMsg?.id < notification.message?.id) {
                lastMsg = notification.message;
            }
        }
        return lastMsg;
    },
});
```

**计算逻辑**:
1. 从第一个通知的消息开始
2. 遍历所有通知的消息
3. 比较消息ID，找到最新的消息
4. 返回ID最大的消息（最新消息）

## 计算属性

### 1. 模型名称

```javascript
get modelName() {
    return this.notifications?.[0]?.message?.thread?.modelName;
}
```

**功能**:
- 获取失败相关的模型显示名称
- 从第一个通知的消息线程获取
- 用于用户界面显示

### 2. 资源模型

```javascript
get resModel() {
    return this.notifications?.[0]?.message?.thread?.model;
}
```

**功能**:
- 获取失败相关的资源模型名称
- 技术标识符，用于系统内部处理

### 3. 资源ID集合

```javascript
get resIds() {
    return new Set([
        ...this.notifications.map((notif) => notif.message?.thread?.id).filter((id) => !!id),
    ]);
}
```

**功能**:
- 收集所有相关的资源ID
- 使用Set确保ID唯一性
- 过滤掉空值和无效ID

### 4. 失败类型

```javascript
get type() {
    return this.notifications?.[0]?.notification_type;
}
```

**类型值**:
- `'sms'`: 短信发送失败
- `'email'`: 邮件发送失败

### 5. 失败状态

```javascript
get status() {
    return this.notifications?.[0]?.notification_status;
}
```

**状态值**:
- 表示具体的失败状态
- 从第一个通知获取状态信息

### 6. 图标源

```javascript
get iconSrc() {
    return "/mail/static/src/img/smiley/mailfailure.jpg";
}
```

**功能**:
- 提供失败状态的图标
- 固定的失败图标路径
- 用于UI中的视觉反馈

### 7. 错误消息

```javascript
get body() {
    return _t("An error occurred when sending an email");
}
```

**功能**:
- 提供用户友好的错误描述
- 支持国际化
- 通用的错误消息文本

### 8. 时间戳

```javascript
get datetime() {
    return this.lastMessage?.datetime;
}
```

**功能**:
- 获取失败发生的时间
- 基于最后一条消息的时间
- 用于时间排序和显示

## 使用场景

### 1. 邮件发送失败处理

```javascript
// 创建邮件发送失败记录
const emailFailure = store.Failure.insert({
    notifications: [emailNotification]
});

console.log(`邮件发送失败: ${emailFailure.body}`);
console.log(`失败时间: ${emailFailure.datetime}`);
console.log(`影响的资源: ${Array.from(emailFailure.resIds)}`);
```

### 2. 短信发送失败处理

```javascript
// 创建短信发送失败记录
const smsFailure = store.Failure.insert({
    notifications: [smsNotification]
});

if (smsFailure.type === 'sms') {
    console.log('短信发送失败');
    showSmsFailureDialog(smsFailure);
}
```

### 3. 失败通知聚合

```javascript
// 将多个相关失败通知聚合到一个失败记录
const failure = store.Failure.insert({});
failure.notifications.add(notification1);
failure.notifications.add(notification2);
failure.notifications.add(notification3);

// 自动计算最新消息和相关资源
console.log(`聚合了 ${failure.notifications.length} 个失败通知`);
console.log(`最新失败消息: ${failure.lastMessage.id}`);
```

### 4. 失败状态显示

```javascript
// 在UI中显示失败信息
const renderFailure = (failure) => {
    return `
        <div class="failure-item">
            <img src="${failure.iconSrc}" alt="Failure" />
            <div class="failure-content">
                <div class="failure-message">${failure.body}</div>
                <div class="failure-time">${failure.datetime}</div>
                <div class="failure-type">Type: ${failure.type}</div>
                <div class="failure-status">Status: ${failure.status}</div>
            </div>
        </div>
    `;
};
```

## 失败管理

### 1. 失败记录清理

```javascript
// 清理已解决的失败记录
const cleanupResolvedFailures = () => {
    Object.values(store.Failure.records).forEach(failure => {
        if (failure.notifications.length === 0) {
            failure.delete();  // 自动删除机制会处理
        }
    });
};
```

### 2. 失败统计

```javascript
// 统计不同类型的失败
const getFailureStats = () => {
    const stats = { email: 0, sms: 0, total: 0 };
    
    Object.values(store.Failure.records).forEach(failure => {
        stats[failure.type] = (stats[failure.type] || 0) + 1;
        stats.total++;
    });
    
    return stats;
};
```

### 3. 失败重试

```javascript
// 重试失败的发送
const retryFailure = async (failure) => {
    const notifications = [...failure.notifications];
    
    for (const notification of notifications) {
        try {
            await retryNotification(notification);
            failure.notifications.delete(notification);
        } catch (error) {
            console.error('重试失败:', error);
        }
    }
    
    // 如果所有通知都重试成功，失败记录会自动删除
};
```

### 4. 失败分组

```javascript
// 按模型分组失败记录
const groupFailuresByModel = () => {
    const groups = {};
    
    Object.values(store.Failure.records).forEach(failure => {
        const model = failure.resModel;
        if (!groups[model]) {
            groups[model] = [];
        }
        groups[model].push(failure);
    });
    
    return groups;
};
```

## 错误处理

### 1. 安全访问

```javascript
// 安全访问失败属性
const getFailureInfo = (failure) => {
    return {
        type: failure.type || 'unknown',
        status: failure.status || 'unknown',
        modelName: failure.modelName || 'Unknown Model',
        resourceCount: failure.resIds.size,
        notificationCount: failure.notifications.length,
        hasLastMessage: Boolean(failure.lastMessage)
    };
};
```

### 2. 验证失败记录

```javascript
// 验证失败记录的完整性
const validateFailure = (failure) => {
    const issues = [];
    
    if (!failure.notifications || failure.notifications.length === 0) {
        issues.push('No notifications associated with failure');
    }
    
    if (!failure.type) {
        issues.push('Missing failure type');
    }
    
    if (!failure.lastMessage) {
        issues.push('No last message found');
    }
    
    return issues;
};
```

## 性能优化

### 1. 批量处理

```javascript
// 批量处理失败通知
const processBatchFailures = (notifications) => {
    // 按类型分组通知
    const groupedByType = notifications.reduce((groups, notif) => {
        const type = notif.notification_type;
        if (!groups[type]) {
            groups[type] = [];
        }
        groups[type].push(notif);
        return groups;
    }, {});
    
    // 为每种类型创建失败记录
    Object.entries(groupedByType).forEach(([type, notifs]) => {
        const failure = store.Failure.insert({});
        notifs.forEach(notif => failure.notifications.add(notif));
    });
};
```

### 2. 缓存计算结果

```javascript
// 缓存资源ID集合
let cachedResIds = null;
let lastNotificationUpdate = null;

get resIds() {
    const currentUpdate = this.notifications.length;
    
    if (cachedResIds === null || lastNotificationUpdate !== currentUpdate) {
        cachedResIds = new Set([
            ...this.notifications.map((notif) => notif.message?.thread?.id).filter((id) => !!id),
        ]);
        lastNotificationUpdate = currentUpdate;
    }
    
    return cachedResIds;
}
```

## 设计模式

### 1. 聚合模式 (Aggregate Pattern)
- 将相关的失败通知聚合到一个失败记录
- 提供统一的失败信息视图

### 2. 观察者模式 (Observer Pattern)
- 监听通知集合的变化
- 自动管理失败记录的生命周期

### 3. 策略模式 (Strategy Pattern)
- 根据失败类型采用不同的处理策略
- 支持不同类型失败的扩展

## 注意事项

1. **自动清理**: 失败记录会在无通知时自动删除
2. **内存管理**: 及时清理已解决的失败记录
3. **错误恢复**: 提供失败重试机制
4. **用户体验**: 提供清晰的错误信息和解决建议

## 扩展建议

1. **详细错误信息**: 提供更详细的错误原因和解决方案
2. **自动重试**: 实现智能的自动重试机制
3. **错误分类**: 按错误类型进行更细致的分类
4. **统计报告**: 提供失败统计和趋势分析
5. **通知机制**: 实现失败通知的推送机制

该模型为邮件系统提供了完整的失败处理机制，帮助用户及时发现和解决邮件发送问题，提升系统的可靠性和用户体验。

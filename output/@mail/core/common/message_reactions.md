# Message Reactions - 消息反应容器

## 概述

`message_reactions.js` 实现了 Odoo 邮件系统中的消息反应容器组件，用于管理和显示消息的所有表情反应。该组件集成了表情选择器和反应列表，提供了完整的表情反应交互功能，支持添加新反应和管理现有反应，是消息反应系统的核心容器组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_reactions.js`
- **行数**: 39
- **模块**: `@mail/core/common/message_reactions`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@mail/core/common/message_reaction_list'      // 消息反应列表组件
'@web/core/utils/hooks'                        // Web 核心钩子
'@web/core/emoji_picker/emoji_picker'          // 表情选择器
```

## 组件定义

### MessageReactions 类

```javascript
class MessageReactions extends Component {
    static props = ["message", "openReactionMenu"];
    static template = "mail.MessageReactions";
    static components = { MessageReactionList };
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    message: import("models").Message;          // 消息对象
    openReactionMenu: function;                 // 打开反应菜单回调
}
```

### Props 详细说明

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 包含反应的消息对象
  - 功能: 提供反应数据和操作接口

- **`openReactionMenu`** (必需):
  - 类型: 函数
  - 用途: 打开反应菜单的回调
  - 触发: 移动端或特定交互时调用

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 服务依赖
    this.store = useState(useService("mail.store"));
    this.ui = useService("ui");
    
    // DOM 引用
    this.addRef = useRef("add");
    
    // 表情选择器
    this.emojiPicker = useEmojiPicker(this.addRef, {
        onSelect: (emoji) => {
            const reaction = this.props.message.reactions.find(
                ({ content, personas }) =>
                    content === emoji && personas.find((persona) => persona.eq(this.store.self))
            );
            if (!reaction) {
                this.props.message.react(emoji);
            }
        },
    });
}
```

**初始化内容**:
- 邮件存储和UI服务
- 添加按钮的DOM引用
- 表情选择器配置和回调

## 核心功能

### 1. 表情选择处理

```javascript
onSelect: (emoji) => {
    const reaction = this.props.message.reactions.find(
        ({ content, personas }) =>
            content === emoji && personas.find((persona) => persona.eq(this.store.self))
    );
    if (!reaction) {
        this.props.message.react(emoji);
    }
}
```

**选择逻辑**:
1. 检查当前用户是否已经添加了该表情反应
2. 如果没有添加过，则添加新的反应
3. 如果已经添加过，则不执行操作（避免重复）

### 2. 反应去重检查

```javascript
// 检查用户是否已经添加了特定表情
const hasUserReacted = (emoji, message, user) => {
    return message.reactions.some(reaction => 
        reaction.content === emoji && 
        reaction.personas.some(persona => persona.eq(user))
    );
};
```

## 使用场景

### 1. 消息底部反应区域

```javascript
// 在消息组件中显示反应区域
const MessageWithReactions = ({ message }) => {
    return (
        <div class="message-container">
            <div class="message-content">{message.body}</div>
            <div class="message-footer">
                <MessageReactions 
                    message={message}
                    openReactionMenu={() => showReactionMenu(message)}
                />
            </div>
        </div>
    );
};
```

### 2. 聊天界面反应显示

```javascript
// 聊天界面中的反应显示
const ChatMessageReactions = ({ message }) => {
    const hasReactions = message.reactions.length > 0;
    
    return (
        <div class="chat-message">
            <div class="message-bubble">{message.body}</div>
            {hasReactions && (
                <MessageReactions 
                    message={message}
                    openReactionMenu={() => openMobileReactionMenu(message)}
                />
            )}
        </div>
    );
};
```

### 3. 线程中的反应管理

```javascript
// 线程中的反应管理
const ThreadMessageReactions = ({ message, thread }) => {
    return (
        <div class="thread-message-reactions">
            <MessageReactions 
                message={message}
                openReactionMenu={() => showThreadReactionMenu(message, thread)}
            />
            <div class="reaction-stats">
                {message.reactions.length} 种反应
            </div>
        </div>
    );
};
```

### 4. 反应统计显示

```javascript
// 显示反应统计信息
const ReactionStatistics = ({ message }) => {
    const totalReactions = message.reactions.reduce((sum, r) => sum + r.count, 0);
    const uniqueUsers = new Set(
        message.reactions.flatMap(r => r.personas.map(p => p.id))
    ).size;
    
    return (
        <div class="reaction-statistics">
            <MessageReactions 
                message={message}
                openReactionMenu={() => showDetailedStats(message)}
            />
            <div class="stats-summary">
                {totalReactions} 个反应，{uniqueUsers} 个用户参与
            </div>
        </div>
    );
};
```

## 表情选择器集成

### 1. 表情选择器配置

```javascript
// 表情选择器的详细配置
this.emojiPicker = useEmojiPicker(this.addRef, {
    onSelect: (emoji) => {
        this.handleEmojiSelect(emoji);
    },
    position: 'top',
    categories: ['people', 'nature', 'objects', 'symbols'],
    recentEmojis: true,
    searchEnabled: true
});
```

### 2. 表情选择处理

```javascript
// 处理表情选择
handleEmojiSelect(emoji) {
    // 检查是否已经反应过
    const existingReaction = this.findUserReaction(emoji);
    
    if (existingReaction) {
        // 如果已经反应过，移除反应
        existingReaction.remove();
    } else {
        // 如果没有反应过，添加反应
        this.props.message.react(emoji);
    }
    
    // 关闭表情选择器
    this.emojiPicker.close();
}

findUserReaction(emoji) {
    return this.props.message.reactions.find(reaction => 
        reaction.content === emoji && 
        reaction.personas.some(persona => persona.eq(this.store.self))
    );
}
```

### 3. 表情选择器触发

```javascript
// 触发表情选择器
const triggerEmojiPicker = (event) => {
    event.preventDefault();
    this.emojiPicker.open();
};
```

## 反应列表管理

### 1. 反应列表渲染

```javascript
// 渲染反应列表
const renderReactionList = (message) => {
    return message.reactions.map(reaction => (
        <MessageReactionList 
            key={reaction.content}
            message={message}
            reaction={reaction}
            openReactionMenu={this.props.openReactionMenu}
        />
    ));
};
```

### 2. 反应排序

```javascript
// 按反应数量排序
const sortReactionsByCount = (reactions) => {
    return [...reactions].sort((a, b) => b.count - a.count);
};

// 按添加时间排序
const sortReactionsByTime = (reactions) => {
    return [...reactions].sort((a, b) => a.createdAt - b.createdAt);
};
```

### 3. 反应过滤

```javascript
// 过滤用户的反应
const getUserReactions = (reactions, user) => {
    return reactions.filter(reaction => 
        reaction.personas.some(persona => persona.eq(user))
    );
};

// 过滤热门反应
const getPopularReactions = (reactions, threshold = 3) => {
    return reactions.filter(reaction => reaction.count >= threshold);
};
```

## 交互功能

### 1. 添加反应按钮

```javascript
// 添加反应按钮组件
const AddReactionButton = ({ onAddReaction }) => {
    return (
        <button 
            class="add-reaction-btn"
            onClick={onAddReaction}
            aria-label="添加反应"
        >
            <i class="fa fa-smile-o"></i>
            <span>添加反应</span>
        </button>
    );
};
```

### 2. 快速反应

```javascript
// 快速反应功能
const QuickReactions = ({ message, quickEmojis = ['👍', '❤️', '😂', '😮', '😢', '😡'] }) => {
    return (
        <div class="quick-reactions">
            {quickEmojis.map(emoji => (
                <button 
                    key={emoji}
                    class="quick-reaction-btn"
                    onClick={() => message.react(emoji)}
                >
                    {emoji}
                </button>
            ))}
        </div>
    );
};
```

### 3. 反应悬停预览

```javascript
// 反应悬停预览
const ReactionHoverPreview = ({ reaction }) => {
    const [showPreview, setShowPreview] = useState(false);
    
    return (
        <div 
            class="reaction-item"
            onMouseEnter={() => setShowPreview(true)}
            onMouseLeave={() => setShowPreview(false)}
        >
            <MessageReactionList reaction={reaction} />
            {showPreview && (
                <div class="reaction-preview">
                    <div class="preview-users">
                        {reaction.personas.map(persona => (
                            <span key={persona.id}>{persona.name}</span>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};
```

## 样式和布局

### 1. 基础样式

```css
.message-reactions {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-top: 8px;
    
    .reaction-list {
        display: flex;
        gap: 4px;
    }
    
    .add-reaction-btn {
        display: flex;
        align-items: center;
        padding: 4px 8px;
        border: 1px dashed #ccc;
        border-radius: 16px;
        background: transparent;
        color: #666;
        cursor: pointer;
        font-size: 0.9em;
        
        &:hover {
            border-color: #007bff;
            color: #007bff;
        }
        
        i {
            margin-right: 4px;
        }
    }
}
```

### 2. 响应式设计

```css
@media (max-width: 768px) {
    .message-reactions {
        .add-reaction-btn {
            padding: 8px 12px;
            font-size: 1em;
        }
        
        .reaction-list {
            gap: 6px;
        }
    }
}
```

## 性能优化

### 1. 反应缓存

```javascript
// 缓存反应数据
const reactionCache = new Map();

const getCachedReactions = (messageId) => {
    if (reactionCache.has(messageId)) {
        return reactionCache.get(messageId);
    }
    
    const reactions = calculateReactions(messageId);
    reactionCache.set(messageId, reactions);
    return reactions;
};
```

### 2. 虚拟化

```javascript
// 大量反应时的虚拟化
const VirtualizedReactions = ({ reactions, maxVisible = 10 }) => {
    const [visibleReactions, setVisibleReactions] = useState(
        reactions.slice(0, maxVisible)
    );
    
    return (
        <div class="virtualized-reactions">
            {visibleReactions.map(reaction => (
                <MessageReactionList reaction={reaction} />
            ))}
            {reactions.length > maxVisible && (
                <button onClick={() => showAllReactions()}>
                    +{reactions.length - maxVisible} 更多
                </button>
            )}
        </div>
    );
};
```

## 可访问性

### 1. ARIA 标签

```xml
<!-- 可访问性标记 -->
<div 
    class="message-reactions"
    role="group"
    aria-label="消息反应"
>
    <div class="reaction-list" role="list">
        <!-- 反应列表项 -->
    </div>
    
    <button 
        class="add-reaction-btn"
        aria-label="添加表情反应"
        aria-expanded="false"
    >
        添加反应
    </button>
</div>
```

### 2. 键盘导航

```javascript
// 键盘导航支持
const handleKeydown = (event) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            this.emojiPicker.open();
            break;
        case 'Escape':
            this.emojiPicker.close();
            break;
    }
};
```

## 设计模式

### 1. 容器模式 (Container Pattern)
- 管理多个反应组件
- 提供统一的反应接口

### 2. 组合模式 (Composition Pattern)
- 组合反应列表和表情选择器
- 统一的反应管理

### 3. 观察者模式 (Observer Pattern)
- 监听反应数据变化
- 响应用户交互事件

## 注意事项

1. **性能考虑**: 大量反应时的渲染性能
2. **用户体验**: 提供直观的反应交互
3. **数据一致性**: 确保反应数据的同步
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **自定义表情**: 支持自定义表情符号
2. **反应动画**: 添加反应变化的动画效果
3. **反应分组**: 支持按类型分组显示反应
4. **反应搜索**: 支持搜索特定表情反应
5. **批量操作**: 支持批量管理反应

该组件为消息系统提供了完整的表情反应容器功能，增强了用户的情感表达和社交互动体验。

# Attachment View - 附件查看器

## 概述

`attachment_view.js` 实现了 Odoo 邮件系统中的附件查看器组件，提供了附件的预览、导航和弹出窗口功能。该组件支持多种文件类型的查看，特别是对 PDF 文件的特殊处理，并提供了完整的用户交互体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/attachment_view.js`
- **行数**: 94
- **模块**: `@mail/core/common/attachment_view`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'              // OWL 框架组件和钩子
'@web/core/utils/hooks'  // 服务钩子和事件总线
'@web/libs/pdfjs'        // PDF.js 库支持
```

## 组件定义

### AttachmentView 类

继承自 OWL Component 的附件查看器组件：

```javascript
class AttachmentView extends Component {
    static template = "mail.AttachmentView";
    static components = {};
    static props = ["threadId", "threadModel"];
}
```

#### Props 接口

```typescript
interface Props {
    threadId: number;    // 线程ID
    threadModel: string; // 线程模型名称
}
```

## 组件设置

### setup() 方法

组件初始化和配置：

```javascript
setup() {
    super.setup();
    
    // 服务依赖
    this.store = useState(useService("mail.store"));
    this.uiService = useService("ui");
    this.mailPopoutService = useService("mail.popout");
    
    // DOM 引用
    this.iframeViewerPdfRef = useRef("iframeViewerPdf");
    
    // 组件状态
    this.state = useState({
        thread: undefined,
    });
    
    // 生命周期和事件监听
    useEffect(() => {
        if (this.iframeViewerPdfRef.el) {
            hidePDFJSButtons(this.iframeViewerPdfRef.el);
        }
    });
    
    this.updateFromProps(this.props);
    onWillUpdateProps((props) => this.updateFromProps(props));
    
    useBus(this.uiService.bus, "resize", this.updatePopup);
    onMounted(this.updatePopup);
    onPatched(this.updatePopup);
    onWillUnmount(this.mailPopoutService.reset);
}
```

### 服务依赖

- **`mail.store`**: 邮件数据存储服务
- **`ui`**: UI 服务，监听窗口大小变化
- **`mail.popout`**: 弹出窗口服务

### 状态管理

```javascript
this.state = useState({
    thread: undefined,  // 当前线程对象
});
```

## 核心功能

### 1. 附件导航

#### onClickNext() - 下一个附件
```javascript
onClickNext() {
    const index = this.state.thread.attachmentsInWebClientView.findIndex((attachment) =>
        attachment.eq(this.state.thread.mainAttachment)
    );
    this.state.thread.setMainAttachmentFromIndex(
        index === this.state.thread.attachmentsInWebClientView.length - 1 ? 0 : index + 1
    );
}
```

**功能说明**:
- 查找当前主附件在附件列表中的索引
- 切换到下一个附件，如果是最后一个则回到第一个（循环导航）
- 调用线程的 `setMainAttachmentFromIndex()` 方法更新主附件

#### onClickPrevious() - 上一个附件
```javascript
onClickPrevious() {
    const index = this.state.thread.attachmentsInWebClientView.findIndex((attachment) =>
        attachment.eq(this.state.thread.mainAttachment)
    );
    this.state.thread.setMainAttachmentFromIndex(
        index === 0 ? this.state.thread.attachmentsInWebClientView.length - 1 : index - 1
    );
}
```

**功能说明**:
- 查找当前主附件的索引位置
- 切换到上一个附件，如果是第一个则跳到最后一个（循环导航）

### 2. 属性更新处理

#### updateFromProps() - 更新组件状态
```javascript
updateFromProps(props) {
    this.state.thread = this.store.Thread.insert({
        id: props.threadId,
        model: props.threadModel,
    });
}
```

**功能**:
- 根据新的 props 更新线程对象
- 使用 store 的 insert 方法确保线程对象存在

### 3. 弹出窗口功能

#### popoutAttachment() - 弹出附件查看器
```javascript
popoutAttachment() {
    this.mailPopoutService.popout(this.__owl__.bdom.parentEl).focus();
}
```

**功能**:
- 将附件查看器在新窗口中打开
- 自动聚焦到新窗口

#### updatePopup() - 更新弹出窗口
```javascript
updatePopup() {
    if (this.mailPopoutService.externalWindow) {
        this.mailPopoutService.popout(this.__owl__.bdom.parentEl, false);
    }
}
```

**功能**:
- 检查是否存在外部窗口
- 更新弹出窗口的内容和位置

### 4. PDF 特殊处理

#### PDF.js 集成
```javascript
useEffect(() => {
    if (this.iframeViewerPdfRef.el) {
        hidePDFJSButtons(this.iframeViewerPdfRef.el);
    }
});
```

**功能**:
- 隐藏 PDF.js 查看器的默认按钮
- 提供更简洁的 PDF 查看体验

### 5. 计算属性

#### displayName - 显示名称
```javascript
get displayName() {
    return this.state.thread.mainAttachment.filename;
}
```

**功能**:
- 返回当前主附件的文件名
- 用于在 UI 中显示附件标题

## 生命周期管理

### 事件监听

```javascript
// 监听窗口大小变化
useBus(this.uiService.bus, "resize", this.updatePopup);

// 生命周期钩子
onMounted(this.updatePopup);           // 挂载后更新弹出窗口
onPatched(this.updatePopup);           // 更新后更新弹出窗口
onWillUnmount(this.mailPopoutService.reset); // 卸载前重置弹出服务
```

### Props 变化处理

```javascript
onWillUpdateProps((props) => this.updateFromProps(props));
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 继承 OWL Component 基类
- 使用声明式模板和响应式状态

### 2. 服务注入模式 (Service Injection)
- 通过 `useService` 注入所需服务
- 解耦组件和服务实现

### 3. 观察者模式 (Observer Pattern)
- 监听 UI 服务的 resize 事件
- 响应窗口大小变化

### 4. 状态管理模式
- 使用 `useState` 管理组件状态
- 响应式更新 UI

## 使用场景

### 1. 邮件附件预览
```xml
<AttachmentView threadId="123" threadModel="mail.thread" />
```

### 2. 聊天文件查看
- 在聊天窗口中预览发送的文件
- 支持图片、PDF、文档等多种格式

### 3. 附件画廊模式
- 浏览线程中的所有附件
- 提供前进/后退导航

### 4. 弹出窗口查看
- 在独立窗口中查看大文件
- 不影响主界面的使用

## 特性优势

### 1. 多格式支持
- 支持图片、PDF、文档等多种文件格式
- 特殊优化 PDF 查看体验

### 2. 导航便利
- 循环导航所有附件
- 键盘和鼠标双重支持

### 3. 弹出功能
- 独立窗口查看大文件
- 自动适应窗口大小变化

### 4. 响应式设计
- 自动适应不同屏幕尺寸
- 优化移动端体验

## 注意事项

1. **性能考虑**: 大文件预览可能影响性能，建议添加加载状态
2. **浏览器兼容**: PDF.js 功能依赖现代浏览器支持
3. **内存管理**: 及时清理不再使用的附件资源
4. **安全性**: 确保附件来源可信，防止恶意文件

## 扩展建议

1. **缩放功能**: 为图片和PDF添加缩放控制
2. **全屏模式**: 支持全屏查看附件
3. **下载功能**: 添加附件下载按钮
4. **分享功能**: 支持附件链接分享
5. **批注功能**: 为PDF添加批注和标记功能

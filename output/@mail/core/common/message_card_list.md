# Message Card List - 消息卡片列表

## 概述

`message_card_list.js` 实现了 Odoo 邮件系统中的消息卡片列表组件，用于以卡片形式批量显示消息。该组件支持搜索高亮、懒加载、跳转功能等特性，主要用于搜索结果展示和消息浏览，是邮件系统中重要的消息展示组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_card_list.js`
- **行数**: 77
- **模块**: `@mail/core/common/message_card_list`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'        // 消息组件
'@mail/utils/common/hooks'         // 邮件工具钩子
'@odoo/owl'                        // OWL 框架
'@web/core/l10n/translation'       // 国际化
'@web/core/utils/hooks'            // Web 核心钩子
```

## 组件定义

### MessageCardList 类

```javascript
class MessageCardList extends Component {
    static components = { Message };
    static props = [
        "emptyText?",
        "messages", 
        "messageSearch?",
        "loadMore?",
        "mode",
        "onClickJump?",
        "onLoadMoreVisible?",
        "showEmpty?",
        "thread",
    ];
    static template = "mail.MessageCardList";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    emptyText?: string;                                    // 空状态文本（可选）
    messages: import("@mail/core/common/message_model").Message[];  // 消息列表
    messageSearch?: ReturnType<useMessageSearch>;          // 消息搜索钩子（可选）
    loadMore?: function;                                   // 加载更多回调（可选）
    mode: string;                                          // 显示模式
    onClickJump?: function;                                // 跳转点击回调（可选）
    onLoadMoreVisible?: function;                          // 加载更多可见回调（可选）
    showEmpty?: boolean;                                   // 是否显示空状态（可选）
    thread: import("@mail/core/common/thread_model").Thread;  // 关联线程
}
```

### Props 详细说明

- **`messages`** (必需):
  - 类型: Message 模型数组
  - 用途: 要显示的消息列表
  - 功能: 提供卡片列表的数据源

- **`mode`** (必需):
  - 类型: 字符串
  - 用途: 控制消息的显示模式
  - 可能值: `"card"`, `"compact"`, `"search"` 等

- **`thread`** (必需):
  - 类型: Thread 模型
  - 用途: 消息所属的线程
  - 功能: 提供上下文和权限信息

- **`messageSearch`** (可选):
  - 类型: 消息搜索钩子返回值
  - 用途: 提供搜索高亮功能
  - 功能: 支持搜索结果的高亮显示

- **`onClickJump`** (可选):
  - 类型: 函数
  - 用途: 点击跳转到消息的回调
  - 参数: 目标消息对象

- **`onLoadMoreVisible`** (可选):
  - 类型: 函数
  - 用途: 加载更多区域可见时的回调
  - 功能: 实现无限滚动加载

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // UI 服务状态
    this.ui = useState(useService("ui"));
    
    // 子环境设置
    useSubEnv({ messageCard: true });
    
    // 可见性监听
    useVisible("load-more", (isVisible) => {
        if (isVisible) {
            this.props.onLoadMoreVisible?.();
        }
    });
}
```

**初始化内容**:
- UI 服务的响应式状态
- 消息卡片模式的子环境
- 加载更多区域的可见性监听

## 核心功能

### 1. 跳转到消息

```javascript
async onClickJump(message) {
    // 触发外部跳转回调
    this.props.onClickJump?.();
    
    // 小屏幕或聊天窗口中关闭菜单
    if (this.ui.isSmall || this.env.inChatWindow) {
        this.env.pinMenu?.close();
        this.env.searchMenu?.close();
    }
    
    // 等待菜单关闭动画完成
    await new Promise((resolve) => 
        setTimeout(() => requestAnimationFrame(resolve))
    );
    
    // 高亮并滚动到目标消息
    await this.env.messageHighlight?.highlightMessage(message, this.props.thread);
}
```

**跳转流程**:
1. 执行外部跳转回调
2. 在小屏幕模式下关闭相关菜单
3. 等待UI动画完成
4. 高亮目标消息并滚动到位置

### 2. 空状态文本

```javascript
get emptyText() {
    return this.props.emptyText ?? _t("No messages found");
}
```

**功能**:
- 提供默认的空状态文本
- 支持自定义空状态消息
- 国际化支持

## 使用场景

### 1. 搜索结果展示

```javascript
// 在搜索面板中显示搜索结果
<MessageCardList 
    messages={searchResults}
    messageSearch={messageSearchHook}
    mode="search"
    thread={currentThread}
    onClickJump={(message) => jumpToMessage(message)}
    emptyText="未找到匹配的消息"
/>
```

### 2. 线程消息浏览

```javascript
// 在线程中以卡片形式浏览消息
<MessageCardList 
    messages={thread.messages}
    mode="card"
    thread={thread}
    onLoadMoreVisible={() => loadMoreMessages()}
    showEmpty={true}
/>
```

### 3. 收藏消息列表

```javascript
// 显示用户收藏的消息
<MessageCardList 
    messages={starredMessages}
    mode="compact"
    thread={null}
    emptyText="暂无收藏的消息"
    onClickJump={(message) => openMessageThread(message)}
/>
```

### 4. 通知消息列表

```javascript
// 显示通知消息
<MessageCardList 
    messages={notifications}
    mode="notification"
    thread={notificationThread}
    showEmpty={false}
/>
```

## 消息显示模式

### 1. 卡片模式

```javascript
// 完整的卡片显示模式
const cardMode = {
    mode: "card",
    features: [
        "完整消息内容",
        "作者信息",
        "时间戳",
        "附件预览",
        "反应按钮"
    ]
};
```

### 2. 紧凑模式

```javascript
// 紧凑的列表显示模式
const compactMode = {
    mode: "compact",
    features: [
        "简化消息内容",
        "作者头像",
        "时间戳",
        "基本操作"
    ]
};
```

### 3. 搜索模式

```javascript
// 搜索结果显示模式
const searchMode = {
    mode: "search",
    features: [
        "搜索高亮",
        "上下文片段",
        "跳转按钮",
        "匹配统计"
    ]
};
```

## 懒加载功能

### 1. 可见性检测

```javascript
// 使用 useVisible 钩子检测加载更多区域
useVisible("load-more", (isVisible) => {
    if (isVisible) {
        this.props.onLoadMoreVisible?.();
    }
});
```

### 2. 无限滚动实现

```javascript
// 实现无限滚动加载
const InfiniteScrollMessageList = ({ thread, pageSize = 20 }) => {
    const [messages, setMessages] = useState([]);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    
    const loadMoreMessages = async () => {
        if (loading || !hasMore) return;
        
        setLoading(true);
        try {
            const newMessages = await fetchMessages(thread, messages.length, pageSize);
            setMessages(prev => [...prev, ...newMessages]);
            setHasMore(newMessages.length === pageSize);
        } catch (error) {
            console.error('加载消息失败:', error);
        } finally {
            setLoading(false);
        }
    };
    
    return (
        <MessageCardList 
            messages={messages}
            mode="card"
            thread={thread}
            onLoadMoreVisible={loadMoreMessages}
            showEmpty={!loading && messages.length === 0}
        />
    );
};
```

### 3. 分页加载

```javascript
// 分页加载消息
const PaginatedMessageList = ({ thread, pageSize = 50 }) => {
    const [currentPage, setCurrentPage] = useState(1);
    const [allMessages, setAllMessages] = useState([]);
    
    const loadPage = async (page) => {
        const messages = await fetchMessagesPage(thread, page, pageSize);
        setAllMessages(prev => [...prev, ...messages]);
    };
    
    const handleLoadMore = () => {
        setCurrentPage(prev => prev + 1);
        loadPage(currentPage + 1);
    };
    
    return (
        <MessageCardList 
            messages={allMessages}
            mode="card"
            thread={thread}
            onLoadMoreVisible={handleLoadMore}
        />
    );
};
```

## 搜索高亮

### 1. 搜索结果高亮

```javascript
// 在搜索模式下高亮关键词
const SearchResultList = ({ searchTerm, searchResults, thread }) => {
    const messageSearch = useMessageSearch(thread);
    messageSearch.searchTerm = searchTerm;
    
    return (
        <MessageCardList 
            messages={searchResults}
            messageSearch={messageSearch}
            mode="search"
            thread={thread}
            emptyText={`未找到包含 "${searchTerm}" 的消息`}
        />
    );
};
```

### 2. 高亮样式

```css
.message-card-list .message.search-mode .highlight {
    background-color: #ffeb3b;
    padding: 2px 4px;
    border-radius: 2px;
    font-weight: bold;
}

.message-card-list .message.search-mode .context {
    color: #666;
    font-style: italic;
}
```

## 响应式设计

### 1. 移动端适配

```javascript
// 移动端优化的消息列表
const MobileMessageCardList = ({ messages, thread }) => {
    const ui = useService("ui");
    
    return (
        <MessageCardList 
            messages={messages}
            mode={ui.isSmall ? "compact" : "card"}
            thread={thread}
            onClickJump={(message) => {
                if (ui.isSmall) {
                    openMessageModal(message);
                } else {
                    jumpToMessage(message);
                }
            }}
        />
    );
};
```

### 2. 自适应布局

```css
.message-card-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

@media (max-width: 768px) {
    .message-card-list {
        gap: 4px;
    }
    
    .message-card-list .message {
        padding: 8px;
        font-size: 14px;
    }
}

@media (min-width: 1200px) {
    .message-card-list {
        gap: 12px;
    }
}
```

## 性能优化

### 1. 虚拟化滚动

```javascript
// 大量消息时使用虚拟化
const VirtualizedMessageCardList = ({ messages, thread }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 50 });
    const visibleMessages = messages.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-container">
            <MessageCardList 
                messages={visibleMessages}
                mode="card"
                thread={thread}
            />
        </div>
    );
};
```

### 2. 消息缓存

```javascript
// 缓存已渲染的消息
const CachedMessageCardList = ({ messages, thread }) => {
    const messageCache = useRef(new Map());
    
    const getCachedMessage = (message) => {
        if (messageCache.current.has(message.id)) {
            return messageCache.current.get(message.id);
        }
        
        const rendered = renderMessage(message);
        messageCache.current.set(message.id, rendered);
        return rendered;
    };
    
    return (
        <MessageCardList 
            messages={messages.map(getCachedMessage)}
            mode="card"
            thread={thread}
        />
    );
};
```

## 可访问性

### 1. 键盘导航

```javascript
// 支持键盘导航
const handleKeyNavigation = (event, messageIndex, totalMessages) => {
    switch (event.key) {
        case 'ArrowDown':
            focusMessage(Math.min(messageIndex + 1, totalMessages - 1));
            break;
        case 'ArrowUp':
            focusMessage(Math.max(messageIndex - 1, 0));
            break;
        case 'Enter':
            openMessage(messageIndex);
            break;
    }
};
```

### 2. ARIA 标签

```xml
<!-- 可访问性标记 -->
<div 
    class="message-card-list"
    role="list"
    aria-label="消息列表"
    aria-live="polite"
>
    <t t-foreach="props.messages" t-as="message">
        <div role="listitem" tabindex="0">
            <Message 
                message="message"
                mode="props.mode"
                thread="props.thread"
            />
        </div>
    </t>
</div>
```

## 设计模式

### 1. 容器模式 (Container Pattern)
- 作为消息组件的容器
- 统一管理消息的显示和交互

### 2. 观察者模式 (Observer Pattern)
- 监听可见性变化
- 响应加载更多事件

### 3. 策略模式 (Strategy Pattern)
- 根据模式采用不同的显示策略
- 卡片 vs 紧凑 vs 搜索模式

## 注意事项

1. **性能考虑**: 大量消息时的渲染性能
2. **内存管理**: 避免内存泄漏和过度缓存
3. **用户体验**: 提供流畅的滚动和加载体验
4. **响应式设计**: 适配不同设备和屏幕尺寸

## 扩展建议

1. **虚拟滚动**: 支持大量消息的虚拟化渲染
2. **自定义模式**: 支持更多自定义显示模式
3. **批量操作**: 支持批量选择和操作消息
4. **过滤排序**: 支持消息的过滤和排序
5. **导出功能**: 支持导出消息列表

该组件为邮件系统提供了灵活而强大的消息列表展示功能，特别适用于搜索结果和消息浏览场景。

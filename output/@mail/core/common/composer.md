# Composer - 消息编辑器

## 概述

`composer.js` 是 Odoo 邮件系统中最核心的组件之一，实现了功能完整的消息编辑器。该组件支持文本编辑、附件上传、表情符号选择、@提及、建议补全、拖拽上传等丰富功能，是用户创建和编辑消息的主要界面。

## 文件信息
- **路径**: `/mail/static/src/core/common/composer.js`
- **行数**: 742
- **模块**: `@mail/core/common/composer`

## 依赖关系

```javascript
// 核心组件依赖
'@mail/core/common/attachment_list'        // 附件列表
'@mail/core/common/attachment_uploader_hook' // 附件上传钩子
'@mail/core/common/picker'                 // 选择器（表情符号等）
'@mail/core/common/message_confirm_dialog' // 消息确认对话框
'@mail/core/common/navigable_list'         // 可导航列表
'@mail/core/common/suggestion_hook'        // 建议钩子

// 工具和服务依赖
'@web/core/dropzone/dropzone_hook'         // 拖拽区域钩子
'@mail/utils/common/format'               // 格式化工具
'@mail/utils/common/hooks'                // 邮件系统钩子
'@web/core/network/rpc'                   // RPC 网络请求
'@odoo/owl'                               // OWL 框架
'@web/core/l10n/translation'              // 国际化
```

## 组件定义

### Composer 类

```javascript
class Composer extends Component {
    static components = {
        AttachmentList,    // 附件列表组件
        Picker,           // 选择器组件
        FileUploader,     // 文件上传器
        NavigableList,    // 可导航列表
    };
    static template = "mail.Composer";
}
```

## Props 配置

### Props 接口

```typescript
interface Props {
    composer: import("models").Composer;                    // 编辑器模型（必需）
    messageToReplyTo?: MessageToReplyTo;                   // 回复的消息
    messageEdition?: MessageEdition;                       // 消息编辑状态
    mode?: 'compact' | 'normal' | 'extended';              // 显示模式
    type?: 'message' | 'note' | false;                     // 消息类型
    placeholder?: string;                                   // 占位符文本
    className?: string;                                     // CSS类名
    onDiscardCallback?: function;                          // 取消回调
    onPostCallback?: function;                             // 发送回调
    autofocus?: number;                                    // 自动聚焦
    dropzoneRef?: Ref;                                     // 拖拽区域引用
    sidebar?: boolean;                                     // 是否显示侧边栏
    showFullComposer?: boolean;                            // 是否显示完整编辑器
    allowUpload?: boolean;                                 // 是否允许上传
}
```

### 默认属性

```javascript
static defaultProps = {
    mode: "normal",           // 默认普通模式
    className: "",            // 默认无额外类名
    sidebar: true,            // 默认显示侧边栏
    showFullComposer: true,   // 默认显示完整编辑器
    allowUpload: true,        // 默认允许上传
};
```

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    
    // 基础配置
    this.isMobileOS = isMobileOS();
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    
    // DOM 引用
    this.ref = useRef("textarea");
    this.fakeTextarea = useRef("fakeTextarea");
    this.emojiButton = useRef("emoji-button");
    this.inputContainerRef = useRef("input-container");
    
    // 功能模块
    this.attachmentUploader = useAttachmentUploader(thread, { composer });
    this.selection = useSelection({ refName: "textarea", model: composer.selection });
    this.suggestion = useSuggestion();
    this.picker = usePicker(this.pickerSettings);
    
    // 防抖保存
    this.saveContentDebounced = useDebounced(this.saveContent, 5000, {
        execBeforeUnmount: true,
    });
}
```

### 2. 文本编辑功能

#### 自动调整高度
```javascript
useEffect(() => {
    if (this.fakeTextarea.el.scrollHeight) {
        this.ref.el.style.height = this.fakeTextarea.el.scrollHeight + "px";
    }
    this.saveContentDebounced();
}, () => [this.props.composer.text, this.ref.el]);
```

#### 光标位置管理
```javascript
useEffect(() => {
    if (!this.props.composer.forceCursorMove) {
        return;
    }
    this.selection.restore();
    this.props.composer.forceCursorMove = false;
}, () => [this.props.composer.forceCursorMove]);
```

### 3. 键盘快捷键

```javascript
onKeydown(ev) {
    const composer = toRaw(this.props.composer);
    switch (ev.key) {
        case "ArrowUp":
            // 编辑上一条消息
            if (this.props.messageEdition && composer.text === "") {
                const messageToEdit = composer.thread.lastEditableMessageOfSelf;
                if (messageToEdit) {
                    this.props.messageEdition.editingMessage = messageToEdit;
                }
            }
            break;
            
        case "Enter":
            // 发送消息
            const shouldPost = this.props.mode === "extended" ? ev.ctrlKey : !ev.shiftKey;
            if (shouldPost) {
                ev.preventDefault();
                if (composer.message) {
                    this.editMessage();
                } else {
                    this.sendMessage();
                }
            }
            break;
            
        case "Escape":
            // 取消编辑
            if (this.props.onDiscardCallback) {
                this.props.onDiscardCallback();
                markEventHandled(ev, "Composer.discard");
            }
            break;
    }
}
```

### 4. 附件处理

#### 拖拽上传
```javascript
onDropFile(ev) {
    if (isDragSourceExternalFile(ev.dataTransfer)) {
        for (const file of ev.dataTransfer.files) {
            this.attachmentUploader.uploadFile(file);
        }
    }
}
```

#### 粘贴上传
```javascript
onPaste(ev) {
    if (!this.allowUpload || !ev.clipboardData?.items) {
        return;
    }
    if (ev.clipboardData.files.length === 0) {
        return;
    }
    ev.preventDefault();
    for (const file of ev.clipboardData.files) {
        this.attachmentUploader.uploadFile(file);
    }
}
```

### 5. 建议系统

#### 建议配置
```javascript
get navigableListProps() {
    const props = {
        anchorRef: this.inputContainerRef.el,
        position: this.env.inChatter ? "bottom-fit" : "top-fit",
        onSelect: (ev, option) => {
            this.suggestion.insert(option);
            markEventHandled(ev, "composer.selectSuggestion");
        },
        isLoading: !!this.suggestion.search.term && this.suggestion.state.isFetching,
        options: [],
    };
    
    // 根据建议类型配置不同的模板和选项
    switch (this.suggestion.state.items.type) {
        case "Partner":
            return { ...props, optionTemplate: "mail.Composer.suggestionPartner" };
        case "Thread":
            return { ...props, optionTemplate: "mail.Composer.suggestionThread" };
        case "ChannelCommand":
            return { ...props, optionTemplate: "mail.Composer.suggestionChannelCommand" };
        case "mail.canned.response":
            return { ...props, optionTemplate: "mail.Composer.suggestionCannedResponse" };
    }
}
```

### 6. 消息发送

#### 发送流程
```javascript
async sendMessage() {
    const composer = toRaw(this.props.composer);
    if (composer.message) {
        this.editMessage();
        return;
    }
    await this.processMessage(async (value) => {
        await this._sendMessage(value, this.postData, this.extraData);
    });
}

async _sendMessage(value, postData, extraData) {
    const thread = toRaw(this.props.composer.thread);
    const postThread = toRaw(this.thread);
    const post = postThread.post.bind(postThread, value, postData, extraData);
    
    if (postThread.model === "discuss.channel") {
        // 乐观更新：立即显示消息
        post();
    } else {
        // 等待服务器响应
        await post();
    }
    
    // 清理状态
    this.suggestion?.clearRawMentions();
    this.suggestion?.clearCannedResponses();
    this.props.messageToReplyTo?.cancel();
}
```

#### 消息数据
```javascript
get postData() {
    const composer = toRaw(this.props.composer);
    return {
        attachments: composer.attachments || [],
        isNote: this.props.type === "note",
        mentionedChannels: composer.mentionedChannels || [],
        mentionedPartners: composer.mentionedPartners || [],
        cannedResponseIds: composer.cannedResponses.map((c) => c.id),
        parentId: this.props.messageToReplyTo?.message?.id,
    };
}
```

### 7. 消息编辑

```javascript
async editMessage() {
    const composer = toRaw(this.props.composer);
    if (composer.text || composer.message.attachment_ids.length > 0) {
        await this.processMessage(async (value) =>
            composer.message.edit(value, composer.attachments, {
                mentionedChannels: composer.mentionedChannels,
                mentionedPartners: composer.mentionedPartners,
            })
        );
    } else {
        // 空消息时询问是否删除
        this.env.services.dialog.add(MessageConfirmDialog, {
            message: composer.message,
            onConfirm: () => this.message.remove(),
            prompt: _t("Are you sure you want to delete this message?"),
        });
    }
    this.suggestion?.clearRawMentions();
}
```

### 8. 表情符号功能

```javascript
addEmoji(str) {
    const composer = toRaw(this.props.composer);
    const text = composer.text;
    const firstPart = text.slice(0, composer.selection.start);
    const secondPart = text.slice(composer.selection.end, text.length);
    composer.text = firstPart + str + secondPart;
    this.selection.moveCursor((firstPart + str).length);
    if (!this.ui.isSmall) {
        composer.autofocus++;
    }
}
```

## 显示模式

### 1. 紧凑模式 (compact)
- 最小化的编辑器界面
- 适用于空间受限的场景

### 2. 普通模式 (normal)
- 标准的编辑器界面
- 包含基本的编辑功能

### 3. 扩展模式 (extended)
- 完整的编辑器界面
- 包含所有高级功能

## 占位符文本

```javascript
get placeholder() {
    if (this.props.placeholder) {
        return this.props.placeholder;
    }
    if (this.thread) {
        if (this.thread.channel_type === "channel") {
            const threadName = this.thread.displayName;
            if (this.thread.parent_channel_id) {
                return _t(`Message "%(subChannelName)s"`, { subChannelName: threadName });
            }
            return _t("Message #%(threadName)s…", { threadName });
        }
        return _t("Message %(thread name)s…", { "thread name": this.thread.displayName });
    }
    return "";
}
```

## 内容持久化

### 自动保存
```javascript
saveContent() {
    const composer = toRaw(this.props.composer);
    const fullComposerContent =
        document
            .querySelector(".o_mail_composer_form_view .note-editable")
            ?.innerText.replace(/(\t|\n)+/g, "\n") ?? composer.text;
    browser.localStorage.setItem(composer.localId, fullComposerContent);
}
```

### 内容恢复
```javascript
restoreContent() {
    const composer = toRaw(this.props.composer);
    const fullComposerContent = browser.localStorage.getItem(composer.localId);
    if (fullComposerContent) {
        composer.text = fullComposerContent;
    }
}
```

## 使用场景

### 1. 聊天消息编辑
```xml
<Composer 
    composer="chatComposer"
    mode="normal"
    type="message"
    placeholder="输入消息..."
    onPostCallback="() => this.onMessageSent()"
/>
```

### 2. 邮件回复
```xml
<Composer 
    composer="replyComposer"
    messageToReplyTo="messageToReply"
    mode="extended"
    type="message"
/>
```

### 3. 笔记编辑
```xml
<Composer 
    composer="noteComposer"
    mode="normal"
    type="note"
    placeholder="添加内部笔记..."
/>
```

## 性能优化

### 1. 防抖保存
- 使用 `useDebounced` 避免频繁保存
- 组件卸载前强制执行保存

### 2. 响应式更新
- 使用 `useState` 管理响应式状态
- 精确控制重新渲染时机

### 3. 事件处理优化
- 使用事件标记避免重复处理
- 合理的事件冒泡控制

## 注意事项

1. **内存管理**: 及时清理事件监听器和定时器
2. **状态同步**: 确保编辑器状态与模型状态一致
3. **用户体验**: 提供适当的加载状态和错误提示
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 完整编辑器功能

### 打开完整编辑器
```javascript
async onClickFullComposer(ev) {
    // 自动创建建议的合作伙伴
    if (this.props.type !== "note") {
        const newPartners = this.thread.suggestedRecipients.filter(
            (recipient) => recipient.checked && !recipient.persona
        );
        if (newPartners.length !== 0) {
            const partners = await rpc("/mail/partner/from_email", {
                emails: recipientEmails,
                additional_values: recipientAdditionalValues,
            });
            // 更新建议收件人的 persona 信息
        }
    }

    // 准备上下文数据
    const context = {
        default_attachment_ids: attachmentIds,
        default_body: await prettifyMessageContent(body, validMentions),
        default_model: this.thread.model,
        default_partner_ids: selectedPartnerIds,
        default_res_ids: [this.thread.id],
        default_subtype_xmlid: this.props.type === "note" ? "mail.mt_note" : "mail.mt_comment",
        mail_post_autofollow: this.thread.hasWriteAccess,
    };

    // 打开表单视图
    const action = {
        name: this.props.type === "note" ? _t("Log note") : _t("Compose Email"),
        type: "ir.actions.act_window",
        res_model: "mail.compose.message",
        view_mode: "form",
        views: [[false, "form"]],
        target: "new",
        context: context,
    };

    await this.env.services.action.doAction(action, options);
}
```

## 状态管理

### 发送按钮状态
```javascript
get isSendButtonDisabled() {
    const attachments = this.props.composer.attachments;
    return (
        !this.state.active ||                                    // 组件未激活
        (!this.props.composer.text && attachments.length === 0) || // 无内容且无附件
        attachments.some(({ uploading }) => Boolean(uploading))     // 有附件正在上传
    );
}
```

### 焦点管理
```javascript
onFocusin() {
    const composer = toRaw(this.props.composer);
    composer.isFocused = true;
    composer.thread?.markAsRead();  // 聚焦时标记为已读
}
```

## 选择器配置

### 表情符号选择器
```javascript
get pickerSettings() {
    return {
        anchor: this.props.mode === "extended" ? undefined : this.mainActionsRef,
        buttons: [this.emojiButton],
        close: () => {
            if (!this.ui.isSmall) {
                this.props.composer.autofocus++;
            }
        },
        pickers: {
            emoji: (emoji) => this.addEmoji(emoji)
        },
        position: this.props.mode === "extended" ? "bottom-start" :
                 this.props.composer.message ? "bottom-start" : "top-end",
        fixed: !this.props.composer.message,
    };
}
```

## 编辑模式文本

### 取消/保存提示
```javascript
get CANCEL_OR_SAVE_EDIT_TEXT() {
    if (this.ui.isSmall) {
        // 移动端显示按钮
        return markup(sprintf(escape(_t("%(icon)s%(text)s")), {
            icon: `<i class='fa fa-times-circle pe-1'></i>`,
            text: "Discard editing"
        }));
    } else {
        // 桌面端显示快捷键提示
        const translation = this.props.mode === "extended" ?
            _t("Escape to cancel, CTRL-Enter to save") :
            _t("Escape to cancel, Enter to save");
        return markup(sprintf(escape(translation), {
            // 格式化参数
        }));
    }
}
```

### 发送按钮文本
```javascript
get SEND_TEXT() {
    if (this.props.composer.message) {
        return _t("Save editing");  // 编辑模式
    }
    return this.props.type === "note" ? _t("Log") : _t("Send");  // 发送模式
}
```

## 高级功能

### 消息处理流程
```javascript
async processMessage(cb) {
    const attachments = this.props.composer.attachments;

    // 检查上传状态
    if (attachments.some(({ uploading }) => uploading)) {
        this.env.services.notification.add(_t("Please wait while the file is uploading."), {
            type: "warning",
        });
        return;
    }

    // 检查内容
    if (this.props.composer.text.trim() || attachments.length > 0 ||
        (this.message && this.message.attachment_ids.length > 0)) {

        if (!this.state.active) return;

        this.state.active = false;
        await cb(this.props.composer.text);

        if (this.props.onPostCallback) {
            this.props.onPostCallback();
        }

        this.clear();
        this.state.active = true;
        this.ref.el.focus();
    }
}
```

### 内容清理
```javascript
clear() {
    this.props.composer.clear();
    browser.localStorage.removeItem(this.props.composer.localId);
}
```

## 事件处理

### 事件信任验证
```javascript
isEventTrusted(ev) {
    // 允许在测试期间进行补丁
    return ev.isTrusted;
}
```

### 选择保持逻辑
```javascript
preserveOnClickAwayPredicate: async (ev) => {
    // 让事件首先由冒泡处理程序处理
    await new Promise(setTimeout);
    return (
        !this.isEventTrusted(ev) ||
        isEventHandled(ev, "sidebar.openThread") ||
        isEventHandled(ev, "emoji.selectEmoji") ||
        isEventHandled(ev, "Composer.onClickAddEmoji") ||
        isEventHandled(ev, "composer.clickOnAddAttachment") ||
        isEventHandled(ev, "composer.selectSuggestion")
    );
}
```

## 通知功能

### 邮箱发送通知
```javascript
notifySendFromMailbox() {
    this.env.services.notification.add(
        _t('Message posted on "%s"', this.thread.displayName),
        { type: "info" }
    );
}
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个功能模块（附件、选择器、建议等）
- 每个模块独立管理自己的状态和逻辑

### 2. 命令模式 (Command Pattern)
- 键盘快捷键映射到具体的命令操作
- 支持撤销和重做操作

### 3. 观察者模式 (Observer Pattern)
- 监听编辑器状态变化
- 自动保存和恢复内容

### 4. 策略模式 (Strategy Pattern)
- 根据不同模式采用不同的显示策略
- 根据消息类型采用不同的处理策略

## 扩展建议

1. **富文本编辑**: 集成更强大的富文本编辑器
2. **实时协作**: 支持多人同时编辑
3. **模板系统**: 支持消息模板和快速回复
4. **语音输入**: 集成语音转文字功能
5. **AI辅助**: 集成AI写作助手

## 测试建议

1. **单元测试**: 测试各个功能模块的独立性
2. **集成测试**: 测试组件间的交互
3. **用户体验测试**: 测试键盘导航和可访问性
4. **性能测试**: 测试大量内容时的性能表现

该组件是邮件系统用户交互的核心，提供了完整的消息创建和编辑体验，支持从简单的文本输入到复杂的富媒体消息编辑的全部功能。

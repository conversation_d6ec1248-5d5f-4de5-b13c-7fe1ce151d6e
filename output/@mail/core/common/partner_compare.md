# Partner Compare - 合作伙伴比较器

## 概述

`partner_compare.js` 实现了 Odoo 邮件系统中的合作伙伴比较和排序功能，用于在用户搜索和选择合作伙伴时提供智能排序。该模块定义了多个比较函数，按照不同的优先级对合作伙伴进行排序，包括活跃状态、内部用户、关注者、姓名匹配、邮箱匹配等维度，为用户提供最相关的搜索结果，是邮件系统中重要的搜索优化工具。

## 文件信息
- **路径**: `/mail/static/src/core/common/partner_compare.js`
- **行数**: 121
- **模块**: `@mail/core/common/partner_compare`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/format'    // 邮件格式化工具
'@web/core/registry'           // 注册表系统
```

## 注册表定义

### partnerCompareRegistry

```javascript
/**
 * 合作伙伴排序函数注册表
 * 函数签名:
 * (partner1: Partner, partner2: Partner, { env: OdooEnv, searchTerm: string, thread?: Thread, context?: Object}) => number|undefined
 */
const partnerCompareRegistry = registry.category("mail.partner_compare");
```

**注册表特性**:
- 支持多个比较函数
- 按序列号排序执行
- 支持上下文参数传递
- 返回标准比较结果（-1, 0, 1）

## 比较函数

### 1. 归档状态比较 (序列号: 5)

```javascript
partnerCompareRegistry.add(
    "mail.archived-last-except-odoobot",
    (p1, p2) => {
        const p1active = p1.active || p1.eq(p1.store.odoobot);
        const p2active = p2.active || p2.eq(p2.store.odoobot);
        
        if (!p1active && p2active) {
            return 1;  // p1 归档，p2 活跃，p2 优先
        }
        if (!p2active && p1active) {
            return -1; // p2 归档，p1 活跃，p1 优先
        }
    },
    { sequence: 5 }
);
```

**排序逻辑**:
- 活跃用户优先于归档用户
- OdooBot 始终视为活跃
- 归档用户排在最后

### 2. 内部用户比较 (序列号: 35)

```javascript
partnerCompareRegistry.add(
    "mail.internal-users",
    (p1, p2) => {
        const isAInternalUser = p1.isInternalUser;
        const isBInternalUser = p2.isInternalUser;
        
        if (isAInternalUser && !isBInternalUser) {
            return -1; // p1 是内部用户，p1 优先
        }
        if (!isAInternalUser && isBInternalUser) {
            return 1;  // p2 是内部用户，p2 优先
        }
    },
    { sequence: 35 }
);
```

**排序逻辑**:
- 内部用户优先于外部用户
- 便于快速找到公司内部人员

### 3. 关注者比较 (序列号: 45)

```javascript
partnerCompareRegistry.add(
    "mail.followers",
    (p1, p2, { thread }) => {
        if (thread) {
            const followerList = [...thread.followers];
            if (thread.selfFollower) {
                followerList.push(thread.selfFollower);
            }
            
            const isFollower1 = followerList.some((follower) => p1.eq(follower.partner));
            const isFollower2 = followerList.some((follower) => p2.eq(follower.partner));
            
            if (isFollower1 && !isFollower2) {
                return -1; // p1 是关注者，p1 优先
            }
            if (!isFollower1 && isFollower2) {
                return 1;  // p2 是关注者，p2 优先
            }
        }
    },
    { sequence: 45 }
);
```

**排序逻辑**:
- 线程关注者优先显示
- 包括自己的关注状态
- 提高相关人员的可见性

### 4. 姓名匹配比较 (序列号: 50)

```javascript
partnerCompareRegistry.add(
    "mail.name",
    (p1, p2, { searchTerm }) => {
        const cleanedName1 = cleanTerm(p1.name);
        const cleanedName2 = cleanTerm(p2.name);
        
        // 前缀匹配优先
        if (cleanedName1.startsWith(searchTerm) && !cleanedName2.startsWith(searchTerm)) {
            return -1;
        }
        if (!cleanedName1.startsWith(searchTerm) && cleanedName2.startsWith(searchTerm)) {
            return 1;
        }
        
        // 字母顺序排序
        if (cleanedName1 < cleanedName2) {
            return -1;
        }
        if (cleanedName1 > cleanedName2) {
            return 1;
        }
    },
    { sequence: 50 }
);
```

**排序逻辑**:
- 搜索词前缀匹配优先
- 其次按字母顺序排序
- 使用清理后的名称进行比较

### 5. 邮箱匹配比较 (序列号: 55)

```javascript
partnerCompareRegistry.add(
    "mail.email",
    (p1, p2, { searchTerm }) => {
        const cleanedEmail1 = cleanTerm(p1.email);
        const cleanedEmail2 = cleanTerm(p2.email);
        
        // 前缀匹配优先
        if (cleanedEmail1.startsWith(searchTerm) && !cleanedEmail2.startsWith(searchTerm)) {
            return -1;
        }
        if (!cleanedEmail1.startsWith(searchTerm) && cleanedEmail2.startsWith(searchTerm)) {
            return 1;
        }
        
        // 字母顺序排序
        if (cleanedEmail1 < cleanedEmail2) {
            return -1;
        }
        if (cleanedEmail1 > cleanedEmail2) {
            return 1;
        }
    },
    { sequence: 55 }
);
```

**排序逻辑**:
- 邮箱前缀匹配优先
- 其次按字母顺序排序
- 支持邮箱地址搜索

### 6. ID 比较 (序列号: 75)

```javascript
partnerCompareRegistry.add(
    "mail.id",
    (p1, p2) => {
        return p1.id - p2.id;
    },
    { sequence: 75 }
);
```

**排序逻辑**:
- 最后的排序依据
- 确保排序结果的一致性
- 按 ID 升序排列

## 使用场景

### 1. 用户搜索排序

```javascript
// 在用户搜索中应用排序
const sortPartners = (partners, searchTerm, thread) => {
    return partners.sort((p1, p2) => {
        const context = { searchTerm, thread };
        
        // 按注册表中的序列号顺序执行比较
        for (const [name, compareFn] of partnerCompareRegistry.getEntries()) {
            const result = compareFn(p1, p2, context);
            if (result !== undefined && result !== 0) {
                return result;
            }
        }
        
        return 0;
    });
};
```

### 2. 提及建议排序

```javascript
// 在 @ 提及建议中排序用户
const getMentionSuggestions = (searchTerm, thread) => {
    const allPartners = getAllPartners();
    const filteredPartners = filterPartnersByTerm(allPartners, searchTerm);
    
    return sortPartners(filteredPartners, searchTerm, thread);
};
```

### 3. 收件人选择排序

```javascript
// 在邮件收件人选择中排序
const getRecipientSuggestions = (searchTerm, context) => {
    const partners = searchPartners(searchTerm);
    
    return partners.sort((p1, p2) => {
        // 应用所有比较函数
        for (const compareFn of getComparisonFunctions()) {
            const result = compareFn(p1, p2, { searchTerm, ...context });
            if (result !== 0) return result;
        }
        return 0;
    });
};
```

### 4. 聊天参与者排序

```javascript
// 在聊天参与者列表中排序
const sortChatParticipants = (participants, currentThread) => {
    return participants.sort((p1, p2) => {
        const context = { thread: currentThread };
        
        // 优先显示活跃用户
        const activeComparison = compareByActiveStatus(p1, p2);
        if (activeComparison !== 0) return activeComparison;
        
        // 其次显示关注者
        const followerComparison = compareByFollowerStatus(p1, p2, context);
        if (followerComparison !== 0) return followerComparison;
        
        // 最后按姓名排序
        return compareByName(p1, p2);
    });
};
```

## 比较函数扩展

### 1. 自定义比较函数

```javascript
// 添加自定义比较函数
partnerCompareRegistry.add(
    "custom.recent-activity",
    (p1, p2, { context }) => {
        const activity1 = getRecentActivity(p1.id);
        const activity2 = getRecentActivity(p2.id);
        
        if (activity1 && !activity2) return -1;
        if (!activity1 && activity2) return 1;
        
        if (activity1 && activity2) {
            return activity2.timestamp - activity1.timestamp;
        }
        
        return 0;
    },
    { sequence: 25 }
);
```

### 2. 在线状态比较

```javascript
// 按在线状态排序
partnerCompareRegistry.add(
    "custom.online-status",
    (p1, p2) => {
        const status1 = getOnlineStatus(p1);
        const status2 = getOnlineStatus(p2);
        
        const statusPriority = {
            'online': 3,
            'away': 2,
            'busy': 1,
            'offline': 0
        };
        
        const priority1 = statusPriority[status1] || 0;
        const priority2 = statusPriority[status2] || 0;
        
        return priority2 - priority1;
    },
    { sequence: 15 }
);
```

### 3. 部门优先级

```javascript
// 按部门优先级排序
partnerCompareRegistry.add(
    "custom.department-priority",
    (p1, p2, { context }) => {
        const dept1 = p1.department;
        const dept2 = p2.department;
        const currentUserDept = context.currentUser?.department;
        
        // 同部门优先
        if (dept1 === currentUserDept && dept2 !== currentUserDept) {
            return -1;
        }
        if (dept1 !== currentUserDept && dept2 === currentUserDept) {
            return 1;
        }
        
        return 0;
    },
    { sequence: 20 }
);
```

## 搜索优化

### 1. 模糊匹配

```javascript
// 模糊匹配比较
const fuzzyMatch = (text, searchTerm) => {
    const cleanText = cleanTerm(text);
    const cleanSearch = cleanTerm(searchTerm);
    
    // 计算匹配分数
    let score = 0;
    let searchIndex = 0;
    
    for (let i = 0; i < cleanText.length && searchIndex < cleanSearch.length; i++) {
        if (cleanText[i] === cleanSearch[searchIndex]) {
            score += 1;
            searchIndex++;
        }
    }
    
    return searchIndex === cleanSearch.length ? score : 0;
};
```

### 2. 权重计算

```javascript
// 基于权重的排序
const calculatePartnerWeight = (partner, searchTerm, context) => {
    let weight = 0;
    
    // 活跃状态权重
    if (partner.active) weight += 100;
    
    // 内部用户权重
    if (partner.isInternalUser) weight += 50;
    
    // 关注者权重
    if (isFollower(partner, context.thread)) weight += 30;
    
    // 姓名匹配权重
    if (partner.name.toLowerCase().startsWith(searchTerm.toLowerCase())) {
        weight += 20;
    }
    
    // 邮箱匹配权重
    if (partner.email?.toLowerCase().startsWith(searchTerm.toLowerCase())) {
        weight += 15;
    }
    
    return weight;
};
```

## 性能优化

### 1. 缓存比较结果

```javascript
// 缓存比较结果
const comparisonCache = new Map();

const getCachedComparison = (p1Id, p2Id, compareFnName, context) => {
    const key = `${p1Id}_${p2Id}_${compareFnName}_${JSON.stringify(context)}`;
    
    if (comparisonCache.has(key)) {
        return comparisonCache.get(key);
    }
    
    const result = executeComparison(p1Id, p2Id, compareFnName, context);
    comparisonCache.set(key, result);
    
    return result;
};
```

### 2. 批量排序优化

```javascript
// 批量排序优化
const optimizedSort = (partners, searchTerm, context) => {
    // 预计算权重
    const partnersWithWeights = partners.map(partner => ({
        partner,
        weight: calculatePartnerWeight(partner, searchTerm, context)
    }));
    
    // 按权重排序
    partnersWithWeights.sort((a, b) => b.weight - a.weight);
    
    return partnersWithWeights.map(item => item.partner);
};
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 可扩展的比较函数注册
- 支持插件式添加新的排序逻辑

### 2. 策略模式 (Strategy Pattern)
- 不同的排序策略
- 可配置的排序优先级

### 3. 责任链模式 (Chain of Responsibility Pattern)
- 按序列号执行比较函数
- 第一个非零结果决定排序

## 注意事项

1. **性能考虑**: 避免在大量数据上频繁排序
2. **一致性**: 确保比较函数的传递性
3. **可扩展性**: 支持添加新的比较维度
4. **用户体验**: 提供直观的排序结果

## 扩展建议

1. **动态权重**: 支持用户自定义排序权重
2. **学习算法**: 基于用户行为学习排序偏好
3. **上下文感知**: 根据不同场景调整排序策略
4. **性能监控**: 监控排序性能并优化
5. **A/B测试**: 支持不同排序算法的测试

该模块为邮件系统提供了智能的合作伙伴排序功能，显著提升了用户搜索和选择的效率。

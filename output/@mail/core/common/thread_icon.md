# Thread Icon - 线程图标组件

## 概述

`thread_icon.js` 实现了 Odoo 邮件系统中的线程图标组件，用于显示不同类型线程的图标。该组件根据线程类型、对话者状态等信息动态显示相应的图标，支持多种尺寸和自定义样式，为用户提供直观的线程类型识别，是邮件系统中重要的视觉标识组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/thread_icon.js`
- **行数**: 51
- **模块**: `@mail/core/common/thread_icon`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'              // Web 核心钩子
'@odoo/owl'                         // OWL 框架
'@mail/core/common/thread_model'    // 线程模型
'@web/core/l10n/translation'        // 国际化
```

## 组件定义

### ThreadIcon 类

```javascript
class ThreadIcon extends Component {
    static template = "mail.ThreadIcon";
    static props = {
        thread: { type: Thread },
        size: { optional: true, validate: (size) => ["small", "medium", "large"].includes(size) },
        className: { type: String, optional: true },
    };
    static defaultProps = {
        size: "medium",
        className: "",
    };
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    thread: import("models").Thread;    // 线程对象
    size?: "small" | "medium" | "large"; // 图标尺寸（可选）
    className?: string;                  // 自定义CSS类名（可选）
}
```

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型
  - 用途: 要显示图标的线程对象
  - 功能: 提供图标显示的数据源

- **`size`** (可选):
  - 类型: "small" | "medium" | "large"
  - 默认值: "medium"
  - 用途: 控制图标的显示尺寸
  - 验证: 只接受预定义的尺寸值

- **`className`** (可选):
  - 类型: 字符串
  - 默认值: ""
  - 用途: 添加自定义CSS类名
  - 功能: 支持样式定制

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 用于访问全局数据和配置

## 核心功能

### 1. 对话者获取

```javascript
get correspondent() {
    return this.props.thread.correspondent;
}
```

**功能**:
- 获取线程的对话者信息
- 用于私聊线程的图标显示
- 提供用户头像和状态信息

### 2. 默认聊天图标

```javascript
get defaultChatIcon() {
    return {
        class: "fa fa-question-circle",
        title: _t("No IM status available"),
    };
}
```

**默认图标**:
- 使用问号圆圈图标
- 显示"无IM状态可用"的提示
- 作为状态不明时的回退图标

## 使用场景

### 1. 线程列表图标

```javascript
// 在线程列表中显示图标
const ThreadListItem = ({ thread }) => {
    return (
        <div class="thread-list-item">
            <ThreadIcon 
                thread={thread}
                size="medium"
                className="thread-icon"
            />
            <div class="thread-info">
                <div class="thread-name">{thread.displayName}</div>
                <div class="thread-preview">{thread.lastMessage?.preview}</div>
            </div>
        </div>
    );
};
```

### 2. 聊天窗口标题图标

```javascript
// 在聊天窗口标题中显示图标
const ChatWindowHeader = ({ thread }) => {
    return (
        <div class="chat-window-header">
            <ThreadIcon 
                thread={thread}
                size="small"
                className="header-icon"
            />
            <span class="thread-title">{thread.displayName}</span>
        </div>
    );
};
```

### 3. 通知中的线程图标

```javascript
// 在通知中显示线程图标
const NotificationWithIcon = ({ notification }) => {
    const thread = notification.thread;
    
    return (
        <div class="notification-item">
            <ThreadIcon 
                thread={thread}
                size="large"
                className="notification-icon"
            />
            <div class="notification-content">
                <div class="notification-title">{notification.title}</div>
                <div class="notification-message">{notification.message}</div>
            </div>
        </div>
    );
};
```

### 4. 搜索结果图标

```javascript
// 在搜索结果中显示线程图标
const SearchResultItem = ({ thread, searchTerm }) => {
    return (
        <div class="search-result-item">
            <ThreadIcon 
                thread={thread}
                size="medium"
                className="search-icon"
            />
            <div class="result-content">
                <div class="result-title">{highlightSearchTerm(thread.displayName, searchTerm)}</div>
                <div class="result-type">{getThreadTypeLabel(thread)}</div>
            </div>
        </div>
    );
};
```

## 图标类型

### 1. 私聊图标

```javascript
// 私聊线程图标
const getPrivateChatIcon = (thread) => {
    const correspondent = thread.correspondent;
    
    if (correspondent) {
        return {
            type: 'avatar',
            src: correspondent.avatarUrl,
            alt: correspondent.name,
            status: correspondent.im_status
        };
    }
    
    return {
        type: 'icon',
        class: 'fa fa-user',
        title: '私聊'
    };
};
```

### 2. 群聊图标

```javascript
// 群聊线程图标
const getGroupChatIcon = (thread) => {
    return {
        type: 'icon',
        class: 'fa fa-users',
        title: `群聊 (${thread.memberCount} 人)`,
        color: getGroupColor(thread)
    };
};
```

### 3. 频道图标

```javascript
// 频道线程图标
const getChannelIcon = (thread) => {
    if (thread.channel_type === 'channel') {
        return {
            type: 'icon',
            class: 'fa fa-hashtag',
            title: `频道: ${thread.displayName}`,
            color: '#007bff'
        };
    }
    
    return {
        type: 'icon',
        class: 'fa fa-comments',
        title: '讨论',
        color: '#28a745'
    };
};
```

### 4. 邮件线程图标

```javascript
// 邮件线程图标
const getMailThreadIcon = (thread) => {
    return {
        type: 'icon',
        class: 'fa fa-envelope',
        title: `邮件: ${thread.subject}`,
        color: '#ffc107'
    };
};
```

## 状态指示

### 1. 在线状态

```javascript
// 在线状态指示
const getOnlineStatusIcon = (imStatus) => {
    const statusIcons = {
        'online': {
            class: 'fa fa-circle',
            color: '#28a745',
            title: '在线'
        },
        'away': {
            class: 'fa fa-circle',
            color: '#ffc107',
            title: '离开'
        },
        'busy': {
            class: 'fa fa-circle',
            color: '#dc3545',
            title: '忙碌'
        },
        'offline': {
            class: 'fa fa-circle-o',
            color: '#6c757d',
            title: '离线'
        }
    };
    
    return statusIcons[imStatus] || statusIcons['offline'];
};
```

### 2. 未读消息指示

```javascript
// 未读消息指示
const getUnreadIndicator = (thread) => {
    if (thread.message_unread_counter > 0) {
        return {
            type: 'badge',
            count: thread.message_unread_counter,
            class: 'unread-badge',
            title: `${thread.message_unread_counter} 条未读消息`
        };
    }
    
    return null;
};
```

### 3. 优先级指示

```javascript
// 优先级指示
const getPriorityIndicator = (thread) => {
    if (thread.priority === 'high') {
        return {
            type: 'icon',
            class: 'fa fa-exclamation-triangle',
            color: '#dc3545',
            title: '高优先级'
        };
    }
    
    return null;
};
```

## 尺寸配置

### 1. 尺寸样式

```css
.thread-icon {
    &.size-small {
        width: 16px;
        height: 16px;
        font-size: 12px;
    }
    
    &.size-medium {
        width: 24px;
        height: 24px;
        font-size: 16px;
    }
    
    &.size-large {
        width: 32px;
        height: 32px;
        font-size: 20px;
    }
}

.thread-avatar {
    border-radius: 50%;
    
    &.size-small {
        width: 16px;
        height: 16px;
    }
    
    &.size-medium {
        width: 24px;
        height: 24px;
    }
    
    &.size-large {
        width: 32px;
        height: 32px;
    }
}
```

### 2. 响应式尺寸

```javascript
// 响应式尺寸调整
const getResponsiveSize = (baseSize, screenSize) => {
    const sizeMap = {
        'small': {
            'mobile': 'small',
            'tablet': 'small',
            'desktop': 'small'
        },
        'medium': {
            'mobile': 'small',
            'tablet': 'medium',
            'desktop': 'medium'
        },
        'large': {
            'mobile': 'medium',
            'tablet': 'large',
            'desktop': 'large'
        }
    };
    
    return sizeMap[baseSize][screenSize] || baseSize;
};
```

## 样式和主题

### 1. 基础样式

```css
.thread-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: relative;
    
    .icon {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .status-indicator {
        position: absolute;
        bottom: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 1px solid white;
    }
    
    .unread-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: #dc3545;
        color: white;
        border-radius: 10px;
        padding: 2px 6px;
        font-size: 10px;
        font-weight: bold;
        min-width: 16px;
        text-align: center;
    }
}
```

### 2. 主题适配

```css
/* 深色主题 */
.dark-theme .thread-icon {
    .icon {
        color: #e9ecef;
    }
    
    .status-indicator {
        border-color: #343a40;
    }
    
    .unread-badge {
        background: #e74c3c;
    }
}

/* 高对比度主题 */
.high-contrast .thread-icon {
    .icon {
        color: #000;
        background: #fff;
        border: 2px solid #000;
    }
    
    .status-indicator {
        border-width: 2px;
    }
}
```

## 可访问性

### 1. ARIA 标签

```xml
<!-- 可访问性标记 -->
<div 
    class="thread-icon"
    role="img"
    aria-label="线程图标：{thread.displayName}"
>
    <i 
        class="{iconClass}"
        aria-hidden="true"
        title="{iconTitle}"
    ></i>
    
    {#if hasUnreadMessages}
        <span 
            class="unread-badge"
            aria-label="{unreadCount} 条未读消息"
        >
            {unreadCount}
        </span>
    {/if}
</div>
```

### 2. 屏幕阅读器支持

```javascript
// 生成屏幕阅读器描述
const generateAriaLabel = (thread) => {
    let label = `${getThreadTypeLabel(thread)}: ${thread.displayName}`;
    
    if (thread.message_unread_counter > 0) {
        label += `, ${thread.message_unread_counter} 条未读消息`;
    }
    
    if (thread.correspondent?.im_status) {
        label += `, 状态: ${getStatusLabel(thread.correspondent.im_status)}`;
    }
    
    return label;
};
```

## 性能优化

### 1. 图标缓存

```javascript
// 图标缓存
const iconCache = new Map();

const getCachedIcon = (thread, size) => {
    const cacheKey = `${thread.id}_${size}_${thread.lastUpdateTime}`;
    
    if (iconCache.has(cacheKey)) {
        return iconCache.get(cacheKey);
    }
    
    const icon = generateIcon(thread, size);
    iconCache.set(cacheKey, icon);
    
    return icon;
};
```

### 2. 懒加载

```javascript
// 头像懒加载
const LazyAvatar = ({ src, alt, size }) => {
    const [loaded, setLoaded] = useState(false);
    const [error, setError] = useState(false);
    
    return (
        <div class={`avatar-container size-${size}`}>
            {!loaded && !error && (
                <div class="avatar-placeholder">
                    <i class="fa fa-user"></i>
                </div>
            )}
            
            <img 
                src={src}
                alt={alt}
                onLoad={() => setLoaded(true)}
                onError={() => setError(true)}
                style={{ display: loaded ? 'block' : 'none' }}
            />
            
            {error && (
                <div class="avatar-error">
                    <i class="fa fa-user"></i>
                </div>
            )}
        </div>
    );
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的图标组件
- 统一的图标显示接口

### 2. 策略模式 (Strategy Pattern)
- 根据线程类型采用不同的图标策略
- 可配置的图标显示规则

### 3. 工厂模式 (Factory Pattern)
- 根据线程类型创建相应的图标
- 统一的图标创建接口

## 注意事项

1. **性能考虑**: 避免频繁重新计算图标
2. **用户体验**: 提供清晰的视觉标识
3. **可访问性**: 支持屏幕阅读器和键盘导航
4. **一致性**: 保持图标风格的统一

## 扩展建议

1. **自定义图标**: 支持用户自定义线程图标
2. **动画效果**: 添加状态变化的动画
3. **图标主题**: 支持多种图标主题
4. **批量更新**: 优化大量图标的更新性能
5. **图标预览**: 提供图标选择和预览功能

该组件为邮件系统提供了直观的线程类型识别功能，增强了用户界面的可用性和美观性。

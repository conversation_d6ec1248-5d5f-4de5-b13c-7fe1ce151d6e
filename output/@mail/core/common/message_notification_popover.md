# Message Notification Popover - 消息通知弹出框

## 概述

`message_notification_popover.js` 实现了 Odoo 邮件系统中的消息通知弹出框组件，用于显示消息的通知状态详细信息。该组件以弹出框的形式展示消息的发送状态、接收者信息、失败原因等，为用户提供详细的消息传递状态反馈，是邮件系统中重要的状态查看组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_notification_popover.js`
- **行数**: 16
- **模块**: `@mail/core/common/message_notification_popover`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'    // OWL 框架
```

## 组件定义

### MessageNotificationPopover 类

```javascript
class MessageNotificationPopover extends Component {
    static template = "mail.MessageNotificationPopover";
    static props = ["message", "close?"];
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    message: import("@mail/core/common/message_model").Message;  // 要显示通知信息的消息
    close?: function;                                            // 关闭弹出框回调（可选）
}
```

### Props 详细说明

- **`message`** (必需):
  - 类型: Message 模型
  - 用途: 要显示通知状态的消息对象
  - 功能: 提供通知信息的数据源

- **`close`** (可选):
  - 类型: 函数
  - 用途: 关闭弹出框的回调函数
  - 触发: 点击外部区域或ESC键时

## 使用场景

### 1. 消息发送状态查看

```javascript
// 点击消息状态图标显示详细信息
const showNotificationDetails = (message, event) => {
    const popover = new MessageNotificationPopover({
        message: message,
        close: () => hidePopover()
    });
    
    showPopover(popover, {
        target: event.target,
        placement: 'top'
    });
};
```

### 2. 发送失败详情

```javascript
// 显示发送失败的详细信息
const showFailureDetails = (message) => {
    if (message.notifications.some(n => n.isFailure)) {
        const popover = new MessageNotificationPopover({
            message: message,
            close: () => hideNotificationPopover()
        });
        
        showPopover(popover, {
            title: '发送失败详情',
            type: 'error'
        });
    }
};
```

### 3. 批量通知状态

```javascript
// 显示批量发送的通知状态
const showBatchNotificationStatus = (message) => {
    const popover = new MessageNotificationPopover({
        message: message,
        close: () => closeBatchStatus()
    });
    
    showPopover(popover, {
        title: `发送状态 (${message.notifications.length} 个接收者)`,
        width: 400
    });
};
```

### 4. 实时状态更新

```javascript
// 实时更新通知状态
const showLiveNotificationStatus = (message) => {
    const popover = new MessageNotificationPopover({
        message: message,
        close: () => stopLiveUpdates()
    });
    
    // 定期更新状态
    const updateInterval = setInterval(() => {
        refreshNotificationStatus(message);
    }, 5000);
    
    showPopover(popover, {
        onClose: () => clearInterval(updateInterval)
    });
};
```

## 通知状态显示

### 1. 状态分类

```javascript
// 按状态分类通知
const categorizeNotifications = (notifications) => {
    return {
        sent: notifications.filter(n => n.notification_status === 'sent'),
        pending: notifications.filter(n => n.notification_status === 'pending'),
        failed: notifications.filter(n => n.isFailure),
        bounced: notifications.filter(n => n.notification_status === 'bounce'),
        exception: notifications.filter(n => n.notification_status === 'exception')
    };
};
```

### 2. 状态统计

```javascript
// 生成通知状态统计
const generateNotificationStats = (message) => {
    const notifications = message.notifications;
    const total = notifications.length;
    
    return {
        total: total,
        sent: notifications.filter(n => n.notification_status === 'sent').length,
        pending: notifications.filter(n => n.notification_status === 'pending').length,
        failed: notifications.filter(n => n.isFailure).length,
        successRate: total > 0 ? (notifications.filter(n => n.notification_status === 'sent').length / total * 100).toFixed(1) : 0
    };
};
```

### 3. 接收者信息

```javascript
// 显示接收者详细信息
const renderRecipientInfo = (notification) => {
    return `
        <div class="recipient-item">
            <img src="${notification.persona.avatarUrl}" alt="${notification.persona.name}" />
            <div class="recipient-details">
                <div class="recipient-name">${notification.persona.name}</div>
                <div class="recipient-email">${notification.persona.email || ''}</div>
                <div class="notification-status ${notification.notification_status}">
                    <i class="${notification.statusIcon}"></i>
                    ${notification.statusTitle}
                </div>
            </div>
        </div>
    `;
};
```

## 弹出框内容

### 1. 基础信息显示

```javascript
// 弹出框基础内容
const renderPopoverContent = (message) => {
    const stats = generateNotificationStats(message);
    
    return `
        <div class="notification-popover-content">
            <div class="popover-header">
                <h4>消息发送状态</h4>
                <div class="stats-summary">
                    ${stats.total} 个接收者，${stats.sent} 个已送达
                </div>
            </div>
            <div class="popover-body">
                ${renderNotificationList(message.notifications)}
            </div>
        </div>
    `;
};
```

### 2. 详细状态列表

```javascript
// 渲染通知状态列表
const renderNotificationList = (notifications) => {
    const categorized = categorizeNotifications(notifications);
    
    return `
        <div class="notification-list">
            ${categorized.sent.length > 0 ? `
                <div class="status-group">
                    <h5><i class="fa fa-check text-success"></i> 已送达 (${categorized.sent.length})</h5>
                    ${categorized.sent.map(renderRecipientInfo).join('')}
                </div>
            ` : ''}
            
            ${categorized.pending.length > 0 ? `
                <div class="status-group">
                    <h5><i class="fa fa-clock text-warning"></i> 发送中 (${categorized.pending.length})</h5>
                    ${categorized.pending.map(renderRecipientInfo).join('')}
                </div>
            ` : ''}
            
            ${categorized.failed.length > 0 ? `
                <div class="status-group">
                    <h5><i class="fa fa-exclamation text-danger"></i> 发送失败 (${categorized.failed.length})</h5>
                    ${categorized.failed.map(renderRecipientInfo).join('')}
                </div>
            ` : ''}
        </div>
    `;
};
```

### 3. 操作按钮

```javascript
// 弹出框操作按钮
const renderPopoverActions = (message) => {
    const hasFailures = message.notifications.some(n => n.isFailure);
    
    return `
        <div class="popover-actions">
            ${hasFailures ? `
                <button class="btn btn-sm btn-warning" onclick="retryFailedNotifications(${message.id})">
                    <i class="fa fa-refresh"></i> 重试失败
                </button>
            ` : ''}
            <button class="btn btn-sm btn-secondary" onclick="refreshNotificationStatus(${message.id})">
                <i class="fa fa-sync"></i> 刷新状态
            </button>
            <button class="btn btn-sm btn-info" onclick="exportNotificationReport(${message.id})">
                <i class="fa fa-download"></i> 导出报告
            </button>
        </div>
    `;
};
```

## 弹出框定位

### 1. 智能定位

```javascript
// 智能定位弹出框
const positionPopover = (popover, target) => {
    const targetRect = target.getBoundingClientRect();
    const popoverRect = popover.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let left = targetRect.left + targetRect.width / 2 - popoverRect.width / 2;
    let top = targetRect.top - popoverRect.height - 10;
    
    // 水平边界检查
    if (left < 10) {
        left = 10;
    } else if (left + popoverRect.width > viewportWidth - 10) {
        left = viewportWidth - popoverRect.width - 10;
    }
    
    // 垂直边界检查
    if (top < 10) {
        top = targetRect.bottom + 10; // 显示在下方
        popover.classList.add('popover-bottom');
    } else {
        popover.classList.add('popover-top');
    }
    
    popover.style.left = left + 'px';
    popover.style.top = top + 'px';
};
```

### 2. 响应式定位

```javascript
// 响应式弹出框定位
const responsivePositioning = (popover, target) => {
    const isMobile = window.innerWidth < 768;
    
    if (isMobile) {
        // 移动端全屏显示
        popover.classList.add('popover-fullscreen');
        popover.style.left = '0';
        popover.style.top = '0';
        popover.style.width = '100%';
        popover.style.height = '100%';
    } else {
        // 桌面端智能定位
        positionPopover(popover, target);
    }
};
```

## 交互功能

### 1. 自动关闭

```javascript
// 自动关闭机制
const setupAutoClose = (popover, closeCallback) => {
    // 点击外部关闭
    const handleClickOutside = (event) => {
        if (!popover.contains(event.target)) {
            closeCallback();
            document.removeEventListener('click', handleClickOutside);
        }
    };
    
    // ESC键关闭
    const handleEscKey = (event) => {
        if (event.key === 'Escape') {
            closeCallback();
            document.removeEventListener('keydown', handleEscKey);
        }
    };
    
    setTimeout(() => {
        document.addEventListener('click', handleClickOutside);
        document.addEventListener('keydown', handleEscKey);
    }, 100);
};
```

### 2. 状态刷新

```javascript
// 刷新通知状态
const refreshNotificationStatus = async (messageId) => {
    try {
        showLoadingIndicator();
        
        const updatedMessage = await rpc('/mail/message/notification_status', {
            message_id: messageId
        });
        
        // 更新本地消息状态
        updateMessageNotifications(updatedMessage);
        
        // 重新渲染弹出框
        rerenderPopover();
        
    } catch (error) {
        showErrorMessage('刷新状态失败');
    } finally {
        hideLoadingIndicator();
    }
};
```

### 3. 重试失败通知

```javascript
// 重试失败的通知
const retryFailedNotifications = async (messageId) => {
    try {
        const result = await rpc('/mail/message/retry_notifications', {
            message_id: messageId
        });
        
        if (result.success) {
            showSuccessMessage('重试成功');
            refreshNotificationStatus(messageId);
        } else {
            showErrorMessage('重试失败：' + result.error);
        }
        
    } catch (error) {
        showErrorMessage('重试操作失败');
    }
};
```

## 样式和主题

### 1. 基础样式

```css
.message-notification-popover {
    position: absolute;
    z-index: 1050;
    max-width: 400px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 0;
    font-size: 14px;
}

.popover-header {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.popover-body {
    padding: 12px 16px;
    max-height: 300px;
    overflow-y: auto;
}

.recipient-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.recipient-item:last-child {
    border-bottom: none;
}

.recipient-item img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
}

.notification-status {
    font-size: 12px;
    margin-top: 4px;
}

.notification-status.sent {
    color: #28a745;
}

.notification-status.pending {
    color: #ffc107;
}

.notification-status.exception,
.notification-status.bounce {
    color: #dc3545;
}
```

### 2. 移动端样式

```css
@media (max-width: 768px) {
    .message-notification-popover.popover-fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        max-width: none;
        border-radius: 0;
        z-index: 1060;
    }
    
    .popover-fullscreen .popover-body {
        max-height: calc(100vh - 120px);
    }
}
```

## 性能优化

### 1. 虚拟化列表

```javascript
// 大量通知时使用虚拟化
const VirtualizedNotificationList = ({ notifications }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    const visibleNotifications = notifications.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-notification-list">
            {visibleNotifications.map(renderRecipientInfo)}
        </div>
    );
};
```

### 2. 状态缓存

```javascript
// 缓存通知状态
const notificationCache = new Map();

const getCachedNotificationStatus = (messageId) => {
    const cached = notificationCache.get(messageId);
    if (cached && Date.now() - cached.timestamp < 30000) { // 30秒缓存
        return cached.data;
    }
    return null;
};
```

## 设计模式

### 1. 弹出框模式 (Popover Pattern)
- 非模态的信息显示
- 上下文相关的详细信息

### 2. 观察者模式 (Observer Pattern)
- 监听通知状态变化
- 实时更新显示内容

### 3. 策略模式 (Strategy Pattern)
- 不同设备的不同显示策略
- 桌面端 vs 移动端的适配

## 注意事项

1. **性能考虑**: 大量通知时的渲染性能
2. **用户体验**: 提供清晰的状态指示
3. **实时性**: 及时更新通知状态
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **状态过滤**: 支持按状态过滤通知
2. **批量操作**: 支持批量重试或取消
3. **详细日志**: 显示详细的发送日志
4. **统计图表**: 提供可视化的状态统计
5. **导出功能**: 支持导出通知报告

该组件为用户提供了详细的消息通知状态信息，增强了邮件系统的透明度和可控性。

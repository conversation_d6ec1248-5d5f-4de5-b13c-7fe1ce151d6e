# Chat Bubble - 聊天气泡

## 概述

`chat_bubble.js` 实现了 Odoo 邮件系统中的聊天气泡组件，用于在界面上显示聊天窗口的缩略图标。该组件提供了悬停预览、拖拽移动、状态指示等功能，为用户提供便捷的聊天窗口管理体验。

## 文件信息
- **路径**: `/mail/static/src/core/common/chat_bubble.js`
- **行数**: 71
- **模块**: `@mail/core/common/chat_bubble`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架组件和钩子
'@web/core/utils/hooks'               // 服务钩子
'@mail/utils/common/hooks'            // 邮件系统自定义钩子
'@web/core/dropdown/dropdown_hooks'   // 下拉菜单钩子
'@web/core/dropdown/dropdown'         // 下拉菜单组件
'@mail/core/common/im_status'         // 即时消息状态组件
'@mail/core/common/country_flag'      // 国家旗帜组件
```

## 组件定义

### ChatBubble 类

继承自 OWL Component 的聊天气泡组件：

```javascript
class ChatBubble extends Component {
    static components = { CountryFlag, ImStatus, Dropdown };
    static props = ["chatWindow"];
    static template = "mail.ChatBubble";
}
```

### 子组件

- **`CountryFlag`**: 显示国家旗帜
- **`ImStatus`**: 显示即时消息状态
- **`Dropdown`**: 下拉菜单功能

### Props

- **`chatWindow`**: 聊天窗口对象，包含线程信息和窗口状态

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 服务依赖
    this.store = useState(useService("mail.store"));
    
    // 悬停状态管理
    this.wasHover = false;
    this.hover = useHover(["root", "preview*"], {
        onHover: () => (this.preview.isOpen = true),
        onHovering: [100, () => (this.state.showClose = true)],
        onAway: () => {
            this.state.showClose = false;
            this.preview.isOpen = false;
        },
    });
    
    // 下拉预览状态
    this.preview = useDropdownState();
    
    // DOM 引用和状态
    this.rootRef = useRef("root");
    this.state = useState({ bouncing: false, showClose: true });
    
    // 重要消息计数器效果
    useEffect(
        () => {
            this.state.bouncing = this.thread.importantCounter ? true : this.state.bouncing;
        },
        () => [this.thread.importantCounter]
    );
    
    // 嵌入式聊天的拖拽功能
    if (this.env.embedLivechat) {
        this.position = useState({ left: "auto", top: "auto" });
        useMovable({
            cursor: "grabbing",
            ref: this.rootRef,
            elements: ".o-mail-ChatBubble",
            onDrop: ({ top, left }) =>
                Object.assign(this.position, { left: `${left}px`, top: `${top}px` }),
        });
    }
}
```

## 核心功能

### 1. 悬停交互

#### useHover 配置
```javascript
this.hover = useHover(["root", "preview*"], {
    onHover: () => (this.preview.isOpen = true),
    onHovering: [100, () => (this.state.showClose = true)],
    onAway: () => {
        this.state.showClose = false;
        this.preview.isOpen = false;
    },
});
```

**悬停行为**:
- **`onHover`**: 鼠标悬停时立即打开预览
- **`onHovering`**: 悬停100ms后显示关闭按钮
- **`onAway`**: 鼠标离开时隐藏关闭按钮和预览

**监听元素**:
- `"root"`: 主要的气泡元素
- `"preview*"`: 预览相关的所有元素

### 2. 状态管理

#### 组件状态
```javascript
this.state = useState({ 
    bouncing: false,    // 弹跳动画状态
    showClose: true     // 是否显示关闭按钮
});
```

#### 弹跳动画效果
```javascript
useEffect(
    () => {
        this.state.bouncing = this.thread.importantCounter ? true : this.state.bouncing;
    },
    () => [this.thread.importantCounter]
);
```

**功能说明**:
- 监听线程的重要消息计数器
- 有新的重要消息时触发弹跳动画
- 吸引用户注意未读的重要消息

### 3. 拖拽移动功能

#### 嵌入式聊天拖拽
```javascript
if (this.env.embedLivechat) {
    this.position = useState({ left: "auto", top: "auto" });
    useMovable({
        cursor: "grabbing",
        ref: this.rootRef,
        elements: ".o-mail-ChatBubble",
        onDrop: ({ top, left }) =>
            Object.assign(this.position, { left: `${left}px`, top: `${top}px` }),
    });
}
```

**拖拽特性**:
- 仅在嵌入式聊天环境中启用
- 使用 `grabbing` 光标样式
- 拖拽结束后更新位置状态
- 支持自由定位聊天气泡

### 4. 下拉预览

#### 预览状态管理
```javascript
this.preview = useDropdownState();
```

**预览功能**:
- 悬停时显示最新消息预览
- 使用下拉菜单组件实现
- 自动定位和显示控制

## 计算属性

### thread - 获取线程对象
```javascript
get thread() {
    return this.props.chatWindow.thread;
}
```

### previewContent - 预览内容
```javascript
get previewContent() {
    const lastMessage = this.thread?.newestPersistentNotEmptyOfAllMessage;
    if (!lastMessage) {
        return false;
    }
    return lastMessage.inlineBody;
}
```

**功能说明**:
- 获取线程中最新的非空持久化消息
- 返回消息的内联正文内容
- 用于在预览中显示最新消息

## 使用场景

### 1. 聊天窗口管理
```xml
<ChatBubble chatWindow="chatWindow" />
```

### 2. 多窗口聊天
- 同时管理多个聊天会话
- 快速切换不同的聊天窗口
- 预览最新消息内容

### 3. 嵌入式客服聊天
- 在网站中嵌入客服聊天功能
- 支持拖拽调整位置
- 提供便捷的用户体验

### 4. 状态指示
- 显示在线状态（通过 ImStatus 组件）
- 显示国家信息（通过 CountryFlag 组件）
- 重要消息提醒（弹跳动画）

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合多个子组件提供完整功能
- 每个子组件负责特定的显示内容

### 2. 状态管理模式
- 使用响应式状态管理交互状态
- 自动响应数据变化更新UI

### 3. 事件驱动模式 (Event-Driven Pattern)
- 通过悬停事件驱动预览显示
- 通过拖拽事件更新位置

### 4. 条件渲染模式
- 根据环境条件启用不同功能
- 嵌入式聊天特有的拖拽功能

## 交互体验

### 1. 悬停预览
- 鼠标悬停立即显示预览
- 延迟显示关闭按钮避免误操作
- 鼠标离开自动隐藏

### 2. 视觉反馈
- 弹跳动画提示新消息
- 拖拽时改变光标样式
- 状态图标显示在线状态

### 3. 位置记忆
- 拖拽后记住用户设置的位置
- 下次访问时保持位置

## 技术特点

### 1. 性能优化
- 使用 `useEffect` 精确控制重新渲染
- 悬停延迟避免频繁状态变化

### 2. 响应式设计
- 自适应不同屏幕尺寸
- 支持移动端触摸操作

### 3. 可访问性
- 提供适当的光标样式
- 支持键盘导航

## 注意事项

1. **性能考虑**: 频繁的悬停事件可能影响性能
2. **移动端适配**: 拖拽功能在移动端需要特殊处理
3. **位置持久化**: 考虑将位置信息保存到本地存储
4. **边界检测**: 拖拽时需要防止元素移出可视区域

## 扩展建议

1. **动画增强**: 添加更丰富的过渡动画效果
2. **快捷操作**: 支持右键菜单快捷操作
3. **批量管理**: 支持批量关闭或管理多个聊天
4. **主题定制**: 支持自定义气泡样式和主题
5. **通知集成**: 集成系统通知功能

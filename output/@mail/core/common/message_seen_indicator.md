# Message Seen Indicator - 消息已读指示器

## 概述

`message_seen_indicator.js` 实现了 Odoo 邮件系统中的消息已读指示器组件，用于显示消息的阅读状态。该组件包含已读状态摘要显示和详细的已读用户对话框，支持不同数量用户的智能文本生成，为用户提供清晰的消息传递状态反馈，是邮件系统中重要的状态指示组件。

## 文件信息
- **路径**: `/mail/static/src/core/common/message_seen_indicator.js`
- **行数**: 93
- **模块**: `@mail/core/common/message_seen_indicator`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/dialog/dialog'      // 对话框组件
'@web/core/l10n/translation'   // 国际化
'@web/core/utils/hooks'        // Web 核心钩子
'@web/core/browser/browser'    // 浏览器工具
```

## 组件定义

### MessageSeenIndicatorDialog 类

```javascript
class MessageSeenIndicatorDialog extends Component {
    static components = { Dialog };
    static template = "mail.MessageSeenIndicatorDialog";
    static props = ["message", "close?"];
}
```

### MessageSeenIndicator 类

```javascript
class MessageSeenIndicator extends Component {
    static template = "mail.MessageSeenIndicator";
    static props = ["message", "thread", "className?"];
}
```

## Props 配置

### MessageSeenIndicator Props

```typescript
interface Props {
    message: import("models").Message;    // 消息对象
    thread: import("models").Thread;      // 线程对象
    className?: string;                   // 自定义CSS类名（可选）
}
```

### MessageSeenIndicatorDialog Props

```typescript
interface Props {
    message: import("models").Message;    // 消息对象
    close?: function;                     // 关闭对话框回调（可选）
}
```

## 组件设置

### MessageSeenIndicator setup()

```javascript
setup() {
    super.setup();
    this.dialog = useService("dialog");
}
```

### MessageSeenIndicatorDialog setup()

```javascript
setup() {
    super.setup();
    this.contentRef = useRef("content");
    useExternalListener(
        browser,
        "click",
        (ev) => {
            if (!this.contentRef?.el.contains(ev.target)) {
                this.props.close();
            }
        },
        true
    );
}
```

**初始化内容**:
- 对话框服务引用
- 外部点击监听器
- DOM内容引用

## 核心功能

### 1. 已读状态摘要

```javascript
get summary() {
    if (this.props.message.hasEveryoneSeen) {
        if (this.props.thread.channelMembers.length === 2) {
            return _t("Seen by %(user)s", { user: this.props.thread.correspondent.name });
        }
        return _t("Seen by everyone");
    }
    
    const seenMembers = this.props.message.channelMemberHaveSeen;
    const [user1, user2, user3] = seenMembers.map((member) => member.name);
    
    switch (seenMembers.length) {
        case 0:
            return _t("Sent");
        case 1:
            return _t("Seen by %(user)s", { user: user1 });
        case 2:
            return _t("Seen by %(user1)s and %(user2)s", { user1, user2 });
        case 3:
            return _t("Seen by %(user1)s, %(user2)s and %(user3)s", { user1, user2, user3 });
        case 4:
            return _t("Seen by %(user1)s, %(user2)s, %(user3)s and 1 other", {
                user1, user2, user3,
            });
        default:
            return _t("Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others", {
                user1, user2, user3,
                count: seenMembers.length - 3,
            });
    }
}
```

**摘要生成逻辑**:
- **所有人已读**: 
  - 双人对话显示对方姓名
  - 群聊显示"所有人已读"
- **部分已读**: 根据人数显示不同格式
  - 0人: "已发送"
  - 1人: "用户A已读"
  - 2人: "用户A和用户B已读"
  - 3人: "用户A、用户B和用户C已读"
  - 4人: "用户A、用户B、用户C和1个其他人已读"
  - 更多: "用户A、用户B、用户C和X个其他人已读"

### 2. 详情对话框

```javascript
openDialog() {
    if (this.props.message.channelMemberHaveSeen.length === 0) {
        return;
    }
    this.dialog.add(MessageSeenIndicatorDialog, { 
        message: this.props.message 
    });
}
```

**对话框逻辑**:
- 检查是否有已读用户
- 如果没有已读用户，不打开对话框
- 使用对话框服务显示详细信息

## 使用场景

### 1. 消息状态显示

```javascript
// 在消息组件中显示已读状态
const MessageWithSeenIndicator = ({ message, thread }) => {
    return (
        <div class="message-container">
            <div class="message-content">{message.body}</div>
            <div class="message-footer">
                <span class="message-time">{formatTime(message.date)}</span>
                <MessageSeenIndicator 
                    message={message}
                    thread={thread}
                    className="seen-indicator"
                />
            </div>
        </div>
    );
};
```

### 2. 群聊已读状态

```javascript
// 群聊中的已读状态显示
const GroupChatSeenIndicator = ({ message, thread }) => {
    const seenCount = message.channelMemberHaveSeen.length;
    const totalMembers = thread.channelMembers.length - 1; // 排除发送者
    
    return (
        <div class="group-seen-indicator">
            <MessageSeenIndicator 
                message={message}
                thread={thread}
            />
            <div class="seen-progress">
                <div class="progress-bar">
                    <div 
                        class="progress-fill"
                        style={`width: ${(seenCount / totalMembers) * 100}%`}
                    ></div>
                </div>
                <span class="seen-count">{seenCount}/{totalMembers}</span>
            </div>
        </div>
    );
};
```

### 3. 私聊已读状态

```javascript
// 私聊中的已读状态
const PrivateChatSeenIndicator = ({ message, thread }) => {
    const isRead = message.hasEveryoneSeen;
    
    return (
        <div class="private-seen-indicator">
            <MessageSeenIndicator 
                message={message}
                thread={thread}
                className={isRead ? 'read' : 'unread'}
            />
            <i class={`fa ${isRead ? 'fa-check-double' : 'fa-check'}`}></i>
        </div>
    );
};
```

### 4. 已读详情弹窗

```javascript
// 点击显示已读详情
const showSeenDetails = (message) => {
    if (message.channelMemberHaveSeen.length > 0) {
        dialogService.add(MessageSeenIndicatorDialog, {
            message: message,
            close: () => dialogService.closeAll()
        });
    }
};
```

## 状态计算

### 1. 已读用户列表

```javascript
// 获取已读用户列表
const getSeenUsers = (message) => {
    return message.channelMemberHaveSeen.map(member => ({
        id: member.id,
        name: member.name,
        avatar: member.persona.avatarUrl,
        seenTime: member.seenMessageDate
    }));
};
```

### 2. 已读状态检查

```javascript
// 检查消息是否被所有人已读
const isSeenByEveryone = (message, thread) => {
    const totalMembers = thread.channelMembers.length - 1; // 排除发送者
    const seenCount = message.channelMemberHaveSeen.length;
    return seenCount === totalMembers;
};

// 检查特定用户是否已读
const isSeenByUser = (message, userId) => {
    return message.channelMemberHaveSeen.some(member => member.id === userId);
};
```

### 3. 已读率计算

```javascript
// 计算已读率
const calculateSeenRate = (message, thread) => {
    const totalMembers = thread.channelMembers.length - 1;
    const seenCount = message.channelMemberHaveSeen.length;
    
    if (totalMembers === 0) return 0;
    
    return {
        percentage: (seenCount / totalMembers) * 100,
        seenCount: seenCount,
        totalCount: totalMembers
    };
};
```

## 对话框内容

### 1. 已读用户列表

```javascript
// 渲染已读用户列表
const renderSeenUserList = (message) => {
    const seenUsers = getSeenUsers(message);
    
    return `
        <div class="seen-user-list">
            <h3>已读用户 (${seenUsers.length})</h3>
            ${seenUsers.map(user => `
                <div class="seen-user-item">
                    <img src="${user.avatar}" alt="${user.name}" />
                    <div class="user-info">
                        <div class="user-name">${user.name}</div>
                        <div class="seen-time">${formatTime(user.seenTime)}</div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
};
```

### 2. 未读用户列表

```javascript
// 渲染未读用户列表
const renderUnseenUserList = (message, thread) => {
    const seenUserIds = new Set(message.channelMemberHaveSeen.map(m => m.id));
    const unseenUsers = thread.channelMembers.filter(member => 
        !seenUserIds.has(member.id) && member.id !== message.author.id
    );
    
    return `
        <div class="unseen-user-list">
            <h3>未读用户 (${unseenUsers.length})</h3>
            ${unseenUsers.map(member => `
                <div class="unseen-user-item">
                    <img src="${member.persona.avatarUrl}" alt="${member.name}" />
                    <div class="user-info">
                        <div class="user-name">${member.name}</div>
                        <div class="status">未读</div>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
};
```

## 外部点击处理

### 1. 点击外部关闭

```javascript
// 对话框外部点击关闭
useExternalListener(
    browser,
    "click",
    (ev) => {
        if (!this.contentRef?.el.contains(ev.target)) {
            this.props.close();
        }
    },
    true
);
```

### 2. 键盘事件

```javascript
// 键盘ESC关闭
useExternalListener(
    browser,
    "keydown",
    (ev) => {
        if (ev.key === 'Escape') {
            this.props.close();
        }
    }
);
```

## 样式和主题

### 1. 基础样式

```css
.message-seen-indicator {
    font-size: 0.8em;
    color: #666;
    cursor: pointer;
    
    &:hover {
        color: #333;
        text-decoration: underline;
    }
    
    &.read {
        color: #28a745;
    }
    
    &.unread {
        color: #6c757d;
    }
}

.seen-indicator-dialog {
    .seen-user-list,
    .unseen-user-list {
        margin: 16px 0;
        
        h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            color: #333;
        }
    }
    
    .seen-user-item,
    .unseen-user-item {
        display: flex;
        align-items: center;
        padding: 8px 0;
        
        img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            margin-right: 12px;
        }
        
        .user-info {
            flex: 1;
            
            .user-name {
                font-weight: 500;
            }
            
            .seen-time {
                font-size: 0.9em;
                color: #666;
            }
            
            .status {
                font-size: 0.9em;
                color: #999;
            }
        }
    }
}
```

### 2. 状态图标

```css
.seen-status-icon {
    margin-left: 4px;
    
    &.single-check {
        color: #6c757d;
    }
    
    &.double-check {
        color: #28a745;
    }
    
    &.read-receipt {
        color: #007bff;
    }
}
```

## 国际化支持

### 1. 多语言文本

```javascript
// 国际化文本定义
const SEEN_TEXTS = {
    sent: _t("Sent"),
    seenByUser: _t("Seen by %(user)s"),
    seenByTwo: _t("Seen by %(user1)s and %(user2)s"),
    seenByThree: _t("Seen by %(user1)s, %(user2)s and %(user3)s"),
    seenByMany: _t("Seen by %(user1)s, %(user2)s, %(user3)s and %(count)s others"),
    seenByEveryone: _t("Seen by everyone")
};
```

### 2. 时间格式化

```javascript
// 本地化时间格式
const formatSeenTime = (date, locale) => {
    const now = new Date();
    const seenDate = new Date(date);
    const diffInHours = (now - seenDate) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
        return _t("Just now");
    } else if (diffInHours < 24) {
        return _t("%(hours)s hours ago", { hours: Math.floor(diffInHours) });
    } else {
        return seenDate.toLocaleDateString(locale);
    }
};
```

## 性能优化

### 1. 计算缓存

```javascript
// 缓存已读状态计算
const seenStatusCache = new Map();

const getCachedSeenStatus = (messageId) => {
    if (seenStatusCache.has(messageId)) {
        return seenStatusCache.get(messageId);
    }
    
    const status = calculateSeenStatus(messageId);
    seenStatusCache.set(messageId, status);
    return status;
};
```

### 2. 虚拟化列表

```javascript
// 大量用户时的虚拟化
const VirtualizedUserList = ({ users, maxVisible = 20 }) => {
    const [visibleUsers, setVisibleUsers] = useState(users.slice(0, maxVisible));
    
    return (
        <div class="virtualized-user-list">
            {visibleUsers.map(renderUserItem)}
            {users.length > maxVisible && (
                <button onClick={() => loadMoreUsers()}>
                    显示更多 ({users.length - maxVisible})
                </button>
            )}
        </div>
    );
};
```

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合指示器和对话框组件
- 提供统一的已读状态接口

### 2. 策略模式 (Strategy Pattern)
- 根据用户数量采用不同的文本策略
- 私聊 vs 群聊的不同显示策略

### 3. 观察者模式 (Observer Pattern)
- 监听已读状态变化
- 响应点击和外部事件

## 注意事项

1. **实时性**: 确保已读状态的实时更新
2. **性能考虑**: 大量用户时的渲染性能
3. **用户体验**: 提供清晰的状态指示
4. **隐私保护**: 合理显示已读信息

## 扩展建议

1. **已读时间**: 显示具体的已读时间
2. **已读统计**: 提供已读率统计图表
3. **批量标记**: 支持批量标记为已读
4. **已读提醒**: 支持已读状态变化提醒
5. **自定义显示**: 支持自定义已读状态显示

该组件为邮件系统提供了完整的消息已读状态指示功能，增强了消息传递的透明度和用户体验。

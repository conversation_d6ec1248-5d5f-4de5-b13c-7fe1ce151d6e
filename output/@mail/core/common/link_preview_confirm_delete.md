# Link Preview Confirm Delete - 链接预览删除确认

## 概述

`link_preview_confirm_delete.js` 实现了 Odoo 邮件系统中的链接预览删除确认对话框组件。该组件提供了删除单个链接预览或删除消息中所有链接预览的选项，通过友好的确认界面防止用户误删操作，是链接预览管理功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/core/common/link_preview_confirm_delete.js`
- **行数**: 59
- **模块**: `@mail/core/common/link_preview_confirm_delete`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'        // RPC 网络请求
'@odoo/owl'                    // OWL 框架
'@web/core/dialog/dialog'      // 对话框组件
'@web/core/utils/hooks'        // Web 核心钩子
```

## 组件定义

### LinkPreviewConfirmDelete 类

```javascript
class LinkPreviewConfirmDelete extends Component {
    static components = { Dialog };
    static props = ["linkPreview", "close", "LinkPreview"];
    static template = "mail.LinkPreviewConfirmDelete";
}
```

## Props 配置

### Props 定义

```typescript
interface Props {
    linkPreview: import("models").LinkPreview;  // 要删除的链接预览
    close: function;                            // 关闭对话框的回调
    LinkPreview: Component;                     // 链接预览组件引用
}
```

### Props 详细说明

- **`linkPreview`** (必需):
  - 类型: LinkPreview 模型
  - 用途: 要删除的链接预览对象
  - 包含: 预览数据和关联的消息信息

- **`close`** (必需):
  - 类型: 函数
  - 用途: 关闭对话框的回调函数
  - 调用: 在确认或取消操作后调用

- **`LinkPreview`** (必需):
  - 类型: 组件类
  - 用途: 链接预览组件的引用
  - 功能: 用于类型检查和组件关联

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 用于访问消息和预览数据

## 核心功能

### 1. 消息引用

```javascript
get message() {
    return this.props.linkPreview.message;
}
```

**功能**:
- 获取链接预览所属的消息
- 用于批量删除操作
- 提供消息上下文信息

### 2. 删除单个预览

```javascript
onClickOk() {
    rpc(
        "/mail/link_preview/hide",
        { link_preview_ids: [this.props.linkPreview.id] },
        { silent: true }
    );
    this.props.close();
}
```

**删除流程**:
1. 调用服务器端点隐藏链接预览
2. 传递单个预览ID数组
3. 使用静默模式避免显示加载状态
4. 关闭确认对话框

### 3. 删除所有预览

```javascript
onClickDeleteAll() {
    rpc(
        "/mail/link_preview/hide",
        { link_preview_ids: this.message.linkPreviews.map((lp) => lp.id) },
        { silent: true }
    );
    this.props.close();
}
```

**批量删除流程**:
1. 获取消息中所有链接预览的ID
2. 调用同一个服务器端点
3. 传递所有预览ID数组
4. 静默执行并关闭对话框

## 使用场景

### 1. 单个预览删除

```javascript
// 在链接预览组件中触发删除确认
const showDeleteConfirmation = (linkPreview) => {
    dialogService.add(LinkPreviewConfirmDelete, {
        linkPreview: linkPreview,
        close: () => dialogService.closeAll(),
        LinkPreview: LinkPreviewComponent
    });
};
```

### 2. 消息编辑时删除

```javascript
// 在消息编辑器中删除预览
const deletePreviewInComposer = (preview) => {
    dialogService.add(LinkPreviewConfirmDelete, {
        linkPreview: preview,
        close: () => {
            dialogService.closeAll();
            refreshComposer();
        },
        LinkPreview: LinkPreviewComponent
    });
};
```

### 3. 管理员批量删除

```javascript
// 管理员删除消息中的所有预览
const adminDeleteAllPreviews = (message) => {
    if (message.linkPreviews.length > 0) {
        const firstPreview = message.linkPreviews[0];
        dialogService.add(LinkPreviewConfirmDelete, {
            linkPreview: firstPreview,
            close: () => dialogService.closeAll(),
            LinkPreview: LinkPreviewComponent
        });
    }
};
```

### 4. 权限检查后删除

```javascript
// 检查权限后显示删除确认
const deleteWithPermissionCheck = (preview, user) => {
    if (canDeletePreview(preview, user)) {
        dialogService.add(LinkPreviewConfirmDelete, {
            linkPreview: preview,
            close: () => dialogService.closeAll(),
            LinkPreview: LinkPreviewComponent
        });
    } else {
        showErrorMessage('没有权限删除此预览');
    }
};
```

## 服务器交互

### 1. 隐藏预览端点

```javascript
// 服务器端点: /mail/link_preview/hide
// 参数: { link_preview_ids: [1, 2, 3] }
// 功能: 隐藏指定的链接预览
```

### 2. 批量操作支持

```javascript
// 支持批量隐藏多个预览
const hideMultiplePreviews = async (previewIds) => {
    try {
        await rpc("/mail/link_preview/hide", {
            link_preview_ids: previewIds
        }, { silent: true });
        
        return { success: true };
    } catch (error) {
        console.error('隐藏预览失败:', error);
        return { success: false, error };
    }
};
```

### 3. 错误处理

```javascript
// 带错误处理的删除操作
const deletePreviewWithErrorHandling = async (previewIds) => {
    try {
        await rpc("/mail/link_preview/hide", {
            link_preview_ids: previewIds
        });
        
        showSuccessMessage('链接预览已删除');
        
    } catch (error) {
        if (error.status === 403) {
            showErrorMessage('没有权限删除此预览');
        } else if (error.status === 404) {
            showErrorMessage('预览不存在或已被删除');
        } else {
            showErrorMessage('删除失败，请稍后重试');
        }
    }
};
```

## 对话框界面

### 1. 确认选项

```javascript
// 对话框提供的选项
const dialogOptions = {
    title: '删除链接预览',
    message: '您要删除这个链接预览吗？',
    buttons: [
        {
            text: '删除此预览',
            class: 'btn-primary',
            click: () => this.onClickOk()
        },
        {
            text: '删除所有预览',
            class: 'btn-warning',
            click: () => this.onClickDeleteAll(),
            condition: () => this.message.linkPreviews.length > 1
        },
        {
            text: '取消',
            class: 'btn-secondary',
            click: () => this.props.close()
        }
    ]
};
```

### 2. 条件显示

```javascript
// 根据预览数量显示不同选项
const getDialogButtons = (message) => {
    const buttons = [
        { text: '删除此预览', action: 'delete_single' }
    ];
    
    // 只有多个预览时才显示"删除所有"选项
    if (message.linkPreviews.length > 1) {
        buttons.push({
            text: `删除所有 ${message.linkPreviews.length} 个预览`,
            action: 'delete_all'
        });
    }
    
    buttons.push({ text: '取消', action: 'cancel' });
    
    return buttons;
};
```

### 3. 预览信息显示

```javascript
// 在对话框中显示预览信息
const renderPreviewInfo = (linkPreview) => {
    return `
        <div class="preview-info">
            <h4>${linkPreview.og_title || '无标题'}</h4>
            <p>${linkPreview.og_description || '无描述'}</p>
            <small>${linkPreview.source_url}</small>
        </div>
    `;
};
```

## 用户体验

### 1. 确认提示

```javascript
// 提供清晰的确认提示
const getConfirmationMessage = (linkPreview, messagePreviewCount) => {
    if (messagePreviewCount === 1) {
        return '确定要删除这个链接预览吗？';
    } else {
        return `确定要删除这个链接预览吗？此消息还有 ${messagePreviewCount - 1} 个其他预览。`;
    }
};
```

### 2. 操作反馈

```javascript
// 操作完成后的反馈
const showOperationFeedback = (action, count) => {
    switch (action) {
        case 'delete_single':
            showSuccessMessage('链接预览已删除');
            break;
        case 'delete_all':
            showSuccessMessage(`已删除 ${count} 个链接预览`);
            break;
        case 'cancel':
            // 无需反馈
            break;
    }
};
```

### 3. 键盘快捷键

```javascript
// 支持键盘操作
const handleKeydown = (event) => {
    switch (event.key) {
        case 'Enter':
            this.onClickOk();
            break;
        case 'Escape':
            this.props.close();
            break;
        case 'Delete':
            if (event.shiftKey) {
                this.onClickDeleteAll();
            } else {
                this.onClickOk();
            }
            break;
    }
};
```

## 权限和安全

### 1. 权限验证

```javascript
// 服务器端权限验证
const validateDeletePermission = (linkPreview, user) => {
    // 消息作者可以删除
    if (linkPreview.message.author.id === user.id) {
        return true;
    }
    
    // 管理员可以删除
    if (user.hasRole('admin')) {
        return true;
    }
    
    // 频道管理员可以删除
    if (linkPreview.message.thread.isChannel && 
        linkPreview.message.thread.hasChannelAdmin(user)) {
        return true;
    }
    
    return false;
};
```

### 2. 操作日志

```javascript
// 记录删除操作
const logDeleteOperation = (linkPreview, user, action) => {
    console.log(`User ${user.id} performed ${action} on link preview ${linkPreview.id}`);
    
    // 发送审计日志
    rpc('/mail/audit/log', {
        action: action,
        resource_type: 'link_preview',
        resource_id: linkPreview.id,
        user_id: user.id,
        timestamp: new Date().toISOString()
    });
};
```

## 性能优化

### 1. 静默操作

```javascript
// 使用静默模式避免UI阻塞
const silentDelete = async (previewIds) => {
    return await rpc("/mail/link_preview/hide", {
        link_preview_ids: previewIds
    }, { 
        silent: true,  // 不显示加载指示器
        timeout: 5000  // 5秒超时
    });
};
```

### 2. 批量优化

```javascript
// 批量删除比多次单独删除更高效
const optimizedBatchDelete = (previews) => {
    const previewIds = previews.map(p => p.id);
    
    // 一次性删除所有预览
    return rpc("/mail/link_preview/hide", {
        link_preview_ids: previewIds
    }, { silent: true });
};
```

## 设计模式

### 1. 命令模式 (Command Pattern)
- 删除操作封装为命令
- 支持撤销和重做

### 2. 策略模式 (Strategy Pattern)
- 单个删除 vs 批量删除的不同策略
- 根据权限采用不同的删除策略

### 3. 观察者模式 (Observer Pattern)
- 监听删除操作完成事件
- 通知相关组件更新状态

## 注意事项

1. **权限检查**: 确保用户有权限删除预览
2. **数据一致性**: 删除后更新相关的UI状态
3. **用户体验**: 提供清晰的确认和反馈
4. **错误处理**: 妥善处理网络错误和权限错误

## 扩展建议

1. **撤销功能**: 支持删除后的撤销操作
2. **批量选择**: 支持选择多个预览进行删除
3. **删除原因**: 允许用户提供删除原因
4. **审计日志**: 记录详细的删除操作日志
5. **自动清理**: 定期清理过期的链接预览

该组件为链接预览管理提供了用户友好的删除确认界面，确保用户操作的安全性和可控性。

# Out of Focus Service Patch - 失焦服务补丁

## 概述

`out_of_focus_service_patch.js` 实现了 Odoo 邮件系统中失焦服务在公共Web环境下的补丁，用于在浏览器窗口失去焦点时显示未读消息通知。该补丁扩展了失焦服务的功能，添加了浏览器标题栏的未读消息计数显示、闪烁效果等特性，确保用户在切换到其他应用时仍能及时了解新消息，提升了用户体验和消息可见性。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/out_of_focus_service_patch.js`
- **行数**: 55
- **模块**: `@mail/core/public_web/out_of_focus_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/out_of_focus_service'  // 失焦服务基类
'@web/core/utils/patch'                   // 补丁工具
'@web/core/l10n/translation'              // 国际化
'@web/core/utils/strings'                 // 字符串工具
```

## 常量定义

```javascript
const UNREAD_MSG_TITLE = 100;  // 未读消息标题优先级
```

**常量用途**:
- 定义标题服务中未读消息部分的优先级
- 确保未读消息显示在标题的正确位置

## 补丁实现

### OutOfFocusService 原型补丁

```javascript
patch(OutOfFocusService.prototype, {
    setup(env, services) {
        super.setup(env, services);
        this.titleService = services.title;
        this.counter = 0;
        env.bus.addEventListener("window_focus", () => this.clearUnreadMessage());
        this.intervalShowing = true;
    },
    
    // 其他方法...
});
```

### 服务依赖扩展

```javascript
outOfFocusService.dependencies = [...outOfFocusService.dependencies, "title"];
```

**依赖扩展**:
- 添加标题服务依赖
- 确保标题服务在失焦服务之前初始化

## 核心功能

### 1. 服务初始化

```javascript
setup(env, services) {
    super.setup(env, services);
    this.titleService = services.title;
    this.counter = 0;
    env.bus.addEventListener("window_focus", () => this.clearUnreadMessage());
    this.intervalShowing = true;
}
```

**初始化内容**:
- 调用父类初始化
- 获取标题服务引用
- 初始化未读消息计数器
- 监听窗口焦点事件
- 设置闪烁状态标志

### 2. 清除未读消息

```javascript
clearUnreadMessage() {
    this.counter = 0;
    this.titleService.setParts({ [UNREAD_MSG_TITLE]: undefined });
    this.intervalShowing = true;
    this.titlePattern = undefined;
    clearInterval(this.newMessageInterval);
}
```

**清除逻辑**:
- 重置计数器为0
- 清除标题中的未读消息部分
- 重置闪烁状态
- 清除标题模式
- 停止闪烁定时器

### 3. 标题更新

```javascript
updateTitle() {
    this.titleService.setParts({
        [UNREAD_MSG_TITLE]: this.intervalShowing
            ? sprintf(this.titlePattern, this.counter)
            : undefined,
    });
}
```

**更新逻辑**:
- 根据闪烁状态决定是否显示
- 使用模式和计数器格式化标题
- 通过标题服务更新浏览器标题

### 4. 通知处理

```javascript
notify() {
    super.notify(...arguments);
    clearInterval(this.newMessageInterval);
    this.counter++;
    this.titlePattern = this.counter === 1 ? _t("%s Message") : _t("%s Messages");
    this.updateTitle();
    this.newMessageInterval = this.setInterval(() => {
        this.updateTitle();
        this.intervalShowing = !this.intervalShowing;
    }, 1_000);
}
```

**通知流程**:
1. 调用父类通知方法
2. 清除现有定时器
3. 增加未读计数器
4. 设置单复数标题模式
5. 更新标题显示
6. 启动闪烁定时器

### 5. 定时器设置

```javascript
setInterval(func, duration) {
    return setInterval(func, duration);
}
```

**定时器封装**:
- 提供可测试的定时器接口
- 便于单元测试时的模拟

## 使用场景

### 1. 新消息通知

```javascript
// 当收到新消息时触发通知
const handleNewMessage = (message) => {
    if (!document.hasFocus()) {
        outOfFocusService.notify();
        
        // 标题将显示类似 "1 Message - Odoo" 的内容
        // 并且每秒闪烁一次
    }
};
```

### 2. 窗口焦点恢复

```javascript
// 当用户回到窗口时清除通知
window.addEventListener('focus', () => {
    outOfFocusService.clearUnreadMessage();
    
    // 标题恢复正常显示
    // 停止闪烁效果
});
```

### 3. 多消息累积

```javascript
// 多个消息累积显示
const simulateMultipleMessages = () => {
    // 第一条消息
    outOfFocusService.notify(); // "1 Message"
    
    setTimeout(() => {
        // 第二条消息
        outOfFocusService.notify(); // "2 Messages"
    }, 2000);
    
    setTimeout(() => {
        // 第三条消息
        outOfFocusService.notify(); // "3 Messages"
    }, 4000);
};
```

### 4. 自定义通知行为

```javascript
// 扩展通知行为
const customNotifyBehavior = () => {
    // 保存原始方法
    const originalNotify = outOfFocusService.notify;
    
    // 重写通知方法
    outOfFocusService.notify = function(...args) {
        // 自定义逻辑
        if (this.counter >= 10) {
            // 超过10条消息时显示"10+ Messages"
            this.titlePattern = _t("10+ Messages");
        } else {
            // 调用原始逻辑
            originalNotify.apply(this, args);
        }
    };
};
```

## 标题管理

### 1. 标题部分管理

```javascript
// 标题服务的部分管理
const manageTitleParts = () => {
    const titleService = env.services.title;
    
    // 设置不同优先级的标题部分
    titleService.setParts({
        [UNREAD_MSG_TITLE]: "3 Messages",  // 未读消息
        50: "Odoo",                        // 应用名称
        10: "Discuss"                      // 模块名称
    });
    
    // 最终标题: "3 Messages - Discuss - Odoo"
};
```

### 2. 动态标题更新

```javascript
// 动态更新标题内容
const updateDynamicTitle = (messageCount) => {
    const titleService = env.services.title;
    
    if (messageCount === 0) {
        // 清除未读消息部分
        titleService.setParts({ [UNREAD_MSG_TITLE]: undefined });
    } else {
        // 更新未读消息计数
        const pattern = messageCount === 1 ? "%s Message" : "%s Messages";
        const title = sprintf(pattern, messageCount);
        titleService.setParts({ [UNREAD_MSG_TITLE]: title });
    }
};
```

### 3. 标题闪烁效果

```javascript
// 实现标题闪烁效果
const implementTitleBlinking = () => {
    let isShowing = true;
    const originalTitle = document.title;
    const unreadTitle = "3 Messages - Odoo";
    
    const blinkInterval = setInterval(() => {
        document.title = isShowing ? unreadTitle : originalTitle;
        isShowing = !isShowing;
    }, 1000);
    
    // 窗口获得焦点时停止闪烁
    window.addEventListener('focus', () => {
        clearInterval(blinkInterval);
        document.title = originalTitle;
    }, { once: true });
};
```

## 国际化支持

### 1. 多语言消息模式

```javascript
// 不同语言的消息模式
const getLocalizedMessagePattern = (count, locale) => {
    const patterns = {
        'en': count === 1 ? '%s Message' : '%s Messages',
        'zh': '%s 条消息',
        'fr': count === 1 ? '%s Message' : '%s Messages',
        'es': count === 1 ? '%s Mensaje' : '%s Mensajes',
        'de': count === 1 ? '%s Nachricht' : '%s Nachrichten'
    };
    
    return patterns[locale] || patterns['en'];
};
```

### 2. 动态语言切换

```javascript
// 支持动态语言切换
const handleLanguageChange = (newLocale) => {
    if (outOfFocusService.counter > 0) {
        // 更新标题模式
        outOfFocusService.titlePattern = getLocalizedMessagePattern(
            outOfFocusService.counter, 
            newLocale
        );
        
        // 立即更新标题
        outOfFocusService.updateTitle();
    }
};
```

## 性能优化

### 1. 防抖处理

```javascript
// 防抖通知处理
const debouncedNotify = debounce(() => {
    outOfFocusService.notify();
}, 100);

// 在短时间内收到多条消息时使用
const handleBurstMessages = (messages) => {
    messages.forEach(() => {
        debouncedNotify();
    });
};
```

### 2. 内存泄漏防护

```javascript
// 防止内存泄漏
const preventMemoryLeaks = () => {
    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', () => {
        clearInterval(outOfFocusService.newMessageInterval);
    });
    
    // 组件销毁时清理
    onWillUnmount(() => {
        outOfFocusService.clearUnreadMessage();
    });
};
```

### 3. 节流更新

```javascript
// 节流标题更新
const throttledUpdateTitle = throttle(() => {
    outOfFocusService.updateTitle();
}, 500);
```

## 测试支持

### 1. 模拟定时器

```javascript
// 测试时模拟定时器
const mockSetInterval = (callback, delay) => {
    const intervalId = Math.random();
    
    // 立即执行用于测试
    if (process.env.NODE_ENV === 'test') {
        callback();
        return intervalId;
    }
    
    return setInterval(callback, delay);
};

// 在补丁中使用
patch(OutOfFocusService.prototype, {
    setInterval: mockSetInterval
});
```

### 2. 测试工具

```javascript
// 测试辅助工具
const createTestHelpers = () => {
    return {
        simulateFocus: () => {
            window.dispatchEvent(new Event('focus'));
        },
        
        simulateBlur: () => {
            window.dispatchEvent(new Event('blur'));
        },
        
        getUnreadCount: () => {
            return outOfFocusService.counter;
        },
        
        getTitleContent: () => {
            return document.title;
        }
    };
};
```

## 错误处理

### 1. 标题服务错误

```javascript
// 处理标题服务不可用的情况
const safeUpdateTitle = () => {
    try {
        outOfFocusService.updateTitle();
    } catch (error) {
        console.warn('标题更新失败:', error);
        // 回退到直接设置document.title
        if (outOfFocusService.counter > 0) {
            const pattern = outOfFocusService.titlePattern || '%s Messages';
            document.title = sprintf(pattern, outOfFocusService.counter);
        }
    }
};
```

### 2. 定时器错误

```javascript
// 处理定时器错误
const safeSetInterval = (callback, delay) => {
    try {
        return setInterval(() => {
            try {
                callback();
            } catch (error) {
                console.error('定时器回调错误:', error);
            }
        }, delay);
    } catch (error) {
        console.error('设置定时器失败:', error);
        return null;
    }
};
```

## 浏览器兼容性

### 1. 标题API兼容性

```javascript
// 检查浏览器支持
const checkBrowserSupport = () => {
    const features = {
        titleSupport: typeof document.title !== 'undefined',
        focusEvents: 'onfocus' in window,
        visibilityAPI: typeof document.hidden !== 'undefined'
    };
    
    return features;
};
```

### 2. 降级处理

```javascript
// 功能降级处理
const implementFallback = () => {
    const support = checkBrowserSupport();
    
    if (!support.titleSupport) {
        // 不支持标题API时的降级
        console.warn('浏览器不支持标题API');
        return;
    }
    
    if (!support.focusEvents) {
        // 不支持焦点事件时使用可见性API
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                outOfFocusService.clearUnreadMessage();
            }
        });
    }
};
```

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁扩展原有服务功能
- 不修改原始服务结构

### 2. 观察者模式 (Observer Pattern)
- 监听窗口焦点事件
- 响应消息通知事件

### 3. 策略模式 (Strategy Pattern)
- 不同语言的不同显示策略
- 可配置的通知策略

## 注意事项

1. **内存管理**: 及时清理定时器避免内存泄漏
2. **性能影响**: 避免频繁的标题更新
3. **用户体验**: 提供合适的闪烁频率
4. **浏览器兼容**: 考虑不同浏览器的API支持

## 扩展建议

1. **自定义闪烁**: 支持用户自定义闪烁频率和样式
2. **声音通知**: 结合音频通知功能
3. **图标徽章**: 支持浏览器标签页图标徽章
4. **通知优先级**: 支持不同优先级的消息通知
5. **统计分析**: 添加通知效果的统计分析

该补丁为失焦服务提供了重要的视觉通知功能，确保用户在多任务环境下不会错过重要消息，显著提升了用户体验。

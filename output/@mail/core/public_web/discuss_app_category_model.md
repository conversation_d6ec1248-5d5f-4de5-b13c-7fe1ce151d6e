# Discuss App Category Model - 讨论应用分类模型

## 概述

`discuss_app_category_model.js` 实现了 Odoo 邮件系统中的讨论应用分类模型，用于管理讨论侧边栏中的分类项目。该模型定义了分类的属性、状态管理、线程排序逻辑等功能，支持本地存储和服务器存储两种状态保存方式，为讨论应用的侧边栏提供了灵活的分类管理机制。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/discuss_app_category_model.js`
- **行数**: 124
- **模块**: `@mail/core/public_web/discuss_app_category_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/misc'      // 通用工具函数
'@mail/core/common/record'     // 记录基类
'@web/core/browser/browser'    // 浏览器工具
```

## 模型定义

### DiscussAppCategory 类

```javascript
class DiscussAppCategory extends Record {
    static id = "id";
    
    static get(data) {
        return super.get(data);
    }
    
    static insert(data) {
        return super.insert(...arguments);
    }
}
```

## 核心属性

### 基础属性

```javascript
// 基础标识属性
id: string;              // 分类唯一标识
name: string;            // 分类显示名称
icon: string;            // 分类图标
extraClass: string;      // 额外CSS类名
sequence: number;        // 排序序号

// 功能控制属性
hideWhenEmpty: boolean = false;  // 空时是否隐藏
canView: boolean = false;        // 是否可查看
canAdd: boolean = false;         // 是否可添加

// 操作相关属性
addTitle: string;        // 添加按钮标题
addHotkey: string;       // 添加快捷键
```

### 关联属性

```javascript
// 关联的讨论应用
app = Record.one("DiscussApp", {
    compute() {
        return this.store.discuss;
    }
});

// 包含的线程列表
threads = Record.many("Thread", {
    sort(t1, t2) {
        return this.sortThreads(t1, t2);
    },
    inverse: "discussAppCategory"
});
```

## 核心功能

### 1. 线程排序

```javascript
sortThreads(t1, t2) {
    if (this.id === "channels") {
        return String.prototype.localeCompare.call(t1.name, t2.name);
    }
    if (this.id === "chats") {
        return compareDatetime(t2.lastInterestDt, t1.lastInterestDt) || t2.id - t1.id;
    }
}
```

**排序逻辑**:
- **频道分类**: 按名称字母顺序排序
- **聊天分类**: 按最后交互时间倒序，时间相同则按ID倒序
- **其他分类**: 无特殊排序逻辑

### 2. 可见性控制

```javascript
get isVisible() {
    return (
        !this.hideWhenEmpty ||
        this.threads.some((thread) => thread.displayToSelf || thread.isLocallyPinned)
    );
}
```

**可见性规则**:
- 如果不设置空时隐藏，则始终可见
- 如果设置空时隐藏，则需要有可显示或本地固定的线程

### 3. 状态管理

```javascript
// 状态保存方式判断
get saveStateToServer() {
    return this.serverStateKey && this.store.self?.isInternalUser;
}

// 获取打开状态
get open() {
    return this.saveStateToServer
        ? this.store.settings[this.serverStateKey]
        : this._openLocally;
}

// 设置打开状态
set open(value) {
    if (this.saveStateToServer) {
        this.store.settings[this.serverStateKey] = value;
        this.store.env.services.orm.call(
            "res.users.settings",
            "set_res_users_settings",
            [[this.store.settings.id]],
            {
                new_settings: {
                    [this.serverStateKey]: value,
                },
            }
        );
    } else {
        this._openLocally = value;
        browser.localStorage.setItem(this.localStateKey, value);
    }
}
```

**状态保存策略**:
- **服务器存储**: 内部用户且有服务器状态键时使用
- **本地存储**: 其他情况使用浏览器本地存储

### 4. 本地状态键计算

```javascript
localStateKey = Record.attr(null, {
    compute() {
        if (this.saveStateToServer) {
            return null;
        }
        return `discuss_sidebar_category_${this.id}_open`;
    },
    onUpdate() {
        if (this.localStateKey) {
            this._openLocally = JSON.parse(
                browser.localStorage.getItem(this.localStateKey) ?? "true"
            );
        }
    }
});
```

**本地状态管理**:
- 动态计算本地存储键名
- 自动从本地存储加载状态
- 默认为打开状态

## 使用场景

### 1. 频道分类

```javascript
// 创建频道分类
const channelsCategory = DiscussAppCategory.insert({
    id: "channels",
    name: "频道",
    icon: "fa-hashtag",
    sequence: 10,
    canView: true,
    canAdd: true,
    addTitle: "创建频道",
    addHotkey: "c",
    serverStateKey: "discuss_sidebar_category_channels_open"
});
```

### 2. 聊天分类

```javascript
// 创建聊天分类
const chatsCategory = DiscussAppCategory.insert({
    id: "chats",
    name: "聊天",
    icon: "fa-comments",
    sequence: 20,
    canView: true,
    canAdd: false,
    hideWhenEmpty: true,
    serverStateKey: "discuss_sidebar_category_chats_open"
});
```

### 3. 自定义分类

```javascript
// 创建自定义分类
const customCategory = DiscussAppCategory.insert({
    id: "favorites",
    name: "收藏夹",
    icon: "fa-star",
    sequence: 5,
    canView: true,
    canAdd: false,
    extraClass: "favorite-category",
    hideWhenEmpty: true
});
```

### 4. 动态分类管理

```javascript
// 动态添加分类
const addCategory = (categoryData) => {
    const category = DiscussAppCategory.insert(categoryData);
    
    // 设置初始状态
    if (category.localStateKey) {
        const savedState = browser.localStorage.getItem(category.localStateKey);
        category._openLocally = savedState ? JSON.parse(savedState) : true;
    }
    
    return category;
};

// 移除分类
const removeCategory = (categoryId) => {
    const category = DiscussAppCategory.get({ id: categoryId });
    if (category) {
        category.delete();
    }
};
```

## 状态持久化

### 1. 服务器存储

```javascript
// 服务器状态保存
const saveToServer = async (category, state) => {
    if (category.saveStateToServer) {
        await category.store.env.services.orm.call(
            "res.users.settings",
            "set_res_users_settings",
            [[category.store.settings.id]],
            {
                new_settings: {
                    [category.serverStateKey]: state,
                },
            }
        );
    }
};
```

### 2. 本地存储

```javascript
// 本地状态保存
const saveToLocal = (category, state) => {
    if (category.localStateKey) {
        browser.localStorage.setItem(
            category.localStateKey, 
            JSON.stringify(state)
        );
        category._openLocally = state;
    }
};
```

### 3. 状态同步

```javascript
// 状态同步处理
const syncCategoryState = (category) => {
    if (category.saveStateToServer) {
        // 从服务器设置同步
        const serverState = category.store.settings[category.serverStateKey];
        if (serverState !== undefined) {
            category._openLocally = serverState;
        }
    } else {
        // 从本地存储同步
        const localState = browser.localStorage.getItem(category.localStateKey);
        if (localState !== null) {
            category._openLocally = JSON.parse(localState);
        }
    }
};
```

## 线程管理

### 1. 线程添加

```javascript
// 向分类添加线程
const addThreadToCategory = (category, thread) => {
    thread.discussAppCategory = category;
    // 线程会自动添加到 category.threads 中
};
```

### 2. 线程移除

```javascript
// 从分类移除线程
const removeThreadFromCategory = (thread) => {
    thread.discussAppCategory = null;
    // 线程会自动从原分类中移除
};
```

### 3. 线程过滤

```javascript
// 获取可见线程
const getVisibleThreads = (category) => {
    return category.threads.filter(thread => 
        thread.displayToSelf || thread.isLocallyPinned
    );
};

// 获取未读线程
const getUnreadThreads = (category) => {
    return category.threads.filter(thread => 
        thread.message_unread_counter > 0
    );
};
```

## 分类操作

### 1. 展开/折叠

```javascript
// 切换分类展开状态
const toggleCategory = (category) => {
    category.open = !category.open;
};

// 展开分类
const expandCategory = (category) => {
    category.open = true;
};

// 折叠分类
const collapseCategory = (category) => {
    category.open = false;
};
```

### 2. 批量操作

```javascript
// 展开所有分类
const expandAllCategories = () => {
    DiscussAppCategory.records.forEach(category => {
        category.open = true;
    });
};

// 折叠所有分类
const collapseAllCategories = () => {
    DiscussAppCategory.records.forEach(category => {
        category.open = false;
    });
};
```

### 3. 分类重排序

```javascript
// 重新排序分类
const reorderCategories = (categoryIds) => {
    categoryIds.forEach((id, index) => {
        const category = DiscussAppCategory.get({ id });
        if (category) {
            category.sequence = (index + 1) * 10;
        }
    });
};
```

## 事件处理

### 1. 状态变化监听

```javascript
// 监听分类状态变化
const watchCategoryState = (category, callback) => {
    const originalOpen = category.open;
    
    Object.defineProperty(category, 'open', {
        get() {
            return originalOpen;
        },
        set(value) {
            const oldValue = originalOpen;
            originalOpen = value;
            callback(category, value, oldValue);
        }
    });
};
```

### 2. 线程变化监听

```javascript
// 监听线程变化
const watchThreadChanges = (category, callback) => {
    const observer = new MutationObserver(() => {
        callback(category, category.threads);
    });
    
    // 监听线程列表变化
    observer.observe(category.threads, {
        childList: true,
        subtree: true
    });
};
```

## 性能优化

### 1. 懒加载

```javascript
// 懒加载线程
const lazyLoadThreads = (category) => {
    if (!category.open) {
        return; // 折叠状态不加载
    }
    
    // 只加载可见线程
    const visibleThreads = category.threads.filter(thread => 
        thread.displayToSelf
    );
    
    return visibleThreads;
};
```

### 2. 缓存优化

```javascript
// 缓存分类状态
const categoryStateCache = new Map();

const getCachedCategoryState = (categoryId) => {
    if (categoryStateCache.has(categoryId)) {
        return categoryStateCache.get(categoryId);
    }
    
    const category = DiscussAppCategory.get({ id: categoryId });
    const state = {
        open: category.open,
        threadCount: category.threads.length,
        visibleThreadCount: category.threads.filter(t => t.displayToSelf).length
    };
    
    categoryStateCache.set(categoryId, state);
    return state;
};
```

## 设计模式

### 1. 模型模式 (Model Pattern)
- 封装分类的数据和行为
- 提供统一的数据访问接口

### 2. 策略模式 (Strategy Pattern)
- 不同分类采用不同的排序策略
- 灵活的状态保存策略

### 3. 观察者模式 (Observer Pattern)
- 监听状态变化
- 响应线程变化

## 注意事项

1. **状态同步**: 确保本地和服务器状态的一致性
2. **性能考虑**: 避免频繁的状态保存操作
3. **用户体验**: 提供流畅的展开/折叠动画
4. **数据一致性**: 保持分类和线程的关联关系

## 扩展建议

1. **自定义排序**: 支持用户自定义线程排序规则
2. **分类模板**: 提供预定义的分类模板
3. **权限控制**: 更细粒度的权限控制
4. **分类搜索**: 支持在分类中搜索线程
5. **拖拽排序**: 支持拖拽调整分类和线程顺序

该模型为讨论应用提供了灵活的分类管理机制，支持多种状态保存方式和自定义排序逻辑，是构建可扩展侧边栏的重要基础。

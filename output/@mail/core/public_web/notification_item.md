# Notification Item - 通知项目组件

## 概述

`notification_item.js` 实现了 Odoo 邮件系统中的通知项目组件，用于在消息菜单中显示单个通知项目。该组件支持滑动操作、悬停效果、时间显示、未读计数等功能，提供了丰富的交互体验，包括标记已读按钮、自定义图标、状态指示等特性，是通知系统的核心显示组件。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/notification_item.js`
- **行数**: 63
- **模块**: `@mail/core/public_web/notification_item`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/common/im_status'         // 即时消息状态
'@web/core/action_swiper/action_swiper' // 滑动操作组件

// 工具和钩子依赖
'@mail/utils/common/dates'            // 日期工具
'@mail/utils/common/hooks'            // 邮件工具钩子
'@odoo/owl'                          // OWL 框架
'@web/core/utils/hooks'              // Web 核心钩子
```

## 组件定义

### NotificationItem 类

```javascript
class NotificationItem extends Component {
    static components = { ActionSwiper, ImStatus };
    static props = [
        "body?",                    // 通知内容
        "counter?",                 // 未读计数
        "datetime?",                // 时间戳
        "first?",                   // 是否为第一个项目
        "hasMarkAsReadButton?",     // 是否有标记已读按钮
        "iconSrc?",                 // 图标源
        "muted?",                   // 是否静音
        "onClick",                  // 点击回调
        "onSwipeLeft?",             // 左滑回调
        "onSwipeRight?",            // 右滑回调
        "slots?",                   // 插槽内容
        "isActive?",                // 是否激活
    ];
    static defaultProps = {
        counter: 0,
        muted: 0,
    };
    static template = "mail.NotificationItem";
}
```

## Props 配置

### Props 详细说明

- **`body`** (可选):
  - 类型: 字符串
  - 用途: 通知的主要内容文本
  - 功能: 显示通知的详细信息

- **`counter`** (可选):
  - 类型: 数字
  - 默认值: 0
  - 用途: 未读消息计数
  - 功能: 显示未读消息数量徽章

- **`datetime`** (可选):
  - 类型: DateTime 对象
  - 用途: 通知的时间戳
  - 功能: 显示格式化的时间信息

- **`hasMarkAsReadButton`** (可选):
  - 类型: 布尔值
  - 用途: 控制是否显示标记已读按钮
  - 功能: 提供快速标记已读的操作

- **`onClick`** (必需):
  - 类型: 函数
  - 用途: 点击事件回调
  - 参数: 是否点击的是标记已读按钮

- **`onSwipeLeft/onSwipeRight`** (可选):
  - 类型: 函数
  - 用途: 滑动操作回调
  - 功能: 支持移动端滑动手势

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 日期工具
    this.isToday = isToday;
    this.DateTime = DateTime;
    
    // 服务和状态
    this.ui = useState(useService("ui"));
    
    // DOM引用
    this.markAsReadRef = useRef("markAsRead");
    
    // 悬停效果
    this.rootHover = useHover("root");
}
```

**初始化内容**:
- 日期处理工具
- UI服务状态
- 标记已读按钮引用
- 悬停状态管理

## 核心功能

### 1. 时间显示

```javascript
get dateText() {
    if (isToday(this.props.datetime)) {
        return this.props.datetime?.toLocaleString(DateTime.TIME_SIMPLE);
    }
    return this.props.datetime?.toLocaleString(DateTime.DATE_MED);
}
```

**时间格式化逻辑**:
- **今天的消息**: 显示简单时间格式（如 "14:30"）
- **其他日期**: 显示中等日期格式（如 "Jan 15"）

### 2. 点击处理

```javascript
onClick(ev) {
    this.props.onClick(ev.target === this.markAsReadRef.el);
}
```

**点击逻辑**:
- 检查点击目标是否为标记已读按钮
- 将结果传递给父组件的点击回调
- 支持不同的点击行为

## 使用场景

### 1. 消息通知项目

```javascript
// 在消息菜单中显示通知项目
const MessageNotificationItem = ({ thread }) => {
    const handleClick = (isMarkAsRead) => {
        if (isMarkAsRead) {
            thread.markAllMessagesAsRead();
        } else {
            openThread(thread);
        }
    };
    
    return (
        <NotificationItem
            body={thread.lastMessage?.preview}
            counter={thread.message_unread_counter}
            datetime={thread.lastMessage?.date}
            hasMarkAsReadButton={true}
            iconSrc={thread.correspondent?.avatarUrl}
            onClick={handleClick}
            onSwipeLeft={() => thread.markAllMessagesAsRead()}
            onSwipeRight={() => openThread(thread)}
        />
    );
};
```

### 2. 系统通知项目

```javascript
// 系统通知项目
const SystemNotificationItem = ({ notification }) => {
    return (
        <NotificationItem
            body={notification.message}
            datetime={notification.createdAt}
            iconSrc="/web/static/img/notification-icon.png"
            onClick={() => handleSystemNotification(notification)}
            muted={notification.priority === 'low'}
        />
    );
};
```

### 3. 活动通知项目

```javascript
// 活动通知项目
const ActivityNotificationItem = ({ activity }) => {
    return (
        <NotificationItem
            body={`${activity.user.name} ${activity.action} ${activity.target}`}
            datetime={activity.timestamp}
            iconSrc={activity.user.avatarUrl}
            onClick={() => viewActivity(activity)}
            counter={activity.isUnread ? 1 : 0}
        />
    );
};
```

### 4. 移动端优化项目

```javascript
// 移动端优化的通知项目
const MobileNotificationItem = ({ notification, onMarkRead, onOpen }) => {
    const ui = useService("ui");
    
    return (
        <NotificationItem
            body={notification.body}
            datetime={notification.datetime}
            iconSrc={notification.iconSrc}
            onClick={(isMarkAsRead) => {
                if (isMarkAsRead) {
                    onMarkRead(notification);
                } else {
                    onOpen(notification);
                }
            }}
            onSwipeLeft={ui.isSmall ? () => onMarkRead(notification) : undefined}
            onSwipeRight={ui.isSmall ? () => onOpen(notification) : undefined}
            hasMarkAsReadButton={!ui.isSmall}
        />
    );
};
```

## 交互功能

### 1. 滑动操作

```javascript
// 滑动操作配置
const swipeActions = {
    left: {
        icon: 'fa-check',
        color: 'success',
        action: () => markAsRead(),
        label: '标记已读'
    },
    right: {
        icon: 'fa-eye',
        color: 'primary', 
        action: () => openNotification(),
        label: '查看'
    }
};

// 滑动处理
const handleSwipe = (direction, notification) => {
    const action = swipeActions[direction];
    if (action) {
        action.action(notification);
        showSwipeFeedback(action.label);
    }
};
```

### 2. 悬停效果

```javascript
// 悬停状态管理
const useNotificationHover = () => {
    const [isHovered, setIsHovered] = useState(false);
    
    const hoverProps = {
        onMouseEnter: () => setIsHovered(true),
        onMouseLeave: () => setIsHovered(false)
    };
    
    return { isHovered, hoverProps };
};

// 悬停时显示额外操作
const HoverActions = ({ isHovered, notification }) => {
    if (!isHovered) return null;
    
    return (
        <div class="hover-actions">
            <button onClick={() => markAsRead(notification)}>
                <i class="fa fa-check"></i>
            </button>
            <button onClick={() => deleteNotification(notification)}>
                <i class="fa fa-trash"></i>
            </button>
        </div>
    );
};
```

### 3. 键盘导航

```javascript
// 键盘导航支持
const handleKeyDown = (event, notification) => {
    switch (event.key) {
        case 'Enter':
        case ' ':
            event.preventDefault();
            openNotification(notification);
            break;
        case 'Delete':
        case 'Backspace':
            event.preventDefault();
            markAsRead(notification);
            break;
        case 'ArrowUp':
        case 'ArrowDown':
            event.preventDefault();
            navigateToNextNotification(event.key === 'ArrowDown' ? 1 : -1);
            break;
    }
};
```

## 状态指示

### 1. 未读状态

```javascript
// 未读状态显示
const UnreadIndicator = ({ counter, muted }) => {
    if (counter === 0) return null;
    
    return (
        <span class={`unread-badge ${muted ? 'muted' : ''}`}>
            {counter > 99 ? '99+' : counter}
        </span>
    );
};
```

### 2. 优先级指示

```javascript
// 优先级指示器
const PriorityIndicator = ({ priority }) => {
    const priorityConfig = {
        high: { icon: 'fa-exclamation-triangle', color: 'danger' },
        medium: { icon: 'fa-exclamation-circle', color: 'warning' },
        low: { icon: 'fa-info-circle', color: 'info' }
    };
    
    const config = priorityConfig[priority];
    if (!config) return null;
    
    return (
        <i class={`${config.icon} text-${config.color}`}></i>
    );
};
```

### 3. 状态图标

```javascript
// 状态图标组件
const StatusIcon = ({ status, iconSrc }) => {
    if (iconSrc) {
        return <img src={iconSrc} alt="通知图标" class="notification-icon" />;
    }
    
    const statusIcons = {
        message: 'fa-envelope',
        mention: 'fa-at',
        system: 'fa-cog',
        activity: 'fa-bell'
    };
    
    const iconClass = statusIcons[status] || 'fa-bell';
    
    return <i class={`fa ${iconClass} notification-icon`}></i>;
};
```

## 样式和主题

### 1. 基础样式

```css
.notification-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
        background-color: #f8f9fa;
    }
    
    &.active {
        background-color: #e3f2fd;
        border-left: 3px solid #2196f3;
    }
    
    &.muted {
        opacity: 0.7;
    }
    
    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
    }
    
    .notification-content {
        flex: 1;
        min-width: 0;
        
        .notification-body {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .notification-time {
            font-size: 12px;
            color: #666;
        }
    }
    
    .notification-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .unread-badge {
            background: #f44336;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 11px;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
            
            &.muted {
                background: #999;
            }
        }
        
        .mark-read-btn {
            opacity: 0;
            transition: opacity 0.2s ease;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 4px;
            
            &:hover {
                color: #333;
            }
        }
    }
    
    &:hover .mark-read-btn {
        opacity: 1;
    }
}
```

### 2. 移动端样式

```css
@media (max-width: 768px) {
    .notification-item {
        padding: 16px;
        
        .notification-icon {
            width: 48px;
            height: 48px;
        }
        
        .notification-content {
            .notification-body {
                font-size: 16px;
                white-space: normal;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }
        }
        
        .mark-read-btn {
            opacity: 1; /* 移动端始终显示 */
        }
    }
}
```

## 可访问性

### 1. ARIA 标签

```javascript
// 可访问性属性
const getAccessibilityProps = (notification) => {
    return {
        'role': 'button',
        'tabindex': '0',
        'aria-label': `通知: ${notification.body}`,
        'aria-describedby': `notification-time-${notification.id}`,
        'aria-pressed': notification.isActive ? 'true' : 'false'
    };
};
```

### 2. 屏幕阅读器支持

```javascript
// 屏幕阅读器文本
const getScreenReaderText = (notification) => {
    let text = notification.body;
    
    if (notification.counter > 0) {
        text += `, ${notification.counter} 条未读消息`;
    }
    
    if (notification.datetime) {
        text += `, 时间: ${formatTimeForScreenReader(notification.datetime)}`;
    }
    
    return text;
};
```

## 性能优化

### 1. 虚拟化

```javascript
// 虚拟化通知列表
const VirtualizedNotificationList = ({ notifications }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    
    const visibleNotifications = notifications.slice(
        visibleRange.start, 
        visibleRange.end
    );
    
    return (
        <div class="virtualized-notification-list">
            {visibleNotifications.map(notification => (
                <NotificationItem 
                    key={notification.id} 
                    {...notification} 
                />
            ))}
        </div>
    );
};
```

### 2. 图片懒加载

```javascript
// 图片懒加载
const LazyImage = ({ src, alt, className }) => {
    const [loaded, setLoaded] = useState(false);
    const [inView, setInView] = useState(false);
    
    useEffect(() => {
        const observer = new IntersectionObserver(([entry]) => {
            if (entry.isIntersecting) {
                setInView(true);
                observer.disconnect();
            }
        });
        
        if (imgRef.current) {
            observer.observe(imgRef.current);
        }
        
        return () => observer.disconnect();
    }, []);
    
    return (
        <div className={className}>
            {inView && (
                <img 
                    src={src} 
                    alt={alt}
                    onLoad={() => setLoaded(true)}
                    style={{ opacity: loaded ? 1 : 0 }}
                />
            )}
        </div>
    );
};
```

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的通知项目组件
- 统一的通知显示接口

### 2. 策略模式 (Strategy Pattern)
- 不同类型通知的不同显示策略
- 可配置的交互行为

### 3. 观察者模式 (Observer Pattern)
- 监听用户交互事件
- 响应状态变化

## 注意事项

1. **性能考虑**: 大量通知时的渲染性能
2. **用户体验**: 提供清晰的交互反馈
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **移动端优化**: 适配触摸交互

## 扩展建议

1. **自定义模板**: 支持自定义通知项目模板
2. **动画效果**: 添加进入和退出动画
3. **批量操作**: 支持批量选择和操作
4. **通知分组**: 支持按类型分组显示
5. **实时更新**: 支持实时的通知状态更新

该组件为通知系统提供了灵活的显示界面，支持丰富的交互功能和良好的用户体验。

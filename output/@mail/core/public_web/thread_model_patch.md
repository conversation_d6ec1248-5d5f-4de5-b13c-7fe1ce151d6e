# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了 Odoo 邮件系统中线程模型在公共Web环境下的补丁，用于扩展线程模型的功能以支持讨论应用。该补丁添加了讨论应用分类关联、消息通知处理、讨论线程设置、URL管理、线程打开和取消固定等功能，确保线程模型能够在Web环境下正确支持讨论功能和用户交互，是公共Web模块的重要数据层补丁。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/thread_model_patch.js`
- **行数**: 114
- **模块**: `@mail/core/public_web/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'           // 补丁工具
'@mail/core/common/thread_model'  // 线程模型基类
'@mail/core/common/record'        // 记录基类
'@web/core/browser/router'        // 路由器
```

## 补丁实现

### Thread 原型补丁

```javascript
patch(Thread.prototype, {
    setup() {
        super.setup(...arguments);
        this.discussAppCategory = Record.one("DiscussAppCategory", {
            compute() {
                return this._computeDiscussAppCategory();
            },
        });
    },
    
    // 其他方法...
});
```

## 核心功能

### 1. 讨论应用分类计算

```javascript
_computeDiscussAppCategory() {
    if (["group", "chat"].includes(this.channel_type)) {
        return this.store.discuss.chats;
    }
    if (this.channel_type === "channel" && !this.parent_channel_id) {
        return this.store.discuss.channels;
    }
}
```

**分类逻辑**:
- **群组和聊天**: 归属于聊天分类
- **频道**: 顶级频道归属于频道分类
- **子频道**: 不归属于任何分类

### 2. 消息通知处理

```javascript
notifyMessageToUser(message) {
    if (this.isCorrespondentOdooBot) {
        return;
    }
    
    const channel_notifications =
        this.custom_notifications || this.store.settings.channel_notifications;
        
    if (
        !this.mute_until_dt &&
        !this.store.settings.mute_until_dt &&
        (this.channel_type !== "channel" ||
            (this.channel_type === "channel" &&
                (channel_notifications === "all" ||
                    (channel_notifications === "mentions" &&
                        message.recipients?.includes(this.store.self)))))
    ) {
        const chatWindow = this.store.ChatWindow.get({ thread: this });
        if (!chatWindow) {
            this.store.ChatWindow.insert({ thread: this }).fold();
        }
        this.store.env.services["mail.out_of_focus"].notify(message, this);
    }
}
```

**通知条件**:
- 不是OdooBot对话
- 线程未被静音
- 全局未被静音
- 满足频道通知设置（全部消息或仅提及）

**通知行为**:
- 创建或获取聊天窗口
- 折叠聊天窗口
- 触发失焦通知

### 3. 设置为讨论线程

```javascript
setAsDiscussThread(pushState) {
    if (pushState === undefined) {
        pushState = this.notEq(this.store.discuss.thread);
    }
    
    this.store.discuss.thread = this;
    this.store.discuss.activeTab =
        !this.store.env.services.ui.isSmall || this.model === "mail.box"
            ? "main"
            : ["chat", "group"].includes(this.channel_type)
            ? "chat"
            : "channel";
            
    if (pushState) {
        this.setActiveURL();
    }
    
    if (this.store.env.services.ui.isSmall && this.model !== "mail.box") {
        this.open();
    }
}
```

**设置逻辑**:
- 设置当前线程为讨论线程
- 根据屏幕尺寸和线程类型设置活跃标签页
- 可选择是否推送URL状态
- 小屏幕时自动打开线程

### 4. URL管理

```javascript
setActiveURL() {
    const activeId =
        typeof this.id === "string" ? `mail.box_${this.id}` : `discuss.channel_${this.id}`;
    router.pushState({ active_id: activeId });
}
```

**URL格式**:
- 邮箱: `mail.box_{id}`
- 频道: `discuss.channel_{id}`

### 5. 线程打开

```javascript
open(options) {
    if (this.store.env.services.ui.isSmall) {
        this.openChatWindow(options);
        return;
    }
    this.setAsDiscussThread();
}
```

**打开逻辑**:
- 小屏幕: 打开聊天窗口
- 大屏幕: 设置为讨论线程

### 6. 取消固定

```javascript
async unpin() {
    this.isLocallyPinned = false;
    if (this.eq(this.store.discuss.thread)) {
        router.replaceState({ active_id: undefined });
    }
    if (this.model === "discuss.channel" && this.is_pinned) {
        return this.store.env.services.orm.silent.call(
            "discuss.channel",
            "channel_pin",
            [this.id],
            { pinned: false }
        );
    }
}
```

**取消固定流程**:
1. 设置本地固定状态为false
2. 如果是当前讨论线程，清除URL状态
3. 如果是服务器固定的频道，调用服务器API

## 使用场景

### 1. 线程分类管理

```javascript
// 获取线程所属分类
const getThreadCategory = (thread) => {
    return thread.discussAppCategory;
};

// 按分类组织线程
const organizeThreadsByCategory = (threads) => {
    const categories = {};
    
    threads.forEach(thread => {
        const category = thread.discussAppCategory;
        if (category) {
            if (!categories[category.id]) {
                categories[category.id] = {
                    category: category,
                    threads: []
                };
            }
            categories[category.id].threads.push(thread);
        }
    });
    
    return categories;
};
```

### 2. 智能通知处理

```javascript
// 检查是否应该通知用户
const shouldNotifyUser = (thread, message) => {
    // OdooBot消息不通知
    if (thread.isCorrespondentOdooBot) {
        return false;
    }
    
    // 检查静音状态
    if (thread.mute_until_dt || thread.store.settings.mute_until_dt) {
        return false;
    }
    
    // 检查通知设置
    const notifications = thread.custom_notifications || 
                         thread.store.settings.channel_notifications;
    
    if (thread.channel_type === "channel") {
        if (notifications === "all") {
            return true;
        }
        if (notifications === "mentions") {
            return message.recipients?.includes(thread.store.self);
        }
        return false;
    }
    
    return true; // 非频道类型默认通知
};
```

### 3. 响应式线程打开

```javascript
// 响应式线程打开策略
const openThreadResponsively = (thread, options = {}) => {
    const ui = thread.store.env.services.ui;
    
    if (ui.isSmall) {
        // 移动端策略
        if (thread.model === "mail.box") {
            // 邮箱在移动端直接设置为讨论线程
            thread.setAsDiscussThread();
        } else {
            // 其他类型打开聊天窗口
            thread.openChatWindow(options);
        }
    } else {
        // 桌面端策略
        if (options.forceWindow) {
            // 强制打开窗口模式
            thread.openChatWindow(options);
        } else {
            // 默认设置为讨论线程
            thread.setAsDiscussThread();
        }
    }
};
```

### 4. URL状态管理

```javascript
// URL状态同步
const syncURLState = (thread) => {
    // 设置URL状态
    thread.setActiveURL();
    
    // 监听浏览器后退/前进
    window.addEventListener('popstate', (event) => {
        const state = event.state;
        if (state && state.active_id) {
            const [model, id] = parseActiveId(state.active_id);
            const targetThread = store.Thread.get({ model, id });
            if (targetThread) {
                targetThread.setAsDiscussThread(false); // 不推送状态避免循环
            }
        }
    });
};

// 解析活跃ID
const parseActiveId = (activeId) => {
    if (activeId.startsWith('mail.box_')) {
        return ['mail.box', activeId.replace('mail.box_', '')];
    }
    if (activeId.startsWith('discuss.channel_')) {
        return ['discuss.channel', parseInt(activeId.replace('discuss.channel_', ''))];
    }
    return [null, null];
};
```

## 通知策略

### 1. 通知类型处理

```javascript
// 不同通知类型的处理
const handleNotificationTypes = (thread, message) => {
    const notificationType = thread.custom_notifications || 
                           thread.store.settings.channel_notifications;
    
    switch (notificationType) {
        case 'all':
            // 所有消息都通知
            return {
                shouldNotify: true,
                playSound: true,
                showDesktop: true
            };
            
        case 'mentions':
            // 仅提及时通知
            const isMentioned = message.recipients?.includes(thread.store.self);
            return {
                shouldNotify: isMentioned,
                playSound: isMentioned,
                showDesktop: isMentioned
            };
            
        case 'nothing':
            // 不通知但更新计数器
            return {
                shouldNotify: false,
                playSound: false,
                showDesktop: false,
                updateCounter: true
            };
            
        default:
            return {
                shouldNotify: false,
                playSound: false,
                showDesktop: false
            };
    }
};
```

### 2. 静音状态管理

```javascript
// 静音状态检查
const checkMuteStatus = (thread) => {
    const now = new Date();
    
    // 检查线程级别静音
    if (thread.mute_until_dt && new Date(thread.mute_until_dt) > now) {
        return {
            isMuted: true,
            reason: 'thread_muted',
            until: thread.mute_until_dt
        };
    }
    
    // 检查全局静音
    if (thread.store.settings.mute_until_dt && 
        new Date(thread.store.settings.mute_until_dt) > now) {
        return {
            isMuted: true,
            reason: 'global_muted',
            until: thread.store.settings.mute_until_dt
        };
    }
    
    return {
        isMuted: false,
        reason: null,
        until: null
    };
};
```

## 标签页管理

### 1. 活跃标签页计算

```javascript
// 计算活跃标签页
const computeActiveTab = (thread, ui) => {
    // 大屏幕或邮箱始终使用main标签页
    if (!ui.isSmall || thread.model === "mail.box") {
        return "main";
    }
    
    // 小屏幕根据频道类型决定
    if (["chat", "group"].includes(thread.channel_type)) {
        return "chat";
    }
    
    if (thread.channel_type === "channel") {
        return "channel";
    }
    
    return "main"; // 默认
};
```

### 2. 标签页切换

```javascript
// 标签页切换处理
const handleTabSwitch = (thread, newTab) => {
    const oldTab = thread.store.discuss.activeTab;
    
    if (oldTab !== newTab) {
        // 保存旧标签页状态
        saveTabState(oldTab, thread);
        
        // 切换到新标签页
        thread.store.discuss.activeTab = newTab;
        
        // 恢复新标签页状态
        restoreTabState(newTab, thread);
        
        // 触发标签页切换事件
        thread.store.env.bus.trigger('discuss:tab_changed', {
            from: oldTab,
            to: newTab,
            thread: thread
        });
    }
};
```

## 聊天窗口集成

### 1. 聊天窗口创建

```javascript
// 创建聊天窗口
const createChatWindow = (thread) => {
    let chatWindow = thread.store.ChatWindow.get({ thread: thread });
    
    if (!chatWindow) {
        chatWindow = thread.store.ChatWindow.insert({ 
            thread: thread,
            isOpen: true,
            isFolded: false
        });
    }
    
    return chatWindow;
};
```

### 2. 窗口状态管理

```javascript
// 管理聊天窗口状态
const manageChatWindowState = (thread, message) => {
    const chatWindow = createChatWindow(thread);
    
    // 根据消息类型决定窗口行为
    if (message.isImportant) {
        chatWindow.unfold(); // 展开窗口
        chatWindow.focus();  // 聚焦窗口
    } else {
        chatWindow.fold();   // 折叠窗口
    }
    
    // 更新窗口标题
    chatWindow.updateTitle(thread.displayName);
    
    // 显示未读计数
    if (thread.message_unread_counter > 0) {
        chatWindow.showUnreadBadge(thread.message_unread_counter);
    }
};
```

## 性能优化

### 1. 分类计算缓存

```javascript
// 缓存分类计算结果
const categoryCache = new WeakMap();

const getCachedCategory = (thread) => {
    if (categoryCache.has(thread)) {
        return categoryCache.get(thread);
    }
    
    const category = thread._computeDiscussAppCategory();
    categoryCache.set(thread, category);
    
    return category;
};
```

### 2. 通知防抖

```javascript
// 防抖通知处理
const debouncedNotify = debounce((thread, message) => {
    thread.notifyMessageToUser(message);
}, 100);
```

### 3. URL更新节流

```javascript
// 节流URL更新
const throttledSetActiveURL = throttle((thread) => {
    thread.setActiveURL();
}, 200);
```

## 错误处理

### 1. 安全的分类计算

```javascript
// 安全的分类计算
const safeComputeCategory = (thread) => {
    try {
        return thread._computeDiscussAppCategory();
    } catch (error) {
        console.error('分类计算失败:', error);
        return null; // 返回null表示无分类
    }
};
```

### 2. 通知错误处理

```javascript
// 通知错误处理
const safeNotifyUser = (thread, message) => {
    try {
        thread.notifyMessageToUser(message);
    } catch (error) {
        console.error('用户通知失败:', error);
        // 记录错误但不中断流程
        reportNotificationError(error, thread, message);
    }
};
```

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁扩展线程模型功能
- 不修改原始模型结构

### 2. 策略模式 (Strategy Pattern)
- 不同屏幕尺寸的不同打开策略
- 不同通知类型的不同处理策略

### 3. 观察者模式 (Observer Pattern)
- 监听消息事件
- 响应状态变化

## 注意事项

1. **状态同步**: 确保UI状态与数据状态的一致性
2. **性能考虑**: 避免频繁的分类重计算
3. **用户体验**: 提供流畅的线程切换体验
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **智能分类**: 支持基于AI的智能线程分类
2. **自定义通知**: 支持更细粒度的通知自定义
3. **批量操作**: 支持批量线程操作
4. **状态持久化**: 支持线程状态的持久化
5. **性能监控**: 添加性能监控和优化

该补丁为线程模型在公共Web环境下提供了重要的功能扩展，确保了讨论应用的正常运行和良好的用户体验。

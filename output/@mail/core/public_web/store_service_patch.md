# Store Service Patch - 存储服务补丁

## 概述

`store_service_patch.js` 实现了 Odoo 邮件系统中存储服务在公共Web环境下的补丁，用于扩展存储服务的功能以支持讨论应用。该补丁添加了讨论应用状态管理、消息通知处理、侧边栏计数器、响应式布局适配等功能，确保存储服务能够在Web环境下正确支持讨论功能，是公共Web模块的重要基础设施补丁。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/store_service_patch.js`
- **行数**: 67
- **模块**: `@mail/core/public_web/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/store_service'  // 存储服务
'@mail/core/common/record'         // 记录基类
'@web/core/browser/router'         // 路由器
'@web/core/utils/patch'            // 补丁工具
```

## 补丁实现

### Store 原型补丁

```javascript
patch(Store.prototype, {
    setup() {
        super.setup(...arguments);
        this.discuss = Record.one("DiscussApp");
        this.action_discuss_id;
    },
    
    onStarted() {
        super.onStarted(...arguments);
        this.discuss = { activeTab: "main" };
        // 事件监听器设置
    },
    
    getDiscussSidebarCategoryCounter(categoryId) {
        // 侧边栏分类计数器逻辑
    }
});
```

### storeService 补丁

```javascript
patch(storeService, {
    start(env, services) {
        const store = super.start(...arguments);
        // 讨论应用激活状态设置
        // UI响应式监听器设置
        return store;
    }
});
```

## 核心功能

### 1. 讨论应用初始化

```javascript
setup() {
    super.setup(...arguments);
    this.discuss = Record.one("DiscussApp");
    this.action_discuss_id;
}
```

**初始化内容**:
- 创建讨论应用记录关联
- 设置讨论动作ID属性

### 2. 启动时配置

```javascript
onStarted() {
    super.onStarted(...arguments);
    this.discuss = { activeTab: "main" };
    
    this.env.bus.addEventListener(
        "discuss.channel/new_message",
        ({ detail: { channel, message } }) => {
            if (this.env.services.ui.isSmall || message.isSelfAuthored) {
                return;
            }
            if (channel.isCorrespondentOdooBot && this.odoobotOnboarding) {
                this.odoobotOnboarding = false;
                return;
            }
            channel.notifyMessageToUser(message);
        }
    );
}
```

**启动配置**:
- 设置默认活跃标签页为"main"
- 监听新消息事件
- 处理消息通知逻辑
- 管理OdooBot引导流程

### 3. 消息通知处理

```javascript
// 新消息事件处理逻辑
if (this.env.services.ui.isSmall || message.isSelfAuthored) {
    return; // 小屏幕或自己发送的消息不处理
}

if (channel.isCorrespondentOdooBot && this.odoobotOnboarding) {
    this.odoobotOnboarding = false;
    return; // 取消OdooBot引导的自动打开
}

channel.notifyMessageToUser(message);
```

**通知逻辑**:
- **小屏幕过滤**: 移动设备不显示通知
- **自发消息过滤**: 自己发送的消息不通知
- **OdooBot处理**: 特殊处理OdooBot引导流程
- **用户通知**: 向用户发送消息通知

### 4. 侧边栏计数器

```javascript
getDiscussSidebarCategoryCounter(categoryId) {
    return this.DiscussAppCategory.get({ id: categoryId }).threads.reduce((acc, channel) => {
        if (categoryId === "channels") {
            return channel.message_needaction_counter > 0 ? acc + 1 : acc;
        } else {
            return channel.selfMember?.message_unread_counter > 0 ? acc + 1 : acc;
        }
    }, 0);
}
```

**计数逻辑**:
- **频道分类**: 统计有需要操作消息的频道数量
- **其他分类**: 统计有未读消息的频道数量
- **累计计算**: 返回符合条件的频道总数

### 5. 讨论应用激活状态

```javascript
const discussActionIds = ["mail.action_discuss", "discuss"];
if (store.action_discuss_id) {
    discussActionIds.push(store.action_discuss_id);
}
store.discuss.isActive ||= discussActionIds.includes(router.current.action);
```

**激活逻辑**:
- 定义讨论相关的动作ID列表
- 支持自定义讨论动作ID
- 根据当前路由动作判断是否激活

### 6. 响应式布局适配

```javascript
services.ui.bus.addEventListener("resize", () => {
    store.discuss.activeTab = "main";
    if (services.ui.isSmall && store.discuss.thread?.channel_type) {
        store.discuss.activeTab = store.discuss.thread.channel_type;
    }
});
```

**响应式逻辑**:
- 监听窗口大小变化事件
- 默认重置为主标签页
- 小屏幕时根据线程类型设置标签页

## 使用场景

### 1. 讨论应用状态管理

```javascript
// 检查讨论应用是否激活
const isDiscussActive = () => {
    return store.discuss.isActive;
};

// 设置讨论应用状态
const setDiscussActive = (active) => {
    store.discuss.isActive = active;
    
    if (active) {
        // 激活时的处理逻辑
        initializeDiscussApp();
    } else {
        // 停用时的处理逻辑
        cleanupDiscussApp();
    }
};
```

### 2. 消息通知管理

```javascript
// 自定义消息通知处理
const customMessageHandler = (channel, message) => {
    // 检查是否应该显示通知
    if (shouldShowNotification(channel, message)) {
        // 显示桌面通知
        showDesktopNotification(message);
        
        // 播放提示音
        playNotificationSound();
        
        // 更新未读计数
        updateUnreadCounter(channel);
    }
};

// 注册自定义处理器
store.env.bus.addEventListener("discuss.channel/new_message", customMessageHandler);
```

### 3. 侧边栏计数器使用

```javascript
// 获取分类计数器
const getChannelCounter = () => {
    return store.getDiscussSidebarCategoryCounter("channels");
};

const getChatCounter = () => {
    return store.getDiscussSidebarCategoryCounter("chats");
};

// 显示计数器徽章
const CategoryBadge = ({ categoryId }) => {
    const counter = store.getDiscussSidebarCategoryCounter(categoryId);
    
    if (counter === 0) return null;
    
    return (
        <span class="category-badge">
            {counter > 99 ? '99+' : counter}
        </span>
    );
};
```

### 4. 响应式标签页管理

```javascript
// 响应式标签页切换
const handleResponsiveTabSwitch = () => {
    const ui = store.env.services.ui;
    
    if (ui.isSmall) {
        // 移动端逻辑
        if (store.discuss.thread) {
            store.discuss.activeTab = store.discuss.thread.channel_type;
        }
    } else {
        // 桌面端逻辑
        store.discuss.activeTab = "main";
    }
};

// 监听屏幕尺寸变化
window.addEventListener('resize', handleResponsiveTabSwitch);
```

## 补丁扩展

### 1. 自定义计数器

```javascript
// 扩展计数器功能
patch(Store.prototype, {
    getCustomCategoryCounter(categoryId, filterFn) {
        const category = this.DiscussAppCategory.get({ id: categoryId });
        if (!category) return 0;
        
        return category.threads.filter(filterFn).length;
    },
    
    // 获取高优先级消息计数
    getHighPriorityCounter(categoryId) {
        return this.getCustomCategoryCounter(categoryId, thread => 
            thread.messages.some(msg => msg.priority === 'high' && !msg.isRead)
        );
    },
    
    // 获取提及计数
    getMentionCounter(categoryId) {
        return this.getCustomCategoryCounter(categoryId, thread =>
            thread.message_needaction_counter > 0
        );
    }
});
```

### 2. 消息过滤增强

```javascript
// 增强消息通知过滤
patch(Store.prototype, {
    shouldNotifyMessage(channel, message) {
        // 基础过滤条件
        if (this.env.services.ui.isSmall || message.isSelfAuthored) {
            return false;
        }
        
        // 用户偏好过滤
        if (this.settings.muteAllNotifications) {
            return false;
        }
        
        // 频道特定过滤
        if (channel.isMuted) {
            return false;
        }
        
        // 时间过滤（工作时间外）
        if (this.settings.onlyWorkHours && !isWorkingHours()) {
            return false;
        }
        
        return true;
    }
});
```

### 3. 状态持久化

```javascript
// 状态持久化补丁
patch(Store.prototype, {
    saveDiscussState() {
        const state = {
            activeTab: this.discuss.activeTab,
            currentThreadId: this.discuss.thread?.id,
            isActive: this.discuss.isActive
        };
        
        localStorage.setItem('discuss_state', JSON.stringify(state));
    },
    
    restoreDiscussState() {
        const savedState = localStorage.getItem('discuss_state');
        if (savedState) {
            try {
                const state = JSON.parse(savedState);
                this.discuss.activeTab = state.activeTab || 'main';
                this.discuss.isActive = state.isActive || false;
                
                if (state.currentThreadId) {
                    const thread = this.Thread.get({ id: state.currentThreadId });
                    if (thread) {
                        this.discuss.thread = thread;
                    }
                }
            } catch (error) {
                console.error('恢复讨论状态失败:', error);
            }
        }
    }
});
```

## 事件处理

### 1. 消息事件

```javascript
// 消息事件处理器
const messageEventHandlers = {
    'discuss.channel/new_message': ({ detail: { channel, message } }) => {
        handleNewMessage(channel, message);
    },
    
    'discuss.channel/message_read': ({ detail: { channel, message } }) => {
        updateReadStatus(channel, message);
    },
    
    'discuss.channel/typing': ({ detail: { channel, user } }) => {
        showTypingIndicator(channel, user);
    }
};

// 注册事件处理器
Object.entries(messageEventHandlers).forEach(([event, handler]) => {
    store.env.bus.addEventListener(event, handler);
});
```

### 2. UI事件

```javascript
// UI事件处理
const uiEventHandlers = {
    resize: () => {
        handleResponsiveLayout();
    },
    
    focus: () => {
        // 窗口获得焦点时的处理
        markVisibleMessagesAsRead();
    },
    
    blur: () => {
        // 窗口失去焦点时的处理
        saveCurrentState();
    }
};

// 注册UI事件
Object.entries(uiEventHandlers).forEach(([event, handler]) => {
    window.addEventListener(event, handler);
});
```

## 性能优化

### 1. 计数器缓存

```javascript
// 计数器缓存优化
const counterCache = new Map();

patch(Store.prototype, {
    getDiscussSidebarCategoryCounter(categoryId) {
        const cacheKey = `${categoryId}_${Date.now()}`;
        
        if (counterCache.has(categoryId)) {
            const cached = counterCache.get(categoryId);
            if (Date.now() - cached.timestamp < 5000) { // 5秒缓存
                return cached.value;
            }
        }
        
        const counter = this._calculateCategoryCounter(categoryId);
        counterCache.set(categoryId, {
            value: counter,
            timestamp: Date.now()
        });
        
        return counter;
    }
});
```

### 2. 事件防抖

```javascript
// 事件防抖处理
const debouncedHandlers = {
    resize: debounce(() => {
        handleResponsiveLayout();
    }, 250),
    
    messageUpdate: debounce((channel, message) => {
        updateMessageDisplay(channel, message);
    }, 100)
};
```

## 错误处理

### 1. 补丁错误处理

```javascript
// 安全的补丁应用
const safelyApplyPatch = (target, patches) => {
    try {
        patch(target, patches);
    } catch (error) {
        console.error('应用补丁失败:', error);
        // 回退到原始功能
        restoreOriginalFunctionality(target);
    }
};
```

### 2. 事件错误处理

```javascript
// 事件处理错误捕获
const safeEventHandler = (handler) => {
    return (event) => {
        try {
            handler(event);
        } catch (error) {
            console.error('事件处理错误:', error);
            // 记录错误但不中断应用
            reportError(error);
        }
    };
};
```

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 通过补丁扩展原有功能
- 不修改原始代码结构

### 2. 观察者模式 (Observer Pattern)
- 监听各种事件
- 响应状态变化

### 3. 策略模式 (Strategy Pattern)
- 不同屏幕尺寸的不同处理策略
- 可配置的通知策略

## 注意事项

1. **补丁顺序**: 确保补丁的正确应用顺序
2. **兼容性**: 保持与原始功能的兼容性
3. **性能影响**: 避免补丁影响原有性能
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **配置化**: 支持配置化的补丁功能
2. **插件系统**: 提供插件式的扩展机制
3. **监控工具**: 添加补丁效果的监控
4. **版本兼容**: 支持多版本的兼容性
5. **测试覆盖**: 完善的补丁测试覆盖

该补丁为存储服务在公共Web环境下提供了重要的功能扩展，确保了讨论应用的正常运行和良好的用户体验。

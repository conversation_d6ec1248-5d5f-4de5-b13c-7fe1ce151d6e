# Discuss Sidebar - 讨论侧边栏

## 概述

`discuss_sidebar.js` 实现了 Odoo 邮件系统中的讨论侧边栏组件，用于在讨论应用中显示侧边栏导航。该组件基于注册表系统，支持动态加载侧边栏项目，提供了可扩展的侧边栏架构，允许其他模块注册自定义的侧边栏项目，是讨论应用的重要导航组件。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/discuss_sidebar.js`
- **行数**: 37
- **模块**: `@mail/core/public_web/discuss_sidebar`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                           // OWL 框架
'@web/core/dropdown/dropdown'         // 下拉框组件
'@web/core/dropdown/dropdown_item'    // 下拉框项目
'@web/core/registry'                  // 注册表系统
'@web/core/utils/hooks'               // Web 核心钩子
```

## 注册表定义

### discussSidebarItemsRegistry

```javascript
const discussSidebarItemsRegistry = registry.category("mail.discuss_sidebar_items");
```

**注册表特性**:
- 用于注册侧边栏项目
- 支持动态添加和移除项目
- 提供可扩展的侧边栏架构

## 组件定义

### DiscussSidebar 类

```javascript
class DiscussSidebar extends Component {
    static template = "mail.DiscussSidebar";
    static props = {};
    static components = { Dropdown, DropdownItem };
}
```

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 邮件存储服务的响应式状态
- 用于访问线程、频道等数据

## 核心功能

### 1. 侧边栏项目获取

```javascript
get discussSidebarItems() {
    return discussSidebarItemsRegistry.getAll();
}
```

**功能**:
- 从注册表获取所有侧边栏项目
- 支持动态加载项目
- 提供统一的项目访问接口

## 使用场景

### 1. 基本侧边栏显示

```javascript
// 在讨论应用中显示侧边栏
const DiscussApp = () => {
    return (
        <div class="discuss-app">
            <DiscussSidebar />
            <div class="discuss-content">
                {/* 主要内容区域 */}
            </div>
        </div>
    );
};
```

### 2. 自定义侧边栏项目

```javascript
// 注册自定义侧边栏项目
discussSidebarItemsRegistry.add("custom.channels", {
    Component: ChannelList,
    sequence: 10,
    isDisplayed: (env) => env.services["mail.store"].hasChannels,
});

discussSidebarItemsRegistry.add("custom.direct_messages", {
    Component: DirectMessageList,
    sequence: 20,
    isDisplayed: (env) => env.services["mail.store"].hasDirectMessages,
});
```

### 3. 条件性显示项目

```javascript
// 根据用户权限显示不同项目
const registerConditionalItems = () => {
    // 管理员专用项目
    discussSidebarItemsRegistry.add("admin.settings", {
        Component: AdminSettings,
        sequence: 100,
        isDisplayed: (env) => env.services.user.isAdmin,
    });
    
    // 普通用户项目
    discussSidebarItemsRegistry.add("user.favorites", {
        Component: FavoritesList,
        sequence: 30,
        isDisplayed: (env) => !env.services.user.isGuest,
    });
};
```

### 4. 动态侧边栏

```javascript
// 动态添加和移除侧边栏项目
const DynamicSidebar = () => {
    const [customItems, setCustomItems] = useState([]);
    
    const addCustomItem = (item) => {
        const itemId = `custom.${Date.now()}`;
        discussSidebarItemsRegistry.add(itemId, item);
        setCustomItems(prev => [...prev, itemId]);
    };
    
    const removeCustomItem = (itemId) => {
        discussSidebarItemsRegistry.remove(itemId);
        setCustomItems(prev => prev.filter(id => id !== itemId));
    };
    
    return (
        <div class="dynamic-sidebar">
            <DiscussSidebar />
            <div class="sidebar-controls">
                <button onClick={() => addCustomItem(newItem)}>
                    添加项目
                </button>
            </div>
        </div>
    );
};
```

## 侧边栏项目结构

### 1. 项目配置

```javascript
// 侧边栏项目的标准配置
const sidebarItemConfig = {
    Component: MyComponent,           // 要渲染的组件
    sequence: 10,                    // 排序序号
    isDisplayed: (env) => true,      // 显示条件函数
    props: {},                       // 传递给组件的props
    label: "我的项目",                // 显示标签
    icon: "fa-star",                 // 图标类名
    badge: () => getUnreadCount(),   // 徽章显示函数
};
```

### 2. 项目组件示例

```javascript
// 侧边栏项目组件示例
class SidebarItem extends Component {
    static template = "mail.SidebarItem";
    static props = {
        label: String,
        icon: { type: String, optional: true },
        badge: { type: Number, optional: true },
        onClick: { type: Function, optional: true },
    };
    
    onClick() {
        if (this.props.onClick) {
            this.props.onClick();
        }
    }
}
```

### 3. 复杂项目示例

```javascript
// 复杂的侧边栏项目
class ChannelListItem extends Component {
    static template = "mail.ChannelListItem";
    
    setup() {
        this.store = useState(useService("mail.store"));
    }
    
    get channels() {
        return this.store.Thread.records.filter(
            thread => thread.channel_type === 'channel'
        );
    }
    
    get unreadCount() {
        return this.channels.reduce(
            (sum, channel) => sum + channel.message_unread_counter, 
            0
        );
    }
    
    selectChannel(channel) {
        this.store.discuss.thread = channel;
    }
}
```

## 注册表操作

### 1. 添加项目

```javascript
// 添加侧边栏项目
const addSidebarItem = (key, config) => {
    discussSidebarItemsRegistry.add(key, {
        Component: config.Component,
        sequence: config.sequence || 50,
        isDisplayed: config.isDisplayed || (() => true),
        ...config
    });
};

// 批量添加项目
const addMultipleSidebarItems = (items) => {
    Object.entries(items).forEach(([key, config]) => {
        addSidebarItem(key, config);
    });
};
```

### 2. 移除项目

```javascript
// 移除侧边栏项目
const removeSidebarItem = (key) => {
    discussSidebarItemsRegistry.remove(key);
};

// 条件性移除项目
const removeItemsIf = (condition) => {
    const allItems = discussSidebarItemsRegistry.getAll();
    allItems.forEach(([key, item]) => {
        if (condition(key, item)) {
            discussSidebarItemsRegistry.remove(key);
        }
    });
};
```

### 3. 更新项目

```javascript
// 更新侧边栏项目
const updateSidebarItem = (key, updates) => {
    const currentItem = discussSidebarItemsRegistry.get(key);
    if (currentItem) {
        discussSidebarItemsRegistry.add(key, {
            ...currentItem,
            ...updates
        });
    }
};
```

## 项目排序和过滤

### 1. 序号排序

```javascript
// 按序号排序侧边栏项目
const getSortedSidebarItems = () => {
    const items = discussSidebarItemsRegistry.getAll();
    return items.sort((a, b) => {
        const seqA = a[1].sequence || 50;
        const seqB = b[1].sequence || 50;
        return seqA - seqB;
    });
};
```

### 2. 条件过滤

```javascript
// 过滤可显示的项目
const getVisibleSidebarItems = (env) => {
    const allItems = discussSidebarItemsRegistry.getAll();
    return allItems.filter(([key, item]) => {
        if (typeof item.isDisplayed === 'function') {
            return item.isDisplayed(env);
        }
        return item.isDisplayed !== false;
    });
};
```

### 3. 分组显示

```javascript
// 按组分类显示项目
const getGroupedSidebarItems = () => {
    const items = getSortedSidebarItems();
    const groups = {};
    
    items.forEach(([key, item]) => {
        const group = item.group || 'default';
        if (!groups[group]) {
            groups[group] = [];
        }
        groups[group].push([key, item]);
    });
    
    return groups;
};
```

## 响应式设计

### 1. 移动端适配

```javascript
// 移动端侧边栏适配
const MobileSidebar = () => {
    const [isOpen, setIsOpen] = useState(false);
    const ui = useService("ui");
    
    if (ui.isSmall) {
        return (
            <div class="mobile-sidebar">
                <button 
                    class="sidebar-toggle"
                    onClick={() => setIsOpen(!isOpen)}
                >
                    ☰
                </button>
                {isOpen && (
                    <div class="sidebar-overlay">
                        <DiscussSidebar />
                    </div>
                )}
            </div>
        );
    }
    
    return <DiscussSidebar />;
};
```

### 2. 可折叠侧边栏

```javascript
// 可折叠的侧边栏
const CollapsibleSidebar = () => {
    const [collapsed, setCollapsed] = useState(false);
    
    return (
        <div class={`sidebar ${collapsed ? 'collapsed' : 'expanded'}`}>
            <button 
                class="collapse-toggle"
                onClick={() => setCollapsed(!collapsed)}
            >
                {collapsed ? '→' : '←'}
            </button>
            {!collapsed && <DiscussSidebar />}
        </div>
    );
};
```

## 样式和主题

### 1. 基础样式

```css
.discuss-sidebar {
    width: 250px;
    background: #f8f9fa;
    border-right: 1px solid #dee2e6;
    overflow-y: auto;
    
    .sidebar-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        border-bottom: 1px solid #e9ecef;
        
        &:hover {
            background: #e9ecef;
        }
        
        &.active {
            background: #007bff;
            color: white;
        }
        
        .item-icon {
            margin-right: 8px;
            width: 16px;
        }
        
        .item-label {
            flex: 1;
        }
        
        .item-badge {
            background: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 12px;
        }
    }
}
```

### 2. 主题适配

```css
/* 深色主题 */
.dark-theme .discuss-sidebar {
    background: #2d3748;
    border-right-color: #4a5568;
    
    .sidebar-item {
        border-bottom-color: #4a5568;
        color: #e2e8f0;
        
        &:hover {
            background: #4a5568;
        }
        
        &.active {
            background: #4299e1;
        }
    }
}
```

## 性能优化

### 1. 虚拟化

```javascript
// 大量项目时的虚拟化
const VirtualizedSidebar = ({ items }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    const visibleItems = items.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-sidebar">
            {visibleItems.map(([key, item]) => (
                <SidebarItem key={key} {...item} />
            ))}
        </div>
    );
};
```

### 2. 懒加载

```javascript
// 侧边栏项目懒加载
const LazySidebarItem = ({ itemConfig }) => {
    const [Component, setComponent] = useState(null);
    
    useEffect(() => {
        if (itemConfig.lazy) {
            itemConfig.Component().then(setComponent);
        } else {
            setComponent(itemConfig.Component);
        }
    }, [itemConfig]);
    
    if (!Component) {
        return <div class="loading">加载中...</div>;
    }
    
    return <Component {...itemConfig.props} />;
};
```

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 可扩展的项目注册系统
- 支持插件式架构

### 2. 组合模式 (Composition Pattern)
- 组合多个侧边栏项目
- 灵活的项目组合

### 3. 策略模式 (Strategy Pattern)
- 根据条件显示不同项目
- 可配置的显示策略

## 注意事项

1. **性能考虑**: 避免注册过多项目影响性能
2. **用户体验**: 提供清晰的导航结构
3. **可扩展性**: 支持第三方模块扩展
4. **响应式设计**: 适配不同屏幕尺寸

## 扩展建议

1. **拖拽排序**: 支持用户自定义项目顺序
2. **个性化**: 支持用户隐藏/显示特定项目
3. **搜索功能**: 添加侧边栏项目搜索
4. **快捷键**: 支持键盘快捷键导航
5. **动画效果**: 添加项目切换动画

该组件为讨论应用提供了可扩展的侧边栏架构，支持动态加载和自定义项目，是构建灵活导航系统的重要基础。

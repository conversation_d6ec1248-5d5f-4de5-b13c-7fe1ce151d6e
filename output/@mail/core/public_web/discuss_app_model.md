# Discuss App Model - 讨论应用模型

## 概述

`discuss_app_model.js` 实现了 Odoo 邮件系统中的讨论应用模型，是讨论应用的核心数据模型。该模型管理讨论应用的状态、分类、当前线程等信息，定义了默认的频道和聊天分类，支持侧边栏紧凑模式、搜索功能等特性，为整个讨论应用提供了统一的数据管理和状态控制。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/discuss_app_model.js`
- **行数**: 92
- **模块**: `@mail/core/public_web/discuss_app_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'     // 记录基类
'@web/core/browser/browser'    // 浏览器工具
'@web/core/l10n/translation'   // 国际化
```

## 模型定义

### DiscussApp 类

```javascript
class DiscussApp extends Record {
    static new(data) {
        const res = super.new(data);
        // 初始化默认分类
        Object.assign(res, {
            channels: { /* 频道分类配置 */ },
            chats: { /* 聊天分类配置 */ }
        });
        return res;
    }
}
```

## 核心属性

### 基础状态属性

```javascript
// 应用状态
isActive: boolean = false;           // 应用是否激活
activeTab: string = "main";          // 当前激活标签页
searchTerm: string = "";             // 搜索关键词
hasRestoredThread: boolean = false;  // 是否已恢复线程

// 界面配置
INSPECTOR_WIDTH: number = 300;       // 检查器宽度
```

### 侧边栏配置

```javascript
isSidebarCompact = Record.attr(false, {
    compute() {
        return (
            browser.localStorage.getItem("mail.user_setting.discuss_sidebar_compact") === "true"
        );
    },
    onUpdate() {
        if (this.isSidebarCompact) {
            browser.localStorage.setItem(
                "mail.user_setting.discuss_sidebar_compact",
                this.isSidebarCompact.toString()
            );
        } else {
            browser.localStorage.removeItem("mail.user_setting.discuss_sidebar_compact");
        }
    }
});
```

**侧边栏紧凑模式**:
- 自动从本地存储读取设置
- 状态变化时自动保存到本地存储
- 支持紧凑和正常两种显示模式

### 关联属性

```javascript
// 当前线程
thread = Record.one("Thread");

// 所有分类
allCategories = Record.many("DiscussAppCategory", {
    inverse: "app",
    sort: (c1, c2) =>
        c1.sequence !== c2.sequence
            ? c1.sequence - c2.sequence
            : c1.name.localeCompare(c2.name)
});

// 预定义分类
channels = Record.one("DiscussAppCategory");  // 频道分类
chats = Record.one("DiscussAppCategory");     // 聊天分类
```

## 默认分类配置

### 1. 频道分类

```javascript
channels: {
    extraClass: "o-mail-DiscussSidebarCategory-channel",
    icon: "fa fa-hashtag",
    id: "channels",
    name: _t("Channels"),
    canView: true,
    canAdd: true,
    sequence: 10,
    serverStateKey: "is_discuss_sidebar_category_channel_open",
    addTitle: _t("Add or join a channel"),
    addHotkey: "c"
}
```

**频道分类特性**:
- 使用井号图标
- 支持查看和添加操作
- 快捷键 "c" 用于添加频道
- 状态保存到服务器

### 2. 聊天分类

```javascript
chats: {
    extraClass: "o-mail-DiscussSidebarCategory-chat",
    icon: "fa fa-users",
    id: "chats",
    name: _t("Direct messages"),
    canView: false,
    canAdd: true,
    sequence: 30,
    serverStateKey: "is_discuss_sidebar_category_chat_open",
    addTitle: _t("Start a conversation"),
    addHotkey: "d"
}
```

**聊天分类特性**:
- 使用用户群组图标
- 不支持查看操作（私密性）
- 支持添加新对话
- 快捷键 "d" 用于开始对话

## 使用场景

### 1. 讨论应用初始化

```javascript
// 初始化讨论应用
const initializeDiscussApp = () => {
    const discussApp = DiscussApp.insert({
        isActive: false,
        activeTab: "main",
        searchTerm: "",
        hasRestoredThread: false
    });
    
    // 创建默认分类
    const channelsCategory = DiscussAppCategory.insert(discussApp.channels);
    const chatsCategory = DiscussAppCategory.insert(discussApp.chats);
    
    discussApp.channels = channelsCategory;
    discussApp.chats = chatsCategory;
    
    return discussApp;
};
```

### 2. 应用状态管理

```javascript
// 激活讨论应用
const activateDiscussApp = (discussApp) => {
    discussApp.isActive = true;
    
    // 恢复上次的线程
    if (!discussApp.hasRestoredThread) {
        const lastThreadId = browser.localStorage.getItem('discuss_last_thread');
        if (lastThreadId) {
            const thread = Thread.get({ id: parseInt(lastThreadId) });
            if (thread) {
                discussApp.thread = thread;
            }
        }
        discussApp.hasRestoredThread = true;
    }
};

// 停用讨论应用
const deactivateDiscussApp = (discussApp) => {
    discussApp.isActive = false;
    
    // 保存当前线程
    if (discussApp.thread) {
        browser.localStorage.setItem('discuss_last_thread', discussApp.thread.id);
    }
};
```

### 3. 标签页切换

```javascript
// 切换标签页
const switchTab = (discussApp, tabName) => {
    const validTabs = ['main', 'channel', 'chat', 'livechat'];
    
    if (validTabs.includes(tabName)) {
        discussApp.activeTab = tabName;
        
        // 根据标签页过滤内容
        filterContentByTab(discussApp, tabName);
    }
};

// 根据标签页过滤内容
const filterContentByTab = (discussApp, tab) => {
    switch (tab) {
        case 'channel':
            // 只显示频道
            showOnlyChannels(discussApp);
            break;
        case 'chat':
            // 只显示聊天
            showOnlyChats(discussApp);
            break;
        case 'livechat':
            // 只显示在线聊天
            showOnlyLivechats(discussApp);
            break;
        default:
            // 显示所有内容
            showAllContent(discussApp);
    }
};
```

### 4. 搜索功能

```javascript
// 设置搜索关键词
const setSearchTerm = (discussApp, term) => {
    discussApp.searchTerm = term;
    
    // 触发搜索
    performSearch(discussApp, term);
};

// 执行搜索
const performSearch = (discussApp, term) => {
    if (!term.trim()) {
        // 清空搜索，显示所有内容
        showAllThreads(discussApp);
        return;
    }
    
    // 在所有分类中搜索
    const searchResults = [];
    discussApp.allCategories.forEach(category => {
        const matchingThreads = category.threads.filter(thread =>
            thread.displayName.toLowerCase().includes(term.toLowerCase()) ||
            thread.description?.toLowerCase().includes(term.toLowerCase())
        );
        searchResults.push(...matchingThreads);
    });
    
    displaySearchResults(discussApp, searchResults);
};
```

## 侧边栏管理

### 1. 紧凑模式切换

```javascript
// 切换侧边栏紧凑模式
const toggleSidebarCompact = (discussApp) => {
    discussApp.isSidebarCompact = !discussApp.isSidebarCompact;
    
    // 触发界面重新布局
    triggerLayoutUpdate();
};

// 设置侧边栏模式
const setSidebarCompact = (discussApp, compact) => {
    discussApp.isSidebarCompact = compact;
    
    // 应用样式变化
    applySidebarStyles(compact);
};
```

### 2. 分类管理

```javascript
// 添加新分类
const addCategory = (discussApp, categoryData) => {
    const category = DiscussAppCategory.insert({
        ...categoryData,
        app: discussApp
    });
    
    // 分类会自动添加到 allCategories 中
    return category;
};

// 移除分类
const removeCategory = (discussApp, categoryId) => {
    const category = discussApp.allCategories.find(c => c.id === categoryId);
    if (category && !['channels', 'chats'].includes(categoryId)) {
        category.delete();
    }
};

// 重排序分类
const reorderCategories = (discussApp, categoryIds) => {
    categoryIds.forEach((id, index) => {
        const category = discussApp.allCategories.find(c => c.id === id);
        if (category) {
            category.sequence = (index + 1) * 10;
        }
    });
};
```

## 线程管理

### 1. 当前线程设置

```javascript
// 设置当前线程
const setCurrentThread = (discussApp, thread) => {
    discussApp.thread = thread;
    
    // 更新浏览器历史
    updateBrowserHistory(thread);
    
    // 标记为已读
    if (thread && thread.markAsRead) {
        thread.markAsRead();
    }
};

// 清除当前线程
const clearCurrentThread = (discussApp) => {
    discussApp.thread = null;
    
    // 返回主页面
    updateBrowserHistory(null);
};
```

### 2. 线程恢复

```javascript
// 恢复上次的线程
const restoreLastThread = (discussApp) => {
    if (discussApp.hasRestoredThread) {
        return;
    }
    
    const lastThreadId = browser.localStorage.getItem('discuss_last_thread');
    if (lastThreadId) {
        const thread = Thread.get({ id: parseInt(lastThreadId) });
        if (thread && thread.displayToSelf) {
            discussApp.thread = thread;
        }
    }
    
    discussApp.hasRestoredThread = true;
};
```

## 状态持久化

### 1. 本地存储

```javascript
// 保存应用状态到本地存储
const saveAppState = (discussApp) => {
    const state = {
        activeTab: discussApp.activeTab,
        isSidebarCompact: discussApp.isSidebarCompact,
        currentThreadId: discussApp.thread?.id
    };
    
    browser.localStorage.setItem('discuss_app_state', JSON.stringify(state));
};

// 从本地存储恢复应用状态
const restoreAppState = (discussApp) => {
    const savedState = browser.localStorage.getItem('discuss_app_state');
    if (savedState) {
        try {
            const state = JSON.parse(savedState);
            
            if (state.activeTab) {
                discussApp.activeTab = state.activeTab;
            }
            
            if (state.isSidebarCompact !== undefined) {
                discussApp.isSidebarCompact = state.isSidebarCompact;
            }
            
            if (state.currentThreadId) {
                const thread = Thread.get({ id: state.currentThreadId });
                if (thread) {
                    discussApp.thread = thread;
                }
            }
        } catch (error) {
            console.error('恢复应用状态失败:', error);
        }
    }
};
```

### 2. 服务器同步

```javascript
// 同步分类状态到服务器
const syncCategoriesToServer = async (discussApp) => {
    const categoryStates = {};
    
    discussApp.allCategories.forEach(category => {
        if (category.serverStateKey) {
            categoryStates[category.serverStateKey] = category.open;
        }
    });
    
    if (Object.keys(categoryStates).length > 0) {
        await discussApp.store.env.services.orm.call(
            "res.users.settings",
            "set_res_users_settings",
            [[discussApp.store.settings.id]],
            { new_settings: categoryStates }
        );
    }
};
```

## 事件处理

### 1. 状态变化监听

```javascript
// 监听应用激活状态变化
const watchActiveState = (discussApp, callback) => {
    let lastActiveState = discussApp.isActive;
    
    setInterval(() => {
        if (discussApp.isActive !== lastActiveState) {
            callback(discussApp.isActive, lastActiveState);
            lastActiveState = discussApp.isActive;
        }
    }, 100);
};

// 监听线程变化
const watchThreadChange = (discussApp, callback) => {
    let lastThread = discussApp.thread;
    
    setInterval(() => {
        if (discussApp.thread !== lastThread) {
            callback(discussApp.thread, lastThread);
            lastThread = discussApp.thread;
        }
    }, 100);
};
```

### 2. 快捷键处理

```javascript
// 处理全局快捷键
const handleGlobalHotkeys = (discussApp, event) => {
    if (!discussApp.isActive) {
        return;
    }
    
    const key = event.key.toLowerCase();
    const ctrlOrCmd = event.ctrlKey || event.metaKey;
    
    if (ctrlOrCmd) {
        switch (key) {
            case 'k':
                // 快速搜索
                event.preventDefault();
                focusSearchInput();
                break;
            case 'n':
                // 新建对话
                event.preventDefault();
                startNewConversation(discussApp);
                break;
        }
    } else {
        // 分类快捷键
        discussApp.allCategories.forEach(category => {
            if (category.addHotkey === key) {
                event.preventDefault();
                triggerCategoryAdd(category);
            }
        });
    }
};
```

## 性能优化

### 1. 懒加载

```javascript
// 懒加载分类内容
const lazyLoadCategoryContent = (discussApp, category) => {
    if (!category.open) {
        return; // 折叠状态不加载
    }
    
    // 只加载可见线程
    const visibleThreads = category.threads.filter(thread => 
        thread.displayToSelf
    );
    
    return visibleThreads;
};
```

### 2. 缓存优化

```javascript
// 缓存搜索结果
const searchCache = new Map();

const getCachedSearchResults = (term) => {
    if (searchCache.has(term)) {
        return searchCache.get(term);
    }
    
    const results = performActualSearch(term);
    searchCache.set(term, results);
    
    // 限制缓存大小
    if (searchCache.size > 100) {
        const firstKey = searchCache.keys().next().value;
        searchCache.delete(firstKey);
    }
    
    return results;
};
```

## 设计模式

### 1. 单例模式 (Singleton Pattern)
- 讨论应用在系统中只有一个实例
- 全局状态管理

### 2. 观察者模式 (Observer Pattern)
- 监听状态变化
- 响应用户操作

### 3. 工厂模式 (Factory Pattern)
- 创建默认分类
- 统一的对象创建接口

## 注意事项

1. **状态同步**: 确保本地和服务器状态的一致性
2. **性能考虑**: 避免频繁的状态保存操作
3. **用户体验**: 提供流畅的界面切换
4. **数据一致性**: 保持应用状态的一致性

## 扩展建议

1. **多标签页支持**: 支持多个讨论标签页
2. **自定义布局**: 支持用户自定义界面布局
3. **主题切换**: 支持多种视觉主题
4. **快捷键自定义**: 支持用户自定义快捷键
5. **状态导出**: 支持导出和导入应用配置

该模型是讨论应用的核心数据管理中心，为整个应用提供了统一的状态控制和数据管理功能。

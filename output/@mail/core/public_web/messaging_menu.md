# Messaging Menu - 消息菜单

## 概述

`messaging_menu.js` 实现了 Odoo 邮件系统中的消息菜单组件，是系统托盘中的重要通知中心。该组件显示未读消息、通知项目，支持键盘导航、触摸交互和快速操作，提供了便捷的消息访问入口，集成了讨论系统托盘功能，是用户与邮件系统交互的核心界面组件。

## 文件信息
- **路径**: `/mail/static/src/core/public_web/messaging_menu.js`
- **行数**: 177
- **模块**: `@mail/core/public_web/messaging_menu`

## 依赖关系

```javascript
// UI组件依赖
'@mail/core/common/country_flag'        // 国旗组件
'@mail/core/common/im_status'           // 即时消息状态
'@mail/core/public_web/notification_item' // 通知项目

// 工具和钩子依赖
'@mail/utils/common/hooks'              // 邮件工具钩子
'@odoo/owl'                            // OWL 框架
'@web/core/browser/feature_detection'   // 特性检测
'@web/core/dropdown/dropdown'           // 下拉框组件
'@web/core/dropdown/dropdown_hooks'     // 下拉框钩子
'@web/core/l10n/translation'           // 国际化
'@web/core/registry'                   // 注册表
'@web/core/utils/hooks'                // Web 核心钩子
'@web/core/hotkeys/hotkey_service'     // 热键服务
```

## 组件定义

### MessagingMenu 类

```javascript
class MessagingMenu extends Component {
    static components = { CountryFlag, Dropdown, NotificationItem, ImStatus };
    static props = [];
    static template = "mail.MessagingMenu";
}
```

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    
    // 服务和钩子
    this.discussSystray = useDiscussSystray();
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    
    // 特性检测
    this.hasTouch = hasTouch;
    
    // 组件状态
    this.state = useState({
        activeIndex: null,    // 当前激活的项目索引
        adding: false,        // 是否正在添加状态
    });
    
    // 下拉框状态
    this.dropdown = useDropdownState();
    
    // DOM引用
    this.notificationList = useRef("notification-list");
    
    // 键盘事件监听
    useExternalListener(window, "keydown", this.onKeydown, true);
}
```

**初始化内容**:
- 讨论系统托盘钩子
- 邮件存储和UI服务
- 触摸设备检测
- 组件状态管理
- 键盘事件监听

## 核心功能

### 1. 线程点击处理

```javascript
onClickThread(isMarkAsRead, thread) {
    if (!isMarkAsRead) {
        this.openDiscussion(thread);
        return;
    }
    this.markAsRead(thread);
}
```

**点击逻辑**:
- **打开讨论**: 当不是标记为已读操作时，打开讨论界面
- **标记已读**: 当是标记已读操作时，执行已读标记

### 2. 标记已读

```javascript
markAsRead(thread) {
    if (thread.needactionMessages.length > 0) {
        thread.markAllMessagesAsRead();
    }
    if (thread.model === "discuss.channel") {
        thread.markAsRead();
    }
}
```

**已读逻辑**:
- 标记所有需要操作的消息为已读
- 对于讨论频道，额外调用频道的已读标记

### 3. 键盘导航

```javascript
navigate(direction) {
    if (this.notificationItems.length === 0) {
        return;
    }
    
    const activeOptionId = this.state.activeIndex !== null ? this.state.activeIndex : 0;
    let targetId = undefined;
    
    switch (direction) {
        case "first":
            targetId = 0;
            break;
        case "last":
            targetId = this.notificationItems.length - 1;
            break;
        case "previous":
            targetId = activeOptionId - 1;
            if (targetId < 0) {
                this.navigate("last");
                return;
            }
            break;
        case "next":
            targetId = activeOptionId + 1;
            if (targetId > this.notificationItems.length - 1) {
                this.navigate("first");
                return;
            }
            break;
        default:
            return;
    }
    
    this.state.activeIndex = targetId;
    this.notificationItems[targetId]?.scrollIntoView({ block: "nearest" });
}
```

**导航特性**:
- 支持首个、最后、上一个、下一个导航
- 循环导航（到达边界时跳转到另一端）
- 自动滚动到可见区域

### 4. 键盘事件处理

```javascript
onKeydown(ev) {
    if (!this.dropdown.isOpen) {
        return;
    }
    
    const hotkey = getActiveHotkey(ev);
    switch (hotkey) {
        case "enter":
            if (this.state.activeIndex === null) {
                return;
            }
            this.notificationItems[this.state.activeIndex].click();
            break;
        case "tab":
            this.navigate(this.state.activeIndex === null ? "first" : "next");
            break;
        case "arrowup":
            this.navigate(this.state.activeIndex === null ? "first" : "previous");
            break;
        case "arrowdown":
            this.navigate(this.state.activeIndex === null ? "first" : "next");
            break;
        default:
            return;
    }
    
    ev.preventDefault();
    ev.stopPropagation();
}
```

**快捷键支持**:
- **Enter**: 点击当前激活项目
- **Tab**: 导航到下一个项目
- **上箭头**: 导航到上一个项目
- **下箭头**: 导航到下一个项目

## 使用场景

### 1. 系统托盘消息中心

```javascript
// 在系统托盘中显示消息菜单
const SystemTray = () => {
    return (
        <div class="systray">
            <MessagingMenu />
            <div class="other-systray-items">
                {/* 其他托盘项目 */}
            </div>
        </div>
    );
};
```

### 2. 未读消息通知

```javascript
// 显示未读消息数量
const UnreadMessageIndicator = () => {
    const store = useService("mail.store");
    const unreadCount = store.discuss.inbox.counter;
    
    return (
        <div class="unread-indicator">
            <MessagingMenu />
            {unreadCount > 0 && (
                <span class="unread-badge">{unreadCount}</span>
            )}
        </div>
    );
};
```

### 3. 快速消息访问

```javascript
// 快速访问最近的消息
const QuickMessageAccess = () => {
    const recentThreads = getRecentThreads();
    
    return (
        <div class="quick-access">
            <MessagingMenu />
            <div class="recent-threads">
                {recentThreads.map(thread => (
                    <div 
                        key={thread.id}
                        class="recent-thread"
                        onClick={() => openThread(thread)}
                    >
                        {thread.displayName}
                    </div>
                ))}
            </div>
        </div>
    );
};
```

### 4. 移动端消息菜单

```javascript
// 移动端优化的消息菜单
const MobileMessagingMenu = () => {
    const ui = useService("ui");
    
    if (ui.isSmall) {
        return (
            <div class="mobile-messaging-menu">
                <MessagingMenu />
                <div class="mobile-actions">
                    <button onClick={() => markAllAsRead()}>
                        全部已读
                    </button>
                </div>
            </div>
        );
    }
    
    return <MessagingMenu />;
};
```

## 通知项目管理

### 1. 通知项目获取

```javascript
// 获取通知项目列表
get notificationItems() {
    return this.notificationList.el?.querySelectorAll('.notification-item') || [];
}

// 获取可见的通知项目
const getVisibleNotificationItems = () => {
    const items = this.notificationItems;
    return Array.from(items).filter(item => {
        const rect = item.getBoundingClientRect();
        return rect.height > 0 && rect.width > 0;
    });
};
```

### 2. 通知项目过滤

```javascript
// 过滤通知项目
const filterNotificationItems = (filter) => {
    const allItems = this.store.discuss.inbox.threads;
    
    switch (filter) {
        case 'unread':
            return allItems.filter(thread => thread.message_unread_counter > 0);
        case 'mentions':
            return allItems.filter(thread => thread.message_needaction_counter > 0);
        case 'channels':
            return allItems.filter(thread => thread.channel_type === 'channel');
        case 'direct':
            return allItems.filter(thread => thread.channel_type === 'chat');
        default:
            return allItems;
    }
};
```

### 3. 通知项目排序

```javascript
// 排序通知项目
const sortNotificationItems = (items, sortBy = 'recent') => {
    switch (sortBy) {
        case 'recent':
            return items.sort((a, b) => 
                new Date(b.lastMessage?.date) - new Date(a.lastMessage?.date)
            );
        case 'unread':
            return items.sort((a, b) => 
                b.message_unread_counter - a.message_unread_counter
            );
        case 'alphabetical':
            return items.sort((a, b) => 
                a.displayName.localeCompare(b.displayName)
            );
        default:
            return items;
    }
};
```

## 交互功能

### 1. 触摸设备支持

```javascript
// 触摸设备的特殊处理
const handleTouchInteraction = () => {
    if (this.hasTouch) {
        // 触摸设备上的长按菜单
        const handleLongPress = (event, thread) => {
            event.preventDefault();
            showContextMenu(event, thread);
        };
        
        // 触摸设备上的滑动操作
        const handleSwipe = (direction, thread) => {
            if (direction === 'left') {
                this.markAsRead(thread);
            } else if (direction === 'right') {
                this.openDiscussion(thread);
            }
        };
    }
};
```

### 2. 批量操作

```javascript
// 批量标记已读
const markAllAsRead = () => {
    const unreadThreads = this.store.discuss.inbox.threads.filter(
        thread => thread.message_unread_counter > 0
    );
    
    unreadThreads.forEach(thread => {
        this.markAsRead(thread);
    });
    
    this.dropdown.close();
};

// 批量删除通知
const clearAllNotifications = () => {
    const threads = this.store.discuss.inbox.threads;
    threads.forEach(thread => {
        thread.markAllMessagesAsRead();
    });
};
```

### 3. 搜索功能

```javascript
// 通知搜索
const searchNotifications = (query) => {
    const allThreads = this.store.discuss.inbox.threads;
    
    return allThreads.filter(thread => {
        return thread.displayName.toLowerCase().includes(query.toLowerCase()) ||
               thread.lastMessage?.body.toLowerCase().includes(query.toLowerCase());
    });
};
```

## 状态管理

### 1. 激活状态

```javascript
// 管理激活状态
const manageActiveState = () => {
    const setActiveIndex = (index) => {
        this.state.activeIndex = index;
        
        // 确保激活项目可见
        if (this.notificationItems[index]) {
            this.notificationItems[index].scrollIntoView({ 
                block: "nearest",
                behavior: "smooth"
            });
        }
    };
    
    const clearActiveState = () => {
        this.state.activeIndex = null;
    };
    
    return { setActiveIndex, clearActiveState };
};
```

### 2. 下拉框状态

```javascript
// 下拉框状态管理
const manageDropdownState = () => {
    const openDropdown = () => {
        this.dropdown.open();
        this.state.activeIndex = null; // 重置激活状态
    };
    
    const closeDropdown = () => {
        this.dropdown.close();
        this.state.activeIndex = null;
    };
    
    return { openDropdown, closeDropdown };
};
```

## 性能优化

### 1. 虚拟化

```javascript
// 大量通知时的虚拟化
const VirtualizedNotificationList = ({ notifications }) => {
    const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
    const visibleNotifications = notifications.slice(visibleRange.start, visibleRange.end);
    
    return (
        <div class="virtualized-notification-list">
            {visibleNotifications.map(notification => (
                <NotificationItem key={notification.id} notification={notification} />
            ))}
        </div>
    );
};
```

### 2. 防抖搜索

```javascript
// 搜索防抖
const debouncedSearch = debounce((query) => {
    const results = searchNotifications(query);
    updateNotificationList(results);
}, 300);
```

## 可访问性

### 1. ARIA 标签

```javascript
// 可访问性标签
const getAccessibilityProps = () => {
    return {
        'aria-label': '消息菜单',
        'role': 'menu',
        'aria-expanded': this.dropdown.isOpen,
        'aria-haspopup': 'true'
    };
};
```

### 2. 键盘导航

```javascript
// 完整的键盘导航支持
const setupKeyboardNavigation = () => {
    const keyMap = {
        'Enter': () => activateCurrentItem(),
        'Space': () => activateCurrentItem(),
        'ArrowUp': () => this.navigate('previous'),
        'ArrowDown': () => this.navigate('next'),
        'Home': () => this.navigate('first'),
        'End': () => this.navigate('last'),
        'Escape': () => this.dropdown.close()
    };
    
    return keyMap;
};
```

## 设计模式

### 1. 观察者模式 (Observer Pattern)
- 监听键盘事件
- 响应状态变化

### 2. 命令模式 (Command Pattern)
- 键盘快捷键映射到具体操作
- 可撤销的操作

### 3. 策略模式 (Strategy Pattern)
- 不同设备的不同交互策略
- 触摸 vs 鼠标交互

## 注意事项

1. **键盘导航**: 确保完整的键盘可访问性
2. **性能优化**: 大量通知时的渲染性能
3. **触摸支持**: 移动设备的交互优化
4. **状态同步**: 保持UI状态与数据状态同步

## 扩展建议

1. **自定义过滤**: 支持用户自定义通知过滤规则
2. **通知分组**: 按类型或来源分组显示通知
3. **快速回复**: 支持在菜单中直接回复消息
4. **通知预览**: 悬停显示消息预览
5. **离线支持**: 支持离线查看缓存的通知

该组件是用户与邮件系统交互的重要入口，提供了便捷的消息访问和管理功能，是系统用户体验的关键组件。

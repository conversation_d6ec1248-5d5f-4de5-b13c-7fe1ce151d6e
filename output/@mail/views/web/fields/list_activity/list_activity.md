# List Activity - 列表活动字段

## 概述

`list_activity.js` 是 Odoo 邮件系统的列表活动字段组件，专门用于在列表视图中显示活动相关信息。该组件基于OWL框架和邮件核心活动按钮，集成了活动摘要显示、异常处理、国际化支持等核心功能，为邮件系统提供了完整的列表活动可视化和交互支持，是邮件系统活动管理和列表视图的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/list_activity/list_activity.js`
- **行数**: 54
- **模块**: `@mail/views/web/fields/list_activity/list_activity`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity_button'        // 活动按钮组件
'@odoo/owl'                             // OWL 框架
'@web/core/l10n/translation'            // 国际化翻译
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. ListActivity 组件

```javascript
const ListActivity = class ListActivity extends Component {
    static components = { ActivityButton };
    static fieldDependencies = [
        { name: "activity_exception_decoration", type: "selection", selection: [] },
        { name: "activity_exception_icon", type: "char" },
        { name: "activity_state", type: "selection", selection: [] },
        { name: "activity_summary", type: "char" },
        { name: "activity_type_icon", type: "char" },
        { name: "activity_type_id", type: "many2one", relation: "mail.activity.type" },
    ];
    static props = standardFieldProps;
    static template = "mail.ListActivity";

    get summaryText() {
        if (this.props.record.data.activity_exception_decoration) {
            return _t("Warning");
        }
        if (this.props.record.data.activity_summary) {
            return this.props.record.data.activity_summary;
        }
        if (this.props.record.data.activity_type_id) {
            return this.props.record.data.activity_type_id[1 /* display_name */];
        }
        return undefined;
    }
}
```

**组件特性**:
- **组件集成**: 使用ActivityButton作为子组件
- **字段依赖**: 声明活动相关字段的依赖
- **标准属性**: 使用标准字段属性
- **摘要计算**: 提供智能的摘要文本计算

### 2. 字段依赖声明

```javascript
static fieldDependencies = [
    { name: "activity_exception_decoration", type: "selection", selection: [] },
    { name: "activity_exception_icon", type: "char" },
    { name: "activity_state", type: "selection", selection: [] },
    { name: "activity_summary", type: "char" },
    { name: "activity_type_icon", type: "char" },
    { name: "activity_type_id", type: "many2one", relation: "mail.activity.type" },
];
```

**依赖字段功能**:
- **异常装饰**: activity_exception_decoration - 活动异常装饰类型
- **异常图标**: activity_exception_icon - 活动异常图标
- **活动状态**: activity_state - 活动当前状态
- **活动摘要**: activity_summary - 活动摘要信息
- **类型图标**: activity_type_icon - 活动类型图标
- **类型ID**: activity_type_id - 活动类型关联

### 3. 摘要文本计算

```javascript
get summaryText() {
    if (this.props.record.data.activity_exception_decoration) {
        return _t("Warning");
    }
    if (this.props.record.data.activity_summary) {
        return this.props.record.data.activity_summary;
    }
    if (this.props.record.data.activity_type_id) {
        return this.props.record.data.activity_type_id[1 /* display_name */];
    }
    return undefined;
}
```

**摘要计算功能**:
- **异常优先**: 优先显示异常警告信息
- **摘要显示**: 显示活动的摘要信息
- **类型回退**: 回退显示活动类型名称
- **国际化**: 使用翻译函数支持多语言

### 4. 字段注册

```javascript
const listActivity = {
    component: ListActivity,
    fieldDependencies: ListActivity.fieldDependencies,
    displayName: _t("List Activity"),
    supportedTypes: ["one2many"],
};

registry.category("fields").add("list_activity", listActivity);
```

**注册功能**:
- **组件绑定**: 绑定ListActivity组件
- **依赖传递**: 传递字段依赖声明
- **显示名称**: 设置国际化的显示名称
- **类型支持**: 支持one2many字段类型

## 使用场景

### 1. 列表活动字段增强

```javascript
// 列表活动字段增强功能
const ListActivityEnhancer = {
    enhanceListActivity: () => {
        // 增强的列表活动组件
        class EnhancedListActivity extends ListActivity {
            static fieldDependencies = [
                ...ListActivity.fieldDependencies,
                { name: "activity_date_deadline", type: "datetime" },
                { name: "activity_user_id", type: "many2one", relation: "res.users" },
                { name: "activity_priority", type: "selection" },
                { name: "activity_progress", type: "float" },
                { name: "activity_tags", type: "many2many", relation: "mail.activity.tag" },
                { name: "activity_note", type: "text" },
                { name: "activity_feedback", type: "text" },
                { name: "activity_calendar_event_id", type: "many2one", relation: "calendar.event" },
                { name: "activity_count", type: "integer" }
            ];
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableTooltip: true,
                    enableQuickActions: true,
                    enableBadges: true,
                    enableProgressBar: false,
                    enablePriorityIndicator: true,
                    enableDeadlineWarning: true,
                    enableUserAvatar: true,
                    enableActivityCount: true,
                    compactMode: true,
                    showFullSummary: false,
                    maxSummaryLength: 50
                };
                
                // 活动状态映射
                this.activityStates = {
                    'planned': { color: '#17a2b8', icon: 'fa-clock-o', text: '计划中', badge: 'info' },
                    'today': { color: '#ffc107', icon: 'fa-exclamation-triangle', text: '今日', badge: 'warning' },
                    'overdue': { color: '#dc3545', icon: 'fa-exclamation-circle', text: '逾期', badge: 'danger' },
                    'done': { color: '#28a745', icon: 'fa-check-circle', text: '完成', badge: 'success' },
                    'cancelled': { color: '#6c757d', icon: 'fa-times-circle', text: '取消', badge: 'secondary' }
                };
                
                // 优先级映射
                this.activityPriorities = {
                    '0': { color: '#6c757d', icon: 'fa-minus', text: '低', badge: 'secondary' },
                    '1': { color: '#17a2b8', icon: 'fa-circle', text: '正常', badge: 'info' },
                    '2': { color: '#ffc107', icon: 'fa-star', text: '高', badge: 'warning' },
                    '3': { color: '#dc3545', icon: 'fa-fire', text: '紧急', badge: 'danger' }
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置工具提示
                if (this.enhancedConfig.enableTooltip) {
                    this.setupTooltip();
                }
                
                // 设置快速操作
                if (this.enhancedConfig.enableQuickActions) {
                    this.setupQuickActions();
                }
            }
            
            // 增强的摘要文本计算
            get summaryText() {
                try {
                    const data = this.props.record.data;
                    
                    // 检查异常装饰
                    if (data.activity_exception_decoration) {
                        return _t("Warning");
                    }
                    
                    // 获取活动摘要
                    if (data.activity_summary) {
                        const summary = data.activity_summary;
                        if (this.enhancedConfig.showFullSummary) {
                            return summary;
                        } else {
                            return this.truncateSummary(summary);
                        }
                    }
                    
                    // 获取活动类型名称
                    if (data.activity_type_id) {
                        return data.activity_type_id[1];
                    }
                    
                    // 如果有活动数量，显示数量信息
                    if (data.activity_count && data.activity_count > 0) {
                        return _t("%s activities", data.activity_count);
                    }
                    
                    return undefined;
                } catch (error) {
                    console.error('计算摘要文本失败:', error);
                    return super.summaryText;
                }
            }
            
            // 截断摘要文本
            truncateSummary(summary) {
                if (summary.length <= this.enhancedConfig.maxSummaryLength) {
                    return summary;
                }
                return summary.substring(0, this.enhancedConfig.maxSummaryLength) + '...';
            }
            
            // 获取活动信息
            getActivityInfo() {
                try {
                    const data = this.props.record.data;
                    
                    return {
                        id: data.activity_type_id?.[0],
                        state: data.activity_state,
                        summary: data.activity_summary,
                        deadline: data.activity_date_deadline,
                        userId: data.activity_user_id?.[0],
                        userName: data.activity_user_id?.[1],
                        priority: data.activity_priority,
                        progress: data.activity_progress || 0,
                        tags: data.activity_tags || [],
                        note: data.activity_note,
                        feedback: data.activity_feedback,
                        typeIcon: data.activity_type_icon,
                        exceptionIcon: data.activity_exception_icon,
                        exceptionDecoration: data.activity_exception_decoration,
                        calendarEventId: data.activity_calendar_event_id?.[0],
                        count: data.activity_count || 0
                    };
                } catch (error) {
                    console.error('获取活动信息失败:', error);
                    return null;
                }
            }
            
            // 获取活动状态信息
            getActivityStateInfo() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo || !activityInfo.state) return null;
                
                const stateInfo = this.activityStates[activityInfo.state];
                return stateInfo ? {
                    ...stateInfo,
                    state: activityInfo.state
                } : null;
            }
            
            // 获取活动优先级信息
            getActivityPriorityInfo() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo || !activityInfo.priority) return null;
                
                const priorityInfo = this.activityPriorities[activityInfo.priority];
                return priorityInfo ? {
                    ...priorityInfo,
                    priority: activityInfo.priority
                } : null;
            }
            
            // 获取截止日期状态
            getDeadlineStatus() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo || !activityInfo.deadline) return null;
                
                const deadline = new Date(activityInfo.deadline);
                const now = new Date();
                const diffDays = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));
                
                if (diffDays < 0) {
                    return { 
                        status: 'overdue', 
                        days: Math.abs(diffDays), 
                        text: _t("Overdue %s days", Math.abs(diffDays)),
                        color: '#dc3545',
                        badge: 'danger'
                    };
                } else if (diffDays === 0) {
                    return { 
                        status: 'today', 
                        days: 0, 
                        text: _t("Due today"),
                        color: '#ffc107',
                        badge: 'warning'
                    };
                } else if (diffDays <= 3) {
                    return { 
                        status: 'soon', 
                        days: diffDays, 
                        text: _t("Due in %s days", diffDays),
                        color: '#fd7e14',
                        badge: 'warning'
                    };
                } else {
                    return { 
                        status: 'future', 
                        days: diffDays, 
                        text: _t("Due in %s days", diffDays),
                        color: '#17a2b8',
                        badge: 'info'
                    };
                }
            }
            
            // 获取活动徽章
            getActivityBadges() {
                if (!this.enhancedConfig.enableBadges) return [];
                
                const badges = [];
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return badges;
                
                // 状态徽章
                const stateInfo = this.getActivityStateInfo();
                if (stateInfo) {
                    badges.push({
                        type: 'state',
                        text: stateInfo.text,
                        color: stateInfo.color,
                        badge: stateInfo.badge,
                        icon: stateInfo.icon
                    });
                }
                
                // 优先级徽章
                if (this.enhancedConfig.enablePriorityIndicator) {
                    const priorityInfo = this.getActivityPriorityInfo();
                    if (priorityInfo && priorityInfo.priority !== '1') { // 不显示正常优先级
                        badges.push({
                            type: 'priority',
                            text: priorityInfo.text,
                            color: priorityInfo.color,
                            badge: priorityInfo.badge,
                            icon: priorityInfo.icon
                        });
                    }
                }
                
                // 截止日期徽章
                if (this.enhancedConfig.enableDeadlineWarning) {
                    const deadlineStatus = this.getDeadlineStatus();
                    if (deadlineStatus && (deadlineStatus.status === 'overdue' || deadlineStatus.status === 'today')) {
                        badges.push({
                            type: 'deadline',
                            text: deadlineStatus.text,
                            color: deadlineStatus.color,
                            badge: deadlineStatus.badge,
                            icon: 'fa-calendar'
                        });
                    }
                }
                
                // 活动数量徽章
                if (this.enhancedConfig.enableActivityCount && activityInfo.count > 1) {
                    badges.push({
                        type: 'count',
                        text: activityInfo.count.toString(),
                        color: '#6c757d',
                        badge: 'secondary',
                        icon: 'fa-list'
                    });
                }
                
                return badges;
            }
            
            // 设置工具提示
            setupTooltip() {
                // 这里可以设置工具提示的相关逻辑
            }
            
            // 获取工具提示内容
            getTooltipContent() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return '';
                
                const parts = [];
                
                // 活动摘要
                if (activityInfo.summary) {
                    parts.push(_t("Summary: %s", activityInfo.summary));
                }
                
                // 负责人
                if (activityInfo.userName) {
                    parts.push(_t("Assigned to: %s", activityInfo.userName));
                }
                
                // 截止日期
                if (activityInfo.deadline) {
                    const deadlineStatus = this.getDeadlineStatus();
                    parts.push(_t("Deadline: %s", deadlineStatus.text));
                }
                
                // 优先级
                const priorityInfo = this.getActivityPriorityInfo();
                if (priorityInfo) {
                    parts.push(_t("Priority: %s", priorityInfo.text));
                }
                
                // 状态
                const stateInfo = this.getActivityStateInfo();
                if (stateInfo) {
                    parts.push(_t("Status: %s", stateInfo.text));
                }
                
                // 进度
                if (activityInfo.progress > 0) {
                    parts.push(_t("Progress: %s%%", Math.round(activityInfo.progress)));
                }
                
                // 备注
                if (activityInfo.note) {
                    parts.push(_t("Note: %s", activityInfo.note));
                }
                
                // 活动数量
                if (activityInfo.count > 1) {
                    parts.push(_t("Total activities: %s", activityInfo.count));
                }
                
                return parts.join('\n');
            }
            
            // 设置快速操作
            setupQuickActions() {
                // 这里可以设置快速操作的相关逻辑
            }
            
            // 获取快速操作列表
            getQuickActions() {
                if (!this.enhancedConfig.enableQuickActions) return [];
                
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return [];
                
                const actions = [];
                
                // 根据活动状态提供不同的操作
                switch (activityInfo.state) {
                    case 'planned':
                        actions.push(
                            { id: 'start', icon: 'fa-play', text: _t("Start"), color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: _t("Reschedule"), color: '#17a2b8' }
                        );
                        break;
                    case 'today':
                        actions.push(
                            { id: 'complete', icon: 'fa-check', text: _t("Complete"), color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: _t("Reschedule"), color: '#17a2b8' }
                        );
                        break;
                    case 'overdue':
                        actions.push(
                            { id: 'complete', icon: 'fa-check', text: _t("Complete"), color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: _t("Reschedule"), color: '#17a2b8' }
                        );
                        break;
                    case 'done':
                        actions.push(
                            { id: 'reopen', icon: 'fa-undo', text: _t("Reopen"), color: '#17a2b8' }
                        );
                        break;
                }
                
                // 通用操作
                actions.push(
                    { id: 'edit', icon: 'fa-edit', text: _t("Edit"), color: '#6c757d' }
                );
                
                return actions;
            }
            
            // 执行快速操作
            executeQuickAction(actionId) {
                try {
                    const activityInfo = this.getActivityInfo();
                    if (!activityInfo) return;
                    
                    // 触发相应的操作
                    this.trigger('activity-action', {
                        action: actionId,
                        activity: activityInfo,
                        record: this.props.record
                    });
                } catch (error) {
                    console.error('执行快速操作失败:', error);
                }
            }
            
            // 获取渲染数据
            getRenderData() {
                try {
                    const activityInfo = this.getActivityInfo();
                    const stateInfo = this.getActivityStateInfo();
                    const priorityInfo = this.getActivityPriorityInfo();
                    const deadlineStatus = this.getDeadlineStatus();
                    const badges = this.getActivityBadges();
                    
                    return {
                        activityInfo: activityInfo,
                        stateInfo: stateInfo,
                        priorityInfo: priorityInfo,
                        deadlineStatus: deadlineStatus,
                        badges: badges,
                        summaryText: this.summaryText,
                        tooltipContent: this.getTooltipContent(),
                        quickActions: this.getQuickActions(),
                        config: this.enhancedConfig
                    };
                } catch (error) {
                    console.error('获取渲染数据失败:', error);
                    return {
                        activityInfo: null,
                        stateInfo: null,
                        priorityInfo: null,
                        deadlineStatus: null,
                        badges: [],
                        summaryText: this.summaryText,
                        tooltipContent: '',
                        quickActions: [],
                        config: this.enhancedConfig
                    };
                }
            }
        }
        
        // 增强的字段注册
        const enhancedListActivity = {
            component: EnhancedListActivity,
            fieldDependencies: EnhancedListActivity.fieldDependencies,
            displayName: _t("Enhanced List Activity"),
            supportedTypes: ["one2many"],
            additionalClasses: ["o_list_activity_enhanced"],
            
            // 提取属性
            extractProps: ({ attrs, field }) => {
                const props = {};
                
                // 基础属性
                Object.assign(props, standardFieldProps.extractProps({ attrs, field }));
                
                // 增强配置
                if (attrs.enable_tooltip) {
                    props.enableTooltip = attrs.enable_tooltip;
                }
                
                if (attrs.enable_quick_actions) {
                    props.enableQuickActions = attrs.enable_quick_actions;
                }
                
                if (attrs.compact_mode) {
                    props.compactMode = attrs.compact_mode;
                }
                
                if (attrs.max_summary_length) {
                    props.maxSummaryLength = parseInt(attrs.max_summary_length);
                }
                
                return props;
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("list_activity_enhanced", enhancedListActivity);
    }
};

// 应用列表活动字段增强
ListActivityEnhancer.enhanceListActivity();
```

## 技术特点

### 1. 智能摘要计算
- 优先级的摘要显示逻辑
- 异常状态的优先处理
- 多语言支持的文本显示

### 2. 组件化设计
- 基于OWL组件系统
- 集成ActivityButton子组件
- 清晰的组件层次结构

### 3. 字段依赖管理
- 声明式的字段依赖
- 完整的活动相关字段覆盖
- 类型安全的依赖声明

### 4. 国际化支持
- 使用翻译函数
- 支持多语言显示
- 本地化的用户体验

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可复用的字段实现

### 2. 计算属性模式 (Computed Property Pattern)
- 动态的摘要计算
- 响应式的数据更新

### 3. 依赖注入模式 (Dependency Injection Pattern)
- 字段依赖的声明和注入
- 松耦合的组件设计

## 注意事项

1. **数据依赖**: 确保所有依赖字段的存在和正确性
2. **性能考虑**: 避免复杂的计算影响列表渲染性能
3. **国际化**: 确保所有文本都支持多语言
4. **用户体验**: 提供清晰的活动状态显示

## 扩展建议

1. **更多状态**: 支持更多的活动状态类型
2. **交互功能**: 添加快速操作和编辑功能
3. **徽章显示**: 添加状态和优先级的徽章显示
4. **工具提示**: 实现详细的活动信息工具提示
5. **批量操作**: 支持批量活动操作

该列表活动字段为邮件系统提供了重要的活动可视化功能，是列表视图中活动管理和状态监控的核心组件。

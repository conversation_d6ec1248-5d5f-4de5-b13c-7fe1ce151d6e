# Kanban Activity - 看板活动字段

## 概述

`kanban_activity.js` 是 Odoo 邮件系统的看板活动字段组件，专门用于在看板视图中显示活动相关信息。该组件基于OWL框架和邮件核心活动按钮，集成了活动状态显示、异常处理、类型标识等核心功能，为邮件系统提供了完整的看板活动可视化和交互支持，是邮件系统活动管理和看板视图的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/kanban_activity/kanban_activity.js`
- **行数**: 42
- **模块**: `@mail/views/web/fields/kanban_activity/kanban_activity`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/activity_button'        // 活动按钮组件
'@odoo/owl'                             // OWL 框架
'@web/core/registry'                    // 注册表
'@web/views/fields/standard_field_props' // 标准字段属性
```

## 核心功能

### 1. KanbanActivity 组件

```javascript
const KanbanActivity = class KanbanActivity extends Component {
    static components = { ActivityButton };
    static fieldDependencies = [
        {
            name: "activity_exception_decoration",
            type: "selection",
            selection: [("warning", "Alert"), ("danger", "Error")],
        },
        { name: "activity_exception_icon", type: "char" },
        { name: "activity_state", type: "selection" },
        { name: "activity_summary", type: "char" },
        { name: "activity_type_icon", type: "char" },
        { name: "activity_type_id", type: "many2one", relation: "mail.activity.type" },
    ];
    static props = standardFieldProps;
    static template = "mail.KanbanActivity";
}
```

**组件特性**:
- **组件集成**: 使用ActivityButton作为子组件
- **字段依赖**: 声明多个活动相关字段的依赖
- **标准属性**: 使用标准字段属性
- **模板渲染**: 使用专用的KanbanActivity模板

### 2. 字段依赖声明

```javascript
static fieldDependencies = [
    {
        name: "activity_exception_decoration",
        type: "selection",
        selection: [("warning", "Alert"), ("danger", "Error")],
    },
    { name: "activity_exception_icon", type: "char" },
    { name: "activity_state", type: "selection" },
    { name: "activity_summary", type: "char" },
    { name: "activity_type_icon", type: "char" },
    { name: "activity_type_id", type: "many2one", relation: "mail.activity.type" },
];
```

**依赖字段功能**:
- **异常装饰**: activity_exception_decoration - 活动异常装饰类型
- **异常图标**: activity_exception_icon - 活动异常图标
- **活动状态**: activity_state - 活动当前状态
- **活动摘要**: activity_summary - 活动摘要信息
- **类型图标**: activity_type_icon - 活动类型图标
- **类型ID**: activity_type_id - 活动类型关联

### 3. 字段注册

```javascript
const kanbanActivity = {
    component: KanbanActivity,
    fieldDependencies: KanbanActivity.fieldDependencies,
};

registry.category("fields").add("kanban_activity", kanbanActivity);
```

**注册功能**:
- **组件绑定**: 绑定KanbanActivity组件
- **依赖传递**: 传递字段依赖声明
- **类型注册**: 注册为"kanban_activity"字段类型
- **全局可用**: 使字段在整个系统中可用

## 使用场景

### 1. 看板活动字段增强

```javascript
// 看板活动字段增强功能
const KanbanActivityEnhancer = {
    enhanceKanbanActivity: () => {
        // 增强的看板活动组件
        class EnhancedKanbanActivity extends KanbanActivity {
            static components = {
                ...KanbanActivity.components,
                ActivityTooltip: this.getActivityTooltipComponent(),
                ActivityProgress: this.getActivityProgressComponent(),
                ActivityPriority: this.getActivityPriorityComponent()
            };
            
            static fieldDependencies = [
                ...KanbanActivity.fieldDependencies,
                { name: "activity_date_deadline", type: "datetime" },
                { name: "activity_user_id", type: "many2one", relation: "res.users" },
                { name: "activity_priority", type: "selection" },
                { name: "activity_progress", type: "float" },
                { name: "activity_tags", type: "many2many", relation: "mail.activity.tag" },
                { name: "activity_note", type: "text" },
                { name: "activity_feedback", type: "text" },
                { name: "activity_calendar_event_id", type: "many2one", relation: "calendar.event" }
            ];
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableTooltip: true,
                    enableProgressBar: true,
                    enablePriorityIndicator: true,
                    enableQuickActions: true,
                    enableDragAndDrop: false,
                    enableBulkOperations: false,
                    showActivityTags: true,
                    showAssignedUser: true,
                    showDeadline: true,
                    compactMode: false,
                    animationDuration: 200
                };
                
                // 活动状态映射
                this.activityStates = {
                    'planned': { color: '#17a2b8', icon: 'fa-clock-o', text: '计划中' },
                    'today': { color: '#ffc107', icon: 'fa-exclamation-triangle', text: '今日' },
                    'overdue': { color: '#dc3545', icon: 'fa-exclamation-circle', text: '逾期' },
                    'done': { color: '#28a745', icon: 'fa-check-circle', text: '完成' },
                    'cancelled': { color: '#6c757d', icon: 'fa-times-circle', text: '取消' }
                };
                
                // 优先级映射
                this.activityPriorities = {
                    '0': { color: '#6c757d', icon: 'fa-minus', text: '低' },
                    '1': { color: '#17a2b8', icon: 'fa-circle', text: '正常' },
                    '2': { color: '#ffc107', icon: 'fa-star', text: '高' },
                    '3': { color: '#dc3545', icon: 'fa-fire', text: '紧急' }
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置工具提示
                if (this.enhancedConfig.enableTooltip) {
                    this.setupTooltip();
                }
                
                // 设置快速操作
                if (this.enhancedConfig.enableQuickActions) {
                    this.setupQuickActions();
                }
                
                // 设置拖拽功能
                if (this.enhancedConfig.enableDragAndDrop) {
                    this.setupDragAndDrop();
                }
                
                // 设置批量操作
                if (this.enhancedConfig.enableBulkOperations) {
                    this.setupBulkOperations();
                }
            }
            
            // 获取活动信息
            getActivityInfo() {
                try {
                    const record = this.props.record;
                    const data = record.data;
                    
                    return {
                        id: data.activity_type_id?.[0],
                        state: data.activity_state,
                        summary: data.activity_summary,
                        deadline: data.activity_date_deadline,
                        userId: data.activity_user_id?.[0],
                        userName: data.activity_user_id?.[1],
                        priority: data.activity_priority,
                        progress: data.activity_progress || 0,
                        tags: data.activity_tags || [],
                        note: data.activity_note,
                        feedback: data.activity_feedback,
                        typeIcon: data.activity_type_icon,
                        exceptionIcon: data.activity_exception_icon,
                        exceptionDecoration: data.activity_exception_decoration,
                        calendarEventId: data.activity_calendar_event_id?.[0]
                    };
                } catch (error) {
                    console.error('获取活动信息失败:', error);
                    return null;
                }
            }
            
            // 获取活动状态信息
            getActivityStateInfo() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return null;
                
                const stateInfo = this.activityStates[activityInfo.state];
                return stateInfo ? {
                    ...stateInfo,
                    state: activityInfo.state
                } : null;
            }
            
            // 获取活动优先级信息
            getActivityPriorityInfo() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return null;
                
                const priorityInfo = this.activityPriorities[activityInfo.priority];
                return priorityInfo ? {
                    ...priorityInfo,
                    priority: activityInfo.priority
                } : null;
            }
            
            // 获取截止日期状态
            getDeadlineStatus() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo || !activityInfo.deadline) return null;
                
                const deadline = new Date(activityInfo.deadline);
                const now = new Date();
                const diffDays = Math.ceil((deadline - now) / (1000 * 60 * 60 * 24));
                
                if (diffDays < 0) {
                    return { status: 'overdue', days: Math.abs(diffDays), text: `逾期 ${Math.abs(diffDays)} 天` };
                } else if (diffDays === 0) {
                    return { status: 'today', days: 0, text: '今日到期' };
                } else if (diffDays <= 3) {
                    return { status: 'soon', days: diffDays, text: `${diffDays} 天后到期` };
                } else {
                    return { status: 'future', days: diffDays, text: `${diffDays} 天后到期` };
                }
            }
            
            // 设置工具提示
            setupTooltip() {
                // 这里可以设置工具提示的相关逻辑
                // 实际实现需要与具体的工具提示系统集成
            }
            
            // 获取工具提示内容
            getTooltipContent() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return '';
                
                const parts = [];
                
                // 活动摘要
                if (activityInfo.summary) {
                    parts.push(`摘要: ${activityInfo.summary}`);
                }
                
                // 负责人
                if (activityInfo.userName) {
                    parts.push(`负责人: ${activityInfo.userName}`);
                }
                
                // 截止日期
                if (activityInfo.deadline) {
                    const deadlineStatus = this.getDeadlineStatus();
                    parts.push(`截止日期: ${deadlineStatus.text}`);
                }
                
                // 优先级
                const priorityInfo = this.getActivityPriorityInfo();
                if (priorityInfo) {
                    parts.push(`优先级: ${priorityInfo.text}`);
                }
                
                // 进度
                if (activityInfo.progress > 0) {
                    parts.push(`进度: ${Math.round(activityInfo.progress)}%`);
                }
                
                // 备注
                if (activityInfo.note) {
                    parts.push(`备注: ${activityInfo.note}`);
                }
                
                return parts.join('\n');
            }
            
            // 设置快速操作
            setupQuickActions() {
                // 这里可以设置快速操作的相关逻辑
            }
            
            // 获取快速操作列表
            getQuickActions() {
                const activityInfo = this.getActivityInfo();
                if (!activityInfo) return [];
                
                const actions = [];
                
                // 根据活动状态提供不同的操作
                switch (activityInfo.state) {
                    case 'planned':
                        actions.push(
                            { id: 'start', icon: 'fa-play', text: '开始', color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: '重新安排', color: '#17a2b8' },
                            { id: 'cancel', icon: 'fa-times', text: '取消', color: '#dc3545' }
                        );
                        break;
                    case 'today':
                        actions.push(
                            { id: 'complete', icon: 'fa-check', text: '完成', color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: '重新安排', color: '#17a2b8' },
                            { id: 'add_note', icon: 'fa-sticky-note', text: '添加备注', color: '#ffc107' }
                        );
                        break;
                    case 'overdue':
                        actions.push(
                            { id: 'complete', icon: 'fa-check', text: '完成', color: '#28a745' },
                            { id: 'reschedule', icon: 'fa-calendar', text: '重新安排', color: '#17a2b8' },
                            { id: 'escalate', icon: 'fa-arrow-up', text: '升级', color: '#dc3545' }
                        );
                        break;
                    case 'done':
                        actions.push(
                            { id: 'reopen', icon: 'fa-undo', text: '重新打开', color: '#17a2b8' },
                            { id: 'feedback', icon: 'fa-comment', text: '反馈', color: '#ffc107' }
                        );
                        break;
                }
                
                // 通用操作
                actions.push(
                    { id: 'edit', icon: 'fa-edit', text: '编辑', color: '#6c757d' },
                    { id: 'delete', icon: 'fa-trash', text: '删除', color: '#dc3545' }
                );
                
                return actions;
            }
            
            // 执行快速操作
            executeQuickAction(actionId) {
                try {
                    const activityInfo = this.getActivityInfo();
                    if (!activityInfo) return;
                    
                    switch (actionId) {
                        case 'start':
                            this.startActivity(activityInfo);
                            break;
                        case 'complete':
                            this.completeActivity(activityInfo);
                            break;
                        case 'reschedule':
                            this.rescheduleActivity(activityInfo);
                            break;
                        case 'cancel':
                            this.cancelActivity(activityInfo);
                            break;
                        case 'add_note':
                            this.addNote(activityInfo);
                            break;
                        case 'escalate':
                            this.escalateActivity(activityInfo);
                            break;
                        case 'reopen':
                            this.reopenActivity(activityInfo);
                            break;
                        case 'feedback':
                            this.addFeedback(activityInfo);
                            break;
                        case 'edit':
                            this.editActivity(activityInfo);
                            break;
                        case 'delete':
                            this.deleteActivity(activityInfo);
                            break;
                        default:
                            console.warn('未知的快速操作:', actionId);
                    }
                } catch (error) {
                    console.error('执行快速操作失败:', error);
                }
            }
            
            // 开始活动
            startActivity(activityInfo) {
                console.log('开始活动:', activityInfo);
                // 实现开始活动的逻辑
            }
            
            // 完成活动
            completeActivity(activityInfo) {
                console.log('完成活动:', activityInfo);
                // 实现完成活动的逻辑
            }
            
            // 重新安排活动
            rescheduleActivity(activityInfo) {
                console.log('重新安排活动:', activityInfo);
                // 实现重新安排活动的逻辑
            }
            
            // 取消活动
            cancelActivity(activityInfo) {
                console.log('取消活动:', activityInfo);
                // 实现取消活动的逻辑
            }
            
            // 添加备注
            addNote(activityInfo) {
                console.log('添加备注:', activityInfo);
                // 实现添加备注的逻辑
            }
            
            // 升级活动
            escalateActivity(activityInfo) {
                console.log('升级活动:', activityInfo);
                // 实现升级活动的逻辑
            }
            
            // 重新打开活动
            reopenActivity(activityInfo) {
                console.log('重新打开活动:', activityInfo);
                // 实现重新打开活动的逻辑
            }
            
            // 添加反馈
            addFeedback(activityInfo) {
                console.log('添加反馈:', activityInfo);
                // 实现添加反馈的逻辑
            }
            
            // 编辑活动
            editActivity(activityInfo) {
                console.log('编辑活动:', activityInfo);
                // 实现编辑活动的逻辑
            }
            
            // 删除活动
            deleteActivity(activityInfo) {
                console.log('删除活动:', activityInfo);
                // 实现删除活动的逻辑
            }
            
            // 设置拖拽功能
            setupDragAndDrop() {
                // 实现拖拽功能的设置
                console.log('设置拖拽功能');
            }
            
            // 设置批量操作
            setupBulkOperations() {
                // 实现批量操作的设置
                console.log('设置批量操作');
            }
            
            // 获取活动工具提示组件
            static getActivityTooltipComponent() {
                return class ActivityTooltip extends Component {
                    static template = "mail.ActivityTooltip";
                    static props = ["content", "position"];
                };
            }
            
            // 获取活动进度组件
            static getActivityProgressComponent() {
                return class ActivityProgress extends Component {
                    static template = "mail.ActivityProgress";
                    static props = ["progress", "color"];
                };
            }
            
            // 获取活动优先级组件
            static getActivityPriorityComponent() {
                return class ActivityPriority extends Component {
                    static template = "mail.ActivityPriority";
                    static props = ["priority", "icon", "color"];
                };
            }
            
            // 获取渲染数据
            getRenderData() {
                try {
                    const activityInfo = this.getActivityInfo();
                    const stateInfo = this.getActivityStateInfo();
                    const priorityInfo = this.getActivityPriorityInfo();
                    const deadlineStatus = this.getDeadlineStatus();
                    
                    return {
                        activityInfo: activityInfo,
                        stateInfo: stateInfo,
                        priorityInfo: priorityInfo,
                        deadlineStatus: deadlineStatus,
                        tooltipContent: this.getTooltipContent(),
                        quickActions: this.getQuickActions(),
                        config: this.enhancedConfig
                    };
                } catch (error) {
                    console.error('获取渲染数据失败:', error);
                    return {
                        activityInfo: null,
                        stateInfo: null,
                        priorityInfo: null,
                        deadlineStatus: null,
                        tooltipContent: '',
                        quickActions: [],
                        config: this.enhancedConfig
                    };
                }
            }
        }
        
        // 增强的字段注册
        const enhancedKanbanActivity = {
            component: EnhancedKanbanActivity,
            fieldDependencies: EnhancedKanbanActivity.fieldDependencies,
            additionalClasses: ["o_kanban_activity_enhanced"],
            
            // 提取属性
            extractProps: ({ attrs, field }) => {
                const props = {};
                
                // 基础属性
                Object.assign(props, standardFieldProps.extractProps({ attrs, field }));
                
                // 增强配置
                if (attrs.enable_tooltip) {
                    props.enableTooltip = attrs.enable_tooltip;
                }
                
                if (attrs.enable_quick_actions) {
                    props.enableQuickActions = attrs.enable_quick_actions;
                }
                
                if (attrs.compact_mode) {
                    props.compactMode = attrs.compact_mode;
                }
                
                return props;
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("kanban_activity_enhanced", enhancedKanbanActivity);
    }
};

// 应用看板活动字段增强
KanbanActivityEnhancer.enhanceKanbanActivity();
```

## 技术特点

### 1. 组件化设计
- 基于OWL组件系统
- 集成ActivityButton子组件
- 清晰的组件层次结构

### 2. 字段依赖管理
- 声明式的字段依赖
- 完整的活动相关字段覆盖
- 类型安全的依赖声明

### 3. 标准化集成
- 使用标准字段属性
- 遵循字段系统规范
- 统一的注册机制

### 4. 模板驱动
- 专用的模板系统
- 灵活的渲染控制
- 可定制的显示效果

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可复用的字段实现

### 2. 依赖注入模式 (Dependency Injection Pattern)
- 字段依赖的声明和注入
- 松耦合的组件设计

### 3. 模板模式 (Template Pattern)
- 模板驱动的渲染
- 可定制的显示逻辑

## 注意事项

1. **数据依赖**: 确保所有依赖字段的存在和正确性
2. **性能考虑**: 避免复杂的计算影响看板渲染性能
3. **用户体验**: 提供直观的活动状态显示
4. **响应式设计**: 适配不同屏幕尺寸的看板视图

## 扩展建议

1. **更多状态**: 支持更多的活动状态类型
2. **交互功能**: 添加快速操作和编辑功能
3. **进度显示**: 添加活动进度的可视化显示
4. **批量操作**: 支持批量活动操作
5. **拖拽功能**: 实现活动的拖拽重新安排

该看板活动字段为邮件系统提供了重要的活动可视化功能，是看板视图中活动管理和状态监控的核心组件。

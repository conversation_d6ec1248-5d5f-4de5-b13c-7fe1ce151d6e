# Avatar - 头像组件

## 概述

`avatar.js` 是 Odoo 邮件系统的头像组件，专门用于显示用户或联系人的头像，并提供点击查看详细信息的功能。该组件基于OWL框架和Web弹出框系统，集成了头像显示、弹出卡片、交互处理等核心功能，为邮件系统提供了完整的头像展示和用户信息查看支持，是邮件系统用户界面和用户体验的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/avatar/avatar.js`
- **行数**: 40
- **模块**: `@mail/views/web/fields/avatar/avatar`

## 依赖关系

```javascript
// 核心依赖
'@web/core/popover/popover_hook'                    // 弹出框钩子
'@mail/discuss/web/avatar_card/avatar_card_popover' // 头像卡片弹出框
'@odoo/owl'                                         // OWL 框架
```

## 核心功能

### 1. Avatar 组件

```javascript
class Avatar extends Component {
    static template = "mail.Avatar";
    static props = {
        resModel: { type: String },
        resId: { type: Number },
        displayName: { type: String },
        noSpacing: { type: Boolean, optional: true },
    };

    setup() {
        this.avatarCard = usePopover(AvatarCardPopover);
    }

    onClickAvatar(ev) {
        if (this.env.isSmall || !this.props.resId) {
            return;
        }
        const target = ev.currentTarget;
        if (!this.avatarCard.isOpen) {
            this.avatarCard.open(target, {
                id: this.props.resId,
            });
        }
    }
}
```

**组件特性**:
- **模板渲染**: 使用专用的Avatar模板
- **属性定义**: 定义资源模型、ID、显示名称等属性
- **弹出框集成**: 集成头像卡片弹出框
- **交互处理**: 处理点击事件显示详细信息

### 2. 组件属性

```javascript
static props = {
    resModel: { type: String },        // 资源模型名称
    resId: { type: Number },           // 资源记录ID
    displayName: { type: String },     // 显示名称
    noSpacing: { type: Boolean, optional: true }, // 是否无间距
};
```

**属性功能**:
- **资源标识**: 通过resModel和resId标识具体资源
- **显示控制**: 通过displayName控制显示文本
- **样式控制**: 通过noSpacing控制间距样式
- **类型安全**: 严格的属性类型定义

### 3. 弹出框设置

```javascript
setup() {
    this.avatarCard = usePopover(AvatarCardPopover);
}
```

**设置功能**:
- **钩子使用**: 使用usePopover钩子
- **组件绑定**: 绑定AvatarCardPopover组件
- **生命周期**: 在组件设置阶段初始化
- **状态管理**: 管理弹出框的状态

### 4. 点击事件处理

```javascript
onClickAvatar(ev) {
    if (this.env.isSmall || !this.props.resId) {
        return;
    }
    const target = ev.currentTarget;
    if (!this.avatarCard.isOpen) {
        this.avatarCard.open(target, {
            id: this.props.resId,
        });
    }
}
```

**点击处理功能**:
- **条件检查**: 检查屏幕尺寸和资源ID
- **重复防护**: 防止重复打开弹出框
- **目标定位**: 使用点击目标作为弹出框锚点
- **数据传递**: 传递资源ID给弹出框

## 使用场景

### 1. 头像组件增强

```javascript
// 头像组件增强功能
const AvatarEnhancer = {
    enhanceAvatar: () => {
        // 增强的头像组件
        class EnhancedAvatar extends Avatar {
            static props = {
                ...Avatar.props,
                size: { type: String, optional: true },
                shape: { type: String, optional: true },
                status: { type: String, optional: true },
                badge: { type: String, optional: true },
                enableHover: { type: Boolean, optional: true },
                enableClick: { type: Boolean, optional: true },
                customClass: { type: String, optional: true },
                fallbackIcon: { type: String, optional: true },
                showTooltip: { type: Boolean, optional: true },
                tooltipPosition: { type: String, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    sizes: {
                        'xs': '24px',
                        'sm': '32px',
                        'md': '48px',
                        'lg': '64px',
                        'xl': '96px'
                    },
                    shapes: ['circle', 'square', 'rounded'],
                    statuses: ['online', 'offline', 'away', 'busy'],
                    defaultSize: 'md',
                    defaultShape: 'circle',
                    enableAnimation: true,
                    enableLazyLoad: true
                };
                
                // 状态管理
                this.state = {
                    isLoaded: false,
                    hasError: false,
                    isHovered: false
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置懒加载
                if (this.enhancedConfig.enableLazyLoad) {
                    this.setupLazyLoad();
                }
                
                // 设置工具提示
                if (this.props.showTooltip) {
                    this.setupTooltip();
                }
                
                // 设置悬停效果
                if (this.props.enableHover !== false) {
                    this.setupHoverEffects();
                }
            }
            
            // 获取头像尺寸
            get avatarSize() {
                const size = this.props.size || this.enhancedConfig.defaultSize;
                return this.enhancedConfig.sizes[size] || size;
            }
            
            // 获取头像形状
            get avatarShape() {
                return this.props.shape || this.enhancedConfig.defaultShape;
            }
            
            // 获取头像URL
            get avatarUrl() {
                try {
                    if (!this.props.resModel || !this.props.resId) {
                        return this.getFallbackUrl();
                    }
                    
                    // 构建头像URL
                    const baseUrl = '/web/image';
                    const params = new URLSearchParams({
                        model: this.props.resModel,
                        id: this.props.resId,
                        field: 'avatar_128',
                        unique: Date.now() // 防止缓存
                    });
                    
                    return `${baseUrl}?${params.toString()}`;
                } catch (error) {
                    console.error('获取头像URL失败:', error);
                    return this.getFallbackUrl();
                }
            }
            
            // 获取备用URL
            getFallbackUrl() {
                if (this.props.fallbackIcon) {
                    return `/web/static/src/img/icons/${this.props.fallbackIcon}.png`;
                }
                
                // 根据模型类型返回不同的默认头像
                switch (this.props.resModel) {
                    case 'res.users':
                        return '/web/static/src/img/user_menu_avatar.png';
                    case 'res.partner':
                        return '/web/static/src/img/company_image.png';
                    default:
                        return '/web/static/src/img/placeholder.png';
                }
            }
            
            // 获取头像样式
            get avatarStyle() {
                const styles = {
                    width: this.avatarSize,
                    height: this.avatarSize,
                    borderRadius: this.getBorderRadius()
                };
                
                // 添加动画
                if (this.enhancedConfig.enableAnimation) {
                    styles.transition = 'all 0.3s ease';
                }
                
                // 添加悬停效果
                if (this.state.isHovered && this.props.enableHover !== false) {
                    styles.transform = 'scale(1.05)';
                    styles.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                }
                
                return styles;
            }
            
            // 获取边框半径
            getBorderRadius() {
                switch (this.avatarShape) {
                    case 'circle':
                        return '50%';
                    case 'square':
                        return '0';
                    case 'rounded':
                        return '8px';
                    default:
                        return '50%';
                }
            }
            
            // 获取CSS类
            get avatarClass() {
                const classes = ['o_avatar'];
                
                // 添加尺寸类
                if (this.props.size) {
                    classes.push(`o_avatar_${this.props.size}`);
                }
                
                // 添加形状类
                if (this.avatarShape) {
                    classes.push(`o_avatar_${this.avatarShape}`);
                }
                
                // 添加状态类
                if (this.props.status) {
                    classes.push(`o_avatar_${this.props.status}`);
                }
                
                // 添加自定义类
                if (this.props.customClass) {
                    classes.push(this.props.customClass);
                }
                
                // 添加状态类
                if (this.state.isLoaded) {
                    classes.push('o_avatar_loaded');
                }
                
                if (this.state.hasError) {
                    classes.push('o_avatar_error');
                }
                
                if (this.state.isHovered) {
                    classes.push('o_avatar_hovered');
                }
                
                // 添加间距类
                if (!this.props.noSpacing) {
                    classes.push('o_avatar_spaced');
                }
                
                return classes.join(' ');
            }
            
            // 增强的点击处理
            onClickAvatar(ev) {
                try {
                    // 检查是否启用点击
                    if (this.props.enableClick === false) {
                        return;
                    }
                    
                    // 调用父类方法
                    super.onClickAvatar(ev);
                    
                    // 触发自定义事件
                    this.trigger('avatar-clicked', {
                        resModel: this.props.resModel,
                        resId: this.props.resId,
                        displayName: this.props.displayName
                    });
                } catch (error) {
                    console.error('处理头像点击失败:', error);
                }
            }
            
            // 处理图片加载
            onImageLoad() {
                this.state.isLoaded = true;
                this.state.hasError = false;
            }
            
            // 处理图片错误
            onImageError() {
                this.state.hasError = true;
                this.state.isLoaded = false;
            }
            
            // 处理鼠标悬停
            onMouseEnter() {
                if (this.props.enableHover !== false) {
                    this.state.isHovered = true;
                }
            }
            
            // 处理鼠标离开
            onMouseLeave() {
                this.state.isHovered = false;
            }
            
            // 设置懒加载
            setupLazyLoad() {
                // 这里可以实现图片懒加载逻辑
                // 例如使用Intersection Observer API
            }
            
            // 设置工具提示
            setupTooltip() {
                // 这里可以设置工具提示逻辑
                // 与具体的工具提示系统集成
            }
            
            // 设置悬停效果
            setupHoverEffects() {
                // 这里可以设置更复杂的悬停效果
                // 例如动画、阴影等
            }
            
            // 获取工具提示文本
            getTooltipText() {
                if (this.props.displayName) {
                    return this.props.displayName;
                }
                
                return `${this.props.resModel} #${this.props.resId}`;
            }
            
            // 获取状态指示器
            getStatusIndicator() {
                if (!this.props.status) {
                    return null;
                }
                
                const statusConfig = {
                    online: { color: '#4CAF50', text: '在线' },
                    offline: { color: '#9E9E9E', text: '离线' },
                    away: { color: '#FF9800', text: '离开' },
                    busy: { color: '#F44336', text: '忙碌' }
                };
                
                return statusConfig[this.props.status] || null;
            }
            
            // 获取徽章信息
            getBadgeInfo() {
                if (!this.props.badge) {
                    return null;
                }
                
                return {
                    text: this.props.badge,
                    class: 'o_avatar_badge'
                };
            }
            
            // 获取渲染数据
            getRenderData() {
                return {
                    avatarUrl: this.avatarUrl,
                    avatarStyle: this.avatarStyle,
                    avatarClass: this.avatarClass,
                    displayName: this.props.displayName,
                    tooltipText: this.getTooltipText(),
                    statusIndicator: this.getStatusIndicator(),
                    badgeInfo: this.getBadgeInfo(),
                    showTooltip: this.props.showTooltip,
                    enableClick: this.props.enableClick !== false,
                    enableHover: this.props.enableHover !== false
                };
            }
        }
        
        // 导出增强的头像组件
        __exports.EnhancedAvatar = EnhancedAvatar;
    }
};

// 应用头像组件增强
AvatarEnhancer.enhanceAvatar();
```

## 技术特点

### 1. 组件化设计
- 基于OWL组件系统
- 清晰的属性定义
- 模板驱动的渲染

### 2. 弹出框集成
- 使用usePopover钩子
- 智能的弹出框管理
- 响应式的交互体验

### 3. 条件渲染
- 基于屏幕尺寸的条件判断
- 资源ID的有效性检查
- 防重复操作的保护

### 4. 事件处理
- 标准的DOM事件处理
- 组件间的数据传递
- 用户交互的响应

## 设计模式

### 1. 组件模式 (Component Pattern)
- 封装的UI组件
- 可复用的头像实现

### 2. 代理模式 (Proxy Pattern)
- 弹出框的代理管理
- 延迟加载的实现

### 3. 观察者模式 (Observer Pattern)
- 事件驱动的交互
- 状态变化的响应

## 注意事项

1. **性能考虑**: 避免频繁的弹出框创建和销毁
2. **响应式设计**: 考虑不同屏幕尺寸的适配
3. **数据有效性**: 确保资源ID的有效性
4. **用户体验**: 提供流畅的交互体验

## 扩展建议

1. **尺寸选项**: 支持多种头像尺寸选择
2. **形状变化**: 支持圆形、方形等不同形状
3. **状态指示**: 添加在线状态等指示器
4. **懒加载**: 实现图片的懒加载功能
5. **自定义样式**: 支持自定义的头像样式

该头像组件为邮件系统提供了重要的用户身份可视化功能，是用户界面和用户体验的核心组件。

# Emojis Char Field - 表情符号字符字段

## 概述

`emojis_char_field.js` 是 Odoo 邮件系统的表情符号字符字段组件，专门为字符字段添加表情符号支持功能。该组件基于Web框架的字符字段和表情符号通用字段，集成了表情符号选择、输入增强、模板渲染等核心功能，为邮件系统提供了完整的表情符号输入和显示支持，是邮件系统用户表达和内容丰富化的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/emojis_char_field/emojis_char_field.js`
- **行数**: 41
- **模块**: `@mail/views/web/fields/emojis_char_field/emojis_char_field`

## 依赖关系

```javascript
// 核心依赖
'@mail/views/web/fields/emojis_field_common/emojis_field_common' // 表情符号通用字段
'@odoo/owl'                                                      // OWL 框架
'@web/core/registry'                                             // 注册表
'@web/views/fields/char/char_field'                             // 字符字段
```

## 核心功能

### 1. EmojisCharField 类

```javascript
const EmojisCharField = class EmojisCharField extends EmojisFieldCommon(CharField) {
    static template = "mail.EmojisCharField";
    static components = { ...CharField.components };
    
    setup() {
        super.setup();
        this.targetEditElement = useRef("input");
        this._setupOverride();
    }

    get shouldTrim() {
        return false;
    }
}
```

**组件特性**:
- **多重继承**: 继承EmojisFieldCommon和CharField的功能
- **模板渲染**: 使用专用的EmojisCharField模板
- **组件集成**: 继承CharField的所有组件
- **输入引用**: 使用useRef引用输入元素
- **设置重写**: 调用_setupOverride进行额外设置

### 2. 输入元素引用

```javascript
setup() {
    super.setup();
    this.targetEditElement = useRef("input");
    this._setupOverride();
}
```

**引用功能**:
- **元素绑定**: 绑定到input元素
- **直接访问**: 提供对输入元素的直接访问
- **表情符号插入**: 支持表情符号的插入操作
- **光标管理**: 管理光标位置和选择

### 3. 修剪控制

```javascript
get shouldTrim() {
    return false;
}
```

**修剪控制功能**:
- **空格保留**: 不自动修剪输入内容的空格
- **格式保持**: 保持用户输入的原始格式
- **表情符号保护**: 防止表情符号被意外修剪
- **内容完整性**: 确保内容的完整性

### 4. 字段注册

```javascript
const emojisCharField = {
    ...charField,
    component: EmojisCharField,
    additionalClasses: [...(charField.additionalClasses || []), "o_field_text"],
};

registry.category("fields").add("char_emojis", emojisCharField);
```

**注册功能**:
- **配置继承**: 继承charField的所有配置
- **组件替换**: 使用EmojisCharField组件
- **样式增强**: 添加额外的CSS类
- **类型注册**: 注册为"char_emojis"字段类型

## 使用场景

### 1. 表情符号字符字段增强

```javascript
// 表情符号字符字段增强功能
const EmojisCharFieldEnhancer = {
    enhanceEmojisCharField: () => {
        // 增强的表情符号字符字段
        class EnhancedEmojisCharField extends EmojisCharField {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableAutoComplete: true,
                    enableShortcuts: true,
                    enableRecentEmojis: true,
                    enableCustomEmojis: false,
                    maxRecentEmojis: 20,
                    emojiCategories: ['people', 'nature', 'food', 'activity', 'travel', 'objects', 'symbols', 'flags'],
                    shortcutTrigger: ':',
                    autoCompleteMinLength: 2,
                    showEmojiTooltip: true
                };
                
                // 最近使用的表情符号
                this.recentEmojis = this.loadRecentEmojis();
                
                // 表情符号快捷方式映射
                this.emojiShortcuts = new Map([
                    [':)', '😊'],
                    [':(', '😢'],
                    [':D', '😃'],
                    [':P', '😛'],
                    [';)', '😉'],
                    ['<3', '❤️'],
                    [':o', '😮'],
                    [':|', '😐']
                ]);
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自动完成
                if (this.enhancedConfig.enableAutoComplete) {
                    this.setupAutoComplete();
                }
                
                // 设置快捷方式
                if (this.enhancedConfig.enableShortcuts) {
                    this.setupShortcuts();
                }
                
                // 设置最近表情符号
                if (this.enhancedConfig.enableRecentEmojis) {
                    this.setupRecentEmojis();
                }
                
                // 设置工具提示
                if (this.enhancedConfig.showEmojiTooltip) {
                    this.setupEmojiTooltip();
                }
            }
            
            // 设置自动完成
            setupAutoComplete() {
                const input = this.targetEditElement.el;
                if (!input) return;
                
                input.addEventListener('input', (event) => {
                    this.handleAutoComplete(event);
                });
                
                input.addEventListener('keydown', (event) => {
                    this.handleAutoCompleteKeydown(event);
                });
            }
            
            // 处理自动完成
            handleAutoComplete(event) {
                try {
                    const input = event.target;
                    const value = input.value;
                    const cursorPos = input.selectionStart;
                    
                    // 查找触发字符
                    const triggerIndex = value.lastIndexOf(this.enhancedConfig.shortcutTrigger, cursorPos - 1);
                    if (triggerIndex === -1) {
                        this.hideAutoComplete();
                        return;
                    }
                    
                    // 提取搜索词
                    const searchTerm = value.substring(triggerIndex + 1, cursorPos);
                    if (searchTerm.length < this.enhancedConfig.autoCompleteMinLength) {
                        this.hideAutoComplete();
                        return;
                    }
                    
                    // 搜索匹配的表情符号
                    const matches = this.searchEmojis(searchTerm);
                    if (matches.length > 0) {
                        this.showAutoComplete(matches, triggerIndex, cursorPos);
                    } else {
                        this.hideAutoComplete();
                    }
                } catch (error) {
                    console.error('处理自动完成失败:', error);
                }
            }
            
            // 搜索表情符号
            searchEmojis(searchTerm) {
                try {
                    const term = searchTerm.toLowerCase();
                    const matches = [];
                    
                    // 搜索表情符号数据库（这里简化处理）
                    const emojiDatabase = this.getEmojiDatabase();
                    
                    for (const emoji of emojiDatabase) {
                        if (emoji.name.toLowerCase().includes(term) || 
                            emoji.keywords.some(keyword => keyword.toLowerCase().includes(term))) {
                            matches.push(emoji);
                            if (matches.length >= 10) break; // 限制结果数量
                        }
                    }
                    
                    return matches;
                } catch (error) {
                    console.error('搜索表情符号失败:', error);
                    return [];
                }
            }
            
            // 获取表情符号数据库
            getEmojiDatabase() {
                // 这里应该返回完整的表情符号数据库
                // 简化处理，返回一些示例数据
                return [
                    { name: 'smile', emoji: '😊', keywords: ['happy', 'face'] },
                    { name: 'heart', emoji: '❤️', keywords: ['love', 'red'] },
                    { name: 'thumbs_up', emoji: '👍', keywords: ['good', 'like'] },
                    { name: 'fire', emoji: '🔥', keywords: ['hot', 'flame'] },
                    { name: 'star', emoji: '⭐', keywords: ['favorite', 'rating'] }
                ];
            }
            
            // 显示自动完成
            showAutoComplete(matches, startPos, endPos) {
                // 这里应该显示自动完成下拉列表
                // 实际实现需要创建DOM元素和定位
                console.log('显示自动完成:', matches);
            }
            
            // 隐藏自动完成
            hideAutoComplete() {
                // 这里应该隐藏自动完成下拉列表
                console.log('隐藏自动完成');
            }
            
            // 设置快捷方式
            setupShortcuts() {
                const input = this.targetEditElement.el;
                if (!input) return;
                
                input.addEventListener('input', (event) => {
                    this.handleShortcuts(event);
                });
            }
            
            // 处理快捷方式
            handleShortcuts(event) {
                try {
                    const input = event.target;
                    const value = input.value;
                    const cursorPos = input.selectionStart;
                    
                    // 检查快捷方式
                    for (const [shortcut, emoji] of this.emojiShortcuts) {
                        const shortcutPos = value.lastIndexOf(shortcut, cursorPos);
                        if (shortcutPos !== -1 && shortcutPos + shortcut.length === cursorPos) {
                            // 替换快捷方式为表情符号
                            const newValue = value.substring(0, shortcutPos) + emoji + value.substring(cursorPos);
                            input.value = newValue;
                            
                            // 设置光标位置
                            const newCursorPos = shortcutPos + emoji.length;
                            input.setSelectionRange(newCursorPos, newCursorPos);
                            
                            // 触发输入事件
                            input.dispatchEvent(new Event('input', { bubbles: true }));
                            
                            // 记录使用的表情符号
                            this.recordEmojiUsage(emoji);
                            break;
                        }
                    }
                } catch (error) {
                    console.error('处理快捷方式失败:', error);
                }
            }
            
            // 设置最近表情符号
            setupRecentEmojis() {
                // 这里可以设置最近使用表情符号的相关逻辑
            }
            
            // 记录表情符号使用
            recordEmojiUsage(emoji) {
                try {
                    if (!this.enhancedConfig.enableRecentEmojis) return;
                    
                    // 移除已存在的
                    const index = this.recentEmojis.indexOf(emoji);
                    if (index !== -1) {
                        this.recentEmojis.splice(index, 1);
                    }
                    
                    // 添加到开头
                    this.recentEmojis.unshift(emoji);
                    
                    // 限制数量
                    if (this.recentEmojis.length > this.enhancedConfig.maxRecentEmojis) {
                        this.recentEmojis = this.recentEmojis.slice(0, this.enhancedConfig.maxRecentEmojis);
                    }
                    
                    // 保存到本地存储
                    this.saveRecentEmojis();
                } catch (error) {
                    console.error('记录表情符号使用失败:', error);
                }
            }
            
            // 加载最近表情符号
            loadRecentEmojis() {
                try {
                    const stored = localStorage.getItem('mail_recent_emojis');
                    return stored ? JSON.parse(stored) : [];
                } catch (error) {
                    console.error('加载最近表情符号失败:', error);
                    return [];
                }
            }
            
            // 保存最近表情符号
            saveRecentEmojis() {
                try {
                    localStorage.setItem('mail_recent_emojis', JSON.stringify(this.recentEmojis));
                } catch (error) {
                    console.error('保存最近表情符号失败:', error);
                }
            }
            
            // 设置表情符号工具提示
            setupEmojiTooltip() {
                const input = this.targetEditElement.el;
                if (!input) return;
                
                input.addEventListener('mouseover', (event) => {
                    this.handleEmojiTooltip(event);
                });
                
                input.addEventListener('mouseout', (event) => {
                    this.hideEmojiTooltip();
                });
            }
            
            // 处理表情符号工具提示
            handleEmojiTooltip(event) {
                try {
                    const input = event.target;
                    const value = input.value;
                    const rect = input.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    
                    // 计算字符位置（简化处理）
                    const charIndex = Math.floor(x / 8); // 假设每个字符8px宽
                    const char = value[charIndex];
                    
                    // 检查是否为表情符号
                    if (this.isEmoji(char)) {
                        this.showEmojiTooltip(char, event.clientX, event.clientY);
                    } else {
                        this.hideEmojiTooltip();
                    }
                } catch (error) {
                    console.error('处理表情符号工具提示失败:', error);
                }
            }
            
            // 检查是否为表情符号
            isEmoji(char) {
                // 简化的表情符号检测
                const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u;
                return emojiRegex.test(char);
            }
            
            // 显示表情符号工具提示
            showEmojiTooltip(emoji, x, y) {
                // 这里应该显示表情符号的详细信息
                console.log(`显示表情符号工具提示: ${emoji} at (${x}, ${y})`);
            }
            
            // 隐藏表情符号工具提示
            hideEmojiTooltip() {
                // 这里应该隐藏工具提示
                console.log('隐藏表情符号工具提示');
            }
            
            // 插入表情符号
            insertEmoji(emoji) {
                try {
                    const input = this.targetEditElement.el;
                    if (!input) return;
                    
                    const start = input.selectionStart;
                    const end = input.selectionEnd;
                    const value = input.value;
                    
                    // 插入表情符号
                    const newValue = value.substring(0, start) + emoji + value.substring(end);
                    input.value = newValue;
                    
                    // 设置光标位置
                    const newCursorPos = start + emoji.length;
                    input.setSelectionRange(newCursorPos, newCursorPos);
                    
                    // 触发输入事件
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // 记录使用
                    this.recordEmojiUsage(emoji);
                    
                    // 聚焦输入框
                    input.focus();
                } catch (error) {
                    console.error('插入表情符号失败:', error);
                }
            }
            
            // 获取最近使用的表情符号
            getRecentEmojis() {
                return this.recentEmojis.slice();
            }
            
            // 清除最近使用的表情符号
            clearRecentEmojis() {
                this.recentEmojis = [];
                this.saveRecentEmojis();
            }
        }
        
        // 增强的字段注册
        const enhancedEmojisCharField = {
            ...emojisCharField,
            component: EnhancedEmojisCharField,
            additionalClasses: [...(emojisCharField.additionalClasses || []), "o_field_emojis_enhanced"],
            
            // 提取属性
            extractProps: ({ attrs, field }) => {
                const props = {};
                
                // 基础属性
                Object.assign(props, charField.extractProps({ attrs, field }));
                
                // 增强配置
                if (attrs.enable_auto_complete) {
                    props.enableAutoComplete = attrs.enable_auto_complete;
                }
                
                if (attrs.enable_shortcuts) {
                    props.enableShortcuts = attrs.enable_shortcuts;
                }
                
                if (attrs.enable_recent_emojis) {
                    props.enableRecentEmojis = attrs.enable_recent_emojis;
                }
                
                if (attrs.emoji_categories) {
                    props.emojiCategories = JSON.parse(attrs.emoji_categories);
                }
                
                return props;
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("char_emojis_enhanced", enhancedEmojisCharField);
    }
};

// 应用表情符号字符字段增强
EmojisCharFieldEnhancer.enhanceEmojisCharField();
```

## 技术特点

### 1. 多重继承
- 继承EmojisFieldCommon的表情符号功能
- 继承CharField的字符输入功能
- 组合两者的优势特性

### 2. 模板系统
- 使用专用的EmojisCharField模板
- 继承CharField的组件系统
- 保持UI的一致性

### 3. 输入增强
- 直接引用输入元素
- 支持表情符号插入
- 保持输入格式完整性

### 4. 字段注册
- 标准的字段注册机制
- 配置继承和扩展
- 全局可用的字段类型

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- EmojisFieldCommon作为功能混入
- 增强现有字段的功能

### 2. 装饰器模式 (Decorator Pattern)
- 为CharField添加表情符号功能
- 不改变原有结构

### 3. 组合模式 (Composition Pattern)
- 组合多个功能组件
- 创建复合功能字段

## 注意事项

1. **输入处理**: 确保表情符号的正确输入和显示
2. **格式保持**: 避免自动修剪影响表情符号
3. **兼容性**: 确保与不同浏览器的兼容性
4. **性能考虑**: 避免复杂的表情符号处理影响性能

## 扩展建议

1. **自动完成**: 添加表情符号的自动完成功能
2. **快捷输入**: 支持表情符号的快捷输入方式
3. **最近使用**: 记录和显示最近使用的表情符号
4. **自定义表情**: 支持自定义表情符号
5. **搜索功能**: 提供表情符号的搜索功能

该表情符号字符字段为邮件系统提供了重要的表达增强功能，是用户交流和内容丰富化的核心组件。

# HTML Composer Message Field - HTML撰写消息字段

## 概述

`html_composer_message_field.js` 是 Odoo 邮件系统的HTML撰写消息字段组件，专门用于邮件撰写界面的富文本编辑功能。该组件基于HTML邮件字段和HTML编辑器插件系统，集成了提及功能、动态占位符、附件处理等核心功能，为邮件系统提供了完整的邮件撰写和编辑支持，是邮件系统内容创作和用户交互的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/html_composer_message_field/html_composer_message_field.js`
- **行数**: 46
- **模块**: `@mail/views/web/fields/html_composer_message_field/html_composer_message_field`

## 依赖关系

```javascript
// 核心依赖
'@html_editor/plugin_sets'                                                    // HTML编辑器插件集
'@web/core/registry'                                                          // 注册表
'@mail/views/web/fields/html_mail_field/html_mail_field'                     // HTML邮件字段
'@mail/views/web/fields/html_composer_message_field/mention_plugin'          // 提及插件
```

## 核心功能

### 1. HtmlComposerMessageField 类

```javascript
const HtmlComposerMessageField = class HtmlComposerMessageField extends HtmlMailField {
    getConfig() {
        const config = super.getConfig(...arguments);
        config.Plugins = [...config.Plugins, MentionPlugin];
        
        if (!this.props.record.data.composition_batch) {
            config.Plugins = config.Plugins.filter(
                (plugin) => !DYNAMIC_PLACEHOLDER_PLUGINS.includes(plugin)
            );
        }
        
        config.onAttachmentChange = (attachment) => {
            // 附件处理逻辑
        };
        
        return config;
    }
}
```

**组件特性**:
- **继承扩展**: 继承HtmlMailField的所有功能
- **插件增强**: 添加MentionPlugin提及功能
- **条件插件**: 根据composition_batch条件加载插件
- **附件处理**: 集成附件变更处理逻辑

### 2. 配置获取

```javascript
getConfig() {
    const config = super.getConfig(...arguments);
    config.Plugins = [...config.Plugins, MentionPlugin];
    
    if (!this.props.record.data.composition_batch) {
        config.Plugins = config.Plugins.filter(
            (plugin) => !DYNAMIC_PLACEHOLDER_PLUGINS.includes(plugin)
        );
    }
    
    config.onAttachmentChange = (attachment) => {
        // 附件处理逻辑
    };
    
    return config;
}
```

**配置功能**:
- **基础配置**: 获取父类的基础配置
- **插件添加**: 添加MentionPlugin到插件列表
- **动态过滤**: 根据composition_batch过滤动态占位符插件
- **回调设置**: 设置附件变更回调函数

### 3. 附件处理

```javascript
config.onAttachmentChange = (attachment) => {
    if (
        !(
            this.props.record.fieldNames.includes("attachment_ids") &&
            this.props.record.resModel === "mail.compose.message"
        )
    ) {
        return;
    }
    this.props.record.data.attachment_ids.linkTo(attachment.id, attachment);
};
```

**附件处理功能**:
- **条件检查**: 检查记录是否包含attachment_ids字段
- **模型验证**: 验证记录模型为mail.compose.message
- **关系链接**: 将附件链接到记录的attachment_ids字段
- **撰写专用**: 仅在邮件撰写场景下生效

### 4. 字段注册

```javascript
const htmlComposerMessageField = {
    ...htmlMailField,
    component: HtmlComposerMessageField,
};

registry.category("fields").add("html_composer_message", htmlComposerMessageField);
```

**注册功能**:
- **配置继承**: 继承htmlMailField的所有配置
- **组件替换**: 使用HtmlComposerMessageField组件
- **类型注册**: 注册为"html_composer_message"字段类型
- **全局可用**: 使字段在整个系统中可用

## 使用场景

### 1. HTML撰写消息字段增强

```javascript
// HTML撰写消息字段增强功能
const HtmlComposerMessageFieldEnhancer = {
    enhanceHtmlComposerMessageField: () => {
        // 增强的HTML撰写消息字段
        class EnhancedHtmlComposerMessageField extends HtmlComposerMessageField {
            getConfig() {
                const config = super.getConfig(...arguments);
                
                // 增强的配置选项
                const enhancedConfig = {
                    enableAutoSave: true,
                    autoSaveInterval: 30000, // 30秒
                    enableTemplates: true,
                    enableSignature: true,
                    enableSpellCheck: true,
                    enableWordCount: true,
                    enableReadingTime: true,
                    maxAttachmentSize: 25 * 1024 * 1024, // 25MB
                    allowedAttachmentTypes: ['image/*', 'application/pdf', 'text/*'],
                    enableDragDropAttachment: true,
                    enableInlineImages: true,
                    enableLinkPreview: true,
                    enableEmojiPicker: true
                };
                
                // 合并增强配置
                Object.assign(config, enhancedConfig);
                
                // 增强插件列表
                config.Plugins = [
                    ...config.Plugins,
                    this.getAutoSavePlugin(),
                    this.getTemplatePlugin(),
                    this.getSignaturePlugin(),
                    this.getSpellCheckPlugin(),
                    this.getWordCountPlugin(),
                    this.getLinkPreviewPlugin(),
                    this.getEmojiPickerPlugin()
                ].filter(Boolean);
                
                // 增强附件处理
                const originalOnAttachmentChange = config.onAttachmentChange;
                config.onAttachmentChange = (attachment) => {
                    try {
                        // 执行原有逻辑
                        if (originalOnAttachmentChange) {
                            originalOnAttachmentChange(attachment);
                        }
                        
                        // 增强的附件处理
                        this.handleEnhancedAttachment(attachment);
                    } catch (error) {
                        console.error('处理附件变更失败:', error);
                    }
                };
                
                // 添加其他回调
                config.onContentChange = (content) => {
                    this.handleContentChange(content);
                };
                
                config.onSelectionChange = (selection) => {
                    this.handleSelectionChange(selection);
                };
                
                config.onFocus = () => {
                    this.handleFocus();
                };
                
                config.onBlur = () => {
                    this.handleBlur();
                };
                
                return config;
            }
            
            // 获取自动保存插件
            getAutoSavePlugin() {
                if (!this.enhancedConfig?.enableAutoSave) return null;
                
                return class AutoSavePlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.autoSaveTimer = null;
                        this.lastSavedContent = '';
                        this.setupAutoSave();
                    }
                    
                    setupAutoSave() {
                        this.editor.addEventListener('input', () => {
                            this.scheduleAutoSave();
                        });
                    }
                    
                    scheduleAutoSave() {
                        clearTimeout(this.autoSaveTimer);
                        this.autoSaveTimer = setTimeout(() => {
                            this.performAutoSave();
                        }, this.enhancedConfig.autoSaveInterval);
                    }
                    
                    performAutoSave() {
                        const content = this.editor.getContent();
                        if (content !== this.lastSavedContent) {
                            this.saveContent(content);
                            this.lastSavedContent = content;
                        }
                    }
                    
                    saveContent(content) {
                        // 实现自动保存逻辑
                        console.log('自动保存内容:', content);
                    }
                };
            }
            
            // 获取模板插件
            getTemplatePlugin() {
                if (!this.enhancedConfig?.enableTemplates) return null;
                
                return class TemplatePlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.templates = this.loadTemplates();
                        this.setupTemplateUI();
                    }
                    
                    loadTemplates() {
                        // 加载邮件模板
                        return [
                            { id: 1, name: '问候模板', content: '您好，\n\n希望您一切都好。\n\n此致\n敬礼' },
                            { id: 2, name: '会议邀请', content: '您好，\n\n我想邀请您参加会议。\n\n会议时间：\n会议地点：\n\n期待您的回复。' },
                            { id: 3, name: '感谢信', content: '亲爱的，\n\n感谢您的支持和帮助。\n\n最诚挚的谢意。' }
                        ];
                    }
                    
                    setupTemplateUI() {
                        // 设置模板选择界面
                        this.createTemplateButton();
                    }
                    
                    createTemplateButton() {
                        // 创建模板按钮
                        const button = document.createElement('button');
                        button.textContent = '模板';
                        button.onclick = () => this.showTemplateSelector();
                        
                        // 添加到工具栏
                        this.editor.toolbar.appendChild(button);
                    }
                    
                    showTemplateSelector() {
                        // 显示模板选择器
                        const selector = this.createTemplateSelector();
                        document.body.appendChild(selector);
                    }
                    
                    createTemplateSelector() {
                        const selector = document.createElement('div');
                        selector.className = 'template-selector';
                        
                        this.templates.forEach(template => {
                            const item = document.createElement('div');
                            item.textContent = template.name;
                            item.onclick = () => this.applyTemplate(template);
                            selector.appendChild(item);
                        });
                        
                        return selector;
                    }
                    
                    applyTemplate(template) {
                        this.editor.setContent(template.content);
                    }
                };
            }
            
            // 获取签名插件
            getSignaturePlugin() {
                if (!this.enhancedConfig?.enableSignature) return null;
                
                return class SignaturePlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.signature = this.loadUserSignature();
                        this.setupSignature();
                    }
                    
                    loadUserSignature() {
                        // 加载用户签名
                        return localStorage.getItem('user_signature') || '';
                    }
                    
                    setupSignature() {
                        // 自动添加签名
                        this.editor.addEventListener('focus', () => {
                            this.addSignatureIfNeeded();
                        });
                    }
                    
                    addSignatureIfNeeded() {
                        const content = this.editor.getContent();
                        if (this.signature && !content.includes(this.signature)) {
                            this.editor.setContent(content + '\n\n' + this.signature);
                        }
                    }
                };
            }
            
            // 获取拼写检查插件
            getSpellCheckPlugin() {
                if (!this.enhancedConfig?.enableSpellCheck) return null;
                
                return class SpellCheckPlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.setupSpellCheck();
                    }
                    
                    setupSpellCheck() {
                        // 启用浏览器拼写检查
                        this.editor.setAttribute('spellcheck', 'true');
                    }
                };
            }
            
            // 获取字数统计插件
            getWordCountPlugin() {
                if (!this.enhancedConfig?.enableWordCount) return null;
                
                return class WordCountPlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.wordCountDisplay = this.createWordCountDisplay();
                        this.setupWordCount();
                    }
                    
                    createWordCountDisplay() {
                        const display = document.createElement('div');
                        display.className = 'word-count-display';
                        this.editor.parentNode.appendChild(display);
                        return display;
                    }
                    
                    setupWordCount() {
                        this.updateWordCount();
                        this.editor.addEventListener('input', () => {
                            this.updateWordCount();
                        });
                    }
                    
                    updateWordCount() {
                        const content = this.editor.getTextContent();
                        const words = content.trim().split(/\s+/).filter(word => word.length > 0);
                        const characters = content.length;
                        
                        this.wordCountDisplay.textContent = `字数: ${words.length} | 字符: ${characters}`;
                    }
                };
            }
            
            // 获取链接预览插件
            getLinkPreviewPlugin() {
                if (!this.enhancedConfig?.enableLinkPreview) return null;
                
                return class LinkPreviewPlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.setupLinkPreview();
                    }
                    
                    setupLinkPreview() {
                        this.editor.addEventListener('paste', (event) => {
                            this.handlePaste(event);
                        });
                    }
                    
                    handlePaste(event) {
                        const text = event.clipboardData.getData('text');
                        if (this.isUrl(text)) {
                            this.generateLinkPreview(text);
                        }
                    }
                    
                    isUrl(text) {
                        try {
                            new URL(text);
                            return true;
                        } catch {
                            return false;
                        }
                    }
                    
                    generateLinkPreview(url) {
                        // 生成链接预览
                        console.log('生成链接预览:', url);
                    }
                };
            }
            
            // 获取表情符号选择器插件
            getEmojiPickerPlugin() {
                if (!this.enhancedConfig?.enableEmojiPicker) return null;
                
                return class EmojiPickerPlugin {
                    constructor(editor) {
                        this.editor = editor;
                        this.setupEmojiPicker();
                    }
                    
                    setupEmojiPicker() {
                        const button = document.createElement('button');
                        button.textContent = '😊';
                        button.onclick = () => this.showEmojiPicker();
                        
                        this.editor.toolbar.appendChild(button);
                    }
                    
                    showEmojiPicker() {
                        // 显示表情符号选择器
                        console.log('显示表情符号选择器');
                    }
                };
            }
            
            // 处理增强的附件
            handleEnhancedAttachment(attachment) {
                try {
                    // 验证附件大小
                    if (attachment.size > this.enhancedConfig.maxAttachmentSize) {
                        throw new Error(`附件大小超过限制 (${this.enhancedConfig.maxAttachmentSize / 1024 / 1024}MB)`);
                    }
                    
                    // 验证附件类型
                    if (!this.isAllowedAttachmentType(attachment.type)) {
                        throw new Error('不支持的附件类型');
                    }
                    
                    // 处理内联图片
                    if (this.enhancedConfig.enableInlineImages && attachment.type.startsWith('image/')) {
                        this.handleInlineImage(attachment);
                    }
                    
                    // 记录附件信息
                    this.logAttachmentInfo(attachment);
                } catch (error) {
                    console.error('处理增强附件失败:', error);
                    this.showAttachmentError(error.message);
                }
            }
            
            // 检查允许的附件类型
            isAllowedAttachmentType(type) {
                return this.enhancedConfig.allowedAttachmentTypes.some(allowedType => {
                    if (allowedType.endsWith('/*')) {
                        return type.startsWith(allowedType.slice(0, -1));
                    }
                    return type === allowedType;
                });
            }
            
            // 处理内联图片
            handleInlineImage(attachment) {
                // 将图片插入到编辑器中
                const img = document.createElement('img');
                img.src = URL.createObjectURL(attachment.file);
                img.style.maxWidth = '100%';
                
                this.editor.insertNode(img);
            }
            
            // 记录附件信息
            logAttachmentInfo(attachment) {
                console.log('附件信息:', {
                    name: attachment.name,
                    size: attachment.size,
                    type: attachment.type,
                    lastModified: attachment.lastModified
                });
            }
            
            // 显示附件错误
            showAttachmentError(message) {
                // 显示错误消息
                alert(`附件错误: ${message}`);
            }
            
            // 处理内容变更
            handleContentChange(content) {
                // 更新字数统计
                this.updateWordCount(content);
                
                // 触发自动保存
                this.scheduleAutoSave(content);
            }
            
            // 处理选择变更
            handleSelectionChange(selection) {
                // 更新工具栏状态
                this.updateToolbarState(selection);
            }
            
            // 处理焦点
            handleFocus() {
                // 编辑器获得焦点时的处理
                this.addSignatureIfNeeded();
            }
            
            // 处理失焦
            handleBlur() {
                // 编辑器失去焦点时的处理
                this.performAutoSave();
            }
        }
        
        // 增强的字段注册
        const enhancedHtmlComposerMessageField = {
            ...htmlComposerMessageField,
            component: EnhancedHtmlComposerMessageField,
            additionalClasses: [...(htmlComposerMessageField.additionalClasses || []), "o_field_html_composer_enhanced"],
        };
        
        // 替换原始字段注册
        registry.category("fields").add("html_composer_message_enhanced", enhancedHtmlComposerMessageField);
    }
};

// 应用HTML撰写消息字段增强
HtmlComposerMessageFieldEnhancer.enhanceHtmlComposerMessageField();
```

## 技术特点

### 1. 继承扩展
- 继承HtmlMailField的所有功能
- 添加撰写专用的增强功能
- 保持原有功能的完整性

### 2. 插件系统
- 集成MentionPlugin提及功能
- 支持动态占位符插件
- 可扩展的插件架构

### 3. 条件加载
- 根据composition_batch条件加载插件
- 智能的功能启用和禁用
- 优化的性能表现

### 4. 附件集成
- 专门的附件处理逻辑
- 与邮件撰写流程集成
- 自动的关系链接管理

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承HtmlMailField的功能
- 扩展撰写专用特性

### 2. 插件模式 (Plugin Pattern)
- 可扩展的插件系统
- 模块化的功能组织

### 3. 配置模式 (Configuration Pattern)
- 灵活的配置系统
- 条件性的功能启用

## 注意事项

1. **插件兼容性**: 确保插件之间的兼容性
2. **性能考虑**: 避免过多插件影响性能
3. **附件处理**: 正确处理附件的生命周期
4. **条件逻辑**: 确保条件判断的准确性

## 扩展建议

1. **更多插件**: 添加更多实用的编辑器插件
2. **模板系统**: 实现邮件模板功能
3. **自动保存**: 添加内容的自动保存功能
4. **协作编辑**: 支持多人协作编辑
5. **版本控制**: 实现内容的版本控制

该HTML撰写消息字段为邮件系统提供了强大的邮件撰写功能，是邮件创作和用户交互的核心组件。

# Mention Plugin - 提及插件

## 概述

`mention_plugin.js` 是 Odoo 邮件系统的提及插件，专门为HTML编辑器提供@用户和#频道的提及功能。该插件基于HTML编辑器插件系统和邮件核心提及列表，集成了输入检测、选择处理、链接生成等核心功能，为邮件系统提供了完整的提及和引用支持，是邮件系统社交交互和协作沟通的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/html_composer_message_field/mention_plugin.js`
- **行数**: 66
- **模块**: `@mail/views/web/fields/html_composer_message_field/mention_plugin`

## 依赖关系

```javascript
// 核心依赖
'@html_editor/plugin'                // HTML编辑器插件基类
'@mail/core/web/mention_list'        // 提及列表组件
'@web/core/browser/router'           // 路由器
'@web/core/utils/render'             // 渲染工具
'@web/core/utils/urls'               // URL工具
```

## 核心功能

### 1. MentionPlugin 类

```javascript
const MentionPlugin = class MentionPlugin extends Plugin {
    static name = "mention";
    static dependencies = ["overlay", "dom", "history", "selection"];

    resources = {
        onBeforeInput: this.onBeforeInput.bind(this),
    };

    setup() {
        this.mentionList = this.shared.createOverlay(MentionList, {
            hasAutofocus: true,
            className: "popover",
        });
    }
}
```

**插件特性**:
- **插件继承**: 继承HTML编辑器Plugin基类
- **依赖声明**: 声明对overlay、dom、history、selection的依赖
- **事件绑定**: 绑定onBeforeInput事件处理器
- **覆盖层**: 创建MentionList覆盖层组件

### 2. 插件设置

```javascript
setup() {
    this.mentionList = this.shared.createOverlay(MentionList, {
        hasAutofocus: true,
        className: "popover",
    });
}
```

**设置功能**:
- **覆盖层创建**: 创建提及列表覆盖层
- **自动聚焦**: 启用自动聚焦功能
- **样式类**: 设置popover样式类
- **共享资源**: 使用编辑器的共享资源

### 3. 选择处理

```javascript
onSelect(ev, option) {
    const mentionBlock = renderToElement("mail.Wysiwyg.mentionLink", {
        option,
        href: url(
            stateToUrl({
                model: option.partner ? "res.partner" : "discuss.channel",
                resId: option.partner ? option.partner.id : option.channel.id,
            })
        ),
    });
    
    const nameNode = this.document.createTextNode(
        `${option.partner ? "@" : "#"}${option.label}`
    );
    
    mentionBlock.appendChild(nameNode);
    this.historySavePointRestore();
    this.shared.domInsert(mentionBlock);
    this.dispatch("ADD_STEP");
}
```

**选择处理功能**:
- **模板渲染**: 使用mail.Wysiwyg.mentionLink模板渲染提及链接
- **URL生成**: 根据选项类型生成相应的URL
- **文本节点**: 创建带前缀的文本节点
- **DOM插入**: 将提及块插入到编辑器中
- **历史管理**: 恢复保存点并添加历史步骤

### 4. 输入检测

```javascript
onBeforeInput(ev) {
    if (ev.data === "@" || ev.data === "#") {
        this.historySavePointRestore = this.shared.makeSavePoint();
        this.mentionList.open({
            props: {
                onSelect: this.onSelect.bind(this),
                type: ev.data === "@" ? "partner" : "channel",
                close: () => {
                    this.mentionList.close();
                    this.shared.focusEditable();
                },
            },
        });
    }
}
```

**输入检测功能**:
- **触发字符**: 检测@和#字符输入
- **保存点**: 创建历史保存点
- **列表打开**: 打开提及列表覆盖层
- **类型区分**: 根据触发字符区分partner和channel类型
- **关闭回调**: 设置列表关闭时的回调函数

## 使用场景

### 1. 提及插件增强

```javascript
// 提及插件增强功能
const MentionPluginEnhancer = {
    enhanceMentionPlugin: () => {
        // 增强的提及插件
        class EnhancedMentionPlugin extends MentionPlugin {
            static name = "enhanced_mention";
            static dependencies = [...MentionPlugin.dependencies, "search", "filter"];
            
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableFuzzySearch: true,
                    enableRecentMentions: true,
                    enableMentionHistory: true,
                    enableCustomTriggers: true,
                    enableMentionPreview: true,
                    enableMentionNotification: true,
                    maxRecentMentions: 10,
                    searchDelay: 300,
                    customTriggers: ['!', '$', '%'],
                    mentionTypes: {
                        '@': { type: 'partner', icon: '👤', color: '#007bff' },
                        '#': { type: 'channel', icon: '📢', color: '#28a745' },
                        '!': { type: 'task', icon: '📋', color: '#ffc107' },
                        '$': { type: 'project', icon: '📁', color: '#6f42c1' },
                        '%': { type: 'tag', icon: '🏷️', color: '#fd7e14' }
                    }
                };
                
                // 最近提及记录
                this.recentMentions = this.loadRecentMentions();
                
                // 提及历史
                this.mentionHistory = this.loadMentionHistory();
                
                // 搜索防抖定时器
                this.searchTimer = null;
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自定义触发器
                if (this.enhancedConfig.enableCustomTriggers) {
                    this.setupCustomTriggers();
                }
                
                // 设置模糊搜索
                if (this.enhancedConfig.enableFuzzySearch) {
                    this.setupFuzzySearch();
                }
                
                // 设置提及预览
                if (this.enhancedConfig.enableMentionPreview) {
                    this.setupMentionPreview();
                }
                
                // 设置提及通知
                if (this.enhancedConfig.enableMentionNotification) {
                    this.setupMentionNotification();
                }
            }
            
            // 增强的输入检测
            onBeforeInput(ev) {
                const triggerChar = ev.data;
                const mentionType = this.enhancedConfig.mentionTypes[triggerChar];
                
                if (mentionType) {
                    this.handleMentionTrigger(triggerChar, mentionType);
                } else {
                    // 处理连续输入的搜索
                    this.handleContinuousInput(ev);
                }
            }
            
            // 处理提及触发
            handleMentionTrigger(triggerChar, mentionType) {
                try {
                    // 创建保存点
                    this.historySavePointRestore = this.shared.makeSavePoint();
                    
                    // 打开增强的提及列表
                    this.mentionList.open({
                        props: {
                            onSelect: (ev, option) => this.onEnhancedSelect(ev, option, triggerChar),
                            type: mentionType.type,
                            triggerChar: triggerChar,
                            icon: mentionType.icon,
                            color: mentionType.color,
                            recentMentions: this.getRecentMentionsByType(mentionType.type),
                            enableFuzzySearch: this.enhancedConfig.enableFuzzySearch,
                            searchDelay: this.enhancedConfig.searchDelay,
                            close: () => {
                                this.mentionList.close();
                                this.shared.focusEditable();
                            },
                            onSearch: (query) => this.handleMentionSearch(query, mentionType.type),
                            onPreview: (option) => this.handleMentionPreview(option)
                        },
                    });
                } catch (error) {
                    console.error('处理提及触发失败:', error);
                }
            }
            
            // 处理连续输入
            handleContinuousInput(ev) {
                // 检查是否在提及上下文中
                const selection = this.shared.getSelection();
                const currentNode = selection.anchorNode;
                
                if (this.isInMentionContext(currentNode)) {
                    // 更新搜索查询
                    this.updateMentionSearch(ev.data);
                }
            }
            
            // 检查是否在提及上下文中
            isInMentionContext(node) {
                // 检查当前节点是否在提及输入上下文中
                let current = node;
                while (current && current !== this.editable) {
                    if (current.classList && current.classList.contains('o_mention_input')) {
                        return true;
                    }
                    current = current.parentNode;
                }
                return false;
            }
            
            // 更新提及搜索
            updateMentionSearch(inputChar) {
                clearTimeout(this.searchTimer);
                this.searchTimer = setTimeout(() => {
                    const query = this.getCurrentMentionQuery();
                    if (query) {
                        this.performMentionSearch(query);
                    }
                }, this.enhancedConfig.searchDelay);
            }
            
            // 获取当前提及查询
            getCurrentMentionQuery() {
                try {
                    const selection = this.shared.getSelection();
                    const range = selection.getRangeAt(0);
                    const textContent = range.startContainer.textContent;
                    const cursorPos = range.startOffset;
                    
                    // 查找最近的触发字符
                    let triggerPos = -1;
                    for (const trigger of Object.keys(this.enhancedConfig.mentionTypes)) {
                        const pos = textContent.lastIndexOf(trigger, cursorPos - 1);
                        if (pos > triggerPos) {
                            triggerPos = pos;
                        }
                    }
                    
                    if (triggerPos !== -1) {
                        return textContent.substring(triggerPos + 1, cursorPos);
                    }
                    
                    return null;
                } catch (error) {
                    console.error('获取提及查询失败:', error);
                    return null;
                }
            }
            
            // 执行提及搜索
            performMentionSearch(query) {
                // 更新提及列表的搜索结果
                if (this.mentionList.isOpen) {
                    this.mentionList.updateSearch(query);
                }
            }
            
            // 增强的选择处理
            onEnhancedSelect(ev, option, triggerChar) {
                try {
                    // 执行原有选择逻辑
                    this.onSelect(ev, option);
                    
                    // 记录提及使用
                    this.recordMentionUsage(option, triggerChar);
                    
                    // 更新最近提及
                    this.updateRecentMentions(option);
                    
                    // 发送提及通知
                    if (this.enhancedConfig.enableMentionNotification) {
                        this.sendMentionNotification(option);
                    }
                    
                    // 触发自定义事件
                    this.triggerMentionEvent('mention-selected', {
                        option: option,
                        triggerChar: triggerChar,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error('增强选择处理失败:', error);
                }
            }
            
            // 记录提及使用
            recordMentionUsage(option, triggerChar) {
                try {
                    const mentionId = this.getMentionId(option);
                    const usage = {
                        id: mentionId,
                        option: option,
                        triggerChar: triggerChar,
                        timestamp: Date.now(),
                        count: 1
                    };
                    
                    // 更新历史记录
                    const existingIndex = this.mentionHistory.findIndex(item => item.id === mentionId);
                    if (existingIndex !== -1) {
                        this.mentionHistory[existingIndex].count++;
                        this.mentionHistory[existingIndex].timestamp = Date.now();
                    } else {
                        this.mentionHistory.push(usage);
                    }
                    
                    // 保存历史记录
                    this.saveMentionHistory();
                } catch (error) {
                    console.error('记录提及使用失败:', error);
                }
            }
            
            // 获取提及ID
            getMentionId(option) {
                if (option.partner) {
                    return `partner_${option.partner.id}`;
                } else if (option.channel) {
                    return `channel_${option.channel.id}`;
                } else if (option.task) {
                    return `task_${option.task.id}`;
                } else if (option.project) {
                    return `project_${option.project.id}`;
                } else if (option.tag) {
                    return `tag_${option.tag.id}`;
                }
                return `unknown_${Date.now()}`;
            }
            
            // 更新最近提及
            updateRecentMentions(option) {
                try {
                    if (!this.enhancedConfig.enableRecentMentions) return;
                    
                    const mentionId = this.getMentionId(option);
                    
                    // 移除已存在的
                    this.recentMentions = this.recentMentions.filter(item => 
                        this.getMentionId(item) !== mentionId
                    );
                    
                    // 添加到开头
                    this.recentMentions.unshift(option);
                    
                    // 限制数量
                    if (this.recentMentions.length > this.enhancedConfig.maxRecentMentions) {
                        this.recentMentions = this.recentMentions.slice(0, this.enhancedConfig.maxRecentMentions);
                    }
                    
                    // 保存到本地存储
                    this.saveRecentMentions();
                } catch (error) {
                    console.error('更新最近提及失败:', error);
                }
            }
            
            // 根据类型获取最近提及
            getRecentMentionsByType(type) {
                return this.recentMentions.filter(mention => {
                    switch (type) {
                        case 'partner':
                            return mention.partner;
                        case 'channel':
                            return mention.channel;
                        case 'task':
                            return mention.task;
                        case 'project':
                            return mention.project;
                        case 'tag':
                            return mention.tag;
                        default:
                            return false;
                    }
                });
            }
            
            // 处理提及搜索
            handleMentionSearch(query, type) {
                try {
                    // 实现模糊搜索逻辑
                    if (this.enhancedConfig.enableFuzzySearch) {
                        return this.performFuzzySearch(query, type);
                    } else {
                        return this.performExactSearch(query, type);
                    }
                } catch (error) {
                    console.error('处理提及搜索失败:', error);
                    return [];
                }
            }
            
            // 执行模糊搜索
            performFuzzySearch(query, type) {
                // 实现模糊搜索算法
                console.log('执行模糊搜索:', query, type);
                return [];
            }
            
            // 执行精确搜索
            performExactSearch(query, type) {
                // 实现精确搜索算法
                console.log('执行精确搜索:', query, type);
                return [];
            }
            
            // 处理提及预览
            handleMentionPreview(option) {
                if (!this.enhancedConfig.enableMentionPreview) return;
                
                try {
                    // 显示提及预览
                    this.showMentionPreview(option);
                } catch (error) {
                    console.error('处理提及预览失败:', error);
                }
            }
            
            // 显示提及预览
            showMentionPreview(option) {
                // 实现提及预览显示逻辑
                console.log('显示提及预览:', option);
            }
            
            // 发送提及通知
            sendMentionNotification(option) {
                try {
                    // 发送提及通知给被提及的用户
                    if (option.partner) {
                        this.notifyUser(option.partner.id, 'mention');
                    }
                } catch (error) {
                    console.error('发送提及通知失败:', error);
                }
            }
            
            // 通知用户
            notifyUser(userId, type) {
                // 实现用户通知逻辑
                console.log('通知用户:', userId, type);
            }
            
            // 触发提及事件
            triggerMentionEvent(eventName, data) {
                try {
                    if (this.dispatch) {
                        this.dispatch(eventName, data);
                    }
                } catch (error) {
                    console.error('触发提及事件失败:', error);
                }
            }
            
            // 加载最近提及
            loadRecentMentions() {
                try {
                    const stored = localStorage.getItem('mail_recent_mentions');
                    return stored ? JSON.parse(stored) : [];
                } catch (error) {
                    console.error('加载最近提及失败:', error);
                    return [];
                }
            }
            
            // 保存最近提及
            saveRecentMentions() {
                try {
                    localStorage.setItem('mail_recent_mentions', JSON.stringify(this.recentMentions));
                } catch (error) {
                    console.error('保存最近提及失败:', error);
                }
            }
            
            // 加载提及历史
            loadMentionHistory() {
                try {
                    const stored = localStorage.getItem('mail_mention_history');
                    return stored ? JSON.parse(stored) : [];
                } catch (error) {
                    console.error('加载提及历史失败:', error);
                    return [];
                }
            }
            
            // 保存提及历史
            saveMentionHistory() {
                try {
                    localStorage.setItem('mail_mention_history', JSON.stringify(this.mentionHistory));
                } catch (error) {
                    console.error('保存提及历史失败:', error);
                }
            }
            
            // 获取最常用的提及
            getMostUsedMentions(limit = 5) {
                try {
                    return this.mentionHistory
                        .sort((a, b) => b.count - a.count)
                        .slice(0, limit)
                        .map(item => item.option);
                } catch (error) {
                    console.error('获取最常用提及失败:', error);
                    return [];
                }
            }
            
            // 清除提及数据
            clearMentionData() {
                try {
                    this.recentMentions = [];
                    this.mentionHistory = [];
                    
                    localStorage.removeItem('mail_recent_mentions');
                    localStorage.removeItem('mail_mention_history');
                    
                    this.triggerMentionEvent('mention-data-cleared', {
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error('清除提及数据失败:', error);
                }
            }
        }
        
        // 导出增强的提及插件
        __exports.EnhancedMentionPlugin = EnhancedMentionPlugin;
    }
};

// 应用提及插件增强
MentionPluginEnhancer.enhanceMentionPlugin();
```

## 技术特点

### 1. 插件架构
- 基于HTML编辑器插件系统
- 声明式的依赖管理
- 标准化的插件接口

### 2. 事件驱动
- 输入事件的实时检测
- 选择事件的处理机制
- 历史管理的集成

### 3. 覆盖层集成
- 使用共享的覆盖层系统
- 自动聚焦和样式管理
- 灵活的配置选项

### 4. URL生成
- 动态的URL生成
- 路由状态的转换
- 模型和资源ID的映射

## 设计模式

### 1. 插件模式 (Plugin Pattern)
- 可扩展的插件架构
- 标准化的插件接口

### 2. 观察者模式 (Observer Pattern)
- 事件驱动的交互
- 输入事件的监听

### 3. 工厂模式 (Factory Pattern)
- 动态的组件创建
- 配置驱动的实例化

## 注意事项

1. **性能考虑**: 避免频繁的DOM操作影响性能
2. **事件处理**: 确保事件处理的准确性和及时性
3. **历史管理**: 正确管理编辑历史的保存和恢复
4. **用户体验**: 提供流畅的提及选择体验

## 扩展建议

1. **更多触发器**: 支持更多类型的提及触发器
2. **模糊搜索**: 实现智能的模糊搜索功能
3. **最近使用**: 记录和显示最近使用的提及
4. **预览功能**: 添加提及对象的预览功能
5. **通知系统**: 集成提及通知系统

该提及插件为邮件系统提供了重要的社交交互功能，是协作沟通和用户引用的核心组件。

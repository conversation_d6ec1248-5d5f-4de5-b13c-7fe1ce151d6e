# Emojis Text Field - 表情符号文本字段

## 概述

`emojis_text_field.js` 是 Odoo 邮件系统的表情符号文本字段组件，专门为文本字段添加表情符号支持功能。该组件基于Web框架的文本字段和表情符号通用字段，集成了多行文本编辑、表情符号选择、内容格式化等核心功能，为邮件系统提供了完整的富文本输入和表情符号支持，是邮件系统内容创作和用户表达的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/emojis_text_field/emojis_text_field.js`
- **行数**: 35
- **模块**: `@mail/views/web/fields/emojis_text_field/emojis_text_field`

## 依赖关系

```javascript
// 核心依赖
'@mail/views/web/fields/emojis_field_common/emojis_field_common' // 表情符号通用字段
'@web/core/registry'                                             // 注册表
'@web/views/fields/text/text_field'                             // 文本字段
```

## 核心功能

### 1. EmojisTextField 类

```javascript
const EmojisTextField = class EmojisTextField extends EmojisFieldCommon(TextField) {
    static template = "mail.EmojisTextField";
    static components = { ...TextField.components };
    
    setup() {
        super.setup();
        this.targetEditElement = this.textareaRef;
        this._setupOverride();
    }
}
```

**组件特性**:
- **多重继承**: 继承EmojisFieldCommon和TextField的功能
- **模板渲染**: 使用专用的EmojisTextField模板
- **组件集成**: 继承TextField的所有组件
- **文本区域**: 使用textareaRef作为目标编辑元素
- **设置重写**: 调用_setupOverride进行表情符号设置

### 2. 目标元素设置

```javascript
setup() {
    super.setup();
    this.targetEditElement = this.textareaRef;
    this._setupOverride();
}
```

**设置功能**:
- **父类调用**: 调用父类的setup方法
- **元素绑定**: 绑定到textarea元素
- **表情符号初始化**: 初始化表情符号功能
- **多行支持**: 支持多行文本编辑

### 3. 字段配置

```javascript
const emojisTextField = {
    ...textField,
    component: EmojisTextField,
    additionalClasses: [...(textField.additionalClasses || []), "o_field_text"],
};
```

**配置功能**:
- **配置继承**: 继承textField的所有配置
- **组件替换**: 使用EmojisTextField组件
- **样式增强**: 添加额外的CSS类
- **功能扩展**: 在原有功能基础上添加表情符号支持

### 4. 字段注册

```javascript
registry.category("fields").add("text_emojis", emojisTextField);
```

**注册功能**:
- **类型注册**: 注册为"text_emojis"字段类型
- **全局可用**: 使字段在整个系统中可用
- **配置绑定**: 绑定字段配置和组件
- **标准集成**: 与字段系统标准集成

## 使用场景

### 1. 表情符号文本字段增强

```javascript
// 表情符号文本字段增强功能
const EmojisTextFieldEnhancer = {
    enhanceEmojisTextField: () => {
        // 增强的表情符号文本字段
        class EnhancedEmojisTextField extends EmojisTextField {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableMarkdown: false,
                    enableAutoResize: true,
                    enableLineNumbers: false,
                    enableSyntaxHighlight: false,
                    enableWordWrap: true,
                    enableSpellCheck: true,
                    maxLength: null,
                    minRows: 3,
                    maxRows: 20,
                    placeholder: '',
                    enableRichText: false,
                    enableMentions: false,
                    enableHashtags: false
                };
                
                // 文本统计
                this.textStats = {
                    characters: 0,
                    words: 0,
                    lines: 0,
                    paragraphs: 0
                };
                
                // 编辑历史
                this.editHistory = [];
                this.historyIndex = -1;
                this.maxHistorySize = 50;
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自动调整大小
                if (this.enhancedConfig.enableAutoResize) {
                    this.setupAutoResize();
                }
                
                // 设置文本统计
                this.setupTextStatistics();
                
                // 设置编辑历史
                this.setupEditHistory();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置拖拽支持
                this.setupDragAndDrop();
            }
            
            // 设置自动调整大小
            setupAutoResize() {
                const textarea = this.targetEditElement.el;
                if (!textarea) return;
                
                // 初始设置
                this.setTextareaHeight();
                
                // 监听输入事件
                textarea.addEventListener('input', () => {
                    this.setTextareaHeight();
                    this.updateTextStatistics();
                });
                
                // 监听粘贴事件
                textarea.addEventListener('paste', () => {
                    setTimeout(() => {
                        this.setTextareaHeight();
                        this.updateTextStatistics();
                    }, 0);
                });
            }
            
            // 设置文本区域高度
            setTextareaHeight() {
                try {
                    const textarea = this.targetEditElement.el;
                    if (!textarea) return;
                    
                    // 重置高度
                    textarea.style.height = 'auto';
                    
                    // 计算所需高度
                    const scrollHeight = textarea.scrollHeight;
                    const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20;
                    const minHeight = this.enhancedConfig.minRows * lineHeight;
                    const maxHeight = this.enhancedConfig.maxRows * lineHeight;
                    
                    // 设置高度
                    const newHeight = Math.max(minHeight, Math.min(maxHeight, scrollHeight));
                    textarea.style.height = newHeight + 'px';
                    
                    // 显示滚动条（如果需要）
                    textarea.style.overflowY = scrollHeight > maxHeight ? 'scroll' : 'hidden';
                } catch (error) {
                    console.error('设置文本区域高度失败:', error);
                }
            }
            
            // 设置文本统计
            setupTextStatistics() {
                const textarea = this.targetEditElement.el;
                if (!textarea) return;
                
                // 初始统计
                this.updateTextStatistics();
                
                // 监听输入事件
                textarea.addEventListener('input', () => {
                    this.updateTextStatistics();
                });
            }
            
            // 更新文本统计
            updateTextStatistics() {
                try {
                    const textarea = this.targetEditElement.el;
                    if (!textarea) return;
                    
                    const text = textarea.value;
                    
                    // 字符数
                    this.textStats.characters = text.length;
                    
                    // 单词数
                    this.textStats.words = text.trim() ? text.trim().split(/\s+/).length : 0;
                    
                    // 行数
                    this.textStats.lines = text.split('\n').length;
                    
                    // 段落数
                    this.textStats.paragraphs = text.split(/\n\s*\n/).filter(p => p.trim()).length;
                    
                    // 触发统计更新事件
                    this.triggerEvent('text-stats-updated', this.textStats);
                } catch (error) {
                    console.error('更新文本统计失败:', error);
                }
            }
            
            // 设置编辑历史
            setupEditHistory() {
                const textarea = this.targetEditElement.el;
                if (!textarea) return;
                
                // 保存初始状态
                this.saveToHistory(textarea.value);
                
                // 监听输入事件（防抖）
                let timeoutId;
                textarea.addEventListener('input', () => {
                    clearTimeout(timeoutId);
                    timeoutId = setTimeout(() => {
                        this.saveToHistory(textarea.value);
                    }, 1000); // 1秒后保存
                });
            }
            
            // 保存到历史
            saveToHistory(content) {
                try {
                    // 如果内容相同，不保存
                    if (this.editHistory.length > 0 && 
                        this.editHistory[this.editHistory.length - 1] === content) {
                        return;
                    }
                    
                    // 如果不在历史末尾，删除后续历史
                    if (this.historyIndex < this.editHistory.length - 1) {
                        this.editHistory = this.editHistory.slice(0, this.historyIndex + 1);
                    }
                    
                    // 添加新历史
                    this.editHistory.push(content);
                    this.historyIndex = this.editHistory.length - 1;
                    
                    // 限制历史大小
                    if (this.editHistory.length > this.maxHistorySize) {
                        this.editHistory.shift();
                        this.historyIndex--;
                    }
                } catch (error) {
                    console.error('保存编辑历史失败:', error);
                }
            }
            
            // 撤销
            undo() {
                try {
                    if (this.historyIndex > 0) {
                        this.historyIndex--;
                        const content = this.editHistory[this.historyIndex];
                        this.setTextContent(content);
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('撤销失败:', error);
                    return false;
                }
            }
            
            // 重做
            redo() {
                try {
                    if (this.historyIndex < this.editHistory.length - 1) {
                        this.historyIndex++;
                        const content = this.editHistory[this.historyIndex];
                        this.setTextContent(content);
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('重做失败:', error);
                    return false;
                }
            }
            
            // 设置文本内容
            setTextContent(content) {
                try {
                    const textarea = this.targetEditElement.el;
                    if (!textarea) return;
                    
                    textarea.value = content;
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // 更新高度和统计
                    this.setTextareaHeight();
                    this.updateTextStatistics();
                } catch (error) {
                    console.error('设置文本内容失败:', error);
                }
            }
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                const textarea = this.targetEditElement.el;
                if (!textarea) return;
                
                textarea.addEventListener('keydown', (event) => {
                    this.handleKeyboardShortcuts(event);
                });
            }
            
            // 处理键盘快捷键
            handleKeyboardShortcuts(event) {
                try {
                    const isCtrlOrCmd = event.ctrlKey || event.metaKey;
                    
                    // Ctrl/Cmd + Z: 撤销
                    if (isCtrlOrCmd && event.key === 'z' && !event.shiftKey) {
                        event.preventDefault();
                        this.undo();
                        return;
                    }
                    
                    // Ctrl/Cmd + Y 或 Ctrl/Cmd + Shift + Z: 重做
                    if (isCtrlOrCmd && (event.key === 'y' || (event.key === 'z' && event.shiftKey))) {
                        event.preventDefault();
                        this.redo();
                        return;
                    }
                    
                    // Ctrl/Cmd + A: 全选
                    if (isCtrlOrCmd && event.key === 'a') {
                        // 让默认行为执行
                        return;
                    }
                    
                    // Tab: 插入制表符
                    if (event.key === 'Tab') {
                        event.preventDefault();
                        this.insertAtCursor('\t');
                        return;
                    }
                } catch (error) {
                    console.error('处理键盘快捷键失败:', error);
                }
            }
            
            // 在光标位置插入文本
            insertAtCursor(text) {
                try {
                    const textarea = this.targetEditElement.el;
                    if (!textarea) return;
                    
                    const start = textarea.selectionStart;
                    const end = textarea.selectionEnd;
                    const value = textarea.value;
                    
                    const newValue = value.substring(0, start) + text + value.substring(end);
                    textarea.value = newValue;
                    
                    // 设置光标位置
                    const newCursorPos = start + text.length;
                    textarea.setSelectionRange(newCursorPos, newCursorPos);
                    
                    // 触发事件
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    // 更新高度和统计
                    this.setTextareaHeight();
                    this.updateTextStatistics();
                } catch (error) {
                    console.error('在光标位置插入文本失败:', error);
                }
            }
            
            // 设置拖拽支持
            setupDragAndDrop() {
                const textarea = this.targetEditElement.el;
                if (!textarea) return;
                
                // 拖拽进入
                textarea.addEventListener('dragover', (event) => {
                    event.preventDefault();
                    textarea.classList.add('o_drag_over');
                });
                
                // 拖拽离开
                textarea.addEventListener('dragleave', (event) => {
                    textarea.classList.remove('o_drag_over');
                });
                
                // 拖拽放下
                textarea.addEventListener('drop', (event) => {
                    event.preventDefault();
                    textarea.classList.remove('o_drag_over');
                    
                    this.handleDrop(event);
                });
            }
            
            // 处理拖拽放下
            handleDrop(event) {
                try {
                    const files = event.dataTransfer.files;
                    const text = event.dataTransfer.getData('text/plain');
                    
                    if (files.length > 0) {
                        // 处理文件拖拽
                        this.handleFilesDrop(files);
                    } else if (text) {
                        // 处理文本拖拽
                        this.insertAtCursor(text);
                    }
                } catch (error) {
                    console.error('处理拖拽放下失败:', error);
                }
            }
            
            // 处理文件拖拽
            handleFilesDrop(files) {
                try {
                    for (const file of files) {
                        if (file.type.startsWith('text/')) {
                            // 读取文本文件
                            const reader = new FileReader();
                            reader.onload = (e) => {
                                this.insertAtCursor(e.target.result);
                            };
                            reader.readAsText(file);
                        }
                    }
                } catch (error) {
                    console.error('处理文件拖拽失败:', error);
                }
            }
            
            // 获取文本统计
            getTextStatistics() {
                return { ...this.textStats };
            }
            
            // 获取编辑历史
            getEditHistory() {
                return {
                    history: [...this.editHistory],
                    currentIndex: this.historyIndex
                };
            }
            
            // 清除编辑历史
            clearEditHistory() {
                this.editHistory = [];
                this.historyIndex = -1;
                
                // 保存当前状态
                const textarea = this.targetEditElement.el;
                if (textarea) {
                    this.saveToHistory(textarea.value);
                }
            }
        }
        
        // 增强的字段注册
        const enhancedEmojisTextField = {
            ...emojisTextField,
            component: EnhancedEmojisTextField,
            additionalClasses: [...(emojisTextField.additionalClasses || []), "o_field_text_enhanced"],
            
            // 提取属性
            extractProps: ({ attrs, field }) => {
                const props = {};
                
                // 基础属性
                Object.assign(props, textField.extractProps({ attrs, field }));
                
                // 增强配置
                if (attrs.enable_auto_resize) {
                    props.enableAutoResize = attrs.enable_auto_resize;
                }
                
                if (attrs.min_rows) {
                    props.minRows = parseInt(attrs.min_rows);
                }
                
                if (attrs.max_rows) {
                    props.maxRows = parseInt(attrs.max_rows);
                }
                
                if (attrs.enable_word_wrap) {
                    props.enableWordWrap = attrs.enable_word_wrap;
                }
                
                return props;
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("text_emojis_enhanced", enhancedEmojisTextField);
    }
};

// 应用表情符号文本字段增强
EmojisTextFieldEnhancer.enhanceEmojisTextField();
```

## 技术特点

### 1. 多重继承
- 继承EmojisFieldCommon的表情符号功能
- 继承TextField的文本编辑功能
- 完美结合两者的特性

### 2. 多行编辑
- 支持多行文本输入
- 使用textarea元素
- 丰富的文本编辑功能

### 3. 模板系统
- 使用专用的EmojisTextField模板
- 继承TextField的组件系统
- 保持UI的一致性

### 4. 字段注册
- 标准的字段注册机制
- 配置继承和扩展
- 全局可用的字段类型

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- EmojisFieldCommon作为功能混入
- 增强现有字段的功能

### 2. 装饰器模式 (Decorator Pattern)
- 为TextField添加表情符号功能
- 不改变原有结构

### 3. 组合模式 (Composition Pattern)
- 组合多个功能组件
- 创建复合功能字段

## 注意事项

1. **多行处理**: 确保表情符号在多行文本中的正确显示
2. **光标管理**: 在多行环境中正确管理光标位置
3. **性能考虑**: 避免大量文本时的性能问题
4. **格式保持**: 保持文本格式的完整性

## 扩展建议

1. **自动调整大小**: 根据内容自动调整文本区域大小
2. **语法高亮**: 为特定格式提供语法高亮
3. **行号显示**: 添加行号显示功能
4. **搜索替换**: 提供文本搜索和替换功能
5. **格式化工具**: 添加文本格式化工具

该表情符号文本字段为邮件系统提供了重要的富文本编辑功能，是内容创作和用户表达的核心组件。

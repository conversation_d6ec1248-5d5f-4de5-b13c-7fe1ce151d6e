# Many2One Avatar User Field - 多对一用户头像字段

## 概述

`many2one_avatar_user_field.js` 是 Odoo 邮件系统的多对一用户头像字段组件，专门用于显示和选择单个用户的头像。该组件基于Web框架的多对一头像字段和邮件系统的用户分配功能，集成了头像显示、用户卡片、命令钩子等核心功能，为邮件系统提供了完整的单用户选择和可视化支持，是邮件系统用户管理和分配的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js`
- **行数**: 116
- **模块**: `@mail/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field`

## 依赖关系

```javascript
// 核心依赖
'@mail/views/web/fields/assign_user_command_hook'                    // 用户分配命令钩子
'@web/core/l10n/translation'                                         // 国际化翻译
'@web/core/registry'                                                 // 注册表
'@web/views/fields/many2one_avatar/many2one_avatar_field'           // 多对一头像字段
'@web/core/popover/popover_hook'                                     // 弹出框钩子
'@mail/discuss/web/avatar_card/avatar_card_popover'                  // 头像卡片弹出框
```

## 核心功能

### 1. WithUserChatter 混入

```javascript
const WithUserChatter = (T) =>
    (class extends T {
        setup() {
            super.setup(...arguments);
            if (this.props.withCommand) {
                useAssignUserCommand();
            }
            this.avatarCard = usePopover(AvatarCardPopover);
        }

        get displayAvatarCard() {
            return this.relation === "res.users";
        }

        getAvatarCardProps() {
            return {
                id: this.props.record.data[this.props.name][0] ?? false,
            };
        }

        onClickAvatar(ev) {
            const id = this.props.record.data[this.props.name][0] ?? false;
            if (id !== false) {
                if (!this.displayAvatarCard) {
                    return;
                }
                const target = ev.currentTarget;
                if (!this.avatarCard.isOpen) {
                    this.avatarCard.open(target, this.getAvatarCardProps());
                }
            }
        }
    });
```

**混入功能**:
- **命令集成**: 集成用户分配命令功能
- **弹出卡片**: 集成头像卡片弹出框
- **点击处理**: 处理头像点击事件
- **条件显示**: 仅对用户关系显示卡片

### 2. Many2OneAvatarUserField 组件

```javascript
const Many2OneAvatarUserField = class Many2OneAvatarUserField extends WithUserChatter(Many2OneAvatarField) {
    static template = "mail.Many2OneAvatarUserField";
    static props = {
        ...Many2OneAvatarField.props,
        withCommand: { type: Boolean, optional: true },
    };
}
```

**组件特性**:
- **混入增强**: 使用WithUserChatter混入增强功能
- **专用模板**: 使用专门的用户头像字段模板
- **命令支持**: 支持命令功能的开关控制
- **属性扩展**: 扩展基础字段的属性

### 3. KanbanMany2OneAvatarUserField 组件

```javascript
const KanbanMany2OneAvatarUserField = class KanbanMany2OneAvatarUserField extends WithUserChatter(KanbanMany2OneAvatarField) {
    static template = "mail.KanbanMany2OneAvatarUserField";
    static props = {
        ...KanbanMany2OneAvatarField.props,
        displayAvatarName: { type: Boolean, optional: true },
    };

    get popoverProps() {
        const props = super.popoverProps;
        delete props.displayAvatarName;
        return props;
    }
}
```

**看板组件特性**:
- **看板优化**: 专门针对看板视图优化
- **名称显示**: 支持头像名称的显示控制
- **属性过滤**: 过滤弹出框不需要的属性
- **专用模板**: 使用看板专用模板

### 4. 字段配置

```javascript
// 通用字段配置
const many2OneAvatarUserField = {
    ...many2OneAvatarField,
    component: Many2OneAvatarUserField,
    additionalClasses: ["o_field_many2one_avatar"],
    extractProps(fieldInfo, dynamicInfo) {
        const props = many2OneAvatarField.extractProps(...arguments);
        props.withCommand = fieldInfo.viewType === "form" || fieldInfo.viewType === "list";
        return props;
    },
};

// 看板字段配置
const kanbanMany2OneAvatarUserField = {
    ...kanbanMany2OneAvatarField,
    component: KanbanMany2OneAvatarUserField,
    additionalClasses: [...kanbanMany2OneAvatarField.additionalClasses, "o_field_many2one_avatar"],
    supportedOptions: [
        ...(kanbanMany2OneAvatarField.supportedOptions || []),
        {
            label: _t("Display avatar name"),
            name: "display_avatar_name",
            type: "boolean",
        },
    ],
    extractProps({ options }) {
        const props = kanbanMany2OneAvatarField.extractProps(...arguments);
        props.displayAvatarName = options.display_avatar_name || false;
        return props;
    },
};
```

**配置功能**:
- **配置继承**: 继承基础头像字段配置
- **组件替换**: 使用用户专用组件
- **命令控制**: 根据视图类型控制命令功能
- **选项支持**: 支持头像名称显示选项

### 5. 字段注册

```javascript
// 通用注册
registry.category("fields").add("many2one_avatar_user", many2OneAvatarUserField);

// 看板注册
registry.category("fields").add("kanban.many2one_avatar_user", kanbanMany2OneAvatarUserField);

// 活动注册
registry.category("fields").add("activity.many2one_avatar_user", kanbanMany2OneAvatarUserField);
```

**注册功能**:
- **多类型注册**: 为不同视图类型注册不同的字段实现
- **命名空间**: 使用视图前缀区分不同的字段类型
- **复用配置**: 活动视图复用看板字段配置

## 使用场景

### 1. 多对一用户头像字段增强

```javascript
// 多对一用户头像字段增强功能
const Many2OneAvatarUserFieldEnhancer = {
    enhanceMany2OneAvatarUserField: () => {
        // 增强的用户聊天混入
        const EnhancedWithUserChatter = (T) =>
            (class extends T {
                setup() {
                    super.setup(...arguments);
                    
                    // 增强的配置选项
                    this.enhancedConfig = {
                        enableQuickActions: true,
                        enableUserSearch: true,
                        enablePresenceIndicator: true,
                        enableRoleDisplay: true,
                        enablePermissionCheck: true,
                        enableUserHistory: true,
                        enableAutoAssign: false,
                        enableNotifications: true,
                        showUserDetails: true,
                        enableUserFiltering: false,
                        cacheUserData: true
                    };
                    
                    // 用户状态映射
                    this.userStatuses = {
                        'online': { color: '#28a745', icon: 'fa-circle', text: '在线' },
                        'away': { color: '#ffc107', icon: 'fa-circle', text: '离开' },
                        'busy': { color: '#dc3545', icon: 'fa-circle', text: '忙碌' },
                        'offline': { color: '#6c757d', icon: 'fa-circle', text: '离线' }
                    };
                    
                    // 用户角色映射
                    this.userRoles = {
                        'admin': { color: '#dc3545', icon: 'fa-crown', text: '管理员' },
                        'manager': { color: '#ffc107', icon: 'fa-star', text: '经理' },
                        'user': { color: '#17a2b8', icon: 'fa-user', text: '用户' },
                        'guest': { color: '#6c757d', icon: 'fa-eye', text: '访客' }
                    };
                    
                    // 用户数据缓存
                    this.userCache = new Map();
                    
                    // 最近分配的用户
                    this.recentUsers = this.loadRecentUsers();
                    
                    // 初始化增强功能
                    this.initializeEnhancements();
                }
                
                // 初始化增强功能
                initializeEnhancements() {
                    // 设置用户搜索
                    if (this.enhancedConfig.enableUserSearch) {
                        this.setupUserSearch();
                    }
                    
                    // 设置快速操作
                    if (this.enhancedConfig.enableQuickActions) {
                        this.setupQuickActions();
                    }
                    
                    // 设置自动分配
                    if (this.enhancedConfig.enableAutoAssign) {
                        this.setupAutoAssign();
                    }
                    
                    // 设置通知
                    if (this.enhancedConfig.enableNotifications) {
                        this.setupNotifications();
                    }
                }
                
                // 增强的头像卡片显示
                get displayAvatarCard() {
                    // 检查权限
                    if (this.enhancedConfig.enablePermissionCheck && !this.hasViewPermission()) {
                        return false;
                    }
                    
                    return this.relation === "res.users";
                }
                
                // 检查查看权限
                hasViewPermission() {
                    // 这里可以实现权限检查逻辑
                    return true;
                }
                
                // 增强的头像卡片属性
                getAvatarCardProps() {
                    const userId = this.props.record.data[this.props.name][0] ?? false;
                    const baseProps = {
                        id: userId,
                    };
                    
                    // 添加增强属性
                    if (this.enhancedConfig.enablePresenceIndicator) {
                        baseProps.showPresence = true;
                    }
                    
                    if (this.enhancedConfig.enableRoleDisplay) {
                        baseProps.showRole = true;
                    }
                    
                    if (this.enhancedConfig.enableQuickActions) {
                        baseProps.quickActions = this.getUserQuickActions(userId);
                    }
                    
                    if (this.enhancedConfig.showUserDetails) {
                        baseProps.showDetails = true;
                    }
                    
                    return baseProps;
                }
                
                // 获取用户快速操作
                getUserQuickActions(userId) {
                    if (!userId) return [];
                    
                    const actions = [];
                    
                    // 发送消息
                    actions.push({
                        id: 'message',
                        icon: 'fa-envelope',
                        text: '发送消息',
                        action: () => this.sendMessage(userId)
                    });
                    
                    // 查看资料
                    actions.push({
                        id: 'profile',
                        icon: 'fa-user',
                        text: '查看资料',
                        action: () => this.viewProfile(userId)
                    });
                    
                    // 分配任务
                    actions.push({
                        id: 'assign',
                        icon: 'fa-tasks',
                        text: '分配任务',
                        action: () => this.assignTask(userId)
                    });
                    
                    // 更换用户
                    if (!this.props.readonly) {
                        actions.push({
                            id: 'change',
                            icon: 'fa-exchange',
                            text: '更换用户',
                            action: () => this.changeUser()
                        });
                    }
                    
                    return actions;
                }
                
                // 增强的头像点击处理
                onClickAvatar(ev) {
                    try {
                        const userId = this.props.record.data[this.props.name][0] ?? false;
                        
                        // 记录用户交互
                        if (userId) {
                            this.recordUserInteraction(userId, 'avatar_click');
                        }
                        
                        // 执行原有逻辑
                        super.onClickAvatar(ev);
                        
                        // 更新最近用户
                        if (userId) {
                            this.updateRecentUsers(userId);
                        }
                    } catch (error) {
                        console.error('处理头像点击失败:', error);
                    }
                }
                
                // 获取用户信息
                async getUserInfo(userId) {
                    if (!userId) return null;
                    
                    try {
                        // 检查缓存
                        if (this.enhancedConfig.cacheUserData && this.userCache.has(userId)) {
                            return this.userCache.get(userId);
                        }
                        
                        // 从记录中获取基础信息
                        const userRecord = this.props.record.data[this.props.name];
                        if (userRecord && userRecord[0] === userId) {
                            const userInfo = {
                                id: userId,
                                name: userRecord[1],
                                status: this.getUserStatus(userId),
                                role: this.getUserRole(userId),
                                lastSeen: this.getUserLastSeen(userId),
                                isActive: this.getUserActiveStatus(userId)
                            };
                            
                            // 缓存用户信息
                            if (this.enhancedConfig.cacheUserData) {
                                this.userCache.set(userId, userInfo);
                            }
                            
                            return userInfo;
                        }
                        
                        return null;
                    } catch (error) {
                        console.error('获取用户信息失败:', error);
                        return null;
                    }
                }
                
                // 获取用户状态
                getUserStatus(userId) {
                    // 这里可以从用户数据中获取状态
                    // 简化处理，返回在线状态
                    return this.userStatuses['online'];
                }
                
                // 获取用户角色
                getUserRole(userId) {
                    // 这里可以从用户数据中获取角色
                    // 简化处理，返回用户角色
                    return this.userRoles['user'];
                }
                
                // 获取用户最后在线时间
                getUserLastSeen(userId) {
                    // 这里可以获取用户最后在线时间
                    return new Date();
                }
                
                // 获取用户活跃状态
                getUserActiveStatus(userId) {
                    // 这里可以获取用户活跃状态
                    return true;
                }
                
                // 记录用户交互
                recordUserInteraction(userId, action) {
                    console.log('用户交互:', userId, action);
                    
                    // 这里可以记录用户交互数据
                    // 用于分析和推荐
                }
                
                // 更新最近用户
                updateRecentUsers(userId) {
                    try {
                        if (!this.enhancedConfig.enableUserHistory) return;
                        
                        // 移除已存在的
                        this.recentUsers = this.recentUsers.filter(id => id !== userId);
                        
                        // 添加到开头
                        this.recentUsers.unshift(userId);
                        
                        // 限制数量
                        if (this.recentUsers.length > 10) {
                            this.recentUsers = this.recentUsers.slice(0, 10);
                        }
                        
                        // 保存到本地存储
                        this.saveRecentUsers();
                    } catch (error) {
                        console.error('更新最近用户失败:', error);
                    }
                }
                
                // 发送消息
                sendMessage(userId) {
                    console.log('发送消息给用户:', userId);
                    // 实现发送消息的逻辑
                }
                
                // 查看用户资料
                viewProfile(userId) {
                    console.log('查看用户资料:', userId);
                    // 实现查看用户资料的逻辑
                }
                
                // 分配任务
                assignTask(userId) {
                    console.log('分配任务给用户:', userId);
                    // 实现分配任务的逻辑
                }
                
                // 更换用户
                changeUser() {
                    console.log('更换用户');
                    // 实现更换用户的逻辑
                }
                
                // 设置用户搜索
                setupUserSearch() {
                    // 实现用户搜索功能
                    console.log('设置用户搜索');
                }
                
                // 设置快速操作
                setupQuickActions() {
                    // 实现快速操作功能
                    console.log('设置快速操作');
                }
                
                // 设置自动分配
                setupAutoAssign() {
                    // 实现自动分配功能
                    console.log('设置自动分配');
                }
                
                // 设置通知
                setupNotifications() {
                    // 实现通知功能
                    console.log('设置通知');
                }
                
                // 加载最近用户
                loadRecentUsers() {
                    try {
                        const stored = localStorage.getItem('mail_recent_users');
                        return stored ? JSON.parse(stored) : [];
                    } catch (error) {
                        console.error('加载最近用户失败:', error);
                        return [];
                    }
                }
                
                // 保存最近用户
                saveRecentUsers() {
                    try {
                        localStorage.setItem('mail_recent_users', JSON.stringify(this.recentUsers));
                    } catch (error) {
                        console.error('保存最近用户失败:', error);
                    }
                }
                
                // 获取推荐用户
                getRecommendedUsers() {
                    // 基于历史交互推荐用户
                    return this.recentUsers.slice(0, 5);
                }
                
                // 清除用户缓存
                clearUserCache() {
                    this.userCache.clear();
                }
                
                // 获取当前用户ID
                getCurrentUserId() {
                    return this.props.record.data[this.props.name][0] ?? false;
                }
                
                // 检查是否有用户
                hasUser() {
                    return this.getCurrentUserId() !== false;
                }
                
                // 获取用户显示名称
                getUserDisplayName() {
                    const userRecord = this.props.record.data[this.props.name];
                    return userRecord ? userRecord[1] : '';
                }
            });
        
        // 增强的多对一用户头像字段
        class EnhancedMany2OneAvatarUserField extends EnhancedWithUserChatter(Many2OneAvatarField) {
            static template = "mail.EnhancedMany2OneAvatarUserField";
            static props = {
                ...Many2OneAvatarField.props,
                withCommand: { type: Boolean, optional: true },
                enableQuickActions: { type: Boolean, optional: true },
                showUserStatus: { type: Boolean, optional: true },
                showUserRole: { type: Boolean, optional: true }
            };
            
            setup() {
                super.setup();
                
                // 监听用户变更
                this.env.bus.addEventListener('user-changed', this.onUserChanged.bind(this));
            }
            
            // 处理用户变更
            onUserChanged(event) {
                const { userId, action } = event.detail;
                
                if (userId === this.getCurrentUserId()) {
                    // 刷新用户信息
                    this.clearUserCache();
                    this.render();
                }
            }
            
            // 获取渲染数据
            getRenderData() {
                try {
                    const userId = this.getCurrentUserId();
                    const userInfo = userId ? this.getUserInfo(userId) : null;
                    
                    return {
                        hasUser: this.hasUser(),
                        userId: userId,
                        userInfo: userInfo,
                        displayName: this.getUserDisplayName(),
                        quickActions: this.enhancedConfig.enableQuickActions ? this.getUserQuickActions(userId) : [],
                        showStatus: this.props.showUserStatus && userInfo,
                        showRole: this.props.showUserRole && userInfo,
                        config: this.enhancedConfig
                    };
                } catch (error) {
                    console.error('获取渲染数据失败:', error);
                    return {
                        hasUser: false,
                        userId: false,
                        userInfo: null,
                        displayName: '',
                        quickActions: [],
                        showStatus: false,
                        showRole: false,
                        config: this.enhancedConfig
                    };
                }
            }
        }
        
        // 增强的看板字段
        class EnhancedKanbanMany2OneAvatarUserField extends EnhancedWithUserChatter(KanbanMany2OneAvatarField) {
            static template = "mail.EnhancedKanbanMany2OneAvatarUserField";
            static props = {
                ...KanbanMany2OneAvatarField.props,
                displayAvatarName: { type: Boolean, optional: true },
                compactMode: { type: Boolean, optional: true },
                showUserBadge: { type: Boolean, optional: true }
            };
            
            get popoverProps() {
                const props = super.popoverProps;
                delete props.displayAvatarName;
                delete props.compactMode;
                delete props.showUserBadge;
                return props;
            }
            
            // 获取用户徽章
            getUserBadge() {
                if (!this.props.showUserBadge) return null;
                
                const userId = this.getCurrentUserId();
                if (!userId) return null;
                
                const userInfo = this.getUserInfo(userId);
                if (!userInfo) return null;
                
                // 根据用户角色返回徽章
                const role = userInfo.role;
                if (role && role.text !== '用户') {
                    return {
                        text: role.text,
                        color: role.color,
                        icon: role.icon
                    };
                }
                
                return null;
            }
        }
        
        // 增强的字段配置
        const enhancedMany2OneAvatarUserField = {
            ...many2OneAvatarUserField,
            component: EnhancedMany2OneAvatarUserField,
            additionalClasses: [...(many2OneAvatarUserField.additionalClasses || []), "o_field_many2one_avatar_user_enhanced"],
            
            // 增强的属性提取
            extractProps(fieldInfo, dynamicInfo) {
                const props = many2OneAvatarUserField.extractProps(...arguments);
                
                // 添加增强配置
                if (fieldInfo.attrs.enable_quick_actions) {
                    props.enableQuickActions = fieldInfo.attrs.enable_quick_actions;
                }
                
                if (fieldInfo.attrs.show_user_status) {
                    props.showUserStatus = fieldInfo.attrs.show_user_status;
                }
                
                if (fieldInfo.attrs.show_user_role) {
                    props.showUserRole = fieldInfo.attrs.show_user_role;
                }
                
                return props;
            }
        };
        
        const enhancedKanbanMany2OneAvatarUserField = {
            ...kanbanMany2OneAvatarUserField,
            component: EnhancedKanbanMany2OneAvatarUserField,
            additionalClasses: [...(kanbanMany2OneAvatarUserField.additionalClasses || []), "o_field_kanban_many2one_avatar_user_enhanced"],
            
            // 增强的支持选项
            supportedOptions: [
                ...kanbanMany2OneAvatarUserField.supportedOptions,
                {
                    label: _t("Compact mode"),
                    name: "compact_mode",
                    type: "boolean",
                },
                {
                    label: _t("Show user badge"),
                    name: "show_user_badge",
                    type: "boolean",
                }
            ],
            
            // 增强的属性提取
            extractProps({ options }) {
                const props = kanbanMany2OneAvatarUserField.extractProps(...arguments);
                props.compactMode = options.compact_mode || false;
                props.showUserBadge = options.show_user_badge || false;
                return props;
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("many2one_avatar_user_enhanced", enhancedMany2OneAvatarUserField);
        registry.category("fields").add("kanban.many2one_avatar_user_enhanced", enhancedKanbanMany2OneAvatarUserField);
        registry.category("fields").add("activity.many2one_avatar_user_enhanced", enhancedKanbanMany2OneAvatarUserField);
    }
};

// 应用多对一用户头像字段增强
Many2OneAvatarUserFieldEnhancer.enhanceMany2OneAvatarUserField();
```

## 技术特点

### 1. 混入模式
- 使用WithUserChatter混入增强功能
- 可应用于不同的基础字段类型
- 保持代码的可复用性

### 2. 多视图适配
- 针对不同视图类型提供专门的实现
- 优化的显示逻辑和交互体验
- 统一的API接口

### 3. 弹出框集成
- 集成头像卡片弹出框
- 智能的弹出框状态管理
- 流畅的用户交互体验

### 4. 命令系统集成
- 集成用户分配命令功能
- 支持快捷键操作
- 提高用户操作效率

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- WithUserChatter作为功能混入
- 为不同字段类型添加用户功能

### 2. 装饰器模式 (Decorator Pattern)
- 为基础字段添加用户特定功能
- 不改变原有结构

### 3. 工厂模式 (Factory Pattern)
- 根据视图类型创建不同的字段实现
- 统一的创建接口

## 注意事项

1. **权限检查**: 确保用户有查看其他用户信息的权限
2. **性能考虑**: 避免频繁的用户数据查询
3. **状态同步**: 保持弹出框状态的正确同步
4. **响应式设计**: 适配不同屏幕尺寸的显示

## 扩展建议

1. **用户搜索**: 添加用户搜索和过滤功能
2. **快速操作**: 支持更多的用户快速操作
3. **状态指示**: 显示用户的在线状态
4. **角色显示**: 显示用户的角色信息
5. **历史记录**: 记录用户分配历史

该多对一用户头像字段为邮件系统提供了重要的单用户管理功能，是用户分配和选择的核心组件。

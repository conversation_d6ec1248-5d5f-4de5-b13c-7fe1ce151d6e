# Many2Many Tags Email - 多对多邮件标签字段

## 概述

`many2many_tags_email.js` 是 Odoo 邮件系统的多对多邮件标签字段组件，专门用于管理带有邮件地址的标签关系。该组件基于Web框架的多对多标签字段和关系工具，集成了邮件验证、自动编辑、记录管理等核心功能，为邮件系统提供了完整的邮件联系人管理和标签化支持，是邮件系统联系人管理和邮件发送的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js`
- **行数**: 130
- **模块**: `@mail/views/web/fields/many2many_tags_email/many2many_tags_email`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                      // OWL 框架
'@web/core/l10n/translation'                                     // 国际化翻译
'@web/core/registry'                                             // 注册表
'@web/core/tags_list/tags_list'                                 // 标签列表
'@web/views/fields/many2many_tags/many2many_tags_field'         // 多对多标签字段
'@web/views/fields/relational_utils'                            // 关系工具
```

## 核心功能

### 1. FieldMany2ManyTagsEmailTagsList 组件

```javascript
const FieldMany2ManyTagsEmailTagsList = class FieldMany2ManyTagsEmailTagsList extends TagsList {
    static template = "FieldMany2ManyTagsEmailTagsList";
}
```

**组件特性**:
- **继承扩展**: 继承TagsList的所有功能
- **专用模板**: 使用专门的邮件标签列表模板
- **邮件优化**: 针对邮件显示进行优化

### 2. FieldMany2ManyTagsEmail 主组件

```javascript
const FieldMany2ManyTagsEmail = class FieldMany2ManyTagsEmail extends Many2ManyTagsField {
    static components = {
        ...FieldMany2ManyTagsEmail.components,
        TagsList: FieldMany2ManyTagsEmailTagsList,
    };
    static props = {
        ...Many2ManyTagsField.props,
        context: { type: Object, optional: true },
    };

    setup() {
        super.setup();

        this.openedDialogs = 0;
        this.recordsIdsToAdd = [];
        this.openMany2xRecord = useOpenMany2XRecord({
            resModel: this.relation,
            activeActions: {
                create: false,
                createEdit: false,
                write: true,
            },
            isToMany: true,
            onRecordSaved: async (record) => {
                if (record.data.email) {
                    this.recordsIdsToAdd.push(record.resId);
                }
            },
            fieldString: this.props.string,
        });

        const update = this.update;
        this.update = async (object) => {
            await update(object);
            await this.checkEmails();
        };

        onMounted(() => {
            this.checkEmails();
        });
    }
}
```

**主组件特性**:
- **组件替换**: 使用专门的邮件标签列表组件
- **上下文支持**: 支持上下文属性传递
- **对话框管理**: 管理打开的编辑对话框数量
- **记录追踪**: 追踪需要添加的记录ID
- **自动验证**: 挂载和更新时自动检查邮件

### 3. 邮件验证功能

```javascript
async checkEmails() {
    const list = this.props.record.data[this.props.name];
    const invalidRecords = list.records.filter((record) => !record.data.email);
    if (!invalidRecords.length) {
        return;
    }
    // Remove records with invalid data, open form view to edit those and readd them if they are updated correctly.
    const dialogDefs = [];
    for (const record of invalidRecords) {
        dialogDefs.push(
            this.openMany2xRecord({
                resId: record.resId,
                context: this.props.context,
                title: _t("Edit: %s", record.data.display_name),
            })
        );
    }
    this.openedDialogs += invalidRecords.length;
    await Promise.all(dialogDefs);

    this.openedDialogs -= invalidRecords.length;
    if (this.openedDialogs) {
        return;
    }

    const invalidRecordIds = invalidRecords.map((rec) => rec.resId);
    await list.addAndRemove({
        remove: invalidRecordIds.filter((id) => !this.recordsIdsToAdd.includes(id)),
        reload: true,
    });
    this.recordsIdsToAdd = [];
}
```

**验证功能**:
- **邮件检查**: 检查所有记录是否有有效的邮件地址
- **自动编辑**: 自动打开编辑对话框处理无效记录
- **批量处理**: 并行处理多个无效记录
- **智能移除**: 仅移除未修复的无效记录
- **状态管理**: 管理对话框状态和记录状态

### 4. 标签增强

```javascript
get tags() {
    // Add email to our tags
    const tags = super.tags;
    const emailByResId = this.props.record.data[this.props.name].records.reduce(
        (acc, record) => {
            acc[record.resId] = record.data.email;
            return acc;
        },
        {}
    );
    tags.forEach((tag) => (tag.email = emailByResId[tag.resId]));
    return tags;
}
```

**标签增强功能**:
- **邮件附加**: 为每个标签附加邮件地址信息
- **数据映射**: 建立记录ID到邮件地址的映射
- **标签丰富**: 丰富标签的显示信息
- **性能优化**: 使用reduce优化数据处理

### 5. 字段配置

```javascript
const fieldMany2ManyTagsEmail = {
    ...many2ManyTagsField,
    component: FieldMany2ManyTagsEmail,
    extractProps(fieldInfo, dynamicInfo) {
        const props = many2ManyTagsField.extractProps(...arguments);
        props.context = dynamicInfo.context;
        return props;
    },
    relatedFields: (fieldInfo) => {
        return [...many2ManyTagsField.relatedFields(fieldInfo), { name: "email", type: "char" }];
    },
    additionalClasses: ["o_field_many2many_tags"],
};
```

**配置功能**:
- **配置继承**: 继承基础多对多标签字段配置
- **组件替换**: 使用邮件专用组件
- **属性提取**: 提取上下文属性
- **字段关联**: 添加email字段关联
- **样式类**: 添加特定的CSS类

## 使用场景

### 1. 多对多邮件标签字段增强

```javascript
// 多对多邮件标签字段增强功能
const Many2ManyTagsEmailEnhancer = {
    enhanceMany2ManyTagsEmail: () => {
        // 增强的邮件标签字段
        class EnhancedFieldMany2ManyTagsEmail extends FieldMany2ManyTagsEmail {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableEmailValidation: true,
                    enableAutoComplete: true,
                    enableBulkImport: true,
                    enableEmailPreview: true,
                    enableDuplicateCheck: true,
                    enableEmailFormat: true,
                    enableDomainValidation: true,
                    enableBlacklist: false,
                    enableWhitelist: false,
                    maxEmailLength: 254,
                    allowedDomains: [],
                    blockedDomains: [],
                    emailPattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                };
                
                // 邮件验证规则
                this.validationRules = {
                    required: true,
                    format: this.enhancedConfig.emailPattern,
                    maxLength: this.enhancedConfig.maxEmailLength,
                    domains: {
                        allowed: this.enhancedConfig.allowedDomains,
                        blocked: this.enhancedConfig.blockedDomains
                    }
                };
                
                // 邮件缓存
                this.emailCache = new Map();
                
                // 验证结果缓存
                this.validationCache = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置自动完成
                if (this.enhancedConfig.enableAutoComplete) {
                    this.setupAutoComplete();
                }
                
                // 设置批量导入
                if (this.enhancedConfig.enableBulkImport) {
                    this.setupBulkImport();
                }
                
                // 设置重复检查
                if (this.enhancedConfig.enableDuplicateCheck) {
                    this.setupDuplicateCheck();
                }
                
                // 设置邮件预览
                if (this.enhancedConfig.enableEmailPreview) {
                    this.setupEmailPreview();
                }
            }
            
            // 增强的邮件检查
            async checkEmails() {
                try {
                    const list = this.props.record.data[this.props.name];
                    const records = list.records;
                    
                    // 验证所有邮件
                    const validationResults = await this.validateEmails(records);
                    
                    // 处理无效邮件
                    const invalidRecords = records.filter((record, index) => 
                        !validationResults[index].isValid
                    );
                    
                    if (!invalidRecords.length) {
                        return;
                    }
                    
                    // 显示验证错误
                    this.showValidationErrors(invalidRecords, validationResults);
                    
                    // 执行原有逻辑
                    await super.checkEmails();
                } catch (error) {
                    console.error('增强邮件检查失败:', error);
                    // 回退到原有逻辑
                    await super.checkEmails();
                }
            }
            
            // 验证邮件列表
            async validateEmails(records) {
                const validationPromises = records.map(record => 
                    this.validateEmail(record.data.email, record.resId)
                );
                
                return await Promise.all(validationPromises);
            }
            
            // 验证单个邮件
            async validateEmail(email, recordId) {
                try {
                    // 检查缓存
                    const cacheKey = `${email}_${recordId}`;
                    if (this.validationCache.has(cacheKey)) {
                        return this.validationCache.get(cacheKey);
                    }
                    
                    const result = {
                        email: email,
                        recordId: recordId,
                        isValid: true,
                        errors: []
                    };
                    
                    // 基础验证
                    if (!email) {
                        result.isValid = false;
                        result.errors.push('邮件地址不能为空');
                        this.validationCache.set(cacheKey, result);
                        return result;
                    }
                    
                    // 格式验证
                    if (this.enhancedConfig.enableEmailFormat && !this.validationRules.format.test(email)) {
                        result.isValid = false;
                        result.errors.push('邮件地址格式不正确');
                    }
                    
                    // 长度验证
                    if (email.length > this.validationRules.maxLength) {
                        result.isValid = false;
                        result.errors.push(`邮件地址长度不能超过${this.validationRules.maxLength}个字符`);
                    }
                    
                    // 域名验证
                    if (this.enhancedConfig.enableDomainValidation) {
                        const domainValidation = this.validateEmailDomain(email);
                        if (!domainValidation.isValid) {
                            result.isValid = false;
                            result.errors.push(...domainValidation.errors);
                        }
                    }
                    
                    // 重复检查
                    if (this.enhancedConfig.enableDuplicateCheck) {
                        const duplicateCheck = await this.checkEmailDuplicate(email, recordId);
                        if (!duplicateCheck.isValid) {
                            result.isValid = false;
                            result.errors.push(...duplicateCheck.errors);
                        }
                    }
                    
                    // 黑白名单检查
                    const listCheck = this.checkEmailLists(email);
                    if (!listCheck.isValid) {
                        result.isValid = false;
                        result.errors.push(...listCheck.errors);
                    }
                    
                    // 缓存结果
                    this.validationCache.set(cacheKey, result);
                    
                    return result;
                } catch (error) {
                    console.error('验证邮件失败:', error);
                    return {
                        email: email,
                        recordId: recordId,
                        isValid: false,
                        errors: ['验证过程中发生错误']
                    };
                }
            }
            
            // 验证邮件域名
            validateEmailDomain(email) {
                const result = { isValid: true, errors: [] };
                
                try {
                    const domain = email.split('@')[1];
                    if (!domain) {
                        result.isValid = false;
                        result.errors.push('邮件地址缺少域名部分');
                        return result;
                    }
                    
                    // 检查允许的域名
                    if (this.validationRules.domains.allowed.length > 0) {
                        if (!this.validationRules.domains.allowed.includes(domain)) {
                            result.isValid = false;
                            result.errors.push(`域名 ${domain} 不在允许列表中`);
                        }
                    }
                    
                    // 检查阻止的域名
                    if (this.validationRules.domains.blocked.includes(domain)) {
                        result.isValid = false;
                        result.errors.push(`域名 ${domain} 在阻止列表中`);
                    }
                } catch (error) {
                    result.isValid = false;
                    result.errors.push('域名验证失败');
                }
                
                return result;
            }
            
            // 检查邮件重复
            async checkEmailDuplicate(email, recordId) {
                const result = { isValid: true, errors: [] };
                
                try {
                    const list = this.props.record.data[this.props.name];
                    const duplicates = list.records.filter(record => 
                        record.data.email === email && record.resId !== recordId
                    );
                    
                    if (duplicates.length > 0) {
                        result.isValid = false;
                        result.errors.push(`邮件地址 ${email} 已存在`);
                    }
                } catch (error) {
                    console.error('检查邮件重复失败:', error);
                }
                
                return result;
            }
            
            // 检查邮件黑白名单
            checkEmailLists(email) {
                const result = { isValid: true, errors: [] };
                
                try {
                    // 检查白名单
                    if (this.enhancedConfig.enableWhitelist && this.enhancedConfig.allowedDomains.length > 0) {
                        const domain = email.split('@')[1];
                        if (!this.enhancedConfig.allowedDomains.includes(domain)) {
                            result.isValid = false;
                            result.errors.push('邮件域名不在白名单中');
                        }
                    }
                    
                    // 检查黑名单
                    if (this.enhancedConfig.enableBlacklist && this.enhancedConfig.blockedDomains.length > 0) {
                        const domain = email.split('@')[1];
                        if (this.enhancedConfig.blockedDomains.includes(domain)) {
                            result.isValid = false;
                            result.errors.push('邮件域名在黑名单中');
                        }
                    }
                } catch (error) {
                    console.error('检查邮件列表失败:', error);
                }
                
                return result;
            }
            
            // 显示验证错误
            showValidationErrors(invalidRecords, validationResults) {
                const errors = [];
                
                invalidRecords.forEach((record, index) => {
                    const validation = validationResults.find(v => v.recordId === record.resId);
                    if (validation && validation.errors.length > 0) {
                        errors.push(`${record.data.display_name}: ${validation.errors.join(', ')}`);
                    }
                });
                
                if (errors.length > 0) {
                    console.warn('邮件验证错误:', errors);
                    // 这里可以显示用户友好的错误消息
                }
            }
            
            // 设置自动完成
            setupAutoComplete() {
                // 实现邮件自动完成功能
                console.log('设置邮件自动完成');
            }
            
            // 设置批量导入
            setupBulkImport() {
                // 实现批量邮件导入功能
                console.log('设置批量邮件导入');
            }
            
            // 设置重复检查
            setupDuplicateCheck() {
                // 实现重复邮件检查功能
                console.log('设置重复邮件检查');
            }
            
            // 设置邮件预览
            setupEmailPreview() {
                // 实现邮件预览功能
                console.log('设置邮件预览');
            }
            
            // 增强的标签获取
            get tags() {
                try {
                    const tags = super.tags;
                    
                    // 为标签添加验证状态
                    tags.forEach(tag => {
                        if (tag.email) {
                            const cacheKey = `${tag.email}_${tag.resId}`;
                            const validation = this.validationCache.get(cacheKey);
                            tag.isValidEmail = validation ? validation.isValid : true;
                            tag.emailErrors = validation ? validation.errors : [];
                        }
                    });
                    
                    return tags;
                } catch (error) {
                    console.error('获取增强标签失败:', error);
                    return super.tags;
                }
            }
            
            // 批量导入邮件
            async bulkImportEmails(emailList) {
                try {
                    const validEmails = [];
                    const invalidEmails = [];
                    
                    // 验证所有邮件
                    for (const email of emailList) {
                        const validation = await this.validateEmail(email, null);
                        if (validation.isValid) {
                            validEmails.push(email);
                        } else {
                            invalidEmails.push({ email, errors: validation.errors });
                        }
                    }
                    
                    // 添加有效邮件
                    if (validEmails.length > 0) {
                        await this.addEmailsToList(validEmails);
                    }
                    
                    // 报告无效邮件
                    if (invalidEmails.length > 0) {
                        this.reportInvalidEmails(invalidEmails);
                    }
                    
                    return {
                        success: validEmails.length,
                        failed: invalidEmails.length,
                        invalidEmails: invalidEmails
                    };
                } catch (error) {
                    console.error('批量导入邮件失败:', error);
                    throw error;
                }
            }
            
            // 添加邮件到列表
            async addEmailsToList(emails) {
                // 实现添加邮件到列表的逻辑
                console.log('添加邮件到列表:', emails);
            }
            
            // 报告无效邮件
            reportInvalidEmails(invalidEmails) {
                console.warn('无效邮件:', invalidEmails);
                // 这里可以显示用户友好的错误报告
            }
            
            // 清除验证缓存
            clearValidationCache() {
                this.validationCache.clear();
                this.emailCache.clear();
            }
            
            // 获取邮件统计
            getEmailStatistics() {
                try {
                    const tags = this.tags;
                    const total = tags.length;
                    const valid = tags.filter(tag => tag.isValidEmail).length;
                    const invalid = total - valid;
                    
                    return {
                        total: total,
                        valid: valid,
                        invalid: invalid,
                        validRate: total > 0 ? (valid / total * 100).toFixed(2) : 0
                    };
                } catch (error) {
                    console.error('获取邮件统计失败:', error);
                    return { total: 0, valid: 0, invalid: 0, validRate: 0 };
                }
            }
        }
        
        // 增强的字段配置
        const enhancedFieldMany2ManyTagsEmail = {
            ...fieldMany2ManyTagsEmail,
            component: EnhancedFieldMany2ManyTagsEmail,
            additionalClasses: [...(fieldMany2ManyTagsEmail.additionalClasses || []), "o_field_many2many_tags_email_enhanced"],
            
            // 增强的属性提取
            extractProps(fieldInfo, dynamicInfo) {
                const props = fieldMany2ManyTagsEmail.extractProps(...arguments);
                
                // 添加验证配置
                if (fieldInfo.attrs.email_validation) {
                    props.emailValidation = JSON.parse(fieldInfo.attrs.email_validation);
                }
                
                if (fieldInfo.attrs.allowed_domains) {
                    props.allowedDomains = fieldInfo.attrs.allowed_domains.split(',');
                }
                
                if (fieldInfo.attrs.blocked_domains) {
                    props.blockedDomains = fieldInfo.attrs.blocked_domains.split(',');
                }
                
                return props;
            },
            
            // 增强的相关字段
            relatedFields: (fieldInfo) => {
                return [
                    ...fieldMany2ManyTagsEmail.relatedFields(fieldInfo),
                    { name: "email_validated", type: "boolean" },
                    { name: "email_validation_date", type: "datetime" }
                ];
            }
        };
        
        // 替换原始字段注册
        registry.category("fields").add("many2many_tags_email_enhanced", enhancedFieldMany2ManyTagsEmail);
    }
};

// 应用多对多邮件标签字段增强
Many2ManyTagsEmailEnhancer.enhanceMany2ManyTagsEmail();
```

## 技术特点

### 1. 邮件验证
- 自动检查邮件地址的有效性
- 智能的编辑对话框处理
- 批量验证和处理机制

### 2. 状态管理
- 对话框状态的精确管理
- 记录添加状态的追踪
- 异步操作的协调

### 3. 数据增强
- 为标签附加邮件信息
- 高效的数据映射处理
- 性能优化的数据操作

### 4. 国际化支持
- 使用翻译函数
- 支持多语言界面
- 本地化的用户体验

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 为基础标签字段添加邮件功能
- 不改变原有结构

### 2. 观察者模式 (Observer Pattern)
- 监听记录保存事件
- 响应数据变化

### 3. 策略模式 (Strategy Pattern)
- 不同的验证策略
- 可配置的处理逻辑

## 注意事项

1. **邮件验证**: 确保邮件地址的格式正确性
2. **性能考虑**: 避免大量邮件时的性能问题
3. **用户体验**: 提供友好的错误提示和编辑界面
4. **数据一致性**: 保持邮件数据的一致性

## 扩展建议

1. **高级验证**: 添加更复杂的邮件验证规则
2. **批量操作**: 支持批量邮件导入和编辑
3. **自动完成**: 实现邮件地址的自动完成
4. **重复检测**: 增强的重复邮件检测
5. **域名管理**: 支持域名白名单和黑名单

该多对多邮件标签字段为邮件系统提供了重要的联系人管理功能，是邮件发送和联系人组织的核心组件。

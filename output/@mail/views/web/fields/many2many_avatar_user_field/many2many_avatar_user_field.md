# Many2Many Avatar User Field - 多对多用户头像字段

## 概述

`many2many_avatar_user_field.js` 是 Odoo 邮件系统的多对多用户头像字段组件，专门用于显示和管理多个用户的头像标签。该组件基于Web框架的多对多头像标签字段和邮件系统的用户分配功能，集成了头像显示、用户卡片、命令钩子等核心功能，为邮件系统提供了完整的多用户选择和可视化支持，是邮件系统用户管理和协作的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/many2many_avatar_user_field/many2many_avatar_user_field.js`
- **行数**: 132
- **模块**: `@mail/views/web/fields/many2many_avatar_user_field/many2many_avatar_user_field`

## 依赖关系

```javascript
// 核心依赖
'@mail/views/web/fields/assign_user_command_hook'                    // 用户分配命令钩子
'@web/core/registry'                                                 // 注册表
'@web/core/tags_list/tags_list'                                     // 标签列表
'@web/core/popover/popover_hook'                                     // 弹出框钩子
'@mail/discuss/web/avatar_card/avatar_card_popover'                  // 头像卡片弹出框
'@web/views/fields/many2many_tags_avatar/many2many_tags_avatar_field' // 多对多头像标签字段
```

## 核心功能

### 1. Many2ManyAvatarUserTagsList 组件

```javascript
const Many2ManyAvatarUserTagsList = class Many2ManyAvatarUserTagsList extends TagsList {
    static template = "mail.Many2ManyAvatarUserTagsList";
}
```

**组件特性**:
- **继承扩展**: 继承TagsList的所有功能
- **专用模板**: 使用专门的用户头像标签列表模板
- **用户优化**: 针对用户显示进行优化

### 2. WithUserChatter 混入

```javascript
const WithUserChatter = (T) =>
    (class UserChatterMixin extends T {
        setup() {
            super.setup(...arguments);
            if (this.props.withCommand) {
                useAssignUserCommand();
            }
            this.avatarCard = usePopover(AvatarCardPopover);
        }

        displayAvatarCard(record) {
            return this.relation === "res.users";
        }

        getAvatarCardProps(record) {
            return {
                id: record.resId,
            };
        }

        getTagProps(record) {
            return {
                ...super.getTagProps(...arguments),
                onImageClicked: (ev) => {
                    if (!this.displayAvatarCard(record)) {
                        return;
                    }
                    const target = ev.currentTarget;
                    if (
                        !this.avatarCard.isOpen ||
                        (this.lastOpenedId && record.resId !== this.lastOpenedId)
                    ) {
                        this.avatarCard.open(target, this.getAvatarCardProps(record));
                        this.lastOpenedId = record.resId;
                    }
                },
            };
        }
    });
```

**混入功能**:
- **命令集成**: 集成用户分配命令功能
- **弹出卡片**: 集成头像卡片弹出框
- **点击处理**: 处理头像点击事件
- **状态管理**: 管理弹出框的打开状态

### 3. 多视图支持

```javascript
// 通用字段
const Many2ManyTagsAvatarUserField = class Many2ManyTagsAvatarUserField extends WithUserChatter(Many2ManyTagsAvatarField) {
    static components = {
        ...Many2ManyTagsAvatarField.components,
        TagsList: Many2ManyAvatarUserTagsList,
    };
}

// 看板字段
const KanbanMany2ManyTagsAvatarUserField = class KanbanMany2ManyTagsAvatarUserField extends WithUserChatter(
    KanbanMany2ManyTagsAvatarField
) {
    static template = "mail.KanbanMany2ManyTagsAvatarUserField";
    static components = {
        ...KanbanMany2ManyTagsAvatarField.components,
        TagsList: KanbanMany2ManyAvatarUserTagsList,
    };
    get displayText() {
        return !this.props.readonly;
    }
}

// 列表字段
const ListMany2ManyTagsAvatarUserField = class ListMany2ManyTagsAvatarUserField extends WithUserChatter(
    ListMany2ManyTagsAvatarField
) {
    static template = "mail.ListMany2ManyTagsAvatarUserField";
    static components = {
        ...ListMany2ManyTagsAvatarField.components,
        TagsList: Many2ManyAvatarUserTagsList,
    };

    get displayText() {
        return this.props.record.data[this.props.name].records.length === 1 || !this.props.readonly;
    }
}
```

**多视图特性**:
- **通用字段**: 适用于表单等通用视图
- **看板字段**: 专门针对看板视图优化
- **列表字段**: 专门针对列表视图优化
- **显示控制**: 根据上下文控制文本显示

### 4. 字段注册

```javascript
// 通用注册
registry.category("fields").add("many2many_avatar_user", many2ManyTagsAvatarUserField);

// 看板注册
registry.category("fields").add("kanban.many2many_avatar_user", kanbanMany2ManyTagsAvatarUserField);

// 列表注册
registry.category("fields").add("list.many2many_avatar_user", listMany2ManyTagsAvatarUserField);

// 活动注册
registry.category("fields").add("activity.many2many_avatar_user", kanbanMany2ManyTagsAvatarUserField);
```

**注册功能**:
- **多类型注册**: 为不同视图类型注册不同的字段实现
- **命名空间**: 使用视图前缀区分不同的字段类型
- **配置继承**: 继承基础字段的配置
- **样式增强**: 添加特定的CSS类

## 使用场景

### 1. 多对多用户头像字段增强

```javascript
// 多对多用户头像字段增强功能
const Many2ManyAvatarUserFieldEnhancer = {
    enhanceMany2ManyAvatarUserField: () => {
        // 增强的用户聊天混入
        const EnhancedWithUserChatter = (T) =>
            (class EnhancedUserChatterMixin extends T {
                setup() {
                    super.setup(...arguments);
                    
                    // 增强的配置选项
                    this.enhancedConfig = {
                        enableQuickActions: true,
                        enableBulkOperations: true,
                        enableUserSearch: true,
                        enableUserFiltering: true,
                        enablePresenceIndicator: true,
                        enableRoleDisplay: true,
                        enablePermissionCheck: true,
                        maxDisplayUsers: 10,
                        showUserCount: true,
                        enableDragAndDrop: false,
                        enableUserGrouping: false,
                        compactMode: false
                    };
                    
                    // 用户状态映射
                    this.userStatuses = {
                        'online': { color: '#28a745', icon: 'fa-circle', text: '在线' },
                        'away': { color: '#ffc107', icon: 'fa-circle', text: '离开' },
                        'busy': { color: '#dc3545', icon: 'fa-circle', text: '忙碌' },
                        'offline': { color: '#6c757d', icon: 'fa-circle', text: '离线' }
                    };
                    
                    // 用户角色映射
                    this.userRoles = {
                        'admin': { color: '#dc3545', icon: 'fa-crown', text: '管理员' },
                        'manager': { color: '#ffc107', icon: 'fa-star', text: '经理' },
                        'user': { color: '#17a2b8', icon: 'fa-user', text: '用户' },
                        'guest': { color: '#6c757d', icon: 'fa-eye', text: '访客' }
                    };
                    
                    // 初始化增强功能
                    this.initializeEnhancements();
                }
                
                // 初始化增强功能
                initializeEnhancements() {
                    // 设置用户搜索
                    if (this.enhancedConfig.enableUserSearch) {
                        this.setupUserSearch();
                    }
                    
                    // 设置快速操作
                    if (this.enhancedConfig.enableQuickActions) {
                        this.setupQuickActions();
                    }
                    
                    // 设置批量操作
                    if (this.enhancedConfig.enableBulkOperations) {
                        this.setupBulkOperations();
                    }
                    
                    // 设置拖拽功能
                    if (this.enhancedConfig.enableDragAndDrop) {
                        this.setupDragAndDrop();
                    }
                }
                
                // 增强的头像卡片显示
                displayAvatarCard(record) {
                    // 检查权限
                    if (this.enhancedConfig.enablePermissionCheck && !this.hasViewPermission(record)) {
                        return false;
                    }
                    
                    return this.relation === "res.users";
                }
                
                // 检查查看权限
                hasViewPermission(record) {
                    // 这里可以实现权限检查逻辑
                    return true;
                }
                
                // 增强的头像卡片属性
                getAvatarCardProps(record) {
                    const baseProps = {
                        id: record.resId,
                    };
                    
                    // 添加增强属性
                    if (this.enhancedConfig.enablePresenceIndicator) {
                        baseProps.showPresence = true;
                    }
                    
                    if (this.enhancedConfig.enableRoleDisplay) {
                        baseProps.showRole = true;
                    }
                    
                    if (this.enhancedConfig.enableQuickActions) {
                        baseProps.quickActions = this.getUserQuickActions(record);
                    }
                    
                    return baseProps;
                }
                
                // 获取用户快速操作
                getUserQuickActions(record) {
                    const actions = [];
                    
                    // 发送消息
                    actions.push({
                        id: 'message',
                        icon: 'fa-envelope',
                        text: '发送消息',
                        action: () => this.sendMessage(record)
                    });
                    
                    // 查看资料
                    actions.push({
                        id: 'profile',
                        icon: 'fa-user',
                        text: '查看资料',
                        action: () => this.viewProfile(record)
                    });
                    
                    // 分配任务
                    actions.push({
                        id: 'assign',
                        icon: 'fa-tasks',
                        text: '分配任务',
                        action: () => this.assignTask(record)
                    });
                    
                    return actions;
                }
                
                // 增强的标签属性
                getTagProps(record) {
                    const baseProps = super.getTagProps(...arguments);
                    
                    // 添加用户状态
                    if (this.enhancedConfig.enablePresenceIndicator) {
                        const userStatus = this.getUserStatus(record);
                        if (userStatus) {
                            baseProps.statusIndicator = {
                                color: userStatus.color,
                                icon: userStatus.icon,
                                text: userStatus.text
                            };
                        }
                    }
                    
                    // 添加用户角色
                    if (this.enhancedConfig.enableRoleDisplay) {
                        const userRole = this.getUserRole(record);
                        if (userRole) {
                            baseProps.roleIndicator = {
                                color: userRole.color,
                                icon: userRole.icon,
                                text: userRole.text
                            };
                        }
                    }
                    
                    // 增强点击处理
                    const originalOnImageClicked = baseProps.onImageClicked;
                    baseProps.onImageClicked = (ev) => {
                        // 执行原有逻辑
                        if (originalOnImageClicked) {
                            originalOnImageClicked(ev);
                        }
                        
                        // 记录点击事件
                        this.recordUserInteraction(record, 'avatar_click');
                    };
                    
                    // 添加右键菜单
                    baseProps.onContextMenu = (ev) => {
                        ev.preventDefault();
                        this.showUserContextMenu(ev, record);
                    };
                    
                    // 添加双击处理
                    baseProps.onDoubleClick = (ev) => {
                        this.handleUserDoubleClick(record);
                    };
                    
                    return baseProps;
                }
                
                // 获取用户状态
                getUserStatus(record) {
                    // 这里可以从用户数据中获取状态
                    // 简化处理，返回在线状态
                    return this.userStatuses['online'];
                }
                
                // 获取用户角色
                getUserRole(record) {
                    // 这里可以从用户数据中获取角色
                    // 简化处理，返回用户角色
                    return this.userRoles['user'];
                }
                
                // 记录用户交互
                recordUserInteraction(record, action) {
                    console.log('用户交互:', record.resId, action);
                }
                
                // 显示用户上下文菜单
                showUserContextMenu(event, record) {
                    const menu = this.createUserContextMenu(record);
                    this.showContextMenu(event, menu);
                }
                
                // 创建用户上下文菜单
                createUserContextMenu(record) {
                    const menuItems = [];
                    
                    // 查看资料
                    menuItems.push({
                        id: 'profile',
                        text: '查看资料',
                        icon: 'fa-user',
                        action: () => this.viewProfile(record)
                    });
                    
                    // 发送消息
                    menuItems.push({
                        id: 'message',
                        text: '发送消息',
                        icon: 'fa-envelope',
                        action: () => this.sendMessage(record)
                    });
                    
                    // 分配任务
                    menuItems.push({
                        id: 'assign',
                        text: '分配任务',
                        icon: 'fa-tasks',
                        action: () => this.assignTask(record)
                    });
                    
                    // 移除用户
                    if (!this.props.readonly) {
                        menuItems.push({
                            id: 'remove',
                            text: '移除用户',
                            icon: 'fa-times',
                            action: () => this.removeUser(record),
                            danger: true
                        });
                    }
                    
                    return menuItems;
                }
                
                // 处理用户双击
                handleUserDoubleClick(record) {
                    // 双击默认查看用户资料
                    this.viewProfile(record);
                }
                
                // 查看用户资料
                viewProfile(record) {
                    console.log('查看用户资料:', record);
                    // 实现查看用户资料的逻辑
                }
                
                // 发送消息
                sendMessage(record) {
                    console.log('发送消息给用户:', record);
                    // 实现发送消息的逻辑
                }
                
                // 分配任务
                assignTask(record) {
                    console.log('分配任务给用户:', record);
                    // 实现分配任务的逻辑
                }
                
                // 移除用户
                removeUser(record) {
                    console.log('移除用户:', record);
                    // 实现移除用户的逻辑
                }
                
                // 设置用户搜索
                setupUserSearch() {
                    // 实现用户搜索功能
                    console.log('设置用户搜索');
                }
                
                // 设置快速操作
                setupQuickActions() {
                    // 实现快速操作功能
                    console.log('设置快速操作');
                }
                
                // 设置批量操作
                setupBulkOperations() {
                    // 实现批量操作功能
                    console.log('设置批量操作');
                }
                
                // 设置拖拽功能
                setupDragAndDrop() {
                    // 实现拖拽功能
                    console.log('设置拖拽功能');
                }
                
                // 显示上下文菜单
                showContextMenu(event, menuItems) {
                    // 实现上下文菜单显示
                    console.log('显示上下文菜单:', menuItems);
                }
                
                // 获取用户列表
                getUserList() {
                    try {
                        const records = this.props.record.data[this.props.name].records;
                        return records.map(record => ({
                            id: record.resId,
                            name: record.display_name,
                            status: this.getUserStatus(record),
                            role: this.getUserRole(record),
                            record: record
                        }));
                    } catch (error) {
                        console.error('获取用户列表失败:', error);
                        return [];
                    }
                }
                
                // 获取用户数量
                getUserCount() {
                    try {
                        return this.props.record.data[this.props.name].records.length;
                    } catch (error) {
                        console.error('获取用户数量失败:', error);
                        return 0;
                    }
                }
                
                // 检查是否超过显示限制
                isOverDisplayLimit() {
                    return this.getUserCount() > this.enhancedConfig.maxDisplayUsers;
                }
                
                // 获取显示的用户
                getDisplayUsers() {
                    const users = this.getUserList();
                    if (this.isOverDisplayLimit()) {
                        return users.slice(0, this.enhancedConfig.maxDisplayUsers);
                    }
                    return users;
                }
                
                // 获取隐藏的用户数量
                getHiddenUserCount() {
                    const totalCount = this.getUserCount();
                    const displayCount = this.enhancedConfig.maxDisplayUsers;
                    return Math.max(0, totalCount - displayCount);
                }
            });
        
        // 增强的多对多用户头像标签列表
        class EnhancedMany2ManyAvatarUserTagsList extends Many2ManyAvatarUserTagsList {
            static template = "mail.EnhancedMany2ManyAvatarUserTagsList";
            
            setup() {
                super.setup();
                
                // 增强配置
                this.enhancedConfig = {
                    enableAnimation: true,
                    enableSorting: true,
                    enableGrouping: false,
                    enableSearch: true,
                    enableFiltering: true,
                    animationDuration: 300
                };
            }
            
            // 获取排序后的标签
            getSortedTags() {
                if (!this.enhancedConfig.enableSorting) {
                    return this.props.tags;
                }
                
                // 按名称排序
                return [...this.props.tags].sort((a, b) => {
                    return a.display_name.localeCompare(b.display_name);
                });
            }
            
            // 获取分组的标签
            getGroupedTags() {
                if (!this.enhancedConfig.enableGrouping) {
                    return { '默认': this.getSortedTags() };
                }
                
                const groups = {};
                this.getSortedTags().forEach(tag => {
                    const role = this.getUserRole(tag) || { text: '默认' };
                    if (!groups[role.text]) {
                        groups[role.text] = [];
                    }
                    groups[role.text].push(tag);
                });
                
                return groups;
            }
        }
        
        // 增强的字段组件
        class EnhancedMany2ManyTagsAvatarUserField extends EnhancedWithUserChatter(Many2ManyTagsAvatarField) {
            static components = {
                ...Many2ManyTagsAvatarField.components,
                TagsList: EnhancedMany2ManyAvatarUserTagsList,
            };
        }
        
        // 增强的字段注册
        const enhancedMany2ManyTagsAvatarUserField = {
            ...many2ManyTagsAvatarUserField,
            component: EnhancedMany2ManyTagsAvatarUserField,
            additionalClasses: [...(many2ManyTagsAvatarUserField.additionalClasses || []), "o_field_many2many_avatar_user_enhanced"],
        };
        
        // 替换原始字段注册
        registry.category("fields").add("many2many_avatar_user_enhanced", enhancedMany2ManyTagsAvatarUserField);
    }
};

// 应用多对多用户头像字段增强
Many2ManyAvatarUserFieldEnhancer.enhanceMany2ManyAvatarUserField();
```

## 技术特点

### 1. 混入模式
- 使用WithUserChatter混入增强功能
- 可应用于不同的基础字段类型
- 保持代码的可复用性

### 2. 多视图适配
- 针对不同视图类型提供专门的实现
- 优化的显示逻辑和交互体验
- 统一的API接口

### 3. 弹出框集成
- 集成头像卡片弹出框
- 智能的弹出框状态管理
- 流畅的用户交互体验

### 4. 命令系统集成
- 集成用户分配命令功能
- 支持快捷键操作
- 提高用户操作效率

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- WithUserChatter作为功能混入
- 为不同字段类型添加用户功能

### 2. 装饰器模式 (Decorator Pattern)
- 为基础字段添加用户特定功能
- 不改变原有结构

### 3. 工厂模式 (Factory Pattern)
- 根据视图类型创建不同的字段实现
- 统一的创建接口

## 注意事项

1. **性能考虑**: 避免大量用户时的性能问题
2. **权限检查**: 确保用户有查看其他用户信息的权限
3. **状态同步**: 保持弹出框状态的正确同步
4. **响应式设计**: 适配不同屏幕尺寸的显示

## 扩展建议

1. **用户搜索**: 添加用户搜索和过滤功能
2. **批量操作**: 支持批量用户操作
3. **状态指示**: 显示用户的在线状态
4. **角色显示**: 显示用户的角色信息
5. **拖拽排序**: 支持用户的拖拽排序

该多对多用户头像字段为邮件系统提供了重要的多用户管理功能，是用户协作和团队管理的核心组件。

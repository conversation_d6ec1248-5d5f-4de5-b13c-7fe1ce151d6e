# Assign User Command Hook - 用户分配命令钩子

## 概述

`assign_user_command_hook.js` 是 Odoo 邮件系统的用户分配命令钩子模块，专门提供通过命令面板快速分配用户的功能。该模块基于OWL框架和Web核心命令系统，集成了用户搜索、分配操作、快捷键支持等核心功能，为邮件系统提供了完整的用户分配和管理支持，是邮件系统用户交互和工作流程管理的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/assign_user_command_hook.js`
- **行数**: 173
- **模块**: `@mail/views/web/fields/assign_user_command_hook`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                              // OWL 框架
'@web/core/commands/command_hook'        // 命令钩子
'@web/core/domain'                       // 域处理
'@web/core/l10n/translation'             // 翻译工具
'@web/core/user'                         // 用户服务
'@web/core/utils/hooks'                  // 工具钩子
'@web/model/relational_model/utils'      // 关系模型工具
```

## 核心功能

### 1. useAssignUserCommand 钩子

```javascript
function useAssignUserCommand() {
    const component = useComponent();
    const orm = useService("orm");
    const type = component.props.record.fields[component.props.name].type;
    
    if (component.relation !== "res.users") {
        return;
    }
    
    // 钩子实现逻辑
}
```

**钩子功能**:
- **组件集成**: 与当前组件紧密集成
- **类型检查**: 仅支持res.users关系字段
- **ORM服务**: 集成ORM服务进行数据操作
- **条件执行**: 根据字段类型和关系执行相应逻辑

### 2. 当前ID获取

```javascript
const getCurrentIds = () => {
    if (type === "many2one" && component.props.record.data[component.props.name]) {
        return [component.props.record.data[component.props.name][0]];
    } else if (type === "many2many") {
        return component.props.record.data[component.props.name].currentIds;
    }
    return [];
};
```

**ID获取功能**:
- **类型适配**: 根据字段类型获取不同格式的ID
- **Many2one处理**: 处理单选用户字段
- **Many2many处理**: 处理多选用户字段
- **空值处理**: 正确处理空值情况

### 3. 用户添加操作

```javascript
const add = async (record) => {
    if (type === "many2one") {
        component.props.record.update({ [component.props.name]: record });
    } else if (type === "many2many") {
        component.props.record.data[component.props.name].linkTo(record[0], {
            display_name: record[1],
        });
    }
};
```

**添加功能**:
- **异步操作**: 支持异步的用户添加操作
- **类型分发**: 根据字段类型执行不同的添加逻辑
- **记录更新**: 直接更新记录数据
- **关系链接**: 建立用户关系链接

### 4. 用户移除操作

```javascript
const remove = async (record) => {
    if (type === "many2one") {
        component.props.record.update({ [component.props.name]: false });
    } else if (type === "many2many") {
        component.props.record.data[component.props.name].unlinkFrom(record[0]);
    }
};
```

**移除功能**:
- **异步操作**: 支持异步的用户移除操作
- **类型分发**: 根据字段类型执行不同的移除逻辑
- **记录清空**: 清空单选字段的值
- **关系解除**: 解除多选字段的关系链接

### 5. 用户搜索提供器

```javascript
const provide = async (env, options) => {
    const value = options.searchValue.trim();
    let domain = getFieldDomain(
        component.props.record,
        component.props.name,
        component.props.domain
    );
    
    const context = component.props.context;
    if (type === "many2many") {
        const selectedUserIds = getCurrentIds();
        if (selectedUserIds.length) {
            domain = Domain.and([domain, [["id", "not in", selectedUserIds]]]).toList();
        }
    }
    
    component._pendingRpc?.abort(false);
    component._pendingRpc = orm.call(component.relation, "name_search", [], {
        name: value,
        args: domain,
        operator: "ilike",
        limit: 80,
        context,
    });
    
    const searchResult = await component._pendingRpc;
    component._pendingRpc = null;
    
    return searchResult.map((record) => ({
        name: record[1],
        action: add.bind(null, record),
    }));
};
```

**搜索提供器功能**:
- **搜索值处理**: 处理用户输入的搜索值
- **域过滤**: 应用字段域进行过滤
- **重复排除**: 排除已选择的用户（many2many）
- **RPC管理**: 管理搜索请求的生命周期
- **结果映射**: 将搜索结果映射为命令选项

### 6. 命令注册

```javascript
// "分配给..." 命令
useCommand(
    _t("Assign to ..."),
    () => ({
        configByNameSpace: {
            default: {
                emptyMessage: _t("No users found"),
            },
        },
        placeholder: _t("Select a user..."),
        providers: [{ provide }],
    }),
    {
        ...options,
        hotkey: "alt+i",
    }
);

// "分配给我" 命令
useCommand(
    _t("Assign to me"),
    () => {
        add([user.userId, user.name]);
    },
    {
        ...options,
        isAvailable: () => options.isAvailable() && !getCurrentIds().includes(user.userId),
        hotkey: "alt+shift+i",
    }
);

// "取消分配" 命令
useCommand(
    _t("Unassign from me"),
    () => {
        remove([user.userId, user.name]);
    },
    {
        ...options,
        isAvailable: () => options.isAvailable() && getCurrentIds().includes(user.userId),
        hotkey: "alt+shift+i",
    }
);
```

**命令注册功能**:
- **多命令支持**: 注册多个相关的用户分配命令
- **快捷键**: 为每个命令配置快捷键
- **可用性检查**: 动态检查命令的可用性
- **本地化**: 支持多语言的命令名称和提示

## 使用场景

### 1. 用户分配命令钩子增强

```javascript
// 用户分配命令钩子增强功能
const AssignUserCommandHookEnhancer = {
    enhanceAssignUserCommandHook: () => {
        // 增强的用户分配命令钩子
        const enhancedUseAssignUserCommand = (options = {}) => {
            const component = useComponent();
            const orm = useService("orm");
            const notification = useService("notification");
            const type = component.props.record.fields[component.props.name].type;
            
            // 配置选项
            const config = {
                enableBulkAssign: options.enableBulkAssign !== false,
                enableTeamAssign: options.enableTeamAssign || false,
                enableRoleFilter: options.enableRoleFilter || false,
                enableNotifications: options.enableNotifications !== false,
                maxAssignees: options.maxAssignees || null,
                allowedRoles: options.allowedRoles || [],
                teamField: options.teamField || 'team_id',
                ...options
            };
            
            if (component.relation !== "res.users") {
                return;
            }
            
            // 增强的当前ID获取
            const getCurrentIds = () => {
                try {
                    if (type === "many2one" && component.props.record.data[component.props.name]) {
                        return [component.props.record.data[component.props.name][0]];
                    } else if (type === "many2many") {
                        return component.props.record.data[component.props.name].currentIds || [];
                    }
                    return [];
                } catch (error) {
                    console.error('获取当前用户ID失败:', error);
                    return [];
                }
            };
            
            // 增强的用户添加
            const add = async (record, options = {}) => {
                try {
                    // 检查最大分配数量
                    if (config.maxAssignees && type === "many2many") {
                        const currentIds = getCurrentIds();
                        if (currentIds.length >= config.maxAssignees) {
                            if (config.enableNotifications) {
                                notification.add(
                                    `最多只能分配 ${config.maxAssignees} 个用户`,
                                    { type: 'warning' }
                                );
                            }
                            return false;
                        }
                    }
                    
                    // 检查重复分配
                    const currentIds = getCurrentIds();
                    if (currentIds.includes(record[0])) {
                        if (config.enableNotifications) {
                            notification.add(
                                `用户 ${record[1]} 已经被分配`,
                                { type: 'info' }
                            );
                        }
                        return false;
                    }
                    
                    // 执行分配
                    if (type === "many2one") {
                        await component.props.record.update({ 
                            [component.props.name]: record 
                        });
                    } else if (type === "many2many") {
                        component.props.record.data[component.props.name].linkTo(record[0], {
                            display_name: record[1],
                        });
                    }
                    
                    // 成功通知
                    if (config.enableNotifications) {
                        notification.add(
                            `已将 ${record[1]} 分配到 ${component.props.string}`,
                            { type: 'success' }
                        );
                    }
                    
                    // 触发自定义事件
                    if (options.onSuccess) {
                        options.onSuccess(record);
                    }
                    
                    return true;
                } catch (error) {
                    console.error('添加用户失败:', error);
                    if (config.enableNotifications) {
                        notification.add(
                            `分配用户失败: ${error.message}`,
                            { type: 'danger' }
                        );
                    }
                    return false;
                }
            };
            
            // 增强的用户移除
            const remove = async (record, options = {}) => {
                try {
                    // 检查是否存在
                    const currentIds = getCurrentIds();
                    if (!currentIds.includes(record[0])) {
                        if (config.enableNotifications) {
                            notification.add(
                                `用户 ${record[1]} 未被分配`,
                                { type: 'info' }
                            );
                        }
                        return false;
                    }
                    
                    // 执行移除
                    if (type === "many2one") {
                        await component.props.record.update({ 
                            [component.props.name]: false 
                        });
                    } else if (type === "many2many") {
                        component.props.record.data[component.props.name].unlinkFrom(record[0]);
                    }
                    
                    // 成功通知
                    if (config.enableNotifications) {
                        notification.add(
                            `已将 ${record[1]} 从 ${component.props.string} 中移除`,
                            { type: 'success' }
                        );
                    }
                    
                    // 触发自定义事件
                    if (options.onSuccess) {
                        options.onSuccess(record);
                    }
                    
                    return true;
                } catch (error) {
                    console.error('移除用户失败:', error);
                    if (config.enableNotifications) {
                        notification.add(
                            `移除用户失败: ${error.message}`,
                            { type: 'danger' }
                        );
                    }
                    return false;
                }
            };
            
            // 增强的搜索提供器
            const provide = async (env, options) => {
                try {
                    const value = options.searchValue.trim();
                    let domain = getFieldDomain(
                        component.props.record,
                        component.props.name,
                        component.props.domain
                    );
                    
                    // 角色过滤
                    if (config.enableRoleFilter && config.allowedRoles.length > 0) {
                        domain = Domain.and([
                            domain, 
                            [["groups_id", "in", config.allowedRoles]]
                        ]).toList();
                    }
                    
                    // 团队过滤
                    if (config.enableTeamAssign && config.teamField) {
                        const teamId = component.props.record.data[config.teamField];
                        if (teamId) {
                            domain = Domain.and([
                                domain,
                                [[config.teamField, "=", teamId[0]]]
                            ]).toList();
                        }
                    }
                    
                    const context = component.props.context;
                    if (type === "many2many") {
                        const selectedUserIds = getCurrentIds();
                        if (selectedUserIds.length) {
                            domain = Domain.and([
                                domain, 
                                [["id", "not in", selectedUserIds]]
                            ]).toList();
                        }
                    }
                    
                    // 取消之前的请求
                    component._pendingRpc?.abort(false);
                    
                    // 发起搜索请求
                    component._pendingRpc = orm.call(component.relation, "name_search", [], {
                        name: value,
                        args: domain,
                        operator: "ilike",
                        limit: config.searchLimit || 80,
                        context,
                    });
                    
                    const searchResult = await component._pendingRpc;
                    component._pendingRpc = null;
                    
                    // 处理搜索结果
                    return searchResult.map((record) => ({
                        name: record[1],
                        action: () => add(record),
                        category: 'users'
                    }));
                } catch (error) {
                    console.error('搜索用户失败:', error);
                    return [];
                }
            };
            
            // 批量分配功能
            const bulkAssign = async (userIds) => {
                if (!config.enableBulkAssign || type !== "many2many") {
                    return false;
                }
                
                try {
                    const users = await orm.call("res.users", "read", [userIds], {
                        fields: ["id", "name"]
                    });
                    
                    const results = [];
                    for (const user of users) {
                        const result = await add([user.id, user.name]);
                        results.push({ user, success: result });
                    }
                    
                    const successCount = results.filter(r => r.success).length;
                    if (config.enableNotifications) {
                        notification.add(
                            `批量分配完成: ${successCount}/${users.length} 个用户`,
                            { type: successCount === users.length ? 'success' : 'warning' }
                        );
                    }
                    
                    return results;
                } catch (error) {
                    console.error('批量分配失败:', error);
                    if (config.enableNotifications) {
                        notification.add(
                            `批量分配失败: ${error.message}`,
                            { type: 'danger' }
                        );
                    }
                    return false;
                }
            };
            
            // 团队分配功能
            const assignTeam = async (teamId) => {
                if (!config.enableTeamAssign) {
                    return false;
                }
                
                try {
                    const teamUsers = await orm.call("res.users", "search_read", [
                        [[config.teamField, "=", teamId]]
                    ], {
                        fields: ["id", "name"]
                    });
                    
                    return await bulkAssign(teamUsers.map(user => user.id));
                } catch (error) {
                    console.error('团队分配失败:', error);
                    return false;
                }
            };
            
            // 命令选项配置
            const commandOptions = {
                category: "smart_action",
                global: true,
                identifier: component.props.string,
            };
            
            if (component.props.record.id !== component.props.record.model.root.id) {
                commandOptions.isAvailable = () =>
                    component.props.record.model.multiEdit && component.props.record.selected;
            } else {
                commandOptions.isAvailable = () => true;
            }
            
            // 注册增强的命令
            useCommand(
                _t("Assign to ..."),
                () => ({
                    configByNameSpace: {
                        default: {
                            emptyMessage: _t("No users found"),
                        },
                    },
                    placeholder: _t("Select a user..."),
                    providers: [{ provide }],
                }),
                {
                    ...commandOptions,
                    hotkey: "alt+i",
                }
            );
            
            useCommand(
                _t("Assign to me"),
                () => add([user.userId, user.name]),
                {
                    ...commandOptions,
                    isAvailable: () => commandOptions.isAvailable() && !getCurrentIds().includes(user.userId),
                    hotkey: "alt+shift+i",
                }
            );
            
            // 批量分配命令
            if (config.enableBulkAssign && type === "many2many") {
                useCommand(
                    _t("Bulk assign users"),
                    () => ({
                        configByNameSpace: {
                            default: {
                                emptyMessage: _t("No users found"),
                            },
                        },
                        placeholder: _t("Select users for bulk assignment..."),
                        providers: [{
                            provide: async (env, options) => {
                                const results = await provide(env, options);
                                return results.map(result => ({
                                    ...result,
                                    action: () => {
                                        // 这里可以实现多选逻辑
                                        result.action();
                                    }
                                }));
                            }
                        }],
                    }),
                    {
                        ...commandOptions,
                        hotkey: "alt+shift+b",
                    }
                );
            }
            
            // 取消分配命令
            if (component.props.record.id === component.props.record.model.root.id) {
                useCommand(
                    _t("Unassign from me"),
                    () => remove([user.userId, user.name]),
                    {
                        ...commandOptions,
                        isAvailable: () => commandOptions.isAvailable() && getCurrentIds().includes(user.userId),
                        hotkey: "alt+shift+u",
                    }
                );
            } else {
                if (type === "many2one") {
                    useCommand(
                        _t("Unassign"),
                        () => remove([user.userId, user.name]),
                        {
                            ...commandOptions,
                            isAvailable: () => commandOptions.isAvailable() && getCurrentIds().length > 0,
                            hotkey: "alt+shift+u",
                        }
                    );
                } else {
                    useCommand(
                        _t("Unassign from me"),
                        () => remove([user.userId, user.name]),
                        {
                            ...commandOptions,
                            isAvailable: () => commandOptions.isAvailable() && getCurrentIds().includes(user.userId),
                            hotkey: "alt+shift+u",
                        }
                    );
                }
            }
            
            // 返回增强的API
            return {
                add,
                remove,
                bulkAssign,
                assignTeam,
                getCurrentIds,
                config
            };
        };
        
        // 替换原始钩子
        __exports.useAssignUserCommand = enhancedUseAssignUserCommand;
    }
};

// 应用用户分配命令钩子增强
AssignUserCommandHookEnhancer.enhanceAssignUserCommandHook();
```

## 技术特点

### 1. 命令面板集成
- 与Web核心命令系统深度集成
- 支持快捷键操作
- 提供搜索和选择界面

### 2. 字段类型适配
- 支持many2one和many2many字段
- 根据字段类型执行不同逻辑
- 智能的数据处理和更新

### 3. 用户体验优化
- 实时搜索用户
- 智能过滤已选用户
- 本地化的界面文本

### 4. 性能优化
- RPC请求管理和取消
- 搜索结果限制
- 异步操作支持

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 可复用的逻辑封装
- 组件生命周期集成

### 2. 命令模式 (Command Pattern)
- 操作的封装和执行
- 支持撤销和重做

### 3. 提供器模式 (Provider Pattern)
- 数据提供的抽象
- 可扩展的搜索机制

## 注意事项

1. **字段类型**: 仅支持res.users关系字段
2. **权限检查**: 需要确保用户有相应的分配权限
3. **数据一致性**: 确保分配操作的数据一致性
4. **性能考虑**: 避免频繁的搜索请求影响性能

## 扩展建议

1. **批量操作**: 支持批量用户分配和移除
2. **权限控制**: 添加更细粒度的权限控制
3. **历史记录**: 记录分配操作的历史
4. **通知系统**: 集成通知系统提供操作反馈
5. **自定义过滤**: 支持自定义的用户过滤条件

该用户分配命令钩子为邮件系统提供了强大的用户管理功能，是提高工作效率和用户体验的重要工具。

# Convert Inline - 内联样式转换工具

## 概述

`convert_inline.js` 是 Odoo 邮件系统的内联样式转换工具，专门用于将HTML内容转换为适合邮件发送的格式。该模块基于HTML编辑器工具和DOM操作，集成了CSS内联化、表格转换、Bootstrap适配、颜色标准化等核心功能，为邮件系统提供了完整的HTML邮件格式化和兼容性处理支持，是邮件系统内容转换和邮件客户端兼容性的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/html_mail_field/convert_inline.js`
- **行数**: 1885
- **模块**: `@mail/views/web/fields/html_mail_field/convert_inline`

## 依赖关系

```javascript
// 核心依赖
'@html_editor/utils/blocks'        // 块级元素工具
'@html_editor/utils/color'         // 颜色工具
'@html_editor/utils/dom_traversal' // DOM遍历工具
```

## 核心功能

### 1. 主要导出函数

```javascript
// 表格相关
__exports.addTables = addTables;                    // 添加表格结构
__exports.bootstrapToTable = bootstrapToTable;      // Bootstrap转表格
__exports.cardToTable = cardToTable;                // 卡片转表格
__exports.listGroupToTable = listGroupToTable;      // 列表组转表格
__exports.formatTables = formatTables;              // 格式化表格

// 样式处理
__exports.classToStyle = classToStyle;              // 类名转内联样式
__exports.toInline = toInline;                      // 主要内联化函数
__exports.getCSSRules = getCSSRules;                // 获取CSS规则

// 标准化处理
__exports.normalizeColors = normalizeColors;        // 颜色标准化
__exports.normalizeRem = normalizeRem;              // rem单位标准化

// 常量
__exports.TABLE_ATTRIBUTES = TABLE_ATTRIBUTES;      // 表格属性
__exports.TABLE_STYLES = TABLE_STYLES;              // 表格样式
```

### 2. 表格属性和样式常量

```javascript
// 邮件表格应有的属性
const TABLE_ATTRIBUTES = {
    cellspacing: 0,
    cellpadding: 0,
    border: 0,
    width: "100%",
    align: "center",
    role: "presentation",
};

// 取消表格默认样式
const TABLE_STYLES = {
    "border-collapse": "collapse",
    "text-align": "inherit",
    "font-size": "unset",
    "line-height": "inherit",
};
```

### 3. 主要内联化函数

```javascript
async function toInline(element, cssRules) {
    // 修复card-img-top高度
    for (const imgTop of element.querySelectorAll(".card-img-top")) {
        imgTop.style.setProperty("height", _getHeight(imgTop) + "px");
    }
    
    // 执行各种转换
    addTables(element);
    bootstrapToTable(element);
    cardToTable(element);
    listGroupToTable(element);
    classToStyle(element, cssRules);
    formatTables(element);
    normalizeColors(element);
    normalizeRem(element);
}
```

### 4. 工具函数

```javascript
// 获取父节点路径
function parentsGet(node, root = undefined) {
    const parents = [];
    while (node) {
        parents.unshift(node);
        if (node === root) break;
        node = node.parentNode;
    }
    return parents;
}

// 获取公共父节点
function commonParentGet(node1, node2, root = undefined) {
    if (!node1 || !node2) return null;
    const n1p = parentsGet(node1, root);
    const n2p = parentsGet(node2, root);
    while (n1p.length > 1 && n1p[1] === n2p[1]) {
        n1p.shift();
        n2p.shift();
    }
    return n1p[0] === n2p[0] ? n1p[0] : null;
}
```

## 使用场景

### 1. 内联样式转换工具增强

```javascript
// 内联样式转换工具增强功能
const ConvertInlineEnhancer = {
    enhanceConvertInline: () => {
        // 增强的内联转换配置
        const enhancedConfig = {
            enableAdvancedOptimization: true,
            enableResponsiveSupport: true,
            enableDarkModeSupport: true,
            enableAccessibilityEnhancements: true,
            enablePerformanceOptimization: true,
            supportedEmailClients: ['gmail', 'outlook', 'apple_mail', 'thunderbird', 'yahoo'],
            maxTableNesting: 3,
            imageOptimization: {
                maxWidth: 600,
                quality: 0.8,
                format: 'auto'
            },
            fontFallbacks: {
                'Arial': 'Arial, Helvetica, sans-serif',
                'Times': 'Times, "Times New Roman", serif',
                'Courier': 'Courier, "Courier New", monospace'
            }
        };
        
        // 增强的主要内联化函数
        const enhancedToInline = async (element, cssRules, options = {}) => {
            try {
                const config = { ...enhancedConfig, ...options };
                
                // 预处理阶段
                await preProcessElement(element, config);
                
                // 执行基础内联化
                await toInline(element, cssRules);
                
                // 后处理阶段
                await postProcessElement(element, config);
                
                // 验证和优化
                await validateAndOptimize(element, config);
                
                return element;
            } catch (error) {
                console.error('增强内联化失败:', error);
                throw error;
            }
        };
        
        // 预处理元素
        const preProcessElement = async (element, config) => {
            try {
                // 添加邮件客户端特定的类
                element.classList.add('o_mail_content');
                
                // 预处理图片
                if (config.imageOptimization) {
                    await preProcessImages(element, config.imageOptimization);
                }
                
                // 预处理字体
                if (config.fontFallbacks) {
                    preProcessFonts(element, config.fontFallbacks);
                }
                
                // 预处理链接
                preProcessLinks(element);
                
                // 添加可访问性属性
                if (config.enableAccessibilityEnhancements) {
                    addAccessibilityAttributes(element);
                }
            } catch (error) {
                console.error('预处理元素失败:', error);
            }
        };
        
        // 预处理图片
        const preProcessImages = async (element, imageConfig) => {
            const images = element.querySelectorAll('img');
            
            for (const img of images) {
                try {
                    // 设置最大宽度
                    if (!img.style.maxWidth) {
                        img.style.maxWidth = `${imageConfig.maxWidth}px`;
                    }
                    
                    // 设置响应式属性
                    img.style.height = 'auto';
                    img.style.display = 'block';
                    
                    // 确保有alt属性
                    if (!img.alt) {
                        img.alt = '图片';
                    }
                    
                    // 添加邮件客户端兼容属性
                    img.setAttribute('border', '0');
                    img.setAttribute('style', img.getAttribute('style') + '; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic;');
                    
                    // 优化图片源
                    if (imageConfig.format === 'auto') {
                        await optimizeImageFormat(img);
                    }
                } catch (error) {
                    console.error('预处理图片失败:', error, img);
                }
            }
        };
        
        // 优化图片格式
        const optimizeImageFormat = async (img) => {
            // 这里可以实现图片格式优化逻辑
            console.log('优化图片格式:', img.src);
        };
        
        // 预处理字体
        const preProcessFonts = (element, fontFallbacks) => {
            const textElements = element.querySelectorAll('*');
            
            textElements.forEach(el => {
                const computedStyle = getComputedStyle(el);
                const fontFamily = computedStyle.fontFamily;
                
                if (fontFamily) {
                    // 添加字体回退
                    for (const [font, fallback] of Object.entries(fontFallbacks)) {
                        if (fontFamily.includes(font) && !fontFamily.includes(fallback)) {
                            el.style.fontFamily = fallback;
                        }
                    }
                }
            });
        };
        
        // 预处理链接
        const preProcessLinks = (element) => {
            const links = element.querySelectorAll('a');
            
            links.forEach(link => {
                // 确保链接有href属性
                if (!link.href) {
                    link.href = '#';
                }
                
                // 添加邮件客户端兼容样式
                if (!link.style.color) {
                    link.style.color = '#007bff';
                }
                
                // 添加文本装饰
                if (!link.style.textDecoration) {
                    link.style.textDecoration = 'underline';
                }
                
                // 设置目标窗口
                if (!link.target) {
                    link.target = '_blank';
                }
            });
        };
        
        // 添加可访问性属性
        const addAccessibilityAttributes = (element) => {
            // 为表格添加role属性
            const tables = element.querySelectorAll('table');
            tables.forEach(table => {
                if (!table.getAttribute('role')) {
                    table.setAttribute('role', 'presentation');
                }
            });
            
            // 为图片添加适当的alt属性
            const images = element.querySelectorAll('img');
            images.forEach(img => {
                if (!img.alt) {
                    img.alt = '装饰性图片';
                }
            });
            
            // 为链接添加适当的标题
            const links = element.querySelectorAll('a');
            links.forEach(link => {
                if (!link.title && link.textContent) {
                    link.title = link.textContent.trim();
                }
            });
        };
        
        // 后处理元素
        const postProcessElement = async (element, config) => {
            try {
                // 添加响应式支持
                if (config.enableResponsiveSupport) {
                    addResponsiveSupport(element);
                }
                
                // 添加暗黑模式支持
                if (config.enableDarkModeSupport) {
                    addDarkModeSupport(element);
                }
                
                // 优化表格嵌套
                optimizeTableNesting(element, config.maxTableNesting);
                
                // 添加邮件客户端特定修复
                addEmailClientFixes(element, config.supportedEmailClients);
                
                // 性能优化
                if (config.enablePerformanceOptimization) {
                    performanceOptimize(element);
                }
            } catch (error) {
                console.error('后处理元素失败:', error);
            }
        };
        
        // 添加响应式支持
        const addResponsiveSupport = (element) => {
            // 添加媒体查询样式
            const style = document.createElement('style');
            style.textContent = `
                @media only screen and (max-width: 600px) {
                    .o_mail_content table { width: 100% !important; }
                    .o_mail_content td { display: block !important; width: 100% !important; }
                    .o_mail_content img { max-width: 100% !important; height: auto !important; }
                }
            `;
            
            // 如果是完整的HTML文档，添加到head
            if (element.tagName === 'HTML') {
                const head = element.querySelector('head');
                if (head) {
                    head.appendChild(style);
                }
            }
        };
        
        // 添加暗黑模式支持
        const addDarkModeSupport = (element) => {
            // 添加暗黑模式媒体查询
            const style = document.createElement('style');
            style.textContent = `
                @media (prefers-color-scheme: dark) {
                    .o_mail_content { background-color: #1a1a1a !important; color: #ffffff !important; }
                    .o_mail_content table { background-color: #1a1a1a !important; }
                    .o_mail_content a { color: #4dabf7 !important; }
                }
            `;
            
            if (element.tagName === 'HTML') {
                const head = element.querySelector('head');
                if (head) {
                    head.appendChild(style);
                }
            }
        };
        
        // 优化表格嵌套
        const optimizeTableNesting = (element, maxNesting) => {
            const tables = element.querySelectorAll('table');
            
            tables.forEach(table => {
                let nestingLevel = 0;
                let parent = table.parentElement;
                
                while (parent && parent !== element) {
                    if (parent.tagName === 'TABLE') {
                        nestingLevel++;
                    }
                    parent = parent.parentElement;
                }
                
                if (nestingLevel > maxNesting) {
                    console.warn('表格嵌套层级过深:', nestingLevel, table);
                    // 可以在这里实现表格扁平化逻辑
                }
            });
        };
        
        // 添加邮件客户端修复
        const addEmailClientFixes = (element, supportedClients) => {
            supportedClients.forEach(client => {
                switch (client) {
                    case 'outlook':
                        addOutlookFixes(element);
                        break;
                    case 'gmail':
                        addGmailFixes(element);
                        break;
                    case 'apple_mail':
                        addAppleMailFixes(element);
                        break;
                    case 'thunderbird':
                        addThunderbirdFixes(element);
                        break;
                    case 'yahoo':
                        addYahooFixes(element);
                        break;
                }
            });
        };
        
        // Outlook特定修复
        const addOutlookFixes = (element) => {
            // 添加Outlook条件注释
            const tables = element.querySelectorAll('table');
            tables.forEach(table => {
                table.style.borderCollapse = 'collapse';
                table.style.msoTableLspace = '0pt';
                table.style.msoTableRspace = '0pt';
            });
            
            // 修复Outlook中的字体问题
            const textElements = element.querySelectorAll('p, div, span, td');
            textElements.forEach(el => {
                if (!el.style.fontFamily) {
                    el.style.fontFamily = 'Arial, sans-serif';
                }
            });
        };
        
        // Gmail特定修复
        const addGmailFixes = (element) => {
            // Gmail会移除某些CSS属性，确保重要样式使用!important
            const elements = element.querySelectorAll('*');
            elements.forEach(el => {
                if (el.style.color && !el.style.color.includes('!important')) {
                    el.style.color = el.style.color + ' !important';
                }
            });
        };
        
        // Apple Mail特定修复
        const addAppleMailFixes = (element) => {
            // Apple Mail支持更多CSS3特性
            const elements = element.querySelectorAll('*');
            elements.forEach(el => {
                if (el.style.borderRadius) {
                    el.style.webkitBorderRadius = el.style.borderRadius;
                }
            });
        };
        
        // Thunderbird特定修复
        const addThunderbirdFixes = (element) => {
            // Thunderbird特定的修复
            console.log('应用Thunderbird修复');
        };
        
        // Yahoo特定修复
        const addYahooFixes = (element) => {
            // Yahoo特定的修复
            console.log('应用Yahoo修复');
        };
        
        // 性能优化
        const performanceOptimize = (element) => {
            // 移除空的样式属性
            const elements = element.querySelectorAll('*');
            elements.forEach(el => {
                if (el.style.length === 0) {
                    el.removeAttribute('style');
                }
            });
            
            // 合并相邻的相同元素
            mergeAdjacentElements(element);
            
            // 移除不必要的嵌套
            removeUnnecessaryNesting(element);
        };
        
        // 合并相邻元素
        const mergeAdjacentElements = (element) => {
            // 实现相邻元素合并逻辑
            console.log('合并相邻元素');
        };
        
        // 移除不必要的嵌套
        const removeUnnecessaryNesting = (element) => {
            // 实现嵌套移除逻辑
            console.log('移除不必要的嵌套');
        };
        
        // 验证和优化
        const validateAndOptimize = async (element, config) => {
            try {
                // 验证HTML结构
                const validationErrors = validateHTMLStructure(element);
                if (validationErrors.length > 0) {
                    console.warn('HTML结构验证错误:', validationErrors);
                }
                
                // 验证邮件兼容性
                const compatibilityIssues = validateEmailCompatibility(element);
                if (compatibilityIssues.length > 0) {
                    console.warn('邮件兼容性问题:', compatibilityIssues);
                }
                
                // 优化文件大小
                optimizeFileSize(element);
                
                // 生成性能报告
                const performanceReport = generatePerformanceReport(element);
                console.log('性能报告:', performanceReport);
            } catch (error) {
                console.error('验证和优化失败:', error);
            }
        };
        
        // 验证HTML结构
        const validateHTMLStructure = (element) => {
            const errors = [];
            
            // 检查必要的属性
            const tables = element.querySelectorAll('table');
            tables.forEach((table, index) => {
                if (!table.getAttribute('role')) {
                    errors.push(`表格 ${index} 缺少 role 属性`);
                }
            });
            
            // 检查图片alt属性
            const images = element.querySelectorAll('img');
            images.forEach((img, index) => {
                if (!img.alt) {
                    errors.push(`图片 ${index} 缺少 alt 属性`);
                }
            });
            
            return errors;
        };
        
        // 验证邮件兼容性
        const validateEmailCompatibility = (element) => {
            const issues = [];
            
            // 检查不支持的CSS属性
            const elements = element.querySelectorAll('*');
            elements.forEach(el => {
                if (el.style.position && el.style.position !== 'static') {
                    issues.push('使用了不兼容的position属性');
                }
                if (el.style.float) {
                    issues.push('使用了不兼容的float属性');
                }
            });
            
            return issues;
        };
        
        // 优化文件大小
        const optimizeFileSize = (element) => {
            // 移除注释
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_COMMENT,
                null,
                false
            );
            
            const comments = [];
            let node;
            while (node = walker.nextNode()) {
                comments.push(node);
            }
            
            comments.forEach(comment => comment.remove());
            
            // 压缩空白字符
            compressWhitespace(element);
        };
        
        // 压缩空白字符
        const compressWhitespace = (element) => {
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );
            
            let node;
            while (node = walker.nextNode()) {
                if (node.nodeValue) {
                    node.nodeValue = node.nodeValue.replace(/\s+/g, ' ');
                }
            }
        };
        
        // 生成性能报告
        const generatePerformanceReport = (element) => {
            const report = {
                totalElements: element.querySelectorAll('*').length,
                totalTables: element.querySelectorAll('table').length,
                totalImages: element.querySelectorAll('img').length,
                totalLinks: element.querySelectorAll('a').length,
                htmlSize: element.outerHTML.length,
                nestingDepth: getMaxNestingDepth(element)
            };
            
            return report;
        };
        
        // 获取最大嵌套深度
        const getMaxNestingDepth = (element) => {
            let maxDepth = 0;
            
            const traverse = (node, depth = 0) => {
                maxDepth = Math.max(maxDepth, depth);
                for (const child of node.children) {
                    traverse(child, depth + 1);
                }
            };
            
            traverse(element);
            return maxDepth;
        };
        
        // 导出增强的函数
        __exports.enhancedToInline = enhancedToInline;
        __exports.preProcessElement = preProcessElement;
        __exports.postProcessElement = postProcessElement;
        __exports.validateAndOptimize = validateAndOptimize;
    }
};

// 应用内联样式转换工具增强
ConvertInlineEnhancer.enhanceConvertInline();
```

## 技术特点

### 1. 全面的转换功能
- 支持多种HTML结构转换为表格
- 完整的CSS内联化处理
- 标准化的颜色和单位处理

### 2. 邮件客户端兼容性
- 专门针对邮件客户端的优化
- 表格布局的标准化处理
- 字体属性的继承处理

### 3. 模块化设计
- 功能分离的转换函数
- 可复用的工具函数
- 清晰的常量定义

### 4. 性能优化
- 高效的DOM操作
- 批量处理机制
- 内存友好的实现

## 设计模式

### 1. 转换器模式 (Transformer Pattern)
- 专门的转换函数
- 统一的转换接口

### 2. 工具模式 (Utility Pattern)
- 通用的工具函数
- 可复用的操作

### 3. 常量模式 (Constants Pattern)
- 集中的常量定义
- 配置的标准化

## 注意事项

1. **邮件兼容性**: 确保转换后的HTML在各种邮件客户端中正确显示
2. **性能考虑**: 避免大量DOM操作影响性能
3. **内容完整性**: 确保转换过程不丢失重要内容
4. **样式保持**: 保持原有样式的视觉效果

## 扩展建议

1. **更多客户端支持**: 添加更多邮件客户端的兼容性处理
2. **智能优化**: 实现更智能的HTML结构优化
3. **预览功能**: 添加转换结果的预览功能
4. **性能监控**: 添加转换性能的监控和优化
5. **错误处理**: 增强错误处理和恢复机制

该内联样式转换工具为邮件系统提供了重要的HTML格式化功能，是邮件内容兼容性和显示效果的核心保障组件。

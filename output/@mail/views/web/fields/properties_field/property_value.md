# Property Value - 属性值字段

## 概述

`property_value.js` 是 Odoo 邮件系统的属性值字段补丁，专门为属性字段添加聊天功能支持。该模块基于Web框架的属性值组件和邮件系统的聊天钩子，集成了头像点击、聊天打开、标签列表等核心功能，为邮件系统提供了完整的属性字段聊天集成支持，是邮件系统属性管理和用户交互的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/properties_field/property_value.js`
- **行数**: 59
- **模块**: `@mail/views/web/fields/properties_field/property_value`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/web/open_chat_hook'                    // 聊天打开钩子
'@web/core/tags_list/tags_list'                   // 标签列表
'@web/core/utils/patch'                           // 补丁工具
'@web/views/fields/properties/property_value'     // 属性值字段
```

## 核心功能

### 1. PropertyValue 补丁

```javascript
patch(PropertyValue.prototype, {
    setup() {
        super.setup();

        if (this.env.services["mail.store"]) {
            // work only for the res.users model
            this.openChat = useOpenChat("res.users");
        }
    },

    _onAvatarClicked() {
        if (this.openChat && this.showAvatar && this.props.comodel === "res.users") {
            this.openChat(this.props.value[0]);
        }
    },
});
```

**补丁功能**:
- **聊天集成**: 集成聊天打开功能
- **用户限制**: 仅对res.users模型生效
- **头像点击**: 处理头像点击事件
- **条件检查**: 检查聊天服务和显示条件

### 2. Many2manyPropertiesTagsList 组件

```javascript
const Many2manyPropertiesTagsList = class Many2manyPropertiesTagsList extends TagsList {
    static template = "mail.Many2manyPropertiesTagsList";

    setup() {
        super.setup();
        if (this.env.services["mail.store"]) {
            this.openChat = useOpenChat("res.users");
        }
    }

    _onAvatarClicked(tagIndex) {
        const tag = this.props.tags[tagIndex];
        if (this.openChat && tag.comodel === "res.users") {
            this.openChat(tag.id);
        }
    }
}
```

**组件功能**:
- **继承扩展**: 继承TagsList的所有功能
- **专用模板**: 使用专门的多对多属性标签列表模板
- **聊天集成**: 集成聊天打开功能
- **标签点击**: 处理标签头像点击事件

### 3. 组件替换

```javascript
PropertyValue.components = {
    ...PropertyValue.components,
    TagsList: Many2manyPropertiesTagsList,
};
```

**替换功能**:
- **组件覆盖**: 替换PropertyValue的TagsList组件
- **功能增强**: 为标签列表添加聊天功能
- **向后兼容**: 保持原有组件的完整性

## 使用场景

### 1. 属性值字段增强

```javascript
// 属性值字段增强功能
const PropertyValueEnhancer = {
    enhancePropertyValue: () => {
        // 增强的属性值补丁
        const enhancedPropertyValuePatch = {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    enableChatIntegration: true,
                    enableQuickActions: true,
                    enableUserPreview: true,
                    enablePresenceIndicator: true,
                    enableNotifications: true,
                    enableUserHistory: true,
                    supportedModels: ['res.users', 'res.partner'],
                    chatOpenMode: 'sidebar', // 'sidebar', 'popup', 'new_tab'
                    showUserStatus: true,
                    enableBulkActions: false
                };
                
                // 支持的模型映射
                this.modelChatMapping = {
                    'res.users': 'res.users',
                    'res.partner': 'res.partner'
                };
                
                // 初始化增强功能
                this.initializeEnhancements();
            },
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置多模型聊天支持
                this.setupMultiModelChat();
                
                // 设置快速操作
                if (this.enhancedConfig.enableQuickActions) {
                    this.setupQuickActions();
                }
                
                // 设置用户预览
                if (this.enhancedConfig.enableUserPreview) {
                    this.setupUserPreview();
                }
                
                // 设置通知
                if (this.enhancedConfig.enableNotifications) {
                    this.setupNotifications();
                }
            },
            
            // 设置多模型聊天支持
            setupMultiModelChat() {
                if (!this.env.services["mail.store"]) return;
                
                // 为支持的模型设置聊天钩子
                this.chatHooks = {};
                this.enhancedConfig.supportedModels.forEach(model => {
                    const targetModel = this.modelChatMapping[model] || model;
                    this.chatHooks[model] = useOpenChat(targetModel);
                });
            },
            
            // 增强的头像点击处理
            _onAvatarClicked() {
                try {
                    const comodel = this.props.comodel;
                    const value = this.props.value;
                    
                    // 检查是否支持聊天
                    if (!this.canOpenChat(comodel)) {
                        return;
                    }
                    
                    // 获取用户ID
                    const userId = Array.isArray(value) ? value[0] : value;
                    if (!userId) {
                        return;
                    }
                    
                    // 记录交互
                    this.recordUserInteraction(userId, 'avatar_click');
                    
                    // 打开聊天
                    this.openChatForModel(comodel, userId);
                    
                    // 触发事件
                    this.triggerEvent('property-avatar-clicked', {
                        model: comodel,
                        userId: userId,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error('处理头像点击失败:', error);
                }
            },
            
            // 检查是否可以打开聊天
            canOpenChat(comodel) {
                return this.enhancedConfig.enableChatIntegration && 
                       this.enhancedConfig.supportedModels.includes(comodel) &&
                       this.chatHooks[comodel] &&
                       this.showAvatar;
            },
            
            // 为指定模型打开聊天
            openChatForModel(comodel, userId) {
                const chatHook = this.chatHooks[comodel];
                if (chatHook) {
                    chatHook(userId);
                }
            },
            
            // 记录用户交互
            recordUserInteraction(userId, action) {
                if (!this.enhancedConfig.enableUserHistory) return;
                
                try {
                    const interaction = {
                        userId: userId,
                        action: action,
                        timestamp: Date.now(),
                        context: {
                            model: this.props.comodel,
                            field: this.props.name,
                            record: this.props.record?.resId
                        }
                    };
                    
                    // 保存到本地存储或发送到服务器
                    this.saveUserInteraction(interaction);
                } catch (error) {
                    console.error('记录用户交互失败:', error);
                }
            },
            
            // 保存用户交互
            saveUserInteraction(interaction) {
                try {
                    const key = 'mail_property_interactions';
                    const stored = localStorage.getItem(key);
                    const interactions = stored ? JSON.parse(stored) : [];
                    
                    interactions.push(interaction);
                    
                    // 限制存储数量
                    if (interactions.length > 100) {
                        interactions.splice(0, interactions.length - 100);
                    }
                    
                    localStorage.setItem(key, JSON.stringify(interactions));
                } catch (error) {
                    console.error('保存用户交互失败:', error);
                }
            },
            
            // 设置快速操作
            setupQuickActions() {
                // 实现快速操作功能
                console.log('设置属性字段快速操作');
            },
            
            // 设置用户预览
            setupUserPreview() {
                // 实现用户预览功能
                console.log('设置用户预览');
            },
            
            // 设置通知
            setupNotifications() {
                // 实现通知功能
                console.log('设置通知');
            },
            
            // 触发事件
            triggerEvent(eventName, data) {
                try {
                    if (this.trigger) {
                        this.trigger(eventName, data);
                    }
                    
                    // 也可以触发DOM事件
                    if (this.el) {
                        this.el.dispatchEvent(new CustomEvent(eventName, {
                            detail: data,
                            bubbles: true
                        }));
                    }
                } catch (error) {
                    console.error('触发事件失败:', error);
                }
            },
            
            // 获取用户信息
            getUserInfo(userId) {
                // 这里可以获取用户的详细信息
                return {
                    id: userId,
                    name: this.props.value?.[1] || 'Unknown User',
                    model: this.props.comodel
                };
            },
            
            // 获取快速操作列表
            getQuickActions() {
                if (!this.enhancedConfig.enableQuickActions) return [];
                
                const userId = Array.isArray(this.props.value) ? this.props.value[0] : this.props.value;
                if (!userId) return [];
                
                const actions = [];
                
                // 聊天操作
                if (this.canOpenChat(this.props.comodel)) {
                    actions.push({
                        id: 'chat',
                        icon: 'fa-comments',
                        text: '开始聊天',
                        action: () => this.openChatForModel(this.props.comodel, userId)
                    });
                }
                
                // 查看资料
                actions.push({
                    id: 'profile',
                    icon: 'fa-user',
                    text: '查看资料',
                    action: () => this.viewUserProfile(userId)
                });
                
                // 发送邮件
                if (this.props.comodel === 'res.users' || this.props.comodel === 'res.partner') {
                    actions.push({
                        id: 'email',
                        icon: 'fa-envelope',
                        text: '发送邮件',
                        action: () => this.sendEmail(userId)
                    });
                }
                
                return actions;
            },
            
            // 查看用户资料
            viewUserProfile(userId) {
                console.log('查看用户资料:', userId);
                // 实现查看用户资料的逻辑
            },
            
            // 发送邮件
            sendEmail(userId) {
                console.log('发送邮件给用户:', userId);
                // 实现发送邮件的逻辑
            }
        };
        
        // 应用增强补丁
        patch(PropertyValue.prototype, enhancedPropertyValuePatch);
        
        // 增强的多对多属性标签列表
        class EnhancedMany2manyPropertiesTagsList extends Many2manyPropertiesTagsList {
            setup() {
                super.setup();
                
                // 增强配置
                this.enhancedConfig = {
                    enableBulkActions: true,
                    enableTagPreview: true,
                    enableDragAndDrop: false,
                    enableTagGrouping: false,
                    maxDisplayTags: 10,
                    showTagCount: true
                };
                
                // 初始化增强功能
                this.initializeTagEnhancements();
            }
            
            // 初始化标签增强功能
            initializeTagEnhancements() {
                // 设置标签预览
                if (this.enhancedConfig.enableTagPreview) {
                    this.setupTagPreview();
                }
                
                // 设置批量操作
                if (this.enhancedConfig.enableBulkActions) {
                    this.setupBulkActions();
                }
            }
            
            // 增强的头像点击处理
            _onAvatarClicked(tagIndex) {
                try {
                    const tag = this.props.tags[tagIndex];
                    if (!tag) return;
                    
                    // 记录标签交互
                    this.recordTagInteraction(tag, 'avatar_click');
                    
                    // 执行原有逻辑
                    super._onAvatarClicked(tagIndex);
                    
                    // 触发事件
                    this.triggerTagEvent('tag-avatar-clicked', {
                        tag: tag,
                        index: tagIndex,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.error('处理标签头像点击失败:', error);
                }
            }
            
            // 记录标签交互
            recordTagInteraction(tag, action) {
                console.log('标签交互:', tag.id, action);
            }
            
            // 设置标签预览
            setupTagPreview() {
                console.log('设置标签预览');
            }
            
            // 设置批量操作
            setupBulkActions() {
                console.log('设置批量操作');
            }
            
            // 触发标签事件
            triggerTagEvent(eventName, data) {
                try {
                    if (this.trigger) {
                        this.trigger(eventName, data);
                    }
                } catch (error) {
                    console.error('触发标签事件失败:', error);
                }
            }
            
            // 获取显示的标签
            getDisplayTags() {
                const tags = this.props.tags || [];
                if (tags.length <= this.enhancedConfig.maxDisplayTags) {
                    return tags;
                }
                return tags.slice(0, this.enhancedConfig.maxDisplayTags);
            }
            
            // 获取隐藏的标签数量
            getHiddenTagCount() {
                const totalCount = (this.props.tags || []).length;
                const displayCount = this.enhancedConfig.maxDisplayTags;
                return Math.max(0, totalCount - displayCount);
            }
            
            // 检查是否有隐藏标签
            hasHiddenTags() {
                return this.getHiddenTagCount() > 0;
            }
        }
        
        // 替换组件
        PropertyValue.components = {
            ...PropertyValue.components,
            TagsList: EnhancedMany2manyPropertiesTagsList,
        };
        
        // 导出增强组件
        __exports.EnhancedMany2manyPropertiesTagsList = EnhancedMany2manyPropertiesTagsList;
    }
};

// 应用属性值字段增强
PropertyValueEnhancer.enhancePropertyValue();
```

## 技术特点

### 1. 补丁机制
- 使用patch工具非侵入式扩展功能
- 保持原有组件的完整性
- 支持功能的渐进式增强

### 2. 聊天集成
- 集成邮件系统的聊天功能
- 支持多种模型的聊天打开
- 智能的服务检测和条件判断

### 3. 组件替换
- 替换标签列表组件
- 为多对多属性添加聊天功能
- 保持API的向后兼容性

### 4. 条件执行
- 仅在邮件服务可用时启用功能
- 针对特定模型的功能限制
- 智能的功能检测和降级

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 非侵入式的功能扩展
- 保持原有代码的稳定性

### 2. 装饰器模式 (Decorator Pattern)
- 为属性字段添加聊天功能
- 不改变原有结构

### 3. 适配器模式 (Adapter Pattern)
- 适配不同模型的聊天功能
- 统一的交互接口

## 注意事项

1. **服务检测**: 确保邮件服务可用后再启用功能
2. **模型限制**: 仅对支持的模型启用聊天功能
3. **错误处理**: 妥善处理聊天打开失败的情况
4. **性能考虑**: 避免不必要的功能初始化

## 扩展建议

1. **多模型支持**: 扩展对更多模型的聊天支持
2. **快速操作**: 添加更多的用户快速操作
3. **预览功能**: 实现用户信息的预览功能
4. **批量操作**: 支持多个用户的批量操作
5. **历史记录**: 记录用户交互历史

该属性值字段补丁为邮件系统提供了重要的属性字段聊天集成功能，是属性管理和用户交互的核心组件。

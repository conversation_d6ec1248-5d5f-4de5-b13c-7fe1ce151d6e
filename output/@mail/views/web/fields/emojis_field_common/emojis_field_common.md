# Emojis Field Common - 表情符号字段通用模块

## 概述

`emojis_field_common.js` 是 Odoo 邮件系统的表情符号字段通用模块，专门为各种字段类型提供表情符号支持的通用功能。该模块基于Web核心的表情符号选择器和OWL框架，集成了表情符号选择、插入处理、光标管理等核心功能，为邮件系统提供了完整的表情符号输入支持，是邮件系统表情符号功能的基础组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.js`
- **行数**: 51
- **模块**: `@mail/views/web/fields/emojis_field_common/emojis_field_common`

## 依赖关系

```javascript
// 核心依赖
'@web/core/emoji_picker/emoji_picker'  // 表情符号选择器
'@odoo/owl'                            // OWL 框架
```

## 核心功能

### 1. EmojisFieldCommon 混入

```javascript
const EmojisFieldCommon = (T) =>
    (class EmojisFieldCommon extends T {
        _setupOverride() {
            this.emojiPicker = useEmojiPicker(
                useRef("emojisButton"),
                {
                    onSelect: (codepoints) => {
                        // 表情符号选择处理逻辑
                    },
                },
                {
                    position: "bottom",
                }
            );
        }
    });
```

**混入功能**:
- **高阶组件**: 接受任意组件类型T作为基类
- **功能增强**: 为基类添加表情符号支持
- **可复用**: 可应用于不同类型的字段组件
- **非侵入**: 不改变原有组件的结构

### 2. 表情符号选择器设置

```javascript
this.emojiPicker = useEmojiPicker(
    useRef("emojisButton"),
    {
        onSelect: (codepoints) => {
            // 选择处理逻辑
        },
    },
    {
        position: "bottom",
    }
);
```

**选择器设置功能**:
- **按钮引用**: 使用useRef引用表情符号按钮
- **选择回调**: 定义表情符号选择时的处理逻辑
- **位置配置**: 设置弹出框的显示位置
- **钩子集成**: 使用useEmojiPicker钩子

### 3. 表情符号插入处理

```javascript
onSelect: (codepoints) => {
    const originalContent = this.targetEditElement.el.value;
    const start = this.targetEditElement.el.selectionStart;
    const end = this.targetEditElement.el.selectionEnd;
    const left = originalContent.slice(0, start);
    const right = originalContent.slice(end, originalContent.length);
    this.targetEditElement.el.value = left + codepoints + right;
    
    // 触发输入事件
    this.targetEditElement.el.dispatchEvent(new InputEvent("input"));
    this.targetEditElement.el.dispatchEvent(new KeyboardEvent("keydown"));
    
    // 设置光标位置
    this.targetEditElement.el.focus();
    const newCursorPos = start + codepoints.length;
    this.targetEditElement.el.setSelectionRange(newCursorPos, newCursorPos);
    
    // 触发自定义回调
    if (this._emojiAdded) {
        this._emojiAdded();
    }
}
```

**插入处理功能**:
- **内容获取**: 获取当前输入框的内容和选择范围
- **内容拼接**: 在光标位置插入表情符号
- **事件触发**: 触发input和keydown事件通知字段变更
- **光标管理**: 设置插入后的光标位置
- **回调执行**: 执行自定义的表情符号添加回调

## 使用场景

### 1. 表情符号字段通用模块增强

```javascript
// 表情符号字段通用模块增强功能
const EmojisFieldCommonEnhancer = {
    enhanceEmojisFieldCommon: () => {
        // 增强的表情符号字段通用模块
        const EnhancedEmojisFieldCommon = (T) =>
            (class EnhancedEmojisFieldCommon extends T {
                // 增强的设置重写
                _setupOverride() {
                    // 增强的配置选项
                    this.enhancedEmojiConfig = {
                        enableKeyboardShortcuts: true,
                        enableRecentEmojis: true,
                        enableCustomEmojis: false,
                        enableEmojiSearch: true,
                        enableEmojiCategories: true,
                        maxRecentEmojis: 20,
                        emojiSize: 'medium',
                        showEmojiNames: true,
                        enableSkinTones: true,
                        autoClose: true,
                        animationDuration: 200
                    };
                    
                    // 最近使用的表情符号
                    this.recentEmojis = this.loadRecentEmojis();
                    
                    // 自定义表情符号
                    this.customEmojis = this.loadCustomEmojis();
                    
                    // 表情符号使用统计
                    this.emojiStats = this.loadEmojiStats();
                    
                    // 设置增强的表情符号选择器
                    this.setupEnhancedEmojiPicker();
                    
                    // 设置键盘快捷键
                    if (this.enhancedEmojiConfig.enableKeyboardShortcuts) {
                        this.setupKeyboardShortcuts();
                    }
                    
                    // 设置表情符号搜索
                    if (this.enhancedEmojiConfig.enableEmojiSearch) {
                        this.setupEmojiSearch();
                    }
                }
                
                // 设置增强的表情符号选择器
                setupEnhancedEmojiPicker() {
                    this.emojiPicker = useEmojiPicker(
                        useRef("emojisButton"),
                        {
                            onSelect: (codepoints, emojiData) => {
                                this.handleEnhancedEmojiSelect(codepoints, emojiData);
                            },
                            onOpen: () => {
                                this.handleEmojiPickerOpen();
                            },
                            onClose: () => {
                                this.handleEmojiPickerClose();
                            },
                            recentEmojis: this.recentEmojis,
                            customEmojis: this.customEmojis,
                            enableSearch: this.enhancedEmojiConfig.enableEmojiSearch,
                            enableCategories: this.enhancedEmojiConfig.enableEmojiCategories,
                            showNames: this.enhancedEmojiConfig.showEmojiNames,
                            enableSkinTones: this.enhancedEmojiConfig.enableSkinTones,
                            size: this.enhancedEmojiConfig.emojiSize
                        },
                        {
                            position: "bottom",
                            autoClose: this.enhancedEmojiConfig.autoClose,
                            animationDuration: this.enhancedEmojiConfig.animationDuration
                        }
                    );
                }
                
                // 处理增强的表情符号选择
                handleEnhancedEmojiSelect(codepoints, emojiData) {
                    try {
                        // 执行基础插入逻辑
                        this.insertEmojiAtCursor(codepoints);
                        
                        // 记录表情符号使用
                        this.recordEmojiUsage(emojiData);
                        
                        // 更新最近使用
                        this.updateRecentEmojis(emojiData);
                        
                        // 触发自定义事件
                        this.triggerEmojiEvent('emoji-selected', {
                            codepoints: codepoints,
                            emoji: emojiData,
                            timestamp: Date.now()
                        });
                        
                        // 执行原有回调
                        if (this._emojiAdded) {
                            this._emojiAdded(emojiData);
                        }
                    } catch (error) {
                        console.error('处理表情符号选择失败:', error);
                    }
                }
                
                // 在光标位置插入表情符号
                insertEmojiAtCursor(codepoints) {
                    try {
                        const element = this.targetEditElement.el;
                        if (!element) return;
                        
                        const originalContent = element.value;
                        const start = element.selectionStart;
                        const end = element.selectionEnd;
                        
                        // 构建新内容
                        const left = originalContent.slice(0, start);
                        const right = originalContent.slice(end);
                        const newContent = left + codepoints + right;
                        
                        // 更新内容
                        element.value = newContent;
                        
                        // 触发事件
                        element.dispatchEvent(new InputEvent("input", { bubbles: true }));
                        element.dispatchEvent(new KeyboardEvent("keydown", { bubbles: true }));
                        
                        // 设置光标位置
                        element.focus();
                        const newCursorPos = start + codepoints.length;
                        element.setSelectionRange(newCursorPos, newCursorPos);
                        
                        // 滚动到光标位置
                        this.scrollToCursor(element);
                    } catch (error) {
                        console.error('插入表情符号失败:', error);
                    }
                }
                
                // 滚动到光标位置
                scrollToCursor(element) {
                    try {
                        // 简化的滚动逻辑
                        if (element.scrollIntoView) {
                            element.scrollIntoView({ block: 'nearest' });
                        }
                    } catch (error) {
                        console.error('滚动到光标位置失败:', error);
                    }
                }
                
                // 记录表情符号使用
                recordEmojiUsage(emojiData) {
                    try {
                        if (!emojiData) return;
                        
                        const emojiId = emojiData.id || emojiData.codepoints;
                        if (!this.emojiStats[emojiId]) {
                            this.emojiStats[emojiId] = {
                                count: 0,
                                firstUsed: Date.now(),
                                lastUsed: Date.now(),
                                emoji: emojiData
                            };
                        }
                        
                        this.emojiStats[emojiId].count++;
                        this.emojiStats[emojiId].lastUsed = Date.now();
                        
                        // 保存统计数据
                        this.saveEmojiStats();
                    } catch (error) {
                        console.error('记录表情符号使用失败:', error);
                    }
                }
                
                // 更新最近使用的表情符号
                updateRecentEmojis(emojiData) {
                    try {
                        if (!this.enhancedEmojiConfig.enableRecentEmojis || !emojiData) return;
                        
                        const emojiId = emojiData.id || emojiData.codepoints;
                        
                        // 移除已存在的
                        this.recentEmojis = this.recentEmojis.filter(emoji => 
                            (emoji.id || emoji.codepoints) !== emojiId
                        );
                        
                        // 添加到开头
                        this.recentEmojis.unshift(emojiData);
                        
                        // 限制数量
                        if (this.recentEmojis.length > this.enhancedEmojiConfig.maxRecentEmojis) {
                            this.recentEmojis = this.recentEmojis.slice(0, this.enhancedEmojiConfig.maxRecentEmojis);
                        }
                        
                        // 保存到本地存储
                        this.saveRecentEmojis();
                    } catch (error) {
                        console.error('更新最近表情符号失败:', error);
                    }
                }
                
                // 设置键盘快捷键
                setupKeyboardShortcuts() {
                    const element = this.targetEditElement.el;
                    if (!element) return;
                    
                    element.addEventListener('keydown', (event) => {
                        this.handleKeyboardShortcuts(event);
                    });
                }
                
                // 处理键盘快捷键
                handleKeyboardShortcuts(event) {
                    try {
                        // Ctrl/Cmd + E 打开表情符号选择器
                        if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
                            event.preventDefault();
                            this.openEmojiPicker();
                            return;
                        }
                        
                        // Ctrl/Cmd + Shift + E 打开最近使用的表情符号
                        if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'E') {
                            event.preventDefault();
                            this.openRecentEmojis();
                            return;
                        }
                    } catch (error) {
                        console.error('处理键盘快捷键失败:', error);
                    }
                }
                
                // 打开表情符号选择器
                openEmojiPicker() {
                    try {
                        if (this.emojiPicker && this.emojiPicker.open) {
                            this.emojiPicker.open();
                        }
                    } catch (error) {
                        console.error('打开表情符号选择器失败:', error);
                    }
                }
                
                // 打开最近使用的表情符号
                openRecentEmojis() {
                    try {
                        // 这里可以实现显示最近使用表情符号的逻辑
                        console.log('打开最近使用的表情符号:', this.recentEmojis);
                    } catch (error) {
                        console.error('打开最近表情符号失败:', error);
                    }
                }
                
                // 处理表情符号选择器打开
                handleEmojiPickerOpen() {
                    this.triggerEmojiEvent('emoji-picker-opened', {
                        timestamp: Date.now()
                    });
                }
                
                // 处理表情符号选择器关闭
                handleEmojiPickerClose() {
                    this.triggerEmojiEvent('emoji-picker-closed', {
                        timestamp: Date.now()
                    });
                }
                
                // 触发表情符号事件
                triggerEmojiEvent(eventName, data) {
                    try {
                        if (this.trigger) {
                            this.trigger(eventName, data);
                        }
                        
                        // 也可以触发DOM事件
                        const element = this.targetEditElement.el;
                        if (element) {
                            element.dispatchEvent(new CustomEvent(eventName, {
                                detail: data,
                                bubbles: true
                            }));
                        }
                    } catch (error) {
                        console.error('触发表情符号事件失败:', error);
                    }
                }
                
                // 加载最近表情符号
                loadRecentEmojis() {
                    try {
                        const stored = localStorage.getItem('mail_recent_emojis');
                        return stored ? JSON.parse(stored) : [];
                    } catch (error) {
                        console.error('加载最近表情符号失败:', error);
                        return [];
                    }
                }
                
                // 保存最近表情符号
                saveRecentEmojis() {
                    try {
                        localStorage.setItem('mail_recent_emojis', JSON.stringify(this.recentEmojis));
                    } catch (error) {
                        console.error('保存最近表情符号失败:', error);
                    }
                }
                
                // 加载自定义表情符号
                loadCustomEmojis() {
                    try {
                        const stored = localStorage.getItem('mail_custom_emojis');
                        return stored ? JSON.parse(stored) : [];
                    } catch (error) {
                        console.error('加载自定义表情符号失败:', error);
                        return [];
                    }
                }
                
                // 加载表情符号统计
                loadEmojiStats() {
                    try {
                        const stored = localStorage.getItem('mail_emoji_stats');
                        return stored ? JSON.parse(stored) : {};
                    } catch (error) {
                        console.error('加载表情符号统计失败:', error);
                        return {};
                    }
                }
                
                // 保存表情符号统计
                saveEmojiStats() {
                    try {
                        localStorage.setItem('mail_emoji_stats', JSON.stringify(this.emojiStats));
                    } catch (error) {
                        console.error('保存表情符号统计失败:', error);
                    }
                }
                
                // 获取最常用的表情符号
                getMostUsedEmojis(limit = 10) {
                    try {
                        const sorted = Object.values(this.emojiStats)
                            .sort((a, b) => b.count - a.count)
                            .slice(0, limit);
                        
                        return sorted.map(stat => stat.emoji);
                    } catch (error) {
                        console.error('获取最常用表情符号失败:', error);
                        return [];
                    }
                }
                
                // 清除表情符号数据
                clearEmojiData() {
                    try {
                        this.recentEmojis = [];
                        this.emojiStats = {};
                        
                        localStorage.removeItem('mail_recent_emojis');
                        localStorage.removeItem('mail_emoji_stats');
                        
                        this.triggerEmojiEvent('emoji-data-cleared', {
                            timestamp: Date.now()
                        });
                    } catch (error) {
                        console.error('清除表情符号数据失败:', error);
                    }
                }
            });
        
        // 替换原始混入
        __exports.EnhancedEmojisFieldCommon = EnhancedEmojisFieldCommon;
    }
};

// 应用表情符号字段通用模块增强
EmojisFieldCommonEnhancer.enhanceEmojisFieldCommon();
```

## 技术特点

### 1. 混入模式
- 高阶组件设计
- 可应用于任意基类
- 功能的非侵入式增强

### 2. 钩子集成
- 使用useEmojiPicker钩子
- 与Web核心系统深度集成
- 标准化的API接口

### 3. 事件处理
- 完整的事件触发机制
- 支持字段变更通知
- 自定义回调支持

### 4. 光标管理
- 精确的光标位置控制
- 选择范围的处理
- 插入后的光标定位

## 设计模式

### 1. 混入模式 (Mixin Pattern)
- 为多个类提供共同功能
- 代码复用和功能组合

### 2. 模板方法模式 (Template Method Pattern)
- 定义算法骨架
- 子类实现具体细节

### 3. 观察者模式 (Observer Pattern)
- 事件驱动的通信
- 松耦合的组件交互

## 注意事项

1. **光标位置**: 确保表情符号插入后光标位置正确
2. **事件触发**: 正确触发字段变更事件
3. **内容完整性**: 保持输入内容的完整性
4. **性能考虑**: 避免频繁的DOM操作影响性能

## 扩展建议

1. **键盘快捷键**: 添加表情符号的键盘快捷键
2. **最近使用**: 记录和显示最近使用的表情符号
3. **搜索功能**: 提供表情符号的搜索功能
4. **自定义表情**: 支持自定义表情符号
5. **使用统计**: 记录表情符号的使用统计

该表情符号字段通用模块为邮件系统提供了重要的表情符号基础功能，是各种表情符号字段的核心支撑组件。

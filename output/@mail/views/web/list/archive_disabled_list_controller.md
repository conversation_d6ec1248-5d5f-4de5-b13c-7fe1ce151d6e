# Archive Disabled List Controller - 禁用归档列表控制器

## 概述

`archive_disabled_list_controller.js` 是 Odoo 邮件系统的禁用归档列表控制器，专门用于禁用列表视图中的归档功能。该组件基于Web框架的列表控制器，通过简单的配置禁用归档操作，为邮件系统提供了特定场景下的列表控制功能，是邮件系统数据保护和操作限制的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/list/archive_disabled_list_controller.js`
- **行数**: 14
- **模块**: `@mail/views/web/list/archive_disabled_list_controller`

## 依赖关系

```javascript
// 核心依赖
'@web/views/list/list_controller'  // 列表控制器
```

## 核心功能

### 1. ArchiveDisabledListController 类

```javascript
const ArchiveDisabledListController = class ArchiveDisabledListController extends ListController {
    setup() {
        super.setup();
        this.archiveEnabled = false;
    }
}
```

**控制器特性**:
- **继承扩展**: 继承ListController的所有功能
- **归档禁用**: 通过设置archiveEnabled为false禁用归档功能
- **简单配置**: 最小化的配置实现功能禁用
- **向后兼容**: 保持与基础控制器的完全兼容

### 2. 功能禁用机制

```javascript
setup() {
    super.setup();
    this.archiveEnabled = false;
}
```

**禁用机制**:
- **父类调用**: 调用父类的setup方法保持基础功能
- **属性设置**: 设置archiveEnabled属性为false
- **功能屏蔽**: 阻止归档相关操作的执行
- **UI隐藏**: 隐藏归档相关的UI元素

## 使用场景

### 1. 禁用归档列表控制器增强

```javascript
// 禁用归档列表控制器增强功能
const ArchiveDisabledListControllerEnhancer = {
    enhanceArchiveDisabledListController: () => {
        // 增强的禁用归档列表控制器
        class EnhancedArchiveDisabledListController extends ArchiveDisabledListController {
            setup() {
                super.setup();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    disableArchive: true,
                    disableUnarchive: true,
                    disableDelete: false,
                    disableBulkArchive: true,
                    disableBulkUnarchive: true,
                    disableBulkDelete: false,
                    showDisabledMessage: true,
                    customDisabledMessage: '',
                    enablePermissionCheck: true,
                    logDisabledActions: true,
                    alternativeActions: []
                };
                
                // 禁用的操作列表
                this.disabledActions = new Set();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置禁用操作
                this.setupDisabledActions();
                
                // 设置权限检查
                if (this.enhancedConfig.enablePermissionCheck) {
                    this.setupPermissionCheck();
                }
                
                // 设置日志记录
                if (this.enhancedConfig.logDisabledActions) {
                    this.setupActionLogging();
                }
                
                // 设置替代操作
                if (this.enhancedConfig.alternativeActions.length > 0) {
                    this.setupAlternativeActions();
                }
            }
            
            // 设置禁用操作
            setupDisabledActions() {
                if (this.enhancedConfig.disableArchive) {
                    this.disabledActions.add('archive');
                }
                
                if (this.enhancedConfig.disableUnarchive) {
                    this.disabledActions.add('unarchive');
                }
                
                if (this.enhancedConfig.disableDelete) {
                    this.disabledActions.add('delete');
                }
                
                if (this.enhancedConfig.disableBulkArchive) {
                    this.disabledActions.add('bulk_archive');
                }
                
                if (this.enhancedConfig.disableBulkUnarchive) {
                    this.disabledActions.add('bulk_unarchive');
                }
                
                if (this.enhancedConfig.disableBulkDelete) {
                    this.disabledActions.add('bulk_delete');
                }
            }
            
            // 检查操作是否被禁用
            isActionDisabled(actionName) {
                return this.disabledActions.has(actionName);
            }
            
            // 获取禁用消息
            getDisabledMessage(actionName) {
                if (this.enhancedConfig.customDisabledMessage) {
                    return this.enhancedConfig.customDisabledMessage;
                }
                
                const messages = {
                    'archive': '归档功能已被禁用',
                    'unarchive': '取消归档功能已被禁用',
                    'delete': '删除功能已被禁用',
                    'bulk_archive': '批量归档功能已被禁用',
                    'bulk_unarchive': '批量取消归档功能已被禁用',
                    'bulk_delete': '批量删除功能已被禁用'
                };
                
                return messages[actionName] || '该操作已被禁用';
            }
            
            // 重写归档方法
            async archive(records) {
                if (this.isActionDisabled('archive')) {
                    this.handleDisabledAction('archive', records);
                    return;
                }
                
                return await super.archive(records);
            }
            
            // 重写取消归档方法
            async unarchive(records) {
                if (this.isActionDisabled('unarchive')) {
                    this.handleDisabledAction('unarchive', records);
                    return;
                }
                
                return await super.unarchive(records);
            }
            
            // 重写删除方法
            async delete(records) {
                if (this.isActionDisabled('delete')) {
                    this.handleDisabledAction('delete', records);
                    return;
                }
                
                return await super.delete(records);
            }
            
            // 处理禁用操作
            handleDisabledAction(actionName, records) {
                try {
                    // 记录禁用操作
                    this.logDisabledAction(actionName, records);
                    
                    // 显示禁用消息
                    if (this.enhancedConfig.showDisabledMessage) {
                        this.showDisabledMessage(actionName);
                    }
                    
                    // 触发禁用事件
                    this.triggerDisabledEvent(actionName, records);
                    
                    // 执行替代操作
                    this.executeAlternativeAction(actionName, records);
                } catch (error) {
                    console.error('处理禁用操作失败:', error);
                }
            }
            
            // 记录禁用操作
            logDisabledAction(actionName, records) {
                if (!this.enhancedConfig.logDisabledActions) return;
                
                const logEntry = {
                    action: actionName,
                    recordCount: records ? records.length : 0,
                    timestamp: new Date().toISOString(),
                    user: this.env.services.user?.userId,
                    model: this.props.resModel
                };
                
                console.log('禁用操作记录:', logEntry);
                
                // 这里可以发送到服务器进行记录
                this.sendDisabledActionLog(logEntry);
            }
            
            // 发送禁用操作日志
            sendDisabledActionLog(logEntry) {
                // 实现发送日志到服务器的逻辑
                console.log('发送禁用操作日志:', logEntry);
            }
            
            // 显示禁用消息
            showDisabledMessage(actionName) {
                const message = this.getDisabledMessage(actionName);
                
                // 使用通知服务显示消息
                if (this.env.services.notification) {
                    this.env.services.notification.add(message, {
                        type: 'warning',
                        title: '操作被禁用'
                    });
                } else {
                    // 回退到alert
                    alert(message);
                }
            }
            
            // 触发禁用事件
            triggerDisabledEvent(actionName, records) {
                try {
                    if (this.trigger) {
                        this.trigger('action-disabled', {
                            action: actionName,
                            records: records,
                            timestamp: Date.now()
                        });
                    }
                } catch (error) {
                    console.error('触发禁用事件失败:', error);
                }
            }
            
            // 执行替代操作
            executeAlternativeAction(actionName, records) {
                const alternatives = this.enhancedConfig.alternativeActions.filter(
                    alt => alt.forAction === actionName
                );
                
                alternatives.forEach(alternative => {
                    try {
                        if (typeof alternative.action === 'function') {
                            alternative.action(records, this);
                        } else if (typeof alternative.action === 'string') {
                            this[alternative.action](records);
                        }
                    } catch (error) {
                        console.error('执行替代操作失败:', error);
                    }
                });
            }
            
            // 设置权限检查
            setupPermissionCheck() {
                // 实现权限检查逻辑
                console.log('设置权限检查');
            }
            
            // 设置操作日志
            setupActionLogging() {
                // 实现操作日志设置
                console.log('设置操作日志');
            }
            
            // 设置替代操作
            setupAlternativeActions() {
                // 实现替代操作设置
                console.log('设置替代操作');
            }
            
            // 检查用户权限
            checkUserPermission(actionName) {
                if (!this.enhancedConfig.enablePermissionCheck) return true;
                
                // 这里可以实现具体的权限检查逻辑
                return true;
            }
            
            // 获取可用操作
            getAvailableActions() {
                const allActions = ['archive', 'unarchive', 'delete', 'bulk_archive', 'bulk_unarchive', 'bulk_delete'];
                return allActions.filter(action => !this.isActionDisabled(action));
            }
            
            // 获取禁用操作
            getDisabledActions() {
                return Array.from(this.disabledActions);
            }
            
            // 动态启用操作
            enableAction(actionName) {
                this.disabledActions.delete(actionName);
                this.triggerActionStateChange(actionName, 'enabled');
            }
            
            // 动态禁用操作
            disableAction(actionName) {
                this.disabledActions.add(actionName);
                this.triggerActionStateChange(actionName, 'disabled');
            }
            
            // 触发操作状态变更事件
            triggerActionStateChange(actionName, state) {
                try {
                    if (this.trigger) {
                        this.trigger('action-state-changed', {
                            action: actionName,
                            state: state,
                            timestamp: Date.now()
                        });
                    }
                } catch (error) {
                    console.error('触发操作状态变更事件失败:', error);
                }
            }
            
            // 批量设置操作状态
            setActionsState(actions, enabled = false) {
                actions.forEach(action => {
                    if (enabled) {
                        this.enableAction(action);
                    } else {
                        this.disableAction(action);
                    }
                });
            }
            
            // 重置到默认状态
            resetToDefault() {
                this.disabledActions.clear();
                this.setupDisabledActions();
            }
            
            // 获取配置信息
            getConfiguration() {
                return {
                    ...this.enhancedConfig,
                    disabledActions: Array.from(this.disabledActions),
                    availableActions: this.getAvailableActions()
                };
            }
            
            // 更新配置
            updateConfiguration(newConfig) {
                Object.assign(this.enhancedConfig, newConfig);
                this.resetToDefault();
            }
        }
        
        // 导出增强的控制器
        __exports.EnhancedArchiveDisabledListController = EnhancedArchiveDisabledListController;
    }
};

// 应用禁用归档列表控制器增强
ArchiveDisabledListControllerEnhancer.enhanceArchiveDisabledListController();
```

## 技术特点

### 1. 简单继承
- 继承ListController的所有功能
- 最小化的代码实现
- 保持完全的向后兼容性

### 2. 配置驱动
- 通过简单的属性设置控制功能
- 声明式的功能禁用
- 易于理解和维护

### 3. 功能屏蔽
- 有效阻止归档操作的执行
- 隐藏相关的UI元素
- 保护数据不被意外归档

### 4. 轻量级实现
- 最小的代码占用
- 高效的运行性能
- 简洁的实现逻辑

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承基础控制器功能
- 扩展特定的行为

### 2. 配置模式 (Configuration Pattern)
- 通过配置控制功能
- 声明式的行为定义

### 3. 模板方法模式 (Template Method Pattern)
- 重写特定的方法行为
- 保持整体流程不变

## 注意事项

1. **功能完整性**: 确保禁用归档不影响其他功能
2. **用户体验**: 提供清晰的禁用状态提示
3. **权限检查**: 结合权限系统进行访问控制
4. **数据安全**: 确保重要数据不被意外操作

## 扩展建议

1. **权限集成**: 与权限系统集成进行动态控制
2. **消息提示**: 添加用户友好的禁用提示
3. **日志记录**: 记录禁用操作的尝试
4. **替代操作**: 提供归档的替代操作方案
5. **配置化**: 支持更灵活的配置选项

该禁用归档列表控制器为邮件系统提供了重要的数据保护功能，是特定场景下操作限制和安全控制的核心组件。

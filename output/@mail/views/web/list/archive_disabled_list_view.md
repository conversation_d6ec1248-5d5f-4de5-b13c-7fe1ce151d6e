# Archive Disabled List View - 禁用归档列表视图

## 概述

`archive_disabled_list_view.js` 是 Odoo 邮件系统的禁用归档列表视图，专门用于创建一个禁用归档功能的列表视图类型。该组件基于Web框架的列表视图和禁用归档列表控制器，通过简单的配置组合创建了一个专用的视图类型，为邮件系统提供了特定场景下的列表视图支持，是邮件系统活动管理和数据保护的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/web/list/archive_disabled_list_view.js`
- **行数**: 16
- **模块**: `@mail/views/web/list/archive_disabled_list_view`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                              // 注册表
'@web/views/list/list_view'                                       // 列表视图
'@mail/views/web/list/archive_disabled_list_controller'          // 禁用归档列表控制器
```

## 核心功能

### 1. 视图配置

```javascript
const archiveDisabledListView = {
    ...listView,
    Controller: ArchiveDisabledListController,
};
```

**视图配置功能**:
- **配置继承**: 继承标准列表视图的所有配置
- **控制器替换**: 使用禁用归档的专用控制器
- **功能组合**: 组合基础视图和专用控制器
- **简洁实现**: 最小化的配置实现功能定制

### 2. 视图注册

```javascript
registry.category("views").add("archive_disabled_activity_list", archiveDisabledListView);
```

**注册功能**:
- **类型注册**: 注册为"archive_disabled_activity_list"视图类型
- **全局可用**: 使视图在整个系统中可用
- **命名规范**: 使用描述性的视图类型名称
- **活动专用**: 专门用于活动相关的列表视图

## 使用场景

### 1. 禁用归档列表视图增强

```javascript
// 禁用归档列表视图增强功能
const ArchiveDisabledListViewEnhancer = {
    enhanceArchiveDisabledListView: () => {
        // 增强的禁用归档列表视图配置
        const enhancedArchiveDisabledListView = {
            ...listView,
            Controller: ArchiveDisabledListController,
            
            // 增强的视图配置
            display_name: "禁用归档列表视图",
            icon: "fa-list",
            multiRecord: true,
            searchMenuTypes: ["filter", "groupBy", "favorite"],
            
            // 增强的属性
            props: (genericProps, view, config) => {
                const props = listView.props(genericProps, view, config);
                
                // 添加禁用归档相关的属性
                props.archiveDisabled = true;
                props.showArchiveWarning = true;
                props.alternativeActions = [
                    {
                        name: "标记为完成",
                        action: "mark_done",
                        icon: "fa-check",
                        confirm: false
                    },
                    {
                        name: "延期",
                        action: "postpone",
                        icon: "fa-clock-o",
                        confirm: true
                    }
                ];
                
                return props;
            },
            
            // 增强的渲染器配置
            Renderer: class EnhancedArchiveDisabledListRenderer extends listView.Renderer {
                setup() {
                    super.setup();
                    
                    // 增强配置
                    this.enhancedConfig = {
                        showDisabledIndicator: true,
                        highlightProtectedRecords: true,
                        showAlternativeActions: true,
                        enableTooltips: true,
                        customStyling: true
                    };
                }
                
                // 获取行的CSS类
                getRowClass(record) {
                    const baseClass = super.getRowClass ? super.getRowClass(record) : '';
                    
                    if (this.enhancedConfig.highlightProtectedRecords) {
                        return `${baseClass} o_archive_disabled_row`;
                    }
                    
                    return baseClass;
                }
                
                // 渲染禁用指示器
                renderDisabledIndicator() {
                    if (!this.enhancedConfig.showDisabledIndicator) return '';
                    
                    return `
                        <div class="o_archive_disabled_indicator">
                            <i class="fa fa-shield text-warning" title="归档功能已禁用"></i>
                            <span class="text-muted">受保护的记录</span>
                        </div>
                    `;
                }
                
                // 渲染替代操作
                renderAlternativeActions(record) {
                    if (!this.enhancedConfig.showAlternativeActions) return '';
                    
                    const actions = this.props.alternativeActions || [];
                    return actions.map(action => `
                        <button class="btn btn-sm btn-outline-secondary me-1" 
                                data-action="${action.action}" 
                                data-record-id="${record.id}"
                                title="${action.name}">
                            <i class="${action.icon}"></i>
                            ${action.name}
                        </button>
                    `).join('');
                }
            },
            
            // 增强的搜索面板配置
            SearchPanel: class EnhancedArchiveDisabledSearchPanel extends (listView.SearchPanel || Component) {
                setup() {
                    super.setup();
                    
                    // 添加保护状态过滤器
                    this.protectionFilters = [
                        {
                            name: "protected",
                            label: "受保护的记录",
                            domain: [["archive_disabled", "=", true]]
                        },
                        {
                            name: "unprotected", 
                            label: "可归档的记录",
                            domain: [["archive_disabled", "=", false]]
                        }
                    ];
                }
                
                // 获取增强的过滤器
                getEnhancedFilters() {
                    const baseFilters = super.getFilters ? super.getFilters() : [];
                    return [...baseFilters, ...this.protectionFilters];
                }
            },
            
            // 增强的控制面板配置
            ControlPanel: class EnhancedArchiveDisabledControlPanel extends (listView.ControlPanel || Component) {
                setup() {
                    super.setup();
                    
                    // 添加保护状态指示器
                    this.showProtectionStatus = true;
                }
                
                // 渲染保护状态
                renderProtectionStatus() {
                    if (!this.showProtectionStatus) return '';
                    
                    return `
                        <div class="o_protection_status">
                            <span class="badge badge-warning">
                                <i class="fa fa-shield"></i>
                                归档已禁用
                            </span>
                        </div>
                    `;
                }
                
                // 获取可用操作
                getAvailableActions() {
                    const baseActions = super.getAvailableActions ? super.getAvailableActions() : [];
                    
                    // 过滤掉归档相关操作
                    return baseActions.filter(action => 
                        !['archive', 'unarchive'].includes(action.name)
                    );
                }
            }
        };
        
        // 创建多个专用视图变体
        const viewVariants = {
            // 活动专用视图
            archive_disabled_activity_list: {
                ...enhancedArchiveDisabledListView,
                display_name: "活动列表（禁用归档）",
                description: "专用于活动管理的禁用归档列表视图",
                defaultOrder: "date_deadline asc",
                
                // 活动特定的列配置
                columns: [
                    { name: "activity_type_id", label: "活动类型", width: "150px" },
                    { name: "summary", label: "摘要", width: "300px" },
                    { name: "date_deadline", label: "截止日期", width: "120px" },
                    { name: "user_id", label: "负责人", width: "120px" },
                    { name: "state", label: "状态", width: "100px" }
                ],
                
                // 活动特定的过滤器
                defaultFilters: [
                    {
                        name: "my_activities",
                        label: "我的活动",
                        domain: [["user_id", "=", "uid"]]
                    },
                    {
                        name: "overdue",
                        label: "逾期活动", 
                        domain: [["date_deadline", "<", "today"]]
                    },
                    {
                        name: "today",
                        label: "今日活动",
                        domain: [["date_deadline", "=", "today"]]
                    }
                ]
            },
            
            // 邮件专用视图
            archive_disabled_mail_list: {
                ...enhancedArchiveDisabledListView,
                display_name: "邮件列表（禁用归档）",
                description: "专用于邮件管理的禁用归档列表视图",
                defaultOrder: "date desc",
                
                // 邮件特定的列配置
                columns: [
                    { name: "subject", label: "主题", width: "400px" },
                    { name: "author_id", label: "发件人", width: "150px" },
                    { name: "date", label: "日期", width: "120px" },
                    { name: "message_type", label: "类型", width: "100px" },
                    { name: "state", label: "状态", width: "100px" }
                ],
                
                // 邮件特定的过滤器
                defaultFilters: [
                    {
                        name: "inbox",
                        label: "收件箱",
                        domain: [["message_type", "=", "email"], ["state", "=", "received"]]
                    },
                    {
                        name: "sent",
                        label: "已发送",
                        domain: [["message_type", "=", "email"], ["state", "=", "sent"]]
                    },
                    {
                        name: "unread",
                        label: "未读",
                        domain: [["is_read", "=", false]]
                    }
                ]
            },
            
            // 通用保护视图
            archive_disabled_protected_list: {
                ...enhancedArchiveDisabledListView,
                display_name: "受保护列表",
                description: "通用的禁用归档列表视图",
                
                // 通用的保护指示器
                showProtectionBanner: true,
                protectionMessage: "此列表中的记录受到保护，无法进行归档操作。",
                
                // 通用的替代操作
                alternativeActions: [
                    {
                        name: "标记",
                        action: "toggle_flag",
                        icon: "fa-flag",
                        confirm: false
                    },
                    {
                        name: "导出",
                        action: "export_data",
                        icon: "fa-download",
                        confirm: false
                    },
                    {
                        name: "复制",
                        action: "duplicate_record",
                        icon: "fa-copy",
                        confirm: true
                    }
                ]
            }
        };
        
        // 注册所有视图变体
        Object.entries(viewVariants).forEach(([viewType, viewConfig]) => {
            registry.category("views").add(viewType, viewConfig);
        });
        
        // 创建视图工厂
        const ArchiveDisabledViewFactory = {
            // 创建自定义禁用归档视图
            createCustomView: (options = {}) => {
                const defaultOptions = {
                    baseView: listView,
                    controller: ArchiveDisabledListController,
                    displayName: "自定义禁用归档视图",
                    icon: "fa-list",
                    archiveDisabled: true,
                    showWarnings: true,
                    alternativeActions: []
                };
                
                const config = { ...defaultOptions, ...options };
                
                return {
                    ...config.baseView,
                    Controller: config.controller,
                    display_name: config.displayName,
                    icon: config.icon,
                    
                    props: (genericProps, view, viewConfig) => {
                        const props = config.baseView.props(genericProps, view, viewConfig);
                        
                        // 添加自定义属性
                        props.archiveDisabled = config.archiveDisabled;
                        props.showWarnings = config.showWarnings;
                        props.alternativeActions = config.alternativeActions;
                        
                        return props;
                    }
                };
            },
            
            // 注册自定义视图
            registerCustomView: (viewType, options) => {
                const customView = ArchiveDisabledViewFactory.createCustomView(options);
                registry.category("views").add(viewType, customView);
                return customView;
            },
            
            // 获取所有禁用归档视图
            getAllArchiveDisabledViews: () => {
                const views = registry.category("views").getAll();
                return Object.entries(views).filter(([name, view]) => 
                    name.includes('archive_disabled') || 
                    view.Controller === ArchiveDisabledListController
                );
            }
        };
        
        // 导出工厂
        __exports.ArchiveDisabledViewFactory = ArchiveDisabledViewFactory;
        __exports.enhancedArchiveDisabledListView = enhancedArchiveDisabledListView;
    }
};

// 应用禁用归档列表视图增强
ArchiveDisabledListViewEnhancer.enhanceArchiveDisabledListView();
```

## 技术特点

### 1. 配置组合
- 继承标准列表视图配置
- 替换专用控制器
- 最小化的配置变更

### 2. 视图注册
- 标准的视图注册机制
- 描述性的视图类型名称
- 全局可用的视图类型

### 3. 功能专用化
- 专门用于活动列表
- 禁用归档功能
- 保护重要数据

### 4. 简洁实现
- 最少的代码实现
- 清晰的依赖关系
- 易于理解和维护

## 设计模式

### 1. 组合模式 (Composition Pattern)
- 组合基础视图和专用控制器
- 创建新的功能组合

### 2. 工厂模式 (Factory Pattern)
- 通过配置创建专用视图
- 标准化的视图创建流程

### 3. 注册模式 (Registry Pattern)
- 注册到视图注册表
- 全局可用的视图类型

## 注意事项

1. **视图命名**: 使用描述性的视图类型名称
2. **功能一致性**: 确保视图功能与控制器一致
3. **配置完整性**: 保持视图配置的完整性
4. **向后兼容**: 确保与现有系统的兼容性

## 扩展建议

1. **多变体支持**: 创建不同场景的视图变体
2. **配置增强**: 添加更多的视图配置选项
3. **UI增强**: 添加禁用状态的视觉指示
4. **权限集成**: 与权限系统集成
5. **工厂模式**: 实现视图的工厂创建模式

该禁用归档列表视图为邮件系统提供了重要的专用视图类型，是活动管理和数据保护的核心组件。

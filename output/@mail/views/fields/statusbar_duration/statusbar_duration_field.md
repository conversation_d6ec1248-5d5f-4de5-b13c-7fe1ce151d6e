# StatusBar Duration Field - 状态栏持续时间字段

## 概述

`statusbar_duration_field.js` 是 Odoo 邮件系统的状态栏持续时间字段组件，专门用于在状态栏中显示每个状态阶段的持续时间信息。该组件基于Web框架的状态栏字段，集成了时间跟踪、持续时间格式化、多语言支持等核心功能，为邮件系统提供了完整的状态时间可视化和工作流程分析支持，是邮件系统业务流程监控和时间管理的重要组件。

## 文件信息
- **路径**: `/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js`
- **行数**: 45
- **模块**: `@mail/views/fields/statusbar_duration/statusbar_duration_field`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/dates'                           // 日期本地化工具
'@web/core/l10n/translation'                     // 翻译工具
'@web/core/registry'                             // 注册表
'@web/views/fields/statusbar/statusbar_field'    // 状态栏字段基类
```

## 核心功能

### 1. StatusBarDurationField 类定义

```javascript
const StatusBarDurationField = class StatusBarDurationField extends StatusBarField {
    static template = "mail.StatusBarDurationField";
    
    getAllItems() {
        const items = super.getAllItems();
        const durationTracking = this.props.record.data.duration_tracking || {};
        
        if (Object.keys(durationTracking).length) {
            for (const item of items) {
                const duration = durationTracking[item.value];
                if (duration > 0) {
                    item.shortTimeInStage = formatDuration(duration, false);
                    item.fullTimeInStage = formatDuration(duration, true);
                } else {
                    item.shortTimeInStage = 0;
                }
            }
        }
        
        return items;
    }
}
```

**StatusBarDurationField特性**:
- **继承扩展**: 继承StatusBarField的所有功能
- **时间跟踪**: 集成持续时间跟踪数据
- **格式化显示**: 提供短格式和完整格式的时间显示
- **模板渲染**: 使用专用的模板进行渲染

### 2. 持续时间处理

```javascript
getAllItems() {
    const items = super.getAllItems();
    const durationTracking = this.props.record.data.duration_tracking || {};
    
    if (Object.keys(durationTracking).length) {
        for (const item of items) {
            const duration = durationTracking[item.value];
            if (duration > 0) {
                item.shortTimeInStage = formatDuration(duration, false);
                item.fullTimeInStage = formatDuration(duration, true);
            } else {
                item.shortTimeInStage = 0;
            }
        }
    }
    
    return items;
}
```

**持续时间处理功能**:
- **数据获取**: 从记录数据中获取持续时间跟踪信息
- **时间格式化**: 使用formatDuration函数格式化时间显示
- **双格式支持**: 提供短格式和完整格式的时间显示
- **零值处理**: 正确处理零值和负值的持续时间

### 3. 字段配置

```javascript
const statusBarDurationField = {
    ...statusBarField,
    component: StatusBarDurationField,
    displayName: _t("Status with time"),
    supportedTypes: ["many2one"],
    fieldDependencies: [{ name: "duration_tracking", type: "JSON" }],
};
```

**字段配置功能**:
- **基础继承**: 继承标准状态栏字段的所有配置
- **组件指定**: 指定使用StatusBarDurationField组件
- **显示名称**: 提供本地化的显示名称
- **类型支持**: 支持many2one字段类型
- **依赖声明**: 声明对duration_tracking字段的依赖

### 4. 注册机制

```javascript
registry.category("fields").add("statusbar_duration", statusBarDurationField);
```

**注册功能**:
- **字段注册**: 将字段注册到字段注册表
- **类型标识**: 使用"statusbar_duration"作为字段类型标识
- **全局可用**: 使字段在整个系统中可用
- **配置集成**: 与字段配置系统集成

## 使用场景

### 1. 状态栏持续时间字段增强

```javascript
// 状态栏持续时间字段增强功能
const StatusBarDurationFieldEnhancer = {
    enhanceStatusBarDurationField: () => {
        const EnhancedStatusBarDurationField = class extends StatusBarDurationField {
            constructor() {
                super();
                
                // 增强的配置选项
                this.enhancedConfig = {
                    showAverageTime: false,
                    showTotalTime: false,
                    enableTimeComparison: false,
                    enableTimeAlerts: false,
                    customTimeFormats: new Map(),
                    timeThresholds: {
                        warning: null,
                        critical: null
                    },
                    animationEnabled: true,
                    tooltipEnabled: true
                };
                
                // 时间统计数据
                this.timeStatistics = {
                    averageTimes: new Map(),
                    totalTimes: new Map(),
                    stageHistory: [],
                    lastUpdated: null
                };
                
                // 事件监听器
                this.eventListeners = new Map();
                
                // 初始化增强功能
                this.initializeEnhancements();
            }
            
            // 初始化增强功能
            initializeEnhancements() {
                // 设置时间统计
                this.setupTimeStatistics();
                
                // 设置事件监听
                this.setupEventListeners();
                
                // 设置动画效果
                this.setupAnimations();
            }
            
            // 增强的获取所有项目
            getAllItems() {
                try {
                    // 获取基础项目
                    const items = super.getAllItems();
                    
                    // 获取持续时间跟踪数据
                    const durationTracking = this.props.record.data.duration_tracking || {};
                    const currentStage = this.props.record.data[this.props.name];
                    
                    if (Object.keys(durationTracking).length) {
                        // 计算统计数据
                        this.calculateTimeStatistics(durationTracking);
                        
                        // 增强每个项目
                        for (const item of items) {
                            this.enhanceItem(item, durationTracking, currentStage);
                        }
                        
                        // 添加总体统计
                        if (this.enhancedConfig.showTotalTime) {
                            this.addTotalTimeInfo(items, durationTracking);
                        }
                    }
                    
                    return items;
                } catch (error) {
                    console.error('获取增强项目失败:', error);
                    return super.getAllItems();
                }
            }
            
            // 增强单个项目
            enhanceItem(item, durationTracking, currentStage) {
                try {
                    const duration = durationTracking[item.value] || 0;
                    
                    // 基础时间信息
                    if (duration > 0) {
                        item.shortTimeInStage = this.formatDuration(duration, false);
                        item.fullTimeInStage = this.formatDuration(duration, true);
                        item.rawDuration = duration;
                    } else {
                        item.shortTimeInStage = 0;
                        item.rawDuration = 0;
                    }
                    
                    // 增强信息
                    item.isCurrentStage = currentStage && item.value === currentStage[0];
                    item.timeStatus = this.getTimeStatus(duration, item.value);
                    item.progressPercentage = this.calculateProgressPercentage(duration, item.value);
                    
                    // 平均时间比较
                    if (this.enhancedConfig.enableTimeComparison) {
                        const averageTime = this.timeStatistics.averageTimes.get(item.value);
                        if (averageTime && duration > 0) {
                            item.comparedToAverage = this.compareToAverage(duration, averageTime);
                        }
                    }
                    
                    // 时间警告
                    if (this.enhancedConfig.enableTimeAlerts) {
                        item.timeAlert = this.checkTimeAlert(duration, item.value);
                    }
                    
                    // 工具提示信息
                    if (this.enhancedConfig.tooltipEnabled) {
                        item.tooltipInfo = this.generateTooltipInfo(item, duration);
                    }
                    
                    // CSS类名
                    item.cssClasses = this.generateCssClasses(item);
                    
                } catch (error) {
                    console.error('增强项目失败:', error, item);
                }
            }
            
            // 自定义时间格式化
            formatDuration(duration, full = false) {
                try {
                    // 检查自定义格式
                    const customFormat = this.enhancedConfig.customTimeFormats.get(
                        full ? 'full' : 'short'
                    );
                    
                    if (customFormat) {
                        return customFormat(duration);
                    }
                    
                    // 使用默认格式化
                    return formatDuration(duration, full);
                } catch (error) {
                    console.error('格式化持续时间失败:', error);
                    return formatDuration(duration, full);
                }
            }
            
            // 获取时间状态
            getTimeStatus(duration, stageValue) {
                try {
                    if (duration <= 0) {
                        return 'not-started';
                    }
                    
                    const thresholds = this.enhancedConfig.timeThresholds;
                    
                    if (thresholds.critical && duration >= thresholds.critical) {
                        return 'critical';
                    }
                    
                    if (thresholds.warning && duration >= thresholds.warning) {
                        return 'warning';
                    }
                    
                    return 'normal';
                } catch (error) {
                    console.error('获取时间状态失败:', error);
                    return 'normal';
                }
            }
            
            // 计算进度百分比
            calculateProgressPercentage(duration, stageValue) {
                try {
                    if (duration <= 0) {
                        return 0;
                    }
                    
                    const averageTime = this.timeStatistics.averageTimes.get(stageValue);
                    if (!averageTime || averageTime <= 0) {
                        return 50; // 默认50%
                    }
                    
                    const percentage = Math.min((duration / averageTime) * 100, 200);
                    return Math.round(percentage);
                } catch (error) {
                    console.error('计算进度百分比失败:', error);
                    return 0;
                }
            }
            
            // 与平均时间比较
            compareToAverage(duration, averageTime) {
                try {
                    if (!averageTime || averageTime <= 0) {
                        return null;
                    }
                    
                    const ratio = duration / averageTime;
                    
                    if (ratio > 1.5) {
                        return { status: 'much-slower', ratio: ratio };
                    } else if (ratio > 1.2) {
                        return { status: 'slower', ratio: ratio };
                    } else if (ratio < 0.8) {
                        return { status: 'faster', ratio: ratio };
                    } else if (ratio < 0.5) {
                        return { status: 'much-faster', ratio: ratio };
                    } else {
                        return { status: 'normal', ratio: ratio };
                    }
                } catch (error) {
                    console.error('比较平均时间失败:', error);
                    return null;
                }
            }
            
            // 检查时间警告
            checkTimeAlert(duration, stageValue) {
                try {
                    const alerts = [];
                    
                    // 阈值警告
                    const thresholds = this.enhancedConfig.timeThresholds;
                    if (thresholds.critical && duration >= thresholds.critical) {
                        alerts.push({
                            type: 'critical',
                            message: `阶段时间已超过临界值 (${this.formatDuration(thresholds.critical)})`
                        });
                    } else if (thresholds.warning && duration >= thresholds.warning) {
                        alerts.push({
                            type: 'warning',
                            message: `阶段时间接近警告值 (${this.formatDuration(thresholds.warning)})`
                        });
                    }
                    
                    // 平均时间警告
                    const averageTime = this.timeStatistics.averageTimes.get(stageValue);
                    if (averageTime && duration > averageTime * 2) {
                        alerts.push({
                            type: 'performance',
                            message: `阶段时间超过平均值的2倍 (平均: ${this.formatDuration(averageTime)})`
                        });
                    }
                    
                    return alerts.length > 0 ? alerts : null;
                } catch (error) {
                    console.error('检查时间警告失败:', error);
                    return null;
                }
            }
            
            // 生成工具提示信息
            generateTooltipInfo(item, duration) {
                try {
                    const info = {
                        stageName: item.label,
                        duration: item.fullTimeInStage,
                        status: item.timeStatus
                    };
                    
                    // 添加比较信息
                    if (item.comparedToAverage) {
                        info.comparison = {
                            status: item.comparedToAverage.status,
                            ratio: item.comparedToAverage.ratio.toFixed(2)
                        };
                    }
                    
                    // 添加进度信息
                    if (item.progressPercentage) {
                        info.progress = `${item.progressPercentage}%`;
                    }
                    
                    // 添加警告信息
                    if (item.timeAlert) {
                        info.alerts = item.timeAlert.map(alert => alert.message);
                    }
                    
                    return info;
                } catch (error) {
                    console.error('生成工具提示信息失败:', error);
                    return null;
                }
            }
            
            // 生成CSS类名
            generateCssClasses(item) {
                try {
                    const classes = ['o_statusbar_duration_item'];
                    
                    // 状态类
                    if (item.isCurrentStage) {
                        classes.push('o_statusbar_current');
                    }
                    
                    // 时间状态类
                    if (item.timeStatus) {
                        classes.push(`o_time_status_${item.timeStatus}`);
                    }
                    
                    // 比较状态类
                    if (item.comparedToAverage) {
                        classes.push(`o_time_${item.comparedToAverage.status}`);
                    }
                    
                    // 警告类
                    if (item.timeAlert) {
                        classes.push('o_time_alert');
                        const criticalAlert = item.timeAlert.find(alert => alert.type === 'critical');
                        if (criticalAlert) {
                            classes.push('o_time_critical');
                        }
                    }
                    
                    return classes.join(' ');
                } catch (error) {
                    console.error('生成CSS类名失败:', error);
                    return 'o_statusbar_duration_item';
                }
            }
            
            // 计算时间统计
            calculateTimeStatistics(durationTracking) {
                try {
                    // 更新平均时间（这里简化处理，实际应该从历史数据计算）
                    for (const [stage, duration] of Object.entries(durationTracking)) {
                        if (duration > 0) {
                            // 简化的平均时间计算
                            const currentAverage = this.timeStatistics.averageTimes.get(stage) || duration;
                            const newAverage = (currentAverage + duration) / 2;
                            this.timeStatistics.averageTimes.set(stage, newAverage);
                        }
                    }
                    
                    // 更新总时间
                    const totalTime = Object.values(durationTracking).reduce((sum, duration) => sum + (duration || 0), 0);
                    this.timeStatistics.totalTimes.set('overall', totalTime);
                    
                    // 更新时间戳
                    this.timeStatistics.lastUpdated = Date.now();
                } catch (error) {
                    console.error('计算时间统计失败:', error);
                }
            }
            
            // 添加总时间信息
            addTotalTimeInfo(items, durationTracking) {
                try {
                    const totalTime = Object.values(durationTracking).reduce((sum, duration) => sum + (duration || 0), 0);
                    
                    if (totalTime > 0) {
                        // 在项目列表中添加总时间信息
                        items.totalTimeInfo = {
                            totalDuration: totalTime,
                            formattedTotal: this.formatDuration(totalTime, true),
                            averagePerStage: totalTime / items.length,
                            completedStages: Object.values(durationTracking).filter(d => d > 0).length
                        };
                    }
                } catch (error) {
                    console.error('添加总时间信息失败:', error);
                }
            }
            
            // 设置动画效果
            setupAnimations() {
                if (this.enhancedConfig.animationEnabled) {
                    // 这里可以添加动画相关的设置
                    // 例如：进度条动画、状态变更动画等
                }
            }
            
            // 事件监听器
            addEventListener(event, listener) {
                if (!this.eventListeners.has(event)) {
                    this.eventListeners.set(event, new Set());
                }
                this.eventListeners.get(event).add(listener);
            }
            
            triggerEvent(event, data) {
                const listeners = this.eventListeners.get(event);
                if (listeners) {
                    listeners.forEach(listener => {
                        try {
                            listener(data);
                        } catch (error) {
                            console.error('事件监听器错误:', error);
                        }
                    });
                }
            }
            
            // 获取统计信息
            getStatistics() {
                return {
                    timeStatistics: { ...this.timeStatistics },
                    config: { ...this.enhancedConfig },
                    lastUpdated: this.timeStatistics.lastUpdated
                };
            }
        };
        
        // 增强的字段配置
        const enhancedStatusBarDurationField = {
            ...statusBarDurationField,
            component: EnhancedStatusBarDurationField,
            displayName: _t("Enhanced Status with time"),
            
            // 添加额外的配置选项
            extractProps: ({ attrs, field }) => {
                const props = statusBarDurationField.extractProps({ attrs, field });
                
                // 添加增强配置
                if (attrs.show_average_time) {
                    props.showAverageTime = attrs.show_average_time;
                }
                
                if (attrs.show_total_time) {
                    props.showTotalTime = attrs.show_total_time;
                }
                
                if (attrs.time_thresholds) {
                    props.timeThresholds = JSON.parse(attrs.time_thresholds);
                }
                
                return props;
            }
        };
        
        // 替换原始字段
        registry.category("fields").add("statusbar_duration_enhanced", enhancedStatusBarDurationField);
    }
};

// 应用状态栏持续时间字段增强
StatusBarDurationFieldEnhancer.enhanceStatusBarDurationField();
```

## 技术特点

### 1. 继承扩展
- 基于标准状态栏字段的扩展
- 保持原有功能的完整性
- 添加时间跟踪特性

### 2. 时间处理
- 集成formatDuration函数
- 支持短格式和完整格式
- 正确处理零值和异常情况

### 3. 数据依赖
- 依赖duration_tracking JSON字段
- 自动处理数据缺失情况
- 与记录数据紧密集成

### 4. 本地化支持
- 使用翻译函数处理显示文本
- 支持多语言环境
- 时间格式的本地化

## 设计模式

### 1. 继承模式 (Inheritance Pattern)
- 继承StatusBarField的功能
- 扩展特定的时间显示能力

### 2. 装饰器模式 (Decorator Pattern)
- 为状态栏项目添加时间信息
- 不改变原有结构的情况下增强功能

### 3. 配置模式 (Configuration Pattern)
- 通过字段配置定义行为
- 支持依赖声明和类型限制

## 注意事项

1. **数据完整性**: 确保duration_tracking数据的完整性和正确性
2. **性能考虑**: 避免在大量状态项目时的性能问题
3. **时间格式**: 确保时间格式化的一致性和可读性
4. **错误处理**: 处理数据缺失或格式错误的情况

## 扩展建议

1. **统计分析**: 添加时间统计和分析功能
2. **可视化**: 提供时间进度的可视化显示
3. **警告系统**: 实现时间阈值警告机制
4. **历史跟踪**: 支持历史时间数据的查看
5. **自定义格式**: 支持自定义时间显示格式

该状态栏持续时间字段为邮件系统提供了重要的时间跟踪和可视化功能，是业务流程监控和时间管理的核心组件。

# Thread Icon Patch - 线程图标补丁

## 概述

`thread_icon_patch.js` 实现了对 Odoo 讨论应用线程图标组件的补丁扩展，专门添加了输入状态显示功能。该补丁通过Odoo的补丁机制扩展了ThreadIcon组件，注册了Typing组件，为线程图标提供了输入状态的可视化显示支持，是讨论应用输入状态功能在线程图标中的集成组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/typing/common/thread_icon_patch.js`
- **行数**: 18
- **模块**: `@mail/discuss/typing/common/thread_icon_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_icon'        // 线程图标组件
'@mail/discuss/typing/common/typing'   // 输入状态组件
'@web/core/utils/patch'                // 补丁工具
```

## 补丁定义

### ThreadIcon 补丁

```javascript
patch(ThreadIcon, {
    components: { ...ThreadIcon.components, Typing },
});
```

**补丁特性**:
- 扩展线程图标组件功能
- 注册Typing组件
- 保持原有组件结构
- 支持输入状态显示

## 核心功能

### 1. 组件注册

```javascript
patch(ThreadIcon, {
    components: { ...ThreadIcon.components, Typing },
});
```

**注册功能**:
- **组件扩展**: 将Typing组件添加到ThreadIcon的子组件列表
- **功能集成**: 集成输入状态显示功能
- **无缝集成**: 与现有线程图标框架无缝集成
- **模块化设计**: 保持组件的模块化结构

## 使用场景

### 1. 线程图标输入状态增强

```javascript
// 线程图标输入状态增强功能
const ThreadIconTypingPatchEnhancer = {
    enhanceThreadIconTypingPatch: () => {
        const EnhancedThreadIconPatch = {
            components: { 
                ...ThreadIcon.components, 
                Typing,
                TypingIndicator,
                TypingAnimation,
                TypingCounter
            },
            
            // 增强的属性
            props: {
                ...ThreadIcon.props,
                showTypingIndicator: { type: Boolean, optional: true },
                typingAnimationType: { type: String, optional: true },
                maxTypingUsers: { type: Number, optional: true }
            },
            
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.typingState = useState({
                    isVisible: false,
                    typingUsers: [],
                    animationType: this.props.typingAnimationType || 'dots',
                    maxUsers: this.props.maxTypingUsers || 3,
                    showCounter: false,
                    lastUpdate: null
                });
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.userService = useService("user");
                
                // 设置增强功能
                this.setupTypingEnhancements();
            },
            
            // 设置输入状态增强功能
            setupTypingEnhancements() {
                // 设置输入状态监听
                this.setupTypingStateListener();
                
                // 设置动画控制
                this.setupTypingAnimation();
                
                // 设置用户过滤
                this.setupTypingUserFilter();
                
                // 设置显示控制
                this.setupTypingDisplayControl();
            },
            
            // 设置输入状态监听
            setupTypingStateListener() {
                // 监听线程输入状态变化
                this.env.bus.addEventListener('thread:typing_changed', (event) => {
                    this.handleTypingChanged(event.detail);
                });
                
                // 监听用户输入状态
                this.env.bus.addEventListener('user:typing_status', (event) => {
                    this.handleUserTypingStatus(event.detail);
                });
                
                // 清理监听器
                onWillUnmount(() => {
                    this.env.bus.removeEventListener('thread:typing_changed', this.handleTypingChanged);
                    this.env.bus.removeEventListener('user:typing_status', this.handleUserTypingStatus);
                });
            },
            
            // 处理输入状态变化
            handleTypingChanged(detail) {
                try {
                    const { threadId, typingUsers } = detail;
                    
                    // 检查是否为当前线程
                    if (this.props.thread?.id !== threadId) {
                        return;
                    }
                    
                    // 过滤当前用户
                    const filteredUsers = this.filterTypingUsers(typingUsers);
                    
                    // 更新状态
                    this.updateTypingState(filteredUsers);
                    
                    // 触发动画
                    this.triggerTypingAnimation();
                    
                } catch (error) {
                    console.error('处理输入状态变化失败:', error);
                }
            },
            
            // 处理用户输入状态
            handleUserTypingStatus(detail) {
                try {
                    const { userId, isTyping, threadId } = detail;
                    
                    // 检查是否为当前线程
                    if (this.props.thread?.id !== threadId) {
                        return;
                    }
                    
                    // 更新用户输入状态
                    this.updateUserTypingStatus(userId, isTyping);
                    
                } catch (error) {
                    console.error('处理用户输入状态失败:', error);
                }
            },
            
            // 过滤输入用户
            filterTypingUsers(typingUsers) {
                const currentUserId = this.userService.userId;
                
                return typingUsers.filter(user => {
                    // 排除当前用户
                    if (user.id === currentUserId) {
                        return false;
                    }
                    
                    // 排除已离线用户
                    if (!user.isOnline) {
                        return false;
                    }
                    
                    // 排除被屏蔽用户
                    if (user.isBlocked) {
                        return false;
                    }
                    
                    return true;
                });
            },
            
            // 更新输入状态
            updateTypingState(typingUsers) {
                try {
                    this.typingState.typingUsers = typingUsers;
                    this.typingState.isVisible = typingUsers.length > 0;
                    this.typingState.showCounter = typingUsers.length > this.typingState.maxUsers;
                    this.typingState.lastUpdate = Date.now();
                    
                    // 记录状态更新
                    this.recordTypingStateUpdate(typingUsers);
                    
                } catch (error) {
                    console.error('更新输入状态失败:', error);
                }
            },
            
            // 更新用户输入状态
            updateUserTypingStatus(userId, isTyping) {
                try {
                    const users = [...this.typingState.typingUsers];
                    const userIndex = users.findIndex(user => user.id === userId);
                    
                    if (isTyping) {
                        // 添加输入用户
                        if (userIndex === -1) {
                            const user = this.getUserById(userId);
                            if (user) {
                                users.push(user);
                            }
                        }
                    } else {
                        // 移除输入用户
                        if (userIndex !== -1) {
                            users.splice(userIndex, 1);
                        }
                    }
                    
                    // 更新状态
                    this.updateTypingState(users);
                    
                } catch (error) {
                    console.error('更新用户输入状态失败:', error);
                }
            },
            
            // 获取用户信息
            getUserById(userId) {
                try {
                    // 从线程成员中查找
                    const member = this.props.thread?.members?.find(m => m.persona?.id === userId);
                    if (member) {
                        return {
                            id: userId,
                            name: member.persona.name,
                            avatar: member.persona.avatar,
                            isOnline: member.persona.isOnline,
                            isBlocked: false
                        };
                    }
                    
                    // 从存储中查找
                    const user = this.store.users?.get(userId);
                    if (user) {
                        return {
                            id: userId,
                            name: user.name,
                            avatar: user.avatar,
                            isOnline: user.isOnline,
                            isBlocked: user.isBlocked
                        };
                    }
                    
                    return null;
                } catch (error) {
                    console.error('获取用户信息失败:', error);
                    return null;
                }
            },
            
            // 触发输入动画
            triggerTypingAnimation() {
                try {
                    const element = this.el?.querySelector('.o-typing-indicator');
                    if (element) {
                        // 添加动画类
                        element.classList.add('o-typing-animation');
                        
                        // 移除动画类
                        setTimeout(() => {
                            element.classList.remove('o-typing-animation');
                        }, 300);
                    }
                } catch (error) {
                    console.warn('触发输入动画失败:', error);
                }
            },
            
            // 获取输入状态文本
            getTypingStatusText() {
                const users = this.typingState.typingUsers;
                const count = users.length;
                
                if (count === 0) {
                    return '';
                }
                
                if (count === 1) {
                    return `${users[0].name} 正在输入...`;
                }
                
                if (count === 2) {
                    return `${users[0].name} 和 ${users[1].name} 正在输入...`;
                }
                
                if (count <= this.typingState.maxUsers) {
                    const names = users.slice(0, -1).map(u => u.name).join(', ');
                    return `${names} 和 ${users[count - 1].name} 正在输入...`;
                }
                
                return `${count} 人正在输入...`;
            },
            
            // 获取输入用户头像
            getTypingUserAvatars() {
                const maxAvatars = 3;
                const users = this.typingState.typingUsers.slice(0, maxAvatars);
                
                return users.map(user => ({
                    id: user.id,
                    name: user.name,
                    avatar: user.avatar || '/web/static/img/user_menu_avatar.png'
                }));
            },
            
            // 检查是否显示输入指示器
            shouldShowTypingIndicator() {
                if (!this.props.showTypingIndicator) {
                    return false;
                }
                
                if (!this.typingState.isVisible) {
                    return false;
                }
                
                if (this.typingState.typingUsers.length === 0) {
                    return false;
                }
                
                return true;
            },
            
            // 获取动画类型
            getAnimationType() {
                return this.typingState.animationType;
            },
            
            // 记录状态更新
            recordTypingStateUpdate(typingUsers) {
                try {
                    const updates = JSON.parse(
                        localStorage.getItem('typing_state_updates') || '[]'
                    );
                    
                    updates.push({
                        threadId: this.props.thread?.id,
                        userCount: typingUsers.length,
                        userIds: typingUsers.map(u => u.id),
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个更新
                    if (updates.length > 100) {
                        updates.splice(0, updates.length - 100);
                    }
                    
                    localStorage.setItem('typing_state_updates', JSON.stringify(updates));
                } catch (error) {
                    console.warn('记录状态更新失败:', error);
                }
            },
            
            // 获取输入状态统计
            getTypingStatistics() {
                try {
                    const updates = JSON.parse(
                        localStorage.getItem('typing_state_updates') || '[]'
                    );
                    
                    return {
                        currentTypingUsers: this.typingState.typingUsers.length,
                        isVisible: this.typingState.isVisible,
                        showCounter: this.typingState.showCounter,
                        animationType: this.typingState.animationType,
                        maxUsers: this.typingState.maxUsers,
                        totalUpdates: updates.length,
                        lastUpdate: this.typingState.lastUpdate
                    };
                } catch (error) {
                    return {
                        currentTypingUsers: 0,
                        isVisible: false,
                        showCounter: false,
                        animationType: 'dots',
                        maxUsers: 3,
                        totalUpdates: 0,
                        lastUpdate: null
                    };
                }
            }
        };
        
        patch(ThreadIcon, EnhancedThreadIconPatch);
    }
};

// 应用线程图标输入状态补丁增强
ThreadIconTypingPatchEnhancer.enhanceThreadIconTypingPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 组件集成
- 无缝组件注册
- 模块化设计
- 清晰的组件边界

### 3. 可视化显示
- 输入状态的可视化
- 线程图标的功能扩展
- 用户友好的界面

### 4. 简洁设计
- 最小化的代码实现
- 专注于核心功能
- 易于维护和扩展

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 组合模式 (Composite Pattern)
- 组件的组合和管理
- 统一的组件接口

### 3. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 不改变原有结构

## 注意事项

1. **组件兼容性**: 确保新组件与线程图标框架的兼容性
2. **性能影响**: 避免过多组件影响渲染性能
3. **状态同步**: 保持输入状态的同步显示
4. **用户体验**: 提供直观的输入状态指示

## 扩展建议

1. **动画效果**: 添加更丰富的输入状态动画
2. **自定义样式**: 允许自定义输入状态的显示样式
3. **多状态支持**: 支持更多类型的用户状态显示
4. **交互功能**: 添加点击输入状态的交互功能
5. **配置选项**: 提供更多的配置选项

该补丁为线程图标提供了重要的输入状态显示功能，是输入状态功能在线程图标中的简洁集成。

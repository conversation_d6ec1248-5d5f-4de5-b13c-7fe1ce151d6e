# Typing - 输入状态组件

## 概述

`typing.js` 实现了 Odoo 讨论应用中的输入状态显示组件，提供了"正在输入"状态的可视化展示功能。该组件支持单个或多个用户的输入状态显示，集成了国际化文本、灵活的属性配置、智能的文本生成和响应式的状态更新等特性，为用户在讨论频道中提供了直观的输入状态反馈，是讨论应用输入状态功能的核心显示组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/typing/common/typing.js`
- **行数**: 49
- **模块**: `@mail/discuss/typing/common/typing`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/l10n/translation'   // 国际化
```

## 类型定义

### Props 类型

```javascript
/**
 * @typedef {Object} Props
 * @property {import("models").Thread} channel
 * @property {string} [size]
 * @property {boolean} [displayText]
 */
```

**属性说明**:
- **channel**: 讨论频道对象，包含输入状态信息
- **size**: 组件大小，可选参数
- **displayText**: 是否显示文本，可选参数

## 组件定义

### Typing 类

```javascript
const Typing = class Typing extends Component {
    static defaultProps = {
        size: "small",
        displayText: true,
    };
    static props = ["channel?", "size?", "displayText?", "member?"];
    static template = "discuss.Typing";
}
```

**组件特性**:
- 继承自OWL Component
- 使用专用模板
- 支持默认属性
- 灵活的属性配置

## 核心功能

### 1. 默认属性

```javascript
static defaultProps = {
    size: "small",
    displayText: true,
};
```

**默认属性功能**:
- **size**: 默认为"small"尺寸
- **displayText**: 默认显示文本
- **用户友好**: 提供合理的默认值
- **可覆盖**: 允许外部覆盖默认值

### 2. 属性定义

```javascript
static props = ["channel?", "size?", "displayText?", "member?"];
```

**属性定义功能**:
- **channel**: 可选的频道属性
- **size**: 可选的尺寸属性
- **displayText**: 可选的文本显示属性
- **member**: 可选的成员属性
- **灵活性**: 所有属性都是可选的

### 3. 文本生成

```javascript
get text() {
    const typingMemberNames = this.props.member
        ? [this.props.member.name]
        : this.props.channel.otherTypingMembers.map(({ name }) => name);
    if (typingMemberNames.length === 1) {
        return _t("%s is typing...", typingMemberNames[0]);
    }
    if (typingMemberNames.length === 2) {
        return _t("%(user1)s and %(user2)s are typing...", {
            user1: typingMemberNames[0],
            user2: typingMemberNames[1],
        });
    }
    return _t("%(user1)s, %(user2)s and more are typing...", {
        user1: typingMemberNames[0],
        user2: typingMemberNames[1],
    });
}
```

**文本生成功能**:
- **成员获取**: 从属性或频道获取输入成员
- **单用户**: 显示单个用户的输入状态
- **双用户**: 显示两个用户的输入状态
- **多用户**: 显示多个用户的输入状态
- **国际化**: 使用翻译函数支持多语言

## 使用场景

### 1. 输入状态组件增强

```javascript
// 输入状态组件增强功能
const TypingComponentEnhancer = {
    enhanceTypingComponent: () => {
        const EnhancedTyping = class extends Typing {
            static defaultProps = {
                ...Typing.defaultProps,
                showAvatars: false,
                maxUsers: 3,
                animationType: 'dots',
                showTimestamp: false,
                compactMode: false
            };
            
            static props = [
                ...Typing.props,
                "showAvatars?",
                "maxUsers?", 
                "animationType?",
                "showTimestamp?",
                "compactMode?"
            ];
            
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.typingState = useState({
                    isVisible: false,
                    lastUpdate: null,
                    animationFrame: null,
                    typingUsers: [],
                    displayMode: 'normal' // normal, compact, minimal
                });
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.userService = useService("user");
                
                // 设置增强功能
                this.setupTypingEnhancements();
            },
            
            // 设置输入状态增强功能
            setupTypingEnhancements() {
                // 设置动画控制
                this.setupTypingAnimation();
                
                // 设置用户头像
                this.setupUserAvatars();
                
                // 设置时间戳显示
                this.setupTimestampDisplay();
                
                // 设置响应式布局
                this.setupResponsiveLayout();
                
                // 设置状态监听
                this.setupStateListener();
            },
            
            // 增强的文本生成
            get text() {
                const typingMemberNames = this.getTypingMemberNames();
                const count = typingMemberNames.length;
                
                if (count === 0) {
                    return '';
                }
                
                // 紧凑模式
                if (this.props.compactMode) {
                    return this.getCompactText(typingMemberNames);
                }
                
                // 普通模式
                return this.getNormalText(typingMemberNames);
            },
            
            // 获取输入成员名称
            getTypingMemberNames() {
                if (this.props.member) {
                    return [this.props.member.name];
                }
                
                if (this.props.channel?.otherTypingMembers) {
                    return this.props.channel.otherTypingMembers
                        .slice(0, this.props.maxUsers)
                        .map(({ name }) => name);
                }
                
                return [];
            },
            
            // 获取紧凑文本
            getCompactText(names) {
                const count = names.length;
                
                if (count === 1) {
                    return _t("%s...", names[0]);
                }
                
                if (count <= this.props.maxUsers) {
                    return _t("%s users...", count);
                }
                
                return _t("%s+...", this.props.maxUsers);
            },
            
            // 获取普通文本
            getNormalText(names) {
                const count = names.length;
                
                if (count === 1) {
                    return _t("%s is typing...", names[0]);
                }
                
                if (count === 2) {
                    return _t("%(user1)s and %(user2)s are typing...", {
                        user1: names[0],
                        user2: names[1],
                    });
                }
                
                if (count <= this.props.maxUsers) {
                    const lastUser = names[count - 1];
                    const otherUsers = names.slice(0, -1).join(', ');
                    return _t("%(users)s and %(lastUser)s are typing...", {
                        users: otherUsers,
                        lastUser: lastUser
                    });
                }
                
                return _t("%(user1)s, %(user2)s and %(count)s more are typing...", {
                    user1: names[0],
                    user2: names[1],
                    count: count - 2
                });
            },
            
            // 获取用户头像
            get userAvatars() {
                if (!this.props.showAvatars) {
                    return [];
                }
                
                const members = this.props.member 
                    ? [this.props.member]
                    : this.props.channel?.otherTypingMembers || [];
                
                return members
                    .slice(0, this.props.maxUsers)
                    .map(member => ({
                        id: member.id,
                        name: member.name,
                        avatar: member.avatar || '/web/static/img/user_menu_avatar.png'
                    }));
            },
            
            // 获取时间戳
            get timestamp() {
                if (!this.props.showTimestamp || !this.typingState.lastUpdate) {
                    return '';
                }
                
                const now = Date.now();
                const diff = now - this.typingState.lastUpdate;
                
                if (diff < 60000) { // 小于1分钟
                    return _t('now');
                }
                
                if (diff < 3600000) { // 小于1小时
                    const minutes = Math.floor(diff / 60000);
                    return _t('%s min ago', minutes);
                }
                
                const hours = Math.floor(diff / 3600000);
                return _t('%s hr ago', hours);
            },
            
            // 获取动画类型
            get animationType() {
                return this.props.animationType || 'dots';
            },
            
            // 检查是否应该显示
            get shouldShow() {
                const names = this.getTypingMemberNames();
                return names.length > 0 && this.props.displayText;
            },
            
            // 获取CSS类
            get cssClasses() {
                const classes = ['o-discuss-Typing'];
                
                // 尺寸类
                classes.push(`o-discuss-Typing--${this.props.size}`);
                
                // 动画类
                classes.push(`o-discuss-Typing--${this.animationType}`);
                
                // 模式类
                if (this.props.compactMode) {
                    classes.push('o-discuss-Typing--compact');
                }
                
                if (this.props.showAvatars) {
                    classes.push('o-discuss-Typing--with-avatars');
                }
                
                return classes.join(' ');
            },
            
            // 设置动画控制
            setupTypingAnimation() {
                // 动画帧控制
                this.startAnimation = () => {
                    if (this.typingState.animationFrame) {
                        return;
                    }
                    
                    const animate = () => {
                        // 动画逻辑
                        this.updateAnimation();
                        
                        if (this.shouldShow) {
                            this.typingState.animationFrame = requestAnimationFrame(animate);
                        } else {
                            this.typingState.animationFrame = null;
                        }
                    };
                    
                    this.typingState.animationFrame = requestAnimationFrame(animate);
                };
                
                this.stopAnimation = () => {
                    if (this.typingState.animationFrame) {
                        cancelAnimationFrame(this.typingState.animationFrame);
                        this.typingState.animationFrame = null;
                    }
                };
                
                // 清理动画
                onWillUnmount(() => {
                    this.stopAnimation();
                });
            },
            
            // 更新动画
            updateAnimation() {
                try {
                    const element = this.el?.querySelector('.o-typing-animation');
                    if (element) {
                        // 更新动画状态
                        element.style.animationPlayState = this.shouldShow ? 'running' : 'paused';
                    }
                } catch (error) {
                    console.warn('更新动画失败:', error);
                }
            },
            
            // 设置状态监听
            setupStateListener() {
                // 监听属性变化
                this.env.bus.addEventListener('typing:state_changed', (event) => {
                    this.handleStateChanged(event.detail);
                });
                
                // 清理监听器
                onWillUnmount(() => {
                    this.env.bus.removeEventListener('typing:state_changed', this.handleStateChanged);
                });
            },
            
            // 处理状态变化
            handleStateChanged(detail) {
                try {
                    this.typingState.lastUpdate = Date.now();
                    this.typingState.isVisible = this.shouldShow;
                    
                    // 启动或停止动画
                    if (this.shouldShow) {
                        this.startAnimation();
                    } else {
                        this.stopAnimation();
                    }
                    
                    // 记录状态变化
                    this.recordStateChange(detail);
                    
                } catch (error) {
                    console.error('处理状态变化失败:', error);
                }
            },
            
            // 记录状态变化
            recordStateChange(detail) {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('typing_state_changes') || '[]'
                    );
                    
                    changes.push({
                        channelId: this.props.channel?.id,
                        memberId: this.props.member?.id,
                        userCount: this.getTypingMemberNames().length,
                        timestamp: Date.now(),
                        detail: detail
                    });
                    
                    // 保留最近100个变化
                    if (changes.length > 100) {
                        changes.splice(0, changes.length - 100);
                    }
                    
                    localStorage.setItem('typing_state_changes', JSON.stringify(changes));
                } catch (error) {
                    console.warn('记录状态变化失败:', error);
                }
            },
            
            // 获取组件统计
            getTypingComponentStatistics() {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('typing_state_changes') || '[]'
                    );
                    
                    return {
                        isVisible: this.typingState.isVisible,
                        shouldShow: this.shouldShow,
                        userCount: this.getTypingMemberNames().length,
                        showAvatars: this.props.showAvatars,
                        compactMode: this.props.compactMode,
                        animationType: this.animationType,
                        lastUpdate: this.typingState.lastUpdate,
                        totalStateChanges: changes.length,
                        cssClasses: this.cssClasses
                    };
                } catch (error) {
                    return {
                        isVisible: false,
                        shouldShow: false,
                        userCount: 0,
                        showAvatars: false,
                        compactMode: false,
                        animationType: 'dots',
                        lastUpdate: null,
                        totalStateChanges: 0,
                        cssClasses: 'o-discuss-Typing'
                    };
                }
            }
        };
        
        // 替换原始组件
        __exports.Typing = EnhancedTyping;
    }
};

// 应用输入状态组件增强
TypingComponentEnhancer.enhanceTypingComponent();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 类型定义完善
- 模板驱动渲染

### 2. 国际化支持
- 多语言文本支持
- 本地化用户体验
- 翻译函数集成

### 3. 灵活配置
- 可选属性支持
- 默认值设置
- 响应式属性

### 4. 智能文本
- 动态文本生成
- 用户数量感知
- 上下文相关显示

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同用户数量的显示策略
- 可配置的文本生成

### 3. 模板方法模式 (Template Method Pattern)
- 标准化的组件结构
- 可扩展的功能点

## 注意事项

1. **性能优化**: 避免频繁的文本重新计算
2. **用户体验**: 提供清晰和直观的状态显示
3. **国际化**: 确保所有文本都支持多语言
4. **响应式**: 适配不同的显示环境和尺寸

## 扩展建议

1. **动画效果**: 添加更丰富的输入状态动画
2. **用户头像**: 显示输入用户的头像
3. **时间戳**: 显示输入状态的时间信息
4. **自定义样式**: 允许自定义组件的显示样式
5. **交互功能**: 添加点击查看详情的功能

该组件为讨论应用提供了完整的输入状态显示功能，是用户了解其他人输入状态的重要界面组件。

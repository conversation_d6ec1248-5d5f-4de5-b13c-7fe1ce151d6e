# Composer Patch - 编辑器补丁

## 概述

`composer_patch.js` 实现了对 Odoo 讨论应用编辑器组件的补丁扩展，专门添加了输入状态检测和通知功能。该补丁通过Odoo的补丁机制扩展了Composer组件，集成了Typing组件、输入检测逻辑、防抖处理、命令识别和状态通知等特性，为用户在讨论频道中提供了实时的"正在输入"状态显示功能，是讨论应用输入状态功能的核心组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/typing/common/composer_patch.js`
- **行数**: 95
- **模块**: `@mail/discuss/typing/common/composer_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/composer'           // 编辑器组件
'@mail/discuss/typing/common/typing'   // 输入状态组件
'@web/core/network/rpc'                // RPC网络服务
'@web/core/browser/browser'            // 浏览器服务
'@web/core/registry'                   // 注册表
'@web/core/utils/patch'                // 补丁工具
'@web/core/utils/timing'               // 时间工具
```

## 常量定义

### 输入状态时间常量

```javascript
const SHORT_TYPING = 5000;   // 短输入状态时间 (5秒)
const LONG_TYPING = 50000;   // 长输入状态时间 (50秒)
```

**时间常量功能**:
- **SHORT_TYPING**: 用于防抖处理，5秒后停止输入状态
- **LONG_TYPING**: 用于长时间输入保护，50秒后重置状态
- **状态控制**: 控制输入状态的持续时间和更新频率

## 补丁定义

### 组件注册

```javascript
patch(Composer, {
    components: { ...Composer.components, Typing },
});
```

**组件注册功能**:
- 将Typing组件添加到Composer的子组件列表
- 扩展编辑器的功能范围
- 支持输入状态的显示

### Composer 原型补丁

```javascript
patch(Composer.prototype, {
    setup() {
        super.setup();
        this.typingNotified = false;
        this.stopTypingDebounced = useDebounced(this.stopTyping.bind(this), SHORT_TYPING);
    },
    // 其他方法...
});
```

**补丁特性**:
- 扩展编辑器组件功能
- 添加输入状态管理
- 集成防抖处理机制
- 保持原有功能完整性

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.typingNotified = false;
    this.stopTypingDebounced = useDebounced(this.stopTyping.bind(this), SHORT_TYPING);
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **状态标志**: 初始化输入通知状态为false
- **防抖处理**: 创建防抖的停止输入方法
- **时间控制**: 使用SHORT_TYPING作为防抖延迟

### 2. 输入状态通知

```javascript
notifyIsTyping(is_typing = true) {
    if (this.thread?.model === "discuss.channel" && this.thread.id > 0) {
        rpc(
            "/discuss/channel/notify_typing",
            {
                channel_id: this.thread.id,
                is_typing,
            },
            { silent: true }
        );
    }
}
```

**通知功能**:
- **条件检查**: 仅在讨论频道中发送通知
- **RPC调用**: 调用后端API通知输入状态
- **静默请求**: 使用静默模式避免错误提示
- **状态参数**: 传递输入状态布尔值

### 3. 输入检测

```javascript
detectTyping() {
    const value = this.props.composer.text;
    if (this.thread?.model === "discuss.channel" && value.startsWith("/")) {
        const [firstWord] = value.substring(1).split(/\s/);
        const command = commandRegistry.get(firstWord, false);
        if (
            value === "/" || // suggestions not yet started
            this.hasSuggestions ||
            (command &&
                (!command.channel_types ||
                    command.channel_types.includes(this.thread.channel_type)))
        ) {
            this.stopTyping();
            return;
        }
    }
    if (!this.typingNotified && value) {
        this.typingNotified = true;
        this.notifyIsTyping();
        browser.setTimeout(() => (this.typingNotified = false), LONG_TYPING);
    }
    this.stopTypingDebounced();
}
```

**检测功能**:
- **文本获取**: 获取编辑器当前文本内容
- **命令识别**: 检测是否为频道命令输入
- **命令过滤**: 对命令输入不发送输入状态
- **状态管理**: 管理输入通知状态
- **防抖调用**: 调用防抖的停止输入方法

### 4. 消息发送处理

```javascript
async sendMessage() {
    await super.sendMessage();
    this.stopTyping();
}
```

**发送处理功能**:
- **父类调用**: 执行原有的消息发送逻辑
- **状态清理**: 发送消息后停止输入状态
- **异步处理**: 支持异步消息发送

### 5. 停止输入

```javascript
stopTyping() {
    if (this.typingNotified) {
        this.typingNotified = false;
        this.notifyIsTyping(false);
    }
}
```

**停止功能**:
- **状态检查**: 检查是否已通知输入状态
- **状态重置**: 重置输入通知标志
- **通知停止**: 通知服务器停止输入状态

### 6. 表情符号处理

```javascript
addEmoji(str) {
    super.addEmoji(str);
    this.detectTyping();
}
```

**表情处理功能**:
- **父类调用**: 执行原有的表情添加逻辑
- **输入检测**: 添加表情后触发输入检测
- **状态更新**: 确保输入状态的及时更新

## 使用场景

### 1. 编辑器输入状态增强

```javascript
// 编辑器输入状态增强功能
const ComposerTypingPatchEnhancer = {
    enhanceComposerTypingPatch: () => {
        const EnhancedComposerPatch = {
            setup() {
                super.setup();
                
                // 原有的输入状态管理
                this.typingNotified = false;
                this.stopTypingDebounced = useDebounced(this.stopTyping.bind(this), SHORT_TYPING);
                
                // 增强的输入状态管理
                this.typingState = {
                    isTyping: false,
                    lastTypingTime: null,
                    typingDuration: 0,
                    typingCount: 0,
                    pauseCount: 0,
                    averageTypingSpeed: 0,
                    lastTextLength: 0
                };
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.userService = useService("user");
                
                // 设置增强功能
                this.setupTypingEnhancements();
            },
            
            // 设置输入增强功能
            setupTypingEnhancements() {
                // 设置输入统计
                this.setupTypingStatistics();
                
                // 设置输入模式检测
                this.setupTypingPatternDetection();
                
                // 设置智能通知
                this.setupSmartNotification();
                
                // 设置输入历史
                this.setupTypingHistory();
            },
            
            // 增强的输入检测
            detectTyping() {
                const value = this.props.composer.text;
                const currentTime = Date.now();
                
                // 更新输入统计
                this.updateTypingStatistics(value, currentTime);
                
                // 原有的命令检测逻辑
                if (this.thread?.model === "discuss.channel" && value.startsWith("/")) {
                    const [firstWord] = value.substring(1).split(/\s/);
                    const command = commandRegistry.get(firstWord, false);
                    if (
                        value === "/" ||
                        this.hasSuggestions ||
                        (command &&
                            (!command.channel_types ||
                                command.channel_types.includes(this.thread.channel_type)))
                    ) {
                        this.stopTyping();
                        return;
                    }
                }
                
                // 增强的输入状态管理
                if (!this.typingNotified && value) {
                    // 检查是否应该发送通知
                    if (this.shouldNotifyTyping(value)) {
                        this.typingNotified = true;
                        this.typingState.isTyping = true;
                        this.typingState.lastTypingTime = currentTime;
                        this.typingState.typingCount++;
                        
                        this.notifyIsTyping();
                        
                        // 设置长时间输入保护
                        browser.setTimeout(() => {
                            if (this.typingNotified) {
                                this.typingNotified = false;
                                this.typingState.isTyping = false;
                            }
                        }, LONG_TYPING);
                    }
                }
                
                // 调用防抖停止方法
                this.stopTypingDebounced();
            },
            
            // 检查是否应该通知输入
            shouldNotifyTyping(value) {
                // 检查文本长度变化
                const textLengthChange = Math.abs(value.length - this.typingState.lastTextLength);
                
                // 如果变化太小，可能是光标移动，不通知
                if (textLengthChange < 2 && value.length > 10) {
                    return false;
                }
                
                // 检查输入频率
                const timeSinceLastTyping = Date.now() - (this.typingState.lastTypingTime || 0);
                if (timeSinceLastTyping < 1000) { // 1秒内的频繁输入
                    return false;
                }
                
                // 检查是否为有意义的输入
                if (this.isSpamTyping(value)) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否为垃圾输入
            isSpamTyping(value) {
                // 检查重复字符
                const repeatedChars = value.match(/(.)\1{5,}/g);
                if (repeatedChars) {
                    return true;
                }
                
                // 检查随机字符串
                const randomPattern = /^[a-zA-Z]{1,3}$/;
                if (randomPattern.test(value.trim())) {
                    return true;
                }
                
                return false;
            },
            
            // 更新输入统计
            updateTypingStatistics(value, currentTime) {
                try {
                    const textLength = value.length;
                    const lastLength = this.typingState.lastTextLength;
                    const lastTime = this.typingState.lastTypingTime;
                    
                    if (lastTime && textLength > lastLength) {
                        // 计算输入速度 (字符/分钟)
                        const timeDiff = currentTime - lastTime;
                        const charDiff = textLength - lastLength;
                        const speed = (charDiff / timeDiff) * 60000; // 转换为每分钟字符数
                        
                        // 更新平均输入速度
                        if (this.typingState.averageTypingSpeed === 0) {
                            this.typingState.averageTypingSpeed = speed;
                        } else {
                            this.typingState.averageTypingSpeed = 
                                (this.typingState.averageTypingSpeed + speed) / 2;
                        }
                    }
                    
                    this.typingState.lastTextLength = textLength;
                    
                } catch (error) {
                    console.warn('更新输入统计失败:', error);
                }
            },
            
            // 增强的输入状态通知
            notifyIsTyping(is_typing = true) {
                try {
                    // 检查权限
                    if (!this.hasTypingPermission()) {
                        return;
                    }
                    
                    // 检查频率限制
                    if (!this.checkNotificationRate()) {
                        return;
                    }
                    
                    // 调用原有逻辑
                    if (this.thread?.model === "discuss.channel" && this.thread.id > 0) {
                        rpc(
                            "/discuss/channel/notify_typing",
                            {
                                channel_id: this.thread.id,
                                is_typing,
                                typing_speed: this.typingState.averageTypingSpeed,
                                user_id: this.userService.userId
                            },
                            { silent: true }
                        );
                        
                        // 记录通知
                        this.recordTypingNotification(is_typing);
                    }
                    
                } catch (error) {
                    console.error('发送输入状态通知失败:', error);
                }
            },
            
            // 检查输入权限
            hasTypingPermission() {
                const user = this.userService.user;
                const thread = this.thread;
                
                if (!user || !thread) {
                    return false;
                }
                
                // 检查是否为频道成员
                if (!thread.hasSelfAsMember) {
                    return false;
                }
                
                // 检查频道设置
                if (thread.typingDisabled) {
                    return false;
                }
                
                return true;
            },
            
            // 检查通知频率
            checkNotificationRate() {
                const now = Date.now();
                const lastNotification = this.lastTypingNotification || 0;
                const minInterval = 2000; // 最小2秒间隔
                
                if (now - lastNotification < minInterval) {
                    return false;
                }
                
                this.lastTypingNotification = now;
                return true;
            },
            
            // 增强的停止输入
            stopTyping() {
                try {
                    if (this.typingNotified) {
                        this.typingNotified = false;
                        this.typingState.isTyping = false;
                        
                        // 计算输入持续时间
                        if (this.typingState.lastTypingTime) {
                            const duration = Date.now() - this.typingState.lastTypingTime;
                            this.typingState.typingDuration += duration;
                        }
                        
                        this.notifyIsTyping(false);
                        
                        // 记录停止输入
                        this.recordTypingStop();
                    }
                } catch (error) {
                    console.error('停止输入状态失败:', error);
                }
            },
            
            // 增强的消息发送处理
            async sendMessage() {
                try {
                    // 记录发送前的输入状态
                    this.recordMessageSent();
                    
                    // 调用父类方法
                    await super.sendMessage();
                    
                    // 停止输入状态
                    this.stopTyping();
                    
                    // 重置输入统计
                    this.resetTypingStatistics();
                    
                } catch (error) {
                    console.error('发送消息失败:', error);
                    throw error;
                }
            },
            
            // 增强的表情符号处理
            addEmoji(str) {
                try {
                    // 调用父类方法
                    super.addEmoji(str);
                    
                    // 记录表情添加
                    this.recordEmojiAdded(str);
                    
                    // 触发输入检测
                    this.detectTyping();
                    
                } catch (error) {
                    console.error('添加表情符号失败:', error);
                }
            },
            
            // 记录输入通知
            recordTypingNotification(is_typing) {
                try {
                    const notifications = JSON.parse(
                        localStorage.getItem('typing_notifications') || '[]'
                    );
                    
                    notifications.push({
                        threadId: this.thread.id,
                        isTyping: is_typing,
                        timestamp: Date.now(),
                        typingSpeed: this.typingState.averageTypingSpeed
                    });
                    
                    // 保留最近100个通知
                    if (notifications.length > 100) {
                        notifications.splice(0, notifications.length - 100);
                    }
                    
                    localStorage.setItem('typing_notifications', JSON.stringify(notifications));
                } catch (error) {
                    console.warn('记录输入通知失败:', error);
                }
            },
            
            // 记录停止输入
            recordTypingStop() {
                try {
                    const stops = JSON.parse(
                        localStorage.getItem('typing_stops') || '[]'
                    );
                    
                    stops.push({
                        threadId: this.thread.id,
                        duration: this.typingState.typingDuration,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (stops.length > 50) {
                        stops.splice(0, stops.length - 50);
                    }
                    
                    localStorage.setItem('typing_stops', JSON.stringify(stops));
                } catch (error) {
                    console.warn('记录停止输入失败:', error);
                }
            },
            
            // 记录消息发送
            recordMessageSent() {
                try {
                    const messages = JSON.parse(
                        localStorage.getItem('typing_messages_sent') || '[]'
                    );
                    
                    messages.push({
                        threadId: this.thread.id,
                        typingDuration: this.typingState.typingDuration,
                        typingCount: this.typingState.typingCount,
                        averageSpeed: this.typingState.averageTypingSpeed,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (messages.length > 100) {
                        messages.splice(0, messages.length - 100);
                    }
                    
                    localStorage.setItem('typing_messages_sent', JSON.stringify(messages));
                } catch (error) {
                    console.warn('记录消息发送失败:', error);
                }
            },
            
            // 记录表情添加
            recordEmojiAdded(emoji) {
                try {
                    const emojis = JSON.parse(
                        localStorage.getItem('typing_emojis_added') || '[]'
                    );
                    
                    emojis.push({
                        emoji: emoji,
                        threadId: this.thread.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (emojis.length > 50) {
                        emojis.splice(0, emojis.length - 50);
                    }
                    
                    localStorage.setItem('typing_emojis_added', JSON.stringify(emojis));
                } catch (error) {
                    console.warn('记录表情添加失败:', error);
                }
            },
            
            // 重置输入统计
            resetTypingStatistics() {
                this.typingState = {
                    isTyping: false,
                    lastTypingTime: null,
                    typingDuration: 0,
                    typingCount: 0,
                    pauseCount: 0,
                    averageTypingSpeed: 0,
                    lastTextLength: 0
                };
            },
            
            // 获取输入统计
            getTypingStatistics() {
                return {
                    currentState: { ...this.typingState },
                    isCurrentlyTyping: this.typingNotified,
                    notifications: JSON.parse(localStorage.getItem('typing_notifications') || '[]').length,
                    stops: JSON.parse(localStorage.getItem('typing_stops') || '[]').length,
                    messagesSent: JSON.parse(localStorage.getItem('typing_messages_sent') || '[]').length,
                    emojisAdded: JSON.parse(localStorage.getItem('typing_emojis_added') || '[]').length
                };
            }
        };
        
        patch(Composer.prototype, EnhancedComposerPatch);
    }
};

// 应用编辑器输入状态补丁增强
ComposerTypingPatchEnhancer.enhanceComposerTypingPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 防抖处理
- 智能的输入状态管理
- 避免频繁的网络请求
- 优化用户体验

### 3. 命令识别
- 智能识别频道命令
- 避免命令输入时的状态通知
- 支持命令建议功能

### 4. 状态管理
- 完整的输入状态跟踪
- 时间控制机制
- 自动状态重置

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 输入事件的监听和响应
- 状态变化的通知

### 3. 策略模式 (Strategy Pattern)
- 不同输入情况的处理策略
- 可配置的行为模式

## 注意事项

1. **网络优化**: 合理控制输入状态通知的频率
2. **用户体验**: 避免过于频繁的状态更新
3. **命令处理**: 正确识别和处理频道命令
4. **状态一致性**: 确保输入状态的准确性

## 扩展建议

1. **智能检测**: 实现更智能的输入模式检测
2. **个性化设置**: 允许用户自定义输入状态设置
3. **统计分析**: 提供输入行为的统计分析
4. **多语言支持**: 支持不同语言的输入检测
5. **性能优化**: 进一步优化输入检测的性能

该补丁为讨论应用的编辑器提供了重要的输入状态功能，让用户能够看到其他人的实时输入状态。

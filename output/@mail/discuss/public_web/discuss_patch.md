# Discuss Patch - 讨论补丁

## 概述

`discuss_patch.js` 实现了对 Odoo 讨论应用公共Web环境中讨论组件的补丁扩展，专门添加了实时通信(RTC)功能的集成。该补丁通过Odoo的补丁机制扩展了Discuss组件，注册了Call组件并添加了RTC服务的状态管理，为公共Web环境中的讨论应用提供了音视频通话功能的支持，是讨论应用在公共Web环境中实时通信功能的核心集成组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/public_web/discuss_patch.js`
- **行数**: 25
- **模块**: `@mail/discuss/public_web/discuss_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/public_web/discuss'     // 公共Web讨论组件
'@mail/discuss/call/common/call'    // 通话组件
'@odoo/owl'                         // OWL 框架
'@web/core/utils/hooks'             // Web核心钩子
'@web/core/utils/patch'             // 补丁工具
```

## 补丁定义

### 组件注册

```javascript
Object.assign(Discuss.components, { Call });
```

**组件注册功能**:
- 将Call组件添加到Discuss组件的子组件列表
- 使通话功能在讨论组件中可用
- 扩展讨论组件的功能范围

### Discuss 补丁

```javascript
patch(Discuss.prototype, {
    setup() {
        super.setup(...arguments);
        this.rtc = useState(useService("discuss.rtc"));
    },
});
```

**补丁特性**:
- 扩展讨论组件功能
- 添加RTC服务状态管理
- 保持原有功能完整性

## 核心功能

### 1. 组件集成

```javascript
Object.assign(Discuss.components, { Call });
```

**集成功能**:
- **组件注册**: 将通话组件注册到讨论组件
- **功能扩展**: 扩展讨论组件的可用功能
- **无缝集成**: 与现有讨论框架无缝集成
- **模块化设计**: 保持组件的模块化结构

### 2. RTC服务集成

```javascript
setup() {
    super.setup(...arguments);
    this.rtc = useState(useService("discuss.rtc"));
}
```

**RTC集成功能**:
- **父类调用**: 保持原有的初始化逻辑
- **服务注入**: 注入讨论RTC服务
- **状态管理**: 使RTC状态变化可响应
- **实时通信**: 为音视频通话提供基础支持

## 使用场景

### 1. 讨论组件RTC增强

```javascript
// 讨论组件RTC增强功能
const DiscussPatchEnhancer = {
    enhanceDiscussPatch: () => {
        const EnhancedDiscussPatch = {
            setup() {
                super.setup();
                
                // 原有的RTC服务
                this.rtc = useState(useService("discuss.rtc"));
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.dialogService = useService("dialog");
                this.userService = useService("user");
                this.audioService = useService("audio");
                
                // 增强的RTC状态管理
                this.rtcState = useState({
                    isInitialized: false,
                    connectionStatus: 'disconnected', // disconnected, connecting, connected, failed
                    activeCall: null,
                    incomingCalls: [],
                    callHistory: [],
                    devicePermissions: {
                        camera: null,
                        microphone: null
                    },
                    mediaDevices: {
                        cameras: [],
                        microphones: [],
                        speakers: []
                    },
                    callSettings: {
                        videoEnabled: true,
                        audioEnabled: true,
                        screenShareEnabled: false,
                        recordingEnabled: false
                    },
                    networkQuality: {
                        status: 'unknown', // excellent, good, fair, poor, unknown
                        latency: 0,
                        bandwidth: 0
                    }
                });
                
                // 设置增强功能
                this.setupRTCEnhancements();
            },
            
            // 设置RTC增强功能
            setupRTCEnhancements() {
                // 初始化RTC
                this.initializeRTC();
                
                // 设置设备检测
                this.setupDeviceDetection();
                
                // 设置权限管理
                this.setupPermissionManagement();
                
                // 设置网络质量监控
                this.setupNetworkQualityMonitoring();
                
                // 设置通话历史
                this.setupCallHistory();
                
                // 设置事件监听
                this.setupRTCEventListeners();
            },
            
            // 初始化RTC
            async initializeRTC() {
                try {
                    // 检查浏览器支持
                    if (!this.isBrowserSupported()) {
                        throw new Error('浏览器不支持WebRTC功能');
                    }
                    
                    // 检查设备权限
                    await this.checkDevicePermissions();
                    
                    // 枚举媒体设备
                    await this.enumerateMediaDevices();
                    
                    // 初始化RTC连接
                    await this.initializeRTCConnection();
                    
                    // 加载通话设置
                    this.loadCallSettings();
                    
                    // 加载通话历史
                    this.loadCallHistory();
                    
                    this.rtcState.isInitialized = true;
                    this.rtcState.connectionStatus = 'connected';
                    
                    console.log('RTC初始化成功');
                    
                } catch (error) {
                    console.error('RTC初始化失败:', error);
                    this.rtcState.connectionStatus = 'failed';
                    this.handleRTCInitError(error);
                }
            },
            
            // 检查浏览器支持
            isBrowserSupported() {
                return !!(
                    navigator.mediaDevices &&
                    navigator.mediaDevices.getUserMedia &&
                    window.RTCPeerConnection &&
                    window.RTCSessionDescription &&
                    window.RTCIceCandidate
                );
            },
            
            // 检查设备权限
            async checkDevicePermissions() {
                try {
                    // 检查摄像头权限
                    const cameraPermission = await navigator.permissions.query({ name: 'camera' });
                    this.rtcState.devicePermissions.camera = cameraPermission.state;
                    
                    // 检查麦克风权限
                    const microphonePermission = await navigator.permissions.query({ name: 'microphone' });
                    this.rtcState.devicePermissions.microphone = microphonePermission.state;
                    
                    // 监听权限变化
                    cameraPermission.addEventListener('change', () => {
                        this.rtcState.devicePermissions.camera = cameraPermission.state;
                        this.handlePermissionChange('camera', cameraPermission.state);
                    });
                    
                    microphonePermission.addEventListener('change', () => {
                        this.rtcState.devicePermissions.microphone = microphonePermission.state;
                        this.handlePermissionChange('microphone', microphonePermission.state);
                    });
                    
                } catch (error) {
                    console.warn('检查设备权限失败:', error);
                }
            },
            
            // 枚举媒体设备
            async enumerateMediaDevices() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    
                    this.rtcState.mediaDevices.cameras = devices.filter(
                        device => device.kind === 'videoinput'
                    );
                    
                    this.rtcState.mediaDevices.microphones = devices.filter(
                        device => device.kind === 'audioinput'
                    );
                    
                    this.rtcState.mediaDevices.speakers = devices.filter(
                        device => device.kind === 'audiooutput'
                    );
                    
                    console.log('媒体设备枚举完成:', this.rtcState.mediaDevices);
                    
                } catch (error) {
                    console.error('枚举媒体设备失败:', error);
                }
            },
            
            // 初始化RTC连接
            async initializeRTCConnection() {
                try {
                    // 这里可以初始化WebRTC连接
                    // 具体实现取决于使用的WebRTC库或服务
                    
                    // 设置连接状态监听
                    this.setupConnectionStatusMonitoring();
                    
                } catch (error) {
                    console.error('初始化RTC连接失败:', error);
                    throw error;
                }
            },
            
            // 发起通话
            async initiateCall(threadId, callType = 'video') {
                try {
                    // 检查权限
                    if (!this.hasCallPermission()) {
                        throw new Error('没有通话权限');
                    }
                    
                    // 检查设备权限
                    if (!this.hasRequiredDevicePermissions(callType)) {
                        await this.requestDevicePermissions(callType);
                    }
                    
                    // 检查网络状态
                    if (!navigator.onLine) {
                        throw new Error('网络连接不可用');
                    }
                    
                    // 创建通话
                    const call = await this.createCall(threadId, callType);
                    
                    // 设置活跃通话
                    this.rtcState.activeCall = call;
                    
                    // 记录通话历史
                    this.recordCallHistory(call, 'initiated');
                    
                    // 显示通话界面
                    this.showCallInterface(call);
                    
                    return call;
                    
                } catch (error) {
                    console.error('发起通话失败:', error);
                    this.handleCallError(error);
                    throw error;
                }
            },
            
            // 接听通话
            async answerCall(callId) {
                try {
                    const call = this.findIncomingCall(callId);
                    
                    if (!call) {
                        throw new Error('找不到来电');
                    }
                    
                    // 检查设备权限
                    if (!this.hasRequiredDevicePermissions(call.type)) {
                        await this.requestDevicePermissions(call.type);
                    }
                    
                    // 接听通话
                    await call.answer();
                    
                    // 移除来电列表
                    this.removeIncomingCall(callId);
                    
                    // 设置活跃通话
                    this.rtcState.activeCall = call;
                    
                    // 记录通话历史
                    this.recordCallHistory(call, 'answered');
                    
                    // 显示通话界面
                    this.showCallInterface(call);
                    
                } catch (error) {
                    console.error('接听通话失败:', error);
                    this.handleCallError(error);
                }
            },
            
            // 拒绝通话
            async rejectCall(callId, reason = 'declined') {
                try {
                    const call = this.findIncomingCall(callId);
                    
                    if (!call) {
                        throw new Error('找不到来电');
                    }
                    
                    // 拒绝通话
                    await call.reject(reason);
                    
                    // 移除来电列表
                    this.removeIncomingCall(callId);
                    
                    // 记录通话历史
                    this.recordCallHistory(call, 'rejected', reason);
                    
                } catch (error) {
                    console.error('拒绝通话失败:', error);
                }
            },
            
            // 结束通话
            async endCall(reason = 'ended') {
                try {
                    const call = this.rtcState.activeCall;
                    
                    if (!call) {
                        return;
                    }
                    
                    // 结束通话
                    await call.end(reason);
                    
                    // 清除活跃通话
                    this.rtcState.activeCall = null;
                    
                    // 记录通话历史
                    this.recordCallHistory(call, 'ended', reason);
                    
                    // 隐藏通话界面
                    this.hideCallInterface();
                    
                } catch (error) {
                    console.error('结束通话失败:', error);
                }
            },
            
            // 切换音频
            toggleAudio() {
                try {
                    const call = this.rtcState.activeCall;
                    
                    if (!call) {
                        return;
                    }
                    
                    const newState = !this.rtcState.callSettings.audioEnabled;
                    call.toggleAudio(newState);
                    this.rtcState.callSettings.audioEnabled = newState;
                    
                    this.saveCallSettings();
                    
                } catch (error) {
                    console.error('切换音频失败:', error);
                }
            },
            
            // 切换视频
            toggleVideo() {
                try {
                    const call = this.rtcState.activeCall;
                    
                    if (!call) {
                        return;
                    }
                    
                    const newState = !this.rtcState.callSettings.videoEnabled;
                    call.toggleVideo(newState);
                    this.rtcState.callSettings.videoEnabled = newState;
                    
                    this.saveCallSettings();
                    
                } catch (error) {
                    console.error('切换视频失败:', error);
                }
            },
            
            // 切换屏幕共享
            async toggleScreenShare() {
                try {
                    const call = this.rtcState.activeCall;
                    
                    if (!call) {
                        return;
                    }
                    
                    const newState = !this.rtcState.callSettings.screenShareEnabled;
                    
                    if (newState) {
                        await call.startScreenShare();
                    } else {
                        await call.stopScreenShare();
                    }
                    
                    this.rtcState.callSettings.screenShareEnabled = newState;
                    
                } catch (error) {
                    console.error('切换屏幕共享失败:', error);
                    this.notificationService.add('屏幕共享操作失败', { type: 'error' });
                }
            },
            
            // 检查通话权限
            hasCallPermission() {
                const user = this.userService.user;
                return user && user.isInternalUser;
            },
            
            // 检查设备权限
            hasRequiredDevicePermissions(callType) {
                const permissions = this.rtcState.devicePermissions;
                
                // 音频通话需要麦克风权限
                if (permissions.microphone !== 'granted') {
                    return false;
                }
                
                // 视频通话需要摄像头权限
                if (callType === 'video' && permissions.camera !== 'granted') {
                    return false;
                }
                
                return true;
            },
            
            // 请求设备权限
            async requestDevicePermissions(callType) {
                try {
                    const constraints = {
                        audio: true,
                        video: callType === 'video'
                    };
                    
                    const stream = await navigator.mediaDevices.getUserMedia(constraints);
                    
                    // 立即停止流，我们只是为了获取权限
                    stream.getTracks().forEach(track => track.stop());
                    
                    // 重新检查权限
                    await this.checkDevicePermissions();
                    
                } catch (error) {
                    console.error('请求设备权限失败:', error);
                    throw new Error('无法获取设备权限');
                }
            },
            
            // 处理权限变化
            handlePermissionChange(deviceType, state) {
                console.log(`${deviceType}权限变化:`, state);
                
                if (state === 'denied') {
                    this.notificationService.add(
                        `${deviceType === 'camera' ? '摄像头' : '麦克风'}权限被拒绝，通话功能可能受限`,
                        { type: 'warning' }
                    );
                }
            },
            
            // 处理RTC初始化错误
            handleRTCInitError(error) {
                let message = 'RTC初始化失败';
                
                if (error.message.includes('不支持')) {
                    message = '您的浏览器不支持音视频通话功能';
                } else if (error.message.includes('权限')) {
                    message = '无法获取设备权限，请检查浏览器设置';
                } else if (error.message.includes('网络')) {
                    message = '网络连接异常，请检查网络设置';
                }
                
                this.notificationService.add(message, { type: 'error' });
            },
            
            // 处理通话错误
            handleCallError(error) {
                let message = '通话操作失败';
                
                if (error.message.includes('权限')) {
                    message = '没有通话权限或设备权限不足';
                } else if (error.message.includes('网络')) {
                    message = '网络连接异常，无法建立通话';
                } else if (error.message.includes('设备')) {
                    message = '设备不可用，请检查摄像头和麦克风';
                }
                
                this.notificationService.add(message, { type: 'error' });
            },
            
            // 记录通话历史
            recordCallHistory(call, action, reason = null) {
                try {
                    const historyItem = {
                        callId: call.id,
                        threadId: call.threadId,
                        type: call.type,
                        action: action,
                        reason: reason,
                        timestamp: Date.now(),
                        duration: call.duration || 0
                    };
                    
                    this.rtcState.callHistory.unshift(historyItem);
                    
                    // 限制历史记录数量
                    if (this.rtcState.callHistory.length > 100) {
                        this.rtcState.callHistory.splice(100);
                    }
                    
                    // 保存到本地存储
                    this.saveCallHistory();
                    
                } catch (error) {
                    console.warn('记录通话历史失败:', error);
                }
            },
            
            // 加载通话设置
            loadCallSettings() {
                try {
                    const settings = JSON.parse(
                        localStorage.getItem('rtc_call_settings') || '{}'
                    );
                    
                    this.rtcState.callSettings = {
                        videoEnabled: settings.videoEnabled !== false,
                        audioEnabled: settings.audioEnabled !== false,
                        screenShareEnabled: settings.screenShareEnabled || false,
                        recordingEnabled: settings.recordingEnabled || false
                    };
                    
                } catch (error) {
                    console.warn('加载通话设置失败:', error);
                }
            },
            
            // 保存通话设置
            saveCallSettings() {
                try {
                    localStorage.setItem(
                        'rtc_call_settings',
                        JSON.stringify(this.rtcState.callSettings)
                    );
                } catch (error) {
                    console.warn('保存通话设置失败:', error);
                }
            },
            
            // 加载通话历史
            loadCallHistory() {
                try {
                    const history = JSON.parse(
                        localStorage.getItem('rtc_call_history') || '[]'
                    );
                    
                    this.rtcState.callHistory = history;
                    
                } catch (error) {
                    console.warn('加载通话历史失败:', error);
                }
            },
            
            // 保存通话历史
            saveCallHistory() {
                try {
                    localStorage.setItem(
                        'rtc_call_history',
                        JSON.stringify(this.rtcState.callHistory)
                    );
                } catch (error) {
                    console.warn('保存通话历史失败:', error);
                }
            },
            
            // 获取RTC统计信息
            getRTCStatistics() {
                return {
                    isInitialized: this.rtcState.isInitialized,
                    connectionStatus: this.rtcState.connectionStatus,
                    hasActiveCall: !!this.rtcState.activeCall,
                    incomingCallsCount: this.rtcState.incomingCalls.length,
                    callHistoryCount: this.rtcState.callHistory.length,
                    devicePermissions: { ...this.rtcState.devicePermissions },
                    mediaDevicesCount: {
                        cameras: this.rtcState.mediaDevices.cameras.length,
                        microphones: this.rtcState.mediaDevices.microphones.length,
                        speakers: this.rtcState.mediaDevices.speakers.length
                    },
                    networkQuality: { ...this.rtcState.networkQuality }
                };
            }
        };
        
        patch(Discuss.prototype, EnhancedDiscussPatch);
    }
};

// 应用讨论补丁增强
DiscussPatchEnhancer.enhanceDiscussPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 组件集成
- 无缝组件注册
- 模块化设计
- 清晰的组件边界

### 3. 服务集成
- RTC服务状态管理
- 响应式状态更新
- 服务依赖注入

### 4. 实时通信
- WebRTC功能支持
- 音视频通话集成
- 实时状态同步

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 组合模式 (Composite Pattern)
- 组件的组合和管理
- 统一的组件接口

### 3. 服务定位器模式 (Service Locator Pattern)
- 服务的定位和注入
- 依赖关系管理

## 注意事项

1. **组件兼容性**: 确保新组件与讨论框架的兼容性
2. **服务依赖**: 正确管理RTC服务的依赖关系
3. **状态同步**: 保持RTC状态的同步和一致性
4. **性能影响**: 避免RTC功能影响讨论组件性能

## 扩展建议

1. **更多通话功能**: 添加更多类型的通话功能
2. **设备管理**: 实现更完善的设备管理
3. **网络优化**: 优化网络连接和质量监控
4. **录制功能**: 添加通话录制和回放功能
5. **屏幕共享**: 增强屏幕共享功能

该补丁为公共Web环境中的讨论应用提供了重要的实时通信功能集成，是音视频通话功能的核心组件。

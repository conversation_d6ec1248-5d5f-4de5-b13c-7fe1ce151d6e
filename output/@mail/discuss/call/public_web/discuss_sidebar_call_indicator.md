# Discuss Sidebar Call Indicator - 讨论侧边栏通话指示器

## 概述

`discuss_sidebar_call_indicator.js` 实现了 Odoo 讨论应用中侧边栏的通话指示器组件，用于在讨论侧边栏中显示线程的通话状态。该组件通过侧边栏指示器注册表机制集成到讨论界面，支持线程通话状态显示、RTC服务集成、响应式状态管理等功能，提供了直观的通话状态可视化，是视频通话系统中用户界面集成的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/public_web/discuss_sidebar_call_indicator.js`
- **行数**: 34
- **模块**: `@mail/discuss/call/public_web/discuss_sidebar_call_indicator`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/thread_model'                           // 线程模型
'@mail/discuss/core/public_web/discuss_sidebar_categories' // 侧边栏分类

// 核心依赖
'@odoo/owl'                                                // OWL 框架
'@web/core/utils/hooks'                                    // Web 核心钩子
```

## 组件定义

### DiscussSidebarCallIndicator 类

```javascript
class DiscussSidebarCallIndicator extends Component {
    static template = "mail.DiscussSidebarCallIndicator";
    static props = { thread: { type: Thread } };
    static components = {};
}
```

**组件特性**:
- 继承自Component基类
- 使用专用模板
- 接收线程属性
- 无子组件依赖

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.rtc = useState(useService("discuss.rtc"));
}
```

**初始化内容**:
- 邮件存储服务集成
- RTC服务状态管理
- 响应式状态绑定

## 注册机制

### 侧边栏指示器注册

```javascript
discussSidebarChannelIndicatorsRegistry.add("call-indicator", DiscussSidebarCallIndicator);
```

**注册特性**:
- 注册到侧边栏指示器注册表
- 使用"call-indicator"标识符
- 自动集成到讨论侧边栏

## 使用场景

### 1. 通话指示器增强

```javascript
// 通话指示器增强功能
const CallIndicatorEnhancer = {
    enhanceCallIndicator: () => {
        const EnhancedCallIndicator = class extends DiscussSidebarCallIndicator {
            setup() {
                super.setup();
                
                // 添加增强状态
                this.indicatorState = useState({
                    isBlinking: false,
                    participantCount: 0,
                    callDuration: 0,
                    callQuality: 'unknown',
                    isRecording: false
                });
                
                // 设置状态更新
                this.setupStateUpdates();
                
                // 设置动画效果
                this.setupAnimations();
                
                // 设置工具提示
                this.setupTooltips();
            },
            
            // 设置状态更新
            setupStateUpdates() {
                // 监听RTC状态变化
                this.rtc.addEventListener('state_changed', () => {
                    this.updateIndicatorState();
                });
                
                // 监听线程变化
                this.props.thread.addEventListener('change', () => {
                    this.updateIndicatorState();
                });
                
                // 初始状态更新
                this.updateIndicatorState();
            },
            
            // 更新指示器状态
            updateIndicatorState() {
                const thread = this.props.thread;
                const rtcState = this.rtc.state;
                
                // 检查是否为当前通话线程
                const isCurrentCallThread = rtcState.channel?.eq(thread);
                
                if (isCurrentCallThread) {
                    // 更新通话相关状态
                    this.indicatorState.participantCount = thread.rtcSessions?.length || 0;
                    this.indicatorState.callDuration = this.calculateCallDuration();
                    this.indicatorState.callQuality = this.getCallQuality();
                    this.indicatorState.isRecording = this.checkRecordingStatus();
                    this.indicatorState.isBlinking = false; // 当前通话不闪烁
                } else if (thread.rtcInvitingSession) {
                    // 有来电邀请时闪烁
                    this.indicatorState.isBlinking = true;
                    this.indicatorState.participantCount = 0;
                } else if (thread.rtcSessions?.length > 0) {
                    // 其他线程有通话时显示参与者数量
                    this.indicatorState.participantCount = thread.rtcSessions.length;
                    this.indicatorState.isBlinking = false;
                } else {
                    // 无通话状态
                    this.resetIndicatorState();
                }
                
                // 更新UI显示
                this.updateIndicatorDisplay();
            },
            
            // 计算通话时长
            calculateCallDuration() {
                const rtcSession = this.rtc.selfSession;
                if (rtcSession && rtcSession.startTime) {
                    return Math.floor((Date.now() - rtcSession.startTime) / 1000);
                }
                return 0;
            },
            
            // 获取通话质量
            getCallQuality() {
                const rtcSession = this.rtc.selfSession;
                if (rtcSession) {
                    return rtcSession.connectionQuality || 'unknown';
                }
                return 'unknown';
            },
            
            // 检查录制状态
            checkRecordingStatus() {
                const thread = this.props.thread;
                return thread.rtcSessions?.some(session => session.isRecording) || false;
            },
            
            // 重置指示器状态
            resetIndicatorState() {
                this.indicatorState.isBlinking = false;
                this.indicatorState.participantCount = 0;
                this.indicatorState.callDuration = 0;
                this.indicatorState.callQuality = 'unknown';
                this.indicatorState.isRecording = false;
            },
            
            // 更新指示器显示
            updateIndicatorDisplay() {
                if (!this.el) return;
                
                // 更新闪烁状态
                this.el.classList.toggle('blinking', this.indicatorState.isBlinking);
                
                // 更新通话质量样式
                this.el.classList.remove('quality-excellent', 'quality-good', 'quality-fair', 'quality-poor');
                if (this.indicatorState.callQuality !== 'unknown') {
                    this.el.classList.add(`quality-${this.indicatorState.callQuality}`);
                }
                
                // 更新录制状态
                this.el.classList.toggle('recording', this.indicatorState.isRecording);
                
                // 更新参与者计数显示
                const countElement = this.el.querySelector('.participant-count');
                if (countElement) {
                    countElement.textContent = this.indicatorState.participantCount;
                    countElement.style.display = this.indicatorState.participantCount > 0 ? 'block' : 'none';
                }
                
                // 更新通话时长显示
                const durationElement = this.el.querySelector('.call-duration');
                if (durationElement && this.indicatorState.callDuration > 0) {
                    durationElement.textContent = this.formatDuration(this.indicatorState.callDuration);
                    durationElement.style.display = 'block';
                } else if (durationElement) {
                    durationElement.style.display = 'none';
                }
            },
            
            // 格式化时长
            formatDuration(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = seconds % 60;
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            },
            
            // 设置动画效果
            setupAnimations() {
                onMounted(() => {
                    if (this.el) {
                        // 添加CSS动画类
                        this.el.classList.add('call-indicator-enhanced');
                        
                        // 设置闪烁动画
                        this.setupBlinkingAnimation();
                        
                        // 设置脉冲动画
                        this.setupPulseAnimation();
                    }
                });
            },
            
            // 设置闪烁动画
            setupBlinkingAnimation() {
                let blinkInterval;
                
                const startBlinking = () => {
                    if (blinkInterval) return;
                    
                    blinkInterval = setInterval(() => {
                        if (this.el) {
                            this.el.classList.toggle('blink-active');
                        }
                    }, 500);
                };
                
                const stopBlinking = () => {
                    if (blinkInterval) {
                        clearInterval(blinkInterval);
                        blinkInterval = null;
                        if (this.el) {
                            this.el.classList.remove('blink-active');
                        }
                    }
                };
                
                // 监听闪烁状态变化
                this.indicatorState.addEventListener('change', () => {
                    if (this.indicatorState.isBlinking) {
                        startBlinking();
                    } else {
                        stopBlinking();
                    }
                });
                
                // 组件销毁时清理
                onWillUnmount(() => {
                    stopBlinking();
                });
            },
            
            // 设置脉冲动画
            setupPulseAnimation() {
                onMounted(() => {
                    if (this.el && this.indicatorState.participantCount > 0) {
                        this.el.classList.add('pulse-active');
                    }
                });
                
                // 监听参与者数量变化
                this.indicatorState.addEventListener('change', () => {
                    if (this.el) {
                        this.el.classList.toggle('pulse-active', this.indicatorState.participantCount > 0);
                    }
                });
            },
            
            // 设置工具提示
            setupTooltips() {
                onMounted(() => {
                    if (this.el) {
                        this.el.addEventListener('mouseenter', () => {
                            this.showTooltip();
                        });
                        
                        this.el.addEventListener('mouseleave', () => {
                            this.hideTooltip();
                        });
                    }
                });
            },
            
            // 显示工具提示
            showTooltip() {
                const tooltip = this.createTooltip();
                document.body.appendChild(tooltip);
                
                // 定位工具提示
                this.positionTooltip(tooltip);
            },
            
            // 创建工具提示
            createTooltip() {
                const tooltip = document.createElement('div');
                tooltip.className = 'call-indicator-tooltip';
                tooltip.innerHTML = this.getTooltipContent();
                
                return tooltip;
            },
            
            // 获取工具提示内容
            getTooltipContent() {
                const thread = this.props.thread;
                const state = this.indicatorState;
                
                let content = `<div class="tooltip-header">${thread.displayName}</div>`;
                
                if (state.participantCount > 0) {
                    content += `<div class="tooltip-participants">${state.participantCount} 人在通话中</div>`;
                }
                
                if (state.callDuration > 0) {
                    content += `<div class="tooltip-duration">通话时长: ${this.formatDuration(state.callDuration)}</div>`;
                }
                
                if (state.callQuality !== 'unknown') {
                    const qualityText = {
                        excellent: '优秀',
                        good: '良好',
                        fair: '一般',
                        poor: '较差'
                    };
                    content += `<div class="tooltip-quality">通话质量: ${qualityText[state.callQuality]}</div>`;
                }
                
                if (state.isRecording) {
                    content += `<div class="tooltip-recording">🔴 正在录制</div>`;
                }
                
                if (state.isBlinking) {
                    content += `<div class="tooltip-invitation">📞 有来电邀请</div>`;
                }
                
                return content;
            },
            
            // 定位工具提示
            positionTooltip(tooltip) {
                const rect = this.el.getBoundingClientRect();
                
                tooltip.style.position = 'fixed';
                tooltip.style.left = `${rect.right + 10}px`;
                tooltip.style.top = `${rect.top}px`;
                tooltip.style.zIndex = '1000';
            },
            
            // 隐藏工具提示
            hideTooltip() {
                const tooltip = document.querySelector('.call-indicator-tooltip');
                if (tooltip) {
                    tooltip.remove();
                }
            },
            
            // 获取指示器状态
            getIndicatorStatus() {
                return {
                    hasCall: this.indicatorState.participantCount > 0,
                    hasInvitation: this.indicatorState.isBlinking,
                    isRecording: this.indicatorState.isRecording,
                    quality: this.indicatorState.callQuality,
                    duration: this.indicatorState.callDuration,
                    participants: this.indicatorState.participantCount
                };
            }
        };
        
        // 替换原始组件
        __exports.DiscussSidebarCallIndicator = EnhancedCallIndicator;
        
        // 重新注册增强组件
        discussSidebarChannelIndicatorsRegistry.add("call-indicator", EnhancedCallIndicator);
    }
};

// 应用通话指示器增强
CallIndicatorEnhancer.enhanceCallIndicator();
```

### 2. 指示器样式管理

```javascript
// 指示器样式管理
const IndicatorStyleManager = {
    setupStyleManagement: () => {
        const StylePatch = {
            setup() {
                super.setup();
                
                // 样式配置
                this.styleConfig = {
                    theme: 'default',
                    size: 'medium',
                    position: 'right',
                    showAnimation: true,
                    customColors: {
                        active: '#00a651',
                        inactive: '#6c757d',
                        warning: '#ffc107',
                        danger: '#dc3545'
                    }
                };
                
                // 应用样式
                this.applyStyles();
            },
            
            // 应用样式
            applyStyles() {
                onMounted(() => {
                    if (this.el) {
                        this.el.classList.add(`theme-${this.styleConfig.theme}`);
                        this.el.classList.add(`size-${this.styleConfig.size}`);
                        this.el.classList.add(`position-${this.styleConfig.position}`);
                        
                        if (this.styleConfig.showAnimation) {
                            this.el.classList.add('animated');
                        }
                        
                        // 应用自定义颜色
                        this.applyCustomColors();
                    }
                });
            },
            
            // 应用自定义颜色
            applyCustomColors() {
                const colors = this.styleConfig.customColors;
                const style = document.createElement('style');
                style.textContent = `
                    .call-indicator-enhanced.active {
                        color: ${colors.active};
                        border-color: ${colors.active};
                    }
                    .call-indicator-enhanced.inactive {
                        color: ${colors.inactive};
                        border-color: ${colors.inactive};
                    }
                    .call-indicator-enhanced.warning {
                        color: ${colors.warning};
                        border-color: ${colors.warning};
                    }
                    .call-indicator-enhanced.danger {
                        color: ${colors.danger};
                        border-color: ${colors.danger};
                    }
                `;
                document.head.appendChild(style);
            },
            
            // 更新样式主题
            updateTheme(theme) {
                if (this.el) {
                    this.el.classList.remove(`theme-${this.styleConfig.theme}`);
                    this.styleConfig.theme = theme;
                    this.el.classList.add(`theme-${theme}`);
                }
            },
            
            // 更新指示器大小
            updateSize(size) {
                if (this.el) {
                    this.el.classList.remove(`size-${this.styleConfig.size}`);
                    this.styleConfig.size = size;
                    this.el.classList.add(`size-${size}`);
                }
            }
        };
        
        patch(DiscussSidebarCallIndicator.prototype, StylePatch);
    }
};

// 应用样式管理
IndicatorStyleManager.setupStyleManagement();
```

### 3. 交互功能增强

```javascript
// 交互功能增强
const InteractionEnhancer = {
    setupInteractionFeatures: () => {
        const InteractionPatch = {
            setup() {
                super.setup();
                
                // 交互配置
                this.interactionConfig = {
                    clickToJoin: true,
                    rightClickMenu: true,
                    doubleClickAction: 'toggle_video',
                    hoverPreview: true
                };
                
                // 设置交互事件
                this.setupInteractionEvents();
            },
            
            // 设置交互事件
            setupInteractionEvents() {
                onMounted(() => {
                    if (this.el) {
                        // 点击事件
                        if (this.interactionConfig.clickToJoin) {
                            this.el.addEventListener('click', (event) => {
                                this.handleClick(event);
                            });
                        }
                        
                        // 右键菜单
                        if (this.interactionConfig.rightClickMenu) {
                            this.el.addEventListener('contextmenu', (event) => {
                                this.handleRightClick(event);
                            });
                        }
                        
                        // 双击事件
                        if (this.interactionConfig.doubleClickAction) {
                            this.el.addEventListener('dblclick', (event) => {
                                this.handleDoubleClick(event);
                            });
                        }
                        
                        // 悬停预览
                        if (this.interactionConfig.hoverPreview) {
                            this.el.addEventListener('mouseenter', () => {
                                this.showHoverPreview();
                            });
                            
                            this.el.addEventListener('mouseleave', () => {
                                this.hideHoverPreview();
                            });
                        }
                    }
                });
            },
            
            // 处理点击事件
            handleClick(event) {
                event.preventDefault();
                event.stopPropagation();
                
                const thread = this.props.thread;
                
                if (thread.rtcInvitingSession) {
                    // 有邀请时加入通话
                    this.rtc.toggleCall(thread);
                } else if (thread.rtcSessions?.length > 0) {
                    // 有通话时加入或离开
                    this.rtc.toggleCall(thread);
                } else {
                    // 无通话时开始通话
                    this.rtc.toggleCall(thread);
                }
            },
            
            // 处理右键事件
            handleRightClick(event) {
                event.preventDefault();
                event.stopPropagation();
                
                this.showContextMenu(event);
            },
            
            // 显示上下文菜单
            showContextMenu(event) {
                const menu = document.createElement('div');
                menu.className = 'call-indicator-context-menu';
                menu.innerHTML = this.getContextMenuContent();
                
                // 定位菜单
                menu.style.position = 'fixed';
                menu.style.left = `${event.clientX}px`;
                menu.style.top = `${event.clientY}px`;
                menu.style.zIndex = '1001';
                
                document.body.appendChild(menu);
                
                // 绑定菜单事件
                this.bindContextMenuEvents(menu);
                
                // 点击外部关闭菜单
                const closeMenu = (e) => {
                    if (!menu.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                };
                
                setTimeout(() => {
                    document.addEventListener('click', closeMenu);
                }, 0);
            },
            
            // 获取上下文菜单内容
            getContextMenuContent() {
                const thread = this.props.thread;
                const hasCall = thread.rtcSessions?.length > 0;
                const hasInvitation = Boolean(thread.rtcInvitingSession);
                
                let content = '';
                
                if (hasInvitation) {
                    content += '<div class="menu-item" data-action="accept_call">接受通话</div>';
                    content += '<div class="menu-item" data-action="decline_call">拒绝通话</div>';
                } else if (hasCall) {
                    content += '<div class="menu-item" data-action="join_call">加入通话</div>';
                    content += '<div class="menu-item" data-action="leave_call">离开通话</div>';
                } else {
                    content += '<div class="menu-item" data-action="start_call">开始通话</div>';
                    content += '<div class="menu-item" data-action="start_video_call">开始视频通话</div>';
                }
                
                content += '<div class="menu-separator"></div>';
                content += '<div class="menu-item" data-action="call_settings">通话设置</div>';
                content += '<div class="menu-item" data-action="call_history">通话历史</div>';
                
                return content;
            },
            
            // 绑定上下文菜单事件
            bindContextMenuEvents(menu) {
                const menuItems = menu.querySelectorAll('.menu-item');
                
                menuItems.forEach(item => {
                    item.addEventListener('click', () => {
                        const action = item.dataset.action;
                        this.handleContextMenuAction(action);
                        menu.remove();
                    });
                });
            },
            
            // 处理上下文菜单操作
            handleContextMenuAction(action) {
                const thread = this.props.thread;
                
                switch (action) {
                    case 'accept_call':
                    case 'join_call':
                    case 'start_call':
                        this.rtc.toggleCall(thread);
                        break;
                        
                    case 'start_video_call':
                        this.rtc.toggleCall(thread, { video: true });
                        break;
                        
                    case 'decline_call':
                    case 'leave_call':
                        this.rtc.leaveCall(thread);
                        break;
                        
                    case 'call_settings':
                        this.showCallSettings();
                        break;
                        
                    case 'call_history':
                        this.showCallHistory();
                        break;
                }
            },
            
            // 处理双击事件
            handleDoubleClick(event) {
                event.preventDefault();
                event.stopPropagation();
                
                const action = this.interactionConfig.doubleClickAction;
                const thread = this.props.thread;
                
                switch (action) {
                    case 'toggle_video':
                        if (this.rtc.selfSession) {
                            this.rtc.toggleVideo();
                        }
                        break;
                        
                    case 'toggle_audio':
                        if (this.rtc.selfSession) {
                            this.rtc.toggleAudio();
                        }
                        break;
                        
                    case 'start_screen_share':
                        if (this.rtc.selfSession) {
                            this.rtc.toggleVideo('screen');
                        }
                        break;
                }
            },
            
            // 显示悬停预览
            showHoverPreview() {
                const preview = this.createHoverPreview();
                document.body.appendChild(preview);
                
                // 定位预览
                this.positionHoverPreview(preview);
            },
            
            // 创建悬停预览
            createHoverPreview() {
                const preview = document.createElement('div');
                preview.className = 'call-indicator-hover-preview';
                preview.innerHTML = this.getHoverPreviewContent();
                
                return preview;
            },
            
            // 获取悬停预览内容
            getHoverPreviewContent() {
                const thread = this.props.thread;
                const rtcSessions = thread.rtcSessions || [];
                
                let content = `<div class="preview-header">${thread.displayName}</div>`;
                
                if (rtcSessions.length > 0) {
                    content += '<div class="preview-participants">';
                    content += '<div class="participants-title">通话参与者:</div>';
                    
                    rtcSessions.forEach(session => {
                        const name = session.channelMember?.persona?.name || '未知用户';
                        const status = session.isTalking ? '🎤' : session.isMute ? '🔇' : '🔊';
                        content += `<div class="participant-item">${status} ${name}</div>`;
                    });
                    
                    content += '</div>';
                } else if (thread.rtcInvitingSession) {
                    content += '<div class="preview-invitation">📞 有来电邀请</div>';
                } else {
                    content += '<div class="preview-no-call">暂无通话</div>';
                }
                
                return content;
            },
            
            // 定位悬停预览
            positionHoverPreview(preview) {
                const rect = this.el.getBoundingClientRect();
                
                preview.style.position = 'fixed';
                preview.style.left = `${rect.right + 10}px`;
                preview.style.top = `${rect.top}px`;
                preview.style.zIndex = '1000';
            },
            
            // 隐藏悬停预览
            hideHoverPreview() {
                const preview = document.querySelector('.call-indicator-hover-preview');
                if (preview) {
                    preview.remove();
                }
            }
        };
        
        patch(DiscussSidebarCallIndicator.prototype, InteractionPatch);
    }
};

// 应用交互功能增强
InteractionEnhancer.setupInteractionFeatures();
```

## 技术特点

### 1. 组件集成
- OWL组件系统集成
- 侧边栏注册表机制
- 服务依赖注入

### 2. 状态管理
- 响应式状态绑定
- RTC状态监听
- 线程状态跟踪

### 3. 用户界面
- 直观的状态指示
- 动态样式更新
- 交互式操作支持

### 4. 扩展性
- 注册表机制支持
- 模块化设计
- 易于功能扩展

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的指示器组件
- 清晰的属性接口

### 2. 注册表模式 (Registry Pattern)
- 指示器的注册和管理
- 动态功能扩展

### 3. 观察者模式 (Observer Pattern)
- 状态变化监听
- 自动化UI更新

## 注意事项

1. **性能优化**: 避免频繁的DOM更新
2. **状态同步**: 确保指示器状态与实际状态一致
3. **用户体验**: 提供清晰的视觉反馈
4. **可访问性**: 支持键盘导航和屏幕阅读器

## 扩展建议

1. **自定义样式**: 支持用户自定义指示器样式
2. **更多状态**: 显示更多通话相关状态信息
3. **快捷操作**: 添加更多快捷操作功能
4. **通知集成**: 集成系统通知功能
5. **分析统计**: 添加指示器使用统计

该组件为视频通话系统提供了直观的侧边栏通话状态指示，是用户界面集成的重要组成部分。

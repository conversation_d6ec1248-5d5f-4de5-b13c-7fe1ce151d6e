# Discuss Sidebar Categories Patch - 讨论侧边栏分类补丁

## 概述

`discuss_sidebar_categories_patch.js` 实现了对 Odoo 讨论应用中侧边栏分类组件的补丁扩展，为侧边栏频道组件添加了通话相关的功能和样式。该补丁通过Odoo的补丁机制扩展了DiscussSidebarChannel组件，集成了通话参与者组件、添加了通话状态样式类、增强了边框显示逻辑，提供了完整的通话状态可视化，是视频通话系统中侧边栏界面集成的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/public_web/discuss_sidebar_categories_patch.js`
- **行数**: 35
- **模块**: `@mail/discuss/call/public_web/discuss_sidebar_categories_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/call/public_web/discuss_sidebar_call_participants' // 通话参与者组件
'@mail/discuss/core/public_web/discuss_sidebar_categories'        // 侧边栏分类

// 核心依赖
'@web/core/utils/patch'                                           // 补丁工具
```

## 组件集成

### 组件注册

```javascript
DiscussSidebarChannel.components = Object.assign(DiscussSidebarChannel.components || {}, {
    DiscussSidebarCallParticipants,
});
```

**集成特性**:
- 扩展现有组件集合
- 添加通话参与者组件
- 保持原有组件完整性

## 补丁定义

### 样式类扩展

```javascript
patch(DiscussSidebarChannel.prototype, {
    get attClass() {
        return {
            ...super.attClass,
            "o-ongoingCall": this.thread.rtcSessions.length > 0,
        };
    },
    get attClassContainer() {
        return {
            ...super.attClassContainer,
            "o-selfInCall": this.store.rtc.selfSession?.in(this.thread.rtcSessions),
        };
    },
    get bordered() {
        return super.bordered || this.thread.rtcSessions.length > 0;
    },
});
```

**补丁功能**:
- **attClass**: 添加进行中通话样式类
- **attClassContainer**: 添加自身参与通话样式类
- **bordered**: 有通话时显示边框

## 核心功能

### 1. 通话状态样式

```javascript
get attClass() {
    return {
        ...super.attClass,
        "o-ongoingCall": this.thread.rtcSessions.length > 0,
    };
}
```

**样式功能**:
- 继承原有样式类
- 添加"o-ongoingCall"类当有通话时
- 基于RTC会话数量判断

### 2. 自身参与状态

```javascript
get attClassContainer() {
    return {
        ...super.attClassContainer,
        "o-selfInCall": this.store.rtc.selfSession?.in(this.thread.rtcSessions),
    };
}
```

**状态功能**:
- 继承原有容器样式
- 添加"o-selfInCall"类当自己参与通话时
- 基于自身会话状态判断

### 3. 边框显示增强

```javascript
get bordered() {
    return super.bordered || this.thread.rtcSessions.length > 0;
}
```

**边框功能**:
- 保持原有边框逻辑
- 有通话时强制显示边框
- 增强视觉区分效果

## 使用场景

### 1. 样式增强

```javascript
// 样式增强功能
const SidebarStyleEnhancer = {
    enhanceSidebarStyles: () => {
        const StylePatch = {
            get attClass() {
                const baseClass = super.attClass;
                const thread = this.thread;
                const rtcSessions = thread.rtcSessions || [];
                
                return {
                    ...baseClass,
                    "o-ongoingCall": rtcSessions.length > 0,
                    "o-hasVideo": rtcSessions.some(s => s.isVideoStreaming),
                    "o-hasScreenShare": rtcSessions.some(s => s.isScreenSharingOn),
                    "o-isRecording": rtcSessions.some(s => s.isRecording),
                    "o-hasInvitation": Boolean(thread.rtcInvitingSession),
                    "o-callQuality-poor": rtcSessions.some(s => s.connectionQuality === 'poor'),
                    "o-participantCount-many": rtcSessions.length > 5
                };
            },
            
            get attClassContainer() {
                const baseClass = super.attClassContainer;
                const selfSession = this.store.rtc.selfSession;
                const thread = this.thread;
                
                return {
                    ...baseClass,
                    "o-selfInCall": selfSession?.in(thread.rtcSessions),
                    "o-selfTalking": selfSession?.isActuallyTalking,
                    "o-selfMuted": selfSession?.isMute,
                    "o-selfVideo": selfSession?.isVideoStreaming,
                    "o-selfScreenShare": selfSession?.isScreenSharingOn
                };
            },
            
            get bordered() {
                const baseBordered = super.bordered;
                const thread = this.thread;
                
                // 有通话、邀请或自己参与时显示边框
                return baseBordered || 
                       thread.rtcSessions.length > 0 || 
                       Boolean(thread.rtcInvitingSession) ||
                       this.store.rtc.selfSession?.in(thread.rtcSessions);
            }
        };
        
        patch(DiscussSidebarChannel.prototype, StylePatch);
    }
};

// 应用样式增强
SidebarStyleEnhancer.enhanceSidebarStyles();
```

### 2. 动态样式管理

```javascript
// 动态样式管理
const DynamicStyleManager = {
    setupDynamicStyles: () => {
        const DynamicPatch = {
            setup() {
                super.setup();
                
                // 动态样式状态
                this.dynamicStyles = useState({
                    pulseAnimation: false,
                    glowEffect: false,
                    customTheme: 'default'
                });
                
                // 设置样式监听
                this.setupStyleListeners();
            },
            
            setupStyleListeners() {
                // 监听通话状态变化
                this.thread.rtcSessions.addEventListener('change', () => {
                    this.updateDynamicStyles();
                });
                
                // 监听自身状态变化
                if (this.store.rtc.selfSession) {
                    this.store.rtc.selfSession.addEventListener('change', () => {
                        this.updateDynamicStyles();
                    });
                }
            },
            
            updateDynamicStyles() {
                const thread = this.thread;
                const selfSession = this.store.rtc.selfSession;
                
                // 更新脉冲动画
                this.dynamicStyles.pulseAnimation = Boolean(thread.rtcInvitingSession);
                
                // 更新发光效果
                this.dynamicStyles.glowEffect = selfSession?.in(thread.rtcSessions) && 
                                                selfSession?.isActuallyTalking;
                
                // 应用动态样式
                this.applyDynamicStyles();
            },
            
            applyDynamicStyles() {
                onMounted(() => {
                    if (this.el) {
                        // 应用脉冲动画
                        this.el.classList.toggle('pulse-animation', this.dynamicStyles.pulseAnimation);
                        
                        // 应用发光效果
                        this.el.classList.toggle('glow-effect', this.dynamicStyles.glowEffect);
                        
                        // 应用自定义主题
                        this.el.classList.add(`theme-${this.dynamicStyles.customTheme}`);
                    }
                });
            },
            
            get attClass() {
                const baseClass = super.attClass;
                
                return {
                    ...baseClass,
                    "o-pulse": this.dynamicStyles.pulseAnimation,
                    "o-glow": this.dynamicStyles.glowEffect,
                    [`o-theme-${this.dynamicStyles.customTheme}`]: true
                };
            }
        };
        
        patch(DiscussSidebarChannel.prototype, DynamicPatch);
    }
};

// 应用动态样式管理
DynamicStyleManager.setupDynamicStyles();
```

### 3. 交互增强

```javascript
// 交互增强功能
const InteractionEnhancer = {
    enhanceInteractions: () => {
        const InteractionPatch = {
            setup() {
                super.setup();
                
                // 交互状态
                this.interactionState = useState({
                    isHovered: false,
                    isPressed: false,
                    showQuickActions: false
                });
                
                // 设置交互事件
                this.setupInteractionEvents();
            },
            
            setupInteractionEvents() {
                onMounted(() => {
                    if (this.el) {
                        // 悬停事件
                        this.el.addEventListener('mouseenter', () => {
                            this.interactionState.isHovered = true;
                            this.showQuickActions();
                        });
                        
                        this.el.addEventListener('mouseleave', () => {
                            this.interactionState.isHovered = false;
                            this.hideQuickActions();
                        });
                        
                        // 按下事件
                        this.el.addEventListener('mousedown', () => {
                            this.interactionState.isPressed = true;
                        });
                        
                        this.el.addEventListener('mouseup', () => {
                            this.interactionState.isPressed = false;
                        });
                        
                        // 右键菜单
                        this.el.addEventListener('contextmenu', (event) => {
                            this.showCallContextMenu(event);
                        });
                    }
                });
            },
            
            showQuickActions() {
                if (this.thread.rtcSessions.length > 0) {
                    this.interactionState.showQuickActions = true;
                    this.createQuickActionButtons();
                }
            },
            
            hideQuickActions() {
                this.interactionState.showQuickActions = false;
                this.removeQuickActionButtons();
            },
            
            createQuickActionButtons() {
                if (this.quickActionsContainer) return;
                
                this.quickActionsContainer = document.createElement('div');
                this.quickActionsContainer.className = 'quick-actions-container';
                this.quickActionsContainer.innerHTML = `
                    <button class="quick-action-btn join-call" title="加入通话">
                        <i class="fa fa-phone"></i>
                    </button>
                    <button class="quick-action-btn mute-all" title="静音所有">
                        <i class="fa fa-microphone-slash"></i>
                    </button>
                    <button class="quick-action-btn show-participants" title="显示参与者">
                        <i class="fa fa-users"></i>
                    </button>
                `;
                
                this.el.appendChild(this.quickActionsContainer);
                
                // 绑定快捷操作事件
                this.bindQuickActionEvents();
            },
            
            removeQuickActionButtons() {
                if (this.quickActionsContainer) {
                    this.quickActionsContainer.remove();
                    this.quickActionsContainer = null;
                }
            },
            
            bindQuickActionEvents() {
                const joinBtn = this.quickActionsContainer.querySelector('.join-call');
                const muteBtn = this.quickActionsContainer.querySelector('.mute-all');
                const participantsBtn = this.quickActionsContainer.querySelector('.show-participants');
                
                joinBtn.addEventListener('click', (event) => {
                    event.stopPropagation();
                    this.store.rtc.toggleCall(this.thread);
                });
                
                muteBtn.addEventListener('click', (event) => {
                    event.stopPropagation();
                    this.muteAllParticipants();
                });
                
                participantsBtn.addEventListener('click', (event) => {
                    event.stopPropagation();
                    this.showParticipantsList();
                });
            },
            
            showCallContextMenu(event) {
                event.preventDefault();
                
                const menu = document.createElement('div');
                menu.className = 'call-context-menu';
                menu.innerHTML = this.getCallContextMenuContent();
                
                // 定位菜单
                menu.style.position = 'fixed';
                menu.style.left = `${event.clientX}px`;
                menu.style.top = `${event.clientY}px`;
                menu.style.zIndex = '1001';
                
                document.body.appendChild(menu);
                
                // 绑定菜单事件
                this.bindCallContextMenuEvents(menu);
            },
            
            getCallContextMenuContent() {
                const thread = this.thread;
                const hasCall = thread.rtcSessions.length > 0;
                const selfInCall = this.store.rtc.selfSession?.in(thread.rtcSessions);
                
                let content = '';
                
                if (hasCall) {
                    if (selfInCall) {
                        content += '<div class="menu-item" data-action="leave_call">离开通话</div>';
                        content += '<div class="menu-item" data-action="toggle_mute">切换静音</div>';
                        content += '<div class="menu-item" data-action="toggle_video">切换视频</div>';
                    } else {
                        content += '<div class="menu-item" data-action="join_call">加入通话</div>';
                    }
                    content += '<div class="menu-item" data-action="show_participants">显示参与者</div>';
                } else {
                    content += '<div class="menu-item" data-action="start_call">开始通话</div>';
                    content += '<div class="menu-item" data-action="start_video_call">开始视频通话</div>';
                }
                
                content += '<div class="menu-separator"></div>';
                content += '<div class="menu-item" data-action="call_settings">通话设置</div>';
                
                return content;
            },
            
            bindCallContextMenuEvents(menu) {
                const menuItems = menu.querySelectorAll('.menu-item');
                
                menuItems.forEach(item => {
                    item.addEventListener('click', () => {
                        const action = item.dataset.action;
                        this.handleCallContextMenuAction(action);
                        menu.remove();
                    });
                });
                
                // 点击外部关闭菜单
                const closeMenu = (e) => {
                    if (!menu.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                };
                
                setTimeout(() => {
                    document.addEventListener('click', closeMenu);
                }, 0);
            },
            
            handleCallContextMenuAction(action) {
                const thread = this.thread;
                const rtc = this.store.rtc;
                
                switch (action) {
                    case 'join_call':
                    case 'start_call':
                        rtc.toggleCall(thread);
                        break;
                        
                    case 'start_video_call':
                        rtc.toggleCall(thread, { video: true });
                        break;
                        
                    case 'leave_call':
                        rtc.leaveCall(thread);
                        break;
                        
                    case 'toggle_mute':
                        rtc.toggleAudio();
                        break;
                        
                    case 'toggle_video':
                        rtc.toggleVideo();
                        break;
                        
                    case 'show_participants':
                        this.showParticipantsList();
                        break;
                        
                    case 'call_settings':
                        this.showCallSettings();
                        break;
                }
            },
            
            get attClass() {
                const baseClass = super.attClass;
                
                return {
                    ...baseClass,
                    "o-hovered": this.interactionState.isHovered,
                    "o-pressed": this.interactionState.isPressed,
                    "o-has-quick-actions": this.interactionState.showQuickActions
                };
            }
        };
        
        patch(DiscussSidebarChannel.prototype, InteractionPatch);
    }
};

// 应用交互增强
InteractionEnhancer.enhanceInteractions();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 组件集成
- 无缝组件集成
- 扩展现有组件集合
- 保持组件层次结构

### 3. 样式管理
- 动态样式类添加
- 状态驱动样式更新
- CSS类命名规范

### 4. 视觉增强
- 通话状态可视化
- 边框显示增强
- 用户参与状态指示

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 装饰器模式 (Decorator Pattern)
- 功能装饰和增强
- 透明的功能扩展

### 3. 状态模式 (State Pattern)
- 状态驱动的样式变化
- 动态行为调整

## 注意事项

1. **样式冲突**: 避免与原有样式类冲突
2. **性能影响**: 最小化样式计算开销
3. **兼容性**: 确保与原有组件的兼容性
4. **可维护性**: 保持补丁代码的可维护性

## 扩展建议

1. **更多状态**: 添加更多通话状态的可视化
2. **动画效果**: 增强状态变化的动画效果
3. **主题支持**: 支持多种视觉主题
4. **可配置性**: 支持用户自定义样式配置
5. **无障碍**: 增强无障碍访问支持

该补丁为视频通话系统提供了完整的侧边栏界面集成，是用户界面增强的重要组成部分。

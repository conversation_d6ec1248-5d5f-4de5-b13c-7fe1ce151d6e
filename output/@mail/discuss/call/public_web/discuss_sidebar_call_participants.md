# Discuss Sidebar Call Participants - 讨论侧边栏通话参与者

## 概述

`discuss_sidebar_call_participants.js` 实现了 Odoo 讨论应用中侧边栏的通话参与者组件，用于在讨论侧边栏中显示和管理通话参与者。该组件支持参与者列表显示、悬停交互、下拉菜单、紧凑模式、参与者排序等功能，集成了通话操作注册表和RTC服务，提供了完整的参与者管理界面，是视频通话系统中参与者管理的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/public_web/discuss_sidebar_call_participants.js`
- **行数**: 80
- **模块**: `@mail/discuss/call/public_web/discuss_sidebar_call_participants`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/utils/hooks'                        // Web 核心钩子
'@mail/core/common/thread_model'               // 线程模型
'@mail/discuss/call/common/call_actions'       // 通话操作
'@mail/utils/common/hooks'                     // 邮件工具钩子
'@web/core/dropdown/dropdown_hooks'            // 下拉菜单钩子
'@web/core/dropdown/dropdown'                  // 下拉菜单组件
```

## 组件定义

### DiscussSidebarCallParticipants 类

```javascript
class DiscussSidebarCallParticipants extends Component {
    static template = "mail.DiscussSidebarCallParticipants";
    static props = {
        thread: { type: Thread },
        compact: { type: Boolean, optional: true }
    };
    static components = { DiscussSidebarCallParticipants, Dropdown };
}
```

**组件特性**:
- 继承自Component基类
- 使用专用模板
- 接收线程和紧凑模式属性
- 集成下拉菜单组件

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.rtc = useState(useService("discuss.rtc"));
    this.hover = useHover(["root", "floating*"], {
        onHover: () => (this.floating.isOpen = true),
        onAway: () => (this.floating.isOpen = false),
    });
    this.floating = useDropdownState();
}
```

**初始化内容**:
- 邮件存储服务集成
- RTC服务状态管理
- 悬停交互设置
- 下拉菜单状态管理

## 核心功能

### 1. 通话操作注册表

```javascript
get callActionsRegistry() {
    return callActionsRegistry;
}
```

**注册表功能**:
- 提供通话操作访问
- 支持动态操作扩展
- 集成操作管理系统

### 2. 紧凑模式控制

```javascript
get compact() {
    if (typeof this.props.compact === "boolean") {
        return this.props.compact;
    }
    return this.store.discuss.isSidebarCompact;
}
```

**紧凑模式功能**:
- 属性优先级控制
- 全局设置回退
- 动态显示调整

### 3. 最后活跃会话

```javascript
get lastActiveSession() {
    const sessions = [...this.props.thread.rtcSessions];
    sessions?.sort((s1, s2) => {
        if (s1.isActuallyTalking && !s2.isActuallyTalking) {
            return -1;
        }
        if (!s1.isActuallyTalking && s2.isActuallyTalking) {
            return 1;
        }
        if (s1.isVideoStreaming && !s2.isVideoStreaming) {
            return -1;
        }
        if (!s1.isVideoStreaming && s2.isVideoStreaming) {
            return 1;
        }
        return s2.talkingTime - s1.talkingTime;
    });
    return sessions[0];
}
```

**排序优先级**:
- **正在说话**: 最高优先级
- **视频流**: 次高优先级
- **说话时间**: 按时间长短排序

### 4. 会话排序

```javascript
get sessions() {
    const sessions = [...this.props.thread.rtcSessions];
    return sessions.sort((s1, s2) => {
        return (
            s1.channelMember?.persona?.nameOrDisplayName?.localeCompare(
                s2.channelMember?.persona?.nameOrDisplayName
            ) ?? 1
        );
    });
}
```

**排序功能**:
- 按用户名字母排序
- 本地化排序支持
- 空值处理机制

## 使用场景

### 1. 参与者管理增强

```javascript
// 参与者管理增强功能
const ParticipantManagerEnhancer = {
    enhanceParticipantManager: () => {
        const EnhancedParticipantManager = class extends DiscussSidebarCallParticipants {
            setup() {
                super.setup();

                // 添加增强状态
                this.participantState = useState({
                    selectedParticipants: new Set(),
                    filterText: '',
                    sortBy: 'name',
                    showOffline: false,
                    groupByRole: false
                });

                // 设置参与者监控
                this.setupParticipantMonitoring();

                // 设置批量操作
                this.setupBatchOperations();

                // 设置搜索过滤
                this.setupSearchFilter();
            },

            // 设置参与者监控
            setupParticipantMonitoring() {
                // 监听参与者变化
                this.props.thread.rtcSessions.addEventListener('change', () => {
                    this.updateParticipantStats();
                });

                // 定期更新参与者状态
                this.participantUpdateInterval = setInterval(() => {
                    this.updateParticipantActivity();
                }, 5000);

                onWillUnmount(() => {
                    if (this.participantUpdateInterval) {
                        clearInterval(this.participantUpdateInterval);
                    }
                });
            },

            // 更新参与者统计
            updateParticipantStats() {
                const sessions = this.props.thread.rtcSessions || [];

                this.participantStats = {
                    total: sessions.length,
                    talking: sessions.filter(s => s.isActuallyTalking).length,
                    muted: sessions.filter(s => s.isMute).length,
                    video: sessions.filter(s => s.isVideoStreaming).length,
                    screenShare: sessions.filter(s => s.isScreenSharingOn).length
                };

                // 触发统计更新事件
                this.trigger('participant_stats_updated', this.participantStats);
            },

            // 更新参与者活动
            updateParticipantActivity() {
                const sessions = this.props.thread.rtcSessions || [];

                sessions.forEach(session => {
                    // 更新最后活动时间
                    if (session.isActuallyTalking) {
                        session.lastTalkingTime = Date.now();
                    }

                    // 检查连接质量
                    this.checkConnectionQuality(session);
                });
            },

            // 检查连接质量
            checkConnectionQuality(session) {
                const quality = session.connectionQuality;

                if (quality === 'poor') {
                    this.notifyPoorConnection(session);
                }
            },

            // 通知连接质量差
            notifyPoorConnection(session) {
                const userName = session.channelMember?.persona?.name || '未知用户';

                this.env.services.notification.add(
                    `${userName} 的连接质量较差`,
                    { type: 'warning' }
                );
            },

            // 设置批量操作
            setupBatchOperations() {
                this.batchOperations = {
                    muteAll: () => this.muteAllParticipants(),
                    unmuteAll: () => this.unmuteAllParticipants(),
                    removeSelected: () => this.removeSelectedParticipants(),
                    promoteSelected: () => this.promoteSelectedParticipants()
                };
            },

            // 静音所有参与者
            async muteAllParticipants() {
                const sessions = this.props.thread.rtcSessions || [];
                const promises = [];

                sessions.forEach(session => {
                    if (!session.isSelf && !session.isMute) {
                        promises.push(this.muteParticipant(session));
                    }
                });

                try {
                    await Promise.all(promises);
                    this.showNotification('已静音所有参与者', 'success');
                } catch (error) {
                    this.showNotification('批量静音失败', 'error');
                }
            },

            // 取消静音所有参与者
            async unmuteAllParticipants() {
                const sessions = this.props.thread.rtcSessions || [];
                const promises = [];

                sessions.forEach(session => {
                    if (!session.isSelf && session.isMute) {
                        promises.push(this.unmuteParticipant(session));
                    }
                });

                try {
                    await Promise.all(promises);
                    this.showNotification('已取消静音所有参与者', 'success');
                } catch (error) {
                    this.showNotification('批量取消静音失败', 'error');
                }
            },

            // 静音参与者
            async muteParticipant(session) {
                try {
                    await rpc('/mail/rtc/mute_participant', {
                        session_id: session.id
                    });
                } catch (error) {
                    throw new Error(`静音 ${session.channelMember?.persona?.name} 失败`);
                }
            },

            // 取消静音参与者
            async unmuteParticipant(session) {
                try {
                    await rpc('/mail/rtc/unmute_participant', {
                        session_id: session.id
                    });
                } catch (error) {
                    throw new Error(`取消静音 ${session.channelMember?.persona?.name} 失败`);
                }
            },

            // 移除选中的参与者
            async removeSelectedParticipants() {
                const selectedSessions = Array.from(this.participantState.selectedParticipants);

                if (selectedSessions.length === 0) {
                    this.showNotification('请先选择要移除的参与者', 'warning');
                    return;
                }

                const confirmed = confirm(`确定要移除 ${selectedSessions.length} 位参与者吗？`);
                if (!confirmed) return;

                try {
                    const promises = selectedSessions.map(sessionId =>
                        this.removeParticipant(sessionId)
                    );

                    await Promise.all(promises);
                    this.participantState.selectedParticipants.clear();
                    this.showNotification('已移除选中的参与者', 'success');
                } catch (error) {
                    this.showNotification('移除参与者失败', 'error');
                }
            },

            // 移除参与者
            async removeParticipant(sessionId) {
                try {
                    await rpc('/mail/rtc/remove_participant', {
                        session_id: sessionId
                    });
                } catch (error) {
                    throw new Error(`移除参与者失败: ${error.message}`);
                }
            },

            // 设置搜索过滤
            setupSearchFilter() {
                this.searchFilter = {
                    filterParticipants: (sessions, filterText) => {
                        if (!filterText) return sessions;

                        const lowerFilter = filterText.toLowerCase();
                        return sessions.filter(session => {
                            const name = session.channelMember?.persona?.nameOrDisplayName || '';
                            return name.toLowerCase().includes(lowerFilter);
                        });
                    },

                    sortParticipants: (sessions, sortBy) => {
                        switch (sortBy) {
                            case 'name':
                                return this.sortByName(sessions);
                            case 'activity':
                                return this.sortByActivity(sessions);
                            case 'role':
                                return this.sortByRole(sessions);
                            case 'quality':
                                return this.sortByQuality(sessions);
                            default:
                                return sessions;
                        }
                    }
                };
            },

            // 按活动排序
            sortByActivity(sessions) {
                return sessions.sort((s1, s2) => {
                    // 正在说话的优先
                    if (s1.isActuallyTalking !== s2.isActuallyTalking) {
                        return s2.isActuallyTalking - s1.isActuallyTalking;
                    }

                    // 按最后说话时间排序
                    const t1 = s1.lastTalkingTime || 0;
                    const t2 = s2.lastTalkingTime || 0;
                    return t2 - t1;
                });
            },

            // 按角色排序
            sortByRole(sessions) {
                const roleOrder = { admin: 3, moderator: 2, member: 1, guest: 0 };

                return sessions.sort((s1, s2) => {
                    const role1 = s1.channelMember?.role || 'guest';
                    const role2 = s2.channelMember?.role || 'guest';

                    const order1 = roleOrder[role1] || 0;
                    const order2 = roleOrder[role2] || 0;

                    return order2 - order1;
                });
            },

            // 按连接质量排序
            sortByQuality(sessions) {
                const qualityOrder = { excellent: 4, good: 3, fair: 2, poor: 1, unknown: 0 };

                return sessions.sort((s1, s2) => {
                    const q1 = qualityOrder[s1.connectionQuality] || 0;
                    const q2 = qualityOrder[s2.connectionQuality] || 0;

                    return q2 - q1;
                });
            },

            // 获取过滤后的会话
            get filteredSessions() {
                let sessions = [...(this.props.thread.rtcSessions || [])];

                // 应用搜索过滤
                sessions = this.searchFilter.filterParticipants(
                    sessions,
                    this.participantState.filterText
                );

                // 应用排序
                sessions = this.searchFilter.sortParticipants(
                    sessions,
                    this.participantState.sortBy
                );

                return sessions;
            },

            // 显示通知
            showNotification(message, type = 'info') {
                this.env.services.notification.add(message, { type });
            }
        };

        // 替换原始组件
        __exports.DiscussSidebarCallParticipants = EnhancedParticipantManager;
    }
};

// 应用参与者管理增强
ParticipantManagerEnhancer.enhanceParticipantManager();
```
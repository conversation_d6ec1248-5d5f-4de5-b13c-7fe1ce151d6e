# RTC Service - 实时通信服务

## 概述

`rtc_service.js` 实现了 Odoo 讨论应用中的核心实时通信（RTC）服务，负责管理WebRTC连接、媒体流处理、通话状态管理等功能。该服务支持点对点（P2P）和服务器中转两种连接模式，集成了背景模糊、媒体监控、SFU客户端等高级功能，提供了完整的视频通话基础设施，是整个通话系统的核心引擎。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/rtc_service.js`
- **行数**: 1439
- **模块**: `@mail/discuss/call/common/rtc_service`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'                    // 记录模型
'@mail/discuss/call/common/blur_manager'      // 背景模糊管理器
'@mail/discuss/call/common/media_monitoring'  // 媒体监控
'@web/core/network/rpc'                      // RPC通信
'@mail/utils/common/misc'                    // 通用工具
'@odoo/owl'                                  // OWL 框架
'@web/core/browser/browser'                  // 浏览器服务
'@web/core/l10n/translation'                 // 国际化
'@web/core/registry'                         // 注册表系统
'@web/core/utils/timing'                     // 时间工具
'@web/core/assets'                           // 资源加载
'@web/core/utils/functions'                  // 函数工具
'@mail/discuss/call/common/call_actions'     // 通话操作
```

## 核心常量

### 连接类型

```javascript
const CONNECTION_TYPES = { P2P: "p2p", SERVER: "server" };
```

### 媒体配置

```javascript
const SCREEN_CONFIG = {
    width: { max: 1920 },
    height: { max: 1080 },
    aspectRatio: 16 / 9,
    frameRate: { max: 24 },
};

const CAMERA_CONFIG = {
    width: { max: 1280 },
    height: { max: 720 },
    aspectRatio: 16 / 9,
    frameRate: { max: 30 },
};
```

### 兼容性检查

```javascript
const IS_CLIENT_RTC_COMPATIBLE = Boolean(window.RTCPeerConnection && window.MediaStream);
```

## 核心功能

### 1. SFU资源加载

```javascript
const loadSfuAssets = memoize(async () => await loadBundle("mail.assets_odoo_sfu"));
```

**功能特性**:
- 异步加载SFU客户端资源
- 使用memoize缓存加载结果
- 支持动态资源加载

### 2. RTC服务类

```javascript
class RtcService extends Record {
    // 服务实现...
}
```

**服务特性**:
- 继承自Record模型
- 管理WebRTC连接
- 处理媒体流
- 状态同步

## 使用场景

### 1. 视频通话初始化

```javascript
// 视频通话初始化管理
const VideoCallInitializer = {
    initializeCall: async (thread, options = {}) => {
        try {
            // 检查浏览器兼容性
            if (!IS_CLIENT_RTC_COMPATIBLE) {
                throw new Error('浏览器不支持WebRTC功能');
            }
            
            // 获取用户媒体权限
            const mediaConstraints = VideoCallInitializer.buildMediaConstraints(options);
            const userMedia = await navigator.mediaDevices.getUserMedia(mediaConstraints);
            
            // 初始化RTC服务
            const rtcService = VideoCallInitializer.createRtcService(thread, options);
            
            // 设置本地流
            await rtcService.setLocalStream(userMedia);
            
            // 加入通话
            await rtcService.joinCall(thread.id);
            
            return {
                rtcService,
                localStream: userMedia,
                thread
            };
        } catch (error) {
            console.error('视频通话初始化失败:', error);
            VideoCallInitializer.handleInitError(error);
            throw error;
        }
    },
    
    buildMediaConstraints: (options) => {
        const constraints = {
            audio: {
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
                sampleRate: 48000,
                channelCount: 1
            },
            video: false
        };
        
        if (options.enableVideo) {
            constraints.video = {
                ...CAMERA_CONFIG,
                facingMode: options.facingMode || 'user'
            };
        }
        
        if (options.audioOnly) {
            constraints.video = false;
        }
        
        return constraints;
    },
    
    createRtcService: (thread, options) => {
        const serviceConfig = {
            connectionType: options.connectionType || CONNECTION_TYPES.SERVER,
            enableBackgroundBlur: options.enableBackgroundBlur || false,
            enableMediaMonitoring: options.enableMediaMonitoring !== false,
            iceServers: options.iceServers || VideoCallInitializer.getDefaultIceServers()
        };
        
        return new RtcService(thread, serviceConfig);
    },
    
    getDefaultIceServers: () => {
        return [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' }
        ];
    },
    
    handleInitError: (error) => {
        let userMessage = '初始化视频通话失败';
        
        if (error.name === 'NotAllowedError') {
            userMessage = '请允许访问摄像头和麦克风';
        } else if (error.name === 'NotFoundError') {
            userMessage = '未找到摄像头或麦克风设备';
        } else if (error.name === 'NotReadableError') {
            userMessage = '摄像头或麦克风被其他应用占用';
        } else if (error.name === 'OverconstrainedError') {
            userMessage = '设备不支持请求的媒体配置';
        }
        
        showNotification(userMessage, 'error');
    }
};
```

### 2. 媒体流管理

```javascript
// 媒体流管理
const MediaStreamManager = {
    setupLocalStream: async (rtcService, constraints) => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            
            // 设置音频监控
            if (stream.getAudioTracks().length > 0) {
                MediaStreamManager.setupAudioMonitoring(stream, rtcService);
            }
            
            // 设置视频处理
            if (stream.getVideoTracks().length > 0) {
                await MediaStreamManager.setupVideoProcessing(stream, rtcService);
            }
            
            return stream;
        } catch (error) {
            console.error('获取本地媒体流失败:', error);
            throw error;
        }
    },
    
    setupAudioMonitoring: (stream, rtcService) => {
        const audioTrack = stream.getAudioTracks()[0];
        
        // 创建音频监控
        const audioMonitor = monitorAudio(stream, {
            onVolumeChange: (volume) => {
                rtcService.updateAudioLevel(volume);
            },
            onSpeakingChange: (isSpeaking) => {
                rtcService.updateSpeakingState(isSpeaking);
            }
        });
        
        // 监听音轨状态变化
        audioTrack.addEventListener('ended', () => {
            console.warn('音频轨道已结束');
            MediaStreamManager.handleAudioTrackEnded(rtcService);
        });
        
        audioTrack.addEventListener('mute', () => {
            rtcService.updateMuteState(true);
        });
        
        audioTrack.addEventListener('unmute', () => {
            rtcService.updateMuteState(false);
        });
        
        return audioMonitor;
    },
    
    setupVideoProcessing: async (stream, rtcService) => {
        const videoTrack = stream.getVideoTracks()[0];
        
        // 设置背景模糊（如果启用）
        if (rtcService.config.enableBackgroundBlur) {
            const blurManager = new BlurManager(stream, {
                backgroundBlur: rtcService.settings.backgroundBlur || 10,
                edgeBlur: rtcService.settings.edgeBlur || 5
            });
            
            const blurredStream = await blurManager.stream;
            rtcService.setBlurManager(blurManager);
            
            return blurredStream;
        }
        
        // 监听视频轨道状态
        videoTrack.addEventListener('ended', () => {
            console.warn('视频轨道已结束');
            MediaStreamManager.handleVideoTrackEnded(rtcService);
        });
        
        return stream;
    },
    
    handleAudioTrackEnded: (rtcService) => {
        // 尝试重新获取音频
        MediaStreamManager.restartAudio(rtcService);
    },
    
    handleVideoTrackEnded: (rtcService) => {
        // 尝试重新获取视频
        MediaStreamManager.restartVideo(rtcService);
    },
    
    restartAudio: async (rtcService) => {
        try {
            const audioStream = await navigator.mediaDevices.getUserMedia({ 
                audio: true, 
                video: false 
            });
            
            await rtcService.replaceAudioTrack(audioStream.getAudioTracks()[0]);
            showNotification('音频已恢复', 'success');
        } catch (error) {
            console.error('重启音频失败:', error);
            showNotification('音频恢复失败', 'error');
        }
    },
    
    restartVideo: async (rtcService) => {
        try {
            const videoStream = await navigator.mediaDevices.getUserMedia({ 
                audio: false, 
                video: CAMERA_CONFIG 
            });
            
            await rtcService.replaceVideoTrack(videoStream.getVideoTracks()[0]);
            showNotification('视频已恢复', 'success');
        } catch (error) {
            console.error('重启视频失败:', error);
            showNotification('视频恢复失败', 'error');
        }
    },
    
    switchCamera: async (rtcService) => {
        try {
            const currentTrack = rtcService.localVideoTrack;
            const currentFacingMode = currentTrack?.getSettings().facingMode;
            const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
            
            const newStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    ...CAMERA_CONFIG,
                    facingMode: newFacingMode
                }
            });
            
            await rtcService.replaceVideoTrack(newStream.getVideoTracks()[0]);
            
            // 关闭旧轨道
            if (currentTrack) {
                currentTrack.stop();
            }
            
            showNotification('摄像头已切换', 'success');
        } catch (error) {
            console.error('切换摄像头失败:', error);
            showNotification('切换摄像头失败', 'error');
        }
    },
    
    startScreenShare: async (rtcService) => {
        try {
            const screenStream = await navigator.mediaDevices.getDisplayMedia({
                video: SCREEN_CONFIG,
                audio: true
            });
            
            await rtcService.setScreenShareStream(screenStream);
            
            // 监听屏幕共享结束
            screenStream.getVideoTracks()[0].addEventListener('ended', () => {
                MediaStreamManager.stopScreenShare(rtcService);
            });
            
            showNotification('屏幕共享已开始', 'success');
            return screenStream;
        } catch (error) {
            console.error('开始屏幕共享失败:', error);
            showNotification('屏幕共享失败', 'error');
            throw error;
        }
    },
    
    stopScreenShare: (rtcService) => {
        rtcService.stopScreenShare();
        showNotification('屏幕共享已结束', 'info');
    }
};
```

### 3. 连接管理

```javascript
// WebRTC连接管理
const ConnectionManager = {
    createPeerConnection: (rtcService, remoteSessionId) => {
        const config = {
            iceServers: rtcService.config.iceServers,
            iceCandidatePoolSize: 10,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require'
        };
        
        const peerConnection = new RTCPeerConnection(config);
        
        // 绑定事件处理器
        ConnectionManager.bindPeerConnectionEvents(peerConnection, rtcService, remoteSessionId);
        
        return peerConnection;
    },
    
    bindPeerConnectionEvents: (pc, rtcService, remoteSessionId) => {
        // ICE候选者事件
        pc.addEventListener('icecandidate', (event) => {
            if (event.candidate) {
                rtcService.sendIceCandidate(remoteSessionId, event.candidate);
            }
        });
        
        // ICE连接状态变化
        pc.addEventListener('iceconnectionstatechange', () => {
            console.log(`ICE连接状态: ${pc.iceConnectionState}`);
            ConnectionManager.handleIceConnectionStateChange(pc, rtcService, remoteSessionId);
        });
        
        // 连接状态变化
        pc.addEventListener('connectionstatechange', () => {
            console.log(`连接状态: ${pc.connectionState}`);
            ConnectionManager.handleConnectionStateChange(pc, rtcService, remoteSessionId);
        });
        
        // 远程流接收
        pc.addEventListener('track', (event) => {
            console.log('接收到远程轨道:', event.track.kind);
            ConnectionManager.handleRemoteTrack(event, rtcService, remoteSessionId);
        });
        
        // 数据通道接收
        pc.addEventListener('datachannel', (event) => {
            console.log('接收到数据通道:', event.channel.label);
            ConnectionManager.handleDataChannel(event.channel, rtcService, remoteSessionId);
        });
    },
    
    handleIceConnectionStateChange: (pc, rtcService, remoteSessionId) => {
        switch (pc.iceConnectionState) {
            case 'connected':
                console.log(`与 ${remoteSessionId} 的ICE连接已建立`);
                rtcService.updateConnectionState(remoteSessionId, 'connected');
                break;
            case 'disconnected':
                console.warn(`与 ${remoteSessionId} 的ICE连接断开`);
                rtcService.updateConnectionState(remoteSessionId, 'disconnected');
                break;
            case 'failed':
                console.error(`与 ${remoteSessionId} 的ICE连接失败`);
                ConnectionManager.handleConnectionFailure(pc, rtcService, remoteSessionId);
                break;
            case 'closed':
                console.log(`与 ${remoteSessionId} 的ICE连接已关闭`);
                rtcService.updateConnectionState(remoteSessionId, 'closed');
                break;
        }
    },
    
    handleConnectionStateChange: (pc, rtcService, remoteSessionId) => {
        switch (pc.connectionState) {
            case 'connected':
                showNotification(`与 ${remoteSessionId} 连接成功`, 'success');
                break;
            case 'disconnected':
                showNotification(`与 ${remoteSessionId} 连接断开`, 'warning');
                break;
            case 'failed':
                showNotification(`与 ${remoteSessionId} 连接失败`, 'error');
                break;
        }
    },
    
    handleRemoteTrack: (event, rtcService, remoteSessionId) => {
        const [stream] = event.streams;
        const track = event.track;
        
        if (track.kind === 'video') {
            rtcService.setRemoteVideoStream(remoteSessionId, stream);
        } else if (track.kind === 'audio') {
            rtcService.setRemoteAudioStream(remoteSessionId, stream);
        }
        
        // 监听轨道结束
        track.addEventListener('ended', () => {
            console.log(`远程${track.kind}轨道结束:`, remoteSessionId);
            rtcService.handleRemoteTrackEnded(remoteSessionId, track.kind);
        });
    },
    
    handleDataChannel: (channel, rtcService, remoteSessionId) => {
        channel.addEventListener('open', () => {
            console.log(`数据通道已打开: ${channel.label}`);
            rtcService.setDataChannel(remoteSessionId, channel);
        });
        
        channel.addEventListener('message', (event) => {
            console.log(`收到数据通道消息:`, event.data);
            rtcService.handleDataChannelMessage(remoteSessionId, event.data);
        });
        
        channel.addEventListener('close', () => {
            console.log(`数据通道已关闭: ${channel.label}`);
            rtcService.removeDataChannel(remoteSessionId);
        });
        
        channel.addEventListener('error', (error) => {
            console.error(`数据通道错误:`, error);
        });
    },
    
    handleConnectionFailure: async (pc, rtcService, remoteSessionId) => {
        console.log(`尝试重新连接到 ${remoteSessionId}`);
        
        try {
            // 重启ICE
            await pc.restartIce();
            
            // 如果重启失败，尝试重新创建连接
            setTimeout(() => {
                if (pc.iceConnectionState === 'failed') {
                    rtcService.recreateConnection(remoteSessionId);
                }
            }, 5000);
        } catch (error) {
            console.error('重新连接失败:', error);
            rtcService.removeConnection(remoteSessionId);
        }
    }
};
```

## 技术特点

### 1. WebRTC集成
- 完整的WebRTC API封装
- 支持P2P和SFU模式
- ICE候选者管理

### 2. 媒体处理
- 音视频流管理
- 背景模糊集成
- 屏幕共享支持

### 3. 状态管理
- 实时状态同步
- 连接状态监控
- 错误恢复机制

### 4. 性能优化
- 资源懒加载
- 内存管理
- 连接复用

## 设计模式

### 1. 服务模式 (Service Pattern)
- 单例RTC服务
- 统一的服务接口

### 2. 观察者模式 (Observer Pattern)
- 事件驱动架构
- 状态变化通知

### 3. 工厂模式 (Factory Pattern)
- 连接对象创建
- 配置对象生成

## 注意事项

1. **浏览器兼容性**: WebRTC API的兼容性处理
2. **网络环境**: NAT穿透和防火墙问题
3. **性能影响**: 大量连接时的性能优化
4. **安全考虑**: 媒体流的安全传输

## 扩展建议

1. **AI增强**: 集成AI降噪和美颜
2. **网络自适应**: 根据网络状况调整质量
3. **录制功能**: 支持通话录制
4. **统计分析**: 通话质量统计和分析
5. **移动优化**: 移动端的特殊优化

该服务是整个视频通话系统的核心引擎，提供了完整的WebRTC基础设施。

# Discuss Call Settings Client Action - 讨论通话设置客户端操作

## 概述

`discuss_call_settings_client_action.js` 实现了 Odoo 讨论应用中通话设置的客户端操作组件，作为独立的客户端操作页面提供通话配置功能。该组件集成了CallSettings组件，注册为系统操作，支持独立页面访问、完整设置界面、系统级配置等功能，提供了专门的通话设置管理界面，是视频通话系统中设置管理的重要入口。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/discuss_call_settings_client_action.js`
- **行数**: 23
- **模块**: `@mail/discuss/call/common/discuss_call_settings_client_action`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/call/common/call_settings'  // 通话设置组件

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/registry'                       // 注册表系统
```

## 组件定义

### DiscussCallSettingsClientAction 类

```javascript
class DiscussCallSettingsClientAction extends Component {
    static components = { CallSettings };
    static props = ["*"];
    static template = "mail.DiscussCallSettingsClientAction";
}
```

**组件特性**:
- 继承自Component基类
- 集成CallSettings子组件
- 支持任意属性传递
- 使用专用模板

## 操作注册

### 客户端操作注册

```javascript
registry
    .category("actions")
    .add("mail.discuss_call_settings_action", DiscussCallSettingsClientAction);
```

**注册特性**:
- 注册到操作注册表
- 系统级操作访问
- 独立页面入口

## 使用场景

### 1. 设置页面增强

```javascript
// 设置页面增强功能
const SettingsPageEnhancer = {
    enhanceSettingsPage: () => {
        const EnhancedSettingsAction = class extends DiscussCallSettingsClientAction {
            setup() {
                super.setup();
                
                // 添加页面状态管理
                this.pageState = useState({
                    currentTab: 'general',
                    hasUnsavedChanges: false,
                    isLoading: false,
                    errors: []
                });
                
                // 添加导航管理
                this.navigationManager = SettingsPageEnhancer.createNavigationManager(this);
                
                // 添加数据管理
                this.dataManager = SettingsPageEnhancer.createDataManager(this);
                
                // 监听设置变化
                this.addEventListener('settings_changed', this.handleSettingsChange.bind(this));
                
                // 页面离开确认
                this.setupPageLeaveConfirmation();
            },
            
            handleSettingsChange(event) {
                this.pageState.hasUnsavedChanges = true;
                this.dataManager.markDirty(event.detail.setting, event.detail.value);
            },
            
            setupPageLeaveConfirmation() {
                window.addEventListener('beforeunload', (event) => {
                    if (this.pageState.hasUnsavedChanges) {
                        event.preventDefault();
                        event.returnValue = '您有未保存的更改，确定要离开吗？';
                        return event.returnValue;
                    }
                });
            },
            
            async saveAllSettings() {
                this.pageState.isLoading = true;
                this.pageState.errors = [];
                
                try {
                    await this.dataManager.saveAll();
                    this.pageState.hasUnsavedChanges = false;
                    showNotification('设置已保存', 'success');
                } catch (error) {
                    console.error('保存设置失败:', error);
                    this.pageState.errors.push(error.message);
                    showNotification('保存设置失败', 'error');
                } finally {
                    this.pageState.isLoading = false;
                }
            },
            
            async resetAllSettings() {
                const confirmed = confirm('确定要重置所有设置吗？此操作不可撤销。');
                if (!confirmed) return;
                
                this.pageState.isLoading = true;
                
                try {
                    await this.dataManager.resetAll();
                    this.pageState.hasUnsavedChanges = false;
                    showNotification('设置已重置', 'success');
                } catch (error) {
                    console.error('重置设置失败:', error);
                    showNotification('重置设置失败', 'error');
                } finally {
                    this.pageState.isLoading = false;
                }
            }
        };
        
        // 替换原有注册
        registry.category("actions").add("mail.discuss_call_settings_action", EnhancedSettingsAction);
    },
    
    createNavigationManager: (component) => {
        return {
            tabs: [
                { id: 'general', name: '常规设置', icon: 'fa-cog' },
                { id: 'audio', name: '音频设置', icon: 'fa-volume-up' },
                { id: 'video', name: '视频设置', icon: 'fa-video-camera' },
                { id: 'network', name: '网络设置', icon: 'fa-wifi' },
                { id: 'advanced', name: '高级设置', icon: 'fa-wrench' }
            ],
            
            switchTab(tabId) {
                if (component.pageState.hasUnsavedChanges) {
                    const confirmed = confirm('您有未保存的更改，确定要切换标签吗？');
                    if (!confirmed) return;
                }
                
                component.pageState.currentTab = tabId;
                this.updateTabDisplay(tabId);
            },
            
            updateTabDisplay(activeTabId) {
                const tabElements = document.querySelectorAll('.settings-tab');
                const contentElements = document.querySelectorAll('.settings-content');
                
                tabElements.forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.tabId === activeTabId);
                });
                
                contentElements.forEach(content => {
                    content.classList.toggle('active', content.dataset.tabId === activeTabId);
                });
            },
            
            addBreadcrumb() {
                const breadcrumb = document.createElement('div');
                breadcrumb.className = 'settings-breadcrumb';
                breadcrumb.innerHTML = `
                    <nav class="breadcrumb-nav">
                        <a href="/web#action=mail.action_discuss" class="breadcrumb-item">
                            <i class="fa fa-comments"></i>
                            <span>讨论</span>
                        </a>
                        <span class="breadcrumb-separator">/</span>
                        <span class="breadcrumb-item active">
                            <i class="fa fa-cog"></i>
                            <span>通话设置</span>
                        </span>
                    </nav>
                `;
                
                return breadcrumb;
            }
        };
    },
    
    createDataManager: (component) => {
        return {
            dirtySettings: new Map(),
            originalSettings: new Map(),
            
            markDirty(setting, value) {
                this.dirtySettings.set(setting, value);
            },
            
            isDirty(setting) {
                return this.dirtySettings.has(setting);
            },
            
            async saveAll() {
                const settingsToSave = Array.from(this.dirtySettings.entries());
                
                for (const [setting, value] of settingsToSave) {
                    await this.saveSetting(setting, value);
                }
                
                this.dirtySettings.clear();
            },
            
            async saveSetting(setting, value) {
                try {
                    await rpc('/mail/settings/save', {
                        setting: setting,
                        value: value
                    });
                    
                    this.originalSettings.set(setting, value);
                } catch (error) {
                    throw new Error(`保存设置 ${setting} 失败: ${error.message}`);
                }
            },
            
            async resetAll() {
                const defaultSettings = await this.getDefaultSettings();
                
                for (const [setting, defaultValue] of Object.entries(defaultSettings)) {
                    await this.saveSetting(setting, defaultValue);
                }
                
                this.dirtySettings.clear();
                this.originalSettings.clear();
                
                // 重新加载页面以应用默认设置
                window.location.reload();
            },
            
            async getDefaultSettings() {
                try {
                    const response = await rpc('/mail/settings/defaults');
                    return response.settings;
                } catch (error) {
                    throw new Error('获取默认设置失败');
                }
            },
            
            getChangedSettings() {
                return Array.from(this.dirtySettings.entries()).map(([setting, value]) => ({
                    setting,
                    value,
                    original: this.originalSettings.get(setting)
                }));
            }
        };
    }
};

// 应用设置页面增强
SettingsPageEnhancer.enhanceSettingsPage();
```

### 2. 设置导入导出

```javascript
// 设置导入导出功能
const SettingsImportExport = {
    addImportExportFeatures: () => {
        const ImportExportPatch = {
            setup() {
                super.setup();
                
                // 添加导入导出管理器
                this.importExportManager = SettingsImportExport.createImportExportManager(this);
                
                // 添加导入导出UI
                onMounted(() => {
                    SettingsImportExport.addImportExportUI(this);
                });
            }
        };
        
        patch(DiscussCallSettingsClientAction.prototype, ImportExportPatch);
    },
    
    createImportExportManager: (component) => {
        return {
            async exportSettings() {
                try {
                    const settings = await this.getAllSettings();
                    const exportData = {
                        version: '1.0',
                        timestamp: new Date().toISOString(),
                        settings: settings
                    };
                    
                    this.downloadSettingsFile(exportData);
                    showNotification('设置已导出', 'success');
                } catch (error) {
                    console.error('导出设置失败:', error);
                    showNotification('导出设置失败', 'error');
                }
            },
            
            async importSettings(file) {
                try {
                    const importData = await this.readSettingsFile(file);
                    
                    // 验证导入数据
                    this.validateImportData(importData);
                    
                    // 显示导入预览
                    const confirmed = await this.showImportPreview(importData);
                    if (!confirmed) return;
                    
                    // 应用设置
                    await this.applyImportedSettings(importData.settings);
                    
                    showNotification('设置已导入', 'success');
                    
                    // 重新加载页面
                    window.location.reload();
                } catch (error) {
                    console.error('导入设置失败:', error);
                    showNotification(`导入设置失败: ${error.message}`, 'error');
                }
            },
            
            async getAllSettings() {
                const response = await rpc('/mail/settings/export');
                return response.settings;
            },
            
            downloadSettingsFile(data) {
                const blob = new Blob([JSON.stringify(data, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `odoo_call_settings_${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                
                URL.revokeObjectURL(url);
            },
            
            async readSettingsFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    
                    reader.onload = (event) => {
                        try {
                            const data = JSON.parse(event.target.result);
                            resolve(data);
                        } catch (error) {
                            reject(new Error('无效的设置文件格式'));
                        }
                    };
                    
                    reader.onerror = () => {
                        reject(new Error('读取文件失败'));
                    };
                    
                    reader.readAsText(file);
                });
            },
            
            validateImportData(data) {
                if (!data.version || !data.settings) {
                    throw new Error('无效的设置文件结构');
                }
                
                if (data.version !== '1.0') {
                    throw new Error('不支持的设置文件版本');
                }
                
                // 验证设置项
                const requiredSettings = ['audioInputDevice', 'useBlur', 'logRtc'];
                for (const setting of requiredSettings) {
                    if (!(setting in data.settings)) {
                        console.warn(`缺少设置项: ${setting}`);
                    }
                }
            },
            
            async showImportPreview(data) {
                return new Promise((resolve) => {
                    const dialog = SettingsImportExport.createImportPreviewDialog(data, resolve);
                    document.body.appendChild(dialog);
                });
            },
            
            async applyImportedSettings(settings) {
                for (const [key, value] of Object.entries(settings)) {
                    await rpc('/mail/settings/save', {
                        setting: key,
                        value: value
                    });
                }
            }
        };
    },
    
    createImportPreviewDialog: (data, callback) => {
        const dialog = document.createElement('div');
        dialog.className = 'import-preview-dialog-overlay';
        dialog.innerHTML = `
            <div class="import-preview-dialog">
                <div class="dialog-header">
                    <h3>导入设置预览</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="import-info">
                        <p><strong>文件版本:</strong> ${data.version}</p>
                        <p><strong>导出时间:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                        <p><strong>设置项数量:</strong> ${Object.keys(data.settings).length}</p>
                    </div>
                    
                    <div class="settings-preview">
                        <h4>将要导入的设置:</h4>
                        <div class="settings-list">
                            ${Object.entries(data.settings).map(([key, value]) => `
                                <div class="setting-item">
                                    <span class="setting-key">${key}:</span>
                                    <span class="setting-value">${JSON.stringify(value)}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="import-warning">
                        <i class="fa fa-warning"></i>
                        <span>导入设置将覆盖当前配置，此操作不可撤销。</span>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary cancel-btn">取消</button>
                    <button class="btn btn-primary confirm-btn">确认导入</button>
                </div>
            </div>
        `;
        
        // 绑定事件
        const closeBtn = dialog.querySelector('.close-btn');
        const cancelBtn = dialog.querySelector('.cancel-btn');
        const confirmBtn = dialog.querySelector('.confirm-btn');
        
        const closeDialog = (confirmed = false) => {
            dialog.remove();
            callback(confirmed);
        };
        
        closeBtn.addEventListener('click', () => closeDialog(false));
        cancelBtn.addEventListener('click', () => closeDialog(false));
        confirmBtn.addEventListener('click', () => closeDialog(true));
        
        return dialog;
    },
    
    addImportExportUI: (component) => {
        const settingsHeader = component.el.querySelector('.call-settings-header');
        if (!settingsHeader) return;
        
        const importExportContainer = document.createElement('div');
        importExportContainer.className = 'import-export-controls';
        importExportContainer.innerHTML = `
            <div class="control-group">
                <button class="btn btn-secondary export-btn">
                    <i class="fa fa-download"></i>
                    <span>导出设置</span>
                </button>
                
                <div class="import-control">
                    <input type="file" class="import-input" accept=".json" style="display: none;">
                    <button class="btn btn-secondary import-btn">
                        <i class="fa fa-upload"></i>
                        <span>导入设置</span>
                    </button>
                </div>
            </div>
        `;
        
        settingsHeader.appendChild(importExportContainer);
        
        // 绑定事件
        const exportBtn = importExportContainer.querySelector('.export-btn');
        const importBtn = importExportContainer.querySelector('.import-btn');
        const importInput = importExportContainer.querySelector('.import-input');
        
        exportBtn.addEventListener('click', () => {
            component.importExportManager.exportSettings();
        });
        
        importBtn.addEventListener('click', () => {
            importInput.click();
        });
        
        importInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                component.importExportManager.importSettings(file);
            }
        });
    }
};

// 应用导入导出功能
SettingsImportExport.addImportExportFeatures();
```

### 3. 设置搜索和过滤

```javascript
// 设置搜索和过滤功能
const SettingsSearchFilter = {
    addSearchFeatures: () => {
        const SearchPatch = {
            setup() {
                super.setup();
                
                // 添加搜索状态
                this.searchState = useState({
                    query: '',
                    results: [],
                    isSearching: false
                });
                
                // 添加搜索管理器
                this.searchManager = SettingsSearchFilter.createSearchManager(this);
                
                onMounted(() => {
                    SettingsSearchFilter.addSearchUI(this);
                });
            }
        };
        
        patch(DiscussCallSettingsClientAction.prototype, SearchPatch);
    },
    
    createSearchManager: (component) => {
        return {
            searchableSettings: [
                { key: 'audioInputDevice', name: '音频输入设备', category: 'audio' },
                { key: 'useBlur', name: '背景模糊', category: 'video' },
                { key: 'backgroundBlurAmount', name: '背景模糊强度', category: 'video' },
                { key: 'logRtc', name: 'RTC日志', category: 'advanced' },
                { key: 'showOnlyVideo', name: '仅显示视频', category: 'general' }
            ],
            
            search(query) {
                if (!query.trim()) {
                    return [];
                }
                
                const lowerQuery = query.toLowerCase();
                
                return this.searchableSettings.filter(setting => 
                    setting.name.toLowerCase().includes(lowerQuery) ||
                    setting.key.toLowerCase().includes(lowerQuery) ||
                    setting.category.toLowerCase().includes(lowerQuery)
                );
            },
            
            highlightResults(results) {
                // 清除之前的高亮
                this.clearHighlights();
                
                // 高亮搜索结果
                results.forEach(result => {
                    const settingElement = document.querySelector(`[data-setting="${result.key}"]`);
                    if (settingElement) {
                        settingElement.classList.add('search-highlight');
                        settingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
            },
            
            clearHighlights() {
                const highlighted = document.querySelectorAll('.search-highlight');
                highlighted.forEach(element => {
                    element.classList.remove('search-highlight');
                });
            }
        };
    },
    
    addSearchUI: (component) => {
        const settingsContainer = component.el.querySelector('.call-settings-container');
        if (!settingsContainer) return;
        
        const searchContainer = document.createElement('div');
        searchContainer.className = 'settings-search-container';
        searchContainer.innerHTML = `
            <div class="search-box">
                <div class="search-input-wrapper">
                    <i class="fa fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="搜索设置...">
                    <button class="clear-search-btn" style="display: none;">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                
                <div class="search-results" style="display: none;">
                    <div class="results-header">
                        <span class="results-count">找到 0 个结果</span>
                    </div>
                    <div class="results-list"></div>
                </div>
            </div>
        `;
        
        settingsContainer.insertBefore(searchContainer, settingsContainer.firstChild);
        
        // 绑定搜索事件
        SettingsSearchFilter.bindSearchEvents(searchContainer, component);
    },
    
    bindSearchEvents: (container, component) => {
        const searchInput = container.querySelector('.search-input');
        const clearBtn = container.querySelector('.clear-search-btn');
        const resultsContainer = container.querySelector('.search-results');
        const resultsList = container.querySelector('.results-list');
        const resultsCount = container.querySelector('.results-count');
        
        let searchTimeout;
        
        searchInput.addEventListener('input', (event) => {
            const query = event.target.value;
            component.searchState.query = query;
            
            // 显示/隐藏清除按钮
            clearBtn.style.display = query ? 'block' : 'none';
            
            // 防抖搜索
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                SettingsSearchFilter.performSearch(query, component, resultsContainer, resultsList, resultsCount);
            }, 300);
        });
        
        clearBtn.addEventListener('click', () => {
            searchInput.value = '';
            component.searchState.query = '';
            component.searchState.results = [];
            clearBtn.style.display = 'none';
            resultsContainer.style.display = 'none';
            component.searchManager.clearHighlights();
        });
        
        // 点击外部关闭搜索结果
        document.addEventListener('click', (event) => {
            if (!container.contains(event.target)) {
                resultsContainer.style.display = 'none';
            }
        });
    },
    
    performSearch: (query, component, resultsContainer, resultsList, resultsCount) => {
        if (!query.trim()) {
            resultsContainer.style.display = 'none';
            component.searchManager.clearHighlights();
            return;
        }
        
        component.searchState.isSearching = true;
        
        const results = component.searchManager.search(query);
        component.searchState.results = results;
        
        // 更新结果显示
        resultsCount.textContent = `找到 ${results.length} 个结果`;
        
        resultsList.innerHTML = results.map(result => `
            <div class="result-item" data-setting="${result.key}">
                <div class="result-name">${result.name}</div>
                <div class="result-category">${result.category}</div>
            </div>
        `).join('');
        
        resultsContainer.style.display = 'block';
        
        // 绑定结果点击事件
        resultsList.querySelectorAll('.result-item').forEach(item => {
            item.addEventListener('click', () => {
                const settingKey = item.dataset.setting;
                SettingsSearchFilter.navigateToSetting(settingKey, component);
                resultsContainer.style.display = 'none';
            });
        });
        
        // 高亮结果
        component.searchManager.highlightResults(results);
        
        component.searchState.isSearching = false;
    },
    
    navigateToSetting: (settingKey, component) => {
        const settingElement = document.querySelector(`[data-setting="${settingKey}"]`);
        if (settingElement) {
            // 切换到对应的标签页
            const tabId = settingElement.closest('[data-tab-id]')?.dataset.tabId;
            if (tabId && component.navigationManager) {
                component.navigationManager.switchTab(tabId);
            }
            
            // 滚动到设置项
            setTimeout(() => {
                settingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                settingElement.classList.add('setting-focus');
                
                setTimeout(() => {
                    settingElement.classList.remove('setting-focus');
                }, 2000);
            }, 100);
        }
    }
};

// 应用搜索功能
SettingsSearchFilter.addSearchFeatures();
```

## 技术特点

### 1. 客户端操作
- 独立的页面组件
- 系统级操作注册
- 完整的设置界面

### 2. 组件集成
- CallSettings组件复用
- 模块化设计
- 清晰的职责分离

### 3. 系统集成
- 注册表机制
- 标准操作模式
- 路由访问支持

### 4. 简洁设计
- 最小化实现
- 专注核心功能
- 易于扩展

## 设计模式

### 1. 门面模式 (Facade Pattern)
- 简化的设置访问入口
- 统一的操作界面

### 2. 组合模式 (Composite Pattern)
- 组件的组合和集成
- 功能的统一管理

### 3. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 系统级访问控制

## 注意事项

1. **组件兼容性**: 确保CallSettings组件的正确集成
2. **路由配置**: 正确配置操作路由和访问权限
3. **用户体验**: 提供一致的设置管理体验
4. **数据持久化**: 确保设置的正确保存和加载

## 扩展建议

1. **权限控制**: 添加设置访问权限控制
2. **多语言支持**: 增强国际化支持
3. **主题定制**: 支持设置界面主题定制
4. **快捷访问**: 添加设置快捷访问功能
5. **帮助文档**: 集成设置帮助和说明文档

该组件提供了专门的通话设置管理界面，是视频通话系统设置管理的重要入口。

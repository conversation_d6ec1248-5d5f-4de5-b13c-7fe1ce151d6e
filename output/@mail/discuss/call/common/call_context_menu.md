# Call Context Menu - 通话上下文菜单

## 概述

`call_context_menu.js` 实现了 Odoo 讨论应用中通话参与者的上下文菜单组件，用于显示参与者的详细信息和提供相关操作。该组件支持连接统计信息显示、音量控制、协议类型识别、调试信息展示等功能，集成了RTC服务和SFU客户端统计，提供了完整的参与者管理和诊断界面，是视频通话系统中的重要调试和管理工具。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_context_menu.js`
- **行数**: 158
- **模块**: `@mail/discuss/call/common/call_context_menu`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/browser/browser'                    // 浏览器服务
'@web/core/l10n/translation'                   // 国际化
'@web/core/utils/hooks'                        // Web 核心钩子
'@mail/discuss/call/common/rtc_service'        // RTC服务
```

## 核心常量

### 协议文本映射

```javascript
const PROTOCOLS_TEXT = { 
    host: "HOST", 
    srflx: "STUN", 
    prflx: "STUN", 
    relay: "TURN" 
};
```

**协议类型**:
- **HOST**: 主机直连
- **STUN**: STUN服务器中转
- **TURN**: TURN服务器中继

## 组件定义

### CallContextMenu 类

```javascript
class CallContextMenu extends Component {
    static props = ["rtcSession", "close?"];
    static template = "discuss.CallContextMenu";
}
```

**组件特性**:
- 接收RTC会话对象
- 可选的关闭回调
- 使用自定义模板

## Props 配置

### Props 详细说明

- **`rtcSession`** (必需):
  - 类型: RtcSession 模型实例
  - 用途: 目标RTC会话
  - 功能: 提供会话信息和统计数据

- **`close`** (可选):
  - 类型: 函数
  - 用途: 关闭菜单的回调
  - 功能: 菜单关闭时的处理

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.rtc = useState(useService("discuss.rtc"));
    this.state = useState({
        downloadStats: {},
        uploadStats: {},
        producerStats: {},
        peerStats: {},
    });
    onMounted(() => {
        if (!this.env.debug) {
            return;
        }
        this.updateStats();
        this.updateStatsTimeout = browser.setInterval(() => this.updateStats(), 3000);
    });
    onWillUnmount(() => browser.clearInterval(this.updateStatsTimeout));
}
```

**初始化内容**:
- 邮件存储和RTC服务
- 统计数据状态管理
- 调试模式下的统计更新
- 定时器资源清理

## 核心功能

### 1. 身份识别

```javascript
get isSelf() {
    return this.rtc.selfSession?.eq(this.props.rtcSession);
}
```

**身份判断**:
- 检查是否为当前用户的会话
- 用于区分自己和其他参与者

### 2. 连接类型显示

```javascript
get inboundConnectionTypeText() {
    const candidateType =
        this.rtc.state.connectionType === CONNECTION_TYPES.SERVER
            ? this.state.downloadStats.remoteCandidateType
            : this.state.peerStats.remoteCandidateType;
    return this.formatProtocol(candidateType);
}

get outboundConnectionTypeText() {
    const candidateType =
        this.rtc.state.connectionType === CONNECTION_TYPES.SERVER
            ? this.state.uploadStats.localCandidateType
            : this.state.peerStats.localCandidateType;
    return this.formatProtocol(candidateType);
}
```

**连接类型**:
- **入站连接**: 接收数据的连接类型
- **出站连接**: 发送数据的连接类型
- **服务器模式**: 使用SFU服务器统计
- **P2P模式**: 使用点对点统计

### 3. 音量控制

```javascript
get volume() {
    return this.store.settings.getVolume(this.props.rtcSession);
}

onChangeVolume(ev) {
    const volume = Number(ev.target.value);
    this.store.settings.saveVolumeSetting({
        guestId: this.props.rtcSession?.guestId,
        partnerId: this.props.rtcSession?.partnerId,
        volume,
    });
    this.props.rtcSession.volume = volume;
}
```

**音量管理**:
- 获取当前音量设置
- 保存音量设置到存储
- 实时更新会话音量

### 4. 协议格式化

```javascript
formatProtocol(candidateType) {
    if (!candidateType) {
        return _t("no connection");
    }
    return _t("%(candidateType)s (%(protocol)s)", {
        candidateType,
        protocol: PROTOCOLS_TEXT[candidateType],
    });
}
```

**格式化功能**:
- 将候选类型转换为可读文本
- 显示协议类型说明
- 处理无连接状态

### 5. 统计数据更新

```javascript
async updateStats() {
    if (this.rtc.selfSession?.eq(this.props.rtcSession)) {
        if (this.rtc.sfuClient) {
            // SFU客户端统计处理
            const { uploadStats, downloadStats, ...producerStats } = await this.rtc.sfuClient.getStats();
            // 格式化上传统计...
            // 格式化下载统计...
            // 格式化生产者统计...
        }
        return;
    }
    // P2P统计处理
    this.state.peerStats = await this.rtc.p2pService.getFormattedStats(this.props.rtcSession.id);
}
```

**统计更新**:
- 区分自己和其他参与者
- SFU模式和P2P模式的不同处理
- 定期更新统计数据

## 使用场景

### 1. 调试信息显示

```javascript
// 调试信息显示增强
const DebugInfoEnhancer = {
    enhanceContextMenu: (contextMenu) => {
        if (!contextMenu.env.debug) return;
        
        // 添加详细调试信息
        DebugInfoEnhancer.addDetailedStats(contextMenu);
        
        // 添加网络诊断
        DebugInfoEnhancer.addNetworkDiagnostics(contextMenu);
        
        // 添加媒体信息
        DebugInfoEnhancer.addMediaInfo(contextMenu);
        
        // 添加性能监控
        DebugInfoEnhancer.addPerformanceMonitoring(contextMenu);
    },
    
    addDetailedStats: (contextMenu) => {
        const statsContainer = document.createElement('div');
        statsContainer.className = 'detailed-stats-container';
        statsContainer.innerHTML = `
            <div class="stats-section">
                <h4>连接统计</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <label>连接状态:</label>
                        <span class="connection-state">${contextMenu.props.rtcSession.connectionState}</span>
                    </div>
                    <div class="stat-item">
                        <label>ICE状态:</label>
                        <span class="ice-state">${contextMenu.props.rtcSession.iceState}</span>
                    </div>
                    <div class="stat-item">
                        <label>DTLS状态:</label>
                        <span class="dtls-state">${contextMenu.state.uploadStats.dtlsState}</span>
                    </div>
                </div>
            </div>
            
            <div class="stats-section">
                <h4>数据传输</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <label>发送包数:</label>
                        <span class="packets-sent">${contextMenu.state.uploadStats.packetsSent || 0}</span>
                    </div>
                    <div class="stat-item">
                        <label>接收包数:</label>
                        <span class="packets-received">${contextMenu.state.downloadStats.packetsReceived || 0}</span>
                    </div>
                    <div class="stat-item">
                        <label>可用带宽:</label>
                        <span class="available-bandwidth">${contextMenu.state.uploadStats.availableOutgoingBitrate || 0} bps</span>
                    </div>
                </div>
            </div>
        `;
        
        contextMenu.el.appendChild(statsContainer);
        
        // 定期更新统计显示
        setInterval(() => {
            DebugInfoEnhancer.updateStatsDisplay(statsContainer, contextMenu);
        }, 1000);
    },
    
    updateStatsDisplay: (container, contextMenu) => {
        const connectionState = container.querySelector('.connection-state');
        const iceState = container.querySelector('.ice-state');
        const dtlsState = container.querySelector('.dtls-state');
        const packetsSent = container.querySelector('.packets-sent');
        const packetsReceived = container.querySelector('.packets-received');
        const availableBandwidth = container.querySelector('.available-bandwidth');
        
        if (connectionState) connectionState.textContent = contextMenu.props.rtcSession.connectionState || 'unknown';
        if (iceState) iceState.textContent = contextMenu.props.rtcSession.iceState || 'unknown';
        if (dtlsState) dtlsState.textContent = contextMenu.state.uploadStats.dtlsState || 'unknown';
        if (packetsSent) packetsSent.textContent = contextMenu.state.uploadStats.packetsSent || 0;
        if (packetsReceived) packetsReceived.textContent = contextMenu.state.downloadStats.packetsReceived || 0;
        if (availableBandwidth) availableBandwidth.textContent = `${contextMenu.state.uploadStats.availableOutgoingBitrate || 0} bps`;
    },
    
    addNetworkDiagnostics: (contextMenu) => {
        const diagnosticsContainer = document.createElement('div');
        diagnosticsContainer.className = 'network-diagnostics-container';
        diagnosticsContainer.innerHTML = `
            <div class="diagnostics-section">
                <h4>网络诊断</h4>
                <div class="diagnostic-tests">
                    <button class="diagnostic-btn ping-test-btn">延迟测试</button>
                    <button class="diagnostic-btn bandwidth-test-btn">带宽测试</button>
                    <button class="diagnostic-btn connection-test-btn">连接测试</button>
                </div>
                <div class="diagnostic-results">
                    <div class="result-item">
                        <label>延迟:</label>
                        <span class="ping-result">-</span>
                    </div>
                    <div class="result-item">
                        <label>下载速度:</label>
                        <span class="download-speed">-</span>
                    </div>
                    <div class="result-item">
                        <label>上传速度:</label>
                        <span class="upload-speed">-</span>
                    </div>
                </div>
            </div>
        `;
        
        DebugInfoEnhancer.bindDiagnosticEvents(diagnosticsContainer, contextMenu);
        contextMenu.el.appendChild(diagnosticsContainer);
    },
    
    bindDiagnosticEvents: (container, contextMenu) => {
        const pingTestBtn = container.querySelector('.ping-test-btn');
        const bandwidthTestBtn = container.querySelector('.bandwidth-test-btn');
        const connectionTestBtn = container.querySelector('.connection-test-btn');
        
        pingTestBtn.addEventListener('click', () => {
            DebugInfoEnhancer.runPingTest(container, contextMenu);
        });
        
        bandwidthTestBtn.addEventListener('click', () => {
            DebugInfoEnhancer.runBandwidthTest(container, contextMenu);
        });
        
        connectionTestBtn.addEventListener('click', () => {
            DebugInfoEnhancer.runConnectionTest(container, contextMenu);
        });
    },
    
    runPingTest: async (container, contextMenu) => {
        const pingResult = container.querySelector('.ping-result');
        pingResult.textContent = '测试中...';
        
        try {
            const startTime = Date.now();
            await fetch('/web/webclient/ping', { method: 'HEAD' });
            const endTime = Date.now();
            const latency = endTime - startTime;
            
            pingResult.textContent = `${latency}ms`;
            pingResult.className = latency < 100 ? 'good' : latency < 300 ? 'fair' : 'poor';
        } catch (error) {
            pingResult.textContent = '测试失败';
            pingResult.className = 'error';
        }
    },
    
    runBandwidthTest: async (container, contextMenu) => {
        const downloadSpeed = container.querySelector('.download-speed');
        const uploadSpeed = container.querySelector('.upload-speed');
        
        downloadSpeed.textContent = '测试中...';
        uploadSpeed.textContent = '测试中...';
        
        try {
            // 简单的带宽测试
            const testData = new ArrayBuffer(1024 * 1024); // 1MB
            
            const startTime = Date.now();
            await fetch('/web/dataset/call_kw', {
                method: 'POST',
                body: testData
            });
            const endTime = Date.now();
            
            const duration = (endTime - startTime) / 1000;
            const speed = (testData.byteLength * 8) / duration / 1000000; // Mbps
            
            uploadSpeed.textContent = `${speed.toFixed(2)} Mbps`;
            downloadSpeed.textContent = `${speed.toFixed(2)} Mbps`;
        } catch (error) {
            downloadSpeed.textContent = '测试失败';
            uploadSpeed.textContent = '测试失败';
        }
    },
    
    runConnectionTest: async (container, contextMenu) => {
        const session = contextMenu.props.rtcSession;
        
        if (session.peerConnection) {
            try {
                const stats = await session.peerConnection.getStats();
                let connectionQuality = 'unknown';
                
                stats.forEach(report => {
                    if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                        const packetsLost = report.packetsLost || 0;
                        const packetsReceived = report.packetsReceived || 0;
                        const lossRate = packetsReceived > 0 ? packetsLost / packetsReceived : 0;
                        
                        if (lossRate < 0.01) connectionQuality = 'excellent';
                        else if (lossRate < 0.05) connectionQuality = 'good';
                        else if (lossRate < 0.1) connectionQuality = 'fair';
                        else connectionQuality = 'poor';
                    }
                });
                
                showNotification(`连接质量: ${connectionQuality}`, 'info');
            } catch (error) {
                showNotification('连接测试失败', 'error');
            }
        }
    },
    
    addMediaInfo: (contextMenu) => {
        const mediaContainer = document.createElement('div');
        mediaContainer.className = 'media-info-container';
        mediaContainer.innerHTML = `
            <div class="media-section">
                <h4>媒体信息</h4>
                <div class="codec-info">
                    <div class="codec-item">
                        <label>音频编解码器:</label>
                        <span class="audio-codec">${contextMenu.state.producerStats.audio?.codec || 'unknown'}</span>
                    </div>
                    <div class="codec-item">
                        <label>视频编解码器:</label>
                        <span class="video-codec">${contextMenu.state.producerStats.video?.codec || 'unknown'}</span>
                    </div>
                    <div class="codec-item">
                        <label>音频采样率:</label>
                        <span class="audio-sample-rate">${contextMenu.state.producerStats.audio?.clockRate || 'unknown'} Hz</span>
                    </div>
                </div>
            </div>
        `;
        
        contextMenu.el.appendChild(mediaContainer);
    }
};
```

### 2. 音量控制增强

```javascript
// 音量控制增强
const VolumeControlEnhancer = {
    enhanceVolumeControl: (contextMenu) => {
        const volumeContainer = document.createElement('div');
        volumeContainer.className = 'enhanced-volume-control';
        volumeContainer.innerHTML = `
            <div class="volume-section">
                <h4>音量控制</h4>
                <div class="volume-controls">
                    <div class="volume-slider-container">
                        <label>音量: <span class="volume-value">${Math.round(contextMenu.volume * 100)}%</span></label>
                        <input type="range" class="volume-slider" min="0" max="1" step="0.01" value="${contextMenu.volume}">
                    </div>
                    
                    <div class="volume-presets">
                        <button class="preset-btn" data-volume="0">静音</button>
                        <button class="preset-btn" data-volume="0.25">25%</button>
                        <button class="preset-btn" data-volume="0.5">50%</button>
                        <button class="preset-btn" data-volume="0.75">75%</button>
                        <button class="preset-btn" data-volume="1">100%</button>
                    </div>
                    
                    <div class="volume-meter">
                        <div class="meter-bar">
                            <div class="meter-fill"></div>
                        </div>
                        <span class="meter-label">音频级别</span>
                    </div>
                </div>
            </div>
        `;
        
        VolumeControlEnhancer.bindVolumeEvents(volumeContainer, contextMenu);
        contextMenu.el.appendChild(volumeContainer);
    },
    
    bindVolumeEvents: (container, contextMenu) => {
        const volumeSlider = container.querySelector('.volume-slider');
        const volumeValue = container.querySelector('.volume-value');
        const presetBtns = container.querySelectorAll('.preset-btn');
        const meterFill = container.querySelector('.meter-fill');
        
        // 滑块事件
        volumeSlider.addEventListener('input', (event) => {
            const volume = parseFloat(event.target.value);
            contextMenu.onChangeVolume({ target: { value: volume } });
            volumeValue.textContent = `${Math.round(volume * 100)}%`;
        });
        
        // 预设按钮事件
        presetBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const volume = parseFloat(btn.dataset.volume);
                volumeSlider.value = volume;
                contextMenu.onChangeVolume({ target: { value: volume } });
                volumeValue.textContent = `${Math.round(volume * 100)}%`;
            });
        });
        
        // 音频级别监控
        VolumeControlEnhancer.startAudioLevelMonitoring(meterFill, contextMenu);
    },
    
    startAudioLevelMonitoring: (meterFill, contextMenu) => {
        const updateMeter = () => {
            const audioLevel = contextMenu.props.rtcSession.audioLevel || 0;
            meterFill.style.width = `${audioLevel * 100}%`;
            
            // 根据音频级别改变颜色
            if (audioLevel > 0.8) {
                meterFill.className = 'meter-fill high';
            } else if (audioLevel > 0.5) {
                meterFill.className = 'meter-fill medium';
            } else {
                meterFill.className = 'meter-fill low';
            }
        };
        
        const intervalId = setInterval(updateMeter, 100);
        
        // 清理定时器
        contextMenu.addEventListener('destroy', () => {
            clearInterval(intervalId);
        });
    }
};
```

## 技术特点

### 1. 统计数据管理
- SFU和P2P模式的统计
- 实时数据更新
- 格式化显示

### 2. 调试功能
- 调试模式检测
- 详细连接信息
- 性能监控

### 3. 用户交互
- 音量控制
- 协议信息显示
- 实时状态更新

### 4. 资源管理
- 定时器清理
- 内存泄漏防护
- 组件生命周期管理

## 设计模式

### 1. 菜单模式 (Menu Pattern)
- 上下文相关的操作菜单
- 动态内容显示

### 2. 观察者模式 (Observer Pattern)
- 统计数据的定期更新
- 状态变化监听

### 3. 策略模式 (Strategy Pattern)
- SFU和P2P的不同统计策略
- 调试和生产模式的不同行为

## 注意事项

1. **调试模式**: 只在调试模式下显示详细信息
2. **性能影响**: 定期统计更新的性能考虑
3. **内存管理**: 及时清理定时器和事件监听器
4. **用户体验**: 提供清晰的信息展示

## 扩展建议

1. **更多统计**: 添加更详细的网络和媒体统计
2. **导出功能**: 支持统计数据导出
3. **历史记录**: 保存历史统计数据
4. **可视化**: 添加图表显示统计趋势
5. **自动诊断**: 自动检测和报告问题

该组件为视频通话系统提供了重要的调试和管理功能，是开发和维护的重要工具。

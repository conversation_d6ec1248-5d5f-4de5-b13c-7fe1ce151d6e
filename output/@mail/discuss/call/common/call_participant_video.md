# Call Participant Video - 通话参与者视频

## 概述

`call_participant_video.js` 实现了 Odoo 讨论应用中通话参与者的视频显示组件，负责渲染和管理单个参与者的视频流。该组件支持不同类型的视频流（摄像头、屏幕共享）、自动播放控制、视频错误处理、媒体流更新等功能，通过HTML5 video元素进行视频渲染，提供了完整的视频播放和管理功能，是视频通话系统中的核心视频显示单元。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_participant_video.js`
- **行数**: 58
- **模块**: `@mail/discuss/call/common/call_participant_video`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                // OWL 框架
'@web/core/utils/hooks'    // Web 核心钩子
```

## 组件定义

### CallParticipantVideo 类

```javascript
class CallParticipantVideo extends Component {
    static props = ["session", "type", "inset?"];
    static template = "discuss.CallParticipantVideo";
}
```

**组件特性**:
- 支持会话和类型属性
- 可选的嵌入模式
- 使用自定义模板

## Props 配置

### Props 详细说明

- **`session`** (必需):
  - 类型: RtcSession 模型实例
  - 用途: RTC会话对象
  - 功能: 提供视频流和会话信息

- **`type`** (必需):
  - 类型: 字符串
  - 用途: 视频流类型
  - 功能: 指定显示的流类型（camera/screen）

- **`inset`** (可选):
  - 类型: 布尔值
  - 用途: 是否嵌入显示
  - 功能: 控制视频显示模式

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.rtc = useState(useService("discuss.rtc"));
    this.root = useRef("root");
    onMounted(() => this._update());
    onPatched(() => this._update());
    useExternalListener(this.env.bus, "RTC-SERVICE:PLAY_MEDIA", async () => {
        await this.play();
    });
}
```

**初始化内容**:
- RTC服务状态管理
- 视频元素引用
- 生命周期钩子绑定
- 外部事件监听

## 核心功能

### 1. 视频流更新

```javascript
_update() {
    if (!this.root.el) {
        return;
    }
    if (!this.props.session || !this.props.session.getStream(this.props.type)) {
        this.root.el.srcObject = undefined;
    } else {
        this.root.el.srcObject = this.props.session.getStream(this.props.type);
    }
    this.root.el.load();
}
```

**更新逻辑**:
- 检查视频元素存在性
- 获取指定类型的视频流
- 设置视频源对象
- 重新加载视频

### 2. 视频播放控制

```javascript
async play() {
    try {
        await this.root.el?.play?.();
        this.props.session.videoError = undefined;
    } catch (error) {
        this.props.session.videoError = error.name;
    }
}
```

**播放控制**:
- 异步播放视频
- 清除播放错误
- 捕获和记录播放错误

### 3. 元数据加载处理

```javascript
async onVideoLoadedMetaData() {
    await this.play();
}
```

**元数据处理**:
- 视频元数据加载完成后自动播放
- 确保视频正常显示

## 使用场景

### 1. 多类型视频流管理

```javascript
// 多类型视频流管理
const VideoStreamManager = {
    createVideoComponent: (session, type, options = {}) => {
        const videoContainer = document.createElement('div');
        videoContainer.className = `video-container ${type}-video`;
        
        // 根据类型设置不同的样式和配置
        const config = VideoStreamManager.getTypeConfig(type);
        
        videoContainer.innerHTML = `
            <div class="video-wrapper ${config.wrapperClass}">
                <CallParticipantVideo 
                    session={session}
                    type="${type}"
                    inset={${options.inset || false}}
                />
                
                <div class="video-overlay">
                    <div class="video-info">
                        <span class="participant-name">
                            ${session.channelMember?.persona?.name || 'Unknown'}
                        </span>
                        <span class="video-type">${config.displayName}</span>
                    </div>
                    
                    <div class="video-controls">
                        ${VideoStreamManager.renderVideoControls(session, type)}
                    </div>
                </div>
                
                <div class="video-status">
                    ${VideoStreamManager.renderVideoStatus(session, type)}
                </div>
            </div>
        `;
        
        VideoStreamManager.bindVideoEvents(videoContainer, session, type);
        return videoContainer;
    },
    
    getTypeConfig: (type) => {
        const configs = {
            camera: {
                displayName: '摄像头',
                wrapperClass: 'camera-wrapper',
                aspectRatio: '16:9',
                controls: ['mute', 'pin', 'fullscreen']
            },
            screen: {
                displayName: '屏幕共享',
                wrapperClass: 'screen-wrapper',
                aspectRatio: '16:10',
                controls: ['pin', 'fullscreen', 'download']
            }
        };
        
        return configs[type] || configs.camera;
    },
    
    renderVideoControls: (session, type) => {
        const config = VideoStreamManager.getTypeConfig(type);
        
        return config.controls.map(control => {
            switch (control) {
                case 'mute':
                    return `<button class="video-control-btn mute-btn" data-action="mute" title="静音">
                        <i class="fa fa-microphone${session.isMuted ? '-slash' : ''}"></i>
                    </button>`;
                case 'pin':
                    return `<button class="video-control-btn pin-btn" data-action="pin" title="固定">
                        <i class="fa fa-thumb-tack"></i>
                    </button>`;
                case 'fullscreen':
                    return `<button class="video-control-btn fullscreen-btn" data-action="fullscreen" title="全屏">
                        <i class="fa fa-expand"></i>
                    </button>`;
                case 'download':
                    return `<button class="video-control-btn download-btn" data-action="download" title="下载">
                        <i class="fa fa-download"></i>
                    </button>`;
                default:
                    return '';
            }
        }).join('');
    },
    
    renderVideoStatus: (session, type) => {
        const stream = session.getStream(type);
        const hasVideo = stream && stream.getVideoTracks().length > 0;
        const hasAudio = stream && stream.getAudioTracks().length > 0;
        
        return `
            <div class="status-indicators">
                <div class="connection-status ${session.connectionState || 'connected'}">
                    <i class="fa fa-wifi"></i>
                    <span>${VideoStreamManager.getConnectionStatusText(session.connectionState)}</span>
                </div>
                
                ${hasVideo ? `
                    <div class="video-quality">
                        <i class="fa fa-video"></i>
                        <span>${VideoStreamManager.getVideoQuality(stream)}</span>
                    </div>
                ` : ''}
                
                ${hasAudio && type === 'camera' ? `
                    <div class="audio-level">
                        <i class="fa fa-volume-up"></i>
                        <div class="level-bar">
                            <div class="level-fill" style="width: ${session.audioLevel * 100}%"></div>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    },
    
    bindVideoEvents: (container, session, type) => {
        // 绑定控制按钮事件
        container.querySelectorAll('.video-control-btn').forEach(btn => {
            btn.addEventListener('click', async (event) => {
                const action = event.currentTarget.dataset.action;
                await VideoStreamManager.executeVideoAction(action, session, type, container);
            });
        });
        
        // 绑定视频事件
        const videoElement = container.querySelector('video');
        if (videoElement) {
            VideoStreamManager.bindVideoElementEvents(videoElement, session, type);
        }
        
        // 绑定悬停事件
        VideoStreamManager.bindHoverEvents(container);
    },
    
    bindVideoElementEvents: (videoElement, session, type) => {
        videoElement.addEventListener('loadstart', () => {
            console.log(`${type} 视频开始加载:`, session.id);
        });
        
        videoElement.addEventListener('loadedmetadata', () => {
            console.log(`${type} 视频元数据已加载:`, session.id);
            VideoStreamManager.updateVideoInfo(videoElement, session, type);
        });
        
        videoElement.addEventListener('canplay', () => {
            console.log(`${type} 视频可以播放:`, session.id);
        });
        
        videoElement.addEventListener('play', () => {
            console.log(`${type} 视频开始播放:`, session.id);
        });
        
        videoElement.addEventListener('pause', () => {
            console.log(`${type} 视频暂停:`, session.id);
        });
        
        videoElement.addEventListener('error', (event) => {
            console.error(`${type} 视频错误:`, event.error);
            VideoStreamManager.handleVideoError(videoElement, session, type, event.error);
        });
        
        videoElement.addEventListener('stalled', () => {
            console.warn(`${type} 视频卡顿:`, session.id);
        });
        
        videoElement.addEventListener('waiting', () => {
            console.warn(`${type} 视频缓冲:`, session.id);
        });
    },
    
    bindHoverEvents: (container) => {
        let hoverTimeout;
        
        container.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
            container.classList.add('hover');
        });
        
        container.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                container.classList.remove('hover');
            }, 2000); // 2秒后隐藏控制
        });
        
        // 移动端触摸事件
        container.addEventListener('touchstart', () => {
            container.classList.toggle('hover');
        });
    },
    
    executeVideoAction: async (action, session, type, container) => {
        try {
            switch (action) {
                case 'mute':
                    await VideoStreamManager.toggleMute(session);
                    break;
                case 'pin':
                    VideoStreamManager.pinVideo(session, type, container);
                    break;
                case 'fullscreen':
                    VideoStreamManager.toggleFullscreen(container);
                    break;
                case 'download':
                    VideoStreamManager.downloadVideo(session, type);
                    break;
            }
        } catch (error) {
            console.error(`执行视频操作 ${action} 失败:`, error);
            showNotification('操作失败，请重试', 'error');
        }
    },
    
    toggleMute: async (session) => {
        if (session.isSelfMuted) {
            await session.unmute();
        } else {
            await session.mute();
        }
    },
    
    pinVideo: (session, type, container) => {
        // 移除其他固定的视频
        document.querySelectorAll('.video-container.pinned').forEach(el => {
            el.classList.remove('pinned');
        });
        
        // 固定当前视频
        container.classList.add('pinned');
        
        // 通知其他组件
        const event = new CustomEvent('video_pinned', {
            detail: { session, type }
        });
        document.dispatchEvent(event);
        
        showNotification(`已固定 ${session.channelMember?.persona?.name} 的${type === 'camera' ? '摄像头' : '屏幕共享'}`, 'success');
    },
    
    toggleFullscreen: (container) => {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            container.requestFullscreen();
        }
    },
    
    downloadVideo: (session, type) => {
        const stream = session.getStream(type);
        if (!stream) {
            showNotification('无可下载的视频流', 'warning');
            return;
        }
        
        // 创建录制器
        const mediaRecorder = new MediaRecorder(stream);
        const chunks = [];
        
        mediaRecorder.ondataavailable = (event) => {
            chunks.push(event.data);
        };
        
        mediaRecorder.onstop = () => {
            const blob = new Blob(chunks, { type: 'video/webm' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${session.channelMember?.persona?.name || 'participant'}_${type}_${Date.now()}.webm`;
            a.click();
            
            URL.revokeObjectURL(url);
            showNotification('视频下载已开始', 'success');
        };
        
        // 录制5秒
        mediaRecorder.start();
        setTimeout(() => {
            mediaRecorder.stop();
        }, 5000);
    },
    
    updateVideoInfo: (videoElement, session, type) => {
        const container = videoElement.closest('.video-container');
        const infoElement = container.querySelector('.video-info');
        
        if (infoElement) {
            const resolution = `${videoElement.videoWidth}x${videoElement.videoHeight}`;
            const fps = VideoStreamManager.getVideoFPS(videoElement);
            
            infoElement.innerHTML += `
                <div class="video-details">
                    <span class="resolution">${resolution}</span>
                    <span class="fps">${fps} FPS</span>
                </div>
            `;
        }
    },
    
    getVideoFPS: (videoElement) => {
        // 简单的FPS估算
        return '30'; // 默认值，实际应该通过更复杂的方法计算
    },
    
    getConnectionStatusText: (state) => {
        const statusMap = {
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '已断开',
            'failed': '连接失败',
            'closed': '已关闭'
        };
        return statusMap[state] || '未知';
    },
    
    getVideoQuality: (stream) => {
        const videoTrack = stream.getVideoTracks()[0];
        if (!videoTrack) return '无视频';
        
        const settings = videoTrack.getSettings();
        const width = settings.width || 0;
        const height = settings.height || 0;
        
        if (height >= 1080) return '1080p';
        if (height >= 720) return '720p';
        if (height >= 480) return '480p';
        return `${width}x${height}`;
    },
    
    handleVideoError: (videoElement, session, type, error) => {
        const container = videoElement.closest('.video-container');
        
        // 显示错误信息
        const errorOverlay = document.createElement('div');
        errorOverlay.className = 'video-error-overlay';
        errorOverlay.innerHTML = `
            <div class="error-content">
                <i class="fa fa-exclamation-triangle"></i>
                <span class="error-message">视频播放失败</span>
                <button class="retry-btn">重试</button>
            </div>
        `;
        
        container.appendChild(errorOverlay);
        
        // 绑定重试事件
        errorOverlay.querySelector('.retry-btn').addEventListener('click', async () => {
            try {
                await videoElement.play();
                errorOverlay.remove();
            } catch (retryError) {
                console.error('重试播放失败:', retryError);
            }
        });
        
        // 自动移除错误提示
        setTimeout(() => {
            if (errorOverlay.parentNode) {
                errorOverlay.remove();
            }
        }, 10000);
    }
};
```

### 2. 视频质量自适应

```javascript
// 视频质量自适应管理
const VideoQualityManager = {
    setupQualityAdaptation: (videoComponent) => {
        const qualityConfig = {
            targetBitrate: 1000000, // 1Mbps
            minBitrate: 100000,     // 100Kbps
            maxBitrate: 5000000,    // 5Mbps
            adaptationInterval: 5000 // 5秒
        };
        
        VideoQualityManager.startQualityMonitoring(videoComponent, qualityConfig);
        
        return {
            updateTargetBitrate: (bitrate) => {
                qualityConfig.targetBitrate = bitrate;
            },
            getQualityStats: () => VideoQualityManager.getQualityStats(videoComponent)
        };
    },
    
    startQualityMonitoring: (videoComponent, config) => {
        const monitoringInterval = setInterval(() => {
            VideoQualityManager.adaptQuality(videoComponent, config);
        }, config.adaptationInterval);
        
        // 清理监控
        videoComponent.addEventListener('destroy', () => {
            clearInterval(monitoringInterval);
        });
    },
    
    adaptQuality: async (videoComponent, config) => {
        try {
            const stats = await VideoQualityManager.getConnectionStats(videoComponent);
            const networkQuality = VideoQualityManager.assessNetworkQuality(stats);
            
            const newBitrate = VideoQualityManager.calculateOptimalBitrate(
                networkQuality, 
                config
            );
            
            await VideoQualityManager.adjustVideoBitrate(videoComponent, newBitrate);
        } catch (error) {
            console.error('质量自适应失败:', error);
        }
    },
    
    getConnectionStats: async (videoComponent) => {
        const session = videoComponent.props.session;
        const peerConnection = session.peerConnection;
        
        if (!peerConnection) return null;
        
        const stats = await peerConnection.getStats();
        const videoStats = {};
        
        stats.forEach(report => {
            if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
                videoStats.inbound = report;
            } else if (report.type === 'outbound-rtp' && report.mediaType === 'video') {
                videoStats.outbound = report;
            }
        });
        
        return videoStats;
    },
    
    assessNetworkQuality: (stats) => {
        if (!stats || !stats.inbound) return 'unknown';
        
        const packetsLost = stats.inbound.packetsLost || 0;
        const packetsReceived = stats.inbound.packetsReceived || 0;
        const lossRate = packetsReceived > 0 ? packetsLost / packetsReceived : 0;
        
        if (lossRate < 0.01) return 'excellent';
        if (lossRate < 0.05) return 'good';
        if (lossRate < 0.1) return 'fair';
        return 'poor';
    },
    
    calculateOptimalBitrate: (networkQuality, config) => {
        const qualityMultipliers = {
            'excellent': 1.0,
            'good': 0.8,
            'fair': 0.6,
            'poor': 0.4,
            'unknown': 0.7
        };
        
        const multiplier = qualityMultipliers[networkQuality] || 0.7;
        const optimalBitrate = config.targetBitrate * multiplier;
        
        return Math.max(
            config.minBitrate,
            Math.min(config.maxBitrate, optimalBitrate)
        );
    },
    
    adjustVideoBitrate: async (videoComponent, bitrate) => {
        const session = videoComponent.props.session;
        const stream = session.getStream(videoComponent.props.type);
        
        if (!stream) return;
        
        const videoTrack = stream.getVideoTracks()[0];
        if (!videoTrack) return;
        
        try {
            await videoTrack.applyConstraints({
                advanced: [{
                    bitrate: bitrate
                }]
            });
            
            console.log(`视频码率已调整为: ${bitrate} bps`);
        } catch (error) {
            console.error('调整视频码率失败:', error);
        }
    }
};
```

## 技术特点

### 1. HTML5 Video集成
- 原生video元素控制
- 媒体流对象绑定
- 自动播放管理

### 2. 错误处理
- 播放错误捕获
- 错误状态记录
- 用户友好提示

### 3. 生命周期管理
- 组件挂载更新
- 外部事件监听
- 资源清理

### 4. 流类型支持
- 摄像头视频流
- 屏幕共享流
- 动态流切换

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的视频组件
- 清晰的属性接口

### 2. 观察者模式 (Observer Pattern)
- 外部事件监听
- 状态变化响应

### 3. 策略模式 (Strategy Pattern)
- 不同类型流的处理策略
- 错误处理策略

## 注意事项

1. **浏览器兼容性**: 不同浏览器的video元素行为差异
2. **自动播放策略**: 浏览器的自动播放限制
3. **内存管理**: 及时清理视频资源
4. **性能优化**: 避免不必要的视频更新

## 扩展建议

1. **画中画**: 支持画中画模式
2. **视频滤镜**: 集成实时视频滤镜
3. **手势控制**: 支持手势操作
4. **录制功能**: 集成视频录制
5. **质量指示**: 显示视频质量指标

该组件是视频通话系统中的核心视频显示单元，提供了完整的视频渲染和管理功能。

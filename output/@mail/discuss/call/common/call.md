# Call - 通话组件

## 概述

`call.js` 实现了 Odoo 讨论应用中的核心通话组件，负责管理和显示视频通话界面。该组件支持多参与者视频通话、屏幕共享、举手功能、全屏模式、网格布局自动调整等功能，集成了通话操作列表、参与者卡片和PTT广告横幅等子组件，提供了完整的视频会议用户界面和交互体验。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call.js`
- **行数**: 317
- **模块**: `@mail/discuss/call/common/call`

## 依赖关系

```javascript
// UI组件依赖
'@mail/discuss/call/common/call_action_list'     // 通话操作列表
'@mail/discuss/call/common/call_participant_card' // 参与者卡片
'@mail/discuss/call/common/ptt_ad_banner'        // PTT广告横幅

// 核心依赖
'@web/core/utils/misc'                           // 工具函数
'@odoo/owl'                                      // OWL 框架
'@web/core/browser/browser'                      // 浏览器服务
'@web/core/l10n/translation'                     // 国际化
'@web/core/utils/hooks'                          // Web 核心钩子
```

## 类型定义

### CardData 类型

```javascript
/**
 * @typedef CardData
 * @property {string} key
 * @property {import("models").RtcSession} session
 * @property {MediaStream} videoStream
 * @property {import("models").ChannelMember} [member]
 */
```

### Props 类型

```javascript
/**
 * @typedef {Object} Props
 * @property {import("models").Thread} thread
 * @property {boolean} [compact]
 */
```

## 组件定义

### Call 类

```javascript
class Call extends Component {
    static components = { CallActionList, CallParticipantCard, PttAdBanner };
    static props = ["thread", "compact?"];
    static template = "discuss.Call";
}
```

**组件特性**:
- 集成多个子组件
- 支持紧凑模式
- 使用自定义模板

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.grid = useRef("grid");
    this.notification = useService("notification");
    this.rtc = useState(useService("discuss.rtc"));
    this.state = useState({
        isFullscreen: false,
        sidebar: false,
        tileWidth: 0,
        tileHeight: 0,
        columnCount: 0,
        overlay: false,
        insetCard: undefined,
    });
    this.store = useState(useService("mail.store"));
    
    onMounted(() => {
        this.resizeObserver = new ResizeObserver(() => this.arrangeTiles());
        this.resizeObserver.observe(this.grid.el);
        this.arrangeTiles();
    });
    onPatched(() => this.arrangeTiles());
    onWillUnmount(() => {
        this.resizeObserver.disconnect();
        browser.clearTimeout(this.overlayTimeout);
    });
    useExternalListener(browser, "fullscreenchange", this.onFullScreenChange);
}
```

**初始化内容**:
- 网格引用和服务注入
- 响应式状态管理
- ResizeObserver监听
- 生命周期钩子绑定
- 全屏变化监听

## 核心功能

### 1. 通话状态管理

```javascript
get isActiveCall() {
    return Boolean(this.props.thread.eq(this.rtc.state?.channel));
}

get minimized() {
    if (this.state.isFullscreen || this.props.thread.activeRtcSession) {
        return false;
    }
    if (!this.isActiveCall || this.props.thread.videoCount === 0 || this.props.compact) {
        return true;
    }
    return false;
}
```

**状态判断**:
- **isActiveCall**: 判断是否为当前活跃通话
- **minimized**: 判断是否应该最小化显示

### 2. 可见卡片管理

```javascript
get visibleCards() {
    const raisingHandCards = [];
    const sessionCards = [];
    const invitationCards = [];
    const filterVideos = this.store.settings.showOnlyVideo && this.props.thread.videoCount > 0;
    
    for (const session of this.props.thread.rtcSessions) {
        const target = session.raisingHand ? raisingHandCards : sessionCards;
        const cameraStream = session.isCameraOn
            ? session.videoStreams.get("camera")
            : undefined;
        if (!filterVideos || cameraStream) {
            target.push({
                key: "session_main_" + session.id,
                session,
                type: "camera",
                videoStream: cameraStream,
            });
        }
        const screenStream = session.isScreenSharingOn
            ? session.videoStreams.get("screen")
            : undefined;
        if (screenStream) {
            target.push({
                key: "session_secondary_" + session.id,
                session,
                type: "screen",
                videoStream: screenStream,
            });
        }
    }
    
    // 处理邀请成员
    if (!filterVideos) {
        for (const member of this.props.thread.invitedMembers) {
            invitationCards.push({
                key: "member_" + member.id,
                member,
            });
        }
    }
    
    // 排序逻辑
    raisingHandCards.sort((c1, c2) => {
        return c1.session.raisingHand - c2.session.raisingHand;
    });
    sessionCards.sort((c1, c2) => {
        return (
            c1.session.channelMember?.persona?.name?.localeCompare(
                c2.session.channelMember?.persona?.name
            ) ?? 1
        );
    });
    invitationCards.sort((c1, c2) => {
        return c1.member.persona?.name?.localeCompare(c2.member.persona?.name) ?? 1;
    });
    
    return raisingHandCards.concat(sessionCards, invitationCards);
}
```

**卡片分类**:
- **举手卡片**: 正在举手的参与者
- **会话卡片**: 普通参与者
- **邀请卡片**: 被邀请但未加入的成员

### 3. 网格布局管理

```javascript
arrangeTiles() {
    // 网格布局自动调整逻辑
    // 根据参与者数量和容器尺寸计算最佳布局
}
```

## 使用场景

### 1. 多人视频会议

```javascript
// 多人视频会议管理
const VideoConferenceManager = {
    setupConference: (thread, options = {}) => {
        const conferenceConfig = {
            maxParticipants: options.maxParticipants || 16,
            enableScreenShare: options.enableScreenShare !== false,
            enableChat: options.enableChat !== false,
            enableRecording: options.enableRecording || false,
            layout: options.layout || 'grid'
        };
        
        return VideoConferenceManager.createConferenceInterface(thread, conferenceConfig);
    },
    
    createConferenceInterface: (thread, config) => {
        const container = document.createElement('div');
        container.className = 'video-conference-container';
        container.innerHTML = `
            <div class="conference-header">
                <div class="conference-info">
                    <h3>${thread.displayName}</h3>
                    <span class="participant-count">
                        ${thread.rtcSessions.length} 参与者
                    </span>
                </div>
                
                <div class="conference-controls">
                    <button class="layout-btn" data-layout="grid">网格</button>
                    <button class="layout-btn" data-layout="speaker">演讲者</button>
                    <button class="layout-btn" data-layout="gallery">画廊</button>
                    <button class="fullscreen-btn">全屏</button>
                    <button class="settings-btn">设置</button>
                </div>
            </div>
            
            <div class="conference-main">
                <Call thread={thread} />
            </div>
            
            <div class="conference-sidebar" style="display: none;">
                <div class="sidebar-tabs">
                    <button class="tab-btn active" data-tab="participants">参与者</button>
                    <button class="tab-btn" data-tab="chat">聊天</button>
                    <button class="tab-btn" data-tab="settings">设置</button>
                </div>
                
                <div class="sidebar-content">
                    <div class="tab-panel active" id="participants-panel">
                        <!-- 参与者列表 -->
                    </div>
                    <div class="tab-panel" id="chat-panel">
                        <!-- 聊天界面 -->
                    </div>
                    <div class="tab-panel" id="settings-panel">
                        <!-- 设置界面 -->
                    </div>
                </div>
            </div>
        `;
        
        VideoConferenceManager.bindConferenceEvents(container, thread, config);
        return container;
    },
    
    bindConferenceEvents: (container, thread, config) => {
        // 布局切换
        container.querySelectorAll('.layout-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const layout = event.target.dataset.layout;
                VideoConferenceManager.switchLayout(layout, container);
            });
        });
        
        // 全屏切换
        container.querySelector('.fullscreen-btn').addEventListener('click', () => {
            VideoConferenceManager.toggleFullscreen(container);
        });
        
        // 侧边栏切换
        container.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const tab = event.target.dataset.tab;
                VideoConferenceManager.switchSidebarTab(tab, container);
            });
        });
    },
    
    switchLayout: (layout, container) => {
        const callComponent = container.querySelector('.call-component');
        
        // 移除现有布局类
        callComponent.classList.remove('grid-layout', 'speaker-layout', 'gallery-layout');
        
        // 添加新布局类
        callComponent.classList.add(`${layout}-layout`);
        
        // 更新按钮状态
        container.querySelectorAll('.layout-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.layout === layout);
        });
        
        // 触发重新布局
        const callInstance = getCallInstance(container);
        if (callInstance) {
            callInstance.arrangeTiles();
        }
    },
    
    toggleFullscreen: (container) => {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            container.requestFullscreen();
        }
    },
    
    switchSidebarTab: (tab, container) => {
        // 更新标签状态
        container.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tab);
        });
        
        // 更新面板显示
        container.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.toggle('active', panel.id === `${tab}-panel`);
        });
        
        // 显示侧边栏
        const sidebar = container.querySelector('.conference-sidebar');
        sidebar.style.display = 'block';
    }
};
```

### 2. 参与者管理

```javascript
// 参与者管理功能
const ParticipantManager = {
    createParticipantList: (thread) => {
        const listContainer = document.createElement('div');
        listContainer.className = 'participant-list-container';
        
        ParticipantManager.updateParticipantList(listContainer, thread);
        
        // 监听参与者变化
        thread.addEventListener('participants_changed', () => {
            ParticipantManager.updateParticipantList(listContainer, thread);
        });
        
        return listContainer;
    },
    
    updateParticipantList: (container, thread) => {
        const participants = thread.rtcSessions;
        const invitedMembers = thread.invitedMembers;
        
        container.innerHTML = `
            <div class="participant-header">
                <h4>参与者 (${participants.length})</h4>
                <button class="invite-btn">邀请更多</button>
            </div>
            
            <div class="participant-sections">
                <div class="active-participants">
                    <h5>在线参与者</h5>
                    ${participants.map(session => ParticipantManager.renderParticipant(session)).join('')}
                </div>
                
                ${invitedMembers.length > 0 ? `
                    <div class="invited-participants">
                        <h5>已邀请</h5>
                        ${invitedMembers.map(member => ParticipantManager.renderInvitedMember(member)).join('')}
                    </div>
                ` : ''}
            </div>
        `;
        
        ParticipantManager.bindParticipantEvents(container, thread);
    },
    
    renderParticipant: (session) => {
        const member = session.channelMember;
        const persona = member?.persona;
        
        return `
            <div class="participant-item" data-session-id="${session.id}">
                <div class="participant-avatar">
                    <img src="${persona?.avatarUrl || '/web/static/img/user_menu_avatar.png'}" 
                         alt="${persona?.name || 'Unknown'}" />
                    ${session.isTalking ? '<div class="talking-indicator"></div>' : ''}
                </div>
                
                <div class="participant-info">
                    <span class="participant-name">${persona?.name || 'Unknown'}</span>
                    <div class="participant-status">
                        ${session.isCameraOn ? '<i class="fa fa-video"></i>' : '<i class="fa fa-video-slash"></i>'}
                        ${session.isMuted ? '<i class="fa fa-microphone-slash"></i>' : '<i class="fa fa-microphone"></i>'}
                        ${session.isScreenSharingOn ? '<i class="fa fa-desktop"></i>' : ''}
                        ${session.raisingHand ? '<i class="fa fa-hand-paper-o"></i>' : ''}
                    </div>
                </div>
                
                <div class="participant-actions">
                    <button class="action-btn pin-btn" title="固定视频">
                        <i class="fa fa-thumb-tack"></i>
                    </button>
                    <button class="action-btn more-btn" title="更多操作">
                        <i class="fa fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        `;
    },
    
    renderInvitedMember: (member) => {
        const persona = member.persona;
        
        return `
            <div class="invited-member-item" data-member-id="${member.id}">
                <div class="member-avatar">
                    <img src="${persona?.avatarUrl || '/web/static/img/user_menu_avatar.png'}" 
                         alt="${persona?.name || 'Unknown'}" />
                </div>
                
                <div class="member-info">
                    <span class="member-name">${persona?.name || 'Unknown'}</span>
                    <span class="member-status">等待加入...</span>
                </div>
                
                <div class="member-actions">
                    <button class="action-btn remind-btn" title="提醒加入">
                        <i class="fa fa-bell"></i>
                    </button>
                    <button class="action-btn remove-btn" title="移除邀请">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    },
    
    bindParticipantEvents: (container, thread) => {
        // 固定参与者视频
        container.querySelectorAll('.pin-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const sessionId = event.target.closest('.participant-item').dataset.sessionId;
                ParticipantManager.pinParticipant(sessionId, thread);
            });
        });
        
        // 邀请更多参与者
        container.querySelector('.invite-btn').addEventListener('click', () => {
            ParticipantManager.showInviteDialog(thread);
        });
        
        // 提醒加入
        container.querySelectorAll('.remind-btn').forEach(btn => {
            btn.addEventListener('click', (event) => {
                const memberId = event.target.closest('.invited-member-item').dataset.memberId;
                ParticipantManager.remindToJoin(memberId, thread);
            });
        });
    },
    
    pinParticipant: (sessionId, thread) => {
        const session = thread.rtcSessions.find(s => s.id === sessionId);
        if (session) {
            // 设置为主要显示的参与者
            thread.activeRtcSession = session;
            showNotification(`已固定 ${session.channelMember?.persona?.name} 的视频`, 'success');
        }
    },
    
    showInviteDialog: (thread) => {
        const dialog = document.createElement('div');
        dialog.className = 'invite-dialog-overlay';
        dialog.innerHTML = `
            <div class="invite-dialog">
                <div class="dialog-header">
                    <h4>邀请参与者</h4>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="invite-methods">
                        <div class="invite-method">
                            <h5>通过邮箱邀请</h5>
                            <input type="email" class="email-input" placeholder="输入邮箱地址">
                            <button class="send-invite-btn">发送邀请</button>
                        </div>
                        
                        <div class="invite-method">
                            <h5>分享链接</h5>
                            <div class="link-container">
                                <input type="text" class="invite-link" value="${window.location.href}" readonly>
                                <button class="copy-link-btn">复制</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        ParticipantManager.bindInviteDialogEvents(dialog, thread);
    },
    
    bindInviteDialogEvents: (dialog, thread) => {
        // 关闭对话框
        dialog.querySelector('.close-btn').addEventListener('click', () => {
            dialog.remove();
        });
        
        // 发送邮箱邀请
        dialog.querySelector('.send-invite-btn').addEventListener('click', () => {
            const email = dialog.querySelector('.email-input').value;
            if (email) {
                ParticipantManager.sendEmailInvite(email, thread);
            }
        });
        
        // 复制链接
        dialog.querySelector('.copy-link-btn').addEventListener('click', async () => {
            const link = dialog.querySelector('.invite-link').value;
            try {
                await navigator.clipboard.writeText(link);
                showNotification('链接已复制到剪贴板', 'success');
            } catch (error) {
                console.error('复制失败:', error);
            }
        });
    },
    
    sendEmailInvite: async (email, thread) => {
        try {
            await fetch('/web/dataset/call_kw/mail.thread/send_call_invite', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    model: 'mail.thread',
                    method: 'send_call_invite',
                    args: [thread.id, email],
                    kwargs: {}
                })
            });
            
            showNotification(`邀请已发送到 ${email}`, 'success');
        } catch (error) {
            console.error('发送邀请失败:', error);
            showNotification('发送邀请失败，请重试', 'error');
        }
    }
};
```

## 技术特点

### 1. 响应式布局
- ResizeObserver监听容器变化
- 自动计算最佳网格布局
- 支持全屏模式

### 2. 状态管理
- 多层状态管理
- 实时状态同步
- 组件间通信

### 3. 性能优化
- 智能卡片过滤
- 按需渲染
- 资源清理

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个子组件
- 统一的组件接口

### 2. 观察者模式 (Observer Pattern)
- 监听状态变化
- 响应布局调整

### 3. 策略模式 (Strategy Pattern)
- 不同布局策略
- 可配置的显示模式

## 注意事项

1. **性能考虑**: 大量参与者时的渲染优化
2. **内存管理**: 及时清理观察者和定时器
3. **浏览器兼容**: 全屏API的兼容性处理
4. **用户体验**: 流畅的布局切换动画

## 扩展建议

1. **虚拟背景**: 集成背景替换功能
2. **录制功能**: 支持通话录制
3. **字幕功能**: 实时语音转文字
4. **手势识别**: 支持手势控制
5. **AI增强**: 智能布局和优化

该组件是视频通话系统的核心界面组件，提供了完整的多人视频会议体验。

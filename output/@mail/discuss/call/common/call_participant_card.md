# Call Participant Card - 通话参与者卡片

## 概述

`call_participant_card.js` 实现了 Odoo 讨论应用中通话参与者的卡片组件，用于显示单个参与者的视频、音频状态、连接信息等。该组件支持拖拽操作、上下文菜单、悬停效果、视频下载管理等功能，集成了参与者视频组件和上下文菜单，提供了丰富的参与者交互体验，是视频通话界面中的核心显示单元。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_participant_card.js`
- **行数**: 243
- **模块**: `@mail/discuss/call/common/call_participant_card`

## 依赖关系

```javascript
// UI组件依赖
'@mail/discuss/call/common/call_context_menu'     // 通话上下文菜单
'@mail/discuss/call/common/call_participant_video' // 参与者视频组件
'@mail/discuss/call/common/rtc_service'           // RTC服务

// 工具依赖
'@mail/utils/common/hooks'                        // 通用钩子
'@web/core/utils/misc'                           // 工具函数
'@web/core/browser/browser'                      // 浏览器服务
'@odoo/owl'                                      // OWL 框架
'@web/core/popover/popover_hook'                 // 弹出框钩子
'@web/core/utils/hooks'                          // Web 核心钩子
'@web/core/network/rpc'                          // RPC通信
```

## 组件定义

### CallParticipantCard 类

```javascript
class CallParticipantCard extends Component {
    static props = ["className", "cardData", "thread", "minimized?", "inset?"];
    static components = { CallParticipantVideo };
    static template = "discuss.CallParticipantCard";
}
```

**组件特性**:
- 支持自定义样式类名
- 集成参与者视频组件
- 支持最小化和嵌入模式

## Props 配置

### Props 详细说明

- **`className`** (必需):
  - 类型: 字符串
  - 用途: 自定义CSS类名
  - 功能: 控制卡片外观样式

- **`cardData`** (必需):
  - 类型: 对象
  - 用途: 参与者卡片数据
  - 功能: 包含会话、视频流等信息

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 通话线程对象
  - 功能: 提供通话上下文

- **`minimized`** (可选):
  - 类型: 布尔值
  - 用途: 是否最小化显示
  - 功能: 控制卡片显示模式

- **`inset`** (可选):
  - 类型: 布尔值
  - 用途: 是否嵌入显示
  - 功能: 控制卡片布局模式

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.contextMenuAnchorRef = useRef("contextMenuAnchor");
    this.root = useRef("root");
    this.popover = usePopover(CallContextMenu);
    this.rtc = useState(useService("discuss.rtc"));
    this.store = useState(useService("mail.store"));
    this.ui = useState(useService("ui"));
    this.rootHover = useHover("root");
    this.state = useState({ drag: false, dragPos: undefined });
    
    onMounted(() => {
        if (!this.rtcSession) {
            return;
        }
        this.rtc.updateVideoDownload(this.rtcSession, {
            viewCountIncrement: 1,
        });
    });
}
```

**初始化内容**:
- 引用和服务注入
- 弹出框和悬停钩子
- 拖拽状态管理
- 视频下载计数更新

## 核心功能

### 1. 连接状态管理

```javascript
const HIDDEN_CONNECTION_STATES = new Set([undefined, "connected", "completed"]);
```

**状态过滤**:
- 隐藏正常连接状态
- 只显示异常连接状态
- 提供连接状态指示

### 2. 视频下载管理

```javascript
onMounted(() => {
    if (!this.rtcSession) {
        return;
    }
    this.rtc.updateVideoDownload(this.rtcSession, {
        viewCountIncrement: 1,
    });
});
```

**下载管理**:
- 增加视频查看计数
- 优化带宽使用
- 动态调整视频质量

## 使用场景

### 1. 参与者卡片增强

```javascript
// 参与者卡片增强功能
const ParticipantCardEnhancer = {
    enhanceCard: (cardComponent) => {
        // 添加状态指示器
        ParticipantCardEnhancer.addStatusIndicators(cardComponent);
        
        // 添加快速操作
        ParticipantCardEnhancer.addQuickActions(cardComponent);
        
        // 添加统计信息
        ParticipantCardEnhancer.addStatistics(cardComponent);
        
        // 添加自定义效果
        ParticipantCardEnhancer.addVisualEffects(cardComponent);
    },
    
    addStatusIndicators: (cardComponent) => {
        const statusContainer = document.createElement('div');
        statusContainer.className = 'participant-status-indicators';
        statusContainer.innerHTML = `
            <div class="status-indicator connection-status" title="连接状态">
                <i class="fa fa-wifi"></i>
                <span class="status-text">良好</span>
            </div>
            
            <div class="status-indicator audio-status" title="音频状态">
                <i class="fa fa-microphone"></i>
                <span class="audio-level"></span>
            </div>
            
            <div class="status-indicator video-status" title="视频状态">
                <i class="fa fa-video"></i>
                <span class="video-quality">720p</span>
            </div>
            
            <div class="status-indicator network-status" title="网络状态">
                <i class="fa fa-signal"></i>
                <span class="network-quality">优秀</span>
            </div>
        `;
        
        ParticipantCardEnhancer.updateStatusIndicators(statusContainer, cardComponent);
        cardComponent.el.appendChild(statusContainer);
        
        // 定期更新状态
        setInterval(() => {
            ParticipantCardEnhancer.updateStatusIndicators(statusContainer, cardComponent);
        }, 1000);
    },
    
    updateStatusIndicators: (container, cardComponent) => {
        const session = cardComponent.props.cardData.session;
        if (!session) return;
        
        // 更新连接状态
        const connectionStatus = container.querySelector('.connection-status');
        const connectionState = session.connectionState || 'connected';
        connectionStatus.className = `status-indicator connection-status ${connectionState}`;
        connectionStatus.querySelector('.status-text').textContent = 
            ParticipantCardEnhancer.getConnectionStatusText(connectionState);
        
        // 更新音频状态
        const audioStatus = container.querySelector('.audio-status');
        const audioLevel = container.querySelector('.audio-level');
        if (session.isMuted) {
            audioStatus.classList.add('muted');
            audioLevel.style.width = '0%';
        } else {
            audioStatus.classList.remove('muted');
            const level = session.audioLevel || 0;
            audioLevel.style.width = `${level * 100}%`;
        }
        
        // 更新视频状态
        const videoStatus = container.querySelector('.video-status');
        const videoQuality = container.querySelector('.video-quality');
        if (session.isCameraOn) {
            videoStatus.classList.remove('disabled');
            const quality = session.videoQuality || '720p';
            videoQuality.textContent = quality;
        } else {
            videoStatus.classList.add('disabled');
            videoQuality.textContent = '关闭';
        }
        
        // 更新网络状态
        const networkStatus = container.querySelector('.network-status');
        const networkQuality = container.querySelector('.network-quality');
        const quality = session.networkQuality || 'good';
        networkStatus.className = `status-indicator network-status ${quality}`;
        networkQuality.textContent = ParticipantCardEnhancer.getNetworkQualityText(quality);
    },
    
    getConnectionStatusText: (state) => {
        const statusMap = {
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '已断开',
            'failed': '连接失败',
            'closed': '已关闭'
        };
        return statusMap[state] || '未知';
    },
    
    getNetworkQualityText: (quality) => {
        const qualityMap = {
            'excellent': '优秀',
            'good': '良好',
            'fair': '一般',
            'poor': '较差'
        };
        return qualityMap[quality] || '未知';
    },
    
    addQuickActions: (cardComponent) => {
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'participant-quick-actions';
        actionsContainer.innerHTML = `
            <div class="quick-actions-overlay">
                <button class="quick-action-btn pin-btn" title="固定视频">
                    <i class="fa fa-thumb-tack"></i>
                </button>
                <button class="quick-action-btn focus-btn" title="聚焦参与者">
                    <i class="fa fa-expand"></i>
                </button>
                <button class="quick-action-btn mute-btn" title="静音参与者">
                    <i class="fa fa-microphone-slash"></i>
                </button>
                <button class="quick-action-btn message-btn" title="私聊">
                    <i class="fa fa-comment"></i>
                </button>
                <button class="quick-action-btn more-btn" title="更多操作">
                    <i class="fa fa-ellipsis-v"></i>
                </button>
            </div>
        `;
        
        ParticipantCardEnhancer.bindQuickActionEvents(actionsContainer, cardComponent);
        cardComponent.el.appendChild(actionsContainer);
        
        // 悬停显示/隐藏
        cardComponent.el.addEventListener('mouseenter', () => {
            actionsContainer.style.opacity = '1';
        });
        
        cardComponent.el.addEventListener('mouseleave', () => {
            actionsContainer.style.opacity = '0';
        });
    },
    
    bindQuickActionEvents: (container, cardComponent) => {
        const session = cardComponent.props.cardData.session;
        
        // 固定视频
        container.querySelector('.pin-btn').addEventListener('click', () => {
            ParticipantCardEnhancer.pinParticipant(session, cardComponent);
        });
        
        // 聚焦参与者
        container.querySelector('.focus-btn').addEventListener('click', () => {
            ParticipantCardEnhancer.focusParticipant(session, cardComponent);
        });
        
        // 静音参与者
        container.querySelector('.mute-btn').addEventListener('click', () => {
            ParticipantCardEnhancer.muteParticipant(session, cardComponent);
        });
        
        // 私聊
        container.querySelector('.message-btn').addEventListener('click', () => {
            ParticipantCardEnhancer.startPrivateChat(session, cardComponent);
        });
        
        // 更多操作
        container.querySelector('.more-btn').addEventListener('click', (event) => {
            ParticipantCardEnhancer.showMoreActions(event, session, cardComponent);
        });
    },
    
    pinParticipant: (session, cardComponent) => {
        const thread = cardComponent.props.thread;
        thread.activeRtcSession = session;
        showNotification(`已固定 ${session.channelMember?.persona?.name} 的视频`, 'success');
    },
    
    focusParticipant: (session, cardComponent) => {
        // 进入聚焦模式
        const focusOverlay = document.createElement('div');
        focusOverlay.className = 'participant-focus-overlay';
        focusOverlay.innerHTML = `
            <div class="focus-container">
                <div class="focus-header">
                    <h3>${session.channelMember?.persona?.name}</h3>
                    <button class="close-focus-btn">&times;</button>
                </div>
                <div class="focus-video">
                    <!-- 大尺寸视频显示 -->
                </div>
                <div class="focus-controls">
                    <button class="control-btn">静音</button>
                    <button class="control-btn">关闭视频</button>
                    <button class="control-btn">私聊</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(focusOverlay);
        
        // 绑定关闭事件
        focusOverlay.querySelector('.close-focus-btn').addEventListener('click', () => {
            focusOverlay.remove();
        });
    },
    
    muteParticipant: async (session, cardComponent) => {
        try {
            await rpc('/web/dataset/call_kw/discuss.channel/mute_participant', {
                model: 'discuss.channel',
                method: 'mute_participant',
                args: [cardComponent.props.thread.id, session.id],
                kwargs: {}
            });
            
            showNotification(`已静音 ${session.channelMember?.persona?.name}`, 'success');
        } catch (error) {
            console.error('静音参与者失败:', error);
            showNotification('静音操作失败', 'error');
        }
    },
    
    startPrivateChat: (session, cardComponent) => {
        const member = session.channelMember;
        if (member && member.persona) {
            // 打开私聊窗口
            const chatWindow = ParticipantCardEnhancer.createPrivateChatWindow(member.persona);
            document.body.appendChild(chatWindow);
        }
    },
    
    createPrivateChatWindow: (persona) => {
        const chatWindow = document.createElement('div');
        chatWindow.className = 'private-chat-window';
        chatWindow.innerHTML = `
            <div class="chat-header">
                <div class="chat-user-info">
                    <img src="${persona.avatarUrl}" alt="${persona.name}" class="user-avatar">
                    <span class="user-name">${persona.name}</span>
                </div>
                <button class="close-chat-btn">&times;</button>
            </div>
            
            <div class="chat-messages">
                <!-- 聊天消息列表 -->
            </div>
            
            <div class="chat-input">
                <input type="text" placeholder="输入消息..." class="message-input">
                <button class="send-btn">发送</button>
            </div>
        `;
        
        // 绑定聊天事件
        ParticipantCardEnhancer.bindChatEvents(chatWindow, persona);
        
        return chatWindow;
    },
    
    bindChatEvents: (chatWindow, persona) => {
        // 关闭聊天窗口
        chatWindow.querySelector('.close-chat-btn').addEventListener('click', () => {
            chatWindow.remove();
        });
        
        // 发送消息
        const sendMessage = () => {
            const input = chatWindow.querySelector('.message-input');
            const message = input.value.trim();
            if (message) {
                ParticipantCardEnhancer.sendPrivateMessage(persona, message);
                input.value = '';
            }
        };
        
        chatWindow.querySelector('.send-btn').addEventListener('click', sendMessage);
        chatWindow.querySelector('.message-input').addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
    },
    
    sendPrivateMessage: async (persona, message) => {
        try {
            await rpc('/web/dataset/call_kw/mail.thread/send_private_message', {
                model: 'mail.thread',
                method: 'send_private_message',
                args: [persona.id, message],
                kwargs: {}
            });
            
            // 在聊天窗口中显示消息
            ParticipantCardEnhancer.displayMessage(persona, message, true);
        } catch (error) {
            console.error('发送私聊消息失败:', error);
            showNotification('消息发送失败', 'error');
        }
    },
    
    displayMessage: (persona, message, isSent) => {
        const chatMessages = document.querySelector('.chat-messages');
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${isSent ? 'sent' : 'received'}`;
        messageElement.innerHTML = `
            <div class="message-content">${message}</div>
            <div class="message-time">${new Date().toLocaleTimeString()}</div>
        `;
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
};
```

### 2. 拖拽功能

```javascript
// 参与者卡片拖拽功能
const CardDragManager = {
    enableDrag: (cardComponent) => {
        const cardElement = cardComponent.el;
        
        cardElement.addEventListener('mousedown', (event) => {
            if (event.button !== 0) return; // 只处理左键
            
            CardDragManager.startDrag(event, cardComponent);
        });
    },
    
    startDrag: (event, cardComponent) => {
        event.preventDefault();
        
        const cardElement = cardComponent.el;
        const rect = cardElement.getBoundingClientRect();
        
        const dragData = {
            startX: event.clientX,
            startY: event.clientY,
            offsetX: event.clientX - rect.left,
            offsetY: event.clientY - rect.top,
            originalPosition: {
                left: rect.left,
                top: rect.top
            }
        };
        
        // 设置拖拽状态
        cardComponent.state.drag = true;
        cardComponent.state.dragPos = { x: rect.left, y: rect.top };
        
        // 添加拖拽样式
        cardElement.classList.add('dragging');
        
        // 绑定拖拽事件
        const onMouseMove = (moveEvent) => {
            CardDragManager.onDrag(moveEvent, cardComponent, dragData);
        };
        
        const onMouseUp = (upEvent) => {
            CardDragManager.endDrag(upEvent, cardComponent, dragData);
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        };
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    },
    
    onDrag: (event, cardComponent, dragData) => {
        const newX = event.clientX - dragData.offsetX;
        const newY = event.clientY - dragData.offsetY;
        
        // 更新位置
        cardComponent.state.dragPos = { x: newX, y: newY };
        
        // 应用位置变化
        cardComponent.el.style.transform = `translate(${newX}px, ${newY}px)`;
        
        // 检查拖拽目标
        CardDragManager.checkDropTarget(event, cardComponent);
    },
    
    endDrag: (event, cardComponent, dragData) => {
        const cardElement = cardComponent.el;
        
        // 移除拖拽状态
        cardComponent.state.drag = false;
        cardElement.classList.remove('dragging');
        
        // 检查是否有有效的放置目标
        const dropTarget = CardDragManager.getDropTarget(event);
        
        if (dropTarget) {
            CardDragManager.handleDrop(cardComponent, dropTarget);
        } else {
            // 恢复原始位置
            CardDragManager.resetPosition(cardComponent);
        }
    },
    
    checkDropTarget: (event, cardComponent) => {
        const dropTarget = CardDragManager.getDropTarget(event);
        
        // 更新拖拽指示器
        document.querySelectorAll('.drop-target').forEach(target => {
            target.classList.remove('drag-over');
        });
        
        if (dropTarget) {
            dropTarget.classList.add('drag-over');
        }
    },
    
    getDropTarget: (event) => {
        const elementBelow = document.elementFromPoint(event.clientX, event.clientY);
        return elementBelow?.closest('.drop-target');
    },
    
    handleDrop: (cardComponent, dropTarget) => {
        const dropType = dropTarget.dataset.dropType;
        
        switch (dropType) {
            case 'pin-area':
                CardDragManager.pinToArea(cardComponent, dropTarget);
                break;
            case 'sidebar':
                CardDragManager.moveToSidebar(cardComponent);
                break;
            case 'main-view':
                CardDragManager.moveToMainView(cardComponent);
                break;
            default:
                CardDragManager.resetPosition(cardComponent);
        }
    },
    
    pinToArea: (cardComponent, area) => {
        const session = cardComponent.props.cardData.session;
        const thread = cardComponent.props.thread;
        
        // 固定到指定区域
        thread.activeRtcSession = session;
        
        showNotification(`已将 ${session.channelMember?.persona?.name} 固定到主视图`, 'success');
        CardDragManager.resetPosition(cardComponent);
    },
    
    moveToSidebar: (cardComponent) => {
        // 移动到侧边栏
        showNotification('已移动到侧边栏', 'info');
        CardDragManager.resetPosition(cardComponent);
    },
    
    moveToMainView: (cardComponent) => {
        // 移动到主视图
        showNotification('已移动到主视图', 'info');
        CardDragManager.resetPosition(cardComponent);
    },
    
    resetPosition: (cardComponent) => {
        cardComponent.el.style.transform = '';
        cardComponent.state.dragPos = undefined;
    }
};
```

## 技术特点

### 1. 组件集成
- 集成视频组件
- 上下文菜单支持
- 弹出框钩子

### 2. 交互功能
- 拖拽操作
- 悬停效果
- 快速操作

### 3. 状态管理
- 连接状态显示
- 视频下载管理
- 实时状态更新

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个子组件
- 统一的卡片接口

### 2. 装饰器模式 (Decorator Pattern)
- 增强卡片功能
- 可选的功能扩展

### 3. 观察者模式 (Observer Pattern)
- 状态变化监听
- 事件驱动更新

## 注意事项

1. **性能优化**: 避免频繁的DOM操作
2. **内存管理**: 及时清理事件监听器
3. **用户体验**: 流畅的交互动画
4. **状态同步**: 保持状态的一致性

## 扩展建议

1. **手势支持**: 支持触摸手势操作
2. **键盘快捷键**: 添加键盘快捷键支持
3. **自定义主题**: 支持自定义卡片主题
4. **动画效果**: 增加更多动画效果
5. **无障碍支持**: 改进无障碍访问

该组件是视频通话界面中的核心显示单元，提供了丰富的参与者交互功能。

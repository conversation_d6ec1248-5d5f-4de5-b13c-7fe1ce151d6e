# Media Monitoring - 媒体监控

## 概述

`media_monitoring.js` 实现了 Odoo 讨论应用中的音频媒体监控功能，用于实时监测音频流的活动状态和音量级别。该模块支持音频轨道监控、频率范围分析、音量阈值检测、说话状态识别等功能，使用Web Audio API和AudioWorklet技术进行高性能音频处理，提供了完整的音频活动检测解决方案，是视频通话系统中音频处理的核心组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/media_monitoring.js`
- **行数**: 219
- **模块**: `@mail/discuss/call/common/media_monitoring`

## 核心常量

### 人声频率范围

```javascript
const HUMAN_VOICE_FREQUENCY_RANGE = [80, 1000];
```

**频率特性**:
- **低频**: 80Hz - 人声基频下限
- **高频**: 1000Hz - 人声主要频率上限
- **用途**: 过滤非人声频率，提高检测准确性

## 核心功能

### 1. 音频监控函数

```javascript
async function monitorAudio(track, processorOptions) {
    // 克隆音频轨道以避免影响原始轨道
    const monitoredTrack = track.clone();
    monitoredTrack.enabled = true;
    const stream = new window.MediaStream([monitoredTrack]);
    
    // 创建音频上下文
    const AudioContext = window.AudioContext || window.webkitAudioContext;
    if (!AudioContext) {
        throw "missing audio context";
    }
    const audioContext = new AudioContext();
    const source = audioContext.createMediaStreamSource(stream);

    let processor;
    try {
        // 优先使用AudioWorklet处理器
        processor = await _loadAudioWorkletProcessor(source, audioContext, processorOptions);
    } catch {
        // 降级到ScriptProcessor（Safari兼容）
        processor = _loadScriptProcessor(source, audioContext, processorOptions);
    }

    // 返回清理函数
    return async () => {
        // 清理资源...
    };
}
```

**监控特性**:
- **轨道克隆**: 避免影响原始音频轨道
- **双重处理器**: AudioWorklet和ScriptProcessor兼容
- **资源管理**: 提供清理函数防止内存泄漏

### 2. 处理器选项

```javascript
/**
 * @param {Object} [processorOptions] 音频处理器选项
 * @param {Array<number>} [processorOptions.frequencyRange] 监控的频率范围(Hz)
 * @param {number} [processorOptions.minimumActiveCycles] 最小活跃周期数
 * @param {function(boolean):void} [processorOptions.onThreshold] 阈值回调函数
 * @param {function(number):void} [processorOptions.onTic] 周期回调函数
 * @param {number} [processorOptions.volumeThreshold] 音量阈值
 */
```

**选项说明**:
- **frequencyRange**: 自定义监控频率范围
- **minimumActiveCycles**: 防止音量抖动的最小周期
- **onThreshold**: 超过阈值时的回调
- **onTic**: 每个处理周期的回调
- **volumeThreshold**: 音频检测的最小音量值

## 使用场景

### 1. 通话音频监控

```javascript
// 通话音频监控系统
const CallAudioMonitor = {
    setupAudioMonitoring: async (audioTrack, options = {}) => {
        try {
            const monitorConfig = {
                frequencyRange: options.frequencyRange || HUMAN_VOICE_FREQUENCY_RANGE,
                volumeThreshold: options.volumeThreshold || 0.01,
                minimumActiveCycles: options.minimumActiveCycles || 3,
                onThreshold: (isActive) => {
                    CallAudioMonitor.handleSpeakingStateChange(isActive);
                },
                onTic: (volume) => {
                    CallAudioMonitor.handleVolumeUpdate(volume);
                }
            };
            
            const disconnect = await monitorAudio(audioTrack, monitorConfig);
            
            return {
                disconnect,
                updateThreshold: (newThreshold) => {
                    monitorConfig.volumeThreshold = newThreshold;
                },
                updateFrequencyRange: (newRange) => {
                    monitorConfig.frequencyRange = newRange;
                }
            };
        } catch (error) {
            console.error('音频监控设置失败:', error);
            throw error;
        }
    },
    
    handleSpeakingStateChange: (isActive) => {
        const speakingIndicator = document.querySelector('.speaking-indicator');
        if (speakingIndicator) {
            speakingIndicator.classList.toggle('active', isActive);
        }
        
        // 通知其他参与者
        CallAudioMonitor.notifySpeakingState(isActive);
        
        // 记录说话统计
        CallAudioMonitor.recordSpeakingActivity(isActive);
    },
    
    handleVolumeUpdate: (volume) => {
        // 更新音量指示器
        const volumeBar = document.querySelector('.volume-bar');
        if (volumeBar) {
            const percentage = Math.min(volume * 100, 100);
            volumeBar.style.width = `${percentage}%`;
        }
        
        // 更新音量级别
        CallAudioMonitor.updateVolumeLevel(volume);
    },
    
    notifySpeakingState: (isActive) => {
        const rtcService = useService("discuss.rtc");
        if (rtcService && rtcService.selfSession) {
            rtcService.selfSession.isTalking = isActive;
        }
    },
    
    recordSpeakingActivity: (isActive) => {
        const now = Date.now();
        
        if (isActive && !CallAudioMonitor.speakingStartTime) {
            CallAudioMonitor.speakingStartTime = now;
        } else if (!isActive && CallAudioMonitor.speakingStartTime) {
            const duration = now - CallAudioMonitor.speakingStartTime;
            CallAudioMonitor.addSpeakingSession(duration);
            CallAudioMonitor.speakingStartTime = null;
        }
    },
    
    addSpeakingSession: (duration) => {
        if (!CallAudioMonitor.speakingSessions) {
            CallAudioMonitor.speakingSessions = [];
        }
        
        CallAudioMonitor.speakingSessions.push({
            duration,
            timestamp: Date.now()
        });
        
        // 限制记录数量
        if (CallAudioMonitor.speakingSessions.length > 100) {
            CallAudioMonitor.speakingSessions.shift();
        }
    },
    
    updateVolumeLevel: (volume) => {
        // 计算音量级别
        let level = 'silent';
        if (volume > 0.5) {
            level = 'loud';
        } else if (volume > 0.1) {
            level = 'normal';
        } else if (volume > 0.01) {
            level = 'quiet';
        }
        
        // 更新UI
        const volumeIndicator = document.querySelector('.volume-level-indicator');
        if (volumeIndicator) {
            volumeIndicator.className = `volume-level-indicator ${level}`;
            volumeIndicator.title = `音量级别: ${level}`;
        }
    },
    
    getSpeakingStatistics: () => {
        if (!CallAudioMonitor.speakingSessions) {
            return {
                totalSessions: 0,
                totalDuration: 0,
                averageDuration: 0,
                longestSession: 0
            };
        }
        
        const sessions = CallAudioMonitor.speakingSessions;
        const totalDuration = sessions.reduce((sum, session) => sum + session.duration, 0);
        const longestSession = Math.max(...sessions.map(s => s.duration));
        
        return {
            totalSessions: sessions.length,
            totalDuration,
            averageDuration: totalDuration / sessions.length,
            longestSession
        };
    }
};
```

### 2. 音频质量监控

```javascript
// 音频质量监控
const AudioQualityMonitor = {
    setupQualityMonitoring: async (audioTrack) => {
        const qualityMetrics = {
            volume: [],
            frequency: [],
            noise: [],
            clarity: []
        };
        
        const disconnect = await monitorAudio(audioTrack, {
            frequencyRange: [20, 20000], // 全频率范围
            onTic: (volume, frequencyData) => {
                AudioQualityMonitor.analyzeQuality(volume, frequencyData, qualityMetrics);
            }
        });
        
        // 定期生成质量报告
        const reportInterval = setInterval(() => {
            AudioQualityMonitor.generateQualityReport(qualityMetrics);
        }, 10000); // 每10秒
        
        return {
            disconnect: () => {
                disconnect();
                clearInterval(reportInterval);
            },
            getMetrics: () => qualityMetrics
        };
    },
    
    analyzeQuality: (volume, frequencyData, metrics) => {
        // 记录音量
        metrics.volume.push(volume);
        
        // 分析频率分布
        if (frequencyData) {
            const frequencyAnalysis = AudioQualityMonitor.analyzeFrequency(frequencyData);
            metrics.frequency.push(frequencyAnalysis);
            
            // 检测噪音
            const noiseLevel = AudioQualityMonitor.detectNoise(frequencyData);
            metrics.noise.push(noiseLevel);
            
            // 评估清晰度
            const clarity = AudioQualityMonitor.assessClarity(frequencyData);
            metrics.clarity.push(clarity);
        }
        
        // 限制数据量
        Object.keys(metrics).forEach(key => {
            if (metrics[key].length > 1000) {
                metrics[key] = metrics[key].slice(-500);
            }
        });
    },
    
    analyzeFrequency: (frequencyData) => {
        const bins = frequencyData.length;
        const sampleRate = 48000; // 假设采样率
        const binSize = sampleRate / 2 / bins;
        
        let lowFreq = 0, midFreq = 0, highFreq = 0;
        
        for (let i = 0; i < bins; i++) {
            const frequency = i * binSize;
            const magnitude = frequencyData[i];
            
            if (frequency < 250) {
                lowFreq += magnitude;
            } else if (frequency < 4000) {
                midFreq += magnitude;
            } else {
                highFreq += magnitude;
            }
        }
        
        return { lowFreq, midFreq, highFreq };
    },
    
    detectNoise: (frequencyData) => {
        // 简单的噪音检测：高频能量比例
        const totalEnergy = frequencyData.reduce((sum, val) => sum + val, 0);
        const highFreqStart = Math.floor(frequencyData.length * 0.7);
        const highFreqEnergy = frequencyData.slice(highFreqStart).reduce((sum, val) => sum + val, 0);
        
        return totalEnergy > 0 ? highFreqEnergy / totalEnergy : 0;
    },
    
    assessClarity: (frequencyData) => {
        // 评估语音清晰度：中频能量集中度
        const voiceRange = AudioQualityMonitor.getVoiceFrequencyRange(frequencyData);
        const voiceEnergy = voiceRange.reduce((sum, val) => sum + val, 0);
        const totalEnergy = frequencyData.reduce((sum, val) => sum + val, 0);
        
        return totalEnergy > 0 ? voiceEnergy / totalEnergy : 0;
    },
    
    getVoiceFrequencyRange: (frequencyData) => {
        // 提取人声频率范围的数据
        const bins = frequencyData.length;
        const sampleRate = 48000;
        const binSize = sampleRate / 2 / bins;
        
        const startBin = Math.floor(HUMAN_VOICE_FREQUENCY_RANGE[0] / binSize);
        const endBin = Math.floor(HUMAN_VOICE_FREQUENCY_RANGE[1] / binSize);
        
        return frequencyData.slice(startBin, endBin);
    },
    
    generateQualityReport: (metrics) => {
        const report = {
            timestamp: Date.now(),
            volume: AudioQualityMonitor.calculateStats(metrics.volume),
            noise: AudioQualityMonitor.calculateStats(metrics.noise),
            clarity: AudioQualityMonitor.calculateStats(metrics.clarity),
            overall: 'good' // 综合评分
        };
        
        // 计算综合评分
        report.overall = AudioQualityMonitor.calculateOverallQuality(report);
        
        // 显示质量指示器
        AudioQualityMonitor.updateQualityIndicator(report);
        
        return report;
    },
    
    calculateStats: (data) => {
        if (data.length === 0) return { avg: 0, min: 0, max: 0 };
        
        const avg = data.reduce((sum, val) => sum + val, 0) / data.length;
        const min = Math.min(...data);
        const max = Math.max(...data);
        
        return { avg, min, max };
    },
    
    calculateOverallQuality: (report) => {
        const volumeScore = Math.min(report.volume.avg * 10, 1);
        const noiseScore = 1 - report.noise.avg;
        const clarityScore = report.clarity.avg;
        
        const overallScore = (volumeScore + noiseScore + clarityScore) / 3;
        
        if (overallScore > 0.8) return 'excellent';
        if (overallScore > 0.6) return 'good';
        if (overallScore > 0.4) return 'fair';
        return 'poor';
    },
    
    updateQualityIndicator: (report) => {
        const indicator = document.querySelector('.audio-quality-indicator');
        if (indicator) {
            indicator.className = `audio-quality-indicator ${report.overall}`;
            indicator.title = `音频质量: ${report.overall}`;
        }
    }
};
```

### 3. 自适应音频处理

```javascript
// 自适应音频处理
const AdaptiveAudioProcessor = {
    setupAdaptiveProcessing: async (audioTrack) => {
        const adaptiveConfig = {
            volumeThreshold: 0.01,
            frequencyRange: HUMAN_VOICE_FREQUENCY_RANGE,
            adaptationInterval: 5000, // 5秒调整一次
            learningRate: 0.1
        };
        
        const disconnect = await monitorAudio(audioTrack, {
            ...adaptiveConfig,
            onTic: (volume, frequencyData) => {
                AdaptiveAudioProcessor.collectData(volume, frequencyData);
            }
        });
        
        // 启动自适应调整
        const adaptationTimer = setInterval(() => {
            AdaptiveAudioProcessor.adaptParameters(adaptiveConfig);
        }, adaptiveConfig.adaptationInterval);
        
        return {
            disconnect: () => {
                disconnect();
                clearInterval(adaptationTimer);
            },
            getConfig: () => adaptiveConfig
        };
    },
    
    collectData: (volume, frequencyData) => {
        if (!AdaptiveAudioProcessor.dataBuffer) {
            AdaptiveAudioProcessor.dataBuffer = {
                volumes: [],
                frequencies: [],
                timestamps: []
            };
        }
        
        const buffer = AdaptiveAudioProcessor.dataBuffer;
        buffer.volumes.push(volume);
        buffer.frequencies.push(frequencyData);
        buffer.timestamps.push(Date.now());
        
        // 限制缓冲区大小
        const maxSize = 1000;
        if (buffer.volumes.length > maxSize) {
            buffer.volumes = buffer.volumes.slice(-maxSize);
            buffer.frequencies = buffer.frequencies.slice(-maxSize);
            buffer.timestamps = buffer.timestamps.slice(-maxSize);
        }
    },
    
    adaptParameters: (config) => {
        const buffer = AdaptiveAudioProcessor.dataBuffer;
        if (!buffer || buffer.volumes.length < 100) {
            return; // 数据不足
        }
        
        // 自适应音量阈值
        const avgVolume = buffer.volumes.reduce((sum, v) => sum + v, 0) / buffer.volumes.length;
        const targetThreshold = avgVolume * 0.3; // 30%的平均音量作为阈值
        
        config.volumeThreshold = config.volumeThreshold * (1 - config.learningRate) + 
                                targetThreshold * config.learningRate;
        
        // 自适应频率范围
        if (buffer.frequencies.length > 0) {
            const optimalRange = AdaptiveAudioProcessor.findOptimalFrequencyRange(buffer.frequencies);
            config.frequencyRange[0] = config.frequencyRange[0] * (1 - config.learningRate) + 
                                      optimalRange[0] * config.learningRate;
            config.frequencyRange[1] = config.frequencyRange[1] * (1 - config.learningRate) + 
                                      optimalRange[1] * config.learningRate;
        }
        
        console.log('自适应参数已更新:', config);
    },
    
    findOptimalFrequencyRange: (frequencyDataArray) => {
        // 分析频率数据找到最优范围
        const avgFrequencyData = AdaptiveAudioProcessor.averageFrequencyData(frequencyDataArray);
        
        // 找到能量集中的频率范围
        const energyThreshold = Math.max(...avgFrequencyData) * 0.1;
        let startFreq = 0, endFreq = 0;
        
        for (let i = 0; i < avgFrequencyData.length; i++) {
            if (avgFrequencyData[i] > energyThreshold) {
                if (startFreq === 0) startFreq = i;
                endFreq = i;
            }
        }
        
        // 转换为实际频率
        const sampleRate = 48000;
        const binSize = sampleRate / 2 / avgFrequencyData.length;
        
        return [startFreq * binSize, endFreq * binSize];
    },
    
    averageFrequencyData: (frequencyDataArray) => {
        if (frequencyDataArray.length === 0) return [];
        
        const length = frequencyDataArray[0].length;
        const avgData = new Array(length).fill(0);
        
        frequencyDataArray.forEach(data => {
            for (let i = 0; i < length; i++) {
                avgData[i] += data[i];
            }
        });
        
        return avgData.map(val => val / frequencyDataArray.length);
    }
};
```

## 技术特点

### 1. Web Audio API集成
- AudioContext音频上下文
- MediaStreamSource媒体流源
- AudioWorklet高性能处理

### 2. 兼容性处理
- AudioWorklet优先
- ScriptProcessor降级
- 跨浏览器支持

### 3. 实时处理
- 低延迟音频分析
- 频率域处理
- 音量检测

### 4. 资源管理
- 轨道克隆保护
- 内存泄漏防护
- 清理函数提供

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 处理器创建和选择
- 兼容性适配

### 2. 观察者模式 (Observer Pattern)
- 音频事件监听
- 状态变化通知

### 3. 策略模式 (Strategy Pattern)
- 不同处理器策略
- 自适应参数调整

## 注意事项

1. **浏览器兼容性**: AudioWorklet在Safari中的支持
2. **性能影响**: 实时音频处理的CPU消耗
3. **内存管理**: 及时清理音频资源
4. **隐私考虑**: 音频数据的处理和存储

## 扩展建议

1. **AI增强**: 集成AI音频处理算法
2. **噪音抑制**: 高级噪音抑制功能
3. **语音识别**: 集成语音转文字功能
4. **音频效果**: 支持音频特效处理
5. **质量自适应**: 根据网络状况调整处理质量

该模块为视频通话系统提供了完整的音频监控和分析功能，是音频处理的核心组件。

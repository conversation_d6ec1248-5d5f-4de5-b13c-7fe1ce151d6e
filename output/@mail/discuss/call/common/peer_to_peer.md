# Peer to Peer - 点对点通信

## 概述

`peer_to_peer.js` 实现了 Odoo 讨论应用中的点对点通信核心功能，负责管理WebRTC点对点连接网络。该模块支持多媒体流传输、ICE连接管理、数据通道通信、连接状态监控等功能，集成了完整的WebRTC协议栈，提供了可靠的P2P通信基础设施，是视频通话系统中的核心网络通信层。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/peer_to_peer.js`
- **行数**: 869
- **模块**: `@mail/discuss/call/common/peer_to_peer`

## 依赖关系

```javascript
// 核心依赖
'@web/core/network/rpc'     // RPC网络通信
'@web/core/browser/browser' // 浏览器服务
```

## 核心常量

### 流类型定义

```javascript
const STREAM_TYPE = Object.freeze({
    AUDIO: "audio",
    CAMERA: "camera",
    SCREEN: "screen",
});
```

### 更新事件类型

```javascript
const UPDATE_EVENT = Object.freeze({
    BROADCAST: "broadcast",
    CONNECTION_CHANGE: "connection_change",
    DISCONNECT: "disconnect",
    INFO_CHANGE: "info_change",
    TRACK: "track",
});
```

### 内部事件类型

```javascript
const INTERNAL_EVENT = Object.freeze({
    ANSWER: "answer",
    BROADCAST: "broadcast",
    DISCONNECT: "disconnect",
    ICE_CANDIDATE: "ice-candidate",
    INFO: "info",
    OFFER: "offer",
    TRACK_CHANGE: "trackChange",
});
```

### 配置常量

```javascript
const DEFAULT_ICE_SERVERS = [
    { urls: ["stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302"] },
];

const INITIAL_RECONNECT_DELAY = 2_000 + Math.random() * 1_000;
const MAXIMUM_RECONNECT_DELAY = 25_000 + Math.random() * 5_000;
const INVALID_ICE_CONNECTION_STATES = new Set(["disconnected", "failed", "closed"]);
const IS_CLIENT_RTC_COMPATIBLE = Boolean(window.RTCPeerConnection && window.MediaStream);
```

## 数据结构

### Media 对象

```javascript
/**
 * @typedef {Object} Media
 * @property {MediaStreamTrack | null} track - 关联的媒体轨道
 * @property {boolean} active - 远程是否正在活跃流传输
 * @property {boolean} accepted - 本地是否接受下载此轨道
 */
```

### Info 对象

```javascript
/**
 * @typedef {Object} Info
 * @property {boolean} isSelfMuted - 是否自我静音
 * @property {boolean} isRaisingHand - 是否举手
 * @property {boolean} isDeaf - 是否耳聋
 * @property {boolean} isTalking - 是否正在说话
 * @property {boolean} isCameraOn - 摄像头是否开启
 * @property {boolean} isScreenSharingOn - 屏幕共享是否开启
 */
```

## 核心类

### Peer 类

```javascript
class Peer {
    /** @type {number} */
    id;
    /** @type {RTCPeerConnection} */
    connection;
    /** @type {RTCDataChannel} */
    dataChannel;
    /** @type {Object<STREAM_TYPE, Media>} */
    medias = Object.seal({
        [STREAM_TYPE.AUDIO]: { track: null, active: false, accepted: true },
        [STREAM_TYPE.SCREEN]: { track: null, active: false, accepted: true },
        [STREAM_TYPE.CAMERA]: { track: null, active: false, accepted: true },
    });
}
```

**Peer特性**:
- **连接管理**: RTCPeerConnection实例
- **数据通道**: RTCDataChannel支持
- **媒体管理**: 多种媒体类型支持
- **状态跟踪**: 连接和媒体状态

### PeerToPeer 类

```javascript
class PeerToPeer extends EventTarget {
    /** @type {number} */
    selfId;
    /** @type {number} */
    channelId;
    /** @type {Map<number, Peer>} */
    peers;
    /** @type {Object} */
    iceServers;
    /** @type {string} */
    notificationRoute;
}
```

**PeerToPeer特性**:
- **网络管理**: 管理多个Peer连接
- **事件驱动**: 继承EventTarget
- **配置管理**: ICE服务器配置
- **通知路由**: 消息通知路由

## 使用场景

### 1. P2P网络管理

```javascript
// P2P网络管理增强
const P2PNetworkManager = {
    createEnhancedP2P: (config = {}) => {
        const p2p = new PeerToPeer({
            notificationRoute: config.notificationRoute || DEFAULT_NOTIFICATION_ROUTE,
            iceServers: config.iceServers || DEFAULT_ICE_SERVERS,
            ...config
        });
        
        // 添加网络监控
        P2PNetworkManager.addNetworkMonitoring(p2p);
        
        // 添加连接优化
        P2PNetworkManager.addConnectionOptimization(p2p);
        
        // 添加故障恢复
        P2PNetworkManager.addFailureRecovery(p2p);
        
        // 添加性能监控
        P2PNetworkManager.addPerformanceMonitoring(p2p);
        
        return p2p;
    },
    
    addNetworkMonitoring: (p2p) => {
        const networkMonitor = {
            connections: new Map(),
            statistics: {
                totalConnections: 0,
                activeConnections: 0,
                failedConnections: 0,
                reconnections: 0
            }
        };
        
        // 监听连接事件
        p2p.addEventListener('update', (event) => {
            P2PNetworkManager.handleNetworkEvent(event, networkMonitor);
        });
        
        // 定期生成网络报告
        setInterval(() => {
            P2PNetworkManager.generateNetworkReport(networkMonitor);
        }, 30000); // 每30秒
        
        p2p.networkMonitor = networkMonitor;
    },
    
    handleNetworkEvent: (event, monitor) => {
        const { type, detail } = event;
        
        switch (type) {
            case UPDATE_EVENT.CONNECTION_CHANGE:
                P2PNetworkManager.handleConnectionChange(detail, monitor);
                break;
            case UPDATE_EVENT.DISCONNECT:
                P2PNetworkManager.handleDisconnection(detail, monitor);
                break;
            case UPDATE_EVENT.TRACK:
                P2PNetworkManager.handleTrackEvent(detail, monitor);
                break;
        }
    },
    
    handleConnectionChange: (detail, monitor) => {
        const { peerId, connectionState } = detail;
        
        if (!monitor.connections.has(peerId)) {
            monitor.connections.set(peerId, {
                id: peerId,
                state: connectionState,
                connectedAt: Date.now(),
                reconnectCount: 0,
                lastStateChange: Date.now()
            });
            monitor.statistics.totalConnections++;
        } else {
            const connection = monitor.connections.get(peerId);
            connection.state = connectionState;
            connection.lastStateChange = Date.now();
        }
        
        // 更新活跃连接数
        monitor.statistics.activeConnections = Array.from(monitor.connections.values())
            .filter(conn => conn.state === 'connected').length;
    },
    
    handleDisconnection: (detail, monitor) => {
        const { peerId } = detail;
        
        if (monitor.connections.has(peerId)) {
            const connection = monitor.connections.get(peerId);
            connection.state = 'disconnected';
            connection.disconnectedAt = Date.now();
            monitor.statistics.activeConnections--;
        }
    },
    
    generateNetworkReport: (monitor) => {
        const report = {
            timestamp: Date.now(),
            statistics: { ...monitor.statistics },
            connections: Array.from(monitor.connections.values()),
            networkHealth: P2PNetworkManager.calculateNetworkHealth(monitor)
        };
        
        console.log('P2P网络报告:', report);
        
        // 发送报告到监控系统
        P2PNetworkManager.sendNetworkReport(report);
    },
    
    calculateNetworkHealth: (monitor) => {
        const { totalConnections, activeConnections, failedConnections } = monitor.statistics;
        
        if (totalConnections === 0) return 'unknown';
        
        const successRate = (totalConnections - failedConnections) / totalConnections;
        const activeRate = activeConnections / totalConnections;
        
        const healthScore = (successRate * 0.6 + activeRate * 0.4) * 100;
        
        if (healthScore >= 90) return 'excellent';
        if (healthScore >= 75) return 'good';
        if (healthScore >= 60) return 'fair';
        return 'poor';
    },
    
    sendNetworkReport: (report) => {
        // 发送到监控服务器或本地存储
        try {
            rpc('/mail/rtc/network_report', { report });
        } catch (error) {
            console.warn('发送网络报告失败:', error);
        }
    },
    
    addConnectionOptimization: (p2p) => {
        // ICE候选优化
        P2PNetworkManager.optimizeICECandidates(p2p);
        
        // 带宽自适应
        P2PNetworkManager.addBandwidthAdaptation(p2p);
        
        // 连接质量优化
        P2PNetworkManager.addQualityOptimization(p2p);
    },
    
    optimizeICECandidates: (p2p) => {
        // 优先使用本地网络候选
        const originalCreatePeer = p2p.createPeer;
        p2p.createPeer = function(peerId) {
            const peer = originalCreatePeer.call(this, peerId);
            
            // 配置ICE候选优先级
            peer.connection.addEventListener('icecandidate', (event) => {
                if (event.candidate) {
                    P2PNetworkManager.prioritizeICECandidate(event.candidate);
                }
            });
            
            return peer;
        };
    },
    
    prioritizeICECandidate: (candidate) => {
        // 根据候选类型设置优先级
        const candidateType = candidate.candidate.split(' ')[7];
        
        switch (candidateType) {
            case 'host':
                candidate.priority = 1000; // 最高优先级
                break;
            case 'srflx':
                candidate.priority = 800;
                break;
            case 'prflx':
                candidate.priority = 600;
                break;
            case 'relay':
                candidate.priority = 400; // 最低优先级
                break;
        }
    },
    
    addBandwidthAdaptation: (p2p) => {
        const bandwidthMonitor = {
            measurements: new Map(),
            adaptationRules: {
                excellent: { video: 2000000, audio: 128000 }, // 2Mbps video, 128kbps audio
                good: { video: 1000000, audio: 64000 },       // 1Mbps video, 64kbps audio
                fair: { video: 500000, audio: 32000 },        // 500kbps video, 32kbps audio
                poor: { video: 250000, audio: 16000 }         // 250kbps video, 16kbps audio
            }
        };
        
        // 定期测量带宽
        setInterval(() => {
            P2PNetworkManager.measureBandwidth(p2p, bandwidthMonitor);
        }, 10000); // 每10秒
        
        p2p.bandwidthMonitor = bandwidthMonitor;
    },
    
    measureBandwidth: async (p2p, monitor) => {
        for (const [peerId, peer] of p2p.peers) {
            try {
                const stats = await peer.connection.getStats();
                const bandwidth = P2PNetworkManager.calculateBandwidth(stats);
                
                monitor.measurements.set(peerId, {
                    timestamp: Date.now(),
                    upload: bandwidth.upload,
                    download: bandwidth.download,
                    quality: P2PNetworkManager.assessBandwidthQuality(bandwidth)
                });
                
                // 根据带宽质量调整编码参数
                P2PNetworkManager.adaptEncodingParameters(peer, bandwidth.quality, monitor);
            } catch (error) {
                console.error(`测量带宽失败 (${peerId}):`, error);
            }
        }
    },
    
    calculateBandwidth: (stats) => {
        let uploadBandwidth = 0;
        let downloadBandwidth = 0;
        
        stats.forEach(report => {
            if (report.type === 'outbound-rtp') {
                uploadBandwidth += report.bytesSent || 0;
            } else if (report.type === 'inbound-rtp') {
                downloadBandwidth += report.bytesReceived || 0;
            }
        });
        
        return {
            upload: uploadBandwidth * 8, // 转换为bits
            download: downloadBandwidth * 8
        };
    },
    
    assessBandwidthQuality: (bandwidth) => {
        const totalBandwidth = bandwidth.upload + bandwidth.download;
        
        if (totalBandwidth > 3000000) return 'excellent'; // 3Mbps+
        if (totalBandwidth > 1500000) return 'good';      // 1.5Mbps+
        if (totalBandwidth > 750000) return 'fair';       // 750kbps+
        return 'poor';
    },
    
    adaptEncodingParameters: async (peer, quality, monitor) => {
        const rules = monitor.adaptationRules[quality];
        
        try {
            const senders = peer.connection.getSenders();
            
            for (const sender of senders) {
                if (sender.track) {
                    const params = sender.getParameters();
                    
                    if (sender.track.kind === 'video') {
                        params.encodings[0].maxBitrate = rules.video;
                    } else if (sender.track.kind === 'audio') {
                        params.encodings[0].maxBitrate = rules.audio;
                    }
                    
                    await sender.setParameters(params);
                }
            }
        } catch (error) {
            console.error('调整编码参数失败:', error);
        }
    },
    
    addFailureRecovery: (p2p) => {
        const recoveryManager = {
            failureHistory: new Map(),
            recoveryStrategies: ['restart_ice', 'renegotiate', 'reconnect'],
            maxRetryAttempts: 3
        };
        
        p2p.addEventListener('update', (event) => {
            if (event.type === UPDATE_EVENT.CONNECTION_CHANGE) {
                P2PNetworkManager.handleConnectionFailure(event.detail, p2p, recoveryManager);
            }
        });
        
        p2p.recoveryManager = recoveryManager;
    },
    
    handleConnectionFailure: async (detail, p2p, manager) => {
        const { peerId, connectionState } = detail;
        
        if (INVALID_ICE_CONNECTION_STATES.has(connectionState)) {
            if (!manager.failureHistory.has(peerId)) {
                manager.failureHistory.set(peerId, {
                    attempts: 0,
                    lastAttempt: 0,
                    strategies: [...manager.recoveryStrategies]
                });
            }
            
            const history = manager.failureHistory.get(peerId);
            
            if (history.attempts < manager.maxRetryAttempts) {
                const strategy = history.strategies[history.attempts];
                await P2PNetworkManager.executeRecoveryStrategy(peerId, strategy, p2p);
                
                history.attempts++;
                history.lastAttempt = Date.now();
            } else {
                console.warn(`连接恢复失败，已达到最大重试次数: ${peerId}`);
                P2PNetworkManager.notifyConnectionFailure(peerId);
            }
        }
    },
    
    executeRecoveryStrategy: async (peerId, strategy, p2p) => {
        const peer = p2p.peers.get(peerId);
        if (!peer) return;
        
        try {
            switch (strategy) {
                case 'restart_ice':
                    await peer.connection.restartIce();
                    console.log(`已重启ICE连接: ${peerId}`);
                    break;
                    
                case 'renegotiate':
                    await P2PNetworkManager.renegotiateConnection(peer);
                    console.log(`已重新协商连接: ${peerId}`);
                    break;
                    
                case 'reconnect':
                    await P2PNetworkManager.reconnectPeer(peerId, p2p);
                    console.log(`已重新连接: ${peerId}`);
                    break;
            }
        } catch (error) {
            console.error(`执行恢复策略失败 (${strategy}):`, error);
        }
    },
    
    renegotiateConnection: async (peer) => {
        // 重新创建offer/answer
        const offer = await peer.connection.createOffer();
        await peer.connection.setLocalDescription(offer);
        
        // 发送新的offer到对等端
        // 这里需要通过信令服务器发送
    },
    
    reconnectPeer: async (peerId, p2p) => {
        // 断开现有连接
        const peer = p2p.peers.get(peerId);
        if (peer) {
            peer.connection.close();
            p2p.peers.delete(peerId);
        }
        
        // 创建新连接
        const newPeer = p2p.createPeer(peerId);
        p2p.peers.set(peerId, newPeer);
        
        // 重新建立连接
        await p2p.connectToPeer(peerId);
    },
    
    notifyConnectionFailure: (peerId) => {
        // 通知用户连接失败
        showNotification(`与用户 ${peerId} 的连接失败`, 'error');
    },
    
    addPerformanceMonitoring: (p2p) => {
        const performanceMonitor = {
            metrics: new Map(),
            thresholds: {
                latency: 200,      // 200ms
                packetLoss: 0.05,  // 5%
                jitter: 50         // 50ms
            }
        };
        
        // 定期收集性能指标
        setInterval(() => {
            P2PNetworkManager.collectPerformanceMetrics(p2p, performanceMonitor);
        }, 5000); // 每5秒
        
        p2p.performanceMonitor = performanceMonitor;
    },
    
    collectPerformanceMetrics: async (p2p, monitor) => {
        for (const [peerId, peer] of p2p.peers) {
            try {
                const stats = await peer.connection.getStats();
                const metrics = P2PNetworkManager.extractPerformanceMetrics(stats);
                
                monitor.metrics.set(peerId, {
                    timestamp: Date.now(),
                    ...metrics
                });
                
                // 检查性能阈值
                P2PNetworkManager.checkPerformanceThresholds(peerId, metrics, monitor);
            } catch (error) {
                console.error(`收集性能指标失败 (${peerId}):`, error);
            }
        }
    },
    
    extractPerformanceMetrics: (stats) => {
        const metrics = {
            latency: 0,
            packetLoss: 0,
            jitter: 0,
            bandwidth: 0
        };
        
        stats.forEach(report => {
            if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                metrics.latency = report.currentRoundTripTime * 1000; // 转换为ms
            } else if (report.type === 'inbound-rtp') {
                const packetsLost = report.packetsLost || 0;
                const packetsReceived = report.packetsReceived || 0;
                metrics.packetLoss = packetsReceived > 0 ? packetsLost / packetsReceived : 0;
                metrics.jitter = report.jitter * 1000; // 转换为ms
            }
        });
        
        return metrics;
    },
    
    checkPerformanceThresholds: (peerId, metrics, monitor) => {
        const { thresholds } = monitor;
        const warnings = [];
        
        if (metrics.latency > thresholds.latency) {
            warnings.push(`高延迟: ${metrics.latency.toFixed(0)}ms`);
        }
        
        if (metrics.packetLoss > thresholds.packetLoss) {
            warnings.push(`丢包率高: ${(metrics.packetLoss * 100).toFixed(1)}%`);
        }
        
        if (metrics.jitter > thresholds.jitter) {
            warnings.push(`抖动高: ${metrics.jitter.toFixed(0)}ms`);
        }
        
        if (warnings.length > 0) {
            console.warn(`性能警告 (${peerId}):`, warnings.join(', '));
            P2PNetworkManager.handlePerformanceIssue(peerId, warnings);
        }
    },
    
    handlePerformanceIssue: (peerId, warnings) => {
        // 显示性能警告
        showNotification(`连接质量问题: ${warnings.join(', ')}`, 'warning');
        
        // 可以触发自动优化措施
        // 例如降低视频质量、重新协商连接等
    }
};
```

## 技术特点

### 1. WebRTC集成
- 完整的WebRTC协议栈
- ICE连接管理
- 媒体流处理
- 数据通道支持

### 2. 网络管理
- 多Peer连接管理
- 连接状态监控
- 自动重连机制
- 故障恢复策略

### 3. 媒体处理
- 多种媒体类型支持
- 轨道状态管理
- 流传输控制
- 质量自适应

### 4. 事件驱动
- EventTarget继承
- 丰富的事件类型
- 异步事件处理
- 状态变化通知

## 设计模式

### 1. 网络模式 (Network Pattern)
- P2P网络拓扑管理
- 分布式连接协调

### 2. 观察者模式 (Observer Pattern)
- 事件驱动架构
- 状态变化通知

### 3. 策略模式 (Strategy Pattern)
- 不同连接策略
- 故障恢复策略

## 注意事项

1. **浏览器兼容性**: 确保WebRTC API的浏览器支持
2. **网络环境**: 处理各种网络环境和NAT穿越
3. **性能优化**: 大量连接时的性能考虑
4. **错误处理**: 完善的连接错误处理和恢复

## 扩展建议

1. **连接池**: 实现连接池管理优化资源使用
2. **负载均衡**: 支持多服务器负载均衡
3. **安全增强**: 添加端到端加密支持
4. **监控仪表板**: 实现实时连接监控界面
5. **自动优化**: 基于网络条件的自动参数优化

该模块是视频通话系统的核心网络通信层，提供了完整的P2P通信基础设施。

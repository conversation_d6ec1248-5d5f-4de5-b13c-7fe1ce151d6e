# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了对 Odoo 讨论应用中线程模型的补丁扩展，为线程模型添加了RTC会话管理和音效控制功能。该补丁通过Odoo的补丁机制扩展了Thread模型，添加了RTC会话关联、邀请管理、音效播放、视频统计等功能，建立了线程与RTC功能的深度集成，是视频通话系统中线程级别功能的核心组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/thread_model_patch.js`
- **行数**: 75
- **模块**: `@mail/discuss/call/common/thread_model_patch`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/record'       // 记录基类
'@mail/core/common/thread_model' // 线程模型
'@web/core/utils/patch'          // 补丁工具
```

## 补丁定义

### ThreadPatch 对象

```javascript
const ThreadPatch = {
    setup() {
        super.setup(...arguments);
        // RTC会话管理
        this.activeRtcSession = Record.one("RtcSession", { /* ... */ });
        this.rtcInvitingSession = Record.one("RtcSession", { /* ... */ });
        this.rtcSessions = Record.many("RtcSession", { /* ... */ });
        
        // 状态跟踪
        this.hadSelfSession = false;
        this.lastSessionIds = new Set();
    },
    get videoCount() {
        return Object.values(this.store.RtcSession.records).filter((session) => session.hasVideo).length;
    },
};
```

**补丁特性**:
- 扩展setup方法
- 添加RTC会话关联
- 集成音效控制
- 状态变化跟踪

## 核心功能

### 1. 活跃RTC会话管理

```javascript
this.activeRtcSession = Record.one("RtcSession", {
    onAdd(r) {
        this.store.allActiveRtcSessions.add(r);
    },
    onDelete(r) {
        this.store.allActiveRtcSessions.delete(r);
    },
});
```

**会话管理功能**:
- **会话添加**: 新会话自动添加到全局活跃列表
- **会话删除**: 会话结束时自动从全局列表移除
- **状态同步**: 保持线程级别和全局级别的数据一致性

### 2. 邀请会话管理

```javascript
this.rtcInvitingSession = Record.one("RtcSession", {
    onAdd(r) {
        this.rtcSessions.add(r);
        this.store.ringingThreads.add(this);
    },
    onDelete(r) {
        this.store.ringingThreads.delete(this);
    },
});
```

**邀请管理功能**:
- **邀请添加**: 收到邀请时添加到会话列表和铃声列表
- **邀请删除**: 邀请结束时从铃声列表移除
- **铃声控制**: 自动触发来电铃声播放

### 3. RTC会话集合管理

```javascript
this.rtcSessions = Record.many("RtcSession", {
    onDelete(r) {
        this.store.env.services["discuss.rtc"].deleteSession(r.id);
    },
    onUpdate() {
        // 状态跟踪和音效播放逻辑
        const hadSelfSession = this.hadSelfSession;
        const lastSessionIds = this.lastSessionIds;
        this.hadSelfSession = Boolean(this.store.rtc.selfSession?.in(this.rtcSessions));
        this.lastSessionIds = new Set(this.rtcSessions.map((s) => s.id));
        
        // 音效播放条件检查
        if (!hadSelfSession || !this.hadSelfSession || !this.store.env.services["multi_tab"].isOnMainTab()) {
            return;
        }
        
        // 成员加入音效
        if ([...this.lastSessionIds].some((id) => !lastSessionIds.has(id))) {
            this.store.env.services["mail.sound_effects"].play("channel-join");
        }
        
        // 成员离开音效
        if ([...lastSessionIds].some((id) => !this.lastSessionIds.has(id))) {
            this.store.env.services["mail.sound_effects"].play("member-leave");
        }
    },
});
```

**集合管理功能**:
- **会话删除**: 自动调用RTC服务删除会话
- **状态跟踪**: 跟踪自身会话状态和会话ID变化
- **音效播放**: 根据成员加入/离开播放相应音效
- **多标签页**: 只在主标签页播放音效

### 4. 视频统计

```javascript
get videoCount() {
    return Object.values(this.store.RtcSession.records).filter((session) => session.hasVideo).length;
}
```

**统计功能**:
- **视频计数**: 统计当前有视频的会话数量
- **实时更新**: 基于会话状态实时计算
- **全局统计**: 跨所有线程的视频会话统计

## 使用场景

### 1. 线程RTC功能增强

```javascript
// 线程RTC功能增强
const ThreadRTCEnhancer = {
    enhanceThreadRTC: () => {
        const EnhancedThreadPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加线程级别的RTC配置
                this.rtcConfig = {
                    maxParticipants: 50,
                    allowScreenShare: true,
                    allowRecording: false,
                    autoMuteNewJoiners: false,
                    moderatorRequired: false
                };
                
                // 添加RTC统计
                this.rtcStatistics = {
                    totalJoins: 0,
                    totalLeaves: 0,
                    peakParticipants: 0,
                    totalCallTime: 0,
                    averageCallDuration: 0
                };
                
                // 添加RTC事件监听
                this.setupRTCEventListeners();
                
                // 增强会话管理
                this.enhanceSessionManagement();
            },
            
            setupRTCEventListeners() {
                // 监听会话变化
                this.rtcSessions.addEventListener('add', (session) => {
                    this.handleSessionJoin(session);
                });
                
                this.rtcSessions.addEventListener('remove', (session) => {
                    this.handleSessionLeave(session);
                });
                
                // 监听活跃会话变化
                this.activeRtcSession.addEventListener('change', (session) => {
                    this.handleActiveSessionChange(session);
                });
            },
            
            handleSessionJoin(session) {
                this.rtcStatistics.totalJoins++;
                this.rtcStatistics.peakParticipants = Math.max(
                    this.rtcStatistics.peakParticipants,
                    this.rtcSessions.length
                );
                
                // 检查参与者限制
                if (this.rtcSessions.length > this.rtcConfig.maxParticipants) {
                    this.handleParticipantLimitExceeded(session);
                }
                
                // 自动静音新加入者
                if (this.rtcConfig.autoMuteNewJoiners && !session.isSelf) {
                    session.mute();
                }
                
                // 记录加入事件
                this.recordRTCEvent('session_join', {
                    sessionId: session.id,
                    userId: session.partnerId || session.guestId,
                    timestamp: Date.now()
                });
                
                // 通知其他组件
                this.trigger('rtc_session_joined', { session, thread: this });
            },
            
            handleSessionLeave(session) {
                this.rtcStatistics.totalLeaves++;
                
                // 计算通话时长
                if (session.joinTime) {
                    const duration = Date.now() - session.joinTime;
                    this.rtcStatistics.totalCallTime += duration;
                    this.rtcStatistics.averageCallDuration = 
                        this.rtcStatistics.totalCallTime / this.rtcStatistics.totalLeaves;
                }
                
                // 记录离开事件
                this.recordRTCEvent('session_leave', {
                    sessionId: session.id,
                    userId: session.partnerId || session.guestId,
                    duration: session.callDuration,
                    timestamp: Date.now()
                });
                
                // 通知其他组件
                this.trigger('rtc_session_left', { session, thread: this });
            },
            
            handleActiveSessionChange(session) {
                if (session) {
                    // 有活跃会话
                    this.trigger('rtc_call_started', { session, thread: this });
                } else {
                    // 无活跃会话
                    this.trigger('rtc_call_ended', { thread: this });
                }
            },
            
            handleParticipantLimitExceeded(session) {
                // 拒绝新的参与者
                session.leave();
                
                // 发送通知
                this.env.services.notification.add(
                    `通话参与者已达上限 (${this.rtcConfig.maxParticipants})`,
                    { type: 'warning' }
                );
            },
            
            enhanceSessionManagement() {
                // 增强的RTC会话管理
                this.rtcSessions = Record.many("RtcSession", {
                    onDelete(r) {
                        // 调用原有删除逻辑
                        this.store.env.services["discuss.rtc"].deleteSession(r.id);
                        
                        // 清理相关数据
                        this.cleanupSessionData(r);
                    },
                    
                    onUpdate() {
                        // 调用原有更新逻辑
                        const hadSelfSession = this.hadSelfSession;
                        const lastSessionIds = this.lastSessionIds;
                        this.hadSelfSession = Boolean(this.store.rtc.selfSession?.in(this.rtcSessions));
                        this.lastSessionIds = new Set(this.rtcSessions.map((s) => s.id));
                        
                        // 增强的音效控制
                        this.handleEnhancedSoundEffects(hadSelfSession, lastSessionIds);
                        
                        // 更新线程状态
                        this.updateThreadRTCStatus();
                    },
                });
            },
            
            handleEnhancedSoundEffects(hadSelfSession, lastSessionIds) {
                // 检查播放条件
                if (!hadSelfSession || !this.hadSelfSession || !this.store.env.services["multi_tab"].isOnMainTab()) {
                    return;
                }
                
                // 成员加入音效
                const newMembers = [...this.lastSessionIds].filter((id) => !lastSessionIds.has(id));
                if (newMembers.length > 0) {
                    this.playJoinSound(newMembers.length);
                }
                
                // 成员离开音效
                const leftMembers = [...lastSessionIds].filter((id) => !this.lastSessionIds.has(id));
                if (leftMembers.length > 0) {
                    this.playLeaveSound(leftMembers.length);
                }
            },
            
            playJoinSound(count) {
                if (count === 1) {
                    this.store.env.services["mail.sound_effects"].play("channel-join");
                } else {
                    this.store.env.services["mail.sound_effects"].play("multiple-join");
                }
            },
            
            playLeaveSound(count) {
                if (count === 1) {
                    this.store.env.services["mail.sound_effects"].play("member-leave");
                } else {
                    this.store.env.services["mail.sound_effects"].play("multiple-leave");
                }
            },
            
            updateThreadRTCStatus() {
                // 更新线程的RTC状态
                this.rtcStatus = {
                    isActive: this.rtcSessions.length > 0,
                    participantCount: this.rtcSessions.length,
                    hasVideo: this.videoCount > 0,
                    hasScreenShare: this.rtcSessions.some(s => s.isScreenSharingOn),
                    isRecording: this.rtcSessions.some(s => s.isRecording)
                };
            },
            
            cleanupSessionData(session) {
                // 清理会话相关的本地数据
                if (this.sessionDataCache) {
                    this.sessionDataCache.delete(session.id);
                }
                
                // 清理临时文件
                if (session.tempFiles) {
                    session.tempFiles.forEach(file => {
                        URL.revokeObjectURL(file.url);
                    });
                }
            },
            
            recordRTCEvent(eventType, data) {
                if (!this.rtcEventHistory) {
                    this.rtcEventHistory = [];
                }
                
                this.rtcEventHistory.push({
                    type: eventType,
                    data: data,
                    timestamp: Date.now()
                });
                
                // 限制历史记录数量
                if (this.rtcEventHistory.length > 100) {
                    this.rtcEventHistory.shift();
                }
            },
            
            // 获取增强的视频统计
            get videoCount() {
                return this.rtcSessions.filter((session) => session.hasVideo).length;
            },
            
            // 获取音频统计
            get audioCount() {
                return this.rtcSessions.filter((session) => !session.isMute).length;
            },
            
            // 获取屏幕共享统计
            get screenShareCount() {
                return this.rtcSessions.filter((session) => session.isScreenSharingOn).length;
            },
            
            // 获取RTC会话统计
            getRTCSessionStats() {
                return {
                    total: this.rtcSessions.length,
                    video: this.videoCount,
                    audio: this.audioCount,
                    screenShare: this.screenShareCount,
                    talking: this.rtcSessions.filter(s => s.isTalking).length,
                    muted: this.rtcSessions.filter(s => s.isMute).length
                };
            },
            
            // 线程级别的RTC操作
            async startCall(options = {}) {
                const rtcService = this.store.env.services["discuss.rtc"];
                return await rtcService.toggleCall(this, options);
            },
            
            async endCall() {
                const rtcService = this.store.env.services["discuss.rtc"];
                return await rtcService.leaveCall(this);
            },
            
            async muteAll() {
                const promises = this.rtcSessions.map(session => {
                    if (!session.isSelf && !session.isMute) {
                        return session.mute();
                    }
                });
                
                await Promise.all(promises);
            },
            
            async unmuteAll() {
                const promises = this.rtcSessions.map(session => {
                    if (!session.isSelf && session.isMute) {
                        return session.unmute();
                    }
                });
                
                await Promise.all(promises);
            }
        };
        
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程RTC功能增强
ThreadRTCEnhancer.enhanceThreadRTC();
```

### 2. 音效管理增强

```javascript
// 音效管理增强
const ThreadSoundEnhancer = {
    enhanceThreadSounds: () => {
        const SoundPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加音效配置
                this.soundConfig = {
                    enableJoinLeaveSound: true,
                    enableTalkingSound: false,
                    enableNotificationSound: true,
                    customSounds: new Map()
                };
                
                // 音效播放历史
                this.soundHistory = [];
            },
            
            // 增强的音效播放逻辑
            playThreadSound(soundType, options = {}) {
                if (!this.soundConfig.enableJoinLeaveSound && 
                    ['channel-join', 'member-leave'].includes(soundType)) {
                    return;
                }
                
                // 检查自定义音效
                const customSound = this.soundConfig.customSounds.get(soundType);
                if (customSound) {
                    this.playCustomSound(customSound, options);
                } else {
                    this.store.env.services["mail.sound_effects"].play(soundType, options);
                }
                
                // 记录音效播放历史
                this.recordSoundPlay(soundType, options);
            },
            
            playCustomSound(soundUrl, options) {
                const audio = new Audio(soundUrl);
                audio.volume = options.volume || 0.5;
                audio.play().catch(error => {
                    console.warn('播放自定义音效失败:', error);
                });
            },
            
            recordSoundPlay(soundType, options) {
                this.soundHistory.push({
                    type: soundType,
                    options: options,
                    timestamp: Date.now()
                });
                
                // 限制历史记录
                if (this.soundHistory.length > 50) {
                    this.soundHistory.shift();
                }
            },
            
            // 设置自定义音效
            setCustomSound(soundType, soundUrl) {
                this.soundConfig.customSounds.set(soundType, soundUrl);
            },
            
            // 移除自定义音效
            removeCustomSound(soundType) {
                this.soundConfig.customSounds.delete(soundType);
            }
        };
        
        patch(Thread.prototype, SoundPatch);
    }
};

// 应用音效管理增强
ThreadSoundEnhancer.enhanceThreadSounds();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 运行时模型增强
- 保持原有功能完整性

### 2. 关联管理
- 多种RTC会话关联
- 自动化状态同步
- 事件驱动更新

### 3. 音效集成
- 智能音效播放
- 多标签页控制
- 状态变化响应

### 4. 统计功能
- 实时视频统计
- 跨线程数据聚合
- 性能友好计算

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 会话变化监听
- 自动化响应

### 3. 状态模式 (State Pattern)
- 会话状态跟踪
- 状态驱动行为

## 注意事项

1. **数据一致性**: 确保线程级别和全局级别数据的一致性
2. **性能影响**: 补丁不应影响线程模型性能
3. **音效控制**: 合理控制音效播放避免干扰
4. **内存管理**: 及时清理会话相关资源

## 扩展建议

1. **权限控制**: 添加线程级别的RTC权限控制
2. **录制功能**: 集成通话录制功能
3. **质量监控**: 添加线程级别的通话质量监控
4. **自动化规则**: 支持更多自动化业务规则
5. **数据分析**: 增强RTC使用数据分析

该补丁为视频通话系统提供了完整的线程级别RTC功能，是线程与通话功能集成的核心组成部分。

# Call Settings - 通话设置

## 概述

`call_settings.js` 实现了 Odoo 讨论应用中的通话设置组件，负责管理和配置视频通话的各种参数和选项。该组件支持音频输入设备选择、背景模糊设置、推送通话键配置、RTC日志管理、音频阈值调整等功能，集成了设备枚举、本地存储、防抖处理等技术，提供了完整的通话配置界面，是视频通话系统中的重要设置中心。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_settings.js`
- **行数**: 161
- **模块**: `@mail/discuss/call/common/call_settings`

## 依赖关系

```javascript
// UI组件依赖
'@mail/discuss/core/common/action_panel'  // 操作面板组件

// 核心依赖
'@odoo/owl'                               // OWL 框架
'@web/core/l10n/translation'              // 国际化
'@web/core/browser/browser'               // 浏览器服务
'@web/core/utils/timing'                  // 时间工具
'@web/core/browser/feature_detection'     // 浏览器特性检测
'@web/core/utils/hooks'                   // Web 核心钩子
```

## 组件定义

### CallSettings 类

```javascript
class CallSettings extends Component {
    static template = "discuss.CallSettings";
    static props = ["withActionPanel?", "*"];
    static defaultProps = {
        withActionPanel: true,
    };
    static components = { ActionPanel };
}
```

**组件特性**:
- 可选的操作面板集成
- 支持额外属性传递
- 使用自定义模板

## Props 配置

### Props 详细说明

- **`withActionPanel`** (可选):
  - 类型: 布尔值
  - 默认值: true
  - 用途: 是否显示操作面板
  - 功能: 控制操作面板的显示

- **`*`** (可选):
  - 类型: 任意
  - 用途: 额外的属性传递
  - 功能: 支持组件的灵活扩展

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.notification = useService("notification");
    this.store = useState(useService("mail.store"));
    this.rtc = useState(useService("discuss.rtc"));
    this.state = useState({
        userDevices: [],
    });
    this.pttExtService = useState(useService("discuss.ptt_extension"));

    // 防抖保存函数
    this.saveBackgroundBlurAmount = debounce(() => {
        browser.localStorage.setItem(
            "mail_user_setting_background_blur_amount",
            this.store.settings.backgroundBlurAmount.toString()
        );
    }, 2000);

    this.saveEdgeBlurAmount = debounce(() => {
        browser.localStorage.setItem(
            "mail_user_setting_edge_blur_amount",
            this.store.settings.edgeBlurAmount.toString()
        );
    }, 2000);

    // 键盘事件监听
    useExternalListener(browser, "keydown", this._onKeyDown, { capture: true });
    useExternalListener(browser, "keyup", this._onKeyUp, { capture: true });

    // 设备枚举
    onWillStart(async () => {
        if (!browser.navigator.mediaDevices) {
            this.notification.add(
                _t("Media devices unobtainable. SSL might not be set up properly."),
                { type: "warning" }
            );
            return;
        }
        this.state.userDevices = await browser.navigator.mediaDevices.enumerateDevices();
    });
}
```

**初始化内容**:
- 服务注入和状态管理
- 防抖保存函数设置
- 键盘事件监听器
- 媒体设备枚举

## 核心功能

### 1. 推送通话键管理

```javascript
get pushToTalkKeyText() {
    const { shiftKey, ctrlKey, altKey, key } = this.store.settings.pushToTalkKeyFormat();
    const f = (k, name) => (k ? name : "");
    const keys = [f(ctrlKey, "Ctrl"), f(altKey, "Alt"), f(shiftKey, "Shift"), key].filter(
        Boolean
    );
    return keys.join(" + ");
}

_onKeyDown(ev) {
    if (!this.store.settings.isRegisteringKey) {
        return;
    }
    ev.stopPropagation();
    ev.preventDefault();
    this.store.settings.setPushToTalkKey(ev);
}

_onKeyUp(ev) {
    if (!this.store.settings.isRegisteringKey) {
        return;
    }
    ev.stopPropagation();
    ev.preventDefault();
    this.store.settings.isRegisteringKey = false;
}

onClickRegisterKeyButton() {
    this.store.settings.isRegisteringKey = !this.store.settings.isRegisteringKey;
}
```

**推送通话键功能**:
- **键组合显示**: 格式化显示快捷键组合
- **键盘监听**: 捕获键盘事件进行注册
- **注册模式**: 切换键盘注册状态
- **事件阻止**: 防止默认键盘行为

### 2. 设备管理

```javascript
get isMobileOS() {
    return isMobileOS();
}

onChangeSelectAudioInput(ev) {
    this.store.settings.setAudioInputDevice(ev.target.value);
}
```

**设备管理功能**:
- **平台检测**: 检测是否为移动操作系统
- **音频输入**: 设置音频输入设备
- **设备枚举**: 获取可用媒体设备列表

### 3. 背景模糊设置

```javascript
onChangeBlur(ev) {
    this.store.settings.useBlur = ev.target.checked;
    browser.localStorage.setItem("mail_user_setting_use_blur", this.store.settings.useBlur);
}

onChangeBackgroundBlurAmount(ev) {
    this.store.settings.backgroundBlurAmount = Number(ev.target.value);
    this.saveBackgroundBlurAmount();
}

onChangeEdgeBlurAmount(ev) {
    this.store.settings.edgeBlurAmount = Number(ev.target.value);
    this.saveEdgeBlurAmount();
}
```

**背景模糊功能**:
- **模糊开关**: 启用或禁用背景模糊
- **模糊强度**: 调整背景模糊程度
- **边缘模糊**: 调整边缘模糊效果
- **防抖保存**: 延迟保存避免频繁写入

### 4. 音频设置

```javascript
onChangeDelay(ev) {
    this.store.settings.setDelayValue(ev.target.value);
}

onChangeThreshold(ev) {
    this.store.settings.setThresholdValue(parseFloat(ev.target.value));
}
```

**音频设置功能**:
- **延迟设置**: 调整音频处理延迟
- **阈值设置**: 设置音频检测阈值

### 5. 显示设置

```javascript
onChangeShowOnlyVideo(ev) {
    const showOnlyVideo = ev.target.checked;
    this.store.settings.showOnlyVideo = showOnlyVideo;
    browser.localStorage.setItem(
        "mail_user_setting_show_only_video",
        this.store.settings.showOnlyVideo
    );
    const activeRtcSessions = this.store.allActiveRtcSessions;
    if (showOnlyVideo && activeRtcSessions) {
        activeRtcSessions
            .filter((rtcSession) => !rtcSession.videoStream)
            .forEach((rtcSession) => {
                rtcSession.channel.activeRtcSession = undefined;
            });
    }
}
```

**显示设置功能**:
- **仅显示视频**: 只显示有视频的参与者
- **会话过滤**: 过滤无视频的会话
- **本地存储**: 保存显示偏好设置

### 6. 日志管理

```javascript
onChangeLogRtc(ev) {
    this.store.settings.logRtc = ev.target.checked;
}

onClickDownloadLogs() {
    const data = JSON.stringify(Object.fromEntries(this.rtc.state.logs));
    const blob = new Blob([data], { type: "application/json" });
    const downloadLink = document.createElement("a");
    const channelId = this.rtc.state.logs.get("channelId");
    const sessionId = this.rtc.state.logs.get("selfSessionId");
    const now = luxon.DateTime.now().toFormat("yyyy-ll-dd_HH-mm");
    downloadLink.download = `RtcLogs_Channel_${channelId}_Session_${sessionId}_${now}.json`;
    const url = URL.createObjectURL(blob);
    downloadLink.href = url;
    downloadLink.click();
    URL.revokeObjectURL(url);
}
```

**日志管理功能**:
- **日志开关**: 启用或禁用RTC日志记录
- **日志下载**: 导出RTC日志为JSON文件
- **文件命名**: 包含频道和会话信息的文件名
- **资源清理**: 及时清理URL对象
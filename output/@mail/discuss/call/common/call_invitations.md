# Call Invitations - 通话邀请列表

## 概述

`call_invitations.js` 实现了 Odoo 讨论应用中的通话邀请列表组件，负责管理和显示所有活跃的通话邀请。该组件作为主要组件注册到系统中，集成了CallInvitation子组件，支持多个邀请的统一管理和显示，提供了完整的邀请列表界面，是视频通话系统中邀请管理的核心容器组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_invitations.js`
- **行数**: 30
- **模块**: `@mail/discuss/call/common/call_invitations`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/call/common/call_invitation'  // 单个邀请组件

// 核心依赖
'@odoo/owl'                                  // OWL 框架
'@web/core/registry'                         // 注册表系统
'@web/core/utils/hooks'                      // Web 核心钩子
```

## 组件定义

### CallInvitations 类

```javascript
class CallInvitations extends Component {
    static props = [];
    static components = { CallInvitation };
    static template = "discuss.CallInvitations";
}
```

**组件特性**:
- 无需外部属性
- 集成CallInvitation子组件
- 使用自定义模板

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.rtc = useState(useService("discuss.rtc"));
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- RTC服务状态管理
- 邮件存储服务
- 响应式状态绑定

## 主要组件注册

### 主要组件注册

```javascript
registry.category("main_components").add("discuss.CallInvitations", { Component: CallInvitations });
```

**注册特性**:
- 注册为主要组件
- 全局可访问
- 系统级组件

## 使用场景

### 1. 邀请列表管理

```javascript
// 邀请列表管理增强
const InvitationListManager = {
    enhanceInvitationsList: (invitationsComponent) => {
        // 添加邀请过滤
        InvitationListManager.addInvitationFiltering(invitationsComponent);
        
        // 添加批量操作
        InvitationListManager.addBatchOperations(invitationsComponent);
        
        // 添加邀请统计
        InvitationListManager.addInvitationStatistics(invitationsComponent);
        
        // 添加自动管理
        InvitationListManager.addAutoManagement(invitationsComponent);
        
        // 添加通知管理
        InvitationListManager.addNotificationManagement(invitationsComponent);
    },
    
    addInvitationFiltering: (component) => {
        const filterContainer = document.createElement('div');
        filterContainer.className = 'invitation-filters';
        filterContainer.innerHTML = `
            <div class="filter-section">
                <h4>邀请筛选</h4>
                <div class="filter-options">
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" data-filter="all" checked>
                        <span>全部邀请</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" data-filter="video">
                        <span>视频通话</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" data-filter="audio">
                        <span>语音通话</span>
                    </label>
                    <label class="filter-option">
                        <input type="checkbox" class="filter-checkbox" data-filter="group">
                        <span>群组通话</span>
                    </label>
                </div>
                
                <div class="sort-options">
                    <label>排序方式:</label>
                    <select class="sort-select">
                        <option value="time">按时间</option>
                        <option value="priority">按优先级</option>
                        <option value="caller">按呼叫者</option>
                    </select>
                </div>
            </div>
        `;
        
        component.el.insertBefore(filterContainer, component.el.firstChild);
        
        // 绑定过滤事件
        InvitationListManager.bindFilterEvents(filterContainer, component);
    },
    
    bindFilterEvents: (filterContainer, component) => {
        const checkboxes = filterContainer.querySelectorAll('.filter-checkbox');
        const sortSelect = filterContainer.querySelector('.sort-select');
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                InvitationListManager.applyFilters(component);
            });
        });
        
        sortSelect.addEventListener('change', () => {
            InvitationListManager.applySorting(component, sortSelect.value);
        });
    },
    
    applyFilters: (component) => {
        const activeFilters = Array.from(
            component.el.querySelectorAll('.filter-checkbox:checked')
        ).map(cb => cb.dataset.filter);
        
        const invitations = component.el.querySelectorAll('.call-invitation');
        
        invitations.forEach(invitation => {
            const shouldShow = InvitationListManager.shouldShowInvitation(invitation, activeFilters);
            invitation.style.display = shouldShow ? 'block' : 'none';
        });
        
        // 更新计数
        InvitationListManager.updateInvitationCount(component);
    },
    
    shouldShowInvitation: (invitation, filters) => {
        if (filters.includes('all')) return true;
        
        const invitationType = invitation.dataset.type || 'video';
        const isGroup = invitation.dataset.isGroup === 'true';
        
        if (filters.includes('video') && invitationType === 'video') return true;
        if (filters.includes('audio') && invitationType === 'audio') return true;
        if (filters.includes('group') && isGroup) return true;
        
        return false;
    },
    
    applySorting: (component, sortBy) => {
        const invitationsContainer = component.el.querySelector('.invitations-container');
        const invitations = Array.from(invitationsContainer.querySelectorAll('.call-invitation'));
        
        invitations.sort((a, b) => {
            switch (sortBy) {
                case 'time':
                    return new Date(b.dataset.timestamp) - new Date(a.dataset.timestamp);
                case 'priority':
                    return (b.dataset.priority || 0) - (a.dataset.priority || 0);
                case 'caller':
                    return (a.dataset.callerName || '').localeCompare(b.dataset.callerName || '');
                default:
                    return 0;
            }
        });
        
        // 重新排列DOM元素
        invitations.forEach(invitation => {
            invitationsContainer.appendChild(invitation);
        });
    },
    
    addBatchOperations: (component) => {
        const batchContainer = document.createElement('div');
        batchContainer.className = 'batch-operations';
        batchContainer.innerHTML = `
            <div class="batch-section">
                <h4>批量操作</h4>
                <div class="batch-controls">
                    <label class="select-all-option">
                        <input type="checkbox" class="select-all-checkbox">
                        <span>全选</span>
                    </label>
                    
                    <div class="batch-buttons">
                        <button class="batch-btn accept-all-btn" disabled>
                            <i class="fa fa-check"></i>
                            <span>全部接受</span>
                        </button>
                        <button class="batch-btn reject-all-btn" disabled>
                            <i class="fa fa-times"></i>
                            <span>全部拒绝</span>
                        </button>
                        <button class="batch-btn snooze-all-btn" disabled>
                            <i class="fa fa-clock-o"></i>
                            <span>全部延迟</span>
                        </button>
                    </div>
                </div>
                
                <div class="selection-info">
                    <span class="selected-count">已选择: 0</span>
                </div>
            </div>
        `;
        
        component.el.appendChild(batchContainer);
        
        // 绑定批量操作事件
        InvitationListManager.bindBatchEvents(batchContainer, component);
    },
    
    bindBatchEvents: (batchContainer, component) => {
        const selectAllCheckbox = batchContainer.querySelector('.select-all-checkbox');
        const acceptAllBtn = batchContainer.querySelector('.accept-all-btn');
        const rejectAllBtn = batchContainer.querySelector('.reject-all-btn');
        const snoozeAllBtn = batchContainer.querySelector('.snooze-all-btn');
        const selectedCount = batchContainer.querySelector('.selected-count');
        
        // 全选/取消全选
        selectAllCheckbox.addEventListener('change', () => {
            const invitationCheckboxes = component.el.querySelectorAll('.invitation-checkbox');
            invitationCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            InvitationListManager.updateBatchButtons(batchContainer, component);
        });
        
        // 批量接受
        acceptAllBtn.addEventListener('click', () => {
            InvitationListManager.batchAcceptInvitations(component);
        });
        
        // 批量拒绝
        rejectAllBtn.addEventListener('click', () => {
            InvitationListManager.batchRejectInvitations(component);
        });
        
        // 批量延迟
        snoozeAllBtn.addEventListener('click', () => {
            InvitationListManager.batchSnoozeInvitations(component);
        });
        
        // 监听单个邀请选择变化
        component.el.addEventListener('change', (event) => {
            if (event.target.classList.contains('invitation-checkbox')) {
                InvitationListManager.updateBatchButtons(batchContainer, component);
            }
        });
    },
    
    updateBatchButtons: (batchContainer, component) => {
        const selectedCheckboxes = component.el.querySelectorAll('.invitation-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        const buttons = batchContainer.querySelectorAll('.batch-btn');
        const selectedCount = batchContainer.querySelector('.selected-count');
        
        buttons.forEach(btn => {
            btn.disabled = count === 0;
        });
        
        selectedCount.textContent = `已选择: ${count}`;
    },
    
    batchAcceptInvitations: async (component) => {
        const selectedInvitations = InvitationListManager.getSelectedInvitations(component);
        
        for (const invitation of selectedInvitations) {
            try {
                await invitation.onClickAccept();
            } catch (error) {
                console.error('批量接受邀请失败:', error);
            }
        }
        
        showNotification(`已接受 ${selectedInvitations.length} 个邀请`, 'success');
    },
    
    batchRejectInvitations: async (component) => {
        const selectedInvitations = InvitationListManager.getSelectedInvitations(component);
        
        for (const invitation of selectedInvitations) {
            try {
                await invitation.onClickRefuse();
            } catch (error) {
                console.error('批量拒绝邀请失败:', error);
            }
        }
        
        showNotification(`已拒绝 ${selectedInvitations.length} 个邀请`, 'info');
    },
    
    batchSnoozeInvitations: (component) => {
        const selectedInvitations = InvitationListManager.getSelectedInvitations(component);
        
        // 显示延迟选项对话框
        InvitationListManager.showSnoozeDialog(selectedInvitations);
    },
    
    getSelectedInvitations: (component) => {
        const selectedCheckboxes = component.el.querySelectorAll('.invitation-checkbox:checked');
        return Array.from(selectedCheckboxes).map(checkbox => {
            return checkbox.closest('.call-invitation').invitationComponent;
        });
    },
    
    showSnoozeDialog: (invitations) => {
        const dialog = document.createElement('div');
        dialog.className = 'snooze-dialog-overlay';
        dialog.innerHTML = `
            <div class="snooze-dialog">
                <div class="dialog-header">
                    <h3>延迟邀请</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <p>选择延迟时间:</p>
                    <div class="snooze-options">
                        <button class="snooze-option-btn" data-minutes="5">5分钟</button>
                        <button class="snooze-option-btn" data-minutes="15">15分钟</button>
                        <button class="snooze-option-btn" data-minutes="30">30分钟</button>
                        <button class="snooze-option-btn" data-minutes="60">1小时</button>
                    </div>
                    
                    <div class="custom-snooze">
                        <label>自定义时间(分钟):</label>
                        <input type="number" class="custom-minutes" min="1" max="1440" value="10">
                        <button class="custom-snooze-btn">确定</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // 绑定延迟对话框事件
        InvitationListManager.bindSnoozeDialogEvents(dialog, invitations);
    },
    
    bindSnoozeDialogEvents: (dialog, invitations) => {
        const closeBtn = dialog.querySelector('.close-btn');
        const snoozeOptionBtns = dialog.querySelectorAll('.snooze-option-btn');
        const customSnoozeBtn = dialog.querySelector('.custom-snooze-btn');
        const customMinutes = dialog.querySelector('.custom-minutes');
        
        closeBtn.addEventListener('click', () => {
            dialog.remove();
        });
        
        snoozeOptionBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const minutes = parseInt(btn.dataset.minutes);
                InvitationListManager.snoozeInvitations(invitations, minutes);
                dialog.remove();
            });
        });
        
        customSnoozeBtn.addEventListener('click', () => {
            const minutes = parseInt(customMinutes.value);
            if (minutes > 0) {
                InvitationListManager.snoozeInvitations(invitations, minutes);
                dialog.remove();
            }
        });
    },
    
    snoozeInvitations: (invitations, minutes) => {
        invitations.forEach(invitation => {
            // 隐藏邀请
            invitation.el.style.display = 'none';
            
            // 设置定时器重新显示
            setTimeout(() => {
                invitation.el.style.display = 'block';
                showNotification('延迟的邀请已重新显示', 'info');
            }, minutes * 60 * 1000);
        });
        
        showNotification(`已延迟 ${invitations.length} 个邀请 ${minutes} 分钟`, 'success');
    },
    
    addInvitationStatistics: (component) => {
        const statsContainer = document.createElement('div');
        statsContainer.className = 'invitation-statistics';
        statsContainer.innerHTML = `
            <div class="stats-section">
                <h4>邀请统计</h4>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-label">总邀请数:</span>
                        <span class="stat-value total-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">视频邀请:</span>
                        <span class="stat-value video-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">语音邀请:</span>
                        <span class="stat-value audio-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">群组邀请:</span>
                        <span class="stat-value group-count">0</span>
                    </div>
                </div>
            </div>
        `;
        
        component.el.appendChild(statsContainer);
        
        // 定期更新统计
        setInterval(() => {
            InvitationListManager.updateStatistics(component, statsContainer);
        }, 1000);
    },
    
    updateStatistics: (component, statsContainer) => {
        const invitations = component.el.querySelectorAll('.call-invitation:not([style*="display: none"])');
        
        const stats = {
            total: invitations.length,
            video: 0,
            audio: 0,
            group: 0
        };
        
        invitations.forEach(invitation => {
            const type = invitation.dataset.type || 'video';
            const isGroup = invitation.dataset.isGroup === 'true';
            
            if (type === 'video') stats.video++;
            if (type === 'audio') stats.audio++;
            if (isGroup) stats.group++;
        });
        
        statsContainer.querySelector('.total-count').textContent = stats.total;
        statsContainer.querySelector('.video-count').textContent = stats.video;
        statsContainer.querySelector('.audio-count').textContent = stats.audio;
        statsContainer.querySelector('.group-count').textContent = stats.group;
    },
    
    updateInvitationCount: (component) => {
        const visibleInvitations = component.el.querySelectorAll('.call-invitation:not([style*="display: none"])');
        const countElement = component.el.querySelector('.invitation-count');
        
        if (countElement) {
            countElement.textContent = `(${visibleInvitations.length})`;
        }
    },
    
    addAutoManagement: (component) => {
        // 自动拒绝超时邀请
        setInterval(() => {
            InvitationListManager.autoRejectExpiredInvitations(component);
        }, 30000); // 每30秒检查一次
        
        // 自动接受高优先级邀请
        InvitationListManager.setupAutoAcceptHighPriority(component);
    },
    
    autoRejectExpiredInvitations: (component) => {
        const now = Date.now();
        const timeout = 5 * 60 * 1000; // 5分钟超时
        
        const invitations = component.el.querySelectorAll('.call-invitation');
        
        invitations.forEach(invitation => {
            const timestamp = new Date(invitation.dataset.timestamp).getTime();
            if (now - timestamp > timeout) {
                const invitationComponent = invitation.invitationComponent;
                if (invitationComponent) {
                    invitationComponent.onClickRefuse();
                    showNotification('已自动拒绝超时邀请', 'info');
                }
            }
        });
    },
    
    setupAutoAcceptHighPriority: (component) => {
        // 监听新邀请
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.classList && node.classList.contains('call-invitation')) {
                        const priority = parseInt(node.dataset.priority || '0');
                        if (priority >= 9) { // 高优先级
                            setTimeout(() => {
                                const invitationComponent = node.invitationComponent;
                                if (invitationComponent) {
                                    invitationComponent.onClickAccept();
                                    showNotification('已自动接受高优先级邀请', 'success');
                                }
                            }, 1000); // 延迟1秒自动接受
                        }
                    }
                });
            });
        });
        
        observer.observe(component.el, { childList: true, subtree: true });
    },
    
    addNotificationManagement: (component) => {
        // 桌面通知管理
        if ('Notification' in window && Notification.permission === 'granted') {
            InvitationListManager.setupDesktopNotifications(component);
        }
        
        // 声音通知管理
        InvitationListManager.setupSoundNotifications(component);
    },
    
    setupDesktopNotifications: (component) => {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.classList && node.classList.contains('call-invitation')) {
                        const callerName = node.dataset.callerName || '未知用户';
                        const notification = new Notification('新的通话邀请', {
                            body: `${callerName} 邀请您加入通话`,
                            icon: '/mail/static/src/img/call_icon.png',
                            tag: 'call-invitation'
                        });
                        
                        setTimeout(() => {
                            notification.close();
                        }, 10000);
                    }
                });
            });
        });
        
        observer.observe(component.el, { childList: true, subtree: true });
    },
    
    setupSoundNotifications: (component) => {
        const audio = new Audio('/mail/static/src/sounds/notification.mp3');
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.classList && node.classList.contains('call-invitation')) {
                        audio.play().catch(error => {
                            console.warn('无法播放通知声音:', error);
                        });
                    }
                });
            });
        });
        
        observer.observe(component.el, { childList: true, subtree: true });
    }
};
```

## 技术特点

### 1. 容器组件
- 管理多个邀请组件
- 统一的邀请显示
- 集中的状态管理

### 2. 主要组件
- 注册为系统主要组件
- 全局可访问性
- 系统级集成

### 3. 服务集成
- RTC服务状态管理
- 邮件存储集成
- 响应式状态更新

### 4. 模板驱动
- 声明式UI渲染
- 组件组合模式
- 数据绑定支持

## 设计模式

### 1. 容器模式 (Container Pattern)
- 管理子组件的容器
- 统一的数据和状态管理

### 2. 组合模式 (Composite Pattern)
- 组合多个邀请组件
- 统一的操作接口

### 3. 观察者模式 (Observer Pattern)
- 响应式状态管理
- 服务状态监听

## 注意事项

1. **性能优化**: 大量邀请时的渲染性能
2. **内存管理**: 及时清理不需要的邀请组件
3. **用户体验**: 提供清晰的邀请管理界面
4. **状态同步**: 保持邀请状态与服务状态一致

## 扩展建议

1. **邀请分组**: 按类型或来源分组显示邀请
2. **优先级管理**: 支持邀请优先级排序
3. **历史记录**: 保存邀请历史和统计
4. **自定义规则**: 支持自定义邀请处理规则
5. **批量操作**: 提供批量接受/拒绝功能

该组件是视频通话系统中邀请管理的核心容器，提供了完整的邀请列表管理功能。

# Settings Model Patch - 设置模型补丁

## 概述

`settings_model_patch.js` 实现了对 Odoo 讨论应用中设置模型的补丁扩展，为设置模型添加了RTC会话音量管理功能。该补丁通过Odoo的补丁机制扩展了Settings模型，添加了getVolume方法，支持基于RTC会话的个性化音量设置，建立了设置与RTC会话之间的关联，是视频通话系统中音量管理的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/settings_model_patch.js`
- **行数**: 32
- **模块**: `@mail/discuss/call/common/settings_model_patch`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/settings_model'  // 设置模型
'@web/core/utils/patch'             // 补丁工具
```

## 补丁定义

### SettingsPatch 对象

```javascript
const SettingsPatch = {
    setup() {
        super.setup(...arguments);
    },
    getVolume(rtcSession) {
        return (
            rtcSession.volume ||
            this.volumes.find(
                (volume) =>
                    (volume.type === "partner" && volume.persona.id === rtcSession.partnerId) ||
                    (volume.type === "guest" && volume.persona.id === rtcSession.guestId)
            )?.volume ||
            0.5
        );
    },
};
```

**补丁特性**:
- 扩展setup方法
- 添加音量获取方法
- 支持多种用户类型

### 补丁应用

```javascript
patch(Settings.prototype, SettingsPatch);
```

**应用特性**:
- 原型补丁模式
- 非侵入式扩展
- 运行时增强

## 核心功能

### 1. 音量获取方法

```javascript
getVolume(rtcSession) {
    return (
        rtcSession.volume ||
        this.volumes.find(
            (volume) =>
                (volume.type === "partner" && volume.persona.id === rtcSession.partnerId) ||
                (volume.type === "guest" && volume.persona.id === rtcSession.guestId)
        )?.volume ||
        0.5
    );
}
```

**获取逻辑**:
- **优先级1**: RTC会话直接音量设置
- **优先级2**: 基于用户类型的音量设置
- **优先级3**: 默认音量值(0.5)

### 2. 用户类型支持

```javascript
(volume.type === "partner" && volume.persona.id === rtcSession.partnerId) ||
(volume.type === "guest" && volume.persona.id === rtcSession.guestId)
```

**用户类型**:
- **partner**: 注册用户/合作伙伴
- **guest**: 访客用户
- **ID匹配**: 基于用户ID进行音量匹配

## 使用场景

### 1. 音量管理增强

```javascript
// 音量管理增强功能
const VolumeManagementEnhancer = {
    enhanceVolumeManagement: () => {
        const EnhancedSettingsPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加音量历史记录
                this.volumeHistory = new Map();
                
                // 添加音量预设
                this.volumePresets = {
                    silent: 0,
                    quiet: 0.25,
                    normal: 0.5,
                    loud: 0.75,
                    maximum: 1.0
                };
                
                // 添加自动音量调节
                this.autoVolumeEnabled = false;
                this.volumeAnalyzer = VolumeManagementEnhancer.createVolumeAnalyzer();
            },
            
            // 增强的音量获取方法
            getVolume(rtcSession) {
                // 获取基础音量
                let volume = this.getBaseVolume(rtcSession);
                
                // 应用音量修正
                volume = this.applyVolumeCorrection(rtcSession, volume);
                
                // 应用自动音量调节
                if (this.autoVolumeEnabled) {
                    volume = this.applyAutoVolumeAdjustment(rtcSession, volume);
                }
                
                // 记录音量历史
                this.recordVolumeHistory(rtcSession, volume);
                
                return Math.max(0, Math.min(1, volume));
            },
            
            // 获取基础音量
            getBaseVolume(rtcSession) {
                return (
                    rtcSession.volume ||
                    this.volumes.find(
                        (volume) =>
                            (volume.type === "partner" && volume.persona.id === rtcSession.partnerId) ||
                            (volume.type === "guest" && volume.persona.id === rtcSession.guestId)
                    )?.volume ||
                    this.volumePresets.normal
                );
            },
            
            // 应用音量修正
            applyVolumeCorrection(rtcSession, baseVolume) {
                // 基于连接质量的音量修正
                const connectionQuality = rtcSession.connectionQuality || 'good';
                const qualityMultipliers = {
                    excellent: 1.0,
                    good: 1.0,
                    fair: 1.1,  // 稍微提高音量补偿质量损失
                    poor: 1.2   // 更多提高音量
                };
                
                let correctedVolume = baseVolume * (qualityMultipliers[connectionQuality] || 1.0);
                
                // 基于音频质量的修正
                if (rtcSession.audioQuality) {
                    const audioQuality = rtcSession.audioQuality;
                    if (audioQuality < 0.5) {
                        correctedVolume *= 1.15; // 音质差时提高音量
                    }
                }
                
                return correctedVolume;
            },
            
            // 应用自动音量调节
            applyAutoVolumeAdjustment(rtcSession, volume) {
                const analysis = this.volumeAnalyzer.analyze(rtcSession);
                
                if (analysis.isTooQuiet) {
                    return Math.min(volume * 1.1, 1.0);
                } else if (analysis.isTooLoud) {
                    return Math.max(volume * 0.9, 0.1);
                }
                
                return volume;
            },
            
            // 记录音量历史
            recordVolumeHistory(rtcSession, volume) {
                const sessionId = rtcSession.id;
                
                if (!this.volumeHistory.has(sessionId)) {
                    this.volumeHistory.set(sessionId, []);
                }
                
                const history = this.volumeHistory.get(sessionId);
                history.push({
                    timestamp: Date.now(),
                    volume: volume,
                    connectionQuality: rtcSession.connectionQuality
                });
                
                // 限制历史记录数量
                if (history.length > 100) {
                    history.shift();
                }
            },
            
            // 设置音量
            setVolume(rtcSession, volume) {
                const clampedVolume = Math.max(0, Math.min(1, volume));
                
                // 直接设置到RTC会话
                rtcSession.volume = clampedVolume;
                
                // 同时更新到持久化设置
                this.saveVolumeSetting({
                    partnerId: rtcSession.partnerId,
                    guestId: rtcSession.guestId,
                    volume: clampedVolume
                });
                
                // 触发音量变化事件
                this.trigger('volume_changed', {
                    rtcSession,
                    volume: clampedVolume
                });
            },
            
            // 应用音量预设
            applyVolumePreset(rtcSession, presetName) {
                if (presetName in this.volumePresets) {
                    this.setVolume(rtcSession, this.volumePresets[presetName]);
                }
            },
            
            // 获取音量统计
            getVolumeStatistics(rtcSession) {
                const history = this.volumeHistory.get(rtcSession.id) || [];
                
                if (history.length === 0) {
                    return null;
                }
                
                const volumes = history.map(h => h.volume);
                const average = volumes.reduce((sum, v) => sum + v, 0) / volumes.length;
                const min = Math.min(...volumes);
                const max = Math.max(...volumes);
                
                return {
                    average,
                    min,
                    max,
                    samples: history.length,
                    trend: VolumeManagementEnhancer.calculateVolumeTrend(history)
                };
            },
            
            // 启用/禁用自动音量调节
            setAutoVolumeEnabled(enabled) {
                this.autoVolumeEnabled = enabled;
                
                if (enabled) {
                    this.volumeAnalyzer.start();
                } else {
                    this.volumeAnalyzer.stop();
                }
            }
        };
        
        patch(Settings.prototype, EnhancedSettingsPatch);
    },
    
    createVolumeAnalyzer: () => {
        return {
            isRunning: false,
            analysisInterval: null,
            
            start() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.analysisInterval = setInterval(() => {
                    this.performAnalysis();
                }, 5000); // 每5秒分析一次
            },
            
            stop() {
                if (!this.isRunning) return;
                
                this.isRunning = false;
                if (this.analysisInterval) {
                    clearInterval(this.analysisInterval);
                    this.analysisInterval = null;
                }
            },
            
            analyze(rtcSession) {
                const audioLevel = rtcSession.audioLevel || 0;
                const currentVolume = rtcSession.volume || 0.5;
                
                // 分析音频级别和当前音量的关系
                const effectiveLevel = audioLevel * currentVolume;
                
                return {
                    isTooQuiet: effectiveLevel < 0.1 && audioLevel > 0.2,
                    isTooLoud: effectiveLevel > 0.9,
                    isOptimal: effectiveLevel >= 0.3 && effectiveLevel <= 0.7,
                    recommendation: this.getVolumeRecommendation(audioLevel, currentVolume)
                };
            },
            
            getVolumeRecommendation(audioLevel, currentVolume) {
                if (audioLevel === 0) return 'no_audio';
                
                const effectiveLevel = audioLevel * currentVolume;
                
                if (effectiveLevel < 0.2) return 'increase';
                if (effectiveLevel > 0.8) return 'decrease';
                return 'optimal';
            },
            
            performAnalysis() {
                // 全局音量分析逻辑
                console.log('执行音量分析...');
            }
        };
    },
    
    calculateVolumeTrend: (history) => {
        if (history.length < 2) return 'stable';
        
        const recent = history.slice(-10); // 最近10个记录
        const first = recent[0].volume;
        const last = recent[recent.length - 1].volume;
        
        const change = last - first;
        
        if (Math.abs(change) < 0.05) return 'stable';
        return change > 0 ? 'increasing' : 'decreasing';
    }
};

// 应用音量管理增强
VolumeManagementEnhancer.enhanceVolumeManagement();
```

### 2. 音量配置管理

```javascript
// 音量配置管理器
const VolumeConfigManager = {
    setupVolumeConfiguration: () => {
        const ConfigPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加音量配置
                this.volumeConfig = {
                    globalMasterVolume: 1.0,
                    defaultVolume: 0.5,
                    volumeStep: 0.1,
                    muteThreshold: 0.05,
                    maxVolume: 1.0,
                    minVolume: 0.0
                };
                
                // 添加音量分组
                this.volumeGroups = new Map();
                
                // 添加音量规则
                this.volumeRules = [];
            },
            
            // 获取配置化的音量
            getVolume(rtcSession) {
                let volume = this.getBaseVolume(rtcSession);
                
                // 应用全局主音量
                volume *= this.volumeConfig.globalMasterVolume;
                
                // 应用音量分组
                volume = this.applyVolumeGroup(rtcSession, volume);
                
                // 应用音量规则
                volume = this.applyVolumeRules(rtcSession, volume);
                
                // 应用音量限制
                volume = this.applyVolumeLimits(volume);
                
                return volume;
            },
            
            // 应用音量分组
            applyVolumeGroup(rtcSession, volume) {
                const groupId = this.getVolumeGroupId(rtcSession);
                if (!groupId) return volume;
                
                const group = this.volumeGroups.get(groupId);
                if (!group) return volume;
                
                return volume * group.multiplier;
            },
            
            // 获取音量分组ID
            getVolumeGroupId(rtcSession) {
                // 基于用户角色分组
                if (rtcSession.channelMember?.persona?.isAdmin) {
                    return 'admin';
                }
                
                // 基于频道类型分组
                if (rtcSession.channel?.channel_type === 'channel') {
                    return 'public_channel';
                } else if (rtcSession.channel?.channel_type === 'chat') {
                    return 'private_chat';
                }
                
                return 'default';
            },
            
            // 应用音量规则
            applyVolumeRules(rtcSession, volume) {
                for (const rule of this.volumeRules) {
                    if (rule.condition(rtcSession)) {
                        volume = rule.apply(volume, rtcSession);
                    }
                }
                return volume;
            },
            
            // 应用音量限制
            applyVolumeLimits(volume) {
                const { minVolume, maxVolume } = this.volumeConfig;
                return Math.max(minVolume, Math.min(maxVolume, volume));
            },
            
            // 设置音量分组
            setVolumeGroup(groupId, config) {
                this.volumeGroups.set(groupId, {
                    multiplier: config.multiplier || 1.0,
                    name: config.name || groupId,
                    description: config.description || ''
                });
            },
            
            // 添加音量规则
            addVolumeRule(rule) {
                this.volumeRules.push({
                    id: rule.id || Date.now().toString(),
                    name: rule.name || 'Unnamed Rule',
                    condition: rule.condition,
                    apply: rule.apply,
                    priority: rule.priority || 0
                });
                
                // 按优先级排序
                this.volumeRules.sort((a, b) => b.priority - a.priority);
            },
            
            // 移除音量规则
            removeVolumeRule(ruleId) {
                this.volumeRules = this.volumeRules.filter(rule => rule.id !== ruleId);
            },
            
            // 更新音量配置
            updateVolumeConfig(newConfig) {
                Object.assign(this.volumeConfig, newConfig);
                
                // 触发配置更新事件
                this.trigger('volume_config_updated', this.volumeConfig);
            }
        };
        
        patch(Settings.prototype, ConfigPatch);
    },
    
    // 预定义音量分组
    setupDefaultVolumeGroups: (settings) => {
        settings.setVolumeGroup('admin', {
            multiplier: 1.0,
            name: '管理员',
            description: '管理员用户的音量设置'
        });
        
        settings.setVolumeGroup('public_channel', {
            multiplier: 0.8,
            name: '公共频道',
            description: '公共频道中的音量设置'
        });
        
        settings.setVolumeGroup('private_chat', {
            multiplier: 1.0,
            name: '私人聊天',
            description: '私人聊天中的音量设置'
        });
        
        settings.setVolumeGroup('default', {
            multiplier: 1.0,
            name: '默认',
            description: '默认音量设置'
        });
    },
    
    // 预定义音量规则
    setupDefaultVolumeRules: (settings) => {
        // 夜间模式音量规则
        settings.addVolumeRule({
            id: 'night_mode',
            name: '夜间模式',
            priority: 10,
            condition: (rtcSession) => {
                const hour = new Date().getHours();
                return hour >= 22 || hour <= 6;
            },
            apply: (volume) => volume * 0.7
        });
        
        // 低质量连接音量补偿规则
        settings.addVolumeRule({
            id: 'poor_connection_boost',
            name: '低质量连接补偿',
            priority: 5,
            condition: (rtcSession) => {
                return rtcSession.connectionQuality === 'poor';
            },
            apply: (volume) => Math.min(volume * 1.2, 1.0)
        });
        
        // 背景噪音抑制规则
        settings.addVolumeRule({
            id: 'noise_suppression',
            name: '背景噪音抑制',
            priority: 3,
            condition: (rtcSession) => {
                return rtcSession.backgroundNoiseLevel > 0.3;
            },
            apply: (volume) => volume * 0.9
        });
    }
};

// 应用音量配置管理
VolumeConfigManager.setupVolumeConfiguration();
```

### 3. 音量同步和持久化

```javascript
// 音量同步和持久化管理器
const VolumeSyncManager = {
    setupVolumeSync: () => {
        const SyncPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加同步状态
                this.volumeSyncState = {
                    lastSyncTime: 0,
                    pendingChanges: new Map(),
                    syncInProgress: false
                };
                
                // 定期同步
                this.volumeSyncInterval = setInterval(() => {
                    this.syncVolumeChanges();
                }, 30000); // 每30秒同步一次
            },
            
            // 同步音量变化
            async syncVolumeChanges() {
                if (this.volumeSyncState.syncInProgress) return;
                if (this.volumeSyncState.pendingChanges.size === 0) return;
                
                this.volumeSyncState.syncInProgress = true;
                
                try {
                    const changes = Array.from(this.volumeSyncState.pendingChanges.entries());
                    
                    for (const [sessionId, volumeData] of changes) {
                        await this.syncSingleVolumeChange(sessionId, volumeData);
                    }
                    
                    this.volumeSyncState.pendingChanges.clear();
                    this.volumeSyncState.lastSyncTime = Date.now();
                } catch (error) {
                    console.error('音量同步失败:', error);
                } finally {
                    this.volumeSyncState.syncInProgress = false;
                }
            },
            
            // 同步单个音量变化
            async syncSingleVolumeChange(sessionId, volumeData) {
                try {
                    await rpc('/mail/rtc/sync_volume', {
                        session_id: sessionId,
                        volume: volumeData.volume,
                        user_type: volumeData.userType,
                        user_id: volumeData.userId
                    });
                } catch (error) {
                    console.error(`同步会话 ${sessionId} 音量失败:`, error);
                    throw error;
                }
            },
            
            // 标记音量变化待同步
            markVolumeChangeForSync(rtcSession, volume) {
                this.volumeSyncState.pendingChanges.set(rtcSession.id, {
                    volume: volume,
                    userType: rtcSession.partnerId ? 'partner' : 'guest',
                    userId: rtcSession.partnerId || rtcSession.guestId,
                    timestamp: Date.now()
                });
            },
            
            // 立即同步音量
            async syncVolumeImmediately(rtcSession, volume) {
                this.markVolumeChangeForSync(rtcSession, volume);
                await this.syncVolumeChanges();
            },
            
            // 从服务器加载音量设置
            async loadVolumeSettings() {
                try {
                    const response = await rpc('/mail/rtc/load_volume_settings');
                    
                    // 更新本地音量设置
                    if (response.volumes) {
                        this.volumes = response.volumes;
                    }
                    
                    return response;
                } catch (error) {
                    console.error('加载音量设置失败:', error);
                    throw error;
                }
            },
            
            // 清理同步资源
            destroy() {
                if (this.volumeSyncInterval) {
                    clearInterval(this.volumeSyncInterval);
                    this.volumeSyncInterval = null;
                }
                super.destroy?.();
            }
        };
        
        patch(Settings.prototype, SyncPatch);
    }
};

// 应用音量同步管理
VolumeSyncManager.setupVolumeSync();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 运行时模型增强
- 保持原有功能完整性

### 2. 音量管理
- 多层级音量获取逻辑
- 用户类型区分支持
- 默认值回退机制

### 3. 灵活设计
- 支持多种用户类型
- 优先级明确的获取逻辑
- 简洁的API设计

### 4. 数据关联
- RTC会话与设置关联
- 用户身份识别
- 个性化音量管理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同用户类型的音量策略
- 优先级策略

### 3. 回退模式 (Fallback Pattern)
- 多层级的音量获取
- 默认值保证

## 注意事项

1. **数据一致性**: 确保音量设置的数据一致性
2. **性能影响**: 补丁不应影响原有性能
3. **用户体验**: 提供合理的默认音量值
4. **类型安全**: 正确处理不同用户类型

## 扩展建议

1. **音量预设**: 添加音量预设功能
2. **自动调节**: 实现智能音量自动调节
3. **音量历史**: 记录和分析音量使用历史
4. **批量设置**: 支持批量音量设置
5. **音量同步**: 跨设备音量设置同步

该补丁为视频通话系统提供了完整的音量管理功能，是用户个性化体验的重要组成部分。

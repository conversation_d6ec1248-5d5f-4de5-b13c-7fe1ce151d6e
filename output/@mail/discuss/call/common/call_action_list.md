# Call Action List - 通话操作列表

## 概述

`call_action_list.js` 实现了 Odoo 讨论应用中通话操作列表组件，用于显示和管理通话过程中的各种操作按钮。该组件支持下拉菜单形式的操作列表、移动端适配、紧凑模式显示、全屏状态检测等功能，集成了通话操作钩子和下拉菜单组件，提供了统一的通话控制界面，是视频通话系统中的核心操作面板。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_action_list.js`
- **行数**: 64
- **模块**: `@mail/discuss/call/common/call_action_list`

## 依赖关系

```javascript
// UI组件依赖
'@web/core/dropdown/dropdown'           // 下拉菜单组件
'@web/core/dropdown/dropdown_item'      // 下拉菜单项组件

// 核心依赖
'@odoo/owl'                            // OWL 框架
'@web/core/browser/feature_detection'  // 浏览器特性检测
'@web/core/l10n/translation'           // 国际化
'@web/core/utils/hooks'                // Web 核心钩子
'@mail/discuss/call/common/call_actions' // 通话操作钩子
```

## 组件定义

### CallActionList 类

```javascript
class CallActionList extends Component {
    static components = { Dropdown, DropdownItem };
    static props = ["thread", "fullscreen", "compact?"];
    static template = "discuss.CallActionList";
}
```

**组件特性**:
- 集成下拉菜单组件
- 支持全屏和紧凑模式
- 使用自定义模板

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 通话线程对象
  - 功能: 提供通话上下文信息

- **`fullscreen`** (必需):
  - 类型: 对象
  - 用途: 全屏状态对象
  - 功能: 检测和控制全屏模式

- **`compact`** (可选):
  - 类型: 布尔值
  - 用途: 是否启用紧凑模式
  - 功能: 控制操作列表的显示样式

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    this.rtc = useState(useService("discuss.rtc"));
    this.callActions = useCallActions();
}
```

**初始化内容**:
- 邮件存储服务
- RTC通信服务
- 通话操作钩子

## 核心功能

### 1. 状态检测

```javascript
get isOfActiveCall() {
    return Boolean(this.props.thread.eq(this.rtc.state?.channel));
}

get isSmall() {
    return Boolean(this.props.compact && !this.props.fullscreen.isActive);
}

get isMobileOS() {
    return isMobileOS();
}
```

**状态判断**:
- **isOfActiveCall**: 判断是否为当前活跃通话
- **isSmall**: 判断是否应该使用小尺寸显示
- **isMobileOS**: 检测是否为移动操作系统

### 2. 通话操作

```javascript
async onClickRejectCall(ev) {
    if (this.rtc.state.hasPendingRequest) {
        return;
    }
    await this.rtc.leaveCall(this.props.thread);
}

async onClickToggleAudioCall(ev) {
    await this.rtc.toggleCall(this.props.thread);
}
```

**操作功能**:
- **拒绝通话**: 离开当前通话
- **切换音频通话**: 开启或关闭音频通话

## 使用场景

### 1. 通话控制面板

```javascript
// 通话控制面板增强
const CallControlPanel = {
    createAdvancedPanel: (thread, options = {}) => {
        const panel = document.createElement('div');
        panel.className = 'advanced-call-control-panel';
        panel.innerHTML = `
            <div class="control-sections">
                <div class="primary-controls">
                    <button class="control-btn mute-btn" data-action="toggleMute">
                        <i class="fa fa-microphone"></i>
                        <span>静音</span>
                    </button>
                    <button class="control-btn video-btn" data-action="toggleVideo">
                        <i class="fa fa-video"></i>
                        <span>视频</span>
                    </button>
                    <button class="control-btn screen-btn" data-action="toggleScreen">
                        <i class="fa fa-desktop"></i>
                        <span>共享</span>
                    </button>
                    <button class="control-btn hang-up-btn" data-action="hangUp">
                        <i class="fa fa-phone"></i>
                        <span>挂断</span>
                    </button>
                </div>
                
                <div class="secondary-controls">
                    <CallActionList 
                        thread={thread} 
                        fullscreen={options.fullscreen}
                        compact={options.compact}
                    />
                </div>
                
                <div class="advanced-controls">
                    <button class="control-btn settings-btn" data-action="openSettings">
                        <i class="fa fa-cog"></i>
                        <span>设置</span>
                    </button>
                    <button class="control-btn participants-btn" data-action="showParticipants">
                        <i class="fa fa-users"></i>
                        <span>参与者</span>
                    </button>
                    <button class="control-btn chat-btn" data-action="toggleChat">
                        <i class="fa fa-comment"></i>
                        <span>聊天</span>
                    </button>
                </div>
            </div>
            
            <div class="control-status">
                <div class="connection-status">
                    <i class="fa fa-wifi"></i>
                    <span class="status-text">连接良好</span>
                </div>
                <div class="call-duration">
                    <i class="fa fa-clock-o"></i>
                    <span class="duration-text">00:00:00</span>
                </div>
            </div>
        `;
        
        CallControlPanel.bindControlEvents(panel, thread);
        CallControlPanel.startDurationTimer(panel);
        
        return panel;
    },
    
    bindControlEvents: (panel, thread) => {
        panel.querySelectorAll('.control-btn').forEach(btn => {
            btn.addEventListener('click', async (event) => {
                const action = event.currentTarget.dataset.action;
                await CallControlPanel.executeAction(action, thread, event.currentTarget);
            });
        });
    },
    
    executeAction: async (action, thread, button) => {
        const rtcService = useService("discuss.rtc");
        
        try {
            switch (action) {
                case 'toggleMute':
                    await CallControlPanel.toggleMute(rtcService, button);
                    break;
                case 'toggleVideo':
                    await CallControlPanel.toggleVideo(rtcService, button);
                    break;
                case 'toggleScreen':
                    await CallControlPanel.toggleScreenShare(rtcService, button);
                    break;
                case 'hangUp':
                    await CallControlPanel.hangUp(rtcService, thread);
                    break;
                case 'openSettings':
                    CallControlPanel.openSettings();
                    break;
                case 'showParticipants':
                    CallControlPanel.showParticipants(thread);
                    break;
                case 'toggleChat':
                    CallControlPanel.toggleChat(thread);
                    break;
            }
        } catch (error) {
            console.error(`执行操作 ${action} 失败:`, error);
            showNotification('操作失败，请重试', 'error');
        }
    },
    
    toggleMute: async (rtcService, button) => {
        const isMuted = rtcService.state.isMuted;
        await rtcService.toggleMute();
        
        const icon = button.querySelector('i');
        const text = button.querySelector('span');
        
        if (isMuted) {
            icon.className = 'fa fa-microphone';
            text.textContent = '静音';
            button.classList.remove('active');
        } else {
            icon.className = 'fa fa-microphone-slash';
            text.textContent = '取消静音';
            button.classList.add('active');
        }
    },
    
    toggleVideo: async (rtcService, button) => {
        const isVideoOn = rtcService.state.isVideoOn;
        await rtcService.toggleVideo();
        
        const icon = button.querySelector('i');
        const text = button.querySelector('span');
        
        if (isVideoOn) {
            icon.className = 'fa fa-video-slash';
            text.textContent = '开启视频';
            button.classList.add('active');
        } else {
            icon.className = 'fa fa-video';
            text.textContent = '关闭视频';
            button.classList.remove('active');
        }
    },
    
    toggleScreenShare: async (rtcService, button) => {
        const isScreenSharing = rtcService.state.isScreenSharing;
        
        if (isScreenSharing) {
            await rtcService.stopScreenShare();
            button.querySelector('span').textContent = '共享屏幕';
            button.classList.remove('active');
        } else {
            await rtcService.startScreenShare();
            button.querySelector('span').textContent = '停止共享';
            button.classList.add('active');
        }
    },
    
    hangUp: async (rtcService, thread) => {
        const confirmed = confirm('确定要结束通话吗？');
        if (confirmed) {
            await rtcService.leaveCall(thread);
            showNotification('通话已结束', 'info');
        }
    },
    
    openSettings: () => {
        const settingsDialog = CallControlPanel.createSettingsDialog();
        document.body.appendChild(settingsDialog);
    },
    
    createSettingsDialog: () => {
        const dialog = document.createElement('div');
        dialog.className = 'call-settings-dialog-overlay';
        dialog.innerHTML = `
            <div class="settings-dialog">
                <div class="dialog-header">
                    <h3>通话设置</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="settings-section">
                        <h4>音频设置</h4>
                        <div class="setting-item">
                            <label>麦克风</label>
                            <select class="device-select" data-type="audioinput">
                                <option>默认麦克风</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>扬声器</label>
                            <select class="device-select" data-type="audiooutput">
                                <option>默认扬声器</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h4>视频设置</h4>
                        <div class="setting-item">
                            <label>摄像头</label>
                            <select class="device-select" data-type="videoinput">
                                <option>默认摄像头</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label>视频质量</label>
                            <select class="quality-select">
                                <option value="720p">720p</option>
                                <option value="1080p">1080p</option>
                                <option value="480p">480p</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="settings-section">
                        <h4>高级设置</h4>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" class="setting-checkbox" data-setting="backgroundBlur">
                                启用背景模糊
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" class="setting-checkbox" data-setting="noiseSuppression">
                                噪音抑制
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" class="setting-checkbox" data-setting="echoCancellation">
                                回声消除
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary cancel-btn">取消</button>
                    <button class="btn btn-primary save-btn">保存</button>
                </div>
            </div>
        `;
        
        CallControlPanel.bindSettingsEvents(dialog);
        CallControlPanel.loadDevices(dialog);
        
        return dialog;
    },
    
    bindSettingsEvents: (dialog) => {
        // 关闭对话框
        dialog.querySelector('.close-btn').addEventListener('click', () => {
            dialog.remove();
        });
        
        dialog.querySelector('.cancel-btn').addEventListener('click', () => {
            dialog.remove();
        });
        
        // 保存设置
        dialog.querySelector('.save-btn').addEventListener('click', () => {
            CallControlPanel.saveSettings(dialog);
            dialog.remove();
        });
    },
    
    loadDevices: async (dialog) => {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            
            const audioInputSelect = dialog.querySelector('[data-type="audioinput"]');
            const audioOutputSelect = dialog.querySelector('[data-type="audiooutput"]');
            const videoInputSelect = dialog.querySelector('[data-type="videoinput"]');
            
            devices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.deviceId;
                option.textContent = device.label || `${device.kind} ${device.deviceId.slice(0, 8)}`;
                
                if (device.kind === 'audioinput') {
                    audioInputSelect.appendChild(option);
                } else if (device.kind === 'audiooutput') {
                    audioOutputSelect.appendChild(option);
                } else if (device.kind === 'videoinput') {
                    videoInputSelect.appendChild(option);
                }
            });
        } catch (error) {
            console.error('加载设备列表失败:', error);
        }
    },
    
    saveSettings: (dialog) => {
        const settings = {};
        
        // 收集设备设置
        dialog.querySelectorAll('.device-select').forEach(select => {
            settings[select.dataset.type] = select.value;
        });
        
        // 收集质量设置
        const qualitySelect = dialog.querySelector('.quality-select');
        settings.videoQuality = qualitySelect.value;
        
        // 收集高级设置
        dialog.querySelectorAll('.setting-checkbox').forEach(checkbox => {
            settings[checkbox.dataset.setting] = checkbox.checked;
        });
        
        // 保存到本地存储
        localStorage.setItem('call_settings', JSON.stringify(settings));
        
        showNotification('设置已保存', 'success');
    },
    
    showParticipants: (thread) => {
        const participantsPanel = CallControlPanel.createParticipantsPanel(thread);
        document.body.appendChild(participantsPanel);
    },
    
    createParticipantsPanel: (thread) => {
        const panel = document.createElement('div');
        panel.className = 'participants-panel-overlay';
        panel.innerHTML = `
            <div class="participants-panel">
                <div class="panel-header">
                    <h3>参与者 (${thread.rtcSessions.length})</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="panel-content">
                    <div class="participants-list">
                        ${thread.rtcSessions.map(session => `
                            <div class="participant-item">
                                <img src="${session.channelMember?.persona?.avatarUrl}" 
                                     alt="${session.channelMember?.persona?.name}" 
                                     class="participant-avatar">
                                <div class="participant-info">
                                    <span class="participant-name">
                                        ${session.channelMember?.persona?.name}
                                    </span>
                                    <div class="participant-status">
                                        ${session.isMuted ? '<i class="fa fa-microphone-slash"></i>' : '<i class="fa fa-microphone"></i>'}
                                        ${session.isCameraOn ? '<i class="fa fa-video"></i>' : '<i class="fa fa-video-slash"></i>'}
                                    </div>
                                </div>
                                <div class="participant-actions">
                                    <button class="action-btn" title="私聊">
                                        <i class="fa fa-comment"></i>
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        panel.querySelector('.close-btn').addEventListener('click', () => {
            panel.remove();
        });
        
        return panel;
    },
    
    toggleChat: (thread) => {
        const chatPanel = document.querySelector('.chat-panel');
        if (chatPanel) {
            chatPanel.style.display = chatPanel.style.display === 'none' ? 'block' : 'none';
        } else {
            CallControlPanel.createChatPanel(thread);
        }
    },
    
    createChatPanel: (thread) => {
        const panel = document.createElement('div');
        panel.className = 'chat-panel';
        panel.innerHTML = `
            <div class="chat-header">
                <h4>聊天</h4>
                <button class="minimize-chat-btn">−</button>
            </div>
            
            <div class="chat-messages">
                <!-- 聊天消息列表 -->
            </div>
            
            <div class="chat-input">
                <input type="text" placeholder="输入消息..." class="message-input">
                <button class="send-btn">发送</button>
            </div>
        `;
        
        document.body.appendChild(panel);
        
        // 绑定聊天事件
        CallControlPanel.bindChatEvents(panel, thread);
    },
    
    bindChatEvents: (panel, thread) => {
        const sendMessage = () => {
            const input = panel.querySelector('.message-input');
            const message = input.value.trim();
            if (message) {
                // 发送消息逻辑
                input.value = '';
            }
        };
        
        panel.querySelector('.send-btn').addEventListener('click', sendMessage);
        panel.querySelector('.message-input').addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });
        
        panel.querySelector('.minimize-chat-btn').addEventListener('click', () => {
            panel.style.display = 'none';
        });
    },
    
    startDurationTimer: (panel) => {
        const durationElement = panel.querySelector('.duration-text');
        const startTime = Date.now();
        
        const updateDuration = () => {
            const elapsed = Date.now() - startTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            durationElement.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        };
        
        updateDuration();
        const timer = setInterval(updateDuration, 1000);
        
        // 清理定时器
        panel.addEventListener('remove', () => {
            clearInterval(timer);
        });
    }
};
```

### 2. 移动端适配

```javascript
// 移动端通话控制适配
const MobileCallControls = {
    adaptForMobile: (actionList) => {
        if (!isMobileOS()) return;
        
        // 添加移动端特定样式
        actionList.el.classList.add('mobile-call-controls');
        
        // 添加手势支持
        MobileCallControls.addGestureSupport(actionList);
        
        // 优化触摸目标
        MobileCallControls.optimizeTouchTargets(actionList);
        
        // 添加震动反馈
        MobileCallControls.addHapticFeedback(actionList);
    },
    
    addGestureSupport: (actionList) => {
        let startY = 0;
        let currentY = 0;
        
        actionList.el.addEventListener('touchstart', (event) => {
            startY = event.touches[0].clientY;
        });
        
        actionList.el.addEventListener('touchmove', (event) => {
            currentY = event.touches[0].clientY;
            const deltaY = currentY - startY;
            
            // 上滑显示更多控制
            if (deltaY < -50) {
                MobileCallControls.showMoreControls(actionList);
            }
            // 下滑隐藏控制
            else if (deltaY > 50) {
                MobileCallControls.hideControls(actionList);
            }
        });
    },
    
    optimizeTouchTargets: (actionList) => {
        const buttons = actionList.el.querySelectorAll('button');
        buttons.forEach(button => {
            // 确保触摸目标至少44px
            const rect = button.getBoundingClientRect();
            if (rect.width < 44 || rect.height < 44) {
                button.style.minWidth = '44px';
                button.style.minHeight = '44px';
            }
        });
    },
    
    addHapticFeedback: (actionList) => {
        const buttons = actionList.el.querySelectorAll('button');
        buttons.forEach(button => {
            button.addEventListener('click', () => {
                if (navigator.vibrate) {
                    navigator.vibrate(50); // 50ms震动
                }
            });
        });
    },
    
    showMoreControls: (actionList) => {
        const moreControls = actionList.el.querySelector('.more-controls');
        if (moreControls) {
            moreControls.style.display = 'block';
        }
    },
    
    hideControls: (actionList) => {
        const controls = actionList.el.querySelector('.primary-controls');
        if (controls) {
            controls.style.opacity = '0.5';
            setTimeout(() => {
                controls.style.opacity = '1';
            }, 2000);
        }
    }
};
```

## 技术特点

### 1. 响应式设计
- 移动端适配
- 紧凑模式支持
- 全屏状态检测

### 2. 操作集成
- 通话操作钩子
- 下拉菜单组件
- 状态同步

### 3. 用户体验
- 直观的操作界面
- 快速访问常用功能
- 移动端优化

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组合多个操作按钮
- 统一的操作接口

### 2. 策略模式 (Strategy Pattern)
- 不同平台的不同显示策略
- 可配置的操作行为

### 3. 观察者模式 (Observer Pattern)
- 监听通话状态变化
- 响应用户操作

## 注意事项

1. **移动端适配**: 确保触摸友好的界面设计
2. **状态同步**: 保持操作状态与通话状态一致
3. **性能优化**: 避免频繁的状态检查
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **自定义操作**: 支持用户自定义操作按钮
2. **快捷键**: 添加键盘快捷键支持
3. **语音控制**: 集成语音命令控制
4. **手势识别**: 支持手势操作
5. **主题定制**: 支持自定义主题和样式

该组件为视频通话提供了完整的操作控制界面，是通话系统的重要交互入口。

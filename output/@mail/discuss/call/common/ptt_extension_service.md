# PTT Extension Service - 推送通话扩展服务

## 概述

`ptt_extension_service.js` 实现了 Odoo 讨论应用中的推送通话(Push-to-Talk)扩展服务，负责与Chrome浏览器扩展程序的通信和集成。该服务支持扩展版本检测、消息通信、语音激活控制、说话状态通知等功能，集成了浏览器扩展API和RTC服务，提供了完整的PTT扩展集成方案，是视频通话系统中扩展功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/ptt_extension_service.js`
- **行数**: 122
- **模块**: `@mail/discuss/call/common/ptt_extension_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@mail/utils/common/misc'      // 通用工具
'@web/core/browser/browser'    // 浏览器服务
'@web/core/registry'           // 注册表系统
'@web/core/utils/strings'      // 字符串工具
'@web/core/l10n/translation'   // 国际化
```

## 核心常量

### 扩展配置

```javascript
const INITIAL_RELEASE_TIMEOUT = 500;
const COMMON_RELEASE_TIMEOUT = 200;
const EXT_ID = "mdiacebcbkmjjlpclnbcgiepgifcnpmg";
```

**配置说明**:
- **INITIAL_RELEASE_TIMEOUT**: 首次按键释放超时(500ms)
- **COMMON_RELEASE_TIMEOUT**: 常规按键释放超时(200ms)
- **EXT_ID**: Chrome扩展程序ID

## 服务定义

### pttExtensionHookService 对象

```javascript
const pttExtensionHookService = {
    start(env) {
        // 服务初始化逻辑
        return {
            notifyIsTalking(isTalking) { /* ... */ },
            subscribe() { /* ... */ },
            unsubscribe() { /* ... */ },
            get isEnabled() { /* ... */ },
            downloadURL: `https://chromewebstore.google.com/detail/discuss-push-to-talk/${EXT_ID}`,
            get downloadText() { /* ... */ }
        };
    }
};
```

**服务特性**:
- 环境依赖注入
- 扩展通信管理
- 状态跟踪和控制
- 下载链接提供

## 核心功能

### 1. 扩展版本检测

```javascript
const versionPromise =
    window.chrome?.runtime?.sendMessage(EXT_ID, { type: "ask-version" }) ??
    Promise.resolve("*******");
```

**版本检测功能**:
- **Chrome API**: 使用Chrome运行时API发送消息
- **版本查询**: 向扩展程序查询版本信息
- **默认回退**: 无扩展时使用默认版本

### 2. 消息监听处理

```javascript
browser.addEventListener("message", ({ data, origin, source }) => {
    const rtc = env.services["discuss.rtc"];
    if (
        source !== window ||
        origin !== location.origin ||
        data.from !== "discuss-push-to-talk" ||
        (!rtc && data.type !== "answer-is-enabled")
    ) {
        return;
    }
    switch (data.type) {
        case "push-to-talk-pressed": { /* ... */ } break;
        case "toggle-voice": { /* ... */ } break;
        case "answer-is-enabled": { /* ... */ } break;
    }
});
```

**消息处理功能**:
- **安全验证**: 验证消息来源和域名
- **消息过滤**: 只处理PTT扩展消息
- **类型分发**: 根据消息类型执行不同逻辑

### 3. PTT按键处理

```javascript
case "push-to-talk-pressed":
    {
        voiceActivated = false;
        const isFirstPress = !rtc.selfSession?.isTalking;
        rtc.onPushToTalk();
        if (rtc.selfSession?.isTalking) {
            rtc.setPttReleaseTimeout(
                isFirstPress ? INITIAL_RELEASE_TIMEOUT : COMMON_RELEASE_TIMEOUT
            );
        }
    }
    break;
```

**按键处理功能**:
- **状态重置**: 重置语音激活状态
- **首次检测**: 检测是否为首次按键
- **RTC调用**: 调用RTC服务的PTT方法
- **超时设置**: 根据按键次数设置不同超时

### 4. 语音切换处理

```javascript
case "toggle-voice":
    {
        if (voiceActivated) {
            rtc.setPttReleaseTimeout(0);
        } else {
            rtc.onPushToTalk();
        }
        voiceActivated = !voiceActivated;
    }
    break;
```

**语音切换功能**:
- **状态切换**: 切换语音激活状态
- **超时控制**: 激活时清除超时，否则触发PTT
- **状态同步**: 更新本地语音激活状态

### 5. 消息发送

```javascript
async function sendMessage(type, value) {
    if (!isEnabled && type !== "ask-is-enabled") {
        return;
    }
    const version = parseVersion(await versionPromise);
    if (version.isLowerThan("*******")) {
        window.postMessage({ from: "discuss", type, value }, location.origin);
        return;
    }
    window.chrome?.runtime?.sendMessage(EXT_ID, { type, value });
}
```

**消息发送功能**:
- **启用检查**: 检查扩展是否启用
- **版本兼容**: 根据版本选择通信方式
- **消息格式**: 统一的消息格式和来源标识

## 使用场景

### 1. PTT扩展集成增强

```javascript
// PTT扩展集成增强
const PTTExtensionEnhancer = {
    enhancePTTExtension: () => {
        const EnhancedPTTService = {
            start(env) {
                // 调用原始服务
                const originalService = pttExtensionHookService.start(env);
                
                // 添加增强功能
                const enhancedService = {
                    ...originalService,
                    
                    // 扩展状态管理
                    extensionState: {
                        isInstalled: false,
                        isEnabled: false,
                        version: null,
                        lastActivity: null,
                        connectionStatus: 'disconnected'
                    },
                    
                    // 增强的初始化
                    async initialize() {
                        await this.checkExtensionStatus();
                        this.setupExtensionMonitoring();
                        this.setupAdvancedMessageHandling();
                    },
                    
                    // 检查扩展状态
                    async checkExtensionStatus() {
                        try {
                            const version = await this.getExtensionVersion();
                            this.extensionState.isInstalled = true;
                            this.extensionState.version = version;
                            
                            // 检查是否启用
                            await this.sendMessage("ask-is-enabled");
                            
                            this.extensionState.connectionStatus = 'connected';
                        } catch (error) {
                            console.warn('PTT扩展检查失败:', error);
                            this.extensionState.isInstalled = false;
                            this.extensionState.connectionStatus = 'disconnected';
                        }
                    },
                    
                    // 获取扩展版本
                    async getExtensionVersion() {
                        if (!window.chrome?.runtime) {
                            throw new Error('Chrome运行时不可用');
                        }
                        
                        try {
                            const response = await window.chrome.runtime.sendMessage(EXT_ID, { 
                                type: "ask-version" 
                            });
                            return response || "*******";
                        } catch (error) {
                            throw new Error('无法获取扩展版本');
                        }
                    },
                    
                    // 设置扩展监控
                    setupExtensionMonitoring() {
                        // 定期检查扩展状态
                        setInterval(() => {
                            this.checkExtensionHealth();
                        }, 30000); // 每30秒检查一次
                        
                        // 监听扩展连接状态
                        this.setupConnectionMonitoring();
                    },
                    
                    // 检查扩展健康状态
                    async checkExtensionHealth() {
                        try {
                            await this.sendMessage("ping");
                            this.extensionState.lastActivity = Date.now();
                            
                            if (this.extensionState.connectionStatus !== 'connected') {
                                this.extensionState.connectionStatus = 'connected';
                                this.onExtensionReconnected();
                            }
                        } catch (error) {
                            if (this.extensionState.connectionStatus !== 'disconnected') {
                                this.extensionState.connectionStatus = 'disconnected';
                                this.onExtensionDisconnected();
                            }
                        }
                    },
                    
                    // 设置连接监控
                    setupConnectionMonitoring() {
                        // 监听Chrome扩展连接事件
                        if (window.chrome?.runtime) {
                            window.chrome.runtime.onConnect.addListener((port) => {
                                if (port.name === 'discuss-ptt') {
                                    this.handleExtensionConnection(port);
                                }
                            });
                        }
                    },
                    
                    // 处理扩展连接
                    handleExtensionConnection(port) {
                        console.log('PTT扩展已连接');
                        this.extensionState.connectionStatus = 'connected';
                        
                        port.onDisconnect.addListener(() => {
                            console.log('PTT扩展已断开');
                            this.extensionState.connectionStatus = 'disconnected';
                            this.onExtensionDisconnected();
                        });
                        
                        port.onMessage.addListener((message) => {
                            this.handleExtensionMessage(message);
                        });
                    },
                    
                    // 处理扩展消息
                    handleExtensionMessage(message) {
                        console.log('收到扩展消息:', message);
                        
                        // 更新最后活动时间
                        this.extensionState.lastActivity = Date.now();
                        
                        // 处理特定消息类型
                        switch (message.type) {
                            case 'status_update':
                                this.handleStatusUpdate(message.data);
                                break;
                            case 'error':
                                this.handleExtensionError(message.data);
                                break;
                            case 'settings_changed':
                                this.handleSettingsChanged(message.data);
                                break;
                        }
                    },
                    
                    // 处理状态更新
                    handleStatusUpdate(data) {
                        Object.assign(this.extensionState, data);
                        this.notifyStateChange();
                    },
                    
                    // 处理扩展错误
                    handleExtensionError(error) {
                        console.error('PTT扩展错误:', error);
                        
                        // 显示用户通知
                        env.services.notification.add(
                            `PTT扩展错误: ${error.message}`,
                            { type: 'warning' }
                        );
                    },
                    
                    // 处理设置变化
                    handleSettingsChanged(settings) {
                        console.log('PTT扩展设置已更改:', settings);
                        
                        // 同步本地设置
                        this.syncExtensionSettings(settings);
                    },
                    
                    // 同步扩展设置
                    syncExtensionSettings(settings) {
                        // 更新本地PTT设置
                        const rtcService = env.services["discuss.rtc"];
                        if (rtcService && settings.pttKey) {
                            rtcService.updatePTTKey(settings.pttKey);
                        }
                    },
                    
                    // 扩展重新连接处理
                    onExtensionReconnected() {
                        console.log('PTT扩展已重新连接');
                        
                        // 重新订阅
                        this.subscribe();
                        
                        // 同步当前状态
                        this.syncCurrentState();
                        
                        // 显示通知
                        env.services.notification.add(
                            'PTT扩展已重新连接',
                            { type: 'success' }
                        );
                    },
                    
                    // 扩展断开连接处理
                    onExtensionDisconnected() {
                        console.log('PTT扩展连接已断开');
                        
                        // 显示通知
                        env.services.notification.add(
                            'PTT扩展连接已断开，PTT功能可能受限',
                            { type: 'warning' }
                        );
                    },
                    
                    // 同步当前状态
                    syncCurrentState() {
                        const rtcService = env.services["discuss.rtc"];
                        if (rtcService && rtcService.selfSession) {
                            this.notifyIsTalking(rtcService.selfSession.isTalking);
                        }
                    },
                    
                    // 通知状态变化
                    notifyStateChange() {
                        const event = new CustomEvent('ptt_extension_state_changed', {
                            detail: this.extensionState
                        });
                        document.dispatchEvent(event);
                    },
                    
                    // 获取扩展信息
                    getExtensionInfo() {
                        return {
                            ...this.extensionState,
                            downloadURL: this.downloadURL,
                            downloadText: this.downloadText
                        };
                    },
                    
                    // 安装扩展
                    installExtension() {
                        window.open(this.downloadURL, '_blank');
                    },
                    
                    // 检查扩展兼容性
                    checkCompatibility() {
                        if (!window.chrome?.runtime) {
                            return {
                                compatible: false,
                                reason: '不支持Chrome扩展API'
                            };
                        }
                        
                        if (!this.extensionState.isInstalled) {
                            return {
                                compatible: false,
                                reason: '扩展未安装'
                            };
                        }
                        
                        const version = parseVersion(this.extensionState.version);
                        if (version.isLowerThan("1.0.0.1")) {
                            return {
                                compatible: false,
                                reason: '扩展版本过低，请更新'
                            };
                        }
                        
                        return {
                            compatible: true,
                            reason: '兼容'
                        };
                    }
                };
                
                // 初始化增强服务
                enhancedService.initialize();
                
                return enhancedService;
            }
        };
        
        // 替换原始服务
        registry.category("services").add("discuss.ptt_extension", EnhancedPTTService);
    }
};

// 应用PTT扩展增强
PTTExtensionEnhancer.enhancePTTExtension();
```

### 2. PTT快捷键管理

```javascript
// PTT快捷键管理
const PTTShortcutManager = {
    setupShortcutManagement: () => {
        const ShortcutPatch = {
            start(env) {
                const originalService = pttExtensionHookService.start(env);
                
                return {
                    ...originalService,
                    
                    // 快捷键配置
                    shortcutConfig: {
                        enabled: true,
                        key: 'Space',
                        modifiers: {
                            ctrl: false,
                            alt: false,
                            shift: false
                        },
                        customKeys: new Map()
                    },
                    
                    // 设置快捷键
                    setShortcutKey(key, modifiers = {}) {
                        this.shortcutConfig.key = key;
                        this.shortcutConfig.modifiers = { ...modifiers };
                        
                        // 通知扩展更新快捷键
                        this.sendMessage("update-shortcut", {
                            key: key,
                            modifiers: modifiers
                        });
                    },
                    
                    // 添加自定义快捷键
                    addCustomShortcut(name, key, action) {
                        this.shortcutConfig.customKeys.set(name, {
                            key: key,
                            action: action
                        });
                        
                        // 注册到扩展
                        this.sendMessage("register-custom-shortcut", {
                            name: name,
                            key: key
                        });
                    },
                    
                    // 移除自定义快捷键
                    removeCustomShortcut(name) {
                        this.shortcutConfig.customKeys.delete(name);
                        
                        // 从扩展注销
                        this.sendMessage("unregister-custom-shortcut", {
                            name: name
                        });
                    },
                    
                    // 启用/禁用快捷键
                    setShortcutEnabled(enabled) {
                        this.shortcutConfig.enabled = enabled;
                        
                        if (enabled) {
                            this.subscribe();
                        } else {
                            this.unsubscribe();
                        }
                    },
                    
                    // 获取快捷键配置
                    getShortcutConfig() {
                        return { ...this.shortcutConfig };
                    }
                };
            }
        };
        
        registry.category("services").add("discuss.ptt_extension", ShortcutPatch);
    }
};

// 应用快捷键管理
PTTShortcutManager.setupShortcutManagement();
```

## 技术特点

### 1. 扩展集成
- Chrome扩展API集成
- 版本兼容性处理
- 消息通信机制

### 2. 状态管理
- 扩展启用状态跟踪
- 语音激活状态管理
- 连接状态监控

### 3. 消息处理
- 安全的消息验证
- 类型化消息分发
- 异步消息发送

### 4. 用户体验
- 下载链接提供
- 国际化支持
- 错误处理机制

## 设计模式

### 1. 服务模式 (Service Pattern)
- 标准的服务定义和注册
- 依赖注入机制

### 2. 消息模式 (Message Pattern)
- 消息驱动的通信
- 事件处理机制

### 3. 代理模式 (Proxy Pattern)
- 扩展功能的代理访问
- 透明的API封装

## 注意事项

1. **浏览器兼容性**: 只支持Chrome浏览器扩展
2. **版本兼容**: 处理不同扩展版本的兼容性
3. **安全验证**: 严格验证消息来源和格式
4. **错误处理**: 完善的扩展连接错误处理

## 扩展建议

1. **多浏览器支持**: 扩展支持其他浏览器的扩展API
2. **离线模式**: 支持无扩展时的降级功能
3. **配置管理**: 增强PTT配置管理功能
4. **统计分析**: 添加PTT使用统计和分析
5. **自动更新**: 支持扩展自动更新检测

该服务为视频通话系统提供了完整的PTT扩展集成功能，是扩展功能的重要桥梁。

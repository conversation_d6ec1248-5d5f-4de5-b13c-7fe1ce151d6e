# Discuss P2P Service - 讨论点对点服务

## 概述

`discuss_p2p_service.js` 实现了 Odoo 讨论应用中的点对点通信服务，负责管理和协调P2P连接。该服务集成了PeerToPeer类和总线服务，支持点对点通知处理、会话通知路由、消息分发等功能，提供了完整的P2P通信基础设施，是视频通话系统中点对点连接的核心服务层。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/discuss_p2p_service.js`
- **行数**: 34
- **模块**: `@mail/discuss/call/common/discuss_p2p_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                        // 注册表系统
'@mail/discuss/call/common/peer_to_peer'    // 点对点通信类
```

## 服务定义

### discussP2P 服务

```javascript
const discussP2P = {
    dependencies: ["bus_service"],
    start(env, services) {
        const p2p = new PeerToPeer({ 
            notificationRoute: "/mail/rtc/session/notify_call_members" 
        });
        
        services["bus_service"].subscribe(
            "discuss.channel.rtc.session/peer_notification",
            ({ sender, notifications }) => {
                for (const content of notifications) {
                    p2p.handleNotification(sender, content);
                }
            }
        );
        
        return p2p;
    },
};
```

**服务特性**:
- 依赖总线服务
- 创建PeerToPeer实例
- 订阅点对点通知
- 处理通知消息

## 核心功能

### 1. 服务初始化

```javascript
start(env, services) {
    const p2p = new PeerToPeer({ 
        notificationRoute: "/mail/rtc/session/notify_call_members" 
    });
    // ...
    return p2p;
}
```

**初始化功能**:
- **P2P实例创建**: 创建点对点通信实例
- **通知路由配置**: 设置会话通知路由
- **服务返回**: 返回P2P实例供其他组件使用

### 2. 通知订阅

```javascript
services["bus_service"].subscribe(
    "discuss.channel.rtc.session/peer_notification",
    ({ sender, notifications }) => {
        for (const content of notifications) {
            p2p.handleNotification(sender, content);
        }
    }
);
```

**订阅功能**:
- **总线订阅**: 订阅点对点通知频道
- **消息处理**: 处理来自发送者的通知
- **批量处理**: 支持批量通知处理

### 3. 服务注册

```javascript
registry.category("services").add("discuss.p2p", discussP2P);
```

**注册功能**:
- **服务注册**: 将服务注册到Odoo服务注册表
- **全局访问**: 提供全局服务访问能力

## 使用场景

### 1. P2P连接管理

```javascript
// P2P连接管理增强
const P2PConnectionManager = {
    setupP2PService: (env, services) => {
        // 创建增强的P2P服务
        const enhancedP2P = P2PConnectionManager.createEnhancedP2P();
        
        // 设置连接监控
        P2PConnectionManager.setupConnectionMonitoring(enhancedP2P);
        
        // 设置质量监控
        P2PConnectionManager.setupQualityMonitoring(enhancedP2P);
        
        // 设置故障恢复
        P2PConnectionManager.setupFailureRecovery(enhancedP2P);
        
        return enhancedP2P;
    },
    
    createEnhancedP2P: () => {
        const p2p = new PeerToPeer({ 
            notificationRoute: "/mail/rtc/session/notify_call_members",
            // 增强配置
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                {
                    urls: 'turn:turnserver.example.com:3478',
                    username: 'user',
                    credential: 'pass'
                }
            ],
            connectionTimeout: 30000,
            reconnectAttempts: 3,
            reconnectDelay: 5000
        });
        
        // 添加连接事件监听
        P2PConnectionManager.addConnectionListeners(p2p);
        
        return p2p;
    },
    
    addConnectionListeners: (p2p) => {
        // 连接建立事件
        p2p.on('connection_established', (peerId) => {
            console.log(`P2P连接已建立: ${peerId}`);
            P2PConnectionManager.onConnectionEstablished(peerId);
        });
        
        // 连接失败事件
        p2p.on('connection_failed', (peerId, error) => {
            console.error(`P2P连接失败: ${peerId}`, error);
            P2PConnectionManager.onConnectionFailed(peerId, error);
        });
        
        // 连接断开事件
        p2p.on('connection_closed', (peerId) => {
            console.log(`P2P连接已断开: ${peerId}`);
            P2PConnectionManager.onConnectionClosed(peerId);
        });
        
        // 数据接收事件
        p2p.on('data_received', (peerId, data) => {
            P2PConnectionManager.onDataReceived(peerId, data);
        });
    },
    
    setupConnectionMonitoring: (p2p) => {
        const monitoringInterval = setInterval(() => {
            P2PConnectionManager.checkConnectionHealth(p2p);
        }, 5000); // 每5秒检查一次
        
        // 清理监控
        p2p.on('service_destroyed', () => {
            clearInterval(monitoringInterval);
        });
    },
    
    checkConnectionHealth: async (p2p) => {
        const connections = p2p.getActiveConnections();
        
        for (const [peerId, connection] of connections) {
            try {
                const stats = await connection.getStats();
                const health = P2PConnectionManager.analyzeConnectionHealth(stats);
                
                if (health.status === 'poor') {
                    console.warn(`连接质量较差: ${peerId}`, health);
                    P2PConnectionManager.handlePoorConnection(peerId, connection);
                }
            } catch (error) {
                console.error(`获取连接统计失败: ${peerId}`, error);
            }
        }
    },
    
    analyzeConnectionHealth: (stats) => {
        let packetsLost = 0;
        let packetsReceived = 0;
        let rtt = 0;
        
        stats.forEach(report => {
            if (report.type === 'inbound-rtp') {
                packetsLost += report.packetsLost || 0;
                packetsReceived += report.packetsReceived || 0;
            } else if (report.type === 'candidate-pair' && report.state === 'succeeded') {
                rtt = report.currentRoundTripTime || 0;
            }
        });
        
        const lossRate = packetsReceived > 0 ? packetsLost / packetsReceived : 0;
        
        let status = 'good';
        if (lossRate > 0.1 || rtt > 0.5) {
            status = 'poor';
        } else if (lossRate > 0.05 || rtt > 0.3) {
            status = 'fair';
        }
        
        return {
            status,
            lossRate,
            rtt,
            packetsLost,
            packetsReceived
        };
    },
    
    handlePoorConnection: (peerId, connection) => {
        // 尝试重新协商连接
        P2PConnectionManager.renegotiateConnection(peerId, connection);
        
        // 通知用户连接问题
        showNotification(`与 ${peerId} 的连接质量较差`, 'warning');
    },
    
    renegotiateConnection: async (peerId, connection) => {
        try {
            // 重启ICE连接
            await connection.restartIce();
            console.log(`已重启ICE连接: ${peerId}`);
        } catch (error) {
            console.error(`重启ICE连接失败: ${peerId}`, error);
        }
    },
    
    setupQualityMonitoring: (p2p) => {
        const qualityMetrics = new Map();
        
        const updateQualityMetrics = () => {
            const connections = p2p.getActiveConnections();
            
            connections.forEach(async (connection, peerId) => {
                try {
                    const stats = await connection.getStats();
                    const quality = P2PConnectionManager.calculateQualityScore(stats);
                    
                    qualityMetrics.set(peerId, {
                        score: quality.score,
                        timestamp: Date.now(),
                        details: quality.details
                    });
                    
                    // 更新UI显示
                    P2PConnectionManager.updateQualityDisplay(peerId, quality);
                } catch (error) {
                    console.error(`更新质量指标失败: ${peerId}`, error);
                }
            });
        };
        
        const qualityInterval = setInterval(updateQualityMetrics, 3000);
        
        // 清理质量监控
        p2p.on('service_destroyed', () => {
            clearInterval(qualityInterval);
        });
        
        // 存储质量指标
        p2p.getQualityMetrics = () => qualityMetrics;
    },
    
    calculateQualityScore: (stats) => {
        let videoScore = 0;
        let audioScore = 0;
        let connectionScore = 0;
        
        stats.forEach(report => {
            switch (report.type) {
                case 'inbound-rtp':
                    if (report.mediaType === 'video') {
                        videoScore = P2PConnectionManager.calculateVideoScore(report);
                    } else if (report.mediaType === 'audio') {
                        audioScore = P2PConnectionManager.calculateAudioScore(report);
                    }
                    break;
                case 'candidate-pair':
                    if (report.state === 'succeeded') {
                        connectionScore = P2PConnectionManager.calculateConnectionScore(report);
                    }
                    break;
            }
        });
        
        const overallScore = (videoScore + audioScore + connectionScore) / 3;
        
        return {
            score: overallScore,
            details: {
                video: videoScore,
                audio: audioScore,
                connection: connectionScore
            }
        };
    },
    
    calculateVideoScore: (report) => {
        const fps = report.framesPerSecond || 0;
        const lossRate = (report.packetsLost || 0) / (report.packetsReceived || 1);
        
        let score = 100;
        if (fps < 15) score -= 30;
        else if (fps < 24) score -= 15;
        
        if (lossRate > 0.1) score -= 40;
        else if (lossRate > 0.05) score -= 20;
        
        return Math.max(0, score);
    },
    
    calculateAudioScore: (report) => {
        const lossRate = (report.packetsLost || 0) / (report.packetsReceived || 1);
        const jitter = report.jitter || 0;
        
        let score = 100;
        if (lossRate > 0.05) score -= 50;
        else if (lossRate > 0.02) score -= 25;
        
        if (jitter > 0.1) score -= 30;
        else if (jitter > 0.05) score -= 15;
        
        return Math.max(0, score);
    },
    
    calculateConnectionScore: (report) => {
        const rtt = report.currentRoundTripTime || 0;
        const availableBandwidth = report.availableOutgoingBitrate || 0;
        
        let score = 100;
        if (rtt > 0.5) score -= 40;
        else if (rtt > 0.3) score -= 20;
        
        if (availableBandwidth < 500000) score -= 30; // 500kbps
        else if (availableBandwidth < 1000000) score -= 15; // 1Mbps
        
        return Math.max(0, score);
    },
    
    updateQualityDisplay: (peerId, quality) => {
        const qualityElement = document.querySelector(`[data-peer-id="${peerId}"] .quality-indicator`);
        if (qualityElement) {
            const score = quality.score;
            let className = 'poor';
            
            if (score > 80) className = 'excellent';
            else if (score > 60) className = 'good';
            else if (score > 40) className = 'fair';
            
            qualityElement.className = `quality-indicator ${className}`;
            qualityElement.title = `连接质量: ${score.toFixed(0)}%`;
        }
    },
    
    setupFailureRecovery: (p2p) => {
        const failureHistory = new Map();
        
        p2p.on('connection_failed', (peerId, error) => {
            P2PConnectionManager.recordFailure(peerId, error, failureHistory);
            P2PConnectionManager.attemptRecovery(peerId, p2p, failureHistory);
        });
        
        p2p.on('connection_closed', (peerId) => {
            // 延迟重连，避免立即重连
            setTimeout(() => {
                P2PConnectionManager.attemptReconnection(peerId, p2p);
            }, 3000);
        });
    },
    
    recordFailure: (peerId, error, failureHistory) => {
        if (!failureHistory.has(peerId)) {
            failureHistory.set(peerId, []);
        }
        
        const failures = failureHistory.get(peerId);
        failures.push({
            timestamp: Date.now(),
            error: error.message || error.toString()
        });
        
        // 限制历史记录数量
        if (failures.length > 10) {
            failures.shift();
        }
    },
    
    attemptRecovery: (peerId, p2p, failureHistory) => {
        const failures = failureHistory.get(peerId) || [];
        const recentFailures = failures.filter(f => Date.now() - f.timestamp < 60000); // 1分钟内
        
        if (recentFailures.length >= 3) {
            console.warn(`连接失败次数过多，暂停重连: ${peerId}`);
            return;
        }
        
        // 延迟重连
        const delay = Math.min(5000 * Math.pow(2, recentFailures.length), 30000);
        setTimeout(() => {
            P2PConnectionManager.attemptReconnection(peerId, p2p);
        }, delay);
    },
    
    attemptReconnection: async (peerId, p2p) => {
        try {
            console.log(`尝试重新连接: ${peerId}`);
            await p2p.reconnect(peerId);
            console.log(`重新连接成功: ${peerId}`);
        } catch (error) {
            console.error(`重新连接失败: ${peerId}`, error);
        }
    },
    
    // 事件处理函数
    onConnectionEstablished: (peerId) => {
        showNotification(`已连接到 ${peerId}`, 'success');
    },
    
    onConnectionFailed: (peerId, error) => {
        showNotification(`连接 ${peerId} 失败`, 'error');
    },
    
    onConnectionClosed: (peerId) => {
        showNotification(`与 ${peerId} 的连接已断开`, 'info');
    },
    
    onDataReceived: (peerId, data) => {
        // 处理接收到的数据
        console.log(`收到来自 ${peerId} 的数据:`, data);
    }
};
```

### 2. 通知处理增强

```javascript
// 通知处理增强
const NotificationHandler = {
    enhanceNotificationHandling: (p2p, busService) => {
        // 创建通知队列
        const notificationQueue = new Map();
        
        // 增强的通知订阅
        busService.subscribe(
            "discuss.channel.rtc.session/peer_notification",
            ({ sender, notifications }) => {
                NotificationHandler.processNotifications(sender, notifications, p2p, notificationQueue);
            }
        );
        
        // 设置通知处理监控
        NotificationHandler.setupNotificationMonitoring(notificationQueue);
        
        return notificationQueue;
    },
    
    processNotifications: (sender, notifications, p2p, queue) => {
        // 添加到队列
        if (!queue.has(sender)) {
            queue.set(sender, []);
        }
        
        const senderQueue = queue.get(sender);
        
        for (const content of notifications) {
            senderQueue.push({
                content,
                timestamp: Date.now(),
                processed: false
            });
            
            // 立即处理通知
            NotificationHandler.handleNotification(sender, content, p2p);
        }
        
        // 清理旧通知
        NotificationHandler.cleanupOldNotifications(senderQueue);
    },
    
    handleNotification: (sender, content, p2p) => {
        try {
            // 记录通知
            console.log(`处理来自 ${sender} 的通知:`, content);
            
            // 调用原始处理函数
            p2p.handleNotification(sender, content);
            
            // 记录处理成功
            NotificationHandler.recordNotificationSuccess(sender, content);
        } catch (error) {
            console.error(`处理通知失败:`, error);
            NotificationHandler.recordNotificationError(sender, content, error);
        }
    },
    
    cleanupOldNotifications: (queue) => {
        const now = Date.now();
        const maxAge = 300000; // 5分钟
        
        for (let i = queue.length - 1; i >= 0; i--) {
            if (now - queue[i].timestamp > maxAge) {
                queue.splice(i, 1);
            }
        }
    },
    
    setupNotificationMonitoring: (queue) => {
        setInterval(() => {
            NotificationHandler.generateNotificationReport(queue);
        }, 60000); // 每分钟生成报告
    },
    
    generateNotificationReport: (queue) => {
        const report = {
            totalSenders: queue.size,
            totalNotifications: 0,
            processedNotifications: 0,
            errorRate: 0
        };
        
        queue.forEach((notifications, sender) => {
            report.totalNotifications += notifications.length;
            report.processedNotifications += notifications.filter(n => n.processed).length;
        });
        
        if (report.totalNotifications > 0) {
            report.errorRate = 1 - (report.processedNotifications / report.totalNotifications);
        }
        
        console.log('通知处理报告:', report);
        
        // 如果错误率过高，发出警告
        if (report.errorRate > 0.1) {
            console.warn('通知处理错误率过高:', report.errorRate);
        }
    },
    
    recordNotificationSuccess: (sender, content) => {
        // 记录成功处理的通知
    },
    
    recordNotificationError: (sender, content, error) => {
        // 记录处理失败的通知
        console.error(`通知处理错误 - 发送者: ${sender}, 内容:`, content, '错误:', error);
    }
};
```

## 技术特点

### 1. 服务架构
- 标准的Odoo服务模式
- 依赖注入机制
- 服务注册表集成

### 2. 通信集成
- 总线服务集成
- 点对点通信支持
- 通知路由配置

### 3. 消息处理
- 批量通知处理
- 异步消息分发
- 错误处理机制

### 4. 扩展性
- 可配置的通知路由
- 灵活的服务配置
- 模块化设计

## 设计模式

### 1. 服务模式 (Service Pattern)
- 标准的服务定义和注册
- 依赖管理和注入

### 2. 代理模式 (Proxy Pattern)
- P2P服务的代理和封装
- 通知消息的代理处理

### 3. 观察者模式 (Observer Pattern)
- 总线服务的消息订阅
- 事件驱动的通知处理

## 注意事项

1. **服务依赖**: 确保总线服务正确初始化
2. **通知处理**: 保证通知消息的可靠处理
3. **错误处理**: 完善的错误处理和恢复机制
4. **性能优化**: 避免通知处理的性能瓶颈

## 扩展建议

1. **连接池**: 实现P2P连接池管理
2. **负载均衡**: 支持多个P2P实例的负载均衡
3. **监控仪表板**: 添加P2P连接监控界面
4. **自动故障转移**: 实现自动故障检测和转移
5. **性能优化**: 优化通知处理和连接管理性能

该服务是视频通话系统中P2P通信的核心基础设施，提供了可靠的点对点连接管理。

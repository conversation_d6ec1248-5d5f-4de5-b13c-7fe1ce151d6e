# Chat Window Patch - 聊天窗口补丁

## 概述

`chat_window_patch.js` 实现了对 Odoo 讨论应用中聊天窗口组件的补丁扩展，为聊天窗口添加了通话组件集成功能。该补丁通过组件注册机制将Call组件添加到ChatWindow的组件列表中，使聊天窗口能够显示和管理视频通话界面，实现了聊天和通话功能的无缝集成，是视频通话系统中UI集成的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/chat_window_patch.js`
- **行数**: 14
- **模块**: `@mail/discuss/call/common/chat_window_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/chat_window'      // 聊天窗口组件
'@mail/discuss/call/common/call'     // 通话组件
```

## 补丁实现

### 组件集成

```javascript
Object.assign(ChatWindow.components, { Call });
```

**集成特性**:
- **组件注册**: 将Call组件注册到ChatWindow
- **无缝集成**: 聊天和通话功能统一界面
- **简洁实现**: 最小化代码实现最大功能

## 核心功能

### 1. 组件扩展

```javascript
Object.assign(ChatWindow.components, { Call });
```

**扩展功能**:
- **组件添加**: 向ChatWindow添加Call组件
- **功能增强**: 扩展聊天窗口的通话能力
- **保持兼容**: 不破坏原有聊天功能

## 使用场景

### 1. 聊天窗口通话集成

```javascript
// 聊天窗口通话集成增强
const ChatWindowCallIntegration = {
    enhanceChatWindow: () => {
        // 扩展聊天窗口的通话功能
        const EnhancedChatWindowPatch = {
            setup() {
                super.setup();
                
                // 添加通话状态管理
                this.callState = useState({
                    isCallActive: false,
                    isCallMinimized: false,
                    callType: null, // 'audio' | 'video'
                    participants: []
                });
                
                // 监听通话状态变化
                this.rtc = useState(useService("discuss.rtc"));
                
                // 添加通话控制方法
                this.startCall = this.startCall.bind(this);
                this.endCall = this.endCall.bind(this);
                this.toggleCallMinimize = this.toggleCallMinimize.bind(this);
            },
            
            // 开始通话
            async startCall(type = 'video') {
                try {
                    if (!this.props.thread) {
                        throw new Error('无法在没有线程的情况下开始通话');
                    }
                    
                    const rtcService = useService("discuss.rtc");
                    await rtcService.toggleCall(this.props.thread, { video: type === 'video' });
                    
                    this.callState.isCallActive = true;
                    this.callState.callType = type;
                    
                    // 通知其他组件
                    this.trigger('call_started', { type, thread: this.props.thread });
                } catch (error) {
                    console.error('开始通话失败:', error);
                    showNotification('开始通话失败', 'error');
                }
            },
            
            // 结束通话
            async endCall() {
                try {
                    const rtcService = useService("discuss.rtc");
                    await rtcService.leaveCall(this.props.thread);
                    
                    this.callState.isCallActive = false;
                    this.callState.callType = null;
                    this.callState.participants = [];
                    
                    // 通知其他组件
                    this.trigger('call_ended', { thread: this.props.thread });
                } catch (error) {
                    console.error('结束通话失败:', error);
                    showNotification('结束通话失败', 'error');
                }
            },
            
            // 切换通话最小化
            toggleCallMinimize() {
                this.callState.isCallMinimized = !this.callState.isCallMinimized;
                
                // 更新UI布局
                this.updateChatWindowLayout();
            },
            
            // 更新聊天窗口布局
            updateChatWindowLayout() {
                const chatWindow = this.el;
                if (!chatWindow) return;
                
                if (this.callState.isCallActive) {
                    chatWindow.classList.add('with-call');
                    
                    if (this.callState.isCallMinimized) {
                        chatWindow.classList.add('call-minimized');
                    } else {
                        chatWindow.classList.remove('call-minimized');
                    }
                } else {
                    chatWindow.classList.remove('with-call', 'call-minimized');
                }
            },
            
            // 获取通话状态
            get isInCall() {
                return this.callState.isCallActive;
            },
            
            // 获取通话类型
            get callType() {
                return this.callState.callType;
            },
            
            // 获取参与者数量
            get participantCount() {
                return this.callState.participants.length;
            }
        };
        
        // 应用补丁
        patch(ChatWindow.prototype, EnhancedChatWindowPatch);
    },
    
    // 添加通话控制按钮
    addCallControls: () => {
        const CallControlsPatch = {
            setup() {
                super.setup();
                
                // 在聊天窗口头部添加通话控制按钮
                onMounted(() => {
                    ChatWindowCallIntegration.injectCallControls(this);
                });
            }
        };
        
        patch(ChatWindow.prototype, CallControlsPatch);
    },
    
    injectCallControls: (chatWindow) => {
        const header = chatWindow.el.querySelector('.o-mail-ChatWindow-header');
        if (!header) return;
        
        // 创建通话控制容器
        const callControls = document.createElement('div');
        callControls.className = 'chat-window-call-controls';
        callControls.innerHTML = `
            <div class="call-control-buttons">
                <button class="call-btn audio-call-btn" title="语音通话" data-type="audio">
                    <i class="fa fa-phone"></i>
                </button>
                <button class="call-btn video-call-btn" title="视频通话" data-type="video">
                    <i class="fa fa-video-camera"></i>
                </button>
                <button class="call-btn end-call-btn" title="结束通话" style="display: none;">
                    <i class="fa fa-phone-slash"></i>
                </button>
                <button class="call-btn minimize-call-btn" title="最小化通话" style="display: none;">
                    <i class="fa fa-minus"></i>
                </button>
            </div>
            
            <div class="call-status-indicator" style="display: none;">
                <i class="fa fa-circle status-dot"></i>
                <span class="status-text">通话中</span>
                <span class="participant-count">(1)</span>
            </div>
        `;
        
        header.appendChild(callControls);
        
        // 绑定事件
        ChatWindowCallIntegration.bindCallControlEvents(callControls, chatWindow);
    },
    
    bindCallControlEvents: (controls, chatWindow) => {
        const audioCallBtn = controls.querySelector('.audio-call-btn');
        const videoCallBtn = controls.querySelector('.video-call-btn');
        const endCallBtn = controls.querySelector('.end-call-btn');
        const minimizeCallBtn = controls.querySelector('.minimize-call-btn');
        
        // 语音通话按钮
        audioCallBtn.addEventListener('click', async () => {
            await chatWindow.startCall('audio');
            ChatWindowCallIntegration.updateCallControlsVisibility(controls, true);
        });
        
        // 视频通话按钮
        videoCallBtn.addEventListener('click', async () => {
            await chatWindow.startCall('video');
            ChatWindowCallIntegration.updateCallControlsVisibility(controls, true);
        });
        
        // 结束通话按钮
        endCallBtn.addEventListener('click', async () => {
            await chatWindow.endCall();
            ChatWindowCallIntegration.updateCallControlsVisibility(controls, false);
        });
        
        // 最小化通话按钮
        minimizeCallBtn.addEventListener('click', () => {
            chatWindow.toggleCallMinimize();
        });
        
        // 监听通话状态变化
        chatWindow.addEventListener('call_started', () => {
            ChatWindowCallIntegration.updateCallControlsVisibility(controls, true);
        });
        
        chatWindow.addEventListener('call_ended', () => {
            ChatWindowCallIntegration.updateCallControlsVisibility(controls, false);
        });
    },
    
    updateCallControlsVisibility: (controls, isInCall) => {
        const startButtons = controls.querySelectorAll('.audio-call-btn, .video-call-btn');
        const endButtons = controls.querySelectorAll('.end-call-btn, .minimize-call-btn');
        const statusIndicator = controls.querySelector('.call-status-indicator');
        
        startButtons.forEach(btn => {
            btn.style.display = isInCall ? 'none' : 'inline-block';
        });
        
        endButtons.forEach(btn => {
            btn.style.display = isInCall ? 'inline-block' : 'none';
        });
        
        statusIndicator.style.display = isInCall ? 'flex' : 'none';
    },
    
    // 添加通话区域布局
    addCallArea: () => {
        const CallAreaPatch = {
            setup() {
                super.setup();
                
                onMounted(() => {
                    ChatWindowCallIntegration.injectCallArea(this);
                });
            }
        };
        
        patch(ChatWindow.prototype, CallAreaPatch);
    },
    
    injectCallArea: (chatWindow) => {
        const content = chatWindow.el.querySelector('.o-mail-ChatWindow-content');
        if (!content) return;
        
        // 创建通话区域
        const callArea = document.createElement('div');
        callArea.className = 'chat-window-call-area';
        callArea.style.display = 'none';
        callArea.innerHTML = `
            <div class="call-area-header">
                <div class="call-info">
                    <span class="call-type-indicator">
                        <i class="fa fa-video-camera"></i>
                        <span class="call-type-text">视频通话</span>
                    </span>
                    <span class="call-duration">00:00</span>
                </div>
                
                <div class="call-area-controls">
                    <button class="call-area-btn expand-btn" title="展开通话">
                        <i class="fa fa-expand"></i>
                    </button>
                    <button class="call-area-btn close-btn" title="关闭通话区域">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="call-area-content">
                <!-- Call组件将在这里渲染 -->
            </div>
        `;
        
        // 插入到内容区域顶部
        content.insertBefore(callArea, content.firstChild);
        
        // 绑定通话区域事件
        ChatWindowCallIntegration.bindCallAreaEvents(callArea, chatWindow);
    },
    
    bindCallAreaEvents: (callArea, chatWindow) => {
        const expandBtn = callArea.querySelector('.expand-btn');
        const closeBtn = callArea.querySelector('.close-btn');
        
        expandBtn.addEventListener('click', () => {
            chatWindow.callState.isCallMinimized = false;
            chatWindow.updateChatWindowLayout();
        });
        
        closeBtn.addEventListener('click', () => {
            callArea.style.display = 'none';
        });
        
        // 监听通话状态变化
        chatWindow.addEventListener('call_started', (event) => {
            callArea.style.display = 'block';
            ChatWindowCallIntegration.updateCallAreaInfo(callArea, event.detail);
        });
        
        chatWindow.addEventListener('call_ended', () => {
            callArea.style.display = 'none';
        });
    },
    
    updateCallAreaInfo: (callArea, callInfo) => {
        const typeIndicator = callArea.querySelector('.call-type-indicator i');
        const typeText = callArea.querySelector('.call-type-text');
        
        if (callInfo.type === 'video') {
            typeIndicator.className = 'fa fa-video-camera';
            typeText.textContent = '视频通话';
        } else {
            typeIndicator.className = 'fa fa-phone';
            typeText.textContent = '语音通话';
        }
    },
    
    // 添加通话持续时间计时器
    addCallTimer: () => {
        const CallTimerPatch = {
            setup() {
                super.setup();
                this.callTimer = null;
                this.callStartTime = null;
            },
            
            startCallTimer() {
                this.callStartTime = Date.now();
                this.callTimer = setInterval(() => {
                    this.updateCallDuration();
                }, 1000);
            },
            
            stopCallTimer() {
                if (this.callTimer) {
                    clearInterval(this.callTimer);
                    this.callTimer = null;
                    this.callStartTime = null;
                }
            },
            
            updateCallDuration() {
                if (!this.callStartTime) return;
                
                const duration = Date.now() - this.callStartTime;
                const minutes = Math.floor(duration / 60000);
                const seconds = Math.floor((duration % 60000) / 1000);
                
                const durationText = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                // 更新所有持续时间显示
                const durationElements = this.el.querySelectorAll('.call-duration');
                durationElements.forEach(element => {
                    element.textContent = durationText;
                });
            },
            
            destroy() {
                this.stopCallTimer();
                super.destroy?.();
            }
        };
        
        patch(ChatWindow.prototype, CallTimerPatch);
    }
};

// 应用所有增强功能
ChatWindowCallIntegration.enhanceChatWindow();
ChatWindowCallIntegration.addCallControls();
ChatWindowCallIntegration.addCallArea();
ChatWindowCallIntegration.addCallTimer();
```

### 2. 响应式布局适配

```javascript
// 响应式布局适配
const ResponsiveLayoutAdapter = {
    adaptChatWindowLayout: () => {
        const LayoutPatch = {
            setup() {
                super.setup();
                
                // 添加响应式状态
                this.layoutState = useState({
                    windowWidth: window.innerWidth,
                    windowHeight: window.innerHeight,
                    isCompact: false,
                    callPosition: 'top' // 'top' | 'side' | 'overlay'
                });
                
                // 监听窗口大小变化
                this.resizeObserver = new ResizeObserver(() => {
                    ResponsiveLayoutAdapter.updateLayout(this);
                });
                
                onMounted(() => {
                    this.resizeObserver.observe(this.el);
                    ResponsiveLayoutAdapter.updateLayout(this);
                });
                
                onWillUnmount(() => {
                    this.resizeObserver.disconnect();
                });
            },
            
            // 获取最佳通话布局
            getOptimalCallLayout() {
                const { windowWidth, windowHeight } = this.layoutState;
                
                if (windowWidth < 768) {
                    return 'overlay'; // 移动端使用覆盖模式
                } else if (windowWidth < 1200) {
                    return 'top'; // 中等屏幕使用顶部模式
                } else {
                    return 'side'; // 大屏幕使用侧边模式
                }
            },
            
            // 应用布局
            applyCallLayout(layout) {
                this.layoutState.callPosition = layout;
                
                const chatWindow = this.el;
                chatWindow.classList.remove('call-layout-top', 'call-layout-side', 'call-layout-overlay');
                chatWindow.classList.add(`call-layout-${layout}`);
                
                // 调整通话区域大小
                ResponsiveLayoutAdapter.adjustCallAreaSize(this, layout);
            }
        };
        
        patch(ChatWindow.prototype, LayoutPatch);
    },
    
    updateLayout: (chatWindow) => {
        const rect = chatWindow.el.getBoundingClientRect();
        chatWindow.layoutState.windowWidth = rect.width;
        chatWindow.layoutState.windowHeight = rect.height;
        chatWindow.layoutState.isCompact = rect.width < 600;
        
        // 应用最佳布局
        const optimalLayout = chatWindow.getOptimalCallLayout();
        chatWindow.applyCallLayout(optimalLayout);
    },
    
    adjustCallAreaSize: (chatWindow, layout) => {
        const callArea = chatWindow.el.querySelector('.chat-window-call-area');
        if (!callArea) return;
        
        switch (layout) {
            case 'top':
                callArea.style.height = '200px';
                callArea.style.width = '100%';
                break;
            case 'side':
                callArea.style.height = '100%';
                callArea.style.width = '300px';
                break;
            case 'overlay':
                callArea.style.height = '150px';
                callArea.style.width = '200px';
                callArea.style.position = 'absolute';
                callArea.style.top = '10px';
                callArea.style.right = '10px';
                break;
        }
    }
};

// 应用响应式布局
ResponsiveLayoutAdapter.adaptChatWindowLayout();
```

## 技术特点

### 1. 组件集成
- 简洁的组件注册机制
- 无缝的功能集成
- 保持原有组件结构

### 2. 最小化实现
- 仅一行核心代码
- 最大化功能扩展
- 零侵入式修改

### 3. 功能增强
- 聊天窗口通话能力
- 统一的用户界面
- 完整的功能体验

### 4. 兼容性保证
- 不破坏原有功能
- 向后兼容设计
- 渐进式增强

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组件的组合和集成
- 功能的统一管理

### 2. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 原有功能的保持

### 3. 适配器模式 (Adapter Pattern)
- 不同组件间的适配
- 接口的统一化

## 注意事项

1. **组件兼容性**: 确保Call组件与ChatWindow的兼容性
2. **性能影响**: 组件集成不应影响聊天窗口性能
3. **用户体验**: 保持一致的用户界面体验
4. **功能冲突**: 避免通话功能与聊天功能的冲突

## 扩展建议

1. **更多组件**: 集成更多通话相关组件
2. **布局优化**: 优化通话和聊天的布局切换
3. **状态管理**: 增强通话状态的管理和同步
4. **快捷操作**: 添加更多通话快捷操作
5. **自定义配置**: 支持用户自定义通话界面配置

该补丁实现了聊天和通话功能的完美集成，为用户提供了统一的通信体验。

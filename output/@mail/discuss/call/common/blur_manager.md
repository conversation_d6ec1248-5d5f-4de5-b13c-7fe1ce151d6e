# Blur Manager - 背景模糊管理器

## 概述

`blur_manager.js` 实现了 Odoo 讨论应用中视频通话的背景模糊功能管理器。该组件使用MediaPipe的SelfieSegmentation技术来实现实时的人像分割和背景模糊效果，支持可配置的模糊强度、边缘处理和模型选择，通过Canvas技术进行图像合成处理，为视频通话提供了专业的背景虚化功能，提升了用户的隐私保护和视觉体验。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/blur_manager.js`
- **行数**: 151
- **模块**: `@mail/discuss/call/common/blur_manager`

## 依赖关系

```javascript
// 工具依赖
'@mail/utils/common/misc'    // 通用工具函数
'@web/core/browser/browser'  // 浏览器服务
```

## 核心组件

### BlurManager 类

```javascript
class BlurManager {
    canvas = document.createElement("canvas");
    canvasBlur = document.createElement("canvas");
    canvasMask = document.createElement("canvas");
    canvasStream;
    isVideoDataLoaded = false;
    selfieSegmentation = new window.SelfieSegmentation({
        locateFile: (file) => {
            return `https://cdn.jsdelivr.net/npm/@mediapipe/selfie_segmentation@0.1/${file}`;
        },
    });
    video = document.createElement("video");
}
```

**组件特性**:
- 多Canvas架构用于图像处理
- MediaPipe SelfieSegmentation集成
- 实时视频流处理
- Promise-based异步处理

## 构造函数

### 初始化参数

```javascript
constructor(
    stream,
    { backgroundBlur = 10, edgeBlur = 10, modelSelection = 1, selfieMode = false } = {}
)
```

**参数说明**:
- **`stream`**: 输入的视频流
- **`backgroundBlur`**: 背景模糊强度（默认10px）
- **`edgeBlur`**: 边缘模糊强度（默认10px）
- **`modelSelection`**: 模型选择（0或1）
- **`selfieMode`**: 自拍模式开关

## 核心功能

### 1. 图像模糊处理

```javascript
function drawAndBlurImageOnCanvas(image, blurAmount, canvas) {
    canvas.width = image.width;
    canvas.height = image.height;
    if (blurAmount === 0) {
        canvas.getContext("2d").drawImage(image, 0, 0, image.width, image.height);
        return;
    }
    canvas.getContext("2d").clearRect(0, 0, image.width, image.height);
    canvas.getContext("2d").save();
    canvas.getContext("2d").filter = `blur(${blurAmount}px)`;
    canvas.getContext("2d").drawImage(image, 0, 0, image.width, image.height);
    canvas.getContext("2d").restore();
}
```

**模糊处理逻辑**:
- 动态调整Canvas尺寸
- 支持零模糊（直接绘制）
- 使用CSS filter实现模糊效果
- 保存和恢复Canvas状态

### 2. 视频流初始化

```javascript
constructor(stream, options) {
    this.edgeBlur = edgeBlur;
    this.backgroundBlur = backgroundBlur;
    this._onVideoPlay = this._onVideoPlay.bind(this);
    this.video.addEventListener("loadeddata", this._onVideoPlay);
    this.canvas.getContext("2d");
    this.canvasStream = this.canvas.captureStream();
    
    this.stream = new Promise((resolve, reject) => {
        this.rejectStreamPromise = reject;
        this.resolveStreamPromise = resolve;
    });
    
    this.video.srcObject = stream;
    this.video.load();
    this.selfieSegmentation.setOptions({
        selfieMode,
        modelSelection,
    });
    this.selfieSegmentation.onResults((r) => this._onSelfieSegmentationResults(r));
    this.video.autoplay = true;
    Promise.resolve(this.video.play()).catch(() => {});
}
```

**初始化流程**:
- 绑定事件处理器
- 创建Canvas流
- 设置Promise机制
- 配置SelfieSegmentation
- 启动视频播放

### 3. 帧处理循环

```javascript
async _onFrame() {
    if (!this.selfieSegmentation) {
        return;
    }
    if (!this.video) {
        return;
    }
    if (!this.isVideoDataLoaded) {
        return;
    }
    await this.selfieSegmentation.send({ image: this.video });
    browser.setTimeout(() => this._requestFrame(), Math.floor(1000 / 30)); // 30 fps
}
```

**帧处理逻辑**:
- 检查组件状态
- 发送视频帧到SelfieSegmentation
- 维持30fps的处理频率
- 异步处理确保性能

### 4. 分割结果处理

```javascript
_onSelfieSegmentationResults(results) {
    drawAndBlurImageOnCanvas(results.image, this.backgroundBlur, this.canvasBlur);
    this.canvas.width = this.canvasBlur.width;
    this.canvas.height = this.canvasBlur.height;
    drawAndBlurImageOnCanvas(results.segmentationMask, this.edgeBlur, this.canvasMask);
    this.canvas.getContext("2d").save();
    this.canvas
        .getContext("2d")
        .drawImage(results.image, 0, 0, this.canvas.width, this.canvas.height);
    this._drawWithCompositing(this.canvasMask, "destination-in");
    this._drawWithCompositing(this.canvasBlur, "destination-over");
    this.canvas.getContext("2d").restore();
}
```

**合成处理**:
- 生成模糊背景图像
- 处理分割遮罩
- 绘制原始图像
- 使用合成模式混合图层

### 5. 资源清理

```javascript
close() {
    this.video.removeEventListener("loadeddata", this._onVideoPlay);
    this.video.srcObject = null;
    this.isVideoDataLoaded = false;
    this.selfieSegmentation.reset();
    closeStream(this.canvasStream);
    this.canvasStream = null;
    if (this.rejectStreamPromise) {
        this.rejectStreamPromise(
            new Error("The source stream was removed before the beginning of the blur process")
        );
    }
}
```

**清理逻辑**:
- 移除事件监听器
- 重置视频源
- 重置分割模型
- 关闭Canvas流
- 拒绝未完成的Promise

## 使用场景

### 1. 视频通话背景模糊

```javascript
// 视频通话中的背景模糊应用
const VideoCallBlurIntegration = {
    setupBackgroundBlur: async (videoStream, options = {}) => {
        try {
            const blurManager = new BlurManager(videoStream, {
                backgroundBlur: options.backgroundBlur || 15,
                edgeBlur: options.edgeBlur || 5,
                modelSelection: options.modelSelection || 1,
                selfieMode: options.selfieMode || true
            });
            
            const blurredStream = await blurManager.stream;
            
            return {
                blurManager,
                blurredStream,
                originalStream: videoStream
            };
        } catch (error) {
            console.error('背景模糊设置失败:', error);
            throw error;
        }
    },
    
    toggleBlur: async (callState, enable) => {
        if (enable && !callState.blurManager) {
            const blurSetup = await VideoCallBlurIntegration.setupBackgroundBlur(
                callState.originalStream,
                callState.blurOptions
            );
            
            callState.blurManager = blurSetup.blurManager;
            callState.currentStream = blurSetup.blurredStream;
            
            // 更新视频元素
            VideoCallBlurIntegration.updateVideoElement(callState);
        } else if (!enable && callState.blurManager) {
            callState.blurManager.close();
            callState.blurManager = null;
            callState.currentStream = callState.originalStream;
            
            // 恢复原始视频
            VideoCallBlurIntegration.updateVideoElement(callState);
        }
    },
    
    updateVideoElement: (callState) => {
        const videoElement = document.querySelector('#local-video');
        if (videoElement) {
            videoElement.srcObject = callState.currentStream;
        }
        
        // 通知其他参与者
        VideoCallBlurIntegration.notifyStreamChange(callState);
    },
    
    notifyStreamChange: (callState) => {
        if (callState.peerConnection) {
            const sender = callState.peerConnection.getSenders().find(
                s => s.track && s.track.kind === 'video'
            );
            
            if (sender) {
                const videoTrack = callState.currentStream.getVideoTracks()[0];
                sender.replaceTrack(videoTrack);
            }
        }
    }
};
```

### 2. 背景模糊设置界面

```javascript
// 背景模糊设置界面
const BlurSettingsUI = {
    createSettingsPanel: () => {
        const panel = document.createElement('div');
        panel.className = 'blur-settings-panel';
        panel.innerHTML = `
            <div class="settings-header">
                <h4>背景模糊设置</h4>
                <button class="close-settings-btn">&times;</button>
            </div>
            
            <div class="settings-content">
                <div class="setting-group">
                    <label>启用背景模糊</label>
                    <input type="checkbox" id="enable-blur" />
                </div>
                
                <div class="setting-group">
                    <label>背景模糊强度: <span id="bg-blur-value">10</span>px</label>
                    <input type="range" id="background-blur" min="0" max="30" value="10" />
                </div>
                
                <div class="setting-group">
                    <label>边缘模糊强度: <span id="edge-blur-value">5</span>px</label>
                    <input type="range" id="edge-blur" min="0" max="15" value="5" />
                </div>
                
                <div class="setting-group">
                    <label>模型精度</label>
                    <select id="model-selection">
                        <option value="0">快速模式</option>
                        <option value="1" selected>精确模式</option>
                    </select>
                </div>
                
                <div class="setting-group">
                    <label>自拍模式</label>
                    <input type="checkbox" id="selfie-mode" checked />
                </div>
                
                <div class="settings-preview">
                    <video id="preview-video" autoplay muted></video>
                    <canvas id="preview-canvas"></canvas>
                </div>
            </div>
            
            <div class="settings-footer">
                <button class="btn btn-secondary cancel-btn">取消</button>
                <button class="btn btn-primary apply-btn">应用</button>
            </div>
        `;
        
        BlurSettingsUI.bindSettingsEvents(panel);
        return panel;
    },
    
    bindSettingsEvents: (panel) => {
        const enableBlur = panel.querySelector('#enable-blur');
        const backgroundBlur = panel.querySelector('#background-blur');
        const edgeBlur = panel.querySelector('#edge-blur');
        const modelSelection = panel.querySelector('#model-selection');
        const selfieMode = panel.querySelector('#selfie-mode');
        const previewVideo = panel.querySelector('#preview-video');
        const previewCanvas = panel.querySelector('#preview-canvas');
        
        let previewBlurManager = null;
        
        // 实时预览
        const updatePreview = async () => {
            if (!enableBlur.checked) {
                if (previewBlurManager) {
                    previewBlurManager.close();
                    previewBlurManager = null;
                }
                previewCanvas.style.display = 'none';
                previewVideo.style.display = 'block';
                return;
            }
            
            try {
                const stream = previewVideo.srcObject;
                if (!stream) return;
                
                if (previewBlurManager) {
                    previewBlurManager.close();
                }
                
                previewBlurManager = new BlurManager(stream, {
                    backgroundBlur: parseInt(backgroundBlur.value),
                    edgeBlur: parseInt(edgeBlur.value),
                    modelSelection: parseInt(modelSelection.value),
                    selfieMode: selfieMode.checked
                });
                
                const blurredStream = await previewBlurManager.stream;
                previewCanvas.srcObject = blurredStream;
                previewCanvas.style.display = 'block';
                previewVideo.style.display = 'none';
            } catch (error) {
                console.error('预览更新失败:', error);
            }
        };
        
        // 绑定事件
        enableBlur.addEventListener('change', updatePreview);
        backgroundBlur.addEventListener('input', (e) => {
            panel.querySelector('#bg-blur-value').textContent = e.target.value;
            updatePreview();
        });
        edgeBlur.addEventListener('input', (e) => {
            panel.querySelector('#edge-blur-value').textContent = e.target.value;
            updatePreview();
        });
        modelSelection.addEventListener('change', updatePreview);
        selfieMode.addEventListener('change', updatePreview);
        
        // 应用设置
        panel.querySelector('.apply-btn').addEventListener('click', () => {
            const settings = {
                enabled: enableBlur.checked,
                backgroundBlur: parseInt(backgroundBlur.value),
                edgeBlur: parseInt(edgeBlur.value),
                modelSelection: parseInt(modelSelection.value),
                selfieMode: selfieMode.checked
            };
            
            BlurSettingsUI.applySettings(settings);
            panel.remove();
        });
        
        // 取消设置
        panel.querySelector('.cancel-btn').addEventListener('click', () => {
            if (previewBlurManager) {
                previewBlurManager.close();
            }
            panel.remove();
        });
        
        // 初始化预览
        BlurSettingsUI.initializePreview(previewVideo, updatePreview);
    },
    
    initializePreview: async (videoElement, updateCallback) => {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
                video: { width: 320, height: 240 } 
            });
            videoElement.srcObject = stream;
            updateCallback();
        } catch (error) {
            console.error('获取摄像头失败:', error);
        }
    },
    
    applySettings: (settings) => {
        // 保存设置到本地存储
        localStorage.setItem('blur_settings', JSON.stringify(settings));
        
        // 应用到当前通话
        const currentCall = getCurrentCall();
        if (currentCall) {
            VideoCallBlurIntegration.toggleBlur(currentCall, settings.enabled);
        }
        
        showNotification('背景模糊设置已应用', 'success');
    }
};
```

### 3. 性能监控和优化

```javascript
// 性能监控和优化
const BlurPerformanceMonitor = {
    metrics: {
        frameProcessingTime: [],
        memoryUsage: [],
        cpuUsage: [],
        frameDrops: 0,
        totalFrames: 0
    },
    
    startMonitoring: (blurManager) => {
        BlurPerformanceMonitor.monitorFrameProcessing(blurManager);
        BlurPerformanceMonitor.monitorMemoryUsage();
        BlurPerformanceMonitor.monitorCPUUsage();
    },
    
    monitorFrameProcessing: (blurManager) => {
        const originalOnFrame = blurManager._onFrame.bind(blurManager);
        
        blurManager._onFrame = async function() {
            const startTime = performance.now();
            
            try {
                await originalOnFrame();
                BlurPerformanceMonitor.metrics.totalFrames++;
            } catch (error) {
                BlurPerformanceMonitor.metrics.frameDrops++;
                console.error('帧处理失败:', error);
            }
            
            const processingTime = performance.now() - startTime;
            BlurPerformanceMonitor.metrics.frameProcessingTime.push(processingTime);
            
            // 保持最近100帧的数据
            if (BlurPerformanceMonitor.metrics.frameProcessingTime.length > 100) {
                BlurPerformanceMonitor.metrics.frameProcessingTime.shift();
            }
            
            // 检查性能问题
            BlurPerformanceMonitor.checkPerformanceIssues();
        };
    },
    
    monitorMemoryUsage: () => {
        if (performance.memory) {
            setInterval(() => {
                const memoryInfo = {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit,
                    timestamp: Date.now()
                };
                
                BlurPerformanceMonitor.metrics.memoryUsage.push(memoryInfo);
                
                // 保持最近50个数据点
                if (BlurPerformanceMonitor.metrics.memoryUsage.length > 50) {
                    BlurPerformanceMonitor.metrics.memoryUsage.shift();
                }
            }, 5000); // 每5秒检查一次
        }
    },
    
    checkPerformanceIssues: () => {
        const recentFrameTimes = BlurPerformanceMonitor.metrics.frameProcessingTime.slice(-10);
        const avgFrameTime = recentFrameTimes.reduce((a, b) => a + b, 0) / recentFrameTimes.length;
        
        // 如果平均帧处理时间超过33ms（30fps），发出警告
        if (avgFrameTime > 33) {
            console.warn('背景模糊性能警告: 帧处理时间过长', avgFrameTime);
            BlurPerformanceMonitor.suggestOptimizations();
        }
        
        // 检查帧丢失率
        const frameDropRate = BlurPerformanceMonitor.metrics.frameDrops / BlurPerformanceMonitor.metrics.totalFrames;
        if (frameDropRate > 0.1) { // 超过10%的帧丢失
            console.warn('背景模糊性能警告: 帧丢失率过高', frameDropRate);
        }
    },
    
    suggestOptimizations: () => {
        const suggestions = [
            '降低背景模糊强度',
            '使用快速模式（modelSelection: 0）',
            '降低视频分辨率',
            '关闭其他占用CPU的应用程序'
        ];
        
        showNotification('性能优化建议: ' + suggestions.join(', '), 'warning');
    },
    
    getPerformanceReport: () => {
        const frameProcessingTime = BlurPerformanceMonitor.metrics.frameProcessingTime;
        const memoryUsage = BlurPerformanceMonitor.metrics.memoryUsage;
        
        return {
            frameProcessing: {
                average: frameProcessingTime.reduce((a, b) => a + b, 0) / frameProcessingTime.length,
                max: Math.max(...frameProcessingTime),
                min: Math.min(...frameProcessingTime)
            },
            frameDropRate: BlurPerformanceMonitor.metrics.frameDrops / BlurPerformanceMonitor.metrics.totalFrames,
            memoryUsage: memoryUsage.length > 0 ? memoryUsage[memoryUsage.length - 1] : null,
            totalFrames: BlurPerformanceMonitor.metrics.totalFrames,
            frameDrops: BlurPerformanceMonitor.metrics.frameDrops
        };
    }
};
```

## 技术特点

### 1. MediaPipe集成
- 使用Google MediaPipe的SelfieSegmentation
- CDN加载模型文件
- 支持不同精度模式

### 2. Canvas图像处理
- 多Canvas架构
- 图像合成技术
- 实时滤镜效果

### 3. 性能优化
- 30fps处理频率
- 异步帧处理
- 资源管理和清理

### 4. 浏览器兼容性
- Safari滤镜兼容性问题处理
- Firefox Canvas流支持
- 跨浏览器API适配

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- Canvas元素的创建和管理
- 统一的资源创建接口

### 2. 观察者模式 (Observer Pattern)
- 视频事件监听
- 分割结果回调处理

### 3. 策略模式 (Strategy Pattern)
- 不同模糊强度的处理策略
- 可配置的处理参数

## 注意事项

1. **性能影响**: 实时图像处理对CPU有较高要求
2. **浏览器兼容**: Safari的滤镜支持问题
3. **网络依赖**: MediaPipe模型需要网络加载
4. **内存管理**: 及时清理Canvas和视频资源

## 扩展建议

1. **虚拟背景**: 支持自定义背景图片
2. **美颜滤镜**: 集成美颜和滤镜效果
3. **手势识别**: 添加手势控制功能
4. **AI增强**: 更智能的人像识别和处理
5. **性能自适应**: 根据设备性能自动调整参数

该组件为视频通话提供了专业级的背景模糊功能，是现代视频会议系统的重要组成部分。

# Call Menu - 通话菜单

## 概述

`call_menu.js` 实现了 Odoo 讨论应用中的通话菜单组件，作为系统托盘中的通话快捷入口。该组件集成了通话操作注册表和RTC服务，支持动态图标显示、快速通话操作、状态指示等功能，提供了便捷的通话访问界面，是视频通话系统中的重要系统级入口组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_menu.js`
- **行数**: 33
- **模块**: `@mail/discuss/call/common/call_menu`

## 依赖关系

```javascript
// 通话操作依赖
'@mail/discuss/call/common/call_actions'  // 通话操作注册表

// 核心依赖
'@odoo/owl'                               // OWL 框架
'@web/core/registry'                      // 注册表系统
'@web/core/utils/hooks'                   // Web 核心钩子
```

## 组件定义

### CallMenu 类

```javascript
class CallMenu extends Component {
    static props = [];
    static template = "discuss.CallMenu";
}
```

**组件特性**:
- 无需外部属性
- 使用自定义模板
- 系统托盘组件

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.rtc = useState(useService("discuss.rtc"));
    this.callActions = useCallActions();
}
```

**初始化内容**:
- RTC服务状态管理
- 通话操作钩子集成
- 响应式状态绑定

## 核心功能

### 1. 动态图标显示

```javascript
get icon() {
    return (
        callActionsRegistry.get(this.rtc.lastSelfCallAction, undefined)?.icon ?? "fa-microphone"
    );
}
```

**图标功能**:
- **动态图标**: 根据最后操作显示对应图标
- **默认图标**: 使用麦克风图标作为默认
- **状态反映**: 图标反映当前通话状态

## 系统托盘注册

### 托盘组件注册

```javascript
registry.category("systray").add("discuss.CallMenu", { Component: CallMenu }, { sequence: 100 });
```

**注册特性**:
- 注册到系统托盘
- 序列号100的显示顺序
- 全局可访问

## 使用场景

### 1. 系统托盘菜单增强

```javascript
// 系统托盘菜单增强
const SystrayMenuEnhancer = {
    enhanceCallMenu: (callMenu) => {
        // 添加快速操作
        SystrayMenuEnhancer.addQuickActions(callMenu);
        
        // 添加状态指示器
        SystrayMenuEnhancer.addStatusIndicator(callMenu);
        
        // 添加通知计数
        SystrayMenuEnhancer.addNotificationBadge(callMenu);
        
        // 添加下拉菜单
        SystrayMenuEnhancer.addDropdownMenu(callMenu);
        
        // 添加键盘快捷键
        SystrayMenuEnhancer.addKeyboardShortcuts(callMenu);
    },
    
    addQuickActions: (callMenu) => {
        const quickActionsContainer = document.createElement('div');
        quickActionsContainer.className = 'call-quick-actions';
        quickActionsContainer.innerHTML = `
            <div class="quick-actions-panel">
                <div class="action-buttons">
                    <button class="quick-action-btn start-call-btn" title="开始通话">
                        <i class="fa fa-phone"></i>
                    </button>
                    <button class="quick-action-btn start-video-btn" title="开始视频通话">
                        <i class="fa fa-video-camera"></i>
                    </button>
                    <button class="quick-action-btn join-call-btn" title="加入通话">
                        <i class="fa fa-sign-in"></i>
                    </button>
                    <button class="quick-action-btn settings-btn" title="通话设置">
                        <i class="fa fa-cog"></i>
                    </button>
                </div>
                
                <div class="recent-calls">
                    <h5>最近通话</h5>
                    <div class="recent-calls-list">
                        <!-- 最近通话列表 -->
                    </div>
                </div>
            </div>
        `;
        
        callMenu.el.appendChild(quickActionsContainer);
        
        // 绑定快速操作事件
        SystrayMenuEnhancer.bindQuickActionEvents(quickActionsContainer, callMenu);
    },
    
    bindQuickActionEvents: (container, callMenu) => {
        const startCallBtn = container.querySelector('.start-call-btn');
        const startVideoBtn = container.querySelector('.start-video-btn');
        const joinCallBtn = container.querySelector('.join-call-btn');
        const settingsBtn = container.querySelector('.settings-btn');
        
        startCallBtn.addEventListener('click', () => {
            SystrayMenuEnhancer.showContactSelector(callMenu, 'audio');
        });
        
        startVideoBtn.addEventListener('click', () => {
            SystrayMenuEnhancer.showContactSelector(callMenu, 'video');
        });
        
        joinCallBtn.addEventListener('click', () => {
            SystrayMenuEnhancer.showJoinCallDialog(callMenu);
        });
        
        settingsBtn.addEventListener('click', () => {
            SystrayMenuEnhancer.openCallSettings(callMenu);
        });
    },
    
    showContactSelector: (callMenu, callType) => {
        const selector = document.createElement('div');
        selector.className = 'contact-selector-overlay';
        selector.innerHTML = `
            <div class="contact-selector">
                <div class="selector-header">
                    <h3>选择联系人 - ${callType === 'video' ? '视频通话' : '语音通话'}</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="selector-content">
                    <div class="search-box">
                        <input type="text" class="contact-search" placeholder="搜索联系人...">
                        <i class="fa fa-search"></i>
                    </div>
                    
                    <div class="contact-list">
                        <!-- 联系人列表 -->
                    </div>
                    
                    <div class="group-options">
                        <button class="create-group-call-btn">创建群组通话</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(selector);
        
        // 加载联系人列表
        SystrayMenuEnhancer.loadContacts(selector, callType);
        
        // 绑定选择器事件
        SystrayMenuEnhancer.bindContactSelectorEvents(selector, callMenu, callType);
    },
    
    loadContacts: async (selector, callType) => {
        try {
            // 模拟加载联系人
            const contacts = await SystrayMenuEnhancer.fetchContacts();
            const contactList = selector.querySelector('.contact-list');
            
            contactList.innerHTML = contacts.map(contact => `
                <div class="contact-item" data-contact-id="${contact.id}">
                    <img src="${contact.avatar}" alt="${contact.name}" class="contact-avatar">
                    <div class="contact-info">
                        <span class="contact-name">${contact.name}</span>
                        <span class="contact-status ${contact.status}">${contact.statusText}</span>
                    </div>
                    <div class="contact-actions">
                        <button class="call-contact-btn" data-type="${callType}">
                            <i class="fa fa-${callType === 'video' ? 'video-camera' : 'phone'}"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        } catch (error) {
            console.error('加载联系人失败:', error);
        }
    },
    
    fetchContacts: async () => {
        // 模拟联系人数据
        return [
            { id: 1, name: '张三', avatar: '/web/image/res.partner/1/avatar_128', status: 'online', statusText: '在线' },
            { id: 2, name: '李四', avatar: '/web/image/res.partner/2/avatar_128', status: 'busy', statusText: '忙碌' },
            { id: 3, name: '王五', avatar: '/web/image/res.partner/3/avatar_128', status: 'away', statusText: '离开' }
        ];
    },
    
    bindContactSelectorEvents: (selector, callMenu, callType) => {
        const closeBtn = selector.querySelector('.close-btn');
        const searchInput = selector.querySelector('.contact-search');
        const contactList = selector.querySelector('.contact-list');
        const createGroupBtn = selector.querySelector('.create-group-call-btn');
        
        closeBtn.addEventListener('click', () => {
            selector.remove();
        });
        
        searchInput.addEventListener('input', (event) => {
            SystrayMenuEnhancer.filterContacts(contactList, event.target.value);
        });
        
        contactList.addEventListener('click', (event) => {
            if (event.target.classList.contains('call-contact-btn')) {
                const contactId = event.target.closest('.contact-item').dataset.contactId;
                SystrayMenuEnhancer.initiateCall(callMenu, contactId, callType);
                selector.remove();
            }
        });
        
        createGroupBtn.addEventListener('click', () => {
            SystrayMenuEnhancer.showGroupCallCreator(callMenu, callType);
            selector.remove();
        });
    },
    
    filterContacts: (contactList, searchTerm) => {
        const contacts = contactList.querySelectorAll('.contact-item');
        
        contacts.forEach(contact => {
            const name = contact.querySelector('.contact-name').textContent.toLowerCase();
            const matches = name.includes(searchTerm.toLowerCase());
            contact.style.display = matches ? 'flex' : 'none';
        });
    },
    
    initiateCall: async (callMenu, contactId, callType) => {
        try {
            // 发起通话
            await callMenu.rtc.startCall(contactId, { video: callType === 'video' });
            showNotification('正在发起通话...', 'info');
        } catch (error) {
            console.error('发起通话失败:', error);
            showNotification('发起通话失败', 'error');
        }
    },
    
    addStatusIndicator: (callMenu) => {
        const statusIndicator = document.createElement('div');
        statusIndicator.className = 'call-status-indicator';
        
        callMenu.el.appendChild(statusIndicator);
        
        // 更新状态指示器
        const updateStatus = () => {
            const rtcState = callMenu.rtc.state;
            let statusClass = 'idle';
            let statusText = '空闲';
            
            if (rtcState.channel) {
                statusClass = 'in-call';
                statusText = '通话中';
            } else if (rtcState.hasPendingRequest) {
                statusClass = 'connecting';
                statusText = '连接中';
            }
            
            statusIndicator.className = `call-status-indicator ${statusClass}`;
            statusIndicator.title = `通话状态: ${statusText}`;
        };
        
        // 定期更新状态
        setInterval(updateStatus, 1000);
        updateStatus();
    },
    
    addNotificationBadge: (callMenu) => {
        const badge = document.createElement('div');
        badge.className = 'notification-badge';
        badge.style.display = 'none';
        
        callMenu.el.appendChild(badge);
        
        // 更新通知计数
        const updateBadge = () => {
            const invitationCount = document.querySelectorAll('.call-invitation').length;
            
            if (invitationCount > 0) {
                badge.textContent = invitationCount > 99 ? '99+' : invitationCount.toString();
                badge.style.display = 'block';
            } else {
                badge.style.display = 'none';
            }
        };
        
        // 监听邀请变化
        const observer = new MutationObserver(updateBadge);
        observer.observe(document.body, { childList: true, subtree: true });
        
        updateBadge();
    },
    
    addDropdownMenu: (callMenu) => {
        const dropdown = document.createElement('div');
        dropdown.className = 'call-menu-dropdown';
        dropdown.innerHTML = `
            <div class="dropdown-content">
                <div class="menu-section">
                    <h5>通话操作</h5>
                    <div class="menu-items">
                        <div class="menu-item" data-action="start-audio">
                            <i class="fa fa-phone"></i>
                            <span>语音通话</span>
                        </div>
                        <div class="menu-item" data-action="start-video">
                            <i class="fa fa-video-camera"></i>
                            <span>视频通话</span>
                        </div>
                        <div class="menu-item" data-action="join-call">
                            <i class="fa fa-sign-in"></i>
                            <span>加入通话</span>
                        </div>
                    </div>
                </div>
                
                <div class="menu-section">
                    <h5>设置</h5>
                    <div class="menu-items">
                        <div class="menu-item" data-action="call-settings">
                            <i class="fa fa-cog"></i>
                            <span>通话设置</span>
                        </div>
                        <div class="menu-item" data-action="call-history">
                            <i class="fa fa-history"></i>
                            <span>通话记录</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        callMenu.el.appendChild(dropdown);
        
        // 绑定下拉菜单事件
        SystrayMenuEnhancer.bindDropdownEvents(dropdown, callMenu);
    },
    
    bindDropdownEvents: (dropdown, callMenu) => {
        const menuItems = dropdown.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            item.addEventListener('click', () => {
                const action = item.dataset.action;
                SystrayMenuEnhancer.executeMenuAction(callMenu, action);
            });
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!callMenu.el.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
        
        // 菜单切换
        callMenu.el.addEventListener('click', (event) => {
            event.stopPropagation();
            dropdown.classList.toggle('show');
        });
    },
    
    executeMenuAction: (callMenu, action) => {
        switch (action) {
            case 'start-audio':
                SystrayMenuEnhancer.showContactSelector(callMenu, 'audio');
                break;
            case 'start-video':
                SystrayMenuEnhancer.showContactSelector(callMenu, 'video');
                break;
            case 'join-call':
                SystrayMenuEnhancer.showJoinCallDialog(callMenu);
                break;
            case 'call-settings':
                SystrayMenuEnhancer.openCallSettings(callMenu);
                break;
            case 'call-history':
                SystrayMenuEnhancer.openCallHistory(callMenu);
                break;
        }
    },
    
    showJoinCallDialog: (callMenu) => {
        const dialog = document.createElement('div');
        dialog.className = 'join-call-dialog-overlay';
        dialog.innerHTML = `
            <div class="join-call-dialog">
                <div class="dialog-header">
                    <h3>加入通话</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="input-group">
                        <label>通话ID或链接:</label>
                        <input type="text" class="call-id-input" placeholder="输入通话ID或粘贴邀请链接">
                    </div>
                    
                    <div class="join-options">
                        <label class="option">
                            <input type="checkbox" class="join-with-video">
                            <span>开启视频</span>
                        </label>
                        <label class="option">
                            <input type="checkbox" class="join-with-audio" checked>
                            <span>开启音频</span>
                        </label>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary cancel-btn">取消</button>
                    <button class="btn btn-primary join-btn">加入</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // 绑定加入对话框事件
        SystrayMenuEnhancer.bindJoinDialogEvents(dialog, callMenu);
    },
    
    bindJoinDialogEvents: (dialog, callMenu) => {
        const closeBtn = dialog.querySelector('.close-btn');
        const cancelBtn = dialog.querySelector('.cancel-btn');
        const joinBtn = dialog.querySelector('.join-btn');
        const callIdInput = dialog.querySelector('.call-id-input');
        
        const closeDialog = () => {
            dialog.remove();
        };
        
        closeBtn.addEventListener('click', closeDialog);
        cancelBtn.addEventListener('click', closeDialog);
        
        joinBtn.addEventListener('click', async () => {
            const callId = callIdInput.value.trim();
            if (callId) {
                try {
                    await callMenu.rtc.joinCall(callId);
                    showNotification('正在加入通话...', 'info');
                    closeDialog();
                } catch (error) {
                    console.error('加入通话失败:', error);
                    showNotification('加入通话失败', 'error');
                }
            }
        });
    },
    
    openCallSettings: (callMenu) => {
        // 打开通话设置页面
        window.open('/web#action=mail.action_discuss_call_settings', '_blank');
    },
    
    openCallHistory: (callMenu) => {
        // 打开通话历史页面
        window.open('/web#action=mail.action_discuss_call_history', '_blank');
    },
    
    addKeyboardShortcuts: (callMenu) => {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+C: 开始语音通话
            if (event.ctrlKey && event.shiftKey && event.key === 'C') {
                event.preventDefault();
                SystrayMenuEnhancer.showContactSelector(callMenu, 'audio');
            }
            
            // Ctrl+Shift+V: 开始视频通话
            if (event.ctrlKey && event.shiftKey && event.key === 'V') {
                event.preventDefault();
                SystrayMenuEnhancer.showContactSelector(callMenu, 'video');
            }
            
            // Ctrl+Shift+J: 加入通话
            if (event.ctrlKey && event.shiftKey && event.key === 'J') {
                event.preventDefault();
                SystrayMenuEnhancer.showJoinCallDialog(callMenu);
            }
        });
    }
};
```

## 技术特点

### 1. 系统集成
- 系统托盘组件
- 全局访问入口
- 标准注册模式

### 2. 动态界面
- 响应式图标显示
- 状态驱动更新
- 实时状态反映

### 3. 操作集成
- 通话操作钩子
- 注册表集成
- 服务状态管理

### 4. 用户体验
- 快速访问入口
- 直观的状态显示
- 便捷的操作界面

## 设计模式

### 1. 门面模式 (Facade Pattern)
- 简化的通话操作入口
- 统一的访问接口

### 2. 观察者模式 (Observer Pattern)
- RTC状态监听
- 响应式界面更新

### 3. 策略模式 (Strategy Pattern)
- 不同操作的不同图标策略
- 状态显示策略

## 注意事项

1. **性能优化**: 避免频繁的状态检查和更新
2. **用户体验**: 提供清晰的状态指示和操作反馈
3. **系统集成**: 确保与系统托盘的正确集成
4. **状态同步**: 保持菜单状态与RTC状态一致

## 扩展建议

1. **快捷操作**: 添加更多快捷通话操作
2. **状态指示**: 增强状态显示和通知
3. **历史记录**: 集成通话历史快速访问
4. **个性化**: 支持用户自定义菜单选项
5. **键盘快捷键**: 添加键盘快捷键支持

该组件是视频通话系统的重要系统级入口，提供了便捷的通话访问和管理功能。

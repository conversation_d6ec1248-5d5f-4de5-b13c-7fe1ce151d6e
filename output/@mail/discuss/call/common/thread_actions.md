# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 讨论应用中线程级别的通话操作注册，为线程界面添加了通话相关的操作按钮。该模块通过线程操作注册表机制注册了"开始通话"和"通话设置"两个操作，支持条件判断、图标显示、组件集成、序列排序等功能，提供了完整的线程级别通话操作入口，是视频通话系统中用户界面集成的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/thread_actions.js`
- **行数**: 60
- **模块**: `@mail/discuss/call/common/thread_actions`

## 依赖关系

```javascript
// 操作注册依赖
'@mail/core/common/thread_actions'        // 线程操作注册表
'@mail/discuss/call/common/call_settings' // 通话设置组件

// 核心依赖
'@odoo/owl'                               // OWL 框架
'@web/core/l10n/translation'              // 国际化
'@web/core/utils/hooks'                   // Web 核心钩子
```

## 操作注册

### 1. 通话操作 (call)

```javascript
threadActionsRegistry.add("call", {
    condition(component) {
        return (
            component.thread?.allowCalls && !component.thread?.eq(component.rtc.state.channel)
        );
    },
    icon: "fa fa-fw fa-phone",
    iconLarge: "fa fa-fw fa-lg fa-phone",
    name: _t("Start a Call"),
    open(component) {
        component.rtc.toggleCall(component.thread);
    },
    sequence: 10,
    sequenceQuick: 30,
    setup() {
        const component = useComponent();
        component.rtc = useState(useService("discuss.rtc"));
    },
});
```

**操作特性**:
- **条件判断**: 线程允许通话且不是当前通话线程
- **图标显示**: 电话图标，支持大小图标
- **操作执行**: 调用RTC服务切换通话状态
- **序列排序**: 主序列10，快速序列30
- **服务注入**: 注入RTC服务状态

### 2. 设置操作 (settings)

```javascript
threadActionsRegistry.add("settings", {
    component: CallSettings,
    componentProps(action) {
        return { isCompact: true };
    },
    condition(component) {
        return (
            component.thread?.allowCalls &&
            (component.props.chatWindow?.isOpen || component.store.inPublicPage)
        );
    },
    icon: "fa fa-fw fa-gear",
    iconLarge: "fa fa-fw fa-lg fa-gear",
    name: _t("Call Settings"),
    sequence: 20,
    sequenceGroup: 30,
    setup() {
        const component = useComponent();
        component.rtc = useState(useService("discuss.rtc"));
    },
    toggle: true,
});
```

**操作特性**:
- **组件集成**: 使用CallSettings组件
- **组件属性**: 紧凑模式显示
- **条件判断**: 线程允许通话且在聊天窗口或公共页面
- **图标显示**: 齿轮图标，支持大小图标
- **切换模式**: 支持切换显示/隐藏
- **序列排序**: 主序列20，分组序列30

## 使用场景

### 1. 线程操作增强

```javascript
// 线程操作增强
const ThreadActionsEnhancer = {
    enhanceThreadActions: () => {
        // 添加更多通话相关操作
        threadActionsRegistry
            .add("video_call", {
                condition(component) {
                    return (
                        component.thread?.allowCalls && 
                        !component.thread?.eq(component.rtc.state.channel) &&
                        component.thread?.supportVideo !== false
                    );
                },
                icon: "fa fa-fw fa-video-camera",
                iconLarge: "fa fa-fw fa-lg fa-video-camera",
                name: _t("Start Video Call"),
                open(component) {
                    component.rtc.toggleCall(component.thread, { video: true });
                },
                sequence: 11,
                sequenceQuick: 31,
                setup() {
                    const component = useComponent();
                    component.rtc = useState(useService("discuss.rtc"));
                },
            })
            .add("screen_share", {
                condition(component) {
                    return (
                        component.thread?.allowCalls && 
                        component.thread?.eq(component.rtc.state.channel) &&
                        component.rtc.selfSession
                    );
                },
                icon: "fa fa-fw fa-desktop",
                iconLarge: "fa fa-fw fa-lg fa-desktop",
                name: _t("Share Screen"),
                open(component) {
                    component.rtc.toggleVideo("screen");
                },
                sequence: 12,
                sequenceQuick: 32,
                setup() {
                    const component = useComponent();
                    component.rtc = useState(useService("discuss.rtc"));
                },
            })
            .add("call_history", {
                condition(component) {
                    return (
                        component.thread?.allowCalls &&
                        component.thread?.callHistory?.length > 0
                    );
                },
                icon: "fa fa-fw fa-history",
                iconLarge: "fa fa-fw fa-lg fa-history",
                name: _t("Call History"),
                open(component) {
                    ThreadActionsEnhancer.showCallHistory(component);
                },
                sequence: 25,
                sequenceGroup: 35,
                setup() {
                    const component = useComponent();
                    component.store = useState(useService("mail.store"));
                },
            })
            .add("call_invite", {
                condition(component) {
                    return (
                        component.thread?.allowCalls &&
                        component.thread?.channel_type === 'channel' &&
                        component.thread?.memberCount > 2
                    );
                },
                icon: "fa fa-fw fa-user-plus",
                iconLarge: "fa fa-fw fa-lg fa-user-plus",
                name: _t("Invite to Call"),
                open(component) {
                    ThreadActionsEnhancer.showInviteDialog(component);
                },
                sequence: 13,
                sequenceQuick: 33,
                setup() {
                    const component = useComponent();
                    component.store = useState(useService("mail.store"));
                },
            });
    },
    
    // 显示通话历史
    showCallHistory: (component) => {
        const dialog = document.createElement('div');
        dialog.className = 'call-history-dialog-overlay';
        dialog.innerHTML = `
            <div class="call-history-dialog">
                <div class="dialog-header">
                    <h3>通话历史</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="history-list">
                        ${ThreadActionsEnhancer.renderCallHistory(component.thread.callHistory)}
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary close-dialog-btn">关闭</button>
                    <button class="btn btn-primary clear-history-btn">清除历史</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // 绑定事件
        ThreadActionsEnhancer.bindHistoryDialogEvents(dialog, component);
    },
    
    // 渲染通话历史
    renderCallHistory: (history) => {
        return history.map(call => `
            <div class="history-item">
                <div class="call-info">
                    <div class="call-type">
                        <i class="fa fa-${call.type === 'video' ? 'video-camera' : 'phone'}"></i>
                        <span>${call.type === 'video' ? '视频通话' : '语音通话'}</span>
                    </div>
                    <div class="call-duration">${ThreadActionsEnhancer.formatDuration(call.duration)}</div>
                </div>
                
                <div class="call-participants">
                    <span class="participant-count">${call.participants.length} 人参与</span>
                    <div class="participant-list">
                        ${call.participants.map(p => `<span class="participant">${p.name}</span>`).join(', ')}
                    </div>
                </div>
                
                <div class="call-time">
                    <span>${new Date(call.startTime).toLocaleString()}</span>
                </div>
                
                <div class="call-actions">
                    <button class="btn btn-sm btn-secondary call-again-btn" data-call-id="${call.id}">
                        <i class="fa fa-phone"></i>
                        <span>再次通话</span>
                    </button>
                </div>
            </div>
        `).join('');
    },
    
    // 格式化时长
    formatDuration: (seconds) => {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    },
    
    // 绑定历史对话框事件
    bindHistoryDialogEvents: (dialog, component) => {
        const closeBtn = dialog.querySelector('.close-btn');
        const closeDialogBtn = dialog.querySelector('.close-dialog-btn');
        const clearHistoryBtn = dialog.querySelector('.clear-history-btn');
        const callAgainBtns = dialog.querySelectorAll('.call-again-btn');
        
        const closeDialog = () => {
            dialog.remove();
        };
        
        closeBtn.addEventListener('click', closeDialog);
        closeDialogBtn.addEventListener('click', closeDialog);
        
        clearHistoryBtn.addEventListener('click', () => {
            if (confirm('确定要清除所有通话历史吗？')) {
                component.thread.callHistory = [];
                closeDialog();
            }
        });
        
        callAgainBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const callId = btn.dataset.callId;
                ThreadActionsEnhancer.initiateCallAgain(component, callId);
                closeDialog();
            });
        });
    },
    
    // 再次发起通话
    initiateCallAgain: (component, callId) => {
        const call = component.thread.callHistory.find(c => c.id === callId);
        if (call) {
            const options = {
                video: call.type === 'video',
                participants: call.participants.map(p => p.id)
            };
            component.rtc.toggleCall(component.thread, options);
        }
    },
    
    // 显示邀请对话框
    showInviteDialog: (component) => {
        const dialog = document.createElement('div');
        dialog.className = 'invite-dialog-overlay';
        dialog.innerHTML = `
            <div class="invite-dialog">
                <div class="dialog-header">
                    <h3>邀请加入通话</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="member-list">
                        ${ThreadActionsEnhancer.renderMemberList(component.thread.channelMembers)}
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary cancel-btn">取消</button>
                    <button class="btn btn-primary invite-btn">发送邀请</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(dialog);
        
        // 绑定事件
        ThreadActionsEnhancer.bindInviteDialogEvents(dialog, component);
    },
    
    // 渲染成员列表
    renderMemberList: (members) => {
        return members.map(member => `
            <div class="member-item">
                <label class="member-checkbox">
                    <input type="checkbox" value="${member.id}">
                    <img src="${member.persona.avatarUrl}" alt="${member.persona.name}" class="member-avatar">
                    <span class="member-name">${member.persona.name}</span>
                    <span class="member-status ${member.isOnline ? 'online' : 'offline'}">
                        ${member.isOnline ? '在线' : '离线'}
                    </span>
                </label>
            </div>
        `).join('');
    },
    
    // 绑定邀请对话框事件
    bindInviteDialogEvents: (dialog, component) => {
        const closeBtn = dialog.querySelector('.close-btn');
        const cancelBtn = dialog.querySelector('.cancel-btn');
        const inviteBtn = dialog.querySelector('.invite-btn');
        
        const closeDialog = () => {
            dialog.remove();
        };
        
        closeBtn.addEventListener('click', closeDialog);
        cancelBtn.addEventListener('click', closeDialog);
        
        inviteBtn.addEventListener('click', () => {
            const selectedMembers = Array.from(
                dialog.querySelectorAll('input[type="checkbox"]:checked')
            ).map(checkbox => parseInt(checkbox.value));
            
            if (selectedMembers.length > 0) {
                ThreadActionsEnhancer.sendCallInvitations(component, selectedMembers);
                closeDialog();
            }
        });
    },
    
    // 发送通话邀请
    sendCallInvitations: async (component, memberIds) => {
        try {
            await rpc('/mail/rtc/send_call_invitations', {
                thread_id: component.thread.id,
                member_ids: memberIds
            });
            
            showNotification(`已向 ${memberIds.length} 位成员发送通话邀请`, 'success');
        } catch (error) {
            console.error('发送通话邀请失败:', error);
            showNotification('发送通话邀请失败', 'error');
        }
    }
};

// 应用线程操作增强
ThreadActionsEnhancer.enhanceThreadActions();
```

### 2. 操作权限管理

```javascript
// 操作权限管理
const ThreadActionPermissions = {
    setupPermissionControl: () => {
        // 增强通话操作的权限检查
        const originalCallCondition = threadActionsRegistry.get("call").condition;
        
        threadActionsRegistry.add("call", {
            ...threadActionsRegistry.get("call"),
            condition(component) {
                // 调用原始条件检查
                if (!originalCallCondition(component)) {
                    return false;
                }
                
                // 添加权限检查
                return ThreadActionPermissions.checkCallPermission(component);
            }
        });
        
        // 增强设置操作的权限检查
        const originalSettingsCondition = threadActionsRegistry.get("settings").condition;
        
        threadActionsRegistry.add("settings", {
            ...threadActionsRegistry.get("settings"),
            condition(component) {
                // 调用原始条件检查
                if (!originalSettingsCondition(component)) {
                    return false;
                }
                
                // 添加权限检查
                return ThreadActionPermissions.checkSettingsPermission(component);
            }
        });
    },
    
    // 检查通话权限
    checkCallPermission: (component) => {
        const thread = component.thread;
        const user = component.store.self;
        
        // 检查用户权限
        if (!user) return false;
        
        // 检查线程类型权限
        if (thread.channel_type === 'channel') {
            // 公共频道权限检查
            return ThreadActionPermissions.checkChannelCallPermission(thread, user);
        } else if (thread.channel_type === 'chat') {
            // 私人聊天权限检查
            return ThreadActionPermissions.checkChatCallPermission(thread, user);
        }
        
        return true;
    },
    
    // 检查频道通话权限
    checkChannelCallPermission: (thread, user) => {
        // 检查是否为频道成员
        const membership = thread.channelMembers.find(m => m.persona.id === user.id);
        if (!membership) return false;
        
        // 检查成员角色权限
        if (membership.role === 'moderator' || membership.role === 'admin') {
            return true;
        }
        
        // 检查频道设置
        if (thread.allowMemberCalls === false) {
            return false;
        }
        
        return true;
    },
    
    // 检查聊天通话权限
    checkChatCallPermission: (thread, user) => {
        // 私人聊天默认允许通话
        return true;
    },
    
    // 检查设置权限
    checkSettingsPermission: (component) => {
        // 设置权限通常与通话权限相同
        return ThreadActionPermissions.checkCallPermission(component);
    }
};

// 应用权限管理
ThreadActionPermissions.setupPermissionControl();
```

### 3. 操作状态管理

```javascript
// 操作状态管理
const ThreadActionStateManager = {
    setupStateManagement: () => {
        // 添加动态状态管理
        const StateManagerPatch = {
            setup() {
                const component = useComponent();
                component.rtc = useState(useService("discuss.rtc"));
                component.actionStates = useState({
                    callInProgress: false,
                    settingsOpen: false,
                    lastCallTime: null
                });
                
                // 监听RTC状态变化
                component.rtc.addEventListener('state_changed', () => {
                    ThreadActionStateManager.updateActionStates(component);
                });
            }
        };
        
        // 应用到所有通话相关操作
        ['call', 'settings'].forEach(actionName => {
            const action = threadActionsRegistry.get(actionName);
            if (action) {
                threadActionsRegistry.add(actionName, {
                    ...action,
                    setup: StateManagerPatch.setup
                });
            }
        });
    },
    
    // 更新操作状态
    updateActionStates: (component) => {
        const rtcState = component.rtc.state;
        
        component.actionStates.callInProgress = Boolean(rtcState.channel);
        component.actionStates.lastCallTime = rtcState.lastCallEndTime;
        
        // 更新UI显示
        ThreadActionStateManager.updateActionUI(component);
    },
    
    // 更新操作UI
    updateActionUI: (component) => {
        const callButton = document.querySelector('[data-action="call"]');
        const settingsButton = document.querySelector('[data-action="settings"]');
        
        if (callButton) {
            if (component.actionStates.callInProgress) {
                callButton.classList.add('in-call');
                callButton.title = '结束通话';
            } else {
                callButton.classList.remove('in-call');
                callButton.title = '开始通话';
            }
        }
        
        if (settingsButton) {
            if (component.actionStates.settingsOpen) {
                settingsButton.classList.add('active');
            } else {
                settingsButton.classList.remove('active');
            }
        }
    }
};

// 应用状态管理
ThreadActionStateManager.setupStateManagement();
```

## 技术特点

### 1. 注册表机制
- 标准的操作注册模式
- 灵活的条件判断
- 序列化排序支持

### 2. 组件集成
- OWL组件系统集成
- 服务依赖注入
- 状态响应式管理

### 3. 条件控制
- 动态显示条件
- 多层级权限检查
- 上下文相关判断

### 4. 用户界面
- 图标和文本支持
- 国际化集成
- 响应式交互

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态功能扩展

### 2. 策略模式 (Strategy Pattern)
- 不同条件的判断策略
- 操作执行策略

### 3. 组合模式 (Composite Pattern)
- 操作的组合和排序
- 统一的操作接口

## 注意事项

1. **条件判断**: 确保条件判断的准确性和性能
2. **权限控制**: 正确实现操作权限检查
3. **状态同步**: 保持操作状态与RTC状态同步
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **更多操作**: 添加更多通话相关操作
2. **权限系统**: 完善操作权限管理系统
3. **快捷键**: 支持操作快捷键绑定
4. **自定义**: 支持用户自定义操作
5. **分析统计**: 添加操作使用统计分析

该模块为视频通话系统提供了完整的线程级别操作入口，是用户界面集成的重要组成部分。

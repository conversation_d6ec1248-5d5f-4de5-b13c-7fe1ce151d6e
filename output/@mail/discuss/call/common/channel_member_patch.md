# Channel Member Patch - 频道成员补丁

## 概述

`channel_member_patch.js` 实现了对 Odoo 讨论应用中频道成员模型的补丁扩展，为频道成员添加了RTC会话关联功能。该补丁通过Odoo的补丁机制扩展了ChannelMember模型，添加了rtcSession关联字段，建立了频道成员与RTC会话之间的数据关系，是视频通话系统中数据模型扩展的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/channel_member_patch.js`
- **行数**: 23
- **模块**: `@mail/discuss/call/common/channel_member_patch`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/channel_member_model'  // 频道成员模型
'@mail/core/common/record'                // 记录基类

// 工具依赖
'@web/core/utils/patch'                   // 补丁工具
```

## 补丁定义

### ChannelMemberPatch 对象

```javascript
const ChannelMemberPatch = {
    setup() {
        super.setup(...arguments);
        this.rtcSession = Record.one("RtcSession");
    },
};
```

**补丁特性**:
- 扩展setup方法
- 添加RTC会话关联
- 保持原有功能

### 补丁应用

```javascript
patch(ChannelMember.prototype, ChannelMemberPatch);
```

**应用特性**:
- 原型补丁模式
- 非侵入式扩展
- 运行时增强

## 核心功能

### 1. RTC会话关联

```javascript
this.rtcSession = Record.one("RtcSession");
```

**关联功能**:
- **一对一关系**: 每个频道成员对应一个RTC会话
- **双向关联**: 建立成员与会话的双向引用
- **数据完整性**: 确保数据关系的一致性

### 2. 模型扩展

```javascript
setup() {
    super.setup(...arguments);
    // 扩展逻辑
}
```

**扩展特性**:
- **继承原有功能**: 调用父类setup方法
- **添加新功能**: 扩展RTC相关功能
- **保持兼容性**: 不破坏原有API

## 使用场景

### 1. 频道成员RTC增强

```javascript
// 频道成员RTC功能增强
const ChannelMemberRTCEnhancer = {
    enhanceChannelMember: () => {
        // 扩展频道成员的RTC功能
        const EnhancedChannelMemberPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加RTC会话关联
                this.rtcSession = Record.one("RtcSession", { inverse: "channelMember" });
                
                // 添加通话状态计算字段
                this.isInCall = Record.attr(false, {
                    compute() {
                        return Boolean(this.rtcSession && this.rtcSession.isActive);
                    }
                });
                
                // 添加媒体状态计算字段
                this.hasVideo = Record.attr(false, {
                    compute() {
                        return Boolean(this.rtcSession && this.rtcSession.isVideoStreaming);
                    }
                });
                
                this.hasAudio = Record.attr(false, {
                    compute() {
                        return Boolean(this.rtcSession && !this.rtcSession.isMute);
                    }
                });
                
                // 添加说话状态
                this.isTalking = Record.attr(false, {
                    compute() {
                        return Boolean(this.rtcSession && this.rtcSession.isTalking);
                    }
                });
                
                // 添加连接质量
                this.connectionQuality = Record.attr('unknown', {
                    compute() {
                        if (!this.rtcSession) return 'unknown';
                        return this.rtcSession.connectionQuality || 'unknown';
                    }
                });
            },
            
            // 添加RTC操作方法
            async startCall(options = {}) {
                if (!this.thread) {
                    throw new Error('无法在没有线程的情况下开始通话');
                }
                
                const rtcService = useService("discuss.rtc");
                await rtcService.toggleCall(this.thread, options);
            },
            
            async joinCall() {
                if (!this.rtcSession) {
                    throw new Error('没有活跃的RTC会话');
                }
                
                const rtcService = useService("discuss.rtc");
                await rtcService.joinCall(this.thread);
            },
            
            async leaveCall() {
                if (!this.rtcSession) {
                    return; // 已经不在通话中
                }
                
                const rtcService = useService("discuss.rtc");
                await rtcService.leaveCall(this.thread);
            },
            
            async toggleMute() {
                if (!this.rtcSession) {
                    throw new Error('没有活跃的RTC会话');
                }
                
                const rtcService = useService("discuss.rtc");
                if (this.rtcSession.isMute) {
                    await rtcService.unmute();
                } else {
                    await rtcService.mute();
                }
            },
            
            async toggleVideo() {
                if (!this.rtcSession) {
                    throw new Error('没有活跃的RTC会话');
                }
                
                const rtcService = useService("discuss.rtc");
                await rtcService.toggleVideo("camera");
            },
            
            async toggleScreenShare() {
                if (!this.rtcSession) {
                    throw new Error('没有活跃的RTC会话');
                }
                
                const rtcService = useService("discuss.rtc");
                await rtcService.toggleVideo("screen");
            },
            
            // 获取通话统计信息
            getCallStatistics() {
                if (!this.rtcSession) {
                    return null;
                }
                
                return {
                    duration: this.rtcSession.callDuration,
                    quality: this.connectionQuality,
                    hasVideo: this.hasVideo,
                    hasAudio: this.hasAudio,
                    isTalking: this.isTalking,
                    talkingTime: this.rtcSession.talkingTime
                };
            },
            
            // 获取媒体状态
            getMediaStatus() {
                if (!this.rtcSession) {
                    return {
                        audio: false,
                        video: false,
                        screen: false
                    };
                }
                
                return {
                    audio: !this.rtcSession.isMute,
                    video: this.rtcSession.isCameraOn,
                    screen: this.rtcSession.isScreenSharingOn
                };
            },
            
            // 设置音量
            setVolume(volume) {
                if (this.rtcSession && this.rtcSession.audioElement) {
                    this.rtcSession.audioElement.volume = Math.max(0, Math.min(1, volume));
                }
            },
            
            // 获取音量
            getVolume() {
                if (this.rtcSession && this.rtcSession.audioElement) {
                    return this.rtcSession.audioElement.volume;
                }
                return 1.0;
            }
        };
        
        // 应用增强补丁
        patch(ChannelMember.prototype, EnhancedChannelMemberPatch);
    },
    
    // 添加事件监听器
    addEventListeners: () => {
        const EventListenerPatch = {
            setup() {
                super.setup(...arguments);
                
                // 监听RTC会话变化
                this.addEventListener('rtcSession', (session) => {
                    ChannelMemberRTCEnhancer.handleRTCSessionChange(this, session);
                });
                
                // 监听通话状态变化
                this.addEventListener('isInCall', (isInCall) => {
                    ChannelMemberRTCEnhancer.handleCallStateChange(this, isInCall);
                });
            }
        };
        
        patch(ChannelMember.prototype, EventListenerPatch);
    },
    
    handleRTCSessionChange: (member, session) => {
        console.log(`频道成员 ${member.persona?.name} 的RTC会话已更新:`, session);
        
        // 通知UI更新
        const event = new CustomEvent('member_rtc_session_changed', {
            detail: { member, session }
        });
        document.dispatchEvent(event);
    },
    
    handleCallStateChange: (member, isInCall) => {
        console.log(`频道成员 ${member.persona?.name} 的通话状态: ${isInCall ? '加入' : '离开'}`);
        
        // 更新成员状态显示
        ChannelMemberRTCEnhancer.updateMemberStatusDisplay(member, isInCall);
        
        // 通知其他组件
        const event = new CustomEvent('member_call_state_changed', {
            detail: { member, isInCall }
        });
        document.dispatchEvent(event);
    },
    
    updateMemberStatusDisplay: (member, isInCall) => {
        const memberElements = document.querySelectorAll(`[data-member-id="${member.id}"]`);
        
        memberElements.forEach(element => {
            element.classList.toggle('in-call', isInCall);
            
            // 更新状态指示器
            const statusIndicator = element.querySelector('.call-status-indicator');
            if (statusIndicator) {
                statusIndicator.classList.toggle('active', isInCall);
                statusIndicator.title = isInCall ? '通话中' : '离线';
            }
        });
    }
};

// 应用所有增强功能
ChannelMemberRTCEnhancer.enhanceChannelMember();
ChannelMemberRTCEnhancer.addEventListeners();
```

### 2. 数据关系管理

```javascript
// 数据关系管理器
const DataRelationshipManager = {
    setupChannelMemberRelations: () => {
        const RelationshipPatch = {
            setup() {
                super.setup(...arguments);
                
                // 建立与RTC会话的关系
                this.rtcSession = Record.one("RtcSession", { 
                    inverse: "channelMember",
                    onUpdate() {
                        DataRelationshipManager.syncRelationshipData(this);
                    }
                });
                
                // 建立与通话的关系
                this.activeCall = Record.attr(null, {
                    compute() {
                        return this.rtcSession?.channel || null;
                    }
                });
                
                // 建立与其他成员的关系
                this.callPeers = Record.attr([], {
                    compute() {
                        if (!this.activeCall) return [];
                        return this.activeCall.channelMembers.filter(member => 
                            member.id !== this.id && member.rtcSession
                        );
                    }
                });
            },
            
            // 同步关系数据
            syncWithRTCSession() {
                if (this.rtcSession) {
                    // 确保双向关系一致
                    if (this.rtcSession.channelMember !== this) {
                        this.rtcSession.channelMember = this;
                    }
                }
            },
            
            // 清理关系
            cleanupRelations() {
                if (this.rtcSession) {
                    this.rtcSession.channelMember = null;
                    this.rtcSession = null;
                }
            }
        };
        
        patch(ChannelMember.prototype, RelationshipPatch);
    },
    
    syncRelationshipData: (member) => {
        // 确保数据一致性
        if (member.rtcSession && member.rtcSession.channelMember !== member) {
            member.rtcSession.channelMember = member;
        }
        
        // 更新相关计算字段
        member.trigger('update');
    },
    
    validateRelationships: (member) => {
        const issues = [];
        
        // 检查RTC会话关系
        if (member.rtcSession) {
            if (member.rtcSession.channelMember !== member) {
                issues.push('RTC会话的channelMember引用不一致');
            }
        }
        
        // 检查线程关系
        if (member.thread && member.rtcSession) {
            if (member.rtcSession.channel !== member.thread) {
                issues.push('RTC会话的频道引用不一致');
            }
        }
        
        return issues;
    }
};

// 应用关系管理
DataRelationshipManager.setupChannelMemberRelations();
```

### 3. 通话状态同步

```javascript
// 通话状态同步管理器
const CallStateSyncManager = {
    setupStateSynchronization: () => {
        const SyncPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加状态同步字段
                this.lastSyncTime = Record.attr(0);
                this.syncErrors = Record.attr([]);
                
                // 定期同步状态
                this.syncInterval = setInterval(() => {
                    CallStateSyncManager.syncMemberState(this);
                }, 5000); // 每5秒同步一次
            },
            
            // 手动同步状态
            async syncState() {
                try {
                    await CallStateSyncManager.syncMemberState(this);
                } catch (error) {
                    console.error('同步成员状态失败:', error);
                    this.syncErrors.push({
                        timestamp: Date.now(),
                        error: error.message
                    });
                }
            },
            
            // 清理同步资源
            destroy() {
                if (this.syncInterval) {
                    clearInterval(this.syncInterval);
                    this.syncInterval = null;
                }
                super.destroy?.();
            }
        };
        
        patch(ChannelMember.prototype, SyncPatch);
    },
    
    syncMemberState: async (member) => {
        if (!member.rtcSession) {
            return; // 没有RTC会话，无需同步
        }
        
        try {
            // 获取最新的RTC状态
            const rtcService = useService("discuss.rtc");
            const latestState = await rtcService.getMemberState(member.id);
            
            // 更新本地状态
            if (latestState) {
                CallStateSyncManager.updateMemberState(member, latestState);
            }
            
            member.lastSyncTime = Date.now();
        } catch (error) {
            console.error(`同步成员 ${member.id} 状态失败:`, error);
            throw error;
        }
    },
    
    updateMemberState: (member, state) => {
        if (!member.rtcSession) return;
        
        // 更新RTC会话状态
        Object.assign(member.rtcSession, {
            isMute: state.isMute,
            isDeaf: state.isDeaf,
            isCameraOn: state.isCameraOn,
            isScreenSharingOn: state.isScreenSharingOn,
            isTalking: state.isTalking,
            connectionState: state.connectionState
        });
        
        // 触发更新事件
        member.trigger('state_updated', state);
    },
    
    // 批量同步所有成员状态
    syncAllMembers: async (thread) => {
        const members = thread.channelMembers.filter(member => member.rtcSession);
        
        const syncPromises = members.map(member => 
            CallStateSyncManager.syncMemberState(member).catch(error => {
                console.error(`同步成员 ${member.id} 失败:`, error);
                return null;
            })
        );
        
        await Promise.all(syncPromises);
    }
};

// 应用状态同步
CallStateSyncManager.setupStateSynchronization();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 运行时模型增强
- 保持原有功能完整性

### 2. 数据关联
- 一对一关系建立
- 双向引用管理
- 数据完整性保证

### 3. 模型扩展
- 继承原有setup逻辑
- 添加新的关联字段
- 保持API兼容性

### 4. 简洁设计
- 最小化代码量
- 专注核心功能
- 清晰的职责分离

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 关联模式 (Association Pattern)
- 模型间关系建立
- 数据引用管理

### 3. 扩展模式 (Extension Pattern)
- 功能渐进式增强
- 向后兼容保证

## 注意事项

1. **数据一致性**: 确保关联关系的数据一致性
2. **性能影响**: 补丁不应影响原有性能
3. **兼容性**: 保持与原有API的兼容性
4. **内存管理**: 注意关联对象的内存泄漏

## 扩展建议

1. **更多关联**: 添加更多RTC相关的关联字段
2. **状态计算**: 增加更多基于RTC状态的计算字段
3. **事件处理**: 添加RTC状态变化的事件处理
4. **验证机制**: 增加数据关系的验证机制
5. **性能优化**: 优化关联查询的性能

该补丁是视频通话系统中数据模型扩展的重要组成部分，建立了频道成员与RTC会话的关联关系。

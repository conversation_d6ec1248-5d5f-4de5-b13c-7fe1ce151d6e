# Call Invitation - 通话邀请

## 概述

`call_invitation.js` 实现了 Odoo 讨论应用中的通话邀请组件，用于显示和处理来电邀请。该组件支持接受通话、拒绝通话、打开聊天窗口等功能，集成了RTC服务进行通话状态管理，提供了简洁的邀请界面和交互逻辑，是视频通话系统中的重要入口组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_invitation.js`
- **行数**: 42
- **模块**: `@mail/discuss/call/common/call_invitation`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                // OWL 框架
'@web/core/utils/hooks'    // Web 核心钩子
```

## 组件定义

### CallInvitation 类

```javascript
class CallInvitation extends Component {
    static props = ["thread"];
    static template = "discuss.CallInvitation";
}
```

**组件特性**:
- 接收线程对象作为属性
- 使用自定义模板
- 简洁的组件结构

## Props 配置

### Props 详细说明

- **`thread`** (必需):
  - 类型: Thread 模型实例
  - 用途: 通话线程对象
  - 功能: 提供通话上下文和操作目标

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.rtc = useService("discuss.rtc");
}
```

**初始化内容**:
- RTC服务注入
- 通话状态管理

## 核心功能

### 1. 接受通话

```javascript
async onClickAccept(ev) {
    this.props.thread.open();
    if (this.rtc.state.hasPendingRequest) {
        return;
    }
    await this.rtc.toggleCall(this.props.thread);
}
```

**接受功能**:
- **打开线程**: 显示聊天窗口
- **状态检查**: 避免重复请求
- **加入通话**: 调用RTC服务加入通话

### 2. 拒绝通话

```javascript
onClickRefuse(ev) {
    if (this.rtc.state.hasPendingRequest) {
        return;
    }
    this.rtc.leaveCall(this.props.thread);
}
```

**拒绝功能**:
- **状态检查**: 避免重复操作
- **离开通话**: 调用RTC服务拒绝通话

### 3. 头像点击

```javascript
onClickAvatar(ev) {
    this.props.thread.open();
}
```

**头像功能**:
- **打开线程**: 显示聊天界面
- **快速访问**: 提供快速访问入口

## 使用场景

### 1. 通话邀请增强

```javascript
// 通话邀请增强功能
const CallInvitationEnhancer = {
    enhanceInvitation: (invitation) => {
        // 添加铃声播放
        CallInvitationEnhancer.addRingtone(invitation);
        
        // 添加振动提醒
        CallInvitationEnhancer.addVibration(invitation);
        
        // 添加桌面通知
        CallInvitationEnhancer.addDesktopNotification(invitation);
        
        // 添加自动拒绝定时器
        CallInvitationEnhancer.addAutoRejectTimer(invitation);
        
        // 添加快速回复
        CallInvitationEnhancer.addQuickReply(invitation);
    },
    
    addRingtone: (invitation) => {
        const audio = new Audio('/mail/static/src/sounds/ringtone.mp3');
        audio.loop = true;
        audio.volume = 0.5;
        
        // 开始播放铃声
        const playRingtone = () => {
            audio.play().catch(error => {
                console.warn('无法播放铃声:', error);
            });
        };
        
        // 停止播放铃声
        const stopRingtone = () => {
            audio.pause();
            audio.currentTime = 0;
        };
        
        // 监听邀请状态
        invitation.addEventListener('invitation_accepted', stopRingtone);
        invitation.addEventListener('invitation_rejected', stopRingtone);
        invitation.addEventListener('invitation_timeout', stopRingtone);
        
        // 开始播放
        playRingtone();
        
        // 存储控制函数
        invitation.stopRingtone = stopRingtone;
    },
    
    addVibration: (invitation) => {
        if (!navigator.vibrate) return;
        
        // 振动模式：长-短-长
        const vibrationPattern = [500, 200, 500, 200, 500];
        
        const startVibration = () => {
            const vibrate = () => {
                navigator.vibrate(vibrationPattern);
                invitation.vibrationTimeout = setTimeout(vibrate, 2000);
            };
            vibrate();
        };
        
        const stopVibration = () => {
            if (invitation.vibrationTimeout) {
                clearTimeout(invitation.vibrationTimeout);
                invitation.vibrationTimeout = null;
            }
            navigator.vibrate(0);
        };
        
        // 监听邀请状态
        invitation.addEventListener('invitation_accepted', stopVibration);
        invitation.addEventListener('invitation_rejected', stopVibration);
        invitation.addEventListener('invitation_timeout', stopVibration);
        
        // 开始振动
        startVibration();
        
        // 存储控制函数
        invitation.stopVibration = stopVibration;
    },
    
    addDesktopNotification: (invitation) => {
        if (!('Notification' in window)) return;
        
        const showNotification = () => {
            if (Notification.permission === 'granted') {
                const caller = invitation.props.thread.displayName || '未知用户';
                const notification = new Notification('来电邀请', {
                    body: `${caller} 邀请您加入通话`,
                    icon: '/mail/static/src/img/call_icon.png',
                    tag: 'call-invitation',
                    requireInteraction: true,
                    actions: [
                        { action: 'accept', title: '接受' },
                        { action: 'reject', title: '拒绝' }
                    ]
                });
                
                notification.onclick = () => {
                    window.focus();
                    invitation.onClickAccept();
                    notification.close();
                };
                
                notification.addEventListener('notificationclick', (event) => {
                    if (event.action === 'accept') {
                        invitation.onClickAccept();
                    } else if (event.action === 'reject') {
                        invitation.onClickRefuse();
                    }
                    notification.close();
                });
                
                // 自动关闭通知
                setTimeout(() => {
                    notification.close();
                }, 30000);
                
                invitation.desktopNotification = notification;
            } else if (Notification.permission === 'default') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        showNotification();
                    }
                });
            }
        };
        
        showNotification();
    },
    
    addAutoRejectTimer: (invitation) => {
        const timeoutDuration = 30000; // 30秒自动拒绝
        
        const timer = setTimeout(() => {
            invitation.onClickRefuse();
            showNotification('通话邀请已超时', 'info');
        }, timeoutDuration);
        
        // 监听邀请状态
        const clearTimer = () => {
            clearTimeout(timer);
        };
        
        invitation.addEventListener('invitation_accepted', clearTimer);
        invitation.addEventListener('invitation_rejected', clearTimer);
        
        // 显示倒计时
        CallInvitationEnhancer.showCountdown(invitation, timeoutDuration);
    },
    
    showCountdown: (invitation, duration) => {
        const countdownElement = document.createElement('div');
        countdownElement.className = 'invitation-countdown';
        countdownElement.innerHTML = `
            <div class="countdown-circle">
                <svg class="countdown-svg" width="60" height="60">
                    <circle class="countdown-bg" cx="30" cy="30" r="25"></circle>
                    <circle class="countdown-progress" cx="30" cy="30" r="25"></circle>
                </svg>
                <span class="countdown-text">30</span>
            </div>
        `;
        
        invitation.el.appendChild(countdownElement);
        
        const progressCircle = countdownElement.querySelector('.countdown-progress');
        const countdownText = countdownElement.querySelector('.countdown-text');
        const circumference = 2 * Math.PI * 25;
        
        progressCircle.style.strokeDasharray = circumference;
        progressCircle.style.strokeDashoffset = 0;
        
        let remaining = duration / 1000;
        const interval = setInterval(() => {
            remaining--;
            countdownText.textContent = remaining;
            
            const progress = remaining / (duration / 1000);
            const offset = circumference * (1 - progress);
            progressCircle.style.strokeDashoffset = offset;
            
            if (remaining <= 0) {
                clearInterval(interval);
                countdownElement.remove();
            }
        }, 1000);
        
        // 清理倒计时
        const clearCountdown = () => {
            clearInterval(interval);
            countdownElement.remove();
        };
        
        invitation.addEventListener('invitation_accepted', clearCountdown);
        invitation.addEventListener('invitation_rejected', clearCountdown);
    },
    
    addQuickReply: (invitation) => {
        const quickReplyContainer = document.createElement('div');
        quickReplyContainer.className = 'quick-reply-container';
        quickReplyContainer.innerHTML = `
            <div class="quick-reply-buttons">
                <button class="quick-reply-btn" data-message="我现在不方便接听">
                    <i class="fa fa-clock-o"></i>
                    <span>稍后回电</span>
                </button>
                <button class="quick-reply-btn" data-message="我在开会，请发消息">
                    <i class="fa fa-users"></i>
                    <span>开会中</span>
                </button>
                <button class="quick-reply-btn" data-message="请发送文字消息">
                    <i class="fa fa-comment"></i>
                    <span>发消息</span>
                </button>
            </div>
        `;
        
        invitation.el.appendChild(quickReplyContainer);
        
        // 绑定快速回复事件
        quickReplyContainer.querySelectorAll('.quick-reply-btn').forEach(btn => {
            btn.addEventListener('click', async () => {
                const message = btn.dataset.message;
                
                // 发送快速回复消息
                await CallInvitationEnhancer.sendQuickReply(invitation, message);
                
                // 拒绝通话
                invitation.onClickRefuse();
                
                showNotification('已发送快速回复', 'success');
            });
        });
    },
    
    sendQuickReply: async (invitation, message) => {
        try {
            const thread = invitation.props.thread;
            await thread.post({
                body: message,
                message_type: 'comment'
            });
        } catch (error) {
            console.error('发送快速回复失败:', error);
        }
    }
};
```

### 2. 通话邀请管理器

```javascript
// 通话邀请管理器
const CallInvitationManager = {
    activeInvitations: new Map(),
    
    createInvitation: (thread, options = {}) => {
        // 检查是否已有邀请
        if (CallInvitationManager.activeInvitations.has(thread.id)) {
            return CallInvitationManager.activeInvitations.get(thread.id);
        }
        
        // 创建邀请组件
        const invitation = new CallInvitation({ thread });
        
        // 增强邀请功能
        if (options.enhanced) {
            CallInvitationEnhancer.enhanceInvitation(invitation);
        }
        
        // 添加到活跃邀请列表
        CallInvitationManager.activeInvitations.set(thread.id, invitation);
        
        // 监听邀请结束
        invitation.addEventListener('invitation_ended', () => {
            CallInvitationManager.removeInvitation(thread.id);
        });
        
        return invitation;
    },
    
    removeInvitation: (threadId) => {
        const invitation = CallInvitationManager.activeInvitations.get(threadId);
        if (invitation) {
            // 清理资源
            if (invitation.stopRingtone) invitation.stopRingtone();
            if (invitation.stopVibration) invitation.stopVibration();
            if (invitation.desktopNotification) invitation.desktopNotification.close();
            
            // 从列表中移除
            CallInvitationManager.activeInvitations.delete(threadId);
        }
    },
    
    acceptAllInvitations: () => {
        CallInvitationManager.activeInvitations.forEach(invitation => {
            invitation.onClickAccept();
        });
    },
    
    rejectAllInvitations: () => {
        CallInvitationManager.activeInvitations.forEach(invitation => {
            invitation.onClickRefuse();
        });
    },
    
    getInvitationCount: () => {
        return CallInvitationManager.activeInvitations.size;
    },
    
    hasInvitation: (threadId) => {
        return CallInvitationManager.activeInvitations.has(threadId);
    }
};
```

### 3. 邀请状态跟踪

```javascript
// 邀请状态跟踪
const InvitationStateTracker = {
    trackInvitation: (invitation) => {
        const startTime = Date.now();
        
        const tracker = {
            threadId: invitation.props.thread.id,
            startTime,
            status: 'pending',
            actions: []
        };
        
        // 记录用户操作
        const recordAction = (action) => {
            tracker.actions.push({
                action,
                timestamp: Date.now(),
                duration: Date.now() - startTime
            });
        };
        
        // 监听操作事件
        invitation.addEventListener('invitation_accepted', () => {
            tracker.status = 'accepted';
            recordAction('accept');
            InvitationStateTracker.saveTracker(tracker);
        });
        
        invitation.addEventListener('invitation_rejected', () => {
            tracker.status = 'rejected';
            recordAction('reject');
            InvitationStateTracker.saveTracker(tracker);
        });
        
        invitation.addEventListener('invitation_timeout', () => {
            tracker.status = 'timeout';
            recordAction('timeout');
            InvitationStateTracker.saveTracker(tracker);
        });
        
        return tracker;
    },
    
    saveTracker: (tracker) => {
        // 保存到本地存储或发送到服务器
        const trackers = JSON.parse(localStorage.getItem('invitation_trackers') || '[]');
        trackers.push(tracker);
        
        // 限制存储数量
        if (trackers.length > 100) {
            trackers.splice(0, trackers.length - 100);
        }
        
        localStorage.setItem('invitation_trackers', JSON.stringify(trackers));
    },
    
    getStatistics: () => {
        const trackers = JSON.parse(localStorage.getItem('invitation_trackers') || '[]');
        
        const stats = {
            total: trackers.length,
            accepted: trackers.filter(t => t.status === 'accepted').length,
            rejected: trackers.filter(t => t.status === 'rejected').length,
            timeout: trackers.filter(t => t.status === 'timeout').length,
            averageResponseTime: 0
        };
        
        const responseTimes = trackers
            .filter(t => t.status === 'accepted' || t.status === 'rejected')
            .map(t => t.actions[0]?.duration || 0);
        
        if (responseTimes.length > 0) {
            stats.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
        }
        
        return stats;
    }
};
```

## 技术特点

### 1. 简洁设计
- 最小化的组件结构
- 清晰的功能分离
- 直观的用户交互

### 2. 状态管理
- RTC服务集成
- 请求状态检查
- 防重复操作

### 3. 线程集成
- 线程对象操作
- 聊天窗口打开
- 通话状态同步

### 4. 事件处理
- 异步操作支持
- 错误处理机制
- 用户体验优化

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的邀请组件
- 清晰的属性接口

### 2. 命令模式 (Command Pattern)
- 接受和拒绝操作的封装
- 统一的操作接口

### 3. 观察者模式 (Observer Pattern)
- 状态变化监听
- 事件驱动更新

## 注意事项

1. **重复请求**: 防止用户重复点击操作
2. **状态同步**: 保持邀请状态与RTC状态一致
3. **用户体验**: 提供清晰的操作反馈
4. **资源清理**: 及时清理邀请相关资源

## 扩展建议

1. **多媒体提醒**: 支持自定义铃声和振动
2. **快速回复**: 添加预设回复消息
3. **邀请历史**: 记录邀请历史和统计
4. **群组邀请**: 支持群组通话邀请
5. **智能提醒**: 基于用户状态的智能提醒

该组件是视频通话系统的重要入口，提供了简洁而有效的邀请处理功能。

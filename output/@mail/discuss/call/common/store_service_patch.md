# Store Service Patch - 存储服务补丁

## 概述

`store_service_patch.js` 实现了对 Odoo 讨论应用中存储服务的补丁扩展，为存储服务添加了RTC相关的数据管理和业务逻辑。该补丁通过Odoo的补丁机制扩展了Store服务，添加了RTC会话管理、来电铃声控制、成员排序逻辑等功能，建立了存储层与RTC功能的集成，是视频通话系统中数据管理的核心组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/store_service_patch.js`
- **行数**: 67
- **模块**: `@mail/discuss/call/common/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'                        // 记录基类
'@mail/core/common/store_service'                 // 存储服务
'@mail/discuss/call/common/rtc_session_model'     // RTC会话模型
'@web/core/utils/patch'                           // 补丁工具
```

## 补丁定义

### StorePatch 对象

```javascript
const StorePatch = {
    setup() {
        super.setup(...arguments);
        this.RtcSession = RtcSession;
        this.rtc = Record.one("Rtc", {
            compute() {
                return {};
            },
        });
        this.ringingThreads = Record.many("Thread", {
            onUpdate() {
                if (this.ringingThreads.length > 0) {
                    this.env.services["mail.sound_effects"].play("incoming-call", {
                        loop: true,
                    });
                } else {
                    this.env.services["mail.sound_effects"].stop("incoming-call");
                }
            },
        });
        this.allActiveRtcSessions = Record.many("RtcSession");
        this.nextTalkingTime = 1;
    },
    // ...其他方法
};
```

**补丁特性**:
- 扩展setup方法
- 添加RTC相关字段
- 集成音效服务

## 核心功能

### 1. RTC数据管理

```javascript
this.RtcSession = RtcSession;
this.rtc = Record.one("Rtc", {
    compute() {
        return {};
    },
});
this.allActiveRtcSessions = Record.many("RtcSession");
this.nextTalkingTime = 1;
```

**数据管理功能**:
- **RTC会话模型**: 注册RTC会话模型类
- **RTC实例**: 创建RTC记录实例
- **活跃会话**: 管理所有活跃的RTC会话
- **说话时间**: 管理说话时间序列

### 2. 来电铃声控制

```javascript
this.ringingThreads = Record.many("Thread", {
    onUpdate() {
        if (this.ringingThreads.length > 0) {
            this.env.services["mail.sound_effects"].play("incoming-call", {
                loop: true,
            });
        } else {
            this.env.services["mail.sound_effects"].stop("incoming-call");
        }
    },
});
```

**铃声控制功能**:
- **来电线程**: 跟踪有来电的线程
- **自动播放**: 有来电时自动播放铃声
- **循环播放**: 铃声循环播放直到处理
- **自动停止**: 无来电时自动停止铃声

### 3. 服务启动

```javascript
onStarted() {
    super.onStarted(...arguments);
    this.rtc.start();
}
```

**启动功能**:
- **服务启动**: 调用父类启动方法
- **RTC启动**: 启动RTC服务

### 4. 成员排序逻辑

```javascript
sortMembers(m1, m2) {
    const m1HasRtc = Boolean(m1.rtcSession);
    const m2HasRtc = Boolean(m2.rtcSession);
    if (m1HasRtc === m2HasRtc) {
        const m1RaisingValue = m1.rtcSession?.raisingHand || Infinity;
        const m2RaisingValue = m2.rtcSession?.raisingHand || Infinity;
        if (m1HasRtc && m1RaisingValue !== m2RaisingValue) {
            return m1RaisingValue - m2RaisingValue;
        } else {
            return super.sortMembers(m1, m2);
        }
    } else {
        return m2HasRtc - m1HasRtc;
    }
}
```

**排序逻辑**:
- **RTC优先**: 有RTC会话的成员排在前面
- **举手排序**: 按举手时间排序(早举手的在前)
- **默认排序**: 其他情况使用默认排序
- **无限值**: 未举手的成员使用无限值排在最后

## 使用场景

### 1. RTC数据管理增强

```javascript
// RTC数据管理增强
const RTCDataManagerEnhancer = {
    enhanceRTCDataManagement: () => {
        const EnhancedStorePatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加RTC统计数据
                this.rtcStatistics = Record.attr({
                    totalSessions: 0,
                    activeSessions: 0,
                    totalCallTime: 0,
                    averageCallDuration: 0
                });
                
                // 添加RTC事件历史
                this.rtcEventHistory = Record.many("RtcEvent");
                
                // 添加RTC性能监控
                this.rtcPerformanceMetrics = Record.attr({
                    connectionQuality: 'unknown',
                    averageLatency: 0,
                    packetLossRate: 0,
                    bandwidthUsage: 0
                });
                
                // 监听RTC会话变化
                this.allActiveRtcSessions.addEventListener('update', () => {
                    this.updateRTCStatistics();
                });
            },
            
            // 更新RTC统计数据
            updateRTCStatistics() {
                const activeSessions = this.allActiveRtcSessions.length;
                this.rtcStatistics.activeSessions = activeSessions;
                this.rtcStatistics.totalSessions = Math.max(
                    this.rtcStatistics.totalSessions,
                    activeSessions
                );
                
                // 计算平均通话时长
                if (activeSessions > 0) {
                    const totalDuration = this.allActiveRtcSessions.reduce((sum, session) => {
                        return sum + (session.callDuration || 0);
                    }, 0);
                    
                    this.rtcStatistics.averageCallDuration = totalDuration / activeSessions;
                }
                
                // 触发统计更新事件
                this.trigger('rtc_statistics_updated', this.rtcStatistics);
            },
            
            // 记录RTC事件
            recordRTCEvent(eventType, data) {
                const event = this.RtcEvent.insert({
                    type: eventType,
                    timestamp: Date.now(),
                    data: data
                });
                
                this.rtcEventHistory.add(event);
                
                // 限制历史记录数量
                if (this.rtcEventHistory.length > 1000) {
                    const oldestEvent = this.rtcEventHistory[0];
                    this.rtcEventHistory.delete(oldestEvent);
                }
            },
            
            // 获取RTC会话统计
            getRTCSessionStatistics() {
                const sessions = this.allActiveRtcSessions;
                
                const stats = {
                    total: sessions.length,
                    byType: {
                        audio: 0,
                        video: 0,
                        screen: 0
                    },
                    byQuality: {
                        excellent: 0,
                        good: 0,
                        fair: 0,
                        poor: 0
                    },
                    totalParticipants: sessions.length,
                    averageQuality: 0
                };
                
                sessions.forEach(session => {
                    // 统计类型
                    if (session.isCameraOn) stats.byType.video++;
                    if (session.isScreenSharingOn) stats.byType.screen++;
                    if (!session.isMute) stats.byType.audio++;
                    
                    // 统计质量
                    const quality = session.connectionQuality || 'unknown';
                    if (quality in stats.byQuality) {
                        stats.byQuality[quality]++;
                    }
                });
                
                return stats;
            },
            
            // 清理过期的RTC数据
            cleanupExpiredRTCData() {
                const now = Date.now();
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                
                // 清理过期事件
                this.rtcEventHistory.filter(event => {
                    return (now - event.timestamp) <= maxAge;
                });
                
                // 清理无效会话
                this.allActiveRtcSessions.filter(session => {
                    return session.isActive && session.connectionState !== 'closed';
                });
            }
        };
        
        patch(Store.prototype, EnhancedStorePatch);
    }
};

// 应用RTC数据管理增强
RTCDataManagerEnhancer.enhanceRTCDataManagement();
```

### 2. 音效管理增强

```javascript
// 音效管理增强
const SoundEffectsEnhancer = {
    enhanceSoundEffects: () => {
        const SoundPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加音效配置
                this.soundConfig = {
                    incomingCallVolume: 0.8,
                    notificationVolume: 0.6,
                    enableVibration: true,
                    customRingtones: new Map()
                };
                
                // 增强来电铃声管理
                this.ringingThreads = Record.many("Thread", {
                    onUpdate() {
                        SoundEffectsEnhancer.handleRingingUpdate(this);
                    },
                });
                
                // 添加其他音效
                this.setupAdditionalSoundEffects();
            },
            
            setupAdditionalSoundEffects() {
                // 连接音效
                this.allActiveRtcSessions.addEventListener('add', (session) => {
                    this.playConnectionSound('connected');
                });
                
                this.allActiveRtcSessions.addEventListener('remove', (session) => {
                    this.playConnectionSound('disconnected');
                });
            },
            
            playConnectionSound(type) {
                const soundMap = {
                    connected: 'user-joined',
                    disconnected: 'user-left',
                    muted: 'mute-on',
                    unmuted: 'mute-off'
                };
                
                const soundName = soundMap[type];
                if (soundName) {
                    this.env.services["mail.sound_effects"].play(soundName, {
                        volume: this.soundConfig.notificationVolume
                    });
                }
            },
            
            // 设置自定义铃声
            setCustomRingtone(threadId, ringtoneUrl) {
                this.soundConfig.customRingtones.set(threadId, ringtoneUrl);
            },
            
            // 播放自定义铃声
            playCustomRingtone(threadId) {
                const customRingtone = this.soundConfig.customRingtones.get(threadId);
                
                if (customRingtone) {
                    const audio = new Audio(customRingtone);
                    audio.volume = this.soundConfig.incomingCallVolume;
                    audio.loop = true;
                    audio.play();
                    return audio;
                }
                
                return null;
            }
        };
        
        patch(Store.prototype, SoundPatch);
    },
    
    handleRingingUpdate(store) {
        const ringingThreads = store.ringingThreads;
        
        if (ringingThreads.length > 0) {
            // 检查是否有自定义铃声
            const hasCustomRingtone = ringingThreads.some(thread => 
                store.soundConfig.customRingtones.has(thread.id)
            );
            
            if (hasCustomRingtone) {
                // 播放自定义铃声
                ringingThreads.forEach(thread => {
                    store.playCustomRingtone(thread.id);
                });
            } else {
                // 播放默认铃声
                store.env.services["mail.sound_effects"].play("incoming-call", {
                    loop: true,
                    volume: store.soundConfig.incomingCallVolume
                });
            }
            
            // 触发振动
            if (store.soundConfig.enableVibration && navigator.vibrate) {
                navigator.vibrate([500, 200, 500]);
            }
        } else {
            // 停止所有铃声
            store.env.services["mail.sound_effects"].stop("incoming-call");
            
            // 停止自定义铃声
            store.soundConfig.customRingtones.forEach((url, threadId) => {
                // 这里需要跟踪音频元素来停止播放
            });
        }
    }
};

// 应用音效管理增强
SoundEffectsEnhancer.enhanceSoundEffects();
```

### 3. 成员排序增强

```javascript
// 成员排序增强
const MemberSortingEnhancer = {
    enhanceMemberSorting: () => {
        const SortingPatch = {
            setup() {
                super.setup(...arguments);
                
                // 添加排序配置
                this.sortingConfig = {
                    prioritizeRTC: true,
                    prioritizeRaisingHand: true,
                    prioritizeTalking: true,
                    prioritizeAdmin: false,
                    customSortRules: []
                };
            },
            
            // 增强的成员排序
            sortMembers(m1, m2) {
                // 应用自定义排序规则
                for (const rule of this.sortingConfig.customSortRules) {
                    const result = rule(m1, m2);
                    if (result !== 0) return result;
                }
                
                // 管理员优先级
                if (this.sortingConfig.prioritizeAdmin) {
                    const m1IsAdmin = m1.persona?.isAdmin || false;
                    const m2IsAdmin = m2.persona?.isAdmin || false;
                    
                    if (m1IsAdmin !== m2IsAdmin) {
                        return m2IsAdmin - m1IsAdmin;
                    }
                }
                
                // 说话状态优先级
                if (this.sortingConfig.prioritizeTalking) {
                    const m1IsTalking = m1.rtcSession?.isTalking || false;
                    const m2IsTalking = m2.rtcSession?.isTalking || false;
                    
                    if (m1IsTalking !== m2IsTalking) {
                        return m2IsTalking - m1IsTalking;
                    }
                }
                
                // RTC会话优先级
                if (this.sortingConfig.prioritizeRTC) {
                    const m1HasRtc = Boolean(m1.rtcSession);
                    const m2HasRtc = Boolean(m2.rtcSession);
                    
                    if (m1HasRtc !== m2HasRtc) {
                        return m2HasRtc - m1HasRtc;
                    }
                    
                    // 举手优先级
                    if (this.sortingConfig.prioritizeRaisingHand && m1HasRtc && m2HasRtc) {
                        const m1RaisingValue = m1.rtcSession?.raisingHand || Infinity;
                        const m2RaisingValue = m2.rtcSession?.raisingHand || Infinity;
                        
                        if (m1RaisingValue !== m2RaisingValue) {
                            return m1RaisingValue - m2RaisingValue;
                        }
                    }
                }
                
                // 默认排序
                return super.sortMembers(m1, m2);
            },
            
            // 添加自定义排序规则
            addCustomSortRule(rule) {
                this.sortingConfig.customSortRules.push(rule);
            },
            
            // 移除自定义排序规则
            removeCustomSortRule(rule) {
                const index = this.sortingConfig.customSortRules.indexOf(rule);
                if (index > -1) {
                    this.sortingConfig.customSortRules.splice(index, 1);
                }
            },
            
            // 更新排序配置
            updateSortingConfig(newConfig) {
                Object.assign(this.sortingConfig, newConfig);
                
                // 触发重新排序
                this.trigger('sorting_config_updated');
            }
        };
        
        patch(Store.prototype, SortingPatch);
    },
    
    // 预定义排序规则
    createPredefinedSortRules: () => {
        return {
            // 按连接质量排序
            byConnectionQuality: (m1, m2) => {
                const qualityOrder = { excellent: 4, good: 3, fair: 2, poor: 1, unknown: 0 };
                const q1 = qualityOrder[m1.rtcSession?.connectionQuality] || 0;
                const q2 = qualityOrder[m2.rtcSession?.connectionQuality] || 0;
                return q2 - q1;
            },
            
            // 按加入时间排序
            byJoinTime: (m1, m2) => {
                const t1 = m1.rtcSession?.joinTime || Infinity;
                const t2 = m2.rtcSession?.joinTime || Infinity;
                return t1 - t2;
            },
            
            // 按说话时间排序
            byTalkingTime: (m1, m2) => {
                const tt1 = m1.rtcSession?.talkingTime || 0;
                const tt2 = m2.rtcSession?.talkingTime || 0;
                return tt2 - tt1;
            }
        };
    }
};

// 应用成员排序增强
MemberSortingEnhancer.enhanceMemberSorting();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 运行时服务增强
- 保持原有功能完整性

### 2. 数据管理
- RTC会话集中管理
- 响应式数据更新
- 自动化业务逻辑

### 3. 音效集成
- 自动铃声控制
- 音效服务集成
- 状态驱动播放

### 4. 智能排序
- 多层级排序逻辑
- RTC状态优先
- 举手时间排序

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 数据变化监听
- 自动化响应

### 3. 策略模式 (Strategy Pattern)
- 不同排序策略
- 可配置的业务逻辑

## 注意事项

1. **数据一致性**: 确保RTC数据的一致性管理
2. **性能影响**: 补丁不应影响存储服务性能
3. **音效控制**: 合理控制音效的播放和停止
4. **排序逻辑**: 确保排序逻辑的正确性和稳定性

## 扩展建议

1. **数据持久化**: 添加RTC数据的持久化存储
2. **性能监控**: 增强RTC性能监控功能
3. **事件系统**: 完善RTC事件系统
4. **配置管理**: 支持更多可配置的业务逻辑
5. **缓存优化**: 优化数据访问和缓存策略

该补丁是视频通话系统中数据管理的核心组成部分，提供了完整的RTC数据管理和业务逻辑。

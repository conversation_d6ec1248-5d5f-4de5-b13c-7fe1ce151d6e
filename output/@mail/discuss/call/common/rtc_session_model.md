# RTC Session Model - RTC会话模型

## 概述

`rtc_session_model.js` 实现了 Odoo 讨论应用中RTC会话的数据模型，负责管理单个参与者在视频通话中的状态和数据。该模型支持音视频状态管理、流媒体处理、连接状态跟踪、说话检测、举手功能等，集成了WebRTC连接信息、媒体流对象、音频元素等，提供了完整的会话数据抽象，是视频通话系统中的核心数据模型。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/rtc_session_model.js`
- **行数**: 208
- **模块**: `@mail/discuss/call/common/rtc_session_model`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'    // 记录模型基类
```

## 模型定义

### RtcSession 类

```javascript
class RtcSession extends Record {
    static id = "id";
    static records = {};
}
```

**模型特性**:
- 继承自Record基类
- 使用ID作为主键
- 静态记录管理

## 数据字段

### 1. 服务器数据字段

```javascript
// 关联字段
channelMember = Record.one("ChannelMember", { inverse: "rtcSession" });

// 媒体状态
isCameraOn;           // 摄像头开启状态
isScreenSharingOn;    // 屏幕共享状态
isDeaf;              // 耳聋状态
isSelfMuted;         // 自我静音状态

// 基础信息
id;                  // 会话ID
```

**字段说明**:
- **channelMember**: 关联的频道成员
- **媒体状态**: 各种媒体设备的开启状态
- **音频控制**: 静音和耳聋状态管理

### 2. 客户端数据字段

```javascript
// 媒体元素
audioElement;        // HTML音频元素
audioStream;         // 音频流
dataChannel;         // 数据通道

// 错误状态
audioError;          // 音频错误
videoError;          // 视频错误

// 连接信息
peerConnection;      // WebRTC连接
connectionState;     // 连接状态
localCandidateType;  // 本地候选类型
remoteCandidateType; // 远程候选类型
```

**字段说明**:
- **媒体元素**: HTML媒体元素和流对象
- **错误管理**: 音视频播放错误状态
- **连接管理**: WebRTC连接相关信息

### 3. 计算字段

```javascript
// 说话状态
isTalking = Record.attr(false, {
    onUpdate() {
        if (this.isTalking && !this.isMute) {
            this.talkingTime = this.store.nextTalkingTime++;
        }
    },
});

// 实际说话状态
isActuallyTalking = Record.attr(false, {
    compute() {
        return this.isTalking && !this.isMute;
    },
});

// 视频流状态
isVideoStreaming = Record.attr(false, {
    compute() {
        return this.isScreenSharingOn || this.isCameraOn;
    },
});

// 简短状态
shortStatus = Record.attr(undefined, {
    compute() {
        if (this.isScreenSharingOn) {
            return "live";
        }
        if (this.isDeaf) {
            return "deafen";
        }
        if (this.isMute) {
            return "mute";
        }
    },
});
```

**计算逻辑**:
- **说话检测**: 结合音频检测和静音状态
- **视频状态**: 综合摄像头和屏幕共享状态
- **状态优先级**: 按重要性显示状态

## 核心方法

### 1. 状态获取器

```javascript
get channel() {
    return this.channelMember?.thread;
}

get isMute() {
    return this.isSelfMuted || this.isDeaf;
}

get hasVideo() {
    return this.isScreenSharingOn || this.isCameraOn;
}

get name() {
    return this.channelMember?.persona.name;
}
```

**获取器功能**:
- **频道获取**: 通过成员关系获取频道
- **静音判断**: 综合自我静音和耳聋状态
- **视频判断**: 检查是否有任何视频流
- **名称获取**: 获取参与者显示名称

### 2. 流管理方法

```javascript
get mainVideoStream() {
    return this.isMainVideoStreamActive && this.videoStreams.get(this.mainVideoStreamType);
}

get isMainVideoStreamActive() {
    if (!this.mainVideoStreamType) {
        return false;
    }
    return this.mainVideoStreamType === "camera" ? this.isCameraOn : this.isScreenSharingOn;
}

getStream(type) {
    const isActive = type === "camera" ? this.isCameraOn : this.isScreenSharingOn;
    return isActive && this.videoStreams.get(type);
}
```

**流管理功能**:
- **主流获取**: 获取当前主要视频流
- **流状态检查**: 检查指定类型流是否活跃
- **条件流获取**: 根据状态返回对应流

### 3. 音频控制方法

```javascript
get volume() {
    return this.audioElement?.volume || this.localVolume;
}

set volume(value) {
    if (this.audioElement) {
        this.audioElement.volume = value;
    }
    this.localVolume = value;
}

async playAudio() {
    if (!this.audioElement) {
        return;
    }
    try {
        await this.audioElement.play();
        this.audioError = undefined;
    } catch (error) {
        this.audioError = error.name;
    }
}
```

**音频控制功能**:
- **音量管理**: 获取和设置音频音量
- **播放控制**: 异步音频播放
- **错误处理**: 播放错误的捕获和记录

### 4. 流状态更新

```javascript
updateStreamState(type, state) {
    if (type === "camera") {
        this.isCameraOn = state;
    } else if (type === "screen") {
        this.isScreenSharingOn = state;
    }
}
```

**状态更新功能**:
- **摄像头状态**: 更新摄像头开关状态
- **屏幕共享状态**: 更新屏幕共享状态
- **类型安全**: 根据类型更新对应状态

## 使用场景

### 1. 会话状态管理

```javascript
// 会话状态管理器
const SessionStateManager = {
    createSession: (sessionData) => {
        const session = RtcSession.insert(sessionData);
        
        // 初始化媒体元素
        SessionStateManager.initializeMediaElements(session);
        
        // 设置状态监听
        SessionStateManager.setupStateListeners(session);
        
        // 初始化连接监控
        SessionStateManager.setupConnectionMonitoring(session);
        
        return session;
    },
    
    initializeMediaElements: (session) => {
        // 创建音频元素
        session.audioElement = document.createElement('audio');
        session.audioElement.autoplay = true;
        session.audioElement.playsInline = true;
        
        // 初始化视频流映射
        session.videoStreams = new Map();
        
        // 设置默认音量
        session.localVolume = 1.0;
    },
    
    setupStateListeners: (session) => {
        // 监听说话状态变化
        session.addEventListener('isTalking', (isTalking) => {
            SessionStateManager.handleTalkingStateChange(session, isTalking);
        });
        
        // 监听视频状态变化
        session.addEventListener('isVideoStreaming', (isStreaming) => {
            SessionStateManager.handleVideoStateChange(session, isStreaming);
        });
        
        // 监听静音状态变化
        session.addEventListener('isMute', (isMute) => {
            SessionStateManager.handleMuteStateChange(session, isMute);
        });
    },
    
    setupConnectionMonitoring: (session) => {
        if (session.peerConnection) {
            session.peerConnection.addEventListener('connectionstatechange', () => {
                session.connectionState = session.peerConnection.connectionState;
                SessionStateManager.handleConnectionStateChange(session);
            });
            
            session.peerConnection.addEventListener('iceconnectionstatechange', () => {
                session.iceState = session.peerConnection.iceConnectionState;
            });
            
            session.peerConnection.addEventListener('icegatheringstatechange', () => {
                session.iceGatheringState = session.peerConnection.iceGatheringState;
            });
        }
    },
    
    handleTalkingStateChange: (session, isTalking) => {
        // 更新UI指示器
        const indicator = document.querySelector(`[data-session-id="${session.id}"] .talking-indicator`);
        if (indicator) {
            indicator.classList.toggle('active', isTalking);
        }
        
        // 记录说话统计
        if (isTalking) {
            SessionStateManager.recordTalkingStart(session);
        } else {
            SessionStateManager.recordTalkingEnd(session);
        }
    },
    
    handleVideoStateChange: (session, isStreaming) => {
        // 更新视频容器显示
        const videoContainer = document.querySelector(`[data-session-id="${session.id}"] .video-container`);
        if (videoContainer) {
            videoContainer.classList.toggle('has-video', isStreaming);
        }
        
        // 通知布局管理器
        SessionStateManager.notifyLayoutManager(session, 'video_state_changed', isStreaming);
    },
    
    handleMuteStateChange: (session, isMute) => {
        // 更新静音指示器
        const muteIndicator = document.querySelector(`[data-session-id="${session.id}"] .mute-indicator`);
        if (muteIndicator) {
            muteIndicator.classList.toggle('muted', isMute);
        }
        
        // 更新音频元素
        if (session.audioElement) {
            session.audioElement.muted = isMute;
        }
    },
    
    handleConnectionStateChange: (session) => {
        const state = session.connectionState;
        
        // 更新连接状态指示器
        const statusIndicator = document.querySelector(`[data-session-id="${session.id}"] .connection-status`);
        if (statusIndicator) {
            statusIndicator.className = `connection-status ${state}`;
            statusIndicator.textContent = SessionStateManager.getConnectionStatusText(state);
        }
        
        // 处理连接问题
        if (state === 'failed' || state === 'disconnected') {
            SessionStateManager.handleConnectionIssue(session);
        }
    },
    
    getConnectionStatusText: (state) => {
        const statusMap = {
            'new': '新建',
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '已断开',
            'failed': '连接失败',
            'closed': '已关闭'
        };
        return statusMap[state] || '未知';
    },
    
    handleConnectionIssue: (session) => {
        // 显示连接问题提示
        showNotification(`与 ${session.name} 的连接出现问题`, 'warning');
        
        // 尝试重新连接
        setTimeout(() => {
            SessionStateManager.attemptReconnection(session);
        }, 3000);
    },
    
    attemptReconnection: async (session) => {
        try {
            // 重启ICE连接
            if (session.peerConnection) {
                await session.peerConnection.restartIce();
            }
        } catch (error) {
            console.error('重新连接失败:', error);
        }
    },
    
    recordTalkingStart: (session) => {
        session.talkingStartTime = Date.now();
    },
    
    recordTalkingEnd: (session) => {
        if (session.talkingStartTime) {
            const duration = Date.now() - session.talkingStartTime;
            SessionStateManager.addTalkingRecord(session, duration);
            session.talkingStartTime = null;
        }
    },
    
    addTalkingRecord: (session, duration) => {
        if (!session.talkingHistory) {
            session.talkingHistory = [];
        }
        
        session.talkingHistory.push({
            duration,
            timestamp: Date.now()
        });
        
        // 限制历史记录数量
        if (session.talkingHistory.length > 100) {
            session.talkingHistory.shift();
        }
    },
    
    notifyLayoutManager: (session, event, data) => {
        const customEvent = new CustomEvent('session_state_changed', {
            detail: {
                session,
                event,
                data
            }
        });
        document.dispatchEvent(customEvent);
    }
};
```

### 2. 媒体流管理

```javascript
// 媒体流管理器
const MediaStreamManager = {
    addVideoStream: (session, type, stream) => {
        session.videoStreams.set(type, stream);
        
        // 设置主视频流类型
        if (!session.mainVideoStreamType || type === 'screen') {
            session.mainVideoStreamType = type;
        }
        
        // 更新流状态
        session.updateStreamState(type, true);
        
        // 通知UI更新
        MediaStreamManager.notifyStreamAdded(session, type, stream);
    },
    
    removeVideoStream: (session, type) => {
        const stream = session.videoStreams.get(type);
        if (stream) {
            // 停止所有轨道
            stream.getTracks().forEach(track => track.stop());
            
            // 从映射中移除
            session.videoStreams.delete(type);
            
            // 更新流状态
            session.updateStreamState(type, false);
            
            // 更新主视频流类型
            if (session.mainVideoStreamType === type) {
                session.mainVideoStreamType = session.videoStreams.size > 0 
                    ? session.videoStreams.keys().next().value 
                    : null;
            }
            
            // 通知UI更新
            MediaStreamManager.notifyStreamRemoved(session, type);
        }
    },
    
    setAudioStream: (session, stream) => {
        session.audioStream = stream;
        
        if (session.audioElement) {
            session.audioElement.srcObject = stream;
            session.playAudio();
        }
    },
    
    updateStreamQuality: (session, type, quality) => {
        const stream = session.getStream(type);
        if (!stream) return;
        
        const videoTrack = stream.getVideoTracks()[0];
        if (!videoTrack) return;
        
        try {
            videoTrack.applyConstraints(quality);
        } catch (error) {
            console.error('更新流质量失败:', error);
        }
    },
    
    notifyStreamAdded: (session, type, stream) => {
        const event = new CustomEvent('stream_added', {
            detail: { session, type, stream }
        });
        document.dispatchEvent(event);
    },
    
    notifyStreamRemoved: (session, type) => {
        const event = new CustomEvent('stream_removed', {
            detail: { session, type }
        });
        document.dispatchEvent(event);
    }
};
```

### 3. 会话信息获取

```javascript
// 会话信息管理器
const SessionInfoManager = {
    getSessionInfo: (session) => {
        return {
            ...session.info,
            name: session.name,
            volume: session.volume,
            connectionState: session.connectionState,
            hasVideo: session.hasVideo,
            mainVideoStreamType: session.mainVideoStreamType,
            talkingTime: session.talkingTime,
            errors: {
                audio: session.audioError,
                video: session.videoError
            }
        };
    },
    
    getSessionStats: (session) => {
        return {
            id: session.id,
            name: session.name,
            connectionState: session.connectionState,
            iceState: session.iceState,
            dtlsState: session.dtlsState,
            packetsReceived: session.packetsReceived,
            packetsSent: session.packetsSent,
            localCandidateType: session.localCandidateType,
            remoteCandidateType: session.remoteCandidateType,
            dataChannelState: session.dataChannelState
        };
    },
    
    getMediaInfo: (session) => {
        const mediaInfo = {
            audio: {
                hasStream: !!session.audioStream,
                volume: session.volume,
                isMuted: session.isMute,
                error: session.audioError
            },
            video: {
                streams: {},
                mainType: session.mainVideoStreamType,
                error: session.videoError
            }
        };
        
        // 添加视频流信息
        session.videoStreams.forEach((stream, type) => {
            const videoTrack = stream.getVideoTracks()[0];
            mediaInfo.video.streams[type] = {
                hasTrack: !!videoTrack,
                settings: videoTrack?.getSettings(),
                constraints: videoTrack?.getConstraints()
            };
        });
        
        return mediaInfo;
    }
};
```

## 技术特点

### 1. 数据模型设计
- 继承Record基类
- 计算字段支持
- 关联关系管理

### 2. 状态管理
- 响应式状态更新
- 计算属性自动更新
- 事件驱动更新

### 3. 媒体集成
- HTML媒体元素管理
- WebRTC流对象
- 错误状态跟踪

### 4. 连接监控
- WebRTC连接状态
- ICE连接监控
- 数据通道管理

## 设计模式

### 1. 模型模式 (Model Pattern)
- 数据抽象和封装
- 业务逻辑集中管理

### 2. 观察者模式 (Observer Pattern)
- 状态变化通知
- 事件驱动更新

### 3. 策略模式 (Strategy Pattern)
- 不同流类型的处理策略
- 状态计算策略

## 注意事项

1. **内存管理**: 及时清理媒体流和连接
2. **状态同步**: 保持模型状态与实际状态一致
3. **错误处理**: 完善的错误状态管理
4. **性能优化**: 避免频繁的状态计算

## 扩展建议

1. **统计分析**: 添加更多会话统计信息
2. **质量监控**: 集成媒体质量监控
3. **历史记录**: 扩展会话历史功能
4. **权限管理**: 添加会话权限控制
5. **数据持久化**: 支持会话数据持久化

该模型是视频通话系统中的核心数据抽象，提供了完整的会话状态管理功能。

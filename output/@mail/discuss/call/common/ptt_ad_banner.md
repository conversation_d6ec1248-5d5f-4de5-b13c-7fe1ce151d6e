# PTT Ad Banner - PTT广告横幅

## 概述

`ptt_ad_banner.js` 实现了 Odoo 讨论应用中的PTT(推送通话)扩展广告横幅组件，用于向用户推广Chrome浏览器扩展程序。该组件支持智能显示条件判断、用户关闭状态记忆、移动设备检测、本地存储管理等功能，集成了PTT扩展服务和设置状态，提供了用户友好的扩展推广界面，是视频通话系统中扩展推广的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/ptt_ad_banner.js`
- **行数**: 43
- **模块**: `@mail/discuss/call/common/ptt_ad_banner`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                               // OWL 框架
'@web/core/browser/browser'               // 浏览器服务
'@web/core/browser/feature_detection'     // 浏览器特性检测
'@web/core/utils/hooks'                   // Web 核心钩子
```

## 组件定义

### PttAdBanner 类

```javascript
class PttAdBanner extends Component {
    static template = "discuss.pttAdBanner";
    static props = {};
    static LOCAL_STORAGE_KEY = "ptt_ad_banner_discarded";
}
```

**组件特性**:
- 继承自Component基类
- 使用专用模板
- 无需外部属性
- 本地存储键定义

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.pttExtService = useState(useService("discuss.ptt_extension"));
    this.store = useState(useService("mail.store"));
    this.state = useState({
        wasDiscarded: browser.localStorage.getItem(PttAdBanner.LOCAL_STORAGE_KEY),
    });
}
```

**初始化内容**:
- PTT扩展服务状态管理
- 邮件存储服务集成
- 本地关闭状态加载

## 核心功能

### 1. 关闭操作

```javascript
onClickClose() {
    browser.localStorage.setItem(PttAdBanner.LOCAL_STORAGE_KEY, true);
    this.state.wasDiscarded = true;
}
```

**关闭功能**:
- **本地存储**: 保存用户关闭状态到本地存储
- **状态更新**: 更新组件内部关闭状态
- **持久化**: 确保用户选择的持久性

### 2. 可见性判断

```javascript
get isVisible() {
    return (
        !this.pttExtService.isEnabled &&
        this.store.settings.use_push_to_talk &&
        !isMobileOS() &&
        !this.state.wasDiscarded
    );
}
```

**可见性条件**:
- **扩展未启用**: PTT扩展服务未启用
- **PTT设置开启**: 用户开启了推送通话设置
- **非移动设备**: 不在移动操作系统上
- **未被关闭**: 用户未主动关闭横幅

## 使用场景

### 1. 广告横幅增强

```javascript
// 广告横幅增强功能
const PTTAdBannerEnhancer = {
    enhancePTTAdBanner: () => {
        const EnhancedPTTAdBanner = class extends PttAdBanner {
            setup() {
                super.setup();
                
                // 添加增强状态
                this.enhancedState = useState({
                    showCount: 0,
                    lastShownTime: null,
                    userInteractions: [],
                    dismissalReason: null
                });
                
                // 加载增强状态
                this.loadEnhancedState();
                
                // 设置显示跟踪
                this.setupDisplayTracking();
                
                // 设置用户行为分析
                this.setupUserBehaviorAnalysis();
            },
            
            // 加载增强状态
            loadEnhancedState() {
                const savedState = browser.localStorage.getItem('ptt_ad_banner_enhanced_state');
                if (savedState) {
                    try {
                        const parsed = JSON.parse(savedState);
                        Object.assign(this.enhancedState, parsed);
                    } catch (error) {
                        console.warn('加载PTT横幅增强状态失败:', error);
                    }
                }
            },
            
            // 保存增强状态
            saveEnhancedState() {
                try {
                    browser.localStorage.setItem(
                        'ptt_ad_banner_enhanced_state',
                        JSON.stringify(this.enhancedState)
                    );
                } catch (error) {
                    console.warn('保存PTT横幅增强状态失败:', error);
                }
            },
            
            // 设置显示跟踪
            setupDisplayTracking() {
                // 监听可见性变化
                const observer = new MutationObserver(() => {
                    if (this.isVisible && this.el && this.el.offsetParent !== null) {
                        this.onBannerShown();
                    }
                });
                
                onMounted(() => {
                    observer.observe(document.body, { childList: true, subtree: true });
                    
                    if (this.isVisible) {
                        this.onBannerShown();
                    }
                });
                
                onWillUnmount(() => {
                    observer.disconnect();
                });
            },
            
            // 横幅显示处理
            onBannerShown() {
                this.enhancedState.showCount++;
                this.enhancedState.lastShownTime = Date.now();
                this.saveEnhancedState();
                
                // 记录显示事件
                this.recordUserInteraction('banner_shown');
                
                // 发送分析数据
                this.sendAnalyticsEvent('ptt_banner_shown', {
                    showCount: this.enhancedState.showCount,
                    userAgent: navigator.userAgent
                });
            },
            
            // 增强的关闭操作
            onClickClose(reason = 'user_close') {
                // 记录关闭原因
                this.enhancedState.dismissalReason = reason;
                this.recordUserInteraction('banner_closed', { reason });
                
                // 调用原始关闭逻辑
                super.onClickClose();
                
                // 保存增强状态
                this.saveEnhancedState();
                
                // 发送分析数据
                this.sendAnalyticsEvent('ptt_banner_closed', {
                    reason: reason,
                    showCount: this.enhancedState.showCount,
                    timeShown: this.calculateTimeShown()
                });
            },
            
            // 计算显示时间
            calculateTimeShown() {
                if (this.enhancedState.lastShownTime) {
                    return Date.now() - this.enhancedState.lastShownTime;
                }
                return 0;
            },
            
            // 记录用户交互
            recordUserInteraction(action, data = {}) {
                this.enhancedState.userInteractions.push({
                    action: action,
                    timestamp: Date.now(),
                    data: data
                });
                
                // 限制交互记录数量
                if (this.enhancedState.userInteractions.length > 50) {
                    this.enhancedState.userInteractions.shift();
                }
            },
            
            // 设置用户行为分析
            setupUserBehaviorAnalysis() {
                // 监听鼠标悬停
                onMounted(() => {
                    if (this.el) {
                        this.el.addEventListener('mouseenter', () => {
                            this.recordUserInteraction('banner_hover_start');
                        });
                        
                        this.el.addEventListener('mouseleave', () => {
                            this.recordUserInteraction('banner_hover_end');
                        });
                        
                        // 监听点击事件
                        this.el.addEventListener('click', (event) => {
                            if (event.target.classList.contains('download-link')) {
                                this.recordUserInteraction('download_link_clicked');
                                this.sendAnalyticsEvent('ptt_extension_download_clicked');
                            }
                        });
                    }
                });
            },
            
            // 发送分析事件
            sendAnalyticsEvent(eventName, data = {}) {
                // 这里可以集成Google Analytics或其他分析服务
                if (window.gtag) {
                    window.gtag('event', eventName, data);
                }
                
                // 或者发送到自定义分析端点
                try {
                    fetch('/web/analytics/track', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            event: eventName,
                            data: data,
                            timestamp: Date.now()
                        })
                    });
                } catch (error) {
                    console.warn('发送分析事件失败:', error);
                }
            },
            
            // 增强的可见性判断
            get isVisible() {
                // 调用原始可见性判断
                const baseVisible = super.isVisible;
                
                if (!baseVisible) return false;
                
                // 添加额外的显示条件
                return this.shouldShowBasedOnUserBehavior();
            },
            
            // 基于用户行为的显示判断
            shouldShowBasedOnUserBehavior() {
                // 如果显示次数过多，减少显示频率
                if (this.enhancedState.showCount > 10) {
                    const daysSinceLastShow = this.getDaysSinceLastShow();
                    return daysSinceLastShow >= 7; // 一周后再显示
                }
                
                // 如果用户最近关闭过，等待一段时间
                if (this.enhancedState.dismissalReason === 'user_close') {
                    const daysSinceLastShow = this.getDaysSinceLastShow();
                    return daysSinceLastShow >= 3; // 3天后再显示
                }
                
                return true;
            },
            
            // 获取距离上次显示的天数
            getDaysSinceLastShow() {
                if (!this.enhancedState.lastShownTime) return Infinity;
                
                const daysDiff = (Date.now() - this.enhancedState.lastShownTime) / (1000 * 60 * 60 * 24);
                return Math.floor(daysDiff);
            },
            
            // 获取用户交互统计
            getUserInteractionStats() {
                const interactions = this.enhancedState.userInteractions;
                
                const stats = {
                    totalInteractions: interactions.length,
                    hoverCount: interactions.filter(i => i.action === 'banner_hover_start').length,
                    clickCount: interactions.filter(i => i.action === 'download_link_clicked').length,
                    closeCount: interactions.filter(i => i.action === 'banner_closed').length,
                    showCount: this.enhancedState.showCount
                };
                
                return stats;
            }
        };
        
        // 替换原始组件
        __exports.PttAdBanner = EnhancedPTTAdBanner;
    }
};

// 应用PTT广告横幅增强
PTTAdBannerEnhancer.enhancePTTAdBanner();
```

### 2. A/B测试支持

```javascript
// A/B测试支持
const PTTAdBannerABTest = {
    setupABTesting: () => {
        const ABTestPatch = {
            setup() {
                super.setup();
                
                // 获取A/B测试变体
                this.abTestVariant = PTTAdBannerABTest.getABTestVariant();
                
                // 应用变体样式
                this.applyVariantStyles();
            },
            
            // 应用变体样式
            applyVariantStyles() {
                onMounted(() => {
                    if (this.el) {
                        this.el.classList.add(`variant-${this.abTestVariant}`);
                        
                        // 根据变体调整内容
                        PTTAdBannerABTest.applyVariantContent(this.el, this.abTestVariant);
                    }
                });
            },
            
            // 记录A/B测试事件
            recordABTestEvent(action) {
                this.sendAnalyticsEvent('ptt_banner_ab_test', {
                    variant: this.abTestVariant,
                    action: action
                });
            }
        };
        
        patch(PttAdBanner.prototype, ABTestPatch);
    },
    
    // 获取A/B测试变体
    getABTestVariant() {
        // 检查是否已有保存的变体
        let variant = browser.localStorage.getItem('ptt_banner_ab_variant');
        
        if (!variant) {
            // 随机分配变体
            const variants = ['control', 'variant_a', 'variant_b'];
            variant = variants[Math.floor(Math.random() * variants.length)];
            
            // 保存变体
            browser.localStorage.setItem('ptt_banner_ab_variant', variant);
        }
        
        return variant;
    },
    
    // 应用变体内容
    applyVariantContent(element, variant) {
        const titleElement = element.querySelector('.banner-title');
        const descElement = element.querySelector('.banner-description');
        const buttonElement = element.querySelector('.download-button');
        
        switch (variant) {
            case 'variant_a':
                if (titleElement) titleElement.textContent = '提升通话体验！';
                if (descElement) descElement.textContent = '安装PTT扩展，享受更便捷的推送通话功能';
                if (buttonElement) buttonElement.textContent = '立即安装';
                break;
                
            case 'variant_b':
                if (titleElement) titleElement.textContent = '专业通话工具';
                if (descElement) descElement.textContent = '使用PTT扩展，让您的通话更加专业高效';
                if (buttonElement) buttonElement.textContent = '免费下载';
                break;
                
            default: // control
                // 保持原始内容
                break;
        }
    }
};

// 应用A/B测试
PTTAdBannerABTest.setupABTesting();
```

### 3. 智能显示策略

```javascript
// 智能显示策略
const SmartDisplayStrategy = {
    setupSmartDisplay: () => {
        const SmartDisplayPatch = {
            setup() {
                super.setup();
                
                // 智能显示状态
                this.smartDisplayState = useState({
                    userEngagement: 0,
                    optimalShowTime: null,
                    contextScore: 0
                });
                
                // 计算最佳显示时机
                this.calculateOptimalShowTime();
            },
            
            // 计算最佳显示时机
            calculateOptimalShowTime() {
                // 分析用户活动模式
                const userActivity = SmartDisplayStrategy.analyzeUserActivity();
                
                // 计算上下文得分
                this.smartDisplayState.contextScore = SmartDisplayStrategy.calculateContextScore();
                
                // 确定最佳显示时间
                this.smartDisplayState.optimalShowTime = SmartDisplayStrategy.getOptimalTime(userActivity);
            },
            
            // 智能可见性判断
            get isVisible() {
                const baseVisible = super.isVisible;
                if (!baseVisible) return false;
                
                // 检查是否为最佳显示时机
                return SmartDisplayStrategy.isOptimalTime(this.smartDisplayState);
            }
        };
        
        patch(PttAdBanner.prototype, SmartDisplayPatch);
    },
    
    // 分析用户活动
    analyzeUserActivity() {
        const now = new Date();
        const hour = now.getHours();
        
        // 基于时间的活动模式
        const timeScore = SmartDisplayStrategy.getTimeScore(hour);
        
        // 基于使用频率的活动模式
        const usageScore = SmartDisplayStrategy.getUsageScore();
        
        return {
            timeScore,
            usageScore,
            overallScore: (timeScore + usageScore) / 2
        };
    },
    
    // 获取时间得分
    getTimeScore(hour) {
        // 工作时间得分更高
        if (hour >= 9 && hour <= 17) {
            return 0.8;
        } else if (hour >= 19 && hour <= 22) {
            return 0.6;
        } else {
            return 0.3;
        }
    },
    
    // 获取使用频率得分
    getUsageScore() {
        const rtcService = useService("discuss.rtc");
        const recentCalls = rtcService.getRecentCallHistory(7); // 最近7天
        
        if (recentCalls.length > 5) {
            return 0.9; // 高频用户
        } else if (recentCalls.length > 2) {
            return 0.7; // 中频用户
        } else {
            return 0.4; // 低频用户
        }
    },
    
    // 计算上下文得分
    calculateContextScore() {
        let score = 0;
        
        // 当前是否在通话中
        const rtcService = useService("discuss.rtc");
        if (rtcService.state.channel) {
            score += 0.3; // 通话中显示PTT广告更相关
        }
        
        // 是否在讨论页面
        if (window.location.pathname.includes('/web/discuss')) {
            score += 0.2;
        }
        
        // 是否有活跃的聊天
        const store = useService("mail.store");
        if (store.discuss.activeThread) {
            score += 0.1;
        }
        
        return Math.min(score, 1.0);
    },
    
    // 获取最佳显示时间
    getOptimalTime(userActivity) {
        // 基于用户活动模式确定最佳时间
        if (userActivity.overallScore > 0.7) {
            return Date.now(); // 立即显示
        } else {
            // 延迟到更合适的时间
            return Date.now() + (30 * 60 * 1000); // 30分钟后
        }
    },
    
    // 检查是否为最佳显示时机
    isOptimalTime(smartState) {
        if (!smartState.optimalShowTime) return true;
        
        const now = Date.now();
        return now >= smartState.optimalShowTime && smartState.contextScore > 0.5;
    }
};

// 应用智能显示策略
SmartDisplayStrategy.setupSmartDisplay();
```

## 技术特点

### 1. 智能显示
- 多条件判断逻辑
- 用户状态感知
- 设备类型检测

### 2. 状态持久化
- 本地存储集成
- 用户选择记忆
- 状态同步管理

### 3. 服务集成
- PTT扩展服务集成
- 设置状态监听
- 响应式状态管理

### 4. 用户体验
- 非侵入式显示
- 用户控制权
- 智能隐藏机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的广告组件
- 清晰的属性接口

### 2. 状态模式 (State Pattern)
- 显示状态管理
- 条件驱动显示

### 3. 策略模式 (Strategy Pattern)
- 不同显示策略
- 智能显示算法

## 注意事项

1. **用户体验**: 避免过度打扰用户
2. **性能影响**: 最小化组件性能开销
3. **隐私保护**: 合理使用本地存储
4. **移动适配**: 正确检测移动设备

## 扩展建议

1. **个性化**: 基于用户行为的个性化显示
2. **A/B测试**: 支持不同版本的效果测试
3. **分析统计**: 添加显示效果分析
4. **多语言**: 增强国际化支持
5. **动画效果**: 添加吸引人的动画效果

该组件为视频通话系统提供了用户友好的扩展推广功能，是扩展推广策略的重要组成部分。

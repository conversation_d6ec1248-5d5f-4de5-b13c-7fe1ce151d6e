# Call Actions - 通话操作定义

## 概述

`call_actions.js` 实现了 Odoo 讨论应用中通话操作的定义和注册系统，负责管理所有可用的通话操作，包括静音、摄像头、屏幕共享、举手、全屏等功能。该模块使用注册表模式来管理操作，支持条件显示、动态名称、状态检测、图标切换等功能，提供了可扩展的通话操作框架，是通话控制系统的核心配置中心。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/common/call_actions.js`
- **行数**: 144
- **模块**: `@mail/discuss/call/common/call_actions`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                            // OWL 框架
'@web/core/browser/feature_detection'  // 浏览器特性检测
'@web/core/l10n/translation'           // 国际化
'@web/core/registry'                   // 注册表系统
```

## 核心组件

### 操作注册表

```javascript
const callActionsRegistry = registry.category("discuss.call/actions");
```

**注册表特性**:
- 基于Odoo注册表系统
- 支持动态操作注册
- 可扩展的操作管理

## 预定义操作

### 1. 静音操作 (mute)

```javascript
.add("mute", {
    condition: (component) => component.rtc,
    name: (component) => (component.rtc.selfSession.isMute ? _t("Unmute") : _t("Mute")),
    isActive: (component) => component.rtc.selfSession?.isMute,
    inactiveIcon: "fa-microphone",
    icon: "fa-microphone-slash",
    activeClass: "text-danger",
    select: (component) => {
        if (component.rtc.selfSession.isMute) {
            if (component.rtc.selfSession.isSelfMuted) {
                component.rtc.unmute();
            }
            if (component.rtc.selfSession.isDeaf) {
                component.rtc.undeafen();
            }
        } else {
            component.rtc.mute();
        }
    },
    sequence: 10,
})
```

**操作特性**:
- **条件**: 需要RTC服务可用
- **动态名称**: 根据当前状态显示"静音"或"取消静音"
- **状态检测**: 检测当前是否静音
- **图标切换**: 静音和非静音状态的不同图标
- **复合操作**: 同时处理静音和耳聋状态

### 2. 耳聋操作 (deafen)

```javascript
.add("deafen", {
    condition: (component) => component.rtc,
    name: (component) => (component.rtc.selfSession.isDeaf ? _t("Undeafen") : _t("Deafen")),
    isActive: (component) => component.rtc.selfSession?.isDeaf,
    inactiveIcon: "fa-headphones",
    icon: "fa-deaf",
    activeClass: "text-danger",
    select: (component) =>
        component.rtc.selfSession.isDeaf ? component.rtc.undeafen() : component.rtc.deafen(),
    sequence: 20,
})
```

**操作特性**:
- **音频控制**: 控制音频输出
- **状态切换**: 耳聋和非耳聋状态切换
- **危险样式**: 激活时显示危险颜色

### 3. 摄像头操作 (camera-on)

```javascript
.add("camera-on", {
    condition: (component) => component.rtc,
    name: (component) =>
        component.rtc.selfSession.isCameraOn ? _t("Stop camera") : _t("Turn camera on"),
    isActive: (component) => component.rtc.selfSession?.isCameraOn,
    icon: "fa-video-camera",
    activeClass: "text-success",
    select: (component) => component.rtc.toggleVideo("camera"),
    sequence: 30,
})
```

**操作特性**:
- **视频控制**: 控制摄像头开关
- **成功样式**: 激活时显示成功颜色
- **视频切换**: 调用RTC服务的视频切换方法

### 4. 举手操作 (raise-hand)

```javascript
.add("raise-hand", {
    condition: (component) => component.rtc,
    name: (component) =>
        component.rtc.selfSession.raisingHand ? _t("Lower Hand") : _t("Raise Hand"),
    isActive: (component) => component.rtc.selfSession?.raisingHand,
    icon: "fa-hand-paper-o",
    select: (component) => component.rtc.raiseHand(!component.rtc.selfSession.raisingHand),
    sequence: 40,
})
```

**操作特性**:
- **举手功能**: 会议中的举手发言
- **状态切换**: 举手和放下手的切换
- **布尔切换**: 使用当前状态的反值

### 5. 屏幕共享操作 (share-screen)

```javascript
.add("share-screen", {
    condition: (component) => component.rtc && !isMobileOS(),
    name: (component) =>
        component.rtc.selfSession.isScreenSharingOn
            ? _t("Stop Sharing Screen")
            : _t("Share Screen"),
    isActive: (component) => component.rtc.selfSession?.isScreenSharingOn,
    icon: "fa-desktop",
    select: (component) => component.rtc.toggleVideo("screen"),
    sequence: 50,
})
```

**操作特性**:
- **平台限制**: 仅在非移动设备上可用
- **屏幕共享**: 控制屏幕共享功能
- **桌面图标**: 使用桌面图标表示

### 6. 全屏操作 (fullscreen)

```javascript
.add("fullscreen", {
    condition: (component) => component.props && component.props.fullscreen,
    name: (component) =>
        component.props.fullscreen.isActive ? _t("Exit Fullscreen") : _t("Enter Full Screen"),
    isActive: (component) => component.props.fullscreen.isActive,
    inactiveIcon: "fa-arrows-alt",
    icon: "fa-compress",
    select: (component) => {
        if (component.props.fullscreen.isActive) {
            component.props.fullscreen.exit();
        } else {
            component.props.fullscreen.enter();
        }
    },
    sequence: 60,
})
```

**操作特性**:
- **全屏控制**: 进入和退出全屏模式
- **双图标**: 不同状态使用不同图标
- **条件检查**: 需要全屏属性可用

## 核心函数

### 1. 操作转换函数

```javascript
function transformAction(component, id, action) {
    return {
        id,
        get condition() {
            return action.condition(component);
        },
        get name() {
            return typeof action.name === "function" ? action.name(component) : action.name;
        },
        get isActive() {
            return action.isActive(component);
        },
        inactiveIcon: action.inactiveIcon,
        get icon() {
            return typeof action.icon === "function" ? action.icon(component) : action.icon;
        },
        activeClass: action.activeClass,
        select() {
            action.select(component);
        },
        get sequence() {
            return typeof action.sequence === "function"
                ? action.sequence(component)
                : action.sequence;
        },
    };
}
```

**转换特性**:
- **动态属性**: 支持函数和静态值
- **组件绑定**: 将组件实例绑定到操作
- **Getter模式**: 使用getter实现动态计算

### 2. 操作钩子函数

```javascript
function useCallActions() {
    const component = useComponent();
    const state = useState({ actions: [] });
    state.actions = callActionsRegistry
        .getEntries()
        .map(([id, action]) => transformAction(component, id, action));
    return {
        get actions() {
            return state.actions
                .filter((action) => action.condition)
                .sort((a1, a2) => a1.sequence - a2.sequence);
        },
    };
}
```

**钩子特性**:
- **组件集成**: 与OWL组件系统集成
- **状态管理**: 使用响应式状态
- **条件过滤**: 只返回满足条件的操作
- **序列排序**: 按序列号排序操作

## 使用场景

### 1. 自定义操作注册

```javascript
// 自定义通话操作注册
const CustomCallActions = {
    registerCustomActions: () => {
        // 注册录制操作
        callActionsRegistry.add("record", {
            condition: (component) => component.rtc && component.hasRecordPermission,
            name: (component) => component.rtc.isRecording ? _t("Stop Recording") : _t("Start Recording"),
            isActive: (component) => component.rtc.isRecording,
            icon: "fa-circle",
            activeClass: "text-danger",
            select: (component) => CustomCallActions.toggleRecording(component),
            sequence: 70,
        });
        
        // 注册背景模糊操作
        callActionsRegistry.add("blur-background", {
            condition: (component) => component.rtc && component.supportsBackgroundBlur,
            name: (component) => component.rtc.isBackgroundBlurred ? _t("Disable Blur") : _t("Enable Blur"),
            isActive: (component) => component.rtc.isBackgroundBlurred,
            icon: "fa-magic",
            activeClass: "text-info",
            select: (component) => CustomCallActions.toggleBackgroundBlur(component),
            sequence: 35,
        });
        
        // 注册虚拟背景操作
        callActionsRegistry.add("virtual-background", {
            condition: (component) => component.rtc && component.supportsVirtualBackground,
            name: () => _t("Virtual Background"),
            isActive: (component) => component.rtc.hasVirtualBackground,
            icon: "fa-image",
            select: (component) => CustomCallActions.showVirtualBackgroundSelector(component),
            sequence: 36,
        });
        
        // 注册美颜操作
        callActionsRegistry.add("beauty-filter", {
            condition: (component) => component.rtc && component.supportsBeautyFilter,
            name: (component) => component.rtc.isBeautyFilterOn ? _t("Disable Beauty") : _t("Enable Beauty"),
            isActive: (component) => component.rtc.isBeautyFilterOn,
            icon: "fa-star",
            activeClass: "text-warning",
            select: (component) => CustomCallActions.toggleBeautyFilter(component),
            sequence: 37,
        });
        
        // 注册聊天操作
        callActionsRegistry.add("toggle-chat", {
            condition: (component) => component.rtc,
            name: () => _t("Chat"),
            isActive: (component) => component.isChatVisible,
            icon: "fa-comment",
            select: (component) => CustomCallActions.toggleChat(component),
            sequence: 80,
        });
        
        // 注册参与者列表操作
        callActionsRegistry.add("participants", {
            condition: (component) => component.rtc,
            name: () => _t("Participants"),
            isActive: (component) => component.isParticipantsVisible,
            icon: "fa-users",
            select: (component) => CustomCallActions.toggleParticipants(component),
            sequence: 90,
        });
    },
    
    toggleRecording: async (component) => {
        try {
            if (component.rtc.isRecording) {
                await component.rtc.stopRecording();
                showNotification('录制已停止', 'info');
            } else {
                await component.rtc.startRecording();
                showNotification('录制已开始', 'success');
            }
        } catch (error) {
            console.error('录制操作失败:', error);
            showNotification('录制操作失败', 'error');
        }
    },
    
    toggleBackgroundBlur: async (component) => {
        try {
            if (component.rtc.isBackgroundBlurred) {
                await component.rtc.disableBackgroundBlur();
                showNotification('背景模糊已关闭', 'info');
            } else {
                await component.rtc.enableBackgroundBlur();
                showNotification('背景模糊已开启', 'success');
            }
        } catch (error) {
            console.error('背景模糊操作失败:', error);
            showNotification('背景模糊操作失败', 'error');
        }
    },
    
    showVirtualBackgroundSelector: (component) => {
        const selector = CustomCallActions.createVirtualBackgroundSelector(component);
        document.body.appendChild(selector);
    },
    
    createVirtualBackgroundSelector: (component) => {
        const selector = document.createElement('div');
        selector.className = 'virtual-background-selector-overlay';
        selector.innerHTML = `
            <div class="background-selector">
                <div class="selector-header">
                    <h3>选择虚拟背景</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="background-options">
                    <div class="background-option" data-type="none">
                        <div class="option-preview no-background"></div>
                        <span class="option-name">无背景</span>
                    </div>
                    
                    <div class="background-option" data-type="blur">
                        <div class="option-preview blur-background"></div>
                        <span class="option-name">模糊背景</span>
                    </div>
                    
                    <div class="background-option" data-type="office">
                        <div class="option-preview office-background"></div>
                        <span class="option-name">办公室</span>
                    </div>
                    
                    <div class="background-option" data-type="home">
                        <div class="option-preview home-background"></div>
                        <span class="option-name">居家</span>
                    </div>
                    
                    <div class="background-option" data-type="nature">
                        <div class="option-preview nature-background"></div>
                        <span class="option-name">自然</span>
                    </div>
                    
                    <div class="background-option upload-option">
                        <div class="option-preview upload-background">
                            <i class="fa fa-plus"></i>
                        </div>
                        <span class="option-name">上传图片</span>
                        <input type="file" accept="image/*" class="upload-input" style="display: none;">
                    </div>
                </div>
            </div>
        `;
        
        CustomCallActions.bindBackgroundSelectorEvents(selector, component);
        return selector;
    },
    
    bindBackgroundSelectorEvents: (selector, component) => {
        // 关闭选择器
        selector.querySelector('.close-btn').addEventListener('click', () => {
            selector.remove();
        });
        
        // 选择背景
        selector.querySelectorAll('.background-option').forEach(option => {
            option.addEventListener('click', async () => {
                const type = option.dataset.type;
                
                if (type === 'upload') {
                    option.querySelector('.upload-input').click();
                } else {
                    await CustomCallActions.applyVirtualBackground(component, type);
                    selector.remove();
                }
            });
        });
        
        // 上传自定义背景
        selector.querySelector('.upload-input').addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (file) {
                await CustomCallActions.uploadCustomBackground(component, file);
                selector.remove();
            }
        });
    },
    
    applyVirtualBackground: async (component, type) => {
        try {
            await component.rtc.setVirtualBackground(type);
            showNotification('虚拟背景已应用', 'success');
        } catch (error) {
            console.error('应用虚拟背景失败:', error);
            showNotification('虚拟背景应用失败', 'error');
        }
    },
    
    uploadCustomBackground: async (component, file) => {
        try {
            const imageUrl = await CustomCallActions.uploadImage(file);
            await component.rtc.setVirtualBackground('custom', imageUrl);
            showNotification('自定义背景已应用', 'success');
        } catch (error) {
            console.error('上传自定义背景失败:', error);
            showNotification('自定义背景上传失败', 'error');
        }
    },
    
    uploadImage: async (file) => {
        const formData = new FormData();
        formData.append('image', file);
        
        const response = await fetch('/web/binary/upload_attachment', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        return result.url;
    },
    
    toggleBeautyFilter: async (component) => {
        try {
            if (component.rtc.isBeautyFilterOn) {
                await component.rtc.disableBeautyFilter();
                showNotification('美颜已关闭', 'info');
            } else {
                await component.rtc.enableBeautyFilter();
                showNotification('美颜已开启', 'success');
            }
        } catch (error) {
            console.error('美颜操作失败:', error);
            showNotification('美颜操作失败', 'error');
        }
    },
    
    toggleChat: (component) => {
        component.isChatVisible = !component.isChatVisible;
        
        const chatPanel = document.querySelector('.chat-panel');
        if (chatPanel) {
            chatPanel.style.display = component.isChatVisible ? 'block' : 'none';
        }
    },
    
    toggleParticipants: (component) => {
        component.isParticipantsVisible = !component.isParticipantsVisible;
        
        const participantsPanel = document.querySelector('.participants-panel');
        if (participantsPanel) {
            participantsPanel.style.display = component.isParticipantsVisible ? 'block' : 'none';
        }
    }
};

// 注册自定义操作
CustomCallActions.registerCustomActions();
```

### 2. 条件操作管理

```javascript
// 条件操作管理
const ConditionalActionManager = {
    registerConditionalActions: () => {
        // 主持人专用操作
        callActionsRegistry.add("mute-all", {
            condition: (component) => component.rtc && component.isHost,
            name: () => _t("Mute All"),
            icon: "fa-microphone-slash",
            select: (component) => ConditionalActionManager.muteAllParticipants(component),
            sequence: 100,
        });
        
        // 管理员操作
        callActionsRegistry.add("kick-participant", {
            condition: (component) => component.rtc && component.isAdmin && component.selectedParticipant,
            name: () => _t("Remove Participant"),
            icon: "fa-user-times",
            activeClass: "text-danger",
            select: (component) => ConditionalActionManager.kickParticipant(component),
            sequence: 110,
        });
        
        // 高级用户操作
        callActionsRegistry.add("advanced-settings", {
            condition: (component) => component.rtc && component.hasAdvancedPermissions,
            name: () => _t("Advanced Settings"),
            icon: "fa-cogs",
            select: (component) => ConditionalActionManager.showAdvancedSettings(component),
            sequence: 120,
        });
    },
    
    muteAllParticipants: async (component) => {
        const confirmed = confirm('确定要静音所有参与者吗？');
        if (confirmed) {
            try {
                await component.rtc.muteAllParticipants();
                showNotification('已静音所有参与者', 'success');
            } catch (error) {
                console.error('静音所有参与者失败:', error);
                showNotification('操作失败', 'error');
            }
        }
    },
    
    kickParticipant: async (component) => {
        const participant = component.selectedParticipant;
        const confirmed = confirm(`确定要移除 ${participant.name} 吗？`);
        
        if (confirmed) {
            try {
                await component.rtc.kickParticipant(participant.id);
                showNotification(`已移除 ${participant.name}`, 'success');
            } catch (error) {
                console.error('移除参与者失败:', error);
                showNotification('移除失败', 'error');
            }
        }
    },
    
    showAdvancedSettings: (component) => {
        const settingsDialog = ConditionalActionManager.createAdvancedSettingsDialog(component);
        document.body.appendChild(settingsDialog);
    },
    
    createAdvancedSettingsDialog: (component) => {
        const dialog = document.createElement('div');
        dialog.className = 'advanced-settings-dialog-overlay';
        dialog.innerHTML = `
            <div class="advanced-settings-dialog">
                <div class="dialog-header">
                    <h3>高级设置</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="settings-tabs">
                        <button class="tab-btn active" data-tab="network">网络</button>
                        <button class="tab-btn" data-tab="media">媒体</button>
                        <button class="tab-btn" data-tab="security">安全</button>
                    </div>
                    
                    <div class="tab-content">
                        <div class="tab-panel active" id="network-panel">
                            <!-- 网络设置 -->
                        </div>
                        <div class="tab-panel" id="media-panel">
                            <!-- 媒体设置 -->
                        </div>
                        <div class="tab-panel" id="security-panel">
                            <!-- 安全设置 -->
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return dialog;
    }
};
```

## 技术特点

### 1. 注册表模式
- 可扩展的操作注册
- 动态操作管理
- 插件化架构

### 2. 条件系统
- 灵活的显示条件
- 运行时条件检查
- 平台特定操作

### 3. 状态管理
- 动态状态检测
- 响应式更新
- 组件绑定

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 可扩展的架构设计

### 2. 策略模式 (Strategy Pattern)
- 不同操作的不同策略
- 可配置的操作行为

### 3. 命令模式 (Command Pattern)
- 操作的封装和执行
- 统一的操作接口

## 注意事项

1. **条件检查**: 确保操作条件的正确性
2. **状态同步**: 保持操作状态与实际状态一致
3. **权限控制**: 根据用户权限显示操作
4. **平台兼容**: 考虑不同平台的功能差异

## 扩展建议

1. **插件系统**: 支持第三方操作插件
2. **快捷键**: 为操作添加快捷键支持
3. **批量操作**: 支持批量操作功能
4. **操作历史**: 记录操作历史和撤销
5. **自定义布局**: 支持用户自定义操作布局

该模块为通话系统提供了完整的操作定义和管理框架，是通话功能的核心配置中心。

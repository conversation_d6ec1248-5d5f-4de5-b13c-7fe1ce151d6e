# Messaging Menu Patch - 消息菜单补丁

## 概述

`messaging_menu_patch.js` 实现了对 Odoo 讨论应用中消息菜单组件的补丁扩展，为消息菜单添加了开始会议的功能。该补丁通过Odoo的补丁机制扩展了MessagingMenu组件，添加了onClickStartMeeting方法，支持快速启动会议并关闭下拉菜单，提供了便捷的会议启动入口，是视频通话系统中快速访问功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/web/messaging_menu_patch.js`
- **行数**: 19
- **模块**: `@mail/discuss/call/web/messaging_menu_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/public_web/messaging_menu'  // 消息菜单组件
'@web/core/utils/patch'                 // 补丁工具
```

## 补丁定义

### MessagingMenu 补丁

```javascript
patch(MessagingMenu.prototype, {
    async onClickStartMeeting() {
        this.store.startMeeting();
        this.dropdown.close();
    },
});
```

**补丁特性**:
- 添加开始会议方法
- 异步操作支持
- 自动关闭下拉菜单
- 简洁的实现方式

## 核心功能

### 1. 开始会议操作

```javascript
async onClickStartMeeting() {
    this.store.startMeeting();
    this.dropdown.close();
}
```

**操作流程**:
- **启动会议**: 调用store的startMeeting方法
- **关闭菜单**: 自动关闭下拉菜单
- **异步支持**: 支持异步操作处理

## 使用场景

### 1. 会议启动增强

```javascript
// 会议启动增强功能
const MeetingStartEnhancer = {
    enhanceMeetingStart: () => {
        const EnhancedMessagingMenuPatch = {
            setup() {
                super.setup();
                
                // 会议启动状态
                this.meetingStartState = useState({
                    isStarting: false,
                    lastMeetingTime: null,
                    meetingHistory: [],
                    quickStartEnabled: true,
                    defaultMeetingSettings: {
                        enableVideo: true,
                        enableAudio: true,
                        enableScreenShare: false,
                        maxParticipants: 50
                    }
                });
                
                // 设置会议启动监听
                this.setupMeetingStartListeners();
            },
            
            // 增强的开始会议方法
            async onClickStartMeeting() {
                if (this.meetingStartState.isStarting) {
                    return; // 防止重复点击
                }
                
                this.meetingStartState.isStarting = true;
                
                try {
                    // 显示启动状态
                    this.showMeetingStartStatus();
                    
                    // 检查会议权限
                    const hasPermission = await this.checkMeetingPermission();
                    if (!hasPermission) {
                        throw new Error('没有启动会议的权限');
                    }
                    
                    // 应用默认设置
                    await this.applyDefaultMeetingSettings();
                    
                    // 启动会议
                    await this.store.startMeeting();
                    
                    // 记录会议启动
                    this.recordMeetingStart();
                    
                    // 显示成功消息
                    this.showMeetingStartSuccess();
                    
                } catch (error) {
                    console.error('启动会议失败:', error);
                    this.handleMeetingStartError(error);
                } finally {
                    this.meetingStartState.isStarting = false;
                    this.dropdown.close();
                }
            },
            
            // 检查会议权限
            async checkMeetingPermission() {
                const user = this.store.self;
                
                if (!user) {
                    return false;
                }
                
                // 检查用户角色权限
                if (user.isGuest && !this.store.settings.allowGuestMeetings) {
                    return false;
                }
                
                // 检查会议频率限制
                if (!this.checkMeetingRateLimit()) {
                    return false;
                }
                
                return true;
            },
            
            // 检查会议频率限制
            checkMeetingRateLimit() {
                const now = Date.now();
                const oneHourAgo = now - (60 * 60 * 1000);
                
                // 清理过期记录
                this.meetingStartState.meetingHistory = this.meetingStartState.meetingHistory
                    .filter(meeting => meeting.startTime > oneHourAgo);
                
                // 检查是否超过限制（每小时最多5个会议）
                return this.meetingStartState.meetingHistory.length < 5;
            },
            
            // 应用默认会议设置
            async applyDefaultMeetingSettings() {
                const settings = this.meetingStartState.defaultMeetingSettings;
                
                try {
                    await rpc('/mail/meeting/apply_default_settings', {
                        settings: settings
                    });
                } catch (error) {
                    console.warn('应用默认会议设置失败:', error);
                    // 不阻止会议启动
                }
            },
            
            // 记录会议启动
            recordMeetingStart() {
                const meetingRecord = {
                    id: Date.now().toString(),
                    startTime: Date.now(),
                    user: this.store.self?.name || 'Unknown',
                    settings: { ...this.meetingStartState.defaultMeetingSettings }
                };
                
                this.meetingStartState.meetingHistory.push(meetingRecord);
                this.meetingStartState.lastMeetingTime = Date.now();
                
                // 保存到本地存储
                this.saveMeetingHistory();
            },
            
            // 保存会议历史
            saveMeetingHistory() {
                try {
                    localStorage.setItem(
                        'messaging_menu_meeting_history',
                        JSON.stringify(this.meetingStartState.meetingHistory)
                    );
                } catch (error) {
                    console.warn('保存会议历史失败:', error);
                }
            },
            
            // 加载会议历史
            loadMeetingHistory() {
                try {
                    const saved = localStorage.getItem('messaging_menu_meeting_history');
                    if (saved) {
                        this.meetingStartState.meetingHistory = JSON.parse(saved);
                    }
                } catch (error) {
                    console.warn('加载会议历史失败:', error);
                }
            },
            
            // 显示会议启动状态
            showMeetingStartStatus() {
                this.env.services.notification.add(
                    '正在启动会议...',
                    { 
                        type: 'info',
                        sticky: true,
                        id: 'meeting_start_status'
                    }
                );
            },
            
            // 显示会议启动成功
            showMeetingStartSuccess() {
                // 移除启动状态通知
                this.env.services.notification.remove('meeting_start_status');
                
                // 显示成功通知
                this.env.services.notification.add(
                    '会议已成功启动！',
                    { type: 'success' }
                );
            },
            
            // 处理会议启动错误
            handleMeetingStartError(error) {
                // 移除启动状态通知
                this.env.services.notification.remove('meeting_start_status');
                
                // 显示错误通知
                this.env.services.notification.add(
                    `启动会议失败: ${error.message}`,
                    { type: 'error' }
                );
            },
            
            // 设置会议启动监听
            setupMeetingStartListeners() {
                // 监听会议状态变化
                this.store.addEventListener('meeting_started', (event) => {
                    this.handleMeetingStarted(event.detail);
                });
                
                this.store.addEventListener('meeting_failed', (event) => {
                    this.handleMeetingFailed(event.detail);
                });
                
                // 加载会议历史
                this.loadMeetingHistory();
            },
            
            // 处理会议启动成功
            handleMeetingStarted(detail) {
                console.log('会议启动成功:', detail);
                
                // 发送分析数据
                this.sendMeetingAnalytics('started', detail);
            },
            
            // 处理会议启动失败
            handleMeetingFailed(detail) {
                console.error('会议启动失败:', detail);
                
                // 发送分析数据
                this.sendMeetingAnalytics('failed', detail);
            },
            
            // 发送会议分析数据
            sendMeetingAnalytics(type, detail) {
                try {
                    this.env.services.analytics?.track('meeting_start', {
                        type: type,
                        source: 'messaging_menu',
                        detail: detail,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('发送会议分析数据失败:', error);
                }
            },
            
            // 快速启动会议
            async quickStartMeeting() {
                if (!this.meetingStartState.quickStartEnabled) {
                    return this.onClickStartMeeting();
                }
                
                try {
                    // 跳过权限检查和设置应用，直接启动
                    await this.store.startMeeting();
                    this.dropdown.close();
                    
                    this.env.services.notification.add(
                        '快速会议已启动！',
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('快速启动会议失败:', error);
                    // 回退到常规启动
                    return this.onClickStartMeeting();
                }
            },
            
            // 获取会议统计
            getMeetingStatistics() {
                const now = Date.now();
                const oneDay = 24 * 60 * 60 * 1000;
                const oneWeek = 7 * oneDay;
                
                const todayMeetings = this.meetingStartState.meetingHistory.filter(
                    meeting => (now - meeting.startTime) < oneDay
                );
                
                const weekMeetings = this.meetingStartState.meetingHistory.filter(
                    meeting => (now - meeting.startTime) < oneWeek
                );
                
                return {
                    totalMeetings: this.meetingStartState.meetingHistory.length,
                    todayMeetings: todayMeetings.length,
                    weekMeetings: weekMeetings.length,
                    lastMeetingTime: this.meetingStartState.lastMeetingTime,
                    averageMeetingsPerDay: weekMeetings.length / 7
                };
            },
            
            // 更新默认设置
            updateDefaultMeetingSettings(newSettings) {
                Object.assign(this.meetingStartState.defaultMeetingSettings, newSettings);
                
                // 保存设置
                try {
                    localStorage.setItem(
                        'messaging_menu_default_settings',
                        JSON.stringify(this.meetingStartState.defaultMeetingSettings)
                    );
                } catch (error) {
                    console.warn('保存默认会议设置失败:', error);
                }
            },
            
            // 加载默认设置
            loadDefaultMeetingSettings() {
                try {
                    const saved = localStorage.getItem('messaging_menu_default_settings');
                    if (saved) {
                        const settings = JSON.parse(saved);
                        Object.assign(this.meetingStartState.defaultMeetingSettings, settings);
                    }
                } catch (error) {
                    console.warn('加载默认会议设置失败:', error);
                }
            }
        };
        
        patch(MessagingMenu.prototype, EnhancedMessagingMenuPatch);
    }
};

// 应用会议启动增强
MeetingStartEnhancer.enhanceMeetingStart();
```

### 2. 快捷操作管理

```javascript
// 快捷操作管理
const QuickActionManager = {
    setupQuickActions: () => {
        const QuickActionPatch = {
            setup() {
                super.setup();
                
                // 快捷操作配置
                this.quickActions = {
                    startMeeting: {
                        enabled: true,
                        shortcut: 'Ctrl+Shift+M',
                        icon: 'fa-video-camera',
                        label: '开始会议'
                    },
                    joinLastMeeting: {
                        enabled: true,
                        shortcut: 'Ctrl+Shift+J',
                        icon: 'fa-sign-in',
                        label: '加入上次会议'
                    },
                    scheduleMeeting: {
                        enabled: false,
                        shortcut: 'Ctrl+Shift+S',
                        icon: 'fa-calendar',
                        label: '安排会议'
                    }
                };
                
                // 设置快捷键
                this.setupShortcuts();
                
                // 设置快捷操作UI
                this.setupQuickActionUI();
            },
            
            // 设置快捷键
            setupShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', (event) => {
                        this.handleShortcut(event);
                    });
                });
            },
            
            // 处理快捷键
            handleShortcut(event) {
                const shortcut = this.getShortcutString(event);
                
                Object.entries(this.quickActions).forEach(([action, config]) => {
                    if (config.enabled && config.shortcut === shortcut) {
                        event.preventDefault();
                        this.executeQuickAction(action);
                    }
                });
            },
            
            // 获取快捷键字符串
            getShortcutString(event) {
                const parts = [];
                
                if (event.ctrlKey) parts.push('Ctrl');
                if (event.shiftKey) parts.push('Shift');
                if (event.altKey) parts.push('Alt');
                
                parts.push(event.key.toUpperCase());
                
                return parts.join('+');
            },
            
            // 执行快捷操作
            async executeQuickAction(action) {
                try {
                    switch (action) {
                        case 'startMeeting':
                            await this.onClickStartMeeting();
                            break;
                        case 'joinLastMeeting':
                            await this.joinLastMeeting();
                            break;
                        case 'scheduleMeeting':
                            await this.scheduleMeeting();
                            break;
                        default:
                            console.warn('未知的快捷操作:', action);
                    }
                } catch (error) {
                    console.error('执行快捷操作失败:', error);
                }
            },
            
            // 加入上次会议
            async joinLastMeeting() {
                try {
                    const lastMeeting = await this.getLastMeeting();
                    
                    if (!lastMeeting) {
                        this.env.services.notification.add(
                            '没有找到最近的会议',
                            { type: 'warning' }
                        );
                        return;
                    }
                    
                    await this.store.joinMeeting(lastMeeting.id);
                    this.dropdown.close();
                    
                    this.env.services.notification.add(
                        '正在加入上次会议...',
                        { type: 'info' }
                    );
                } catch (error) {
                    console.error('加入上次会议失败:', error);
                    this.env.services.notification.add(
                        '加入上次会议失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 获取上次会议
            async getLastMeeting() {
                try {
                    const response = await rpc('/mail/meeting/get_last_meeting');
                    return response.meeting;
                } catch (error) {
                    console.error('获取上次会议失败:', error);
                    return null;
                }
            },
            
            // 安排会议
            async scheduleMeeting() {
                try {
                    // 打开会议安排界面
                    const action = await this.env.services.action.doAction({
                        type: 'ir.actions.act_window',
                        name: '安排会议',
                        res_model: 'mail.meeting',
                        view_mode: 'form',
                        target: 'new'
                    });
                    
                    this.dropdown.close();
                } catch (error) {
                    console.error('打开会议安排界面失败:', error);
                    this.env.services.notification.add(
                        '打开会议安排界面失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 设置快捷操作UI
            setupQuickActionUI() {
                onMounted(() => {
                    this.addQuickActionButtons();
                });
            },
            
            // 添加快捷操作按钮
            addQuickActionButtons() {
                const menuContainer = this.el.querySelector('.messaging-menu-content');
                if (!menuContainer) return;
                
                const quickActionsContainer = document.createElement('div');
                quickActionsContainer.className = 'quick-actions-container';
                quickActionsContainer.innerHTML = this.getQuickActionsHTML();
                
                menuContainer.insertBefore(quickActionsContainer, menuContainer.firstChild);
                
                // 绑定快捷操作事件
                this.bindQuickActionEvents(quickActionsContainer);
            },
            
            // 获取快捷操作HTML
            getQuickActionsHTML() {
                const enabledActions = Object.entries(this.quickActions)
                    .filter(([_, config]) => config.enabled);
                
                return `
                    <div class="quick-actions-header">
                        <span class="quick-actions-title">快捷操作</span>
                    </div>
                    <div class="quick-actions-list">
                        ${enabledActions.map(([action, config]) => `
                            <button class="quick-action-btn" data-action="${action}" title="${config.shortcut}">
                                <i class="fa ${config.icon}"></i>
                                <span class="action-label">${config.label}</span>
                                <span class="action-shortcut">${config.shortcut}</span>
                            </button>
                        `).join('')}
                    </div>
                `;
            },
            
            // 绑定快捷操作事件
            bindQuickActionEvents(container) {
                const actionButtons = container.querySelectorAll('.quick-action-btn');
                
                actionButtons.forEach(button => {
                    button.addEventListener('click', (event) => {
                        event.preventDefault();
                        event.stopPropagation();
                        
                        const action = button.dataset.action;
                        this.executeQuickAction(action);
                    });
                });
            },
            
            // 更新快捷操作配置
            updateQuickActionConfig(action, config) {
                if (action in this.quickActions) {
                    Object.assign(this.quickActions[action], config);
                    
                    // 重新渲染UI
                    this.refreshQuickActionUI();
                    
                    // 保存配置
                    this.saveQuickActionConfig();
                }
            },
            
            // 刷新快捷操作UI
            refreshQuickActionUI() {
                const container = this.el.querySelector('.quick-actions-container');
                if (container) {
                    container.remove();
                    this.addQuickActionButtons();
                }
            },
            
            // 保存快捷操作配置
            saveQuickActionConfig() {
                try {
                    localStorage.setItem(
                        'messaging_menu_quick_actions',
                        JSON.stringify(this.quickActions)
                    );
                } catch (error) {
                    console.warn('保存快捷操作配置失败:', error);
                }
            },
            
            // 加载快捷操作配置
            loadQuickActionConfig() {
                try {
                    const saved = localStorage.getItem('messaging_menu_quick_actions');
                    if (saved) {
                        const config = JSON.parse(saved);
                        Object.assign(this.quickActions, config);
                    }
                } catch (error) {
                    console.warn('加载快捷操作配置失败:', error);
                }
            }
        };
        
        patch(MessagingMenu.prototype, QuickActionPatch);
    }
};

// 应用快捷操作管理
QuickActionManager.setupQuickActions();
```

## 技术特点

### 1. 补丁机制
- 简洁的补丁实现
- 保持原有功能完整性
- 最小化代码修改

### 2. 异步操作
- 支持异步会议启动
- 错误处理机制
- 状态管理优化

### 3. 用户界面
- 自动关闭下拉菜单
- 流畅的用户体验
- 快捷访问入口

### 4. 扩展性
- 易于功能扩展
- 模块化设计
- 配置化支持

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 命令模式 (Command Pattern)
- 操作的封装和执行
- 解耦操作定义和执行

### 3. 门面模式 (Facade Pattern)
- 简化的会议启动接口
- 统一的操作入口

## 注意事项

1. **异步处理**: 确保异步操作的正确处理
2. **错误处理**: 完善的异常处理机制
3. **用户反馈**: 提供清晰的操作反馈
4. **性能优化**: 避免重复的操作执行

## 扩展建议

1. **权限控制**: 添加会议启动权限检查
2. **会议模板**: 支持预定义的会议模板
3. **快捷键**: 添加键盘快捷键支持
4. **会议历史**: 记录和管理会议历史
5. **批量操作**: 支持批量会议管理

该补丁为视频通话系统提供了便捷的会议启动入口，是快速访问功能的重要组成部分。

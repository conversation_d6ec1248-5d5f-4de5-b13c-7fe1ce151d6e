# Discuss Patch - 讨论补丁

## 概述

`discuss_patch.js` 实现了对 Odoo 讨论应用主界面组件的补丁扩展，为讨论主界面添加了自动邀请功能。该补丁通过Odoo的补丁机制扩展了Discuss组件，使用OWL的useEffect钩子监听邀请线程状态，当检测到当前线程需要打开邀请界面时自动触发邀请操作，提供了在主讨论界面中的无缝邀请体验，是视频通话系统中主界面邀请流程的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/web/discuss_patch.js`
- **行数**: 32
- **模块**: `@mail/discuss/call/web/discuss_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/public_web/discuss'  // 讨论主组件
'@web/core/utils/patch'          // 补丁工具
'@odoo/owl'                      // OWL 框架
```

## 补丁定义

### Discuss 补丁

```javascript
patch(Discuss.prototype, {
    setup(...args) {
        super.setup(...args);
        useEffect(
            () => {
                if (this.thread && this.thread === this.store.openInviteThread) {
                    this.threadActions.actions
                        .find((action) => action.id === "invite-people")
                        ?.onSelect();
                    this.store.openInviteThread = null;
                }
            },
            () => [this.store.openInviteThread]
        );
    },
});
```

**补丁特性**:
- 扩展setup方法
- 使用useEffect钩子
- 自动邀请触发
- 状态清理机制

## 核心功能

### 1. 主界面邀请触发

```javascript
if (this.thread && this.thread === this.store.openInviteThread) {
    this.threadActions.actions
        .find((action) => action.id === "invite-people")
        ?.onSelect();
    this.store.openInviteThread = null;
}
```

**触发逻辑**:
- **线程检查**: 确保当前有活跃线程
- **匹配验证**: 当前线程是否为待邀请线程
- **操作查找**: 查找"invite-people"操作
- **自动执行**: 自动触发邀请操作
- **状态清理**: 清除邀请线程状态

### 2. 响应式监听

```javascript
useEffect(
    () => { /* 邀请逻辑 */ },
    () => [this.store.openInviteThread]
);
```

**监听机制**:
- **依赖监听**: 监听openInviteThread状态变化
- **自动执行**: 状态变化时自动执行邀请逻辑
- **性能优化**: 只在相关状态变化时执行

## 使用场景

### 1. 主界面邀请增强

```javascript
// 主界面邀请增强功能
const DiscussInviteEnhancer = {
    enhanceDiscussInvite: () => {
        const EnhancedDiscussPatch = {
            setup(...args) {
                super.setup(...args);
                
                // 增强的邀请状态管理
                this.discussInviteState = useState({
                    inviteQueue: [],
                    currentInvite: null,
                    inviteMode: 'auto', // auto, manual, batch
                    invitePreferences: {
                        showConfirmation: true,
                        autoSelectRecipients: false,
                        rememberLastInvite: true
                    }
                });
                
                // 设置增强的邀请处理
                this.setupEnhancedInviteHandling();
                
                // 设置邀请队列管理
                this.setupInviteQueueManagement();
                
                // 设置邀请偏好设置
                this.setupInvitePreferences();
            },
            
            // 设置增强的邀请处理
            setupEnhancedInviteHandling() {
                useEffect(
                    () => {
                        this.handleDiscussInviteRequest();
                    },
                    () => [
                        this.store.openInviteThread,
                        this.discussInviteState.inviteMode,
                        this.thread
                    ]
                );
                
                // 监听线程变化
                useEffect(
                    () => {
                        this.handleThreadChange();
                    },
                    () => [this.thread]
                );
            },
            
            // 处理讨论邀请请求
            async handleDiscussInviteRequest() {
                const inviteThread = this.store.openInviteThread;
                if (!inviteThread || !this.thread) {
                    return;
                }
                
                // 检查是否为当前线程
                if (this.thread !== inviteThread) {
                    // 如果不是当前线程，添加到队列
                    this.addToInviteQueue(inviteThread);
                    return;
                }
                
                try {
                    // 处理当前线程的邀请
                    await this.processCurrentThreadInvite(inviteThread);
                } catch (error) {
                    console.error('处理讨论邀请失败:', error);
                    this.handleInviteError(inviteThread, error);
                } finally {
                    // 清理邀请状态
                    this.store.openInviteThread = null;
                    
                    // 处理队列中的下一个邀请
                    this.processNextInviteInQueue();
                }
            },
            
            // 处理当前线程邀请
            async processCurrentThreadInvite(thread) {
                // 设置当前邀请
                this.discussInviteState.currentInvite = {
                    thread: thread,
                    startTime: Date.now(),
                    status: 'processing'
                };
                
                // 根据邀请模式处理
                switch (this.discussInviteState.inviteMode) {
                    case 'auto':
                        await this.processAutoInvite(thread);
                        break;
                    case 'manual':
                        await this.processManualInvite(thread);
                        break;
                    case 'batch':
                        await this.processBatchInvite(thread);
                        break;
                    default:
                        await this.processAutoInvite(thread);
                }
                
                // 更新邀请状态
                if (this.discussInviteState.currentInvite) {
                    this.discussInviteState.currentInvite.status = 'completed';
                    this.discussInviteState.currentInvite.endTime = Date.now();
                }
            },
            
            // 处理自动邀请
            async processAutoInvite(thread) {
                const inviteAction = this.threadActions.actions
                    .find((action) => action.id === "invite-people");
                
                if (!inviteAction) {
                    throw new Error('邀请操作不可用');
                }
                
                // 显示确认对话框（如果启用）
                if (this.discussInviteState.invitePreferences.showConfirmation) {
                    const confirmed = await this.showInviteConfirmation(thread);
                    if (!confirmed) {
                        throw new Error('用户取消邀请');
                    }
                }
                
                // 执行邀请操作
                await inviteAction.onSelect();
                
                // 记录邀请历史
                this.recordInviteHistory(thread, 'auto');
            },
            
            // 处理手动邀请
            async processManualInvite(thread) {
                // 显示手动邀请界面
                const inviteDialog = await this.showManualInviteDialog(thread);
                
                if (inviteDialog.confirmed) {
                    const inviteAction = this.threadActions.actions
                        .find((action) => action.id === "invite-people");
                    
                    if (inviteAction) {
                        await inviteAction.onSelect();
                        this.recordInviteHistory(thread, 'manual');
                    }
                }
            },
            
            // 处理批量邀请
            async processBatchInvite(thread) {
                // 添加到批量邀请列表
                this.addToBatchInviteList(thread);
                
                // 显示批量邀请管理界面
                this.showBatchInviteManager();
            },
            
            // 显示邀请确认对话框
            showInviteConfirmation(thread) {
                return new Promise((resolve) => {
                    const dialog = document.createElement('div');
                    dialog.className = 'invite-confirmation-dialog-overlay';
                    dialog.innerHTML = `
                        <div class="invite-confirmation-dialog">
                            <div class="dialog-header">
                                <h3>确认邀请</h3>
                                <button class="close-btn">&times;</button>
                            </div>
                            
                            <div class="dialog-content">
                                <p>是否要打开 <strong>${thread.displayName}</strong> 的邀请界面？</p>
                                
                                <div class="invite-options">
                                    <label>
                                        <input type="checkbox" class="remember-choice">
                                        <span>记住我的选择</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="dialog-footer">
                                <button class="btn btn-secondary cancel-btn">取消</button>
                                <button class="btn btn-primary confirm-btn">确认</button>
                            </div>
                        </div>
                    `;
                    
                    document.body.appendChild(dialog);
                    
                    // 绑定事件
                    const closeBtn = dialog.querySelector('.close-btn');
                    const cancelBtn = dialog.querySelector('.cancel-btn');
                    const confirmBtn = dialog.querySelector('.confirm-btn');
                    const rememberCheckbox = dialog.querySelector('.remember-choice');
                    
                    const closeDialog = (confirmed = false) => {
                        // 处理记住选择
                        if (rememberCheckbox.checked) {
                            this.discussInviteState.invitePreferences.showConfirmation = false;
                        }
                        
                        dialog.remove();
                        resolve(confirmed);
                    };
                    
                    closeBtn.addEventListener('click', () => closeDialog(false));
                    cancelBtn.addEventListener('click', () => closeDialog(false));
                    confirmBtn.addEventListener('click', () => closeDialog(true));
                });
            },
            
            // 添加到邀请队列
            addToInviteQueue(thread) {
                const queueItem = {
                    thread: thread,
                    addedTime: Date.now(),
                    priority: this.calculateInvitePriority(thread)
                };
                
                this.discussInviteState.inviteQueue.push(queueItem);
                
                // 按优先级排序
                this.discussInviteState.inviteQueue.sort((a, b) => b.priority - a.priority);
                
                // 限制队列大小
                if (this.discussInviteState.inviteQueue.length > 10) {
                    this.discussInviteState.inviteQueue.pop();
                }
            },
            
            // 计算邀请优先级
            calculateInvitePriority(thread) {
                let priority = 0;
                
                // 基于线程类型
                if (thread.channel_type === 'chat') {
                    priority += 10; // 私聊优先级更高
                } else if (thread.channel_type === 'channel') {
                    priority += 5;
                }
                
                // 基于参与者数量
                const memberCount = thread.channelMembers?.length || 0;
                if (memberCount < 5) {
                    priority += 5; // 小群组优先级更高
                }
                
                // 基于最近活动
                const lastActivity = thread.lastInterestDateTime;
                if (lastActivity) {
                    const timeDiff = Date.now() - new Date(lastActivity).getTime();
                    if (timeDiff < 60 * 60 * 1000) { // 1小时内
                        priority += 3;
                    }
                }
                
                return priority;
            },
            
            // 处理队列中的下一个邀请
            processNextInviteInQueue() {
                if (this.discussInviteState.inviteQueue.length === 0) {
                    return;
                }
                
                const nextInvite = this.discussInviteState.inviteQueue.shift();
                
                // 设置为待邀请线程
                setTimeout(() => {
                    this.store.openInviteThread = nextInvite.thread;
                }, 100);
            },
            
            // 处理线程变化
            handleThreadChange() {
                // 如果切换到队列中的线程，自动处理邀请
                const queueIndex = this.discussInviteState.inviteQueue.findIndex(
                    item => item.thread === this.thread
                );
                
                if (queueIndex !== -1) {
                    const queueItem = this.discussInviteState.inviteQueue.splice(queueIndex, 1)[0];
                    this.store.openInviteThread = queueItem.thread;
                }
            },
            
            // 设置邀请队列管理
            setupInviteQueueManagement() {
                this.inviteQueueManager = {
                    getQueueStatus: () => {
                        return {
                            length: this.discussInviteState.inviteQueue.length,
                            items: this.discussInviteState.inviteQueue.map(item => ({
                                threadName: item.thread.displayName,
                                addedTime: item.addedTime,
                                priority: item.priority
                            }))
                        };
                    },
                    
                    clearQueue: () => {
                        this.discussInviteState.inviteQueue = [];
                    },
                    
                    removeFromQueue: (threadId) => {
                        this.discussInviteState.inviteQueue = this.discussInviteState.inviteQueue
                            .filter(item => item.thread.id !== threadId);
                    }
                };
            },
            
            // 设置邀请偏好设置
            setupInvitePreferences() {
                this.invitePreferencesManager = {
                    updatePreferences: (newPreferences) => {
                        Object.assign(this.discussInviteState.invitePreferences, newPreferences);
                        this.saveInvitePreferences();
                    },
                    
                    resetPreferences: () => {
                        this.discussInviteState.invitePreferences = {
                            showConfirmation: true,
                            autoSelectRecipients: false,
                            rememberLastInvite: true
                        };
                        this.saveInvitePreferences();
                    },
                    
                    getPreferences: () => {
                        return { ...this.discussInviteState.invitePreferences };
                    }
                };
                
                // 加载保存的偏好设置
                this.loadInvitePreferences();
            },
            
            // 保存邀请偏好设置
            saveInvitePreferences() {
                try {
                    localStorage.setItem(
                        'discuss_invite_preferences',
                        JSON.stringify(this.discussInviteState.invitePreferences)
                    );
                } catch (error) {
                    console.warn('保存邀请偏好设置失败:', error);
                }
            },
            
            // 加载邀请偏好设置
            loadInvitePreferences() {
                try {
                    const saved = localStorage.getItem('discuss_invite_preferences');
                    if (saved) {
                        const preferences = JSON.parse(saved);
                        Object.assign(this.discussInviteState.invitePreferences, preferences);
                    }
                } catch (error) {
                    console.warn('加载邀请偏好设置失败:', error);
                }
            },
            
            // 记录邀请历史
            recordInviteHistory(thread, mode) {
                const historyItem = {
                    threadId: thread.id,
                    threadName: thread.displayName,
                    mode: mode,
                    timestamp: Date.now(),
                    user: this.store.self?.name || 'Unknown'
                };
                
                // 保存到本地存储
                try {
                    const history = JSON.parse(localStorage.getItem('discuss_invite_history') || '[]');
                    history.push(historyItem);
                    
                    // 限制历史记录数量
                    if (history.length > 100) {
                        history.shift();
                    }
                    
                    localStorage.setItem('discuss_invite_history', JSON.stringify(history));
                } catch (error) {
                    console.warn('保存邀请历史失败:', error);
                }
            },
            
            // 处理邀请错误
            handleInviteError(thread, error) {
                this.env.services.notification.add(
                    `邀请到 ${thread.displayName} 失败: ${error.message}`,
                    { type: 'error' }
                );
                
                // 记录错误
                console.error('讨论邀请错误:', { thread: thread.displayName, error });
            },
            
            // 获取邀请统计
            getInviteStatistics() {
                try {
                    const history = JSON.parse(localStorage.getItem('discuss_invite_history') || '[]');
                    const now = Date.now();
                    const oneDay = 24 * 60 * 60 * 1000;
                    
                    const todayInvites = history.filter(item => 
                        (now - item.timestamp) < oneDay
                    );
                    
                    return {
                        totalInvites: history.length,
                        todayInvites: todayInvites.length,
                        queueLength: this.discussInviteState.inviteQueue.length,
                        currentMode: this.discussInviteState.inviteMode,
                        preferences: this.discussInviteState.invitePreferences
                    };
                } catch (error) {
                    console.warn('获取邀请统计失败:', error);
                    return {
                        totalInvites: 0,
                        todayInvites: 0,
                        queueLength: 0,
                        currentMode: this.discussInviteState.inviteMode,
                        preferences: this.discussInviteState.invitePreferences
                    };
                }
            }
        };
        
        patch(Discuss.prototype, EnhancedDiscussPatch);
    }
};

// 应用主界面邀请增强
DiscussInviteEnhancer.enhanceDiscussInvite();
```

### 2. 邀请模式管理

```javascript
// 邀请模式管理
const InviteModeManager = {
    setupModeManagement: () => {
        const ModePatch = {
            setup(...args) {
                super.setup(...args);
                
                // 模式配置
                this.inviteModes = {
                    auto: {
                        name: '自动模式',
                        description: '自动打开邀请界面',
                        enabled: true
                    },
                    manual: {
                        name: '手动模式', 
                        description: '需要用户确认后打开',
                        enabled: true
                    },
                    batch: {
                        name: '批量模式',
                        description: '收集多个邀请后批量处理',
                        enabled: false
                    },
                    smart: {
                        name: '智能模式',
                        description: '根据上下文智能选择处理方式',
                        enabled: false
                    }
                };
                
                // 当前模式
                this.currentInviteMode = 'auto';
                
                // 设置模式切换
                this.setupModeSwitch();
            },
            
            // 设置模式切换
            setupModeSwitch() {
                useEffect(
                    () => {
                        this.handleModeBasedInvite();
                    },
                    () => [this.store.openInviteThread, this.currentInviteMode]
                );
            },
            
            // 基于模式的邀请处理
            async handleModeBasedInvite() {
                const thread = this.store.openInviteThread;
                if (!thread || !this.thread || this.thread !== thread) {
                    return;
                }
                
                try {
                    switch (this.currentInviteMode) {
                        case 'auto':
                            await this.handleAutoMode(thread);
                            break;
                        case 'manual':
                            await this.handleManualMode(thread);
                            break;
                        case 'batch':
                            await this.handleBatchMode(thread);
                            break;
                        case 'smart':
                            await this.handleSmartMode(thread);
                            break;
                        default:
                            await this.handleAutoMode(thread);
                    }
                } catch (error) {
                    console.error('模式处理失败:', error);
                } finally {
                    this.store.openInviteThread = null;
                }
            },
            
            // 处理自动模式
            async handleAutoMode(thread) {
                const inviteAction = this.threadActions.actions
                    .find((action) => action.id === "invite-people");
                
                if (inviteAction) {
                    await inviteAction.onSelect();
                }
            },
            
            // 处理手动模式
            async handleManualMode(thread) {
                const confirmed = confirm(`是否要打开 ${thread.displayName} 的邀请界面？`);
                
                if (confirmed) {
                    const inviteAction = this.threadActions.actions
                        .find((action) => action.id === "invite-people");
                    
                    if (inviteAction) {
                        await inviteAction.onSelect();
                    }
                }
            },
            
            // 处理批量模式
            async handleBatchMode(thread) {
                // 添加到批量处理队列
                if (!this.batchInviteQueue) {
                    this.batchInviteQueue = [];
                }
                
                this.batchInviteQueue.push(thread);
                
                // 显示批量处理通知
                this.showBatchNotification();
            },
            
            // 处理智能模式
            async handleSmartMode(thread) {
                const context = this.analyzeInviteContext(thread);
                
                if (context.urgency === 'high') {
                    await this.handleAutoMode(thread);
                } else if (context.urgency === 'medium') {
                    await this.handleManualMode(thread);
                } else {
                    await this.handleBatchMode(thread);
                }
            },
            
            // 分析邀请上下文
            analyzeInviteContext(thread) {
                let urgency = 'low';
                
                // 基于线程类型
                if (thread.channel_type === 'chat') {
                    urgency = 'high'; // 私聊通常更紧急
                }
                
                // 基于参与者数量
                const memberCount = thread.channelMembers?.length || 0;
                if (memberCount <= 2) {
                    urgency = 'high';
                } else if (memberCount <= 5) {
                    urgency = 'medium';
                }
                
                // 基于最近活动
                const lastActivity = thread.lastInterestDateTime;
                if (lastActivity) {
                    const timeDiff = Date.now() - new Date(lastActivity).getTime();
                    if (timeDiff < 5 * 60 * 1000) { // 5分钟内
                        urgency = 'high';
                    } else if (timeDiff < 30 * 60 * 1000) { // 30分钟内
                        urgency = 'medium';
                    }
                }
                
                return { urgency };
            },
            
            // 显示批量通知
            showBatchNotification() {
                const count = this.batchInviteQueue.length;
                
                this.env.services.notification.add(
                    `已添加到批量邀请队列 (${count} 个待处理)`,
                    { 
                        type: 'info',
                        buttons: [
                            {
                                name: '处理队列',
                                primary: true,
                                onClick: () => this.processBatchQueue()
                            }
                        ]
                    }
                );
            },
            
            // 处理批量队列
            async processBatchQueue() {
                if (!this.batchInviteQueue || this.batchInviteQueue.length === 0) {
                    return;
                }
                
                const threads = [...this.batchInviteQueue];
                this.batchInviteQueue = [];
                
                for (const thread of threads) {
                    try {
                        // 切换到线程并执行邀请
                        await this.switchToThreadAndInvite(thread);
                        
                        // 添加延迟避免过快操作
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } catch (error) {
                        console.error('批量邀请失败:', error);
                    }
                }
            },
            
            // 切换到线程并邀请
            async switchToThreadAndInvite(thread) {
                // 这里需要实现线程切换逻辑
                // 具体实现取决于应用的路由和状态管理
                
                const inviteAction = this.threadActions.actions
                    .find((action) => action.id === "invite-people");
                
                if (inviteAction) {
                    await inviteAction.onSelect();
                }
            },
            
            // 切换邀请模式
            switchInviteMode(mode) {
                if (mode in this.inviteModes && this.inviteModes[mode].enabled) {
                    this.currentInviteMode = mode;
                    
                    // 保存模式偏好
                    try {
                        localStorage.setItem('discuss_invite_mode', mode);
                    } catch (error) {
                        console.warn('保存邀请模式失败:', error);
                    }
                    
                    // 显示模式切换通知
                    this.env.services.notification.add(
                        `已切换到${this.inviteModes[mode].name}`,
                        { type: 'success' }
                    );
                }
            },
            
            // 获取可用模式
            getAvailableModes() {
                return Object.entries(this.inviteModes)
                    .filter(([_, config]) => config.enabled)
                    .map(([key, config]) => ({
                        key,
                        ...config,
                        active: key === this.currentInviteMode
                    }));
            }
        };
        
        patch(Discuss.prototype, ModePatch);
    }
};

// 应用邀请模式管理
InviteModeManager.setupModeManagement();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 响应式设计
- useEffect钩子使用
- 状态依赖监听
- 自动化操作触发

### 3. 主界面集成
- 与讨论主界面无缝集成
- 线程状态监听
- 自动化邀请流程

### 4. 用户体验
- 智能邀请触发
- 无缝操作体验
- 状态管理优化

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 状态变化监听
- 自动化响应

### 3. 策略模式 (Strategy Pattern)
- 不同邀请模式的处理策略
- 可配置的行为模式

## 注意事项

1. **状态同步**: 确保与聊天窗口补丁的状态同步
2. **性能优化**: useEffect的依赖数组要准确
3. **用户体验**: 避免重复的邀请触发
4. **错误处理**: 完善的异常处理机制

## 扩展建议

1. **智能邀请**: 基于上下文的智能邀请决策
2. **邀请队列**: 支持邀请队列管理
3. **批量操作**: 支持批量邀请处理
4. **用户偏好**: 支持用户自定义邀请行为
5. **分析统计**: 添加邀请操作的统计分析

该补丁为视频通话系统提供了主界面的智能邀请流程，与聊天窗口补丁形成完整的邀请体验。

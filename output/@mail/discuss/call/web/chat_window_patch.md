# Chat Window Patch - 聊天窗口补丁

## 概述

`chat_window_patch.js` 实现了对 Odoo 讨论应用中聊天窗口组件的补丁扩展，为聊天窗口添加了自动邀请功能。该补丁通过Odoo的补丁机制扩展了ChatWindow组件，使用OWL的useEffect钩子监听邀请线程状态，当检测到需要打开邀请界面时自动触发邀请操作，提供了无缝的用户邀请体验，是视频通话系统中用户邀请流程的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/call/web/chat_window_patch.js`
- **行数**: 32
- **模块**: `@mail/discuss/call/web/chat_window_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/chat_window'  // 聊天窗口组件
'@web/core/utils/patch'          // 补丁工具
'@odoo/owl'                      // OWL 框架
```

## 补丁定义

### ChatWindow 补丁

```javascript
patch(ChatWindow.prototype, {
    setup(...args) {
        super.setup(...args);
        useEffect(
            () => {
                if (this.props.chatWindow.thread === this.store.openInviteThread) {
                    this.threadActions.actions
                        .find((action) => action.id === "invite-people")
                        ?.onSelect();
                    this.store.openInviteThread = null;
                }
            },
            () => [this.store.openInviteThread]
        );
    },
});
```

**补丁特性**:
- 扩展setup方法
- 使用useEffect钩子
- 自动邀请触发
- 状态清理机制

## 核心功能

### 1. 自动邀请触发

```javascript
if (this.props.chatWindow.thread === this.store.openInviteThread) {
    this.threadActions.actions
        .find((action) => action.id === "invite-people")
        ?.onSelect();
    this.store.openInviteThread = null;
}
```

**触发逻辑**:
- **条件检查**: 当前聊天窗口线程是否为待邀请线程
- **操作查找**: 查找"invite-people"操作
- **自动执行**: 自动触发邀请操作
- **状态清理**: 清除邀请线程状态

### 2. 响应式监听

```javascript
useEffect(
    () => { /* 邀请逻辑 */ },
    () => [this.store.openInviteThread]
);
```

**监听机制**:
- **依赖监听**: 监听openInviteThread状态变化
- **自动执行**: 状态变化时自动执行邀请逻辑
- **性能优化**: 只在相关状态变化时执行

## 使用场景

### 1. 邀请流程增强

```javascript
// 邀请流程增强功能
const InviteFlowEnhancer = {
    enhanceInviteFlow: () => {
        const EnhancedChatWindowPatch = {
            setup(...args) {
                super.setup(...args);
                
                // 增强的邀请状态管理
                this.inviteState = useState({
                    pendingInvites: new Map(),
                    inviteHistory: [],
                    autoInviteEnabled: true,
                    inviteDelay: 0
                });
                
                // 设置增强的邀请监听
                this.setupEnhancedInviteListening();
                
                // 设置邀请分析
                this.setupInviteAnalytics();
            },
            
            // 设置增强的邀请监听
            setupEnhancedInviteListening() {
                useEffect(
                    () => {
                        this.handleInviteRequest();
                    },
                    () => [
                        this.store.openInviteThread,
                        this.inviteState.autoInviteEnabled,
                        this.inviteState.inviteDelay
                    ]
                );
                
                // 监听邀请相关事件
                this.setupInviteEventListeners();
            },
            
            // 处理邀请请求
            async handleInviteRequest() {
                const thread = this.store.openInviteThread;
                if (!thread || !this.inviteState.autoInviteEnabled) {
                    return;
                }
                
                // 检查是否为当前窗口的线程
                if (this.props.chatWindow.thread !== thread) {
                    return;
                }
                
                try {
                    // 记录邀请开始
                    this.recordInviteStart(thread);
                    
                    // 应用邀请延迟
                    if (this.inviteState.inviteDelay > 0) {
                        await this.delay(this.inviteState.inviteDelay);
                    }
                    
                    // 执行邀请操作
                    await this.executeInviteAction(thread);
                    
                    // 记录邀请成功
                    this.recordInviteSuccess(thread);
                    
                } catch (error) {
                    console.error('自动邀请失败:', error);
                    this.recordInviteError(thread, error);
                } finally {
                    // 清理邀请状态
                    this.store.openInviteThread = null;
                }
            },
            
            // 执行邀请操作
            async executeInviteAction(thread) {
                const inviteAction = this.threadActions.actions
                    .find((action) => action.id === "invite-people");
                
                if (!inviteAction) {
                    throw new Error('邀请操作不可用');
                }
                
                // 检查邀请权限
                if (!this.checkInvitePermission(thread)) {
                    throw new Error('没有邀请权限');
                }
                
                // 执行邀请
                await inviteAction.onSelect();
                
                // 更新邀请状态
                this.updateInviteStatus(thread, 'completed');
            },
            
            // 检查邀请权限
            checkInvitePermission(thread) {
                const user = this.store.self;
                
                // 检查用户权限
                if (!user) return false;
                
                // 检查线程类型权限
                if (thread.channel_type === 'channel') {
                    // 频道邀请权限检查
                    const membership = thread.channelMembers.find(m => m.persona.id === user.id);
                    return membership && (membership.role === 'admin' || membership.role === 'moderator');
                } else if (thread.channel_type === 'chat') {
                    // 私聊邀请权限检查
                    return true; // 私聊默认允许邀请
                }
                
                return false;
            },
            
            // 记录邀请开始
            recordInviteStart(thread) {
                const inviteRecord = {
                    id: Date.now().toString(),
                    threadId: thread.id,
                    threadName: thread.displayName,
                    startTime: Date.now(),
                    status: 'started',
                    user: this.store.self?.name || 'Unknown'
                };
                
                this.inviteState.pendingInvites.set(inviteRecord.id, inviteRecord);
                this.inviteState.inviteHistory.push(inviteRecord);
                
                // 限制历史记录数量
                if (this.inviteState.inviteHistory.length > 100) {
                    this.inviteState.inviteHistory.shift();
                }
            },
            
            // 记录邀请成功
            recordInviteSuccess(thread) {
                const pendingInvite = Array.from(this.inviteState.pendingInvites.values())
                    .find(invite => invite.threadId === thread.id && invite.status === 'started');
                
                if (pendingInvite) {
                    pendingInvite.status = 'success';
                    pendingInvite.endTime = Date.now();
                    pendingInvite.duration = pendingInvite.endTime - pendingInvite.startTime;
                    
                    this.inviteState.pendingInvites.delete(pendingInvite.id);
                }
                
                // 发送成功事件
                this.trigger('invite_success', { thread, invite: pendingInvite });
            },
            
            // 记录邀请错误
            recordInviteError(thread, error) {
                const pendingInvite = Array.from(this.inviteState.pendingInvites.values())
                    .find(invite => invite.threadId === thread.id && invite.status === 'started');
                
                if (pendingInvite) {
                    pendingInvite.status = 'error';
                    pendingInvite.error = error.message;
                    pendingInvite.endTime = Date.now();
                    pendingInvite.duration = pendingInvite.endTime - pendingInvite.startTime;
                    
                    this.inviteState.pendingInvites.delete(pendingInvite.id);
                }
                
                // 发送错误事件
                this.trigger('invite_error', { thread, error, invite: pendingInvite });
            },
            
            // 更新邀请状态
            updateInviteStatus(thread, status) {
                const pendingInvite = Array.from(this.inviteState.pendingInvites.values())
                    .find(invite => invite.threadId === thread.id);
                
                if (pendingInvite) {
                    pendingInvite.status = status;
                    pendingInvite.lastUpdate = Date.now();
                }
            },
            
            // 设置邀请事件监听
            setupInviteEventListeners() {
                // 监听邀请成功事件
                this.addEventListener('invite_success', (event) => {
                    this.handleInviteSuccess(event.detail);
                });
                
                // 监听邀请错误事件
                this.addEventListener('invite_error', (event) => {
                    this.handleInviteError(event.detail);
                });
                
                // 监听窗口关闭事件
                onWillUnmount(() => {
                    this.cleanupPendingInvites();
                });
            },
            
            // 处理邀请成功
            handleInviteSuccess(detail) {
                const { thread, invite } = detail;
                
                // 显示成功通知
                this.env.services.notification.add(
                    `已成功打开 ${thread.displayName} 的邀请界面`,
                    { type: 'success' }
                );
                
                // 发送分析数据
                this.sendInviteAnalytics('success', invite);
            },
            
            // 处理邀请错误
            handleInviteError(detail) {
                const { thread, error, invite } = detail;
                
                // 显示错误通知
                this.env.services.notification.add(
                    `打开 ${thread.displayName} 邀请界面失败: ${error.message}`,
                    { type: 'error' }
                );
                
                // 发送分析数据
                this.sendInviteAnalytics('error', invite);
            },
            
            // 清理待处理邀请
            cleanupPendingInvites() {
                this.inviteState.pendingInvites.forEach((invite, id) => {
                    if (invite.status === 'started') {
                        invite.status = 'cancelled';
                        invite.endTime = Date.now();
                        invite.duration = invite.endTime - invite.startTime;
                    }
                });
                
                this.inviteState.pendingInvites.clear();
            },
            
            // 设置邀请分析
            setupInviteAnalytics() {
                this.inviteAnalytics = {
                    totalInvites: 0,
                    successfulInvites: 0,
                    failedInvites: 0,
                    averageDuration: 0
                };
            },
            
            // 发送邀请分析数据
            sendInviteAnalytics(type, invite) {
                this.inviteAnalytics.totalInvites++;
                
                if (type === 'success') {
                    this.inviteAnalytics.successfulInvites++;
                } else if (type === 'error') {
                    this.inviteAnalytics.failedInvites++;
                }
                
                // 计算平均时长
                if (invite && invite.duration) {
                    const totalDuration = this.inviteAnalytics.averageDuration * (this.inviteAnalytics.totalInvites - 1) + invite.duration;
                    this.inviteAnalytics.averageDuration = totalDuration / this.inviteAnalytics.totalInvites;
                }
                
                // 发送到分析服务
                try {
                    this.env.services.analytics?.track('invite_flow', {
                        type: type,
                        threadType: invite?.threadType,
                        duration: invite?.duration,
                        analytics: this.inviteAnalytics
                    });
                } catch (error) {
                    console.warn('发送邀请分析数据失败:', error);
                }
            },
            
            // 延迟函数
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            },
            
            // 获取邀请统计
            getInviteStatistics() {
                return {
                    ...this.inviteAnalytics,
                    pendingCount: this.inviteState.pendingInvites.size,
                    historyCount: this.inviteState.inviteHistory.length,
                    successRate: this.inviteAnalytics.totalInvites > 0 
                        ? (this.inviteAnalytics.successfulInvites / this.inviteAnalytics.totalInvites * 100).toFixed(2) + '%'
                        : '0%'
                };
            },
            
            // 设置自动邀请
            setAutoInviteEnabled(enabled) {
                this.inviteState.autoInviteEnabled = enabled;
            },
            
            // 设置邀请延迟
            setInviteDelay(delay) {
                this.inviteState.inviteDelay = Math.max(0, delay);
            }
        };
        
        patch(ChatWindow.prototype, EnhancedChatWindowPatch);
    }
};

// 应用邀请流程增强
InviteFlowEnhancer.enhanceInviteFlow();
```

### 2. 邀请权限管理

```javascript
// 邀请权限管理
const InvitePermissionManager = {
    setupPermissionControl: () => {
        const PermissionPatch = {
            setup(...args) {
                super.setup(...args);
                
                // 权限配置
                this.permissionConfig = {
                    requireAdminForChannels: true,
                    allowGuestInvites: false,
                    maxInvitesPerHour: 10,
                    inviteHistory: []
                };
                
                // 设置权限检查
                this.setupPermissionChecks();
            },
            
            // 设置权限检查
            setupPermissionChecks() {
                useEffect(
                    () => {
                        this.handlePermissionBasedInvite();
                    },
                    () => [this.store.openInviteThread]
                );
            },
            
            // 基于权限的邀请处理
            async handlePermissionBasedInvite() {
                const thread = this.store.openInviteThread;
                if (!thread) return;
                
                if (this.props.chatWindow.thread !== thread) return;
                
                try {
                    // 检查邀请权限
                    const permissionCheck = await this.checkInvitePermissions(thread);
                    
                    if (!permissionCheck.allowed) {
                        this.handlePermissionDenied(thread, permissionCheck.reason);
                        return;
                    }
                    
                    // 检查邀请频率限制
                    if (!this.checkInviteRateLimit()) {
                        this.handleRateLimitExceeded(thread);
                        return;
                    }
                    
                    // 执行邀请
                    await this.executePermissionedInvite(thread);
                    
                } catch (error) {
                    console.error('权限检查失败:', error);
                    this.handlePermissionError(thread, error);
                } finally {
                    this.store.openInviteThread = null;
                }
            },
            
            // 检查邀请权限
            async checkInvitePermissions(thread) {
                const user = this.store.self;
                
                if (!user) {
                    return { allowed: false, reason: '用户未登录' };
                }
                
                // 检查线程类型权限
                if (thread.channel_type === 'channel') {
                    return this.checkChannelInvitePermission(thread, user);
                } else if (thread.channel_type === 'chat') {
                    return this.checkChatInvitePermission(thread, user);
                }
                
                return { allowed: false, reason: '未知的线程类型' };
            },
            
            // 检查频道邀请权限
            checkChannelInvitePermission(thread, user) {
                const membership = thread.channelMembers.find(m => m.persona.id === user.id);
                
                if (!membership) {
                    return { allowed: false, reason: '不是频道成员' };
                }
                
                if (this.permissionConfig.requireAdminForChannels) {
                    if (membership.role !== 'admin' && membership.role !== 'moderator') {
                        return { allowed: false, reason: '需要管理员或版主权限' };
                    }
                }
                
                return { allowed: true, reason: '权限验证通过' };
            },
            
            // 检查聊天邀请权限
            checkChatInvitePermission(thread, user) {
                // 检查是否允许访客邀请
                if (!this.permissionConfig.allowGuestInvites && user.isGuest) {
                    return { allowed: false, reason: '访客不允许邀请' };
                }
                
                return { allowed: true, reason: '权限验证通过' };
            },
            
            // 检查邀请频率限制
            checkInviteRateLimit() {
                const now = Date.now();
                const oneHourAgo = now - (60 * 60 * 1000);
                
                // 清理过期的邀请记录
                this.permissionConfig.inviteHistory = this.permissionConfig.inviteHistory
                    .filter(invite => invite.timestamp > oneHourAgo);
                
                // 检查是否超过限制
                return this.permissionConfig.inviteHistory.length < this.permissionConfig.maxInvitesPerHour;
            },
            
            // 执行有权限的邀请
            async executePermissionedInvite(thread) {
                const inviteAction = this.threadActions.actions
                    .find((action) => action.id === "invite-people");
                
                if (!inviteAction) {
                    throw new Error('邀请操作不可用');
                }
                
                // 记录邀请
                this.recordInviteAttempt(thread);
                
                // 执行邀请
                await inviteAction.onSelect();
                
                // 显示成功消息
                this.showPermissionSuccessMessage(thread);
            },
            
            // 记录邀请尝试
            recordInviteAttempt(thread) {
                this.permissionConfig.inviteHistory.push({
                    threadId: thread.id,
                    threadName: thread.displayName,
                    timestamp: Date.now(),
                    user: this.store.self?.name || 'Unknown'
                });
            },
            
            // 处理权限被拒绝
            handlePermissionDenied(thread, reason) {
                this.env.services.notification.add(
                    `无法邀请到 ${thread.displayName}: ${reason}`,
                    { type: 'warning' }
                );
                
                console.warn('邀请权限被拒绝:', { thread: thread.displayName, reason });
            },
            
            // 处理频率限制超出
            handleRateLimitExceeded(thread) {
                this.env.services.notification.add(
                    `邀请频率过高，请稍后再试。每小时最多 ${this.permissionConfig.maxInvitesPerHour} 次邀请。`,
                    { type: 'warning' }
                );
                
                console.warn('邀请频率限制超出:', { thread: thread.displayName });
            },
            
            // 处理权限错误
            handlePermissionError(thread, error) {
                this.env.services.notification.add(
                    `邀请权限检查失败: ${error.message}`,
                    { type: 'error' }
                );
                
                console.error('邀请权限错误:', error);
            },
            
            // 显示权限成功消息
            showPermissionSuccessMessage(thread) {
                this.env.services.notification.add(
                    `已成功打开 ${thread.displayName} 的邀请界面`,
                    { type: 'success' }
                );
            },
            
            // 更新权限配置
            updatePermissionConfig(newConfig) {
                Object.assign(this.permissionConfig, newConfig);
            },
            
            // 获取权限统计
            getPermissionStatistics() {
                const now = Date.now();
                const oneHourAgo = now - (60 * 60 * 1000);
                const recentInvites = this.permissionConfig.inviteHistory
                    .filter(invite => invite.timestamp > oneHourAgo);
                
                return {
                    totalInvites: this.permissionConfig.inviteHistory.length,
                    recentInvites: recentInvites.length,
                    remainingInvites: Math.max(0, this.permissionConfig.maxInvitesPerHour - recentInvites.length),
                    nextResetTime: recentInvites.length > 0 
                        ? new Date(Math.min(...recentInvites.map(i => i.timestamp)) + 60 * 60 * 1000)
                        : new Date()
                };
            }
        };
        
        patch(ChatWindow.prototype, PermissionPatch);
    }
};

// 应用邀请权限管理
InvitePermissionManager.setupPermissionControl();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 响应式设计
- useEffect钩子使用
- 状态依赖监听
- 自动化操作触发

### 3. 状态管理
- 全局状态监听
- 状态清理机制
- 条件判断逻辑

### 4. 用户体验
- 自动化邀请流程
- 无缝操作体验
- 智能状态处理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 状态变化监听
- 自动化响应

### 3. 命令模式 (Command Pattern)
- 操作的查找和执行
- 解耦操作定义和执行

## 注意事项

1. **状态管理**: 确保状态的正确清理和更新
2. **性能影响**: useEffect的依赖数组要准确
3. **错误处理**: 完善的异常处理机制
4. **用户反馈**: 提供清晰的操作反馈

## 扩展建议

1. **权限控制**: 添加邀请权限检查机制
2. **批量邀请**: 支持批量邀请多个线程
3. **邀请历史**: 记录和管理邀请历史
4. **自定义配置**: 支持用户自定义邀请行为
5. **分析统计**: 添加邀请操作的统计分析

该补丁为视频通话系统提供了智能的邀请流程自动化，是用户体验优化的重要组成部分。

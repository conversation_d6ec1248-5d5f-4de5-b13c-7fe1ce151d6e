# Pinned Messages Panel - 固定消息面板

## 概述

`pinned_messages_panel.js` 实现了 Odoo 讨论应用中的固定消息面板组件，提供了固定消息的集中显示和管理功能。该组件集成了消息卡片列表、操作面板等子组件，支持自动获取固定消息、属性更新响应和空状态显示等特性，为用户提供了便捷的固定消息浏览和管理界面，是消息固定功能的核心展示组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js`
- **行数**: 53
- **模块**: `@mail/discuss/message_pin/common/pinned_messages_panel`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message_card_list'        // 消息卡片列表组件
'@mail/discuss/core/common/action_panel'     // 操作面板组件
'@odoo/owl'                                  // OWL 框架
'@web/core/l10n/translation'                 // 国际化
```

## 组件定义

### PinnedMessagesPanel 类

```javascript
const PinnedMessagesPanel = class PinnedMessagesPanel extends Component {
    static components = {
        MessageCardList,
        ActionPanel,
    };
    static props = ["thread", "className?"];
    static template = "discuss.PinnedMessagesPanel";
}
```

**组件特性**:
- 使用专用模板
- 集成消息卡片列表
- 包含操作面板
- 支持线程属性

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    onWillStart(() => {
        this.props.thread.fetchPinnedMessages();
    });
    onWillUpdateProps((nextProps) => {
        if (nextProps.thread.notEq(this.props.thread)) {
            nextProps.thread.fetchPinnedMessages();
        }
    });
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **数据获取**: 组件启动时获取固定消息
- **属性监听**: 监听属性变化并重新获取数据
- **线程比较**: 比较线程变化决定是否重新获取

### 2. 空状态文本

```javascript
get emptyText() {
    if (this.props.thread.channel_type === "channel") {
        return _t("This channel doesn't have any pinned messages.");
    } else {
        return _t("This conversation doesn't have any pinned messages.");
    }
}
```

**空状态功能**:
- **类型检查**: 根据线程类型显示不同文本
- **国际化**: 使用翻译函数支持多语言
- **用户友好**: 提供清晰的空状态提示
- **上下文相关**: 根据上下文显示合适的消息

## 使用场景

### 1. 固定消息面板增强

```javascript
// 固定消息面板增强功能
const PinnedMessagesPanelEnhancer = {
    enhancePinnedMessagesPanel: () => {
        const EnhancedPinnedMessagesPanel = class extends PinnedMessagesPanel {
            static components = {
                ...PinnedMessagesPanel.components,
                PinnedMessageSearch,
                PinnedMessageFilter,
                PinnedMessageSort,
                PinnedMessageExport
            };
            
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.state = useState({
                    loading: false,
                    error: null,
                    searchTerm: '',
                    filterBy: 'all', // all, recent, priority, author
                    sortBy: 'pinned_date', // pinned_date, message_date, priority, author
                    sortOrder: 'desc', // asc, desc
                    selectedMessages: new Set(),
                    viewMode: 'card', // card, list, compact
                    showActions: true,
                    autoRefresh: true,
                    refreshInterval: 30000 // 30秒
                });
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.dialogService = useService("dialog");
                this.actionService = useService("action");
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置自动刷新
                this.setupAutoRefresh();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置拖拽排序
                this.setupDragAndDrop();
                
                // 设置批量操作
                this.setupBatchOperations();
                
                // 加载用户偏好
                this.loadUserPreferences();
            },
            
            // 增强的空状态文本
            get emptyText() {
                // 如果有搜索或过滤条件
                if (this.state.searchTerm || this.state.filterBy !== 'all') {
                    return _t("No pinned messages match your criteria.");
                }
                
                // 如果正在加载
                if (this.state.loading) {
                    return _t("Loading pinned messages...");
                }
                
                // 如果有错误
                if (this.state.error) {
                    return _t("Failed to load pinned messages. Please try again.");
                }
                
                // 原有逻辑
                return super.emptyText;
            },
            
            // 获取过滤后的固定消息
            get filteredPinnedMessages() {
                let messages = this.props.thread.pinnedMessages || [];
                
                // 应用搜索过滤
                if (this.state.searchTerm) {
                    messages = this.filterBySearch(messages);
                }
                
                // 应用分类过滤
                messages = this.filterByCategory(messages);
                
                // 应用排序
                messages = this.sortMessages(messages);
                
                return messages;
            },
            
            // 按搜索词过滤
            filterBySearch(messages) {
                const searchTerm = this.state.searchTerm.toLowerCase();
                
                return messages.filter(message => {
                    // 搜索消息内容
                    if (message.body && message.body.toLowerCase().includes(searchTerm)) {
                        return true;
                    }
                    
                    // 搜索作者名称
                    if (message.author?.name?.toLowerCase().includes(searchTerm)) {
                        return true;
                    }
                    
                    // 搜索固定原因
                    if (message.pin_reason && message.pin_reason.toLowerCase().includes(searchTerm)) {
                        return true;
                    }
                    
                    return false;
                });
            },
            
            // 按分类过滤
            filterByCategory(messages) {
                switch (this.state.filterBy) {
                    case 'recent':
                        // 最近7天固定的消息
                        const recentThreshold = Date.now() - 7 * 24 * 60 * 60 * 1000;
                        return messages.filter(message => 
                            new Date(message.pinned_at).getTime() > recentThreshold
                        );
                        
                    case 'priority':
                        // 高优先级消息
                        return messages.filter(message => 
                            message.pin_priority > 0
                        );
                        
                    case 'author':
                        // 当前用户固定的消息
                        const currentUserId = this.env.services.user.userId;
                        return messages.filter(message => 
                            message.pinned_by?.id === currentUserId
                        );
                        
                    case 'expiring':
                        // 即将过期的消息
                        const expiringThreshold = Date.now() + 24 * 60 * 60 * 1000; // 24小时内
                        return messages.filter(message => 
                            message.pin_expires_at && 
                            new Date(message.pin_expires_at).getTime() < expiringThreshold
                        );
                        
                    default:
                        return messages;
                }
            },
            
            // 排序消息
            sortMessages(messages) {
                const sortBy = this.state.sortBy;
                const sortOrder = this.state.sortOrder;
                
                const sorted = [...messages].sort((a, b) => {
                    let comparison = 0;
                    
                    switch (sortBy) {
                        case 'pinned_date':
                            comparison = new Date(a.pinned_at) - new Date(b.pinned_at);
                            break;
                            
                        case 'message_date':
                            comparison = new Date(a.date) - new Date(b.date);
                            break;
                            
                        case 'priority':
                            comparison = (a.pin_priority || 0) - (b.pin_priority || 0);
                            break;
                            
                        case 'author':
                            const authorA = a.author?.name || '';
                            const authorB = b.author?.name || '';
                            comparison = authorA.localeCompare(authorB);
                            break;
                            
                        default:
                            comparison = 0;
                    }
                    
                    return sortOrder === 'desc' ? -comparison : comparison;
                });
                
                return sorted;
            },
            
            // 搜索固定消息
            onSearchMessages(searchTerm) {
                this.state.searchTerm = searchTerm;
                this.recordSearchAction(searchTerm);
            },
            
            // 过滤固定消息
            onFilterMessages(filterBy) {
                this.state.filterBy = filterBy;
                this.recordFilterAction(filterBy);
                this.saveUserPreferences();
            },
            
            // 排序固定消息
            onSortMessages(sortBy, sortOrder) {
                this.state.sortBy = sortBy;
                this.state.sortOrder = sortOrder;
                this.recordSortAction(sortBy, sortOrder);
                this.saveUserPreferences();
            },
            
            // 切换视图模式
            onSwitchViewMode(viewMode) {
                this.state.viewMode = viewMode;
                this.saveUserPreferences();
            },
            
            // 选择消息
            onSelectMessage(message, selected) {
                if (selected) {
                    this.state.selectedMessages.add(message.id);
                } else {
                    this.state.selectedMessages.delete(message.id);
                }
            },
            
            // 全选/取消全选
            onSelectAll(selectAll) {
                if (selectAll) {
                    this.filteredPinnedMessages.forEach(message => {
                        this.state.selectedMessages.add(message.id);
                    });
                } else {
                    this.state.selectedMessages.clear();
                }
            },
            
            // 批量取消固定
            async onBatchUnpin() {
                try {
                    const selectedIds = Array.from(this.state.selectedMessages);
                    const messages = this.filteredPinnedMessages.filter(m => 
                        selectedIds.includes(m.id)
                    );
                    
                    if (messages.length === 0) {
                        return;
                    }
                    
                    // 确认对话框
                    const confirmed = await this.showBatchUnpinConfirm(messages.length);
                    if (!confirmed) {
                        return;
                    }
                    
                    // 执行批量取消固定
                    this.state.loading = true;
                    const results = await Promise.allSettled(
                        messages.map(message => message.unpin({ skipConfirm: true }))
                    );
                    
                    // 统计结果
                    const successful = results.filter(r => r.status === 'fulfilled').length;
                    const failed = results.filter(r => r.status === 'rejected').length;
                    
                    // 显示结果
                    if (successful > 0) {
                        this.notificationService.add(
                            _t('%s messages unpinned successfully', successful),
                            { type: 'success' }
                        );
                    }
                    
                    if (failed > 0) {
                        this.notificationService.add(
                            _t('%s messages failed to unpin', failed),
                            { type: 'warning' }
                        );
                    }
                    
                    // 清除选择
                    this.state.selectedMessages.clear();
                    
                } catch (error) {
                    console.error('批量取消固定失败:', error);
                    this.notificationService.add('批量操作失败', { type: 'error' });
                } finally {
                    this.state.loading = false;
                }
            },
            
            // 导出固定消息
            async onExportMessages() {
                try {
                    const messages = this.filteredPinnedMessages;
                    
                    if (messages.length === 0) {
                        this.notificationService.add('没有可导出的消息', { type: 'warning' });
                        return;
                    }
                    
                    // 生成导出数据
                    const exportData = this.generateExportData(messages);
                    
                    // 下载文件
                    this.downloadExportFile(exportData);
                    
                    this.notificationService.add(
                        _t('%s messages exported successfully', messages.length),
                        { type: 'success' }
                    );
                    
                } catch (error) {
                    console.error('导出消息失败:', error);
                    this.notificationService.add('导出失败', { type: 'error' });
                }
            },
            
            // 刷新固定消息
            async onRefreshMessages() {
                try {
                    this.state.loading = true;
                    this.state.error = null;
                    
                    await this.props.thread.fetchPinnedMessages();
                    
                    this.notificationService.add('固定消息已刷新', { type: 'success' });
                    
                } catch (error) {
                    console.error('刷新固定消息失败:', error);
                    this.state.error = error.message;
                    this.notificationService.add('刷新失败', { type: 'error' });
                } finally {
                    this.state.loading = false;
                }
            },
            
            // 设置自动刷新
            setupAutoRefresh() {
                if (this.state.autoRefresh) {
                    this.refreshTimer = setInterval(() => {
                        if (document.visibilityState === 'visible') {
                            this.props.thread.fetchPinnedMessages();
                        }
                    }, this.state.refreshInterval);
                }
                
                onWillUnmount(() => {
                    if (this.refreshTimer) {
                        clearInterval(this.refreshTimer);
                    }
                });
            },
            
            // 显示批量取消固定确认
            async showBatchUnpinConfirm(count) {
                return new Promise((resolve) => {
                    this.dialogService.add(ConfirmDialog, {
                        title: _t('Unpin Messages'),
                        body: _t('Are you sure you want to unpin %s messages?', count),
                        confirmText: _t('Unpin'),
                        cancelText: _t('Cancel'),
                        onConfirm: () => resolve(true),
                        onCancel: () => resolve(false)
                    });
                });
            },
            
            // 生成导出数据
            generateExportData(messages) {
                const data = messages.map(message => ({
                    id: message.id,
                    content: message.body,
                    author: message.author?.name || 'Unknown',
                    date: message.date,
                    pinned_at: message.pinned_at,
                    pinned_by: message.pinned_by?.name || 'Unknown',
                    pin_reason: message.pin_reason || '',
                    pin_priority: message.pin_priority || 0
                }));
                
                return JSON.stringify(data, null, 2);
            },
            
            // 下载导出文件
            downloadExportFile(data) {
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `pinned_messages_${Date.now()}.json`;
                a.click();
                
                URL.revokeObjectURL(url);
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('pinned_messages_panel_preferences') || '{}'
                    );
                    
                    this.state.filterBy = prefs.filterBy || 'all';
                    this.state.sortBy = prefs.sortBy || 'pinned_date';
                    this.state.sortOrder = prefs.sortOrder || 'desc';
                    this.state.viewMode = prefs.viewMode || 'card';
                    this.state.autoRefresh = prefs.autoRefresh !== false;
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const prefs = {
                        filterBy: this.state.filterBy,
                        sortBy: this.state.sortBy,
                        sortOrder: this.state.sortOrder,
                        viewMode: this.state.viewMode,
                        autoRefresh: this.state.autoRefresh
                    };
                    
                    localStorage.setItem('pinned_messages_panel_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 记录搜索操作
            recordSearchAction(searchTerm) {
                try {
                    const searches = JSON.parse(
                        localStorage.getItem('pinned_messages_searches') || '[]'
                    );
                    
                    searches.push({
                        term: searchTerm,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    });
                    
                    // 保留最近50个搜索
                    if (searches.length > 50) {
                        searches.splice(0, searches.length - 50);
                    }
                    
                    localStorage.setItem('pinned_messages_searches', JSON.stringify(searches));
                } catch (error) {
                    console.warn('记录搜索操作失败:', error);
                }
            },
            
            // 获取固定消息面板统计
            getPinnedMessagesPanelStatistics() {
                return {
                    totalPinnedMessages: this.props.thread.pinnedMessages?.length || 0,
                    filteredMessages: this.filteredPinnedMessages.length,
                    selectedMessages: this.state.selectedMessages.size,
                    currentFilter: this.state.filterBy,
                    currentSort: `${this.state.sortBy}_${this.state.sortOrder}`,
                    currentViewMode: this.state.viewMode,
                    searchTerm: this.state.searchTerm,
                    autoRefreshEnabled: this.state.autoRefresh
                };
            }
        };
        
        // 替换原始组件
        __exports.PinnedMessagesPanel = EnhancedPinnedMessagesPanel;
    }
};

// 应用固定消息面板增强
PinnedMessagesPanelEnhancer.enhancePinnedMessagesPanel();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 子组件集成
- 模板驱动渲染

### 2. 生命周期管理
- 组件启动时数据获取
- 属性变化响应
- 自动数据刷新

### 3. 国际化支持
- 多语言空状态文本
- 上下文相关的消息
- 用户友好的提示

### 4. 数据管理
- 自动数据获取
- 智能数据更新
- 状态同步机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 观察者模式 (Observer Pattern)
- 属性变化的监听
- 自动数据更新

### 3. 模板方法模式 (Template Method Pattern)
- 标准化的组件结构
- 可扩展的功能点

## 注意事项

1. **性能优化**: 避免频繁的数据获取影响性能
2. **用户体验**: 提供清晰的加载和空状态提示
3. **数据一致性**: 确保数据的及时更新和同步
4. **内存管理**: 及时清理定时器和事件监听器

## 扩展建议

1. **搜索功能**: 实现固定消息的搜索功能
2. **过滤选项**: 提供更多的过滤和排序选项
3. **批量操作**: 支持批量管理固定消息
4. **导出功能**: 实现固定消息的导出功能
5. **实时更新**: 实现实时的数据更新机制

该组件为讨论应用提供了完整的固定消息展示和管理功能，是消息固定功能的重要用户界面组件。

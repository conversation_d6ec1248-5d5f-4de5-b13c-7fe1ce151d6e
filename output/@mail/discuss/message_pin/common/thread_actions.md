# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 讨论应用中固定消息功能的线程操作注册，专门添加了"固定消息"面板的操作。该文件通过线程操作注册表添加了"pinned-messages"操作，集成了固定消息面板组件、条件检查、图标显示、环境设置和切换功能等特性，为用户在讨论频道中提供了便捷的固定消息管理入口，是消息固定功能的核心操作入口组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/thread_actions.js`
- **行数**: 46
- **模块**: `@mail/discuss/message_pin/common/thread_actions`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_actions'                        // 线程操作注册表
'@mail/discuss/message_pin/common/pinned_messages_panel'  // 固定消息面板组件
'@odoo/owl'                                               // OWL 框架
'@web/core/l10n/translation'                              // 国际化
```

## 操作注册

### pinned-messages 操作

```javascript
threadActionsRegistry.add("pinned-messages", {
    component: PinnedMessagesPanel,
    condition(component) {
        return (
            component.thread?.model === "discuss.channel" &&
            (!component.props.chatWindow || component.props.chatWindow.isOpen)
        );
    },
    panelOuterClass: "o-discuss-PinnedMessagesPanel bg-inherit",
    icon: "fa fa-fw fa-thumb-tack",
    iconLarge: "fa fa-fw fa-lg fa-thumb-tack",
    name: _t("Pinned Messages"),
    nameActive: _t("Hide Pinned Messages"),
    sequence: 20,
    sequenceGroup: 10,
    setup(action) {
        useChildSubEnv({
            pinMenu: {
                open: () => action.open(),
                close: () => {
                    if (action.isActive) {
                        action.close();
                    }
                },
            },
        });
    },
    toggle: true,
});
```

**操作特性**:
- 组件集成
- 条件显示控制
- 图标和样式配置
- 环境设置
- 切换功能支持

## 核心功能

### 1. 显示条件

```javascript
condition(component) {
    return (
        component.thread?.model === "discuss.channel" &&
        (!component.props.chatWindow || component.props.chatWindow.isOpen)
    );
}
```

**条件检查功能**:
- **线程模型**: 仅在讨论频道中显示
- **聊天窗口**: 在聊天窗口中需要窗口处于打开状态
- **环境适配**: 适配不同的显示环境
- **权限控制**: 确保在正确的上下文中显示

### 2. 环境设置

```javascript
setup(action) {
    useChildSubEnv({
        pinMenu: {
            open: () => action.open(),
            close: () => {
                if (action.isActive) {
                    action.close();
                }
            },
        },
    });
}
```

**环境设置功能**:
- **子环境**: 创建子组件环境
- **菜单接口**: 提供固定菜单的操作接口
- **打开方法**: 封装操作的打开方法
- **关闭方法**: 提供安全的关闭方法
- **状态检查**: 检查操作状态避免重复关闭

### 3. 视觉配置

```javascript
panelOuterClass: "o-discuss-PinnedMessagesPanel bg-inherit",
icon: "fa fa-fw fa-thumb-tack",
iconLarge: "fa fa-fw fa-lg fa-thumb-tack",
name: _t("Pinned Messages"),
nameActive: _t("Hide Pinned Messages"),
```

**视觉配置功能**:
- **面板样式**: 设置面板的外层CSS类
- **图标设置**: 配置普通和大尺寸图标
- **名称国际化**: 支持多语言的操作名称
- **状态名称**: 区分激活和非激活状态的名称
- **一致性**: 保持视觉风格的一致性

### 4. 排序和分组

```javascript
sequence: 20,
sequenceGroup: 10,
toggle: true,
```

**排序配置功能**:
- **序列号**: 控制操作在列表中的位置
- **分组**: 将相关操作分组显示
- **切换支持**: 支持操作的开关切换
- **用户体验**: 提供直观的操作布局

## 使用场景

### 1. 线程操作增强

```javascript
// 线程操作增强功能
const ThreadActionsEnhancer = {
    enhanceThreadActions: () => {
        // 增强原有的固定消息操作
        const originalPinnedMessagesAction = threadActionsRegistry.get("pinned-messages");
        
        threadActionsRegistry.add("pinned-messages", {
            ...originalPinnedMessagesAction,
            
            // 增强的条件检查
            condition(component) {
                const baseCondition = originalPinnedMessagesAction.condition(component);
                
                if (!baseCondition) {
                    return false;
                }
                
                // 增强的条件检查
                return this.enhancedConditionCheck(component);
            },
            
            // 增强的条件检查
            enhancedConditionCheck(component) {
                // 检查用户权限
                if (!this.hasUserPinnedMessagesPermission(component)) {
                    return false;
                }
                
                // 检查频道设置
                if (!this.isChannelPinnedMessagesEnabled(component.thread)) {
                    return false;
                }
                
                // 检查是否有固定消息
                if (!this.hasPinnedMessagesOrCanPin(component.thread)) {
                    return false;
                }
                
                return true;
            },
            
            // 增强的环境设置
            setup(action) {
                // 调用原有设置
                originalPinnedMessagesAction.setup(action);
                
                // 增强的环境设置
                this.setupEnhancedEnvironment(action);
            },
            
            // 设置增强环境
            setupEnhancedEnvironment(action) {
                // 扩展子环境
                useChildSubEnv({
                    pinMenu: {
                        open: () => this.enhancedOpen(action),
                        close: () => this.enhancedClose(action),
                        toggle: () => this.enhancedToggle(action),
                        isOpen: () => action.isActive,
                        getState: () => this.getPinMenuState(action)
                    },
                    pinnedMessagesPanel: {
                        refresh: () => this.refreshPinnedMessages(action),
                        search: (term) => this.searchPinnedMessages(action, term),
                        filter: (filter) => this.filterPinnedMessages(action, filter),
                        export: () => this.exportPinnedMessages(action),
                        getStatistics: () => this.getPinnedMessagesStatistics(action)
                    }
                });
                
                // 设置快捷键
                this.setupPinnedMessagesShortcuts(action);
                
                // 设置事件监听
                this.setupPinnedMessagesEventListeners(action);
            },
            
            // 增强的打开方法
            async enhancedOpen(action) {
                try {
                    // 记录打开事件
                    this.recordPinnedMessagesOpen();
                    
                    // 预加载数据
                    await this.preloadPinnedMessagesData(action);
                    
                    // 执行打开
                    action.open();
                    
                    // 后处理
                    this.postOpenProcessing(action);
                    
                } catch (error) {
                    console.error('打开固定消息面板失败:', error);
                    this.handleOpenError(error);
                }
            },
            
            // 增强的关闭方法
            enhancedClose(action) {
                try {
                    // 记录关闭事件
                    this.recordPinnedMessagesClose();
                    
                    // 执行关闭
                    if (action.isActive) {
                        action.close();
                    }
                    
                    // 清理资源
                    this.cleanupPinnedMessagesResources();
                    
                } catch (error) {
                    console.error('关闭固定消息面板失败:', error);
                }
            },
            
            // 增强的切换方法
            enhancedToggle(action) {
                if (action.isActive) {
                    this.enhancedClose(action);
                } else {
                    this.enhancedOpen(action);
                }
            },
            
            // 检查用户固定消息权限
            hasUserPinnedMessagesPermission(component) {
                const user = component.store?.self;
                const thread = component.thread;
                
                if (!user || !thread) {
                    return false;
                }
                
                // 检查用户类型
                if (user.type !== 'partner') {
                    return false;
                }
                
                // 检查是否为频道成员
                if (!thread.hasSelfAsMember) {
                    return false;
                }
                
                return true;
            },
            
            // 检查频道是否启用固定消息
            isChannelPinnedMessagesEnabled(thread) {
                if (!thread) {
                    return false;
                }
                
                // 检查频道类型
                if (thread.model !== 'discuss.channel') {
                    return false;
                }
                
                // 检查频道设置
                return thread.pinnedMessagesEnabled !== false;
            },
            
            // 检查是否有固定消息或可以固定
            hasPinnedMessagesOrCanPin(thread) {
                if (!thread) {
                    return false;
                }
                
                // 有固定消息
                if (thread.pinnedMessages && thread.pinnedMessages.length > 0) {
                    return true;
                }
                
                // 可以固定消息
                if (thread.canPinMessages) {
                    return true;
                }
                
                return false;
            },
            
            // 预加载固定消息数据
            async preloadPinnedMessagesData(action) {
                try {
                    const component = action.component;
                    
                    if (component && component.thread) {
                        await component.thread.fetchPinnedMessages();
                    }
                    
                } catch (error) {
                    console.warn('预加载固定消息数据失败:', error);
                }
            },
            
            // 打开后处理
            postOpenProcessing(action) {
                try {
                    // 设置焦点
                    this.setFocusOnPinnedMessagesPanel();
                    
                    // 更新最近访问
                    this.updateRecentAccess('pinned_messages');
                    
                    // 触发打开事件
                    this.triggerPinnedMessagesOpenEvent(action);
                    
                } catch (error) {
                    console.warn('固定消息面板打开后处理失败:', error);
                }
            },
            
            // 刷新固定消息
            async refreshPinnedMessages(action) {
                try {
                    const component = action.component;
                    
                    if (component && component.thread) {
                        await component.thread.fetchPinnedMessages({ force: true });
                    }
                    
                } catch (error) {
                    console.error('刷新固定消息失败:', error);
                }
            },
            
            // 搜索固定消息
            searchPinnedMessages(action, searchTerm) {
                try {
                    // 触发搜索事件
                    this.env.bus.trigger('pinned_messages:search', {
                        term: searchTerm,
                        action: action
                    });
                    
                } catch (error) {
                    console.error('搜索固定消息失败:', error);
                }
            },
            
            // 过滤固定消息
            filterPinnedMessages(action, filter) {
                try {
                    // 触发过滤事件
                    this.env.bus.trigger('pinned_messages:filter', {
                        filter: filter,
                        action: action
                    });
                    
                } catch (error) {
                    console.error('过滤固定消息失败:', error);
                }
            },
            
            // 导出固定消息
            async exportPinnedMessages(action) {
                try {
                    // 触发导出事件
                    this.env.bus.trigger('pinned_messages:export', {
                        action: action
                    });
                    
                } catch (error) {
                    console.error('导出固定消息失败:', error);
                }
            },
            
            // 设置固定消息快捷键
            setupPinnedMessagesShortcuts(action) {
                this.keyboardHandler = (event) => {
                    // Ctrl+Shift+P 切换固定消息面板
                    if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                        event.preventDefault();
                        this.enhancedToggle(action);
                    }
                    
                    // Escape 关闭面板
                    if (event.key === 'Escape' && action.isActive) {
                        event.preventDefault();
                        this.enhancedClose(action);
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 设置事件监听
            setupPinnedMessagesEventListeners(action) {
                // 监听消息固定事件
                this.messagePinnedHandler = (event) => {
                    if (action.isActive) {
                        this.refreshPinnedMessages(action);
                    }
                };
                
                this.env.bus.addEventListener('message:pinned', this.messagePinnedHandler);
                this.env.bus.addEventListener('message:unpinned', this.messagePinnedHandler);
                
                onWillUnmount(() => {
                    this.env.bus.removeEventListener('message:pinned', this.messagePinnedHandler);
                    this.env.bus.removeEventListener('message:unpinned', this.messagePinnedHandler);
                });
            },
            
            // 获取固定菜单状态
            getPinMenuState(action) {
                return {
                    isOpen: action.isActive,
                    component: action.component,
                    thread: action.component?.thread,
                    pinnedMessagesCount: action.component?.thread?.pinnedMessages?.length || 0
                };
            },
            
            // 记录固定消息面板打开
            recordPinnedMessagesOpen() {
                try {
                    const opens = JSON.parse(
                        localStorage.getItem('pinned_messages_panel_opens') || '[]'
                    );
                    
                    opens.push({
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent
                    });
                    
                    // 保留最近100个记录
                    if (opens.length > 100) {
                        opens.splice(0, opens.length - 100);
                    }
                    
                    localStorage.setItem('pinned_messages_panel_opens', JSON.stringify(opens));
                } catch (error) {
                    console.warn('记录固定消息面板打开失败:', error);
                }
            },
            
            // 记录固定消息面板关闭
            recordPinnedMessagesClose() {
                try {
                    const closes = JSON.parse(
                        localStorage.getItem('pinned_messages_panel_closes') || '[]'
                    );
                    
                    closes.push({
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (closes.length > 100) {
                        closes.splice(0, closes.length - 100);
                    }
                    
                    localStorage.setItem('pinned_messages_panel_closes', JSON.stringify(closes));
                } catch (error) {
                    console.warn('记录固定消息面板关闭失败:', error);
                }
            },
            
            // 触发固定消息面板打开事件
            triggerPinnedMessagesOpenEvent(action) {
                try {
                    this.env.bus.trigger('pinned_messages_panel:opened', {
                        action: action,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发固定消息面板打开事件失败:', error);
                }
            },
            
            // 获取固定消息统计
            getPinnedMessagesStatistics(action) {
                try {
                    const opens = JSON.parse(localStorage.getItem('pinned_messages_panel_opens') || '[]');
                    const closes = JSON.parse(localStorage.getItem('pinned_messages_panel_closes') || '[]');
                    
                    return {
                        totalOpens: opens.length,
                        totalCloses: closes.length,
                        isCurrentlyOpen: action.isActive,
                        pinnedMessagesCount: action.component?.thread?.pinnedMessages?.length || 0,
                        lastOpenTime: opens.length > 0 ? opens[opens.length - 1].timestamp : null
                    };
                } catch (error) {
                    return {
                        totalOpens: 0,
                        totalCloses: 0,
                        isCurrentlyOpen: false,
                        pinnedMessagesCount: 0,
                        lastOpenTime: null
                    };
                }
            }
        }, { force: true });
        
        // 添加相关的线程操作
        this.addRelatedThreadActions();
    },
    
    // 添加相关的线程操作
    addRelatedThreadActions() {
        // 添加固定消息搜索操作
        threadActionsRegistry.add("pinned-messages-search", {
            condition(component) {
                return component.thread?.model === "discuss.channel" &&
                       component.thread?.pinnedMessages?.length > 0;
            },
            icon: "fa fa-fw fa-search",
            name: _t("Search Pinned Messages"),
            sequence: 21,
            sequenceGroup: 10,
            onClick(component) {
                this.openPinnedMessagesSearch(component);
            }
        });
        
        // 添加固定消息统计操作
        threadActionsRegistry.add("pinned-messages-stats", {
            condition(component) {
                return component.thread?.model === "discuss.channel" &&
                       component.store?.self?.isAdmin;
            },
            icon: "fa fa-fw fa-bar-chart",
            name: _t("Pinned Messages Statistics"),
            sequence: 22,
            sequenceGroup: 10,
            onClick(component) {
                this.showPinnedMessagesStatistics(component);
            }
        });
    }
};

// 应用线程操作增强
ThreadActionsEnhancer.enhanceThreadActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作配置
- 条件化显示控制

### 2. 组件集成
- 固定消息面板组件集成
- 环境变量设置
- 子组件通信机制

### 3. 用户体验
- 切换功能支持
- 国际化名称显示
- 直观的图标设计

### 4. 环境管理
- 子环境创建
- 菜单接口提供
- 状态管理封装

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态操作配置

### 2. 环境模式 (Environment Pattern)
- 子组件环境管理
- 上下文信息传递

### 3. 策略模式 (Strategy Pattern)
- 不同条件下的显示策略
- 可配置的行为模式

## 注意事项

1. **条件检查**: 确保操作在正确的环境中显示
2. **环境管理**: 正确设置和清理子环境
3. **状态同步**: 保持操作状态的一致性
4. **用户体验**: 提供直观的操作入口

## 扩展建议

1. **更多操作**: 添加更多固定消息相关操作
2. **权限控制**: 实现更细粒度的权限控制
3. **快捷键**: 添加键盘快捷键支持
4. **自定义配置**: 允许用户自定义操作配置
5. **批量操作**: 支持批量固定消息管理

该文件为讨论应用的线程提供了重要的固定消息管理入口，是消息固定功能的核心操作组件。

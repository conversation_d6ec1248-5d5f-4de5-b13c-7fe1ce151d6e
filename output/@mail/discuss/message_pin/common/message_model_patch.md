# Message Model Patch - 消息模型补丁

## 概述

`message_model_patch.js` 实现了对 Odoo 讨论应用消息模型的补丁扩展，专门添加了消息固定功能的核心逻辑。该补丁通过Odoo的补丁机制扩展了Message模型，添加了pinned_at属性、pin方法和unpin方法，集成了确认对话框、异步处理和后端API调用等特性，为消息固定功能提供了完整的数据模型支持，是消息固定功能的核心模型组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/message_model_patch.js`
- **行数**: 83
- **模块**: `@mail/discuss/message_pin/common/message_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'                      // 补丁工具
'@mail/core/common/message_model'            // 消息模型
'@mail/core/common/record'                   // 记录基类
'@web/core/l10n/translation'                 // 国际化
'@mail/core/common/message_confirm_dialog'   // 消息确认对话框
'@web/core/utils/concurrency'                // 并发工具
```

## 补丁定义

### Message 补丁

```javascript
patch(Message.prototype, {
    setup() {
        super.setup();
        /** @type {luxon.DateTime} */
        this.pinned_at = Record.attr(undefined, { type: "datetime" });
    },
    pin() {
        // 固定逻辑
    },
    unpin() {
        // 取消固定逻辑
    },
});
```

**补丁特性**:
- 扩展消息模型功能
- 添加固定时间属性
- 实现固定/取消固定方法
- 集成确认对话框

## 核心功能

### 1. 模型扩展

```javascript
setup() {
    super.setup();
    /** @type {luxon.DateTime} */
    this.pinned_at = Record.attr(undefined, { type: "datetime" });
}
```

**模型扩展功能**:
- **父类调用**: 保持原有的初始化逻辑
- **固定时间**: 添加pinned_at属性记录固定时间
- **类型定义**: 使用datetime类型存储时间
- **默认值**: 初始值为undefined表示未固定

### 2. 消息固定

```javascript
pin() {
    if (this.pinned_at) {
        return this.unpin();
    }
    const def = new Deferred();
    this.store.env.services.dialog.add(
        MessageConfirmDialog,
        {
            confirmText: _t("Yeah, pin it!"),
            message: this,
            prompt: _t(
                "You sure want this message pinned to %(conversation)s forever and ever?",
                {
                    conversation: this.thread.prefix + this.thread.displayName,
                }
            ),
            size: "md",
            title: _t("Pin It"),
            onConfirm: () => {
                def.resolve(true);
                this.store.env.services.orm.call(
                    "discuss.channel",
                    "set_message_pin",
                    [this.thread.id],
                    { message_id: this.id, pinned: true }
                );
            },
        },
        { onClose: () => def.resolve(false) }
    );
    return def;
}
```

**固定功能**:
- **状态检查**: 如果已固定则调用取消固定
- **确认对话框**: 显示固定确认对话框
- **异步处理**: 使用Deferred处理异步操作
- **API调用**: 调用后端API执行固定操作
- **用户体验**: 提供友好的确认提示

### 3. 取消固定

```javascript
unpin() {
    const def = new Deferred();
    this.store.env.services.dialog.add(
        MessageConfirmDialog,
        {
            confirmColor: "btn-danger",
            confirmText: _t("Yes, remove it please"),
            message: this,
            prompt: _t(
                "Well, nothing lasts forever, but are you sure you want to unpin this message?"
            ),
            size: "md",
            title: _t("Unpin Message"),
            onConfirm: () => {
                def.resolve(true);
                this.store.env.services.orm.call(
                    "discuss.channel",
                    "set_message_pin",
                    [this.thread.id],
                    { message_id: this.id, pinned: false }
                );
            },
        },
        { onClose: () => def.resolve(false) }
    );
    return def;
}
```

**取消固定功能**:
- **确认对话框**: 显示取消固定确认对话框
- **危险操作**: 使用危险按钮颜色提示
- **异步处理**: 使用Deferred处理异步操作
- **API调用**: 调用后端API执行取消固定操作
- **用户确认**: 确保用户真的要取消固定

## 使用场景

### 1. 消息模型增强

```javascript
// 消息模型增强功能
const MessageModelPatchEnhancer = {
    enhanceMessageModelPatch: () => {
        const EnhancedMessagePatch = {
            setup() {
                super.setup();
                
                // 原有的固定时间属性
                this.pinned_at = Record.attr(undefined, { type: "datetime" });
                
                // 增强的固定相关属性
                this.pinned_by = Record.attr(undefined, { type: "many2one", relation: "res.partner" });
                this.pin_reason = Record.attr("", { type: "char" });
                this.pin_priority = Record.attr(0, { type: "integer" });
                this.pin_expires_at = Record.attr(undefined, { type: "datetime" });
                this.pin_notification_sent = Record.attr(false, { type: "boolean" });
                
                // 固定状态
                this.pinState = {
                    isPinning: false,
                    isUnpinning: false,
                    lastPinAction: null,
                    pinHistory: []
                };
            },
            
            // 增强的固定方法
            async pin(options = {}) {
                try {
                    // 检查是否已固定
                    if (this.pinned_at && !options.force) {
                        return this.unpin(options);
                    }
                    
                    // 设置固定状态
                    this.pinState.isPinning = true;
                    
                    // 预检查
                    if (!this.canPin()) {
                        throw new Error('无法固定此消息');
                    }
                    
                    // 显示确认对话框（如果需要）
                    if (!options.skipConfirm && this.needsPinConfirmation()) {
                        const confirmed = await this.showPinConfirmDialog(options);
                        if (!confirmed) {
                            return false;
                        }
                    }
                    
                    // 记录固定操作
                    this.recordPinAction('pin', 'started');
                    
                    // 执行固定
                    await this.executePinOperation(true, options);
                    
                    // 后处理
                    await this.postPinProcessing(options);
                    
                    // 记录成功
                    this.recordPinAction('pin', 'success');
                    
                    return true;
                    
                } catch (error) {
                    console.error('固定消息失败:', error);
                    this.recordPinAction('pin', 'error', error);
                    throw error;
                } finally {
                    this.pinState.isPinning = false;
                }
            },
            
            // 增强的取消固定方法
            async unpin(options = {}) {
                try {
                    // 检查是否已固定
                    if (!this.pinned_at) {
                        console.warn('消息未固定，无需取消固定');
                        return false;
                    }
                    
                    // 设置取消固定状态
                    this.pinState.isUnpinning = true;
                    
                    // 预检查
                    if (!this.canUnpin()) {
                        throw new Error('无法取消固定此消息');
                    }
                    
                    // 显示确认对话框（如果需要）
                    if (!options.skipConfirm && this.needsUnpinConfirmation()) {
                        const confirmed = await this.showUnpinConfirmDialog(options);
                        if (!confirmed) {
                            return false;
                        }
                    }
                    
                    // 记录取消固定操作
                    this.recordPinAction('unpin', 'started');
                    
                    // 执行取消固定
                    await this.executePinOperation(false, options);
                    
                    // 后处理
                    await this.postUnpinProcessing(options);
                    
                    // 记录成功
                    this.recordPinAction('unpin', 'success');
                    
                    return true;
                    
                } catch (error) {
                    console.error('取消固定消息失败:', error);
                    this.recordPinAction('unpin', 'error', error);
                    throw error;
                } finally {
                    this.pinState.isUnpinning = false;
                }
            },
            
            // 检查是否可以固定
            canPin() {
                // 检查消息是否存在
                if (!this.id || this.isEmpty) {
                    return false;
                }
                
                // 检查消息是否已删除
                if (this.isDeleted) {
                    return false;
                }
                
                // 检查线程是否支持固定
                if (!this.thread || !this.thread.allowMessagePin) {
                    return false;
                }
                
                // 检查用户权限
                if (!this.hasUserPinPermission()) {
                    return false;
                }
                
                // 检查固定数量限制
                if (!this.thread.canPinMoreMessages()) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否可以取消固定
            canUnpin() {
                // 检查消息是否已固定
                if (!this.pinned_at) {
                    return false;
                }
                
                // 检查用户权限
                if (!this.hasUserUnpinPermission()) {
                    return false;
                }
                
                return true;
            },
            
            // 检查用户固定权限
            hasUserPinPermission() {
                const user = this.store.self;
                const thread = this.thread;
                
                if (!user || !thread) {
                    return false;
                }
                
                // 管理员可以固定
                if (user.isAdmin) {
                    return true;
                }
                
                // 检查频道成员权限
                const selfMember = thread.selfMember;
                if (!selfMember) {
                    return false;
                }
                
                // 版主可以固定
                if (selfMember.role === 'moderator') {
                    return true;
                }
                
                // 消息作者可以固定自己的消息
                if (this.author?.id === user.id && thread.allowAuthorPin) {
                    return true;
                }
                
                return false;
            },
            
            // 检查用户取消固定权限
            hasUserUnpinPermission() {
                const user = this.store.self;
                
                // 管理员可以取消固定
                if (user.isAdmin) {
                    return true;
                }
                
                // 固定者可以取消固定
                if (this.pinned_by?.id === user.id) {
                    return true;
                }
                
                // 版主可以取消固定
                const selfMember = this.thread?.selfMember;
                if (selfMember?.role === 'moderator') {
                    return true;
                }
                
                return false;
            },
            
            // 检查是否需要固定确认
            needsPinConfirmation() {
                // 重要消息需要确认
                if (this.isImportant) {
                    return true;
                }
                
                // 旧消息需要确认
                const messageAge = Date.now() - new Date(this.date).getTime();
                const oldMessageThreshold = 7 * 24 * 60 * 60 * 1000; // 7天
                
                if (messageAge > oldMessageThreshold) {
                    return true;
                }
                
                return false;
            },
            
            // 检查是否需要取消固定确认
            needsUnpinConfirmation() {
                // 固定时间较长的消息需要确认
                if (this.pinned_at) {
                    const pinDuration = Date.now() - new Date(this.pinned_at).getTime();
                    const longPinThreshold = 24 * 60 * 60 * 1000; // 24小时
                    
                    if (pinDuration > longPinThreshold) {
                        return true;
                    }
                }
                
                return false;
            },
            
            // 显示固定确认对话框
            async showPinConfirmDialog(options = {}) {
                const def = new Deferred();
                
                this.store.env.services.dialog.add(
                    MessageConfirmDialog,
                    {
                        confirmText: options.confirmText || _t("Pin Message"),
                        message: this,
                        prompt: options.prompt || _t(
                            "Are you sure you want to pin this message to %(conversation)s?",
                            {
                                conversation: this.thread.prefix + this.thread.displayName,
                            }
                        ),
                        size: options.size || "md",
                        title: options.title || _t("Pin Message"),
                        onConfirm: () => def.resolve(true),
                        additionalFields: this.getPinAdditionalFields()
                    },
                    { onClose: () => def.resolve(false) }
                );
                
                return def;
            },
            
            // 显示取消固定确认对话框
            async showUnpinConfirmDialog(options = {}) {
                const def = new Deferred();
                
                this.store.env.services.dialog.add(
                    MessageConfirmDialog,
                    {
                        confirmColor: "btn-danger",
                        confirmText: options.confirmText || _t("Unpin Message"),
                        message: this,
                        prompt: options.prompt || _t(
                            "Are you sure you want to unpin this message?"
                        ),
                        size: options.size || "md",
                        title: options.title || _t("Unpin Message"),
                        onConfirm: () => def.resolve(true),
                    },
                    { onClose: () => def.resolve(false) }
                );
                
                return def;
            },
            
            // 获取固定附加字段
            getPinAdditionalFields() {
                return [
                    {
                        name: 'pin_reason',
                        label: _t('Reason (optional)'),
                        type: 'text',
                        placeholder: _t('Why are you pinning this message?')
                    },
                    {
                        name: 'pin_priority',
                        label: _t('Priority'),
                        type: 'select',
                        options: [
                            { value: 0, label: _t('Normal') },
                            { value: 1, label: _t('High') },
                            { value: 2, label: _t('Critical') }
                        ]
                    }
                ];
            },
            
            // 执行固定操作
            async executePinOperation(pinned, options = {}) {
                const params = {
                    message_id: this.id,
                    pinned: pinned
                };
                
                // 添加额外参数
                if (options.reason) {
                    params.reason = options.reason;
                }
                
                if (options.priority !== undefined) {
                    params.priority = options.priority;
                }
                
                if (options.expires_at) {
                    params.expires_at = options.expires_at;
                }
                
                // 调用后端API
                const result = await this.store.env.services.orm.call(
                    "discuss.channel",
                    "set_message_pin",
                    [this.thread.id],
                    params
                );
                
                // 更新本地状态
                if (result) {
                    this.updatePinState(result);
                }
                
                return result;
            },
            
            // 更新固定状态
            updatePinState(data) {
                if (data.pinned) {
                    this.pinned_at = data.pinned_at;
                    this.pinned_by = data.pinned_by;
                    this.pin_reason = data.pin_reason || '';
                    this.pin_priority = data.pin_priority || 0;
                    this.pin_expires_at = data.pin_expires_at;
                } else {
                    this.pinned_at = undefined;
                    this.pinned_by = undefined;
                    this.pin_reason = '';
                    this.pin_priority = 0;
                    this.pin_expires_at = undefined;
                }
            },
            
            // 固定后处理
            async postPinProcessing(options = {}) {
                try {
                    // 更新线程固定消息列表
                    await this.thread.updatePinnedMessages();
                    
                    // 发送通知
                    if (options.notify !== false) {
                        this.sendPinNotification();
                    }
                    
                    // 更新统计
                    this.updatePinStatistics();
                    
                    // 触发事件
                    this.triggerPinEvent('pinned');
                    
                } catch (error) {
                    console.warn('固定后处理失败:', error);
                }
            },
            
            // 取消固定后处理
            async postUnpinProcessing(options = {}) {
                try {
                    // 更新线程固定消息列表
                    await this.thread.updatePinnedMessages();
                    
                    // 发送通知
                    if (options.notify !== false) {
                        this.sendUnpinNotification();
                    }
                    
                    // 更新统计
                    this.updateUnpinStatistics();
                    
                    // 触发事件
                    this.triggerPinEvent('unpinned');
                    
                } catch (error) {
                    console.warn('取消固定后处理失败:', error);
                }
            },
            
            // 记录固定操作
            recordPinAction(action, status, error = null) {
                try {
                    const record = {
                        messageId: this.id,
                        threadId: this.thread?.id,
                        action: action,
                        status: status,
                        timestamp: Date.now(),
                        error: error ? error.message : null
                    };
                    
                    this.pinState.pinHistory.push(record);
                    this.pinState.lastPinAction = record;
                    
                    // 限制历史记录大小
                    if (this.pinState.pinHistory.length > 10) {
                        this.pinState.pinHistory.splice(0, 5);
                    }
                    
                } catch (recordError) {
                    console.warn('记录固定操作失败:', recordError);
                }
            },
            
            // 发送固定通知
            sendPinNotification() {
                if (this.store.env.services.notification) {
                    this.store.env.services.notification.add(
                        _t('Message pinned successfully'),
                        { type: 'success' }
                    );
                }
            },
            
            // 发送取消固定通知
            sendUnpinNotification() {
                if (this.store.env.services.notification) {
                    this.store.env.services.notification.add(
                        _t('Message unpinned successfully'),
                        { type: 'success' }
                    );
                }
            },
            
            // 触发固定事件
            triggerPinEvent(eventType) {
                try {
                    this.store.env.bus.trigger(`message:${eventType}`, {
                        message: this,
                        thread: this.thread,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发固定事件失败:', error);
                }
            },
            
            // 获取固定信息
            getPinInfo() {
                if (!this.pinned_at) {
                    return null;
                }
                
                return {
                    pinnedAt: this.pinned_at,
                    pinnedBy: this.pinned_by,
                    reason: this.pin_reason,
                    priority: this.pin_priority,
                    expiresAt: this.pin_expires_at,
                    duration: Date.now() - new Date(this.pinned_at).getTime()
                };
            },
            
            // 检查固定是否过期
            isPinExpired() {
                if (!this.pinned_at || !this.pin_expires_at) {
                    return false;
                }
                
                return Date.now() > new Date(this.pin_expires_at).getTime();
            },
            
            // 获取消息固定统计
            getMessagePinStatistics() {
                return {
                    isPinned: !!this.pinned_at,
                    pinInfo: this.getPinInfo(),
                    pinState: { ...this.pinState },
                    canPin: this.canPin(),
                    canUnpin: this.canUnpin(),
                    isExpired: this.isPinExpired()
                };
            }
        };
        
        patch(Message.prototype, EnhancedMessagePatch);
    }
};

// 应用消息模型补丁增强
MessageModelPatchEnhancer.enhanceMessageModelPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 异步处理
- Deferred对象管理异步操作
- Promise链式处理
- 错误处理机制

### 3. 用户交互
- 确认对话框集成
- 友好的用户提示
- 国际化支持

### 4. 数据持久化
- 后端API集成
- 状态同步机制
- 数据一致性保证

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 命令模式 (Command Pattern)
- 固定操作的命令化封装
- 可撤销的操作设计

### 3. 观察者模式 (Observer Pattern)
- 状态变化的通知机制
- 事件驱动的更新

## 注意事项

1. **数据一致性**: 确保前后端数据状态的一致性
2. **用户体验**: 提供清晰的操作反馈和确认
3. **权限控制**: 严格的权限检查和验证
4. **错误处理**: 完善的错误处理和恢复机制

## 扩展建议

1. **批量操作**: 支持批量固定/取消固定消息
2. **定时固定**: 实现定时固定和自动过期
3. **固定模板**: 提供固定原因的模板选择
4. **操作历史**: 记录详细的固定操作历史
5. **权限细化**: 实现更细粒度的权限控制

该补丁为消息模型提供了完整的固定功能支持，是消息固定功能的核心数据层组件。

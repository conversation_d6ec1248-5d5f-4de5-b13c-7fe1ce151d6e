# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了对 Odoo 讨论应用线程模型的补丁扩展，专门添加了固定消息管理功能。该补丁通过Odoo的补丁机制扩展了Thread模型，添加了pinnedMessagesState状态、pinnedMessages集合和fetchPinnedMessages方法，集成了状态管理、数据获取、排序逻辑和错误处理等特性，为线程提供了完整的固定消息数据管理支持，是消息固定功能的核心数据模型组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/thread_model_patch.js`
- **行数**: 58
- **模块**: `@mail/discuss/message_pin/common/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'           // 补丁工具
'@mail/core/common/record'        // 记录基类
'@mail/core/common/thread_model'  // 线程模型
'@web/core/network/rpc'           // RPC网络服务
```

## 补丁定义

### Thread 补丁

```javascript
patch(Thread.prototype, {
    setup() {
        super.setup();
        /** @type {'loaded'|'loading'|'error'|undefined} */
        this.pinnedMessagesState = undefined;
        this.pinnedMessages = Record.many("Message", {
            compute() {
                return this.allMessages.filter((m) => m.pinned_at);
            },
            sort: (m1, m2) => {
                if (m1.pinned_at === m2.pinned_at) {
                    return m1.id - m2.id;
                }
                return m1.pinned_at < m2.pinned_at ? 1 : -1;
            },
        });
    },
    async fetchPinnedMessages() {
        // 获取固定消息逻辑
    },
});
```

**补丁特性**:
- 扩展线程模型功能
- 添加固定消息状态管理
- 实现固定消息集合
- 提供数据获取方法

## 核心功能

### 1. 模型扩展

```javascript
setup() {
    super.setup();
    /** @type {'loaded'|'loading'|'error'|undefined} */
    this.pinnedMessagesState = undefined;
    this.pinnedMessages = Record.many("Message", {
        compute() {
            return this.allMessages.filter((m) => m.pinned_at);
        },
        sort: (m1, m2) => {
            if (m1.pinned_at === m2.pinned_at) {
                return m1.id - m2.id;
            }
            return m1.pinned_at < m2.pinned_at ? 1 : -1;
        },
    });
}
```

**模型扩展功能**:
- **父类调用**: 保持原有的初始化逻辑
- **状态管理**: 添加固定消息加载状态
- **消息集合**: 创建固定消息的响应式集合
- **自动计算**: 从所有消息中过滤固定消息
- **排序逻辑**: 按固定时间倒序排列，相同时间按ID排序

### 2. 固定消息获取

```javascript
async fetchPinnedMessages() {
    if (
        this.model !== "discuss.channel" ||
        ["loaded", "loading"].includes(this.pinnedMessagesState)
    ) {
        return;
    }
    this.pinnedMessagesState = "loading";
    try {
        const data = await rpc("/discuss/channel/pinned_messages", {
            channel_id: this.id,
        });
        this.store.insert(data, { html: true });
        this.pinnedMessagesState = "loaded";
    } catch (e) {
        this.pinnedMessagesState = "error";
        throw e;
    }
}
```

**获取功能**:
- **条件检查**: 仅对讨论频道执行，避免重复加载
- **状态管理**: 设置加载状态防止重复请求
- **RPC调用**: 调用后端API获取固定消息数据
- **数据插入**: 将获取的数据插入到存储中
- **HTML处理**: 支持HTML内容的处理
- **错误处理**: 捕获异常并设置错误状态

## 使用场景

### 1. 线程模型增强

```javascript
// 线程模型增强功能
const ThreadModelPatchEnhancer = {
    enhanceThreadModelPatch: () => {
        const EnhancedThreadPatch = {
            setup() {
                super.setup();
                
                // 原有的固定消息状态和集合
                this.pinnedMessagesState = undefined;
                this.pinnedMessages = Record.many("Message", {
                    compute() {
                        return this.allMessages.filter((m) => m.pinned_at);
                    },
                    sort: (m1, m2) => {
                        if (m1.pinned_at === m2.pinned_at) {
                            return m1.id - m2.id;
                        }
                        return m1.pinned_at < m2.pinned_at ? 1 : -1;
                    },
                });
                
                // 增强的固定消息相关属性
                this.pinnedMessagesCache = new Map();
                this.pinnedMessagesLastFetch = null;
                this.pinnedMessagesAutoRefresh = true;
                this.pinnedMessagesRefreshInterval = 30000; // 30秒
                this.pinnedMessagesMaxCount = 50;
                this.pinnedMessagesStats = {
                    totalFetches: 0,
                    successfulFetches: 0,
                    failedFetches: 0,
                    lastFetchTime: null,
                    lastFetchDuration: 0
                };
                
                // 设置增强功能
                this.setupPinnedMessagesEnhancements();
            },
            
            // 设置固定消息增强功能
            setupPinnedMessagesEnhancements() {
                // 设置自动刷新
                this.setupPinnedMessagesAutoRefresh();
                
                // 设置缓存管理
                this.setupPinnedMessagesCache();
                
                // 设置性能监控
                this.setupPinnedMessagesPerformanceMonitoring();
                
                // 设置事件监听
                this.setupPinnedMessagesEventListeners();
            },
            
            // 增强的固定消息获取
            async fetchPinnedMessages(options = {}) {
                try {
                    // 记录获取开始
                    const fetchStartTime = performance.now();
                    this.pinnedMessagesStats.totalFetches++;
                    
                    // 检查是否需要获取
                    if (!this.shouldFetchPinnedMessages(options)) {
                        return;
                    }
                    
                    // 检查缓存
                    if (options.useCache !== false && this.hasCachedPinnedMessages()) {
                        return this.getCachedPinnedMessages();
                    }
                    
                    // 设置加载状态
                    this.pinnedMessagesState = "loading";
                    
                    // 执行获取
                    const data = await this.executePinnedMessagesFetch(options);
                    
                    // 处理获取结果
                    await this.processPinnedMessagesData(data, options);
                    
                    // 更新状态和统计
                    this.pinnedMessagesState = "loaded";
                    this.pinnedMessagesLastFetch = Date.now();
                    this.pinnedMessagesStats.successfulFetches++;
                    this.pinnedMessagesStats.lastFetchTime = Date.now();
                    this.pinnedMessagesStats.lastFetchDuration = performance.now() - fetchStartTime;
                    
                    // 触发获取完成事件
                    this.triggerPinnedMessagesFetchedEvent(data);
                    
                } catch (error) {
                    console.error('获取固定消息失败:', error);
                    this.pinnedMessagesState = "error";
                    this.pinnedMessagesStats.failedFetches++;
                    this.handlePinnedMessagesFetchError(error);
                    throw error;
                }
            },
            
            // 检查是否应该获取固定消息
            shouldFetchPinnedMessages(options = {}) {
                // 检查线程模型
                if (this.model !== "discuss.channel") {
                    return false;
                }
                
                // 检查当前状态
                if (!options.force && ["loaded", "loading"].includes(this.pinnedMessagesState)) {
                    return false;
                }
                
                // 检查获取间隔
                if (!options.force && this.pinnedMessagesLastFetch) {
                    const timeSinceLastFetch = Date.now() - this.pinnedMessagesLastFetch;
                    const minInterval = options.minInterval || 5000; // 最小5秒间隔
                    
                    if (timeSinceLastFetch < minInterval) {
                        return false;
                    }
                }
                
                // 检查网络状态
                if (!navigator.onLine && !options.allowOffline) {
                    return false;
                }
                
                return true;
            },
            
            // 执行固定消息获取
            async executePinnedMessagesFetch(options = {}) {
                const params = {
                    channel_id: this.id,
                    limit: options.limit || this.pinnedMessagesMaxCount,
                    offset: options.offset || 0
                };
                
                // 添加额外参数
                if (options.since) {
                    params.since = options.since;
                }
                
                if (options.include_metadata) {
                    params.include_metadata = true;
                }
                
                // 执行RPC调用
                const data = await rpc("/discuss/channel/pinned_messages", params, {
                    silent: options.silent || false,
                    timeout: options.timeout || 10000
                });
                
                return data;
            },
            
            // 处理固定消息数据
            async processPinnedMessagesData(data, options = {}) {
                try {
                    // 验证数据
                    if (!this.validatePinnedMessagesData(data)) {
                        throw new Error('无效的固定消息数据');
                    }
                    
                    // 插入数据到存储
                    this.store.insert(data, { 
                        html: true,
                        validate: options.validate !== false
                    });
                    
                    // 更新缓存
                    if (options.updateCache !== false) {
                        this.updatePinnedMessagesCache(data);
                    }
                    
                    // 清理过期消息
                    this.cleanupExpiredPinnedMessages();
                    
                    // 更新统计
                    this.updatePinnedMessagesStatistics(data);
                    
                } catch (error) {
                    console.error('处理固定消息数据失败:', error);
                    throw error;
                }
            },
            
            // 验证固定消息数据
            validatePinnedMessagesData(data) {
                if (!data || typeof data !== 'object') {
                    return false;
                }
                
                // 检查消息数组
                if (data.messages && !Array.isArray(data.messages)) {
                    return false;
                }
                
                // 验证每个消息
                if (data.messages) {
                    for (const message of data.messages) {
                        if (!message.id || !message.pinned_at) {
                            return false;
                        }
                    }
                }
                
                return true;
            },
            
            // 检查是否有缓存的固定消息
            hasCachedPinnedMessages() {
                const cacheKey = `pinned_messages_${this.id}`;
                const cached = this.pinnedMessagesCache.get(cacheKey);
                
                if (!cached) {
                    return false;
                }
                
                // 检查缓存是否过期
                const cacheAge = Date.now() - cached.timestamp;
                const maxAge = 60000; // 1分钟
                
                return cacheAge < maxAge;
            },
            
            // 获取缓存的固定消息
            getCachedPinnedMessages() {
                const cacheKey = `pinned_messages_${this.id}`;
                const cached = this.pinnedMessagesCache.get(cacheKey);
                
                if (cached) {
                    return cached.data;
                }
                
                return null;
            },
            
            // 更新固定消息缓存
            updatePinnedMessagesCache(data) {
                try {
                    const cacheKey = `pinned_messages_${this.id}`;
                    
                    this.pinnedMessagesCache.set(cacheKey, {
                        data: data,
                        timestamp: Date.now()
                    });
                    
                    // 限制缓存大小
                    if (this.pinnedMessagesCache.size > 10) {
                        const oldestKey = this.pinnedMessagesCache.keys().next().value;
                        this.pinnedMessagesCache.delete(oldestKey);
                    }
                    
                } catch (error) {
                    console.warn('更新固定消息缓存失败:', error);
                }
            },
            
            // 清理过期的固定消息
            cleanupExpiredPinnedMessages() {
                try {
                    const now = Date.now();
                    const expiredMessages = this.pinnedMessages.filter(message => {
                        return message.pin_expires_at && 
                               new Date(message.pin_expires_at).getTime() < now;
                    });
                    
                    // 自动取消固定过期消息
                    expiredMessages.forEach(message => {
                        message.unpin({ skipConfirm: true, reason: 'expired' });
                    });
                    
                } catch (error) {
                    console.warn('清理过期固定消息失败:', error);
                }
            },
            
            // 设置固定消息自动刷新
            setupPinnedMessagesAutoRefresh() {
                if (this.pinnedMessagesAutoRefresh) {
                    this.pinnedMessagesRefreshTimer = setInterval(() => {
                        if (document.visibilityState === 'visible' && this.pinnedMessagesState === 'loaded') {
                            this.fetchPinnedMessages({ silent: true, useCache: false });
                        }
                    }, this.pinnedMessagesRefreshInterval);
                }
                
                // 页面可见性变化时刷新
                this.visibilityChangeHandler = () => {
                    if (document.visibilityState === 'visible' && this.pinnedMessagesState === 'loaded') {
                        this.fetchPinnedMessages({ silent: true });
                    }
                };
                
                document.addEventListener('visibilitychange', this.visibilityChangeHandler);
                
                // 清理定时器
                onWillUnmount(() => {
                    if (this.pinnedMessagesRefreshTimer) {
                        clearInterval(this.pinnedMessagesRefreshTimer);
                    }
                    
                    if (this.visibilityChangeHandler) {
                        document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
                    }
                });
            },
            
            // 处理固定消息获取错误
            handlePinnedMessagesFetchError(error) {
                // 记录错误
                console.error('固定消息获取错误:', error);
                
                // 根据错误类型处理
                if (error.message.includes('network')) {
                    // 网络错误，稍后重试
                    setTimeout(() => {
                        this.fetchPinnedMessages({ silent: true });
                    }, 5000);
                } else if (error.message.includes('permission')) {
                    // 权限错误，停止自动刷新
                    this.pinnedMessagesAutoRefresh = false;
                }
                
                // 触发错误事件
                this.triggerPinnedMessagesErrorEvent(error);
            },
            
            // 触发固定消息获取完成事件
            triggerPinnedMessagesFetchedEvent(data) {
                try {
                    this.env.bus.trigger('thread:pinned_messages_fetched', {
                        thread: this,
                        data: data,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发固定消息获取事件失败:', error);
                }
            },
            
            // 触发固定消息错误事件
            triggerPinnedMessagesErrorEvent(error) {
                try {
                    this.env.bus.trigger('thread:pinned_messages_error', {
                        thread: this,
                        error: error,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发固定消息错误事件失败:', error);
                }
            },
            
            // 获取固定消息数量
            getPinnedMessagesCount() {
                return this.pinnedMessages.length;
            },
            
            // 检查是否可以固定更多消息
            canPinMoreMessages() {
                return this.getPinnedMessagesCount() < this.pinnedMessagesMaxCount;
            },
            
            // 获取最新固定的消息
            getLatestPinnedMessage() {
                if (this.pinnedMessages.length === 0) {
                    return null;
                }
                
                return this.pinnedMessages[0]; // 已按固定时间倒序排列
            },
            
            // 获取最旧固定的消息
            getOldestPinnedMessage() {
                if (this.pinnedMessages.length === 0) {
                    return null;
                }
                
                return this.pinnedMessages[this.pinnedMessages.length - 1];
            },
            
            // 按作者获取固定消息
            getPinnedMessagesByAuthor(authorId) {
                return this.pinnedMessages.filter(message => 
                    message.author?.id === authorId
                );
            },
            
            // 按优先级获取固定消息
            getPinnedMessagesByPriority(priority) {
                return this.pinnedMessages.filter(message => 
                    message.pin_priority === priority
                );
            },
            
            // 获取即将过期的固定消息
            getExpiringPinnedMessages(hoursThreshold = 24) {
                const threshold = Date.now() + hoursThreshold * 60 * 60 * 1000;
                
                return this.pinnedMessages.filter(message => 
                    message.pin_expires_at && 
                    new Date(message.pin_expires_at).getTime() < threshold
                );
            },
            
            // 更新固定消息列表
            async updatePinnedMessages() {
                await this.fetchPinnedMessages({ force: true });
            },
            
            // 获取线程固定消息统计
            getThreadPinnedMessagesStatistics() {
                return {
                    totalPinnedMessages: this.getPinnedMessagesCount(),
                    maxPinnedMessages: this.pinnedMessagesMaxCount,
                    canPinMore: this.canPinMoreMessages(),
                    state: this.pinnedMessagesState,
                    lastFetch: this.pinnedMessagesLastFetch,
                    autoRefresh: this.pinnedMessagesAutoRefresh,
                    stats: { ...this.pinnedMessagesStats },
                    cacheSize: this.pinnedMessagesCache.size
                };
            }
        };
        
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程模型补丁增强
ThreadModelPatchEnhancer.enhanceThreadModelPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 响应式数据
- 自动计算的消息集合
- 实时的数据过滤
- 智能的排序逻辑

### 3. 状态管理
- 完整的加载状态跟踪
- 错误状态处理
- 防重复请求机制

### 4. 网络优化
- RPC调用封装
- 错误处理机制
- 数据验证功能

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 响应式数据集合
- 自动数据更新

### 3. 状态模式 (State Pattern)
- 加载状态的管理
- 状态转换控制

## 注意事项

1. **性能优化**: 避免频繁的数据获取影响性能
2. **状态一致性**: 确保加载状态的正确管理
3. **错误处理**: 提供完善的错误处理机制
4. **内存管理**: 及时清理缓存和定时器

## 扩展建议

1. **缓存机制**: 实现更智能的数据缓存策略
2. **增量更新**: 支持固定消息的增量更新
3. **离线支持**: 实现离线模式下的数据管理
4. **性能监控**: 添加详细的性能监控功能
5. **批量操作**: 支持批量固定消息管理

该补丁为线程模型提供了完整的固定消息数据管理功能，是消息固定功能的核心数据层组件。

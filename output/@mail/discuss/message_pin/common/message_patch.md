# Message Patch - 消息补丁

## 概述

`message_patch.js` 实现了对 Odoo 讨论应用消息组件的补丁扩展，专门添加了消息固定功能的界面显示逻辑。该补丁通过Odoo的补丁机制扩展了Message组件，重写了对齐方式、作者名称显示和通知消息点击处理等方法，添加了对消息卡片环境和固定菜单的支持，为消息固定功能提供了完整的用户界面支持，是消息固定功能的核心界面组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/message_patch.js`
- **行数**: 40
- **模块**: `@mail/discuss/message_pin/common/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'  // 消息组件
'@web/core/utils/patch'      // 补丁工具
```

## 补丁定义

### Message 组件补丁

```javascript
patch(Message, {
    components: { ...Message.components },
});
```

**组件补丁特性**:
- 保持原有组件结构
- 扩展组件功能
- 支持组件继承

### Message 原型补丁

```javascript
patch(Message.prototype, {
    get isAlignedRight() {
        return !this.env.messageCard && super.isAlignedRight;
    },
    get shouldDisplayAuthorName() {
        if (this.env.messageCard) {
            return true;
        }
        return super.shouldDisplayAuthorName;
    },
    async onClickNotificationMessage(ev) {
        const { oeType } = ev.target.dataset;
        if (oeType === "pin-menu") {
            this.env.pinMenu?.open();
        }
        await super.onClickNotificationMessage(...arguments);
    },
});
```

**原型补丁特性**:
- 重写对齐方式逻辑
- 修改作者名称显示
- 添加固定菜单处理
- 保持原有功能完整性

## 核心功能

### 1. 消息对齐控制

```javascript
get isAlignedRight() {
    return !this.env.messageCard && super.isAlignedRight;
}
```

**对齐控制功能**:
- **环境检查**: 检查是否在消息卡片环境中
- **条件对齐**: 在消息卡片中不使用右对齐
- **父类逻辑**: 保持原有的对齐逻辑
- **界面适配**: 适配不同的显示环境

### 2. 作者名称显示

```javascript
get shouldDisplayAuthorName() {
    if (this.env.messageCard) {
        return true;
    }
    return super.shouldDisplayAuthorName;
}
```

**作者名称显示功能**:
- **强制显示**: 在消息卡片中强制显示作者名称
- **环境感知**: 根据环境决定显示策略
- **用户体验**: 提供更好的消息识别
- **一致性**: 保持界面显示的一致性

### 3. 通知消息点击处理

```javascript
async onClickNotificationMessage(ev) {
    const { oeType } = ev.target.dataset;
    if (oeType === "pin-menu") {
        this.env.pinMenu?.open();
    }
    await super.onClickNotificationMessage(...arguments);
}
```

**点击处理功能**:
- **事件检查**: 检查点击目标的数据类型
- **固定菜单**: 处理固定菜单的打开操作
- **安全调用**: 使用可选链操作符安全调用
- **父类调用**: 保持原有的点击处理逻辑

## 使用场景

### 1. 消息界面增强

```javascript
// 消息界面增强功能
const MessagePatchEnhancer = {
    enhanceMessagePatch: () => {
        const EnhancedMessagePatch = {
            // 增强的对齐控制
            get isAlignedRight() {
                const baseAlignment = !this.env.messageCard && super.isAlignedRight;
                
                // 在固定消息面板中的特殊处理
                if (this.env.pinnedMessagesPanel) {
                    return false; // 固定消息面板中不使用右对齐
                }
                
                // 在消息预览中的特殊处理
                if (this.env.messagePreview) {
                    return false; // 预览模式中不使用右对齐
                }
                
                // 检查用户偏好
                const userPreference = this.getUserAlignmentPreference();
                if (userPreference !== null) {
                    return userPreference;
                }
                
                return baseAlignment;
            },
            
            // 增强的作者名称显示
            get shouldDisplayAuthorName() {
                // 在消息卡片中强制显示
                if (this.env.messageCard) {
                    return true;
                }
                
                // 在固定消息面板中显示
                if (this.env.pinnedMessagesPanel) {
                    return true;
                }
                
                // 在搜索结果中显示
                if (this.env.searchResults) {
                    return true;
                }
                
                // 对于固定消息，总是显示作者
                if (this.props.message.pinned_at) {
                    return true;
                }
                
                // 检查用户偏好
                const showAuthor = this.getUserAuthorDisplayPreference();
                if (showAuthor !== null) {
                    return showAuthor;
                }
                
                return super.shouldDisplayAuthorName;
            },
            
            // 增强的通知消息点击处理
            async onClickNotificationMessage(ev) {
                try {
                    const { oeType } = ev.target.dataset;
                    
                    // 处理固定菜单
                    if (oeType === "pin-menu") {
                        await this.handlePinMenuClick(ev);
                        return;
                    }
                    
                    // 处理固定消息点击
                    if (oeType === "pinned-message") {
                        await this.handlePinnedMessageClick(ev);
                        return;
                    }
                    
                    // 处理固定状态点击
                    if (oeType === "pin-status") {
                        await this.handlePinStatusClick(ev);
                        return;
                    }
                    
                    // 处理固定信息点击
                    if (oeType === "pin-info") {
                        await this.handlePinInfoClick(ev);
                        return;
                    }
                    
                    // 调用父类处理
                    await super.onClickNotificationMessage(...arguments);
                    
                } catch (error) {
                    console.error('处理通知消息点击失败:', error);
                    this.handleClickError(error);
                }
            },
            
            // 处理固定菜单点击
            async handlePinMenuClick(ev) {
                try {
                    // 记录点击事件
                    this.recordPinMenuClick();
                    
                    // 检查权限
                    if (!this.canAccessPinMenu()) {
                        this.showPermissionDeniedMessage();
                        return;
                    }
                    
                    // 打开固定菜单
                    if (this.env.pinMenu) {
                        await this.env.pinMenu.open({
                            anchor: ev.target,
                            message: this.props.message,
                            thread: this.props.thread
                        });
                    } else {
                        // 降级处理：显示固定操作对话框
                        this.showPinActionDialog();
                    }
                    
                } catch (error) {
                    console.error('处理固定菜单点击失败:', error);
                    this.showErrorMessage('无法打开固定菜单');
                }
            },
            
            // 处理固定消息点击
            async handlePinnedMessageClick(ev) {
                try {
                    const message = this.props.message;
                    
                    // 记录点击事件
                    this.recordPinnedMessageClick(message);
                    
                    // 检查是否需要跳转到原消息
                    if (this.shouldNavigateToOriginal()) {
                        await this.navigateToOriginalMessage(message);
                    } else {
                        // 显示消息详情
                        this.showMessageDetails(message);
                    }
                    
                } catch (error) {
                    console.error('处理固定消息点击失败:', error);
                    this.showErrorMessage('无法处理固定消息点击');
                }
            },
            
            // 处理固定状态点击
            async handlePinStatusClick(ev) {
                try {
                    const message = this.props.message;
                    
                    // 显示固定状态信息
                    this.showPinStatusInfo(message);
                    
                } catch (error) {
                    console.error('处理固定状态点击失败:', error);
                }
            },
            
            // 处理固定信息点击
            async handlePinInfoClick(ev) {
                try {
                    const message = this.props.message;
                    
                    // 显示详细的固定信息
                    this.showDetailedPinInfo(message);
                    
                } catch (error) {
                    console.error('处理固定信息点击失败:', error);
                }
            },
            
            // 检查是否可以访问固定菜单
            canAccessPinMenu() {
                const user = this.store?.self;
                const thread = this.props.thread;
                
                if (!user || !thread) {
                    return false;
                }
                
                // 检查用户类型
                if (user.type !== 'partner') {
                    return false;
                }
                
                // 检查线程类型
                if (thread.model !== 'discuss.channel') {
                    return false;
                }
                
                // 检查是否为频道成员
                if (!thread.hasSelfAsMember) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否应该跳转到原消息
            shouldNavigateToOriginal() {
                // 在固定消息面板中点击时跳转
                if (this.env.pinnedMessagesPanel) {
                    return true;
                }
                
                // 在搜索结果中点击时跳转
                if (this.env.searchResults) {
                    return true;
                }
                
                return false;
            },
            
            // 跳转到原消息
            async navigateToOriginalMessage(message) {
                try {
                    const thread = message.thread;
                    
                    if (!thread) {
                        throw new Error('无法找到消息所属的线程');
                    }
                    
                    // 跳转到线程
                    await this.env.services.action.doAction({
                        type: 'ir.actions.client',
                        tag: 'mail.action_discuss',
                        params: {
                            default_active_id: thread.id,
                            default_active_model: thread.model
                        }
                    });
                    
                    // 滚动到消息
                    setTimeout(() => {
                        this.scrollToMessage(message);
                    }, 500);
                    
                } catch (error) {
                    console.error('跳转到原消息失败:', error);
                    this.showErrorMessage('无法跳转到原消息');
                }
            },
            
            // 滚动到消息
            scrollToMessage(message) {
                try {
                    const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
                    
                    if (messageElement) {
                        messageElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center'
                        });
                        
                        // 高亮显示消息
                        this.highlightMessage(messageElement);
                    }
                    
                } catch (error) {
                    console.warn('滚动到消息失败:', error);
                }
            },
            
            // 高亮显示消息
            highlightMessage(element) {
                try {
                    element.classList.add('o-mail-Message-highlighted');
                    
                    // 3秒后移除高亮
                    setTimeout(() => {
                        element.classList.remove('o-mail-Message-highlighted');
                    }, 3000);
                    
                } catch (error) {
                    console.warn('高亮消息失败:', error);
                }
            },
            
            // 显示消息详情
            showMessageDetails(message) {
                this.env.services.dialog.add(MessageDetailsDialog, {
                    message: message,
                    showPinInfo: true,
                    onPin: () => message.pin(),
                    onUnpin: () => message.unpin()
                });
            },
            
            // 显示固定状态信息
            showPinStatusInfo(message) {
                const pinInfo = message.getPinInfo();
                
                if (!pinInfo) {
                    return;
                }
                
                const statusText = this.formatPinStatusText(pinInfo);
                
                this.env.services.notification.add(statusText, {
                    type: 'info',
                    sticky: false
                });
            },
            
            // 格式化固定状态文本
            formatPinStatusText(pinInfo) {
                const pinnedDate = new Date(pinInfo.pinnedAt).toLocaleString();
                const pinnedBy = pinInfo.pinnedBy?.name || 'Unknown';
                
                let text = _t('Pinned on %s by %s', pinnedDate, pinnedBy);
                
                if (pinInfo.reason) {
                    text += _t('\nReason: %s', pinInfo.reason);
                }
                
                if (pinInfo.expiresAt) {
                    const expiresDate = new Date(pinInfo.expiresAt).toLocaleString();
                    text += _t('\nExpires: %s', expiresDate);
                }
                
                return text;
            },
            
            // 显示详细固定信息
            showDetailedPinInfo(message) {
                this.env.services.dialog.add(PinInfoDialog, {
                    message: message,
                    pinInfo: message.getPinInfo()
                });
            },
            
            // 显示固定操作对话框
            showPinActionDialog() {
                const message = this.props.message;
                
                this.env.services.dialog.add(PinActionDialog, {
                    message: message,
                    onPin: () => message.pin(),
                    onUnpin: () => message.unpin()
                });
            },
            
            // 获取用户对齐偏好
            getUserAlignmentPreference() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('message_display_preferences') || '{}'
                    );
                    
                    return prefs.alignRight !== undefined ? prefs.alignRight : null;
                } catch {
                    return null;
                }
            },
            
            // 获取用户作者显示偏好
            getUserAuthorDisplayPreference() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('message_display_preferences') || '{}'
                    );
                    
                    return prefs.showAuthor !== undefined ? prefs.showAuthor : null;
                } catch {
                    return null;
                }
            },
            
            // 记录固定菜单点击
            recordPinMenuClick() {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('pin_menu_clicks') || '[]'
                    );
                    
                    clicks.push({
                        messageId: this.props.message.id,
                        threadId: this.props.thread?.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个点击
                    if (clicks.length > 50) {
                        clicks.splice(0, clicks.length - 50);
                    }
                    
                    localStorage.setItem('pin_menu_clicks', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录固定菜单点击失败:', error);
                }
            },
            
            // 记录固定消息点击
            recordPinnedMessageClick(message) {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('pinned_message_clicks') || '[]'
                    );
                    
                    clicks.push({
                        messageId: message.id,
                        threadId: message.thread?.id,
                        environment: this.getCurrentEnvironment(),
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个点击
                    if (clicks.length > 100) {
                        clicks.splice(0, clicks.length - 100);
                    }
                    
                    localStorage.setItem('pinned_message_clicks', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录固定消息点击失败:', error);
                }
            },
            
            // 获取当前环境
            getCurrentEnvironment() {
                if (this.env.messageCard) return 'messageCard';
                if (this.env.pinnedMessagesPanel) return 'pinnedMessagesPanel';
                if (this.env.searchResults) return 'searchResults';
                if (this.env.messagePreview) return 'messagePreview';
                return 'default';
            },
            
            // 显示权限拒绝消息
            showPermissionDeniedMessage() {
                this.env.services.notification.add(
                    '您没有权限访问固定菜单',
                    { type: 'warning' }
                );
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 处理点击错误
            handleClickError(error) {
                console.error('消息点击处理错误:', error);
                this.showErrorMessage('操作失败，请重试');
            },
            
            // 获取消息界面统计
            getMessagePatchStatistics() {
                try {
                    return {
                        pinMenuClicks: JSON.parse(localStorage.getItem('pin_menu_clicks') || '[]').length,
                        pinnedMessageClicks: JSON.parse(localStorage.getItem('pinned_message_clicks') || '[]').length,
                        currentEnvironment: this.getCurrentEnvironment(),
                        displayPreferences: JSON.parse(localStorage.getItem('message_display_preferences') || '{}')
                    };
                } catch (error) {
                    return {
                        pinMenuClicks: 0,
                        pinnedMessageClicks: 0,
                        currentEnvironment: 'unknown',
                        displayPreferences: {}
                    };
                }
            }
        };
        
        patch(Message.prototype, EnhancedMessagePatch);
    }
};

// 应用消息补丁增强
MessagePatchEnhancer.enhanceMessagePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 环境感知
- 消息卡片环境检测
- 不同环境的显示策略
- 上下文相关的行为

### 3. 事件处理
- 智能的点击事件处理
- 数据类型识别
- 安全的方法调用

### 4. 用户体验
- 一致的界面显示
- 直观的交互反馈
- 个性化的显示选项

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同环境的显示策略
- 可配置的行为模式

### 3. 责任链模式 (Chain of Responsibility Pattern)
- 事件处理的责任链
- 层次化的处理逻辑

## 注意事项

1. **环境检测**: 正确检测和处理不同的显示环境
2. **事件处理**: 确保事件处理的正确性和安全性
3. **用户体验**: 提供一致和直观的用户界面
4. **性能优化**: 避免频繁的DOM操作影响性能

## 扩展建议

1. **更多环境**: 支持更多类型的显示环境
2. **自定义样式**: 允许用户自定义消息显示样式
3. **快捷操作**: 添加更多快捷操作支持
4. **动画效果**: 实现更丰富的动画效果
5. **无障碍支持**: 增强无障碍访问支持

该补丁为消息组件提供了重要的固定功能界面支持，确保固定消息在不同环境下的正确显示和交互。

# Message Actions - 消息操作

## 概述

`message_actions.js` 实现了 Odoo 讨论应用中消息固定功能的操作注册，专门添加了消息的固定/取消固定操作。该文件通过消息操作注册表添加了"pin"操作，集成了条件检查、图标显示、标题动态化和点击处理等特性，为用户在讨论频道中提供了便捷的消息固定管理功能，是消息固定功能的核心操作组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/message_pin/common/message_actions.js`
- **行数**: 22
- **模块**: `@mail/discuss/message_pin/common/message_actions`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'        // 国际化
'@mail/core/common/message_actions'  // 消息操作注册表
```

## 操作注册

### pin 操作

```javascript
messageActionsRegistry.add("pin", {
    condition: (component) =>
        component.store.self.type === "partner" &&
        component.props.thread?.model === "discuss.channel",
    icon: "fa-thumb-tack",
    title: (component) => (component.props.message.pinned_at ? _t("Unpin") : _t("Pin")),
    onClick: (component) => component.props.message.pin(),
    sequence: 65,
});
```

**操作特性**:
- 条件显示控制
- 动态标题显示
- 图标标识
- 点击处理逻辑

## 核心功能

### 1. 显示条件

```javascript
condition: (component) =>
    component.store.self.type === "partner" &&
    component.props.thread?.model === "discuss.channel"
```

**条件检查功能**:
- **用户类型**: 仅对合作伙伴类型用户显示
- **线程模型**: 仅在讨论频道中显示
- **权限控制**: 确保用户有固定消息的权限
- **环境限制**: 限制在特定环境下使用

### 2. 动态标题

```javascript
title: (component) => (component.props.message.pinned_at ? _t("Unpin") : _t("Pin"))
```

**标题功能**:
- **状态检查**: 根据消息是否已固定显示不同标题
- **国际化**: 使用翻译函数支持多语言
- **动态更新**: 根据消息状态动态更新标题
- **用户友好**: 提供清晰的操作提示

### 3. 操作执行

```javascript
onClick: (component) => component.props.message.pin()
```

**点击处理功能**:
- **消息方法**: 调用消息对象的pin方法
- **状态切换**: 自动处理固定/取消固定的切换
- **异步处理**: 支持异步操作处理
- **错误处理**: 内置错误处理机制

### 4. 视觉标识

```javascript
icon: "fa-thumb-tack"
```

**图标功能**:
- **直观标识**: 使用图钉图标直观表示固定功能
- **一致性**: 与常见的固定操作图标保持一致
- **可识别性**: 用户容易识别和理解

## 使用场景

### 1. 消息固定操作增强

```javascript
// 消息固定操作增强功能
const MessagePinActionsEnhancer = {
    enhanceMessagePinActions: () => {
        // 增强原有的pin操作
        const originalPinAction = messageActionsRegistry.get("pin");
        
        messageActionsRegistry.add("pin", {
            ...originalPinAction,
            
            // 增强的条件检查
            condition: (component) => {
                const baseCondition = originalPinAction.condition(component);
                
                if (!baseCondition) {
                    return false;
                }
                
                // 增强的条件检查
                return this.enhancedConditionCheck(component);
            },
            
            // 增强的条件检查
            enhancedConditionCheck(component) {
                // 检查消息是否可固定
                if (!this.isMessagePinnable(component.props.message)) {
                    return false;
                }
                
                // 检查用户权限
                if (!this.hasUserPinPermission(component)) {
                    return false;
                }
                
                // 检查频道设置
                if (!this.isChannelPinEnabled(component.props.thread)) {
                    return false;
                }
                
                // 检查固定数量限制
                if (!this.canPinMoreMessages(component.props.thread)) {
                    return false;
                }
                
                return true;
            },
            
            // 增强的标题显示
            title: (component) => {
                const message = component.props.message;
                const isPinned = !!message.pinned_at;
                
                if (isPinned) {
                    // 显示取消固定信息
                    const pinnedDate = new Date(message.pinned_at).toLocaleDateString();
                    return _t("Unpin (pinned on %s)", pinnedDate);
                } else {
                    // 检查是否达到固定限制
                    const pinnedCount = this.getPinnedMessageCount(component.props.thread);
                    const maxPinned = this.getMaxPinnedMessages(component.props.thread);
                    
                    if (pinnedCount >= maxPinned) {
                        return _t("Pin (limit reached: %s/%s)", pinnedCount, maxPinned);
                    }
                    
                    return _t("Pin message");
                }
            },
            
            // 增强的点击处理
            async onClick(component) {
                try {
                    const message = component.props.message;
                    const thread = component.props.thread;
                    
                    // 记录操作开始
                    this.recordPinActionStart(message, thread);
                    
                    // 预检查
                    if (!this.preClickCheck(component)) {
                        return;
                    }
                    
                    // 显示确认对话框（如果需要）
                    if (this.needsConfirmation(message)) {
                        const confirmed = await this.showPinConfirmation(message);
                        if (!confirmed) {
                            return;
                        }
                    }
                    
                    // 显示加载状态
                    this.showPinLoading(message);
                    
                    // 执行固定操作
                    await message.pin();
                    
                    // 后处理
                    await this.postPinProcessing(message, thread);
                    
                    // 记录操作成功
                    this.recordPinActionSuccess(message, thread);
                    
                } catch (error) {
                    console.error('消息固定操作失败:', error);
                    this.handlePinError(error, component);
                } finally {
                    this.hidePinLoading(component.props.message);
                }
            },
            
            // 检查消息是否可固定
            isMessagePinnable(message) {
                // 检查消息是否存在
                if (!message || message.isEmpty) {
                    return false;
                }
                
                // 检查消息是否已删除
                if (message.isDeleted) {
                    return false;
                }
                
                // 检查消息类型
                if (message.message_type === 'notification') {
                    return false;
                }
                
                // 检查消息内容
                if (!message.body && !message.attachment_ids?.length) {
                    return false;
                }
                
                return true;
            },
            
            // 检查用户固定权限
            hasUserPinPermission(component) {
                const user = component.store.self;
                const thread = component.props.thread;
                
                // 检查是否为频道成员
                if (!thread.hasSelfAsMember) {
                    return false;
                }
                
                // 检查用户角色
                const selfMember = thread.selfMember;
                if (!selfMember) {
                    return false;
                }
                
                // 管理员和版主可以固定
                if (selfMember.role === 'admin' || selfMember.role === 'moderator') {
                    return true;
                }
                
                // 检查频道设置是否允许普通成员固定
                return thread.allowMemberPin || false;
            },
            
            // 检查频道是否启用固定功能
            isChannelPinEnabled(thread) {
                if (!thread) {
                    return false;
                }
                
                // 检查频道类型
                if (thread.channel_type !== 'channel') {
                    return false;
                }
                
                // 检查频道设置
                return thread.pinEnabled !== false;
            },
            
            // 检查是否可以固定更多消息
            canPinMoreMessages(thread) {
                const pinnedCount = this.getPinnedMessageCount(thread);
                const maxPinned = this.getMaxPinnedMessages(thread);
                
                return pinnedCount < maxPinned;
            },
            
            // 获取已固定消息数量
            getPinnedMessageCount(thread) {
                if (!thread || !thread.pinnedMessages) {
                    return 0;
                }
                
                return thread.pinnedMessages.length;
            },
            
            // 获取最大固定消息数量
            getMaxPinnedMessages(thread) {
                // 默认最大固定数量
                const defaultMax = 10;
                
                if (!thread) {
                    return defaultMax;
                }
                
                // 从频道设置获取
                return thread.maxPinnedMessages || defaultMax;
            },
            
            // 预点击检查
            preClickCheck(component) {
                const message = component.props.message;
                const thread = component.props.thread;
                
                // 检查消息是否仍然存在
                if (!message || message.isDeleted) {
                    this.showErrorMessage('消息不存在或已被删除');
                    return false;
                }
                
                // 检查网络状态
                if (!navigator.onLine) {
                    this.showErrorMessage('网络连接不可用');
                    return false;
                }
                
                // 检查是否正在处理中
                if (message.isPinning) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否需要确认
            needsConfirmation(message) {
                // 取消固定时需要确认
                if (message.pinned_at) {
                    return true;
                }
                
                // 重要消息固定时需要确认
                if (message.isImportant) {
                    return true;
                }
                
                return false;
            },
            
            // 显示固定确认对话框
            async showPinConfirmation(message) {
                const isPinned = !!message.pinned_at;
                
                return new Promise((resolve) => {
                    const dialog = this.env.services.dialog;
                    
                    dialog.add(ConfirmDialog, {
                        title: isPinned ? _t('Unpin Message') : _t('Pin Message'),
                        body: isPinned 
                            ? _t('Are you sure you want to unpin this message?')
                            : _t('Are you sure you want to pin this message?'),
                        confirmText: isPinned ? _t('Unpin') : _t('Pin'),
                        cancelText: _t('Cancel'),
                        onConfirm: () => resolve(true),
                        onCancel: () => resolve(false)
                    });
                });
            },
            
            // 显示固定加载状态
            showPinLoading(message) {
                message.isPinning = true;
                
                // 显示加载提示
                if (this.env.services.notification) {
                    this.pinLoadingNotification = this.env.services.notification.add(
                        message.pinned_at ? _t('Unpinning message...') : _t('Pinning message...'),
                        { type: 'info', sticky: true }
                    );
                }
            },
            
            // 隐藏固定加载状态
            hidePinLoading(message) {
                message.isPinning = false;
                
                if (this.pinLoadingNotification) {
                    this.pinLoadingNotification.close();
                    this.pinLoadingNotification = null;
                }
            },
            
            // 后处理
            async postPinProcessing(message, thread) {
                try {
                    // 更新固定消息列表
                    await this.updatePinnedMessagesList(thread);
                    
                    // 发送通知
                    this.sendPinNotification(message);
                    
                    // 更新统计
                    this.updatePinStatistics(message, thread);
                    
                    // 滚动到固定消息（如果需要）
                    if (message.pinned_at) {
                        this.scrollToPinnedMessage(message);
                    }
                    
                } catch (error) {
                    console.warn('固定后处理失败:', error);
                }
            },
            
            // 处理固定错误
            handlePinError(error, component) {
                let errorMessage = '操作失败';
                
                if (error.message.includes('permission')) {
                    errorMessage = '没有权限执行此操作';
                } else if (error.message.includes('limit')) {
                    errorMessage = '已达到固定消息数量限制';
                } else if (error.message.includes('network')) {
                    errorMessage = '网络错误，请重试';
                }
                
                this.showErrorMessage(errorMessage);
                this.recordPinActionError(component.props.message, component.props.thread, error);
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                if (this.env.services.notification) {
                    this.env.services.notification.add(message, { type: 'error' });
                }
            },
            
            // 发送固定通知
            sendPinNotification(message) {
                const isPinned = !!message.pinned_at;
                const notificationMessage = isPinned 
                    ? _t('Message pinned successfully')
                    : _t('Message unpinned successfully');
                
                if (this.env.services.notification) {
                    this.env.services.notification.add(notificationMessage, { type: 'success' });
                }
            },
            
            // 记录固定操作开始
            recordPinActionStart(message, thread) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('pin_action_logs') || '[]'
                    );
                    
                    actions.push({
                        messageId: message.id,
                        threadId: thread.id,
                        action: message.pinned_at ? 'unpin' : 'pin',
                        status: 'started',
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (actions.length > 100) {
                        actions.splice(0, actions.length - 100);
                    }
                    
                    localStorage.setItem('pin_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录固定操作开始失败:', error);
                }
            },
            
            // 记录固定操作成功
            recordPinActionSuccess(message, thread) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('pin_action_logs') || '[]'
                    );
                    
                    actions.push({
                        messageId: message.id,
                        threadId: thread.id,
                        action: message.pinned_at ? 'pin' : 'unpin', // 注意这里是操作后的状态
                        status: 'success',
                        timestamp: Date.now()
                    });
                    
                    localStorage.setItem('pin_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录固定操作成功失败:', error);
                }
            },
            
            // 记录固定操作错误
            recordPinActionError(message, thread, error) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('pin_action_logs') || '[]'
                    );
                    
                    actions.push({
                        messageId: message.id,
                        threadId: thread.id,
                        action: message.pinned_at ? 'unpin' : 'pin',
                        status: 'error',
                        error: error.message,
                        timestamp: Date.now()
                    });
                    
                    localStorage.setItem('pin_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录固定操作错误失败:', error);
                }
            },
            
            // 获取固定操作统计
            getPinActionStatistics() {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('pin_action_logs') || '[]'
                    );
                    
                    const stats = {
                        total: actions.length,
                        success: actions.filter(a => a.status === 'success').length,
                        errors: actions.filter(a => a.status === 'error').length,
                        pins: actions.filter(a => a.action === 'pin' && a.status === 'success').length,
                        unpins: actions.filter(a => a.action === 'unpin' && a.status === 'success').length
                    };
                    
                    return stats;
                } catch (error) {
                    return { total: 0, success: 0, errors: 0, pins: 0, unpins: 0 };
                }
            }
        }, { force: true });
        
        // 添加批量固定操作
        this.addBatchPinActions();
    },
    
    // 添加批量固定操作
    addBatchPinActions() {
        // 添加批量固定操作
        messageActionsRegistry.add("batch-pin", {
            condition: (component) => {
                return component.store.self.type === "partner" &&
                       component.props.thread?.model === "discuss.channel" &&
                       component.props.thread?.selectedMessages?.length > 1;
            },
            icon: "fa-thumb-tack",
            title: () => _t("Pin Selected"),
            onClick: (component) => this.batchPinMessages(component),
            sequence: 66,
        });
        
        // 添加批量取消固定操作
        messageActionsRegistry.add("batch-unpin", {
            condition: (component) => {
                return component.store.self.type === "partner" &&
                       component.props.thread?.model === "discuss.channel" &&
                       component.props.thread?.selectedPinnedMessages?.length > 1;
            },
            icon: "fa-times",
            title: () => _t("Unpin Selected"),
            onClick: (component) => this.batchUnpinMessages(component),
            sequence: 67,
        });
    },
    
    // 批量固定消息
    async batchPinMessages(component) {
        try {
            const messages = component.props.thread.selectedMessages;
            const results = await Promise.allSettled(
                messages.map(message => message.pin())
            );
            
            const successful = results.filter(r => r.status === 'fulfilled').length;
            const failed = results.filter(r => r.status === 'rejected').length;
            
            if (successful > 0) {
                this.env.services.notification.add(
                    _t('%s messages pinned successfully', successful),
                    { type: 'success' }
                );
            }
            
            if (failed > 0) {
                this.env.services.notification.add(
                    _t('%s messages failed to pin', failed),
                    { type: 'warning' }
                );
            }
            
        } catch (error) {
            console.error('批量固定失败:', error);
            this.env.services.notification.add('批量固定失败', { type: 'error' });
        }
    }
};

// 应用消息固定操作增强
MessagePinActionsEnhancer.enhanceMessagePinActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作配置
- 条件化显示控制

### 2. 权限控制
- 用户类型检查
- 环境限制验证
- 细粒度权限管理

### 3. 状态感知
- 动态标题显示
- 状态检查逻辑
- 智能操作切换

### 4. 国际化支持
- 多语言标题支持
- 本地化用户体验
- 翻译函数集成

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态操作配置

### 2. 策略模式 (Strategy Pattern)
- 不同条件下的显示策略
- 可配置的行为模式

### 3. 命令模式 (Command Pattern)
- 操作的命令化封装
- 统一的执行接口

## 注意事项

1. **权限验证**: 确保用户有执行固定操作的权限
2. **状态一致性**: 保持消息固定状态的一致性
3. **用户体验**: 提供清晰的操作反馈
4. **性能优化**: 避免频繁的状态检查

## 扩展建议

1. **批量操作**: 支持批量固定/取消固定消息
2. **权限细化**: 实现更细粒度的权限控制
3. **操作历史**: 记录固定操作的历史
4. **快捷键**: 添加键盘快捷键支持
5. **自定义规则**: 允许自定义固定规则

该文件为讨论应用的消息提供了重要的固定功能操作，是消息管理的核心组件之一。

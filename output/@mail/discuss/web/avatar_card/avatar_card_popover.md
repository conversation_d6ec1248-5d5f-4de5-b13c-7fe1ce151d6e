# Avatar Card Popover - 头像卡片弹出框

## 概述

`avatar_card_popover.js` 实现了 Odoo 讨论应用中的头像卡片弹出框组件，提供了用户信息展示和快速操作功能。该组件基于OWL框架，支持用户信息获取、聊天开启、个人资料查看等特性，集成了数据加载、动作服务、聊天钩子和用户交互等功能，为用户在讨论应用中提供了便捷的用户信息查看和交互入口，是讨论应用用户体验的重要组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/web/avatar_card/avatar_card_popover.js`
- **行数**: 70
- **模块**: `@mail/discuss/web/avatar_card/avatar_card_popover`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/hooks'           // 钩子工具
'@odoo/owl'                       // OWL 框架
'@mail/core/web/open_chat_hook'   // 开启聊天钩子
```

## 类型定义

### Props 类型

```javascript
static props = {
    id: { type: Number, required: true },
    close: { type: Function, required: true },
};
```

**属性说明**:
- **id**: 用户ID，必需的数字类型
- **close**: 关闭弹出框的函数，必需的函数类型

## 组件定义

### AvatarCardPopover 类

```javascript
const AvatarCardPopover = class AvatarCardPopover extends Component {
    static template = "mail.AvatarCardPopover";
    
    // 服务属性
    actionService;
    orm;
    openChat;
    
    // 数据属性
    user;
}
```

**组件特性**:
- 继承自OWL Component
- 使用专用模板
- 集成多种服务
- 支持用户数据管理

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.actionService = useService("action");
    this.orm = useService("orm");
    this.openChat = useOpenChat("res.users");
    onWillStart(async () => {
        [this.user] = await this.orm.read("res.users", [this.props.id], this.fieldNames);
    });
}
```

**初始化功能**:
- **服务注入**: 注入动作服务、ORM服务和聊天钩子
- **数据加载**: 在组件启动前异步加载用户数据
- **字段指定**: 使用fieldNames指定需要加载的用户字段
- **生命周期**: 利用onWillStart钩子管理数据加载时机

### 2. 字段定义

```javascript
get fieldNames() {
    return ["name", "email", "phone", "im_status", "share", "partner_id"];
}
```

**字段定义功能**:
- **用户信息**: 定义需要获取的用户字段
- **基础信息**: 包含姓名、邮箱、电话等基础信息
- **状态信息**: 包含在线状态和共享状态
- **关联信息**: 包含合作伙伴ID用于关联操作

### 3. 数据访问器

```javascript
get email() {
    return this.user.email;
}

get phone() {
    return this.user.phone;
}

get userId() {
    return this.user.id;
}
```

**访问器功能**:
- **邮箱访问**: 提供用户邮箱的便捷访问
- **电话访问**: 提供用户电话的便捷访问
- **用户ID**: 提供用户ID的便捷访问
- **数据封装**: 封装用户数据的访问逻辑

### 4. 显示控制

```javascript
get showViewProfileBtn() {
    return true;
}
```

**显示控制功能**:
- **按钮显示**: 控制查看个人资料按钮的显示
- **权限控制**: 可扩展为基于权限的显示控制
- **界面定制**: 支持界面元素的动态显示

### 5. 用户交互

```javascript
onSendClick() {
    this.openChat(this.userId);
    this.props.close();
}

async onClickViewProfile() {
    const action = await this.getProfileAction();
    this.actionService.doAction(action);
}
```

**交互功能**:
- **发送消息**: 点击发送按钮开启与用户的聊天
- **查看资料**: 点击查看资料按钮打开用户个人资料
- **弹框关闭**: 操作完成后自动关闭弹出框
- **动作执行**: 通过动作服务执行相应操作

### 6. 动作生成

```javascript
async getProfileAction() {
    return {
        res_id: this.user.partner_id[0],
        res_model: "res.partner",
        type: "ir.actions.act_window",
        views: [[false, "form"]],
    };
}
```

**动作生成功能**:
- **资源定位**: 使用合作伙伴ID定位资源
- **模型指定**: 指定res.partner模型
- **视图配置**: 配置表单视图显示
- **动作类型**: 使用窗口动作类型

## 使用场景

### 1. 头像卡片弹出框增强

```javascript
// 头像卡片弹出框增强功能
const AvatarCardPopoverEnhancer = {
    enhanceAvatarCardPopover: () => {
        const EnhancedAvatarCardPopover = class extends AvatarCardPopover {
            setup() {
                super.setup();
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.userService = useService("user");
                this.presenceService = useService("presence");
                this.dialogService = useService("dialog");
                
                // 增强的状态管理
                this.state = useState({
                    isLoading: false,
                    error: null,
                    showExtendedInfo: false,
                    lastActivity: null,
                    isOnline: false,
                    isFavorite: false,
                    isBlocked: false
                });
                
                // 增强的数据
                this.extendedUserData = null;
                this.userActivities = [];
                this.userPreferences = {};
                
                // 设置增强功能
                this.setupEnhancements();
            }
            
            // 设置增强功能
            setupEnhancements() {
                // 设置实时状态监听
                this.setupPresenceMonitoring();
                
                // 设置用户活动跟踪
                this.setupActivityTracking();
                
                // 设置用户偏好加载
                this.setupUserPreferences();
                
                // 设置扩展信息加载
                this.setupExtendedInfoLoading();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
            }
            
            // 增强的字段定义
            get fieldNames() {
                const baseFields = super.fieldNames;
                return [
                    ...baseFields,
                    "avatar_128",
                    "tz",
                    "lang",
                    "company_id",
                    "groups_id",
                    "login_date",
                    "create_date",
                    "write_date",
                    "active"
                ];
            }
            
            // 获取扩展用户信息
            async getExtendedUserInfo() {
                try {
                    this.state.isLoading = true;
                    this.state.error = null;
                    
                    // 获取用户详细信息
                    const userDetails = await this.orm.read("res.users", [this.props.id], [
                        "signature",
                        "notification_type",
                        "odoobot_state",
                        "livechat_username"
                    ]);
                    
                    // 获取用户活动
                    const activities = await this.getUserActivities();
                    
                    // 获取用户统计
                    const stats = await this.getUserStatistics();
                    
                    this.extendedUserData = {
                        details: userDetails[0],
                        activities: activities,
                        stats: stats
                    };
                    
                    this.state.isLoading = false;
                    
                } catch (error) {
                    console.error('获取扩展用户信息失败:', error);
                    this.state.error = error.message;
                    this.state.isLoading = false;
                }
            }
            
            // 获取用户活动
            async getUserActivities() {
                try {
                    // 获取最近的消息活动
                    const messages = await this.orm.searchRead(
                        "mail.message",
                        [["author_id", "=", this.user.partner_id[0]]],
                        ["date", "message_type", "model", "res_id"],
                        { limit: 10, order: "date desc" }
                    );
                    
                    // 获取最近的邮件活动
                    const mailActivities = await this.orm.searchRead(
                        "mail.activity",
                        [["user_id", "=", this.props.id]],
                        ["date_deadline", "activity_type_id", "summary", "res_model"],
                        { limit: 5, order: "date_deadline desc" }
                    );
                    
                    return {
                        messages: messages,
                        activities: mailActivities
                    };
                } catch (error) {
                    console.error('获取用户活动失败:', error);
                    return { messages: [], activities: [] };
                }
            }
            
            // 获取用户统计
            async getUserStatistics() {
                try {
                    // 获取消息统计
                    const messageCount = await this.orm.searchCount(
                        "mail.message",
                        [["author_id", "=", this.user.partner_id[0]]]
                    );
                    
                    // 获取频道统计
                    const channelCount = await this.orm.searchCount(
                        "discuss.channel.member",
                        [["partner_id", "=", this.user.partner_id[0]]]
                    );
                    
                    return {
                        messageCount: messageCount,
                        channelCount: channelCount,
                        joinDate: this.user.create_date,
                        lastLogin: this.user.login_date
                    };
                } catch (error) {
                    console.error('获取用户统计失败:', error);
                    return {
                        messageCount: 0,
                        channelCount: 0,
                        joinDate: null,
                        lastLogin: null
                    };
                }
            }
            
            // 增强的在线状态
            get isUserOnline() {
                return this.user.im_status === 'online';
            }
            
            // 获取状态文本
            get statusText() {
                switch (this.user.im_status) {
                    case 'online':
                        return '在线';
                    case 'away':
                        return '离开';
                    case 'offline':
                        return '离线';
                    default:
                        return '未知';
                }
            }
            
            // 获取状态颜色
            get statusColor() {
                switch (this.user.im_status) {
                    case 'online':
                        return '#00b04f';
                    case 'away':
                        return '#f0ad4e';
                    case 'offline':
                        return '#d9534f';
                    default:
                        return '#6c757d';
                }
            }
            
            // 增强的显示控制
            get showViewProfileBtn() {
                // 检查当前用户权限
                const currentUser = this.userService.user;
                
                // 不能查看自己的资料
                if (currentUser.userId === this.props.id) {
                    return false;
                }
                
                // 检查是否有查看权限
                return this.hasViewProfilePermission();
            }
            
            // 检查查看资料权限
            hasViewProfilePermission() {
                try {
                    // 检查用户是否为内部用户
                    if (this.user.share) {
                        return false;
                    }
                    
                    // 检查当前用户权限
                    const currentUser = this.userService.user;
                    if (!currentUser.isInternalUser) {
                        return false;
                    }
                    
                    return true;
                } catch (error) {
                    console.error('检查查看资料权限失败:', error);
                    return false;
                }
            }
            
            // 增强的发送消息
            async onSendClick() {
                try {
                    // 检查是否可以发送消息
                    if (!this.canSendMessage()) {
                        this.notificationService.add('无法向该用户发送消息', { type: 'warning' });
                        return;
                    }
                    
                    // 记录交互
                    this.recordUserInteraction('send_message');
                    
                    // 调用父类方法
                    super.onSendClick();
                    
                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.notificationService.add('发送消息失败', { type: 'error' });
                }
            }
            
            // 检查是否可以发送消息
            canSendMessage() {
                // 检查用户是否活跃
                if (!this.user.active) {
                    return false;
                }
                
                // 检查是否被阻止
                if (this.state.isBlocked) {
                    return false;
                }
                
                // 检查当前用户权限
                const currentUser = this.userService.user;
                if (!currentUser.isInternalUser && this.user.share) {
                    return false;
                }
                
                return true;
            }
            
            // 增强的查看资料
            async onClickViewProfile() {
                try {
                    // 记录交互
                    this.recordUserInteraction('view_profile');
                    
                    // 调用父类方法
                    await super.onClickViewProfile();
                    
                } catch (error) {
                    console.error('查看资料失败:', error);
                    this.notificationService.add('查看资料失败', { type: 'error' });
                }
            }
            
            // 添加到收藏
            async addToFavorites() {
                try {
                    // 这里可以实现添加到收藏的逻辑
                    this.state.isFavorite = true;
                    
                    // 保存到本地存储
                    this.saveFavoriteUsers();
                    
                    this.notificationService.add('已添加到收藏', { type: 'success' });
                    
                } catch (error) {
                    console.error('添加到收藏失败:', error);
                    this.notificationService.add('添加到收藏失败', { type: 'error' });
                }
            }
            
            // 从收藏移除
            async removeFromFavorites() {
                try {
                    this.state.isFavorite = false;
                    
                    // 从本地存储移除
                    this.saveFavoriteUsers();
                    
                    this.notificationService.add('已从收藏移除', { type: 'info' });
                    
                } catch (error) {
                    console.error('从收藏移除失败:', error);
                }
            }
            
            // 阻止用户
            async blockUser() {
                try {
                    const confirmed = await this.dialogService.confirm({
                        title: '确认阻止',
                        body: `确定要阻止用户 ${this.user.name} 吗？`,
                        confirmLabel: '阻止',
                        cancelLabel: '取消'
                    });
                    
                    if (confirmed) {
                        this.state.isBlocked = true;
                        
                        // 保存阻止列表
                        this.saveBlockedUsers();
                        
                        this.notificationService.add('用户已被阻止', { type: 'warning' });
                    }
                    
                } catch (error) {
                    console.error('阻止用户失败:', error);
                }
            }
            
            // 切换扩展信息显示
            toggleExtendedInfo() {
                this.state.showExtendedInfo = !this.state.showExtendedInfo;
                
                if (this.state.showExtendedInfo && !this.extendedUserData) {
                    this.getExtendedUserInfo();
                }
            }
            
            // 复制用户信息
            async copyUserInfo(type) {
                try {
                    let textToCopy = '';
                    
                    switch (type) {
                        case 'email':
                            textToCopy = this.user.email;
                            break;
                        case 'phone':
                            textToCopy = this.user.phone;
                            break;
                        case 'name':
                            textToCopy = this.user.name;
                            break;
                        default:
                            textToCopy = `${this.user.name} <${this.user.email}>`;
                    }
                    
                    await navigator.clipboard.writeText(textToCopy);
                    this.notificationService.add('已复制到剪贴板', { type: 'success' });
                    
                } catch (error) {
                    console.error('复制失败:', error);
                    this.notificationService.add('复制失败', { type: 'error' });
                }
            }
            
            // 记录用户交互
            recordUserInteraction(action) {
                try {
                    const interactions = JSON.parse(
                        localStorage.getItem('user_interactions') || '[]'
                    );
                    
                    interactions.push({
                        userId: this.props.id,
                        action: action,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个交互
                    if (interactions.length > 100) {
                        interactions.splice(0, interactions.length - 100);
                    }
                    
                    localStorage.setItem('user_interactions', JSON.stringify(interactions));
                } catch (error) {
                    console.warn('记录用户交互失败:', error);
                }
            }
            
            // 保存收藏用户
            saveFavoriteUsers() {
                try {
                    const favorites = JSON.parse(
                        localStorage.getItem('favorite_users') || '[]'
                    );
                    
                    if (this.state.isFavorite) {
                        if (!favorites.includes(this.props.id)) {
                            favorites.push(this.props.id);
                        }
                    } else {
                        const index = favorites.indexOf(this.props.id);
                        if (index > -1) {
                            favorites.splice(index, 1);
                        }
                    }
                    
                    localStorage.setItem('favorite_users', JSON.stringify(favorites));
                } catch (error) {
                    console.warn('保存收藏用户失败:', error);
                }
            }
            
            // 保存阻止用户
            saveBlockedUsers() {
                try {
                    const blocked = JSON.parse(
                        localStorage.getItem('blocked_users') || '[]'
                    );
                    
                    if (this.state.isBlocked) {
                        if (!blocked.includes(this.props.id)) {
                            blocked.push(this.props.id);
                        }
                    } else {
                        const index = blocked.indexOf(this.props.id);
                        if (index > -1) {
                            blocked.splice(index, 1);
                        }
                    }
                    
                    localStorage.setItem('blocked_users', JSON.stringify(blocked));
                } catch (error) {
                    console.warn('保存阻止用户失败:', error);
                }
            }
            
            // 获取头像卡片统计
            getAvatarCardStatistics() {
                return {
                    userId: this.props.id,
                    userName: this.user?.name,
                    isOnline: this.isUserOnline,
                    statusText: this.statusText,
                    canSendMessage: this.canSendMessage(),
                    canViewProfile: this.showViewProfileBtn,
                    isFavorite: this.state.isFavorite,
                    isBlocked: this.state.isBlocked,
                    hasExtendedInfo: !!this.extendedUserData,
                    isLoading: this.state.isLoading,
                    error: this.state.error
                };
            }
        };
        
        // 替换原始组件
        __exports.AvatarCardPopover = EnhancedAvatarCardPopover;
    }
};

// 应用头像卡片弹出框增强
AvatarCardPopoverEnhancer.enhanceAvatarCardPopover();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 类型安全的属性定义
- 模板驱动的渲染

### 2. 服务集成
- 多种服务的集成使用
- 异步数据加载
- 动作服务的封装

### 3. 用户体验
- 直观的用户信息展示
- 便捷的操作入口
- 响应式的界面设计

### 4. 数据管理
- 高效的数据获取
- 智能的字段选择
- 完整的错误处理

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 服务定位器模式 (Service Locator Pattern)
- 服务的注入和使用
- 依赖关系管理

### 3. 访问器模式 (Accessor Pattern)
- 数据的封装访问
- 计算属性的实现

## 注意事项

1. **数据加载**: 确保用户数据的正确加载和错误处理
2. **权限控制**: 实现适当的权限检查和访问控制
3. **用户体验**: 提供清晰的加载状态和错误反馈
4. **性能优化**: 避免不必要的数据获取和重复渲染

## 扩展建议

1. **功能增强**: 添加更多用户操作和信息展示
2. **权限管理**: 实现更细粒度的权限控制
3. **缓存机制**: 实现用户数据的智能缓存
4. **个性化**: 支持用户界面的个性化定制
5. **集成扩展**: 与其他系统功能的深度集成

该组件为讨论应用提供了重要的用户信息展示和交互功能，是用户体验的关键组件。

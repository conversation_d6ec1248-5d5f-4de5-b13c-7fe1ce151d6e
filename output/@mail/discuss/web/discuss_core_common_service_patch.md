# Discuss Core Common Service Patch - 讨论核心通用服务补丁

## 概述

`discuss_core_common_service_patch.js` 实现了对 Odoo 讨论应用核心通用服务的补丁扩展，专门处理频道删除通知的增强逻辑。该补丁通过Odoo的补丁机制扩展了DiscussCoreCommon服务，重写了频道删除处理方法，集成了星标消息清理、收件箱消息过滤、历史消息清理和线程状态管理等功能，为讨论应用在Web环境中提供了完整的频道删除处理支持，是讨论应用数据一致性和状态管理的重要组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/web/discuss_core_common_service_patch.js`
- **行数**: 44
- **模块**: `@mail/discuss/web/discuss_core_common_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/discuss/core/common/discuss_core_common_service'   // 讨论核心通用服务
'@web/core/utils/patch'                                   // 补丁工具
```

## 补丁定义

### DiscussCoreCommon 原型补丁

```javascript
patch(DiscussCoreCommon.prototype, {
    _handleNotificationChannelDelete(thread, metadata) {
        // 频道删除处理逻辑
        // ...
        super._handleNotificationChannelDelete(thread, metadata);
    },
});
```

**补丁特性**:
- 扩展讨论核心服务功能
- 重写频道删除处理方法
- 增强数据清理逻辑
- 保持原有功能完整性

## 核心功能

### 1. 星标消息清理

```javascript
const filteredStarredMessages = [];
let starredCounter = 0;
for (const msg of this.store.starred.messages) {
    if (!msg.thread?.eq(thread)) {
        filteredStarredMessages.push(msg);
    } else {
        starredCounter++;
    }
}
this.store.starred.messages = filteredStarredMessages;
if (notifId > this.store.starred.counter_bus_id) {
    this.store.starred.counter -= starredCounter;
}
```

**星标清理功能**:
- **消息过滤**: 过滤掉被删除频道中的星标消息
- **计数统计**: 统计被删除的星标消息数量
- **状态更新**: 更新星标消息列表和计数器
- **总线同步**: 基于通知ID进行计数器同步

### 2. 收件箱消息过滤

```javascript
this.store.inbox.messages = this.store.inbox.messages.filter(
    (msg) => !msg.thread?.eq(thread)
);
if (notifId > this.store.inbox.counter_bus_id) {
    this.store.inbox.counter -= thread.message_needaction_counter;
}
```

**收件箱过滤功能**:
- **消息过滤**: 从收件箱中移除被删除频道的消息
- **计数更新**: 减少收件箱的待处理消息计数
- **状态同步**: 基于通知ID和总线ID进行同步
- **数据一致性**: 确保收件箱数据的一致性

### 3. 历史消息清理

```javascript
this.store.history.messages = this.store.history.messages.filter(
    (msg) => !msg.thread?.eq(thread)
);
```

**历史清理功能**:
- **消息过滤**: 从历史记录中移除被删除频道的消息
- **数据清理**: 确保历史数据的完整性
- **内存优化**: 释放不再需要的消息数据
- **状态维护**: 维护历史记录的正确状态

### 4. 线程状态管理

```javascript
if (thread.eq(this.store.discuss.thread)) {
    this.store.inbox.setAsDiscussThread();
}
```

**状态管理功能**:
- **当前线程检查**: 检查被删除的是否为当前讨论线程
- **线程切换**: 如果是当前线程，则切换到收件箱
- **状态重置**: 重置讨论界面的线程状态
- **用户体验**: 确保用户界面的连续性

### 5. 父类方法调用

```javascript
super._handleNotificationChannelDelete(thread, metadata);
```

**父类调用功能**:
- **原有逻辑**: 执行父类的频道删除处理逻辑
- **功能扩展**: 在原有基础上增加额外处理
- **兼容性**: 保持与原有系统的兼容性
- **完整性**: 确保删除处理的完整性

## 使用场景

### 1. 讨论核心服务频道删除增强

```javascript
// 讨论核心服务频道删除增强功能
const DiscussCoreCommonServicePatchEnhancer = {
    enhanceDiscussCoreCommonServicePatch: () => {
        const EnhancedDiscussCoreCommonPatch = {
            // 增强的频道删除处理
            _handleNotificationChannelDelete(thread, metadata) {
                try {
                    const { notifId } = metadata;
                    
                    // 记录删除操作
                    this.recordChannelDeletion(thread, metadata);
                    
                    // 增强的星标消息清理
                    this.cleanupStarredMessagesEnhanced(thread, notifId);
                    
                    // 增强的收件箱消息过滤
                    this.filterInboxMessagesEnhanced(thread, notifId);
                    
                    // 增强的历史消息清理
                    this.cleanupHistoryMessagesEnhanced(thread, notifId);
                    
                    // 增强的线程状态管理
                    this.manageThreadStateEnhanced(thread);
                    
                    // 清理相关数据
                    this.cleanupRelatedData(thread);
                    
                    // 通知其他组件
                    this.notifyChannelDeletion(thread, metadata);
                    
                    // 调用父类方法
                    super._handleNotificationChannelDelete(thread, metadata);
                    
                } catch (error) {
                    console.error('处理频道删除通知失败:', error);
                    this.handleChannelDeletionError(error, thread, metadata);
                }
            },
            
            // 增强的星标消息清理
            cleanupStarredMessagesEnhanced(thread, notifId) {
                try {
                    const filteredStarredMessages = [];
                    let starredCounter = 0;
                    const deletedStarredMessages = [];
                    
                    for (const msg of this.store.starred.messages) {
                        if (!msg.thread?.eq(thread)) {
                            filteredStarredMessages.push(msg);
                        } else {
                            starredCounter++;
                            deletedStarredMessages.push({
                                id: msg.id,
                                subject: msg.subject,
                                date: msg.date
                            });
                        }
                    }
                    
                    // 更新星标消息列表
                    this.store.starred.messages = filteredStarredMessages;
                    
                    // 更新计数器
                    if (notifId > this.store.starred.counter_bus_id) {
                        this.store.starred.counter -= starredCounter;
                    }
                    
                    // 记录删除的星标消息
                    this.recordDeletedStarredMessages(thread.id, deletedStarredMessages);
                    
                    // 触发星标消息更新事件
                    this.triggerStarredMessagesUpdate();
                    
                } catch (error) {
                    console.error('清理星标消息失败:', error);
                }
            },
            
            // 增强的收件箱消息过滤
            filterInboxMessagesEnhanced(thread, notifId) {
                try {
                    const originalCount = this.store.inbox.messages.length;
                    const deletedInboxMessages = [];
                    
                    // 收集要删除的消息
                    this.store.inbox.messages.forEach(msg => {
                        if (msg.thread?.eq(thread)) {
                            deletedInboxMessages.push({
                                id: msg.id,
                                subject: msg.subject,
                                date: msg.date,
                                needaction: msg.needaction
                            });
                        }
                    });
                    
                    // 过滤消息
                    this.store.inbox.messages = this.store.inbox.messages.filter(
                        (msg) => !msg.thread?.eq(thread)
                    );
                    
                    // 更新计数器
                    if (notifId > this.store.inbox.counter_bus_id) {
                        this.store.inbox.counter -= thread.message_needaction_counter;
                    }
                    
                    // 记录删除的收件箱消息
                    this.recordDeletedInboxMessages(thread.id, deletedInboxMessages);
                    
                    // 触发收件箱更新事件
                    this.triggerInboxUpdate();
                    
                    // 更新收件箱统计
                    this.updateInboxStatistics(originalCount - this.store.inbox.messages.length);
                    
                } catch (error) {
                    console.error('过滤收件箱消息失败:', error);
                }
            },
            
            // 增强的历史消息清理
            cleanupHistoryMessagesEnhanced(thread, notifId) {
                try {
                    const originalCount = this.store.history.messages.length;
                    const deletedHistoryMessages = [];
                    
                    // 收集要删除的历史消息
                    this.store.history.messages.forEach(msg => {
                        if (msg.thread?.eq(thread)) {
                            deletedHistoryMessages.push({
                                id: msg.id,
                                subject: msg.subject,
                                date: msg.date
                            });
                        }
                    });
                    
                    // 清理历史消息
                    this.store.history.messages = this.store.history.messages.filter(
                        (msg) => !msg.thread?.eq(thread)
                    );
                    
                    // 记录删除的历史消息
                    this.recordDeletedHistoryMessages(thread.id, deletedHistoryMessages);
                    
                    // 触发历史更新事件
                    this.triggerHistoryUpdate();
                    
                    // 更新历史统计
                    this.updateHistoryStatistics(originalCount - this.store.history.messages.length);
                    
                } catch (error) {
                    console.error('清理历史消息失败:', error);
                }
            },
            
            // 增强的线程状态管理
            manageThreadStateEnhanced(thread) {
                try {
                    // 检查是否为当前讨论线程
                    if (thread.eq(this.store.discuss.thread)) {
                        // 记录线程切换
                        this.recordThreadSwitch(thread.id, 'inbox');
                        
                        // 保存当前状态
                        this.saveCurrentThreadState();
                        
                        // 切换到收件箱
                        this.store.inbox.setAsDiscussThread();
                        
                        // 触发线程切换事件
                        this.triggerThreadSwitch('inbox');
                        
                        // 更新URL
                        this.updateDiscussURL('inbox');
                    }
                    
                    // 清理线程相关的UI状态
                    this.cleanupThreadUIState(thread);
                    
                } catch (error) {
                    console.error('管理线程状态失败:', error);
                }
            },
            
            // 清理相关数据
            cleanupRelatedData(thread) {
                try {
                    // 清理线程成员数据
                    this.cleanupThreadMembers(thread);
                    
                    // 清理线程设置
                    this.cleanupThreadSettings(thread);
                    
                    // 清理缓存数据
                    this.cleanupThreadCache(thread);
                    
                    // 清理本地存储
                    this.cleanupThreadLocalStorage(thread);
                    
                } catch (error) {
                    console.error('清理相关数据失败:', error);
                }
            },
            
            // 清理线程成员数据
            cleanupThreadMembers(thread) {
                try {
                    // 从成员列表中移除
                    if (this.store.threadMembers) {
                        delete this.store.threadMembers[thread.id];
                    }
                    
                    // 清理成员缓存
                    if (this.store.memberCache) {
                        Object.keys(this.store.memberCache).forEach(key => {
                            if (key.startsWith(`${thread.id}_`)) {
                                delete this.store.memberCache[key];
                            }
                        });
                    }
                } catch (error) {
                    console.warn('清理线程成员数据失败:', error);
                }
            },
            
            // 清理线程设置
            cleanupThreadSettings(thread) {
                try {
                    // 清理线程设置
                    if (this.store.threadSettings) {
                        delete this.store.threadSettings[thread.id];
                    }
                    
                    // 清理通知设置
                    if (this.store.notificationSettings) {
                        delete this.store.notificationSettings[thread.id];
                    }
                } catch (error) {
                    console.warn('清理线程设置失败:', error);
                }
            },
            
            // 清理线程缓存
            cleanupThreadCache(thread) {
                try {
                    // 清理消息缓存
                    if (this.store.messageCache) {
                        Object.keys(this.store.messageCache).forEach(key => {
                            const message = this.store.messageCache[key];
                            if (message.thread?.eq(thread)) {
                                delete this.store.messageCache[key];
                            }
                        });
                    }
                    
                    // 清理附件缓存
                    if (this.store.attachmentCache) {
                        Object.keys(this.store.attachmentCache).forEach(key => {
                            const attachment = this.store.attachmentCache[key];
                            if (attachment.thread?.eq(thread)) {
                                delete this.store.attachmentCache[key];
                            }
                        });
                    }
                } catch (error) {
                    console.warn('清理线程缓存失败:', error);
                }
            },
            
            // 清理本地存储
            cleanupThreadLocalStorage(thread) {
                try {
                    // 清理线程相关的本地存储
                    const keysToRemove = [];
                    
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && key.includes(`thread_${thread.id}`)) {
                            keysToRemove.push(key);
                        }
                    }
                    
                    keysToRemove.forEach(key => {
                        localStorage.removeItem(key);
                    });
                } catch (error) {
                    console.warn('清理本地存储失败:', error);
                }
            },
            
            // 记录频道删除
            recordChannelDeletion(thread, metadata) {
                try {
                    const deletionRecord = {
                        threadId: thread.id,
                        threadName: thread.name,
                        threadType: thread.channel_type,
                        notifId: metadata.notifId,
                        timestamp: Date.now(),
                        deletedBy: metadata.deletedBy || 'unknown'
                    };
                    
                    // 保存到本地存储
                    const deletions = JSON.parse(
                        localStorage.getItem('channel_deletions') || '[]'
                    );
                    
                    deletions.push(deletionRecord);
                    
                    // 保留最近100个删除记录
                    if (deletions.length > 100) {
                        deletions.splice(0, deletions.length - 100);
                    }
                    
                    localStorage.setItem('channel_deletions', JSON.stringify(deletions));
                } catch (error) {
                    console.warn('记录频道删除失败:', error);
                }
            },
            
            // 通知频道删除
            notifyChannelDeletion(thread, metadata) {
                try {
                    // 触发全局事件
                    this.env.bus.trigger('channel:deleted', {
                        thread: thread,
                        metadata: metadata,
                        timestamp: Date.now()
                    });
                    
                    // 通知相关组件
                    if (this.notificationService) {
                        this.notificationService.add(
                            `频道 "${thread.name}" 已被删除`,
                            { type: 'info' }
                        );
                    }
                } catch (error) {
                    console.warn('通知频道删除失败:', error);
                }
            },
            
            // 处理频道删除错误
            handleChannelDeletionError(error, thread, metadata) {
                try {
                    // 记录错误
                    console.error('频道删除处理错误:', {
                        error: error.message,
                        thread: thread.id,
                        metadata: metadata
                    });
                    
                    // 尝试恢复
                    this.attemptChannelDeletionRecovery(thread, metadata);
                    
                    // 通知用户
                    if (this.notificationService) {
                        this.notificationService.add(
                            '频道删除处理出现问题，请刷新页面',
                            { type: 'error' }
                        );
                    }
                } catch (recoveryError) {
                    console.error('频道删除错误恢复失败:', recoveryError);
                }
            },
            
            // 获取频道删除统计
            getChannelDeletionStatistics() {
                try {
                    const deletions = JSON.parse(
                        localStorage.getItem('channel_deletions') || '[]'
                    );
                    
                    return {
                        totalDeletions: deletions.length,
                        recentDeletions: deletions.slice(-10),
                        deletionsByType: this.groupDeletionsByType(deletions),
                        lastDeletion: deletions[deletions.length - 1] || null
                    };
                } catch (error) {
                    return {
                        totalDeletions: 0,
                        recentDeletions: [],
                        deletionsByType: {},
                        lastDeletion: null
                    };
                }
            }
        };
        
        patch(DiscussCoreCommon.prototype, EnhancedDiscussCoreCommonPatch);
    }
};

// 应用讨论核心服务频道删除补丁增强
DiscussCoreCommonServicePatchEnhancer.enhanceDiscussCoreCommonServicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 数据一致性
- 完整的数据清理逻辑
- 多层级的状态同步
- 总线ID的正确处理

### 3. 状态管理
- 智能的线程状态切换
- 完整的UI状态维护
- 用户体验的连续性

### 4. 性能优化
- 高效的消息过滤算法
- 合理的内存清理
- 最小化的数据操作

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 状态变化的通知
- 事件驱动的更新

### 3. 策略模式 (Strategy Pattern)
- 不同类型数据的清理策略
- 可配置的处理行为

## 注意事项

1. **数据一致性**: 确保所有相关数据的正确清理
2. **状态同步**: 正确处理总线ID和通知ID的同步
3. **用户体验**: 保持界面状态的连续性和一致性
4. **性能影响**: 避免大量数据操作影响性能

## 扩展建议

1. **错误处理**: 添加更完善的错误处理和恢复机制
2. **日志记录**: 实现详细的操作日志记录
3. **性能优化**: 优化大量消息的处理性能
4. **事件通知**: 添加更丰富的事件通知机制
5. **数据备份**: 实现删除前的数据备份功能

该补丁为讨论应用提供了重要的频道删除处理增强，确保了数据的一致性和用户体验的连续性。

# Message Patch - 消息补丁

## 概述

`message_patch.js` 实现了对 Odoo 讨论应用公共Web环境中消息组件的补丁扩展，专门添加了子频道菜单的点击处理功能。该补丁通过Odoo的补丁机制扩展了Message组件，重写了通知消息点击方法，添加了对子频道菜单的特殊处理，为用户提供了便捷的子频道访问入口，是讨论应用中消息交互功能的重要增强。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/message_patch.js`
- **行数**: 27
- **模块**: `@mail/discuss/core/public_web/message_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message'  // 消息组件
'@web/core/utils/patch'      // 补丁工具
```

## 补丁定义

### Message 补丁

```javascript
patch(Message.prototype, {
    /**
     * @override
     * @param {MouseEvent} ev
     */
    async onClickNotificationMessage(ev) {
        const { oeType } = ev.target.dataset;
        if (oeType === "sub-channels-menu") {
            this.env.subChannelMenu?.open();
        }
        await super.onClickNotificationMessage(...arguments);
    },
});
```

**补丁特性**:
- 扩展消息组件功能
- 添加子频道菜单处理
- 保持原有点击逻辑
- 支持数据属性识别

## 核心功能

### 1. 通知消息点击处理

```javascript
async onClickNotificationMessage(ev) {
    const { oeType } = ev.target.dataset;
    if (oeType === "sub-channels-menu") {
        this.env.subChannelMenu?.open();
    }
    await super.onClickNotificationMessage(...arguments);
}
```

**点击处理功能**:
- **数据属性检查**: 检查点击目标的oeType数据属性
- **子频道菜单**: 特殊处理子频道菜单的点击
- **菜单打开**: 调用环境中的子频道菜单打开方法
- **父类调用**: 保持原有的点击处理逻辑

## 使用场景

### 1. 消息组件增强

```javascript
// 消息组件增强功能
const MessagePatchEnhancer = {
    enhanceMessagePatch: () => {
        const EnhancedMessagePatch = {
            // 增强的通知消息点击处理
            async onClickNotificationMessage(ev) {
                const { oeType, oeAction, oeTarget } = ev.target.dataset;
                
                try {
                    // 记录点击事件
                    this.recordClickEvent(ev, oeType);
                    
                    // 处理不同类型的点击
                    switch (oeType) {
                        case "sub-channels-menu":
                            await this.handleSubChannelMenuClick(ev);
                            break;
                        case "thread-actions":
                            await this.handleThreadActionsClick(ev, oeAction);
                            break;
                        case "user-mention":
                            await this.handleUserMentionClick(ev, oeTarget);
                            break;
                        case "channel-mention":
                            await this.handleChannelMentionClick(ev, oeTarget);
                            break;
                        case "attachment-preview":
                            await this.handleAttachmentPreviewClick(ev, oeTarget);
                            break;
                        case "emoji-reaction":
                            await this.handleEmojiReactionClick(ev, oeAction);
                            break;
                        case "quick-reply":
                            await this.handleQuickReplyClick(ev);
                            break;
                        default:
                            // 处理其他类型或调用父类方法
                            await super.onClickNotificationMessage(...arguments);
                            break;
                    }
                } catch (error) {
                    console.error('处理通知消息点击失败:', error);
                    // 回退到父类处理
                    await super.onClickNotificationMessage(...arguments);
                }
            },
            
            // 处理子频道菜单点击
            async handleSubChannelMenuClick(ev) {
                try {
                    // 检查子频道菜单是否可用
                    if (!this.env.subChannelMenu) {
                        console.warn('子频道菜单不可用');
                        return;
                    }
                    
                    // 获取相关数据
                    const messageId = this.message?.id;
                    const threadId = this.message?.thread?.id;
                    
                    // 打开子频道菜单
                    await this.env.subChannelMenu.open({
                        messageId,
                        threadId,
                        position: this.getMenuPosition(ev)
                    });
                    
                    // 记录菜单打开事件
                    this.recordMenuOpenEvent('sub-channels', messageId);
                    
                } catch (error) {
                    console.error('打开子频道菜单失败:', error);
                    this.showErrorNotification('无法打开子频道菜单');
                }
            },
            
            // 处理线程操作点击
            async handleThreadActionsClick(ev, action) {
                try {
                    const thread = this.message?.thread;
                    if (!thread) {
                        throw new Error('线程不存在');
                    }
                    
                    switch (action) {
                        case 'pin':
                            await thread.pin();
                            break;
                        case 'unpin':
                            await thread.unpin();
                            break;
                        case 'mute':
                            await thread.mute();
                            break;
                        case 'unmute':
                            await thread.unmute();
                            break;
                        case 'archive':
                            await thread.archive();
                            break;
                        case 'unarchive':
                            await thread.unarchive();
                            break;
                        case 'leave':
                            await thread.leave();
                            break;
                        default:
                            console.warn('未知的线程操作:', action);
                    }
                    
                    // 记录操作事件
                    this.recordThreadActionEvent(action, thread.id);
                    
                } catch (error) {
                    console.error('执行线程操作失败:', error);
                    this.showErrorNotification(`操作失败: ${error.message}`);
                }
            },
            
            // 处理用户提及点击
            async handleUserMentionClick(ev, userId) {
                try {
                    const user = this.store.Persona.get(userId);
                    if (!user) {
                        throw new Error('用户不存在');
                    }
                    
                    // 打开用户资料或私聊
                    if (ev.ctrlKey || ev.metaKey) {
                        // Ctrl+点击打开用户资料
                        await this.openUserProfile(user);
                    } else {
                        // 普通点击打开私聊
                        await this.openPrivateChat(user);
                    }
                    
                    // 记录用户交互事件
                    this.recordUserInteractionEvent('mention_click', userId);
                    
                } catch (error) {
                    console.error('处理用户提及点击失败:', error);
                    this.showErrorNotification('无法打开用户信息');
                }
            },
            
            // 处理频道提及点击
            async handleChannelMentionClick(ev, channelId) {
                try {
                    const channel = this.store.Thread.get({ id: channelId, model: 'discuss.channel' });
                    if (!channel) {
                        throw new Error('频道不存在');
                    }
                    
                    // 打开频道
                    await channel.open();
                    
                    // 记录频道访问事件
                    this.recordChannelAccessEvent('mention_click', channelId);
                    
                } catch (error) {
                    console.error('处理频道提及点击失败:', error);
                    this.showErrorNotification('无法打开频道');
                }
            },
            
            // 处理附件预览点击
            async handleAttachmentPreviewClick(ev, attachmentId) {
                try {
                    const attachment = this.store.Attachment.get(attachmentId);
                    if (!attachment) {
                        throw new Error('附件不存在');
                    }
                    
                    // 打开附件预览
                    await this.openAttachmentPreview(attachment);
                    
                    // 记录附件访问事件
                    this.recordAttachmentAccessEvent('preview_click', attachmentId);
                    
                } catch (error) {
                    console.error('处理附件预览点击失败:', error);
                    this.showErrorNotification('无法预览附件');
                }
            },
            
            // 处理表情反应点击
            async handleEmojiReactionClick(ev, emoji) {
                try {
                    const message = this.message;
                    if (!message) {
                        throw new Error('消息不存在');
                    }
                    
                    // 切换表情反应
                    await message.toggleEmojiReaction(emoji);
                    
                    // 记录反应事件
                    this.recordEmojiReactionEvent(emoji, message.id);
                    
                } catch (error) {
                    console.error('处理表情反应失败:', error);
                    this.showErrorNotification('无法添加表情反应');
                }
            },
            
            // 处理快速回复点击
            async handleQuickReplyClick(ev) {
                try {
                    const thread = this.message?.thread;
                    if (!thread) {
                        throw new Error('线程不存在');
                    }
                    
                    // 聚焦到编辑器
                    const composer = thread.composer;
                    if (composer) {
                        composer.focus();
                        
                        // 添加回复引用
                        const replyText = `@${this.message.author?.name}: `;
                        composer.insertText(replyText);
                    }
                    
                    // 记录快速回复事件
                    this.recordQuickReplyEvent(this.message.id);
                    
                } catch (error) {
                    console.error('处理快速回复失败:', error);
                    this.showErrorNotification('无法快速回复');
                }
            },
            
            // 获取菜单位置
            getMenuPosition(ev) {
                const rect = ev.target.getBoundingClientRect();
                return {
                    x: rect.left,
                    y: rect.bottom,
                    width: rect.width,
                    height: rect.height
                };
            },
            
            // 打开用户资料
            async openUserProfile(user) {
                // 实现用户资料打开逻辑
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'res.partner',
                    res_id: user.id,
                    views: [[false, 'form']],
                    target: 'new'
                });
            },
            
            // 打开私聊
            async openPrivateChat(user) {
                try {
                    // 查找或创建私聊
                    let chat = this.store.Thread.find(thread => 
                        thread.channel_type === 'chat' && 
                        thread.correspondent?.id === user.id
                    );
                    
                    if (!chat) {
                        // 创建新的私聊
                        const result = await this.env.services.orm.call(
                            'discuss.channel',
                            'channel_get',
                            [user.id]
                        );
                        chat = this.store.Thread.insert(result);
                    }
                    
                    // 打开私聊
                    await chat.open();
                    
                } catch (error) {
                    console.error('打开私聊失败:', error);
                    throw error;
                }
            },
            
            // 打开附件预览
            async openAttachmentPreview(attachment) {
                // 实现附件预览逻辑
                if (attachment.isImage) {
                    this.env.services.dialog.add(ImagePreviewDialog, {
                        attachment
                    });
                } else {
                    // 下载或在新窗口打开
                    window.open(attachment.url, '_blank');
                }
            },
            
            // 显示错误通知
            showErrorNotification(message) {
                this.env.services.notification.add(message, {
                    type: 'error',
                    sticky: false
                });
            },
            
            // 记录点击事件
            recordClickEvent(ev, oeType) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('message_click_events') || '[]'
                    );
                    
                    events.push({
                        oeType,
                        messageId: this.message?.id,
                        threadId: this.message?.thread?.id,
                        timestamp: Date.now(),
                        ctrlKey: ev.ctrlKey,
                        shiftKey: ev.shiftKey,
                        altKey: ev.altKey
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('message_click_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录点击事件失败:', error);
                }
            },
            
            // 记录菜单打开事件
            recordMenuOpenEvent(menuType, messageId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('menu_open_events') || '[]'
                    );
                    
                    events.push({
                        menuType,
                        messageId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个事件
                    if (events.length > 50) {
                        events.splice(0, events.length - 50);
                    }
                    
                    localStorage.setItem('menu_open_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录菜单打开事件失败:', error);
                }
            },
            
            // 记录线程操作事件
            recordThreadActionEvent(action, threadId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('thread_action_events') || '[]'
                    );
                    
                    events.push({
                        action,
                        threadId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('thread_action_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录线程操作事件失败:', error);
                }
            },
            
            // 记录用户交互事件
            recordUserInteractionEvent(interactionType, userId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('user_interaction_events') || '[]'
                    );
                    
                    events.push({
                        interactionType,
                        userId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('user_interaction_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录用户交互事件失败:', error);
                }
            },
            
            // 记录频道访问事件
            recordChannelAccessEvent(accessType, channelId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('channel_access_events') || '[]'
                    );
                    
                    events.push({
                        accessType,
                        channelId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('channel_access_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录频道访问事件失败:', error);
                }
            },
            
            // 记录附件访问事件
            recordAttachmentAccessEvent(accessType, attachmentId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('attachment_access_events') || '[]'
                    );
                    
                    events.push({
                        accessType,
                        attachmentId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个事件
                    if (events.length > 50) {
                        events.splice(0, events.length - 50);
                    }
                    
                    localStorage.setItem('attachment_access_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录附件访问事件失败:', error);
                }
            },
            
            // 记录表情反应事件
            recordEmojiReactionEvent(emoji, messageId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('emoji_reaction_events') || '[]'
                    );
                    
                    events.push({
                        emoji,
                        messageId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('emoji_reaction_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录表情反应事件失败:', error);
                }
            },
            
            // 记录快速回复事件
            recordQuickReplyEvent(messageId) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('quick_reply_events') || '[]'
                    );
                    
                    events.push({
                        messageId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个事件
                    if (events.length > 50) {
                        events.splice(0, events.length - 50);
                    }
                    
                    localStorage.setItem('quick_reply_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录快速回复事件失败:', error);
                }
            },
            
            // 获取交互统计
            getInteractionStatistics() {
                try {
                    return {
                        clickEvents: JSON.parse(localStorage.getItem('message_click_events') || '[]').length,
                        menuOpens: JSON.parse(localStorage.getItem('menu_open_events') || '[]').length,
                        threadActions: JSON.parse(localStorage.getItem('thread_action_events') || '[]').length,
                        userInteractions: JSON.parse(localStorage.getItem('user_interaction_events') || '[]').length,
                        channelAccess: JSON.parse(localStorage.getItem('channel_access_events') || '[]').length,
                        attachmentAccess: JSON.parse(localStorage.getItem('attachment_access_events') || '[]').length,
                        emojiReactions: JSON.parse(localStorage.getItem('emoji_reaction_events') || '[]').length,
                        quickReplies: JSON.parse(localStorage.getItem('quick_reply_events') || '[]').length
                    };
                } catch (error) {
                    return {
                        clickEvents: 0,
                        menuOpens: 0,
                        threadActions: 0,
                        userInteractions: 0,
                        channelAccess: 0,
                        attachmentAccess: 0,
                        emojiReactions: 0,
                        quickReplies: 0
                    };
                }
            }
        };
        
        patch(Message.prototype, EnhancedMessagePatch);
    }
};

// 应用消息补丁增强
MessagePatchEnhancer.enhanceMessagePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 事件处理
- 数据属性驱动的事件分发
- 异步事件处理支持
- 错误处理和回退机制

### 3. 环境集成
- 环境服务的安全访问
- 可选链操作符使用
- 上下文感知的操作

### 4. 用户交互
- 直观的点击交互
- 菜单操作支持
- 响应式用户反馈

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 命令模式 (Command Pattern)
- 点击事件的命令化处理
- 统一的操作接口

### 3. 策略模式 (Strategy Pattern)
- 不同类型点击的处理策略
- 可配置的行为模式

## 注意事项

1. **事件冒泡**: 注意事件冒泡和阻止默认行为
2. **错误处理**: 提供完善的错误处理和用户反馈
3. **性能优化**: 避免频繁的DOM操作影响性能
4. **兼容性**: 确保与原有功能的兼容性

## 扩展建议

1. **更多交互**: 添加更多类型的交互处理
2. **手势支持**: 支持触摸手势和快捷键
3. **上下文菜单**: 实现右键上下文菜单
4. **拖拽操作**: 支持拖拽相关的交互
5. **无障碍**: 增强无障碍访问支持

该补丁为讨论应用的消息组件提供了重要的交互功能增强，特别是子频道菜单的便捷访问。

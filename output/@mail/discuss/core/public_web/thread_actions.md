# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 讨论应用公共Web环境中的线程操作注册，专门添加了显示子频道列表的操作。该文件通过线程操作注册表添加了"show-threads"操作，集成了子频道列表组件、弹出框功能、环境配置等特性，为用户提供了便捷的子频道浏览入口，是讨论应用中子频道功能的重要交互组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/thread_actions.js`
- **行数**: 48
- **模块**: `@mail/discuss/core/public_web/thread_actions`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_actions'              // 线程操作注册表
'@mail/discuss/core/public_web/sub_channel_list' // 子频道列表组件
'@odoo/owl'                                     // OWL 框架
'@web/core/l10n/translation'                    // 国际化
'@web/core/popover/popover_hook'                // 弹出框钩子
```

## 操作注册

### show-threads 操作

```javascript
threadActionsRegistry.add("show-threads", {
    close(component, action) {
        action.popover?.close();
    },
    condition: (component) =>
        component.thread?.hasSubChannelFeature ||
        component.thread?.parent_channel_id?.hasSubChannelFeature,
    icon: "fa fa-fw fa-comments-o",
    iconLarge: "fa fa-fw fa-lg fa-comments-o",
    name: _t("Threads"),
    component: SubChannelList,
    componentProps(action) {
        return { close: () => action.close() };
    },
    setup(action) {
        const component = useComponent();
        if (!component.props.chatWindow) {
            action.popover = usePopover(SubChannelList, {
                onClose: () => action.close(),
                fixedPosition: true,
            });
        }
        useChildSubEnv({
            subChannelMenu: {
                open: () => action.open(),
            },
        });
    },
    open: (component, action) => {
        const thread = component.thread?.parent_channel_id || component.thread;
        action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), { thread });
    },
    sequence: (comp) => (comp.props.chatWindow ? 40 : 5),
    sequenceGroup: 10,
    toggle: true,
});
```

**操作特性**:
- **条件显示**: 仅在支持子频道功能的线程中显示
- **弹出框集成**: 使用弹出框显示子频道列表
- **环境配置**: 设置子频道菜单环境
- **动态排序**: 根据聊天窗口状态调整显示顺序

## 核心功能

### 1. 显示条件

```javascript
condition: (component) =>
    component.thread?.hasSubChannelFeature ||
    component.thread?.parent_channel_id?.hasSubChannelFeature,
```

**条件检查**:
- **子频道功能**: 当前线程支持子频道功能
- **父频道功能**: 父频道支持子频道功能
- **逻辑或**: 满足任一条件即可显示

### 2. 组件设置

```javascript
setup(action) {
    const component = useComponent();
    if (!component.props.chatWindow) {
        action.popover = usePopover(SubChannelList, {
            onClose: () => action.close(),
            fixedPosition: true,
        });
    }
    useChildSubEnv({
        subChannelMenu: {
            open: () => action.open(),
        },
    });
}
```

**设置功能**:
- **弹出框配置**: 非聊天窗口时使用弹出框
- **固定定位**: 弹出框使用固定定位
- **环境注入**: 注入子频道菜单环境
- **关闭回调**: 设置弹出框关闭回调

### 3. 打开操作

```javascript
open: (component, action) => {
    const thread = component.thread?.parent_channel_id || component.thread;
    action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), { thread });
}
```

**打开功能**:
- **线程选择**: 优先使用父频道，否则使用当前线程
- **元素定位**: 查找操作按钮元素
- **弹出框打开**: 在按钮位置打开弹出框
- **参数传递**: 传递线程参数给子频道列表

### 4. 序列控制

```javascript
sequence: (comp) => (comp.props.chatWindow ? 40 : 5),
sequenceGroup: 10,
```

**序列功能**:
- **动态序列**: 聊天窗口中序列为40，否则为5
- **分组序列**: 属于序列组10
- **显示顺序**: 控制操作在工具栏中的显示位置

## 使用场景

### 1. 线程操作增强

```javascript
// 线程操作增强功能
const ThreadActionsEnhancer = {
    enhanceThreadActions: () => {
        // 添加更多子频道相关操作
        threadActionsRegistry.add("create-thread", {
            close(component, action) {
                action.dialog?.close();
            },
            condition: (component) =>
                component.thread?.hasSubChannelFeature &&
                component.store.self.isInternalUser,
            icon: "fa fa-fw fa-plus",
            iconLarge: "fa fa-fw fa-lg fa-plus",
            name: _t("Create Thread"),
            setup(action) {
                const component = useComponent();
                action.dialog = useDialog(CreateThreadDialog, {
                    onClose: () => action.close(),
                });
            },
            open: (component, action) => {
                action.dialog?.open({ 
                    parentThread: component.thread 
                });
            },
            sequence: 6,
            sequenceGroup: 10,
        });
        
        // 添加线程搜索操作
        threadActionsRegistry.add("search-threads", {
            close(component, action) {
                action.popover?.close();
            },
            condition: (component) =>
                component.thread?.hasSubChannelFeature &&
                component.thread.sub_channel_ids?.length > 5,
            icon: "fa fa-fw fa-search",
            iconLarge: "fa fa-fw fa-lg fa-search",
            name: _t("Search Threads"),
            component: ThreadSearchPanel,
            setup(action) {
                const component = useComponent();
                action.popover = usePopover(ThreadSearchPanel, {
                    onClose: () => action.close(),
                    fixedPosition: true,
                });
            },
            open: (component, action) => {
                action.popover?.open(
                    component.root.el.querySelector(`[name="${action.id}"]`), 
                    { thread: component.thread }
                );
            },
            sequence: 7,
            sequenceGroup: 10,
        });
        
        // 添加线程管理操作
        threadActionsRegistry.add("manage-threads", {
            close(component, action) {
                action.dialog?.close();
            },
            condition: (component) =>
                component.thread?.hasSubChannelFeature &&
                component.thread.canManageSubChannels,
            icon: "fa fa-fw fa-cog",
            iconLarge: "fa fa-fw fa-lg fa-cog",
            name: _t("Manage Threads"),
            setup(action) {
                const component = useComponent();
                action.dialog = useDialog(ThreadManagementDialog, {
                    onClose: () => action.close(),
                    size: 'lg'
                });
            },
            open: (component, action) => {
                action.dialog?.open({ 
                    thread: component.thread 
                });
            },
            sequence: 8,
            sequenceGroup: 10,
        });
        
        // 添加线程统计操作
        threadActionsRegistry.add("thread-stats", {
            close(component, action) {
                action.popover?.close();
            },
            condition: (component) =>
                component.thread?.hasSubChannelFeature &&
                component.store.self.isInternalUser,
            icon: "fa fa-fw fa-bar-chart",
            iconLarge: "fa fa-fw fa-lg fa-bar-chart",
            name: _t("Thread Statistics"),
            component: ThreadStatsPanel,
            setup(action) {
                const component = useComponent();
                action.popover = usePopover(ThreadStatsPanel, {
                    onClose: () => action.close(),
                    fixedPosition: true,
                });
            },
            open: (component, action) => {
                action.popover?.open(
                    component.root.el.querySelector(`[name="${action.id}"]`), 
                    { thread: component.thread }
                );
            },
            sequence: 9,
            sequenceGroup: 10,
        });
        
        // 增强原有的show-threads操作
        const originalShowThreads = threadActionsRegistry.get("show-threads");
        
        threadActionsRegistry.add("show-threads", {
            ...originalShowThreads,
            
            // 增强的设置方法
            setup(action) {
                const component = useComponent();
                
                // 原有设置
                if (!component.props.chatWindow) {
                    action.popover = usePopover(SubChannelList, {
                        onClose: () => action.close(),
                        fixedPosition: true,
                        position: 'bottom-start'
                    });
                }
                
                // 增强的环境设置
                useChildSubEnv({
                    subChannelMenu: {
                        open: () => action.open(),
                        close: () => action.close(),
                        toggle: () => action.toggle(),
                        isOpen: () => action.isOpen(),
                        refresh: () => action.refresh()
                    },
                    threadActions: {
                        createThread: () => this.openCreateDialog(component),
                        searchThreads: () => this.openSearchPanel(component),
                        manageThreads: () => this.openManageDialog(component)
                    }
                });
                
                // 设置快捷键
                this.setupKeyboardShortcuts(action, component);
            },
            
            // 增强的打开方法
            open: (component, action) => {
                const thread = component.thread?.parent_channel_id || component.thread;
                
                // 记录操作
                this.recordThreadAction('show-threads', thread.id);
                
                // 预加载子频道数据
                this.preloadSubChannels(thread);
                
                // 打开弹出框
                if (action.popover) {
                    const targetElement = component.root.el.querySelector(`[name="${action.id}"]`);
                    action.popover.open(targetElement, { 
                        thread,
                        enhanced: true,
                        onThreadClick: (subThread) => this.handleThreadClick(subThread, component),
                        onThreadCreate: (name) => this.handleThreadCreate(name, thread),
                        onThreadSearch: (term) => this.handleThreadSearch(term, thread)
                    });
                }
            },
            
            // 增强的关闭方法
            close(component, action) {
                // 记录关闭操作
                this.recordThreadAction('close-threads', component.thread?.id);
                
                // 原有关闭逻辑
                action.popover?.close();
            },
            
            // 添加刷新方法
            refresh: (component, action) => {
                const thread = component.thread?.parent_channel_id || component.thread;
                
                if (thread && thread.loadMoreSubChannels) {
                    thread.loadMoreSubChannels({ refresh: true });
                }
            },
            
            // 添加切换方法
            toggle: (component, action) => {
                if (action.isOpen && action.isOpen()) {
                    action.close(component, action);
                } else {
                    action.open(component, action);
                }
            },
            
            // 添加状态检查方法
            isOpen: (component, action) => {
                return action.popover?.isOpen || false;
            }
        }, { force: true });
    },
    
    // 设置键盘快捷键
    setupKeyboardShortcuts(action, component) {
        const keyboardHandler = (event) => {
            // Ctrl+T 打开线程列表
            if (event.ctrlKey && event.key === 't') {
                event.preventDefault();
                action.open(component, action);
            }
            
            // Escape 关闭线程列表
            if (event.key === 'Escape' && action.isOpen && action.isOpen()) {
                action.close(component, action);
            }
        };
        
        document.addEventListener('keydown', keyboardHandler);
        
        onWillUnmount(() => {
            document.removeEventListener('keydown', keyboardHandler);
        });
    },
    
    // 预加载子频道数据
    async preloadSubChannels(thread) {
        try {
            if (thread && thread.loadMoreSubChannels) {
                await thread.loadMoreSubChannels({ preload: true });
            }
        } catch (error) {
            console.warn('预加载子频道失败:', error);
        }
    },
    
    // 处理线程点击
    handleThreadClick(subThread, component) {
        // 记录点击事件
        this.recordThreadAction('thread-click', subThread.id);
        
        // 更新最近访问
        this.updateRecentThreads(subThread);
        
        // 原有点击逻辑会在SubChannelList中处理
    },
    
    // 处理线程创建
    async handleThreadCreate(name, parentThread) {
        try {
            const newThread = await parentThread.createSubChannel({ name });
            
            // 记录创建事件
            this.recordThreadAction('thread-create', newThread.id);
            
            return newThread;
        } catch (error) {
            console.error('创建线程失败:', error);
            throw error;
        }
    },
    
    // 处理线程搜索
    handleThreadSearch(term, thread) {
        // 记录搜索事件
        this.recordThreadAction('thread-search', thread.id, { searchTerm: term });
        
        // 搜索逻辑会在SubChannelList中处理
    },
    
    // 打开创建对话框
    openCreateDialog(component) {
        const createAction = threadActionsRegistry.get("create-thread");
        if (createAction) {
            createAction.open(component, createAction);
        }
    },
    
    // 打开搜索面板
    openSearchPanel(component) {
        const searchAction = threadActionsRegistry.get("search-threads");
        if (searchAction) {
            searchAction.open(component, searchAction);
        }
    },
    
    // 打开管理对话框
    openManageDialog(component) {
        const manageAction = threadActionsRegistry.get("manage-threads");
        if (manageAction) {
            manageAction.open(component, manageAction);
        }
    },
    
    // 记录线程操作
    recordThreadAction(actionType, threadId, data = {}) {
        try {
            const actions = JSON.parse(
                localStorage.getItem('thread_actions_log') || '[]'
            );
            
            actions.push({
                actionType,
                threadId,
                data,
                timestamp: Date.now()
            });
            
            // 保留最近100个操作
            if (actions.length > 100) {
                actions.splice(0, actions.length - 100);
            }
            
            localStorage.setItem('thread_actions_log', JSON.stringify(actions));
        } catch (error) {
            console.warn('记录线程操作失败:', error);
        }
    },
    
    // 更新最近线程
    updateRecentThreads(thread) {
        try {
            const recent = JSON.parse(
                localStorage.getItem('recent_threads') || '[]'
            );
            
            // 移除已存在的记录
            const existingIndex = recent.findIndex(item => item.threadId === thread.id);
            if (existingIndex !== -1) {
                recent.splice(existingIndex, 1);
            }
            
            // 添加到开头
            recent.unshift({
                threadId: thread.id,
                threadName: thread.name,
                timestamp: Date.now()
            });
            
            // 保留最近20个
            if (recent.length > 20) {
                recent.splice(20);
            }
            
            localStorage.setItem('recent_threads', JSON.stringify(recent));
        } catch (error) {
            console.warn('更新最近线程失败:', error);
        }
    },
    
    // 获取操作统计
    getActionStatistics() {
        try {
            const actions = JSON.parse(
                localStorage.getItem('thread_actions_log') || '[]'
            );
            
            const stats = {
                totalActions: actions.length,
                actionTypes: {},
                recentActions: actions.slice(-10)
            };
            
            actions.forEach(action => {
                stats.actionTypes[action.actionType] = (stats.actionTypes[action.actionType] || 0) + 1;
            });
            
            return stats;
        } catch (error) {
            return {
                totalActions: 0,
                actionTypes: {},
                recentActions: []
            };
        }
    }
};

// 应用线程操作增强
ThreadActionsEnhancer.enhanceThreadActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作配置
- 条件化显示控制

### 2. 弹出框集成
- 弹出框钩子使用
- 固定定位支持
- 自动关闭处理

### 3. 环境配置
- 子环境注入
- 菜单环境设置
- 组件间通信

### 4. 动态排序
- 上下文感知排序
- 分组序列管理
- 灵活的显示控制

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态操作配置

### 2. 工厂模式 (Factory Pattern)
- 弹出框的创建和管理
- 组件的动态实例化

### 3. 策略模式 (Strategy Pattern)
- 不同环境下的显示策略
- 可配置的行为模式

## 注意事项

1. **条件检查**: 确保操作显示条件的正确性
2. **内存管理**: 及时清理弹出框和事件监听器
3. **用户体验**: 提供流畅的操作交互
4. **性能优化**: 避免不必要的组件创建

## 扩展建议

1. **更多操作**: 添加更多线程相关操作
2. **快捷键**: 实现键盘快捷键支持
3. **批量操作**: 支持批量线程操作
4. **自定义操作**: 允许用户自定义操作
5. **操作历史**: 记录和管理操作历史

该文件为讨论应用的线程提供了重要的子频道浏览功能，是用户访问和管理子频道的主要入口。

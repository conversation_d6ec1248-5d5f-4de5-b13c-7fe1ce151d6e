# Thread Patch - 线程补丁

## 概述

`thread_patch.js` 实现了对 Odoo 讨论应用公共Web环境中线程组件的补丁扩展，专门添加了来源消息的显示功能。该补丁通过Odoo的补丁机制扩展了Thread组件，重写了orderedMessages属性，在消息列表中包含创建子频道的原始消息，根据排序方式将来源消息放置在适当位置，为用户提供了完整的消息上下文，是子频道功能中重要的UI增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/thread_patch.js`
- **行数**: 26
- **模块**: `@mail/discuss/core/public_web/thread_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread'  // 线程组件
'@web/core/utils/patch'     // 补丁工具
```

## 补丁定义

### Thread 补丁

```javascript
patch(Thread.prototype, {
    get orderedMessages() {
        const result = super.orderedMessages;
        if (this.props.thread.from_message_id && !this.props.thread.from_message_id.isEmpty) {
            if (this.props.order === "asc") {
                result.unshift(this.props.thread.from_message_id);
            } else {
                result.push(this.props.thread.from_message_id);
            }
        }
        return result;
    },
});
```

**补丁特性**:
- 扩展线程组件功能
- 重写消息排序逻辑
- 添加来源消息显示
- 支持不同排序方式

## 核心功能

### 1. 有序消息获取

```javascript
get orderedMessages() {
    const result = super.orderedMessages;
    if (this.props.thread.from_message_id && !this.props.thread.from_message_id.isEmpty) {
        if (this.props.order === "asc") {
            result.unshift(this.props.thread.from_message_id);
        } else {
            result.push(this.props.thread.from_message_id);
        }
    }
    return result;
}
```

**消息排序功能**:
- **父类调用**: 获取原有的有序消息列表
- **来源消息检查**: 检查是否存在来源消息且不为空
- **升序排列**: 升序时将来源消息放在开头
- **降序排列**: 降序时将来源消息放在末尾
- **结果返回**: 返回包含来源消息的完整列表

## 使用场景

### 1. 线程组件增强

```javascript
// 线程组件增强功能
const ThreadPatchEnhancer = {
    enhanceThreadPatch: () => {
        const EnhancedThreadPatch = {
            // 增强的有序消息获取
            get orderedMessages() {
                const result = super.orderedMessages;
                const thread = this.props.thread;
                
                // 处理来源消息
                if (thread.from_message_id && !thread.from_message_id.isEmpty) {
                    const fromMessage = thread.from_message_id;
                    
                    // 检查消息是否已在列表中
                    const existingIndex = result.findIndex(msg => msg.id === fromMessage.id);
                    if (existingIndex !== -1) {
                        // 如果已存在，先移除
                        result.splice(existingIndex, 1);
                    }
                    
                    // 根据排序方式添加来源消息
                    if (this.props.order === "asc") {
                        result.unshift(fromMessage);
                    } else {
                        result.push(fromMessage);
                    }
                    
                    // 标记来源消息
                    fromMessage.isOriginMessage = true;
                }
                
                // 处理相关消息
                if (thread.related_message_ids && thread.related_message_ids.length > 0) {
                    const relatedMessages = thread.related_message_ids.filter(msg => !msg.isEmpty);
                    
                    relatedMessages.forEach(relatedMsg => {
                        // 检查是否已在列表中
                        const existingIndex = result.findIndex(msg => msg.id === relatedMsg.id);
                        if (existingIndex === -1) {
                            // 标记相关消息
                            relatedMsg.isRelatedMessage = true;
                            
                            // 根据时间戳插入到合适位置
                            this.insertMessageByTimestamp(result, relatedMsg);
                        }
                    });
                }
                
                // 处理引用消息
                if (thread.referenced_message_ids && thread.referenced_message_ids.length > 0) {
                    const referencedMessages = thread.referenced_message_ids.filter(msg => !msg.isEmpty);
                    
                    referencedMessages.forEach(refMsg => {
                        // 检查是否已在列表中
                        const existingIndex = result.findIndex(msg => msg.id === refMsg.id);
                        if (existingIndex === -1) {
                            // 标记引用消息
                            refMsg.isReferencedMessage = true;
                            
                            // 插入到合适位置
                            this.insertMessageByTimestamp(result, refMsg);
                        }
                    });
                }
                
                // 应用消息过滤器
                return this.applyMessageFilters(result);
            },
            
            // 按时间戳插入消息
            insertMessageByTimestamp(messageList, newMessage) {
                const newTimestamp = new Date(newMessage.datetime).getTime();
                const isAscending = this.props.order === "asc";
                
                let insertIndex = messageList.length;
                
                for (let i = 0; i < messageList.length; i++) {
                    const currentTimestamp = new Date(messageList[i].datetime).getTime();
                    
                    if (isAscending) {
                        if (newTimestamp < currentTimestamp) {
                            insertIndex = i;
                            break;
                        }
                    } else {
                        if (newTimestamp > currentTimestamp) {
                            insertIndex = i;
                            break;
                        }
                    }
                }
                
                messageList.splice(insertIndex, 0, newMessage);
            },
            
            // 应用消息过滤器
            applyMessageFilters(messages) {
                const filters = this.getActiveMessageFilters();
                
                return messages.filter(message => {
                    return filters.every(filter => filter(message));
                });
            },
            
            // 获取活跃的消息过滤器
            getActiveMessageFilters() {
                const filters = [];
                
                // 基础过滤器 - 过滤空消息
                filters.push(message => !message.isEmpty);
                
                // 权限过滤器 - 检查消息访问权限
                filters.push(message => this.canAccessMessage(message));
                
                // 时间过滤器 - 根据用户设置过滤时间范围
                const timeFilter = this.getTimeRangeFilter();
                if (timeFilter) {
                    filters.push(timeFilter);
                }
                
                // 类型过滤器 - 根据消息类型过滤
                const typeFilter = this.getMessageTypeFilter();
                if (typeFilter) {
                    filters.push(typeFilter);
                }
                
                // 用户过滤器 - 根据作者过滤
                const authorFilter = this.getAuthorFilter();
                if (authorFilter) {
                    filters.push(authorFilter);
                }
                
                return filters;
            },
            
            // 检查消息访问权限
            canAccessMessage(message) {
                // 检查消息是否被删除
                if (message.isDeleted) {
                    return false;
                }
                
                // 检查用户权限
                const user = this.store.self;
                if (!user) {
                    return false;
                }
                
                // 检查私有消息权限
                if (message.isPrivate && message.author?.id !== user.id) {
                    return this.hasPrivateMessageAccess(message);
                }
                
                return true;
            },
            
            // 检查私有消息访问权限
            hasPrivateMessageAccess(message) {
                const user = this.store.self;
                
                // 管理员可以访问所有私有消息
                if (user.isAdmin) {
                    return true;
                }
                
                // 检查是否为消息的接收者
                if (message.recipients && message.recipients.includes(user.id)) {
                    return true;
                }
                
                // 检查频道权限
                const thread = this.props.thread;
                if (thread && thread.channelMembers) {
                    const member = thread.channelMembers.find(m => m.persona.id === user.id);
                    return member && (member.role === 'admin' || member.role === 'moderator');
                }
                
                return false;
            },
            
            // 获取时间范围过滤器
            getTimeRangeFilter() {
                const timeRange = this.getUserTimeRangePreference();
                
                if (!timeRange) {
                    return null;
                }
                
                const { startTime, endTime } = timeRange;
                
                return (message) => {
                    const messageTime = new Date(message.datetime).getTime();
                    return messageTime >= startTime && messageTime <= endTime;
                };
            },
            
            // 获取消息类型过滤器
            getMessageTypeFilter() {
                const allowedTypes = this.getUserMessageTypePreference();
                
                if (!allowedTypes || allowedTypes.length === 0) {
                    return null;
                }
                
                return (message) => {
                    return allowedTypes.includes(message.message_type || 'comment');
                };
            },
            
            // 获取作者过滤器
            getAuthorFilter() {
                const allowedAuthors = this.getUserAuthorPreference();
                
                if (!allowedAuthors || allowedAuthors.length === 0) {
                    return null;
                }
                
                return (message) => {
                    return allowedAuthors.includes(message.author?.id);
                };
            },
            
            // 获取用户时间范围偏好
            getUserTimeRangePreference() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('thread_message_time_filter') || 'null'
                    );
                    
                    if (prefs && prefs.enabled) {
                        return {
                            startTime: new Date(prefs.startDate).getTime(),
                            endTime: new Date(prefs.endDate).getTime()
                        };
                    }
                } catch (error) {
                    console.warn('获取时间范围偏好失败:', error);
                }
                
                return null;
            },
            
            // 获取用户消息类型偏好
            getUserMessageTypePreference() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('thread_message_type_filter') || '[]'
                    );
                    
                    return prefs.length > 0 ? prefs : null;
                } catch (error) {
                    console.warn('获取消息类型偏好失败:', error);
                    return null;
                }
            },
            
            // 获取用户作者偏好
            getUserAuthorPreference() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('thread_message_author_filter') || '[]'
                    );
                    
                    return prefs.length > 0 ? prefs : null;
                } catch (error) {
                    console.warn('获取作者偏好失败:', error);
                    return null;
                }
            },
            
            // 获取消息上下文信息
            getMessageContext(message) {
                const context = {
                    isOriginMessage: message.isOriginMessage || false,
                    isRelatedMessage: message.isRelatedMessage || false,
                    isReferencedMessage: message.isReferencedMessage || false,
                    position: this.getMessagePosition(message),
                    threadInfo: this.getThreadInfo(),
                    userInfo: this.getUserInfo()
                };
                
                return context;
            },
            
            // 获取消息位置信息
            getMessagePosition(message) {
                const messages = this.orderedMessages;
                const index = messages.findIndex(msg => msg.id === message.id);
                
                return {
                    index,
                    total: messages.length,
                    isFirst: index === 0,
                    isLast: index === messages.length - 1,
                    percentage: messages.length > 0 ? (index / messages.length) * 100 : 0
                };
            },
            
            // 获取线程信息
            getThreadInfo() {
                const thread = this.props.thread;
                
                return {
                    id: thread.id,
                    name: thread.name || thread.displayName,
                    type: thread.channel_type || thread.model,
                    isSubChannel: !!thread.parent_channel_id,
                    hasSubChannels: thread.sub_channel_ids?.length > 0,
                    memberCount: thread.channelMembers?.length || 0,
                    messageCount: thread.message_ids?.length || 0
                };
            },
            
            // 获取用户信息
            getUserInfo() {
                const user = this.store.self;
                
                return {
                    id: user?.id,
                    name: user?.name,
                    isInternal: user?.isInternalUser || false,
                    isAdmin: user?.isAdmin || false,
                    timezone: user?.tz || 'UTC'
                };
            },
            
            // 记录消息查看
            recordMessageView(message) {
                try {
                    const views = JSON.parse(
                        localStorage.getItem('message_views') || '[]'
                    );
                    
                    views.push({
                        messageId: message.id,
                        threadId: this.props.thread.id,
                        timestamp: Date.now(),
                        context: this.getMessageContext(message)
                    });
                    
                    // 保留最近100个查看记录
                    if (views.length > 100) {
                        views.splice(0, views.length - 100);
                    }
                    
                    localStorage.setItem('message_views', JSON.stringify(views));
                } catch (error) {
                    console.warn('记录消息查看失败:', error);
                }
            },
            
            // 获取消息统计
            getMessageStatistics() {
                const messages = this.orderedMessages;
                
                const stats = {
                    total: messages.length,
                    originMessages: messages.filter(m => m.isOriginMessage).length,
                    relatedMessages: messages.filter(m => m.isRelatedMessage).length,
                    referencedMessages: messages.filter(m => m.isReferencedMessage).length,
                    regularMessages: messages.filter(m => 
                        !m.isOriginMessage && !m.isRelatedMessage && !m.isReferencedMessage
                    ).length,
                    authors: [...new Set(messages.map(m => m.author?.id).filter(Boolean))].length,
                    timeSpan: this.calculateTimeSpan(messages),
                    averageLength: this.calculateAverageMessageLength(messages)
                };
                
                return stats;
            },
            
            // 计算时间跨度
            calculateTimeSpan(messages) {
                if (messages.length === 0) {
                    return 0;
                }
                
                const timestamps = messages
                    .map(m => new Date(m.datetime).getTime())
                    .sort((a, b) => a - b);
                
                return timestamps[timestamps.length - 1] - timestamps[0];
            },
            
            // 计算平均消息长度
            calculateAverageMessageLength(messages) {
                if (messages.length === 0) {
                    return 0;
                }
                
                const totalLength = messages.reduce((sum, message) => {
                    const content = message.body || message.plainTextBody || '';
                    return sum + content.length;
                }, 0);
                
                return Math.round(totalLength / messages.length);
            }
        };
        
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程补丁增强
ThreadPatchEnhancer.enhanceThreadPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 消息排序
- 智能消息排序逻辑
- 来源消息特殊处理
- 多种排序方式支持

### 3. 上下文保持
- 完整的消息上下文
- 来源消息显示
- 用户体验优化

### 4. 扩展性设计
- 易于添加新的消息类型
- 灵活的排序策略
- 可配置的显示逻辑

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 模板方法模式 (Template Method Pattern)
- 消息排序的模板方法
- 可扩展的排序逻辑

### 3. 策略模式 (Strategy Pattern)
- 不同的排序策略
- 可配置的显示策略

## 注意事项

1. **性能优化**: 避免频繁的消息排序影响性能
2. **数据一致性**: 确保消息顺序的正确性
3. **用户体验**: 提供清晰的消息上下文
4. **内存管理**: 避免消息列表过大导致内存问题

## 扩展建议

1. **更多消息类型**: 支持更多类型的特殊消息
2. **高级排序**: 实现更复杂的消息排序算法
3. **消息分组**: 支持消息的分组显示
4. **虚拟滚动**: 实现大量消息的虚拟滚动
5. **消息搜索**: 添加消息内容搜索功能

该补丁为讨论应用的线程组件提供了重要的来源消息显示功能，确保用户能够看到完整的消息上下文。

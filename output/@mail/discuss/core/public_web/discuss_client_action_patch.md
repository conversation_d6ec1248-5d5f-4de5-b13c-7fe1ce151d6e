# Discuss Client Action Patch - 讨论客户端操作补丁

## 概述

`discuss_client_action_patch.js` 实现了对 Odoo 讨论应用客户端操作的补丁扩展，专门为公共Web环境添加了频道获取和ID解析功能。该补丁通过Odoo的补丁机制扩展了DiscussClientAction，重写了线程恢复方法和活动ID解析方法，添加了频道数据预获取、遗留格式兼容等功能，确保讨论应用在公共Web环境中的正确初始化和数据加载，是讨论应用公共Web集成的重要组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/discuss_client_action_patch.js`
- **行数**: 31
- **模块**: `@mail/discuss/core/public_web/discuss_client_action_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/public_web/discuss_client_action'  // 讨论客户端操作
'@web/core/utils/patch'                        // 补丁工具
```

## 补丁定义

### DiscussClientAction 补丁

```javascript
patch(DiscussClientAction.prototype, {
    async restoreDiscussThread() {
        await this.store.channels.fetch();
        return super.restoreDiscussThread(...arguments);
    },
    
    parseActiveId(rawActiveId) {
        if (typeof rawActiveId === "number") {
            return ["discuss.channel", rawActiveId];
        }
        const [model, id] = super.parseActiveId(rawActiveId);
        if (model === "mail.channel") {
            // legacy format (sent in old emails, shared links, ...)
            return ["discuss.channel", id];
        }
        return [model, id];
    },
});
```

**补丁特性**:
- 扩展客户端操作功能
- 添加频道数据预获取
- 实现遗留格式兼容
- 优化线程恢复流程

## 核心功能

### 1. 线程恢复增强

```javascript
async restoreDiscussThread() {
    await this.store.channels.fetch();
    return super.restoreDiscussThread(...arguments);
}
```

**恢复功能**:
- **数据预获取**: 在恢复线程前先获取频道数据
- **异步处理**: 使用异步方式确保数据完整性
- **父类调用**: 调用原有的线程恢复逻辑
- **参数传递**: 正确传递所有参数

### 2. 活动ID解析

```javascript
parseActiveId(rawActiveId) {
    if (typeof rawActiveId === "number") {
        return ["discuss.channel", rawActiveId];
    }
    const [model, id] = super.parseActiveId(rawActiveId);
    if (model === "mail.channel") {
        // legacy format (sent in old emails, shared links, ...)
        return ["discuss.channel", id];
    }
    return [model, id];
}
```

**解析功能**:
- **数字ID处理**: 直接数字ID默认为讨论频道
- **遗留格式兼容**: 将mail.channel转换为discuss.channel
- **标准解析**: 其他格式使用父类解析
- **向后兼容**: 支持旧邮件和共享链接格式

## 使用场景

### 1. 客户端操作增强

```javascript
// 客户端操作增强功能
const DiscussClientActionEnhancer = {
    enhanceDiscussClientAction: () => {
        const EnhancedDiscussClientActionPatch = {
            // 增强的线程恢复
            async restoreDiscussThread() {
                try {
                    // 记录恢复开始时间
                    const startTime = performance.now();
                    
                    // 显示加载状态
                    this.showLoadingState('正在加载频道数据...');
                    
                    // 预获取必要数据
                    await this.preloadEssentialData();
                    
                    // 获取频道数据
                    await this.store.channels.fetch();
                    
                    // 获取用户偏好
                    await this.loadUserPreferences();
                    
                    // 调用父类方法
                    const result = await super.restoreDiscussThread(...arguments);
                    
                    // 记录恢复完成时间
                    const endTime = performance.now();
                    this.recordPerformanceMetric('thread_restore_time', endTime - startTime);
                    
                    // 隐藏加载状态
                    this.hideLoadingState();
                    
                    // 触发恢复完成事件
                    this.triggerEvent('thread_restored', { 
                        duration: endTime - startTime,
                        result 
                    });
                    
                    return result;
                    
                } catch (error) {
                    console.error('线程恢复失败:', error);
                    this.handleRestoreError(error);
                    throw error;
                }
            },
            
            // 预加载必要数据
            async preloadEssentialData() {
                const preloadTasks = [
                    this.preloadUserData(),
                    this.preloadSystemSettings(),
                    this.preloadRecentChannels(),
                    this.preloadNotificationSettings()
                ];
                
                try {
                    await Promise.all(preloadTasks);
                } catch (error) {
                    console.warn('预加载数据部分失败:', error);
                    // 不阻止主流程，继续执行
                }
            },
            
            // 预加载用户数据
            async preloadUserData() {
                try {
                    if (!this.store.self) {
                        await this.store.loadSelf();
                    }
                } catch (error) {
                    console.warn('预加载用户数据失败:', error);
                }
            },
            
            // 预加载系统设置
            async preloadSystemSettings() {
                try {
                    await this.store.loadSystemSettings();
                } catch (error) {
                    console.warn('预加载系统设置失败:', error);
                }
            },
            
            // 预加载最近频道
            async preloadRecentChannels() {
                try {
                    const recentChannels = this.getRecentChannelsFromCache();
                    if (recentChannels.length > 0) {
                        await this.store.loadChannels(recentChannels);
                    }
                } catch (error) {
                    console.warn('预加载最近频道失败:', error);
                }
            },
            
            // 预加载通知设置
            async preloadNotificationSettings() {
                try {
                    await this.store.loadNotificationSettings();
                } catch (error) {
                    console.warn('预加载通知设置失败:', error);
                }
            },
            
            // 增强的活动ID解析
            parseActiveId(rawActiveId) {
                try {
                    // 记录解析请求
                    this.recordIdParseRequest(rawActiveId);
                    
                    // 处理特殊格式
                    const result = this.parseSpecialFormats(rawActiveId);
                    if (result) {
                        return result;
                    }
                    
                    // 数字ID处理
                    if (typeof rawActiveId === "number") {
                        return ["discuss.channel", rawActiveId];
                    }
                    
                    // 字符串格式处理
                    if (typeof rawActiveId === "string") {
                        const stringResult = this.parseStringId(rawActiveId);
                        if (stringResult) {
                            return stringResult;
                        }
                    }
                    
                    // 使用父类解析
                    const [model, id] = super.parseActiveId(rawActiveId);
                    
                    // 遗留格式兼容
                    if (model === "mail.channel") {
                        return ["discuss.channel", id];
                    }
                    
                    // 其他模型映射
                    const mappedModel = this.mapLegacyModel(model);
                    
                    return [mappedModel, id];
                    
                } catch (error) {
                    console.error('ID解析失败:', rawActiveId, error);
                    // 返回默认值
                    return ["discuss.channel", null];
                }
            },
            
            // 解析特殊格式
            parseSpecialFormats(rawActiveId) {
                // URL格式: /discuss/channel/123
                if (typeof rawActiveId === "string" && rawActiveId.startsWith('/discuss/')) {
                    const match = rawActiveId.match(/\/discuss\/(\w+)\/(\d+)/);
                    if (match) {
                        const [, type, id] = match;
                        return [`discuss.${type}`, parseInt(id)];
                    }
                }
                
                // 哈希格式: #channel_123
                if (typeof rawActiveId === "string" && rawActiveId.startsWith('#')) {
                    const match = rawActiveId.match(/#(\w+)_(\d+)/);
                    if (match) {
                        const [, type, id] = match;
                        return [`discuss.${type}`, parseInt(id)];
                    }
                }
                
                // UUID格式
                if (typeof rawActiveId === "string" && this.isUUID(rawActiveId)) {
                    return ["discuss.channel", rawActiveId];
                }
                
                return null;
            },
            
            // 解析字符串ID
            parseStringId(stringId) {
                // 格式: "model,id"
                if (stringId.includes(',')) {
                    const [model, id] = stringId.split(',');
                    return [model.trim(), parseInt(id.trim()) || id.trim()];
                }
                
                // 格式: "model:id"
                if (stringId.includes(':')) {
                    const [model, id] = stringId.split(':');
                    return [model.trim(), parseInt(id.trim()) || id.trim()];
                }
                
                // 纯数字字符串
                if (/^\d+$/.test(stringId)) {
                    return ["discuss.channel", parseInt(stringId)];
                }
                
                return null;
            },
            
            // 映射遗留模型
            mapLegacyModel(model) {
                const modelMap = {
                    'mail.channel': 'discuss.channel',
                    'mail.thread': 'discuss.thread',
                    'res.partner': 'discuss.partner',
                    'res.users': 'discuss.user'
                };
                
                return modelMap[model] || model;
            },
            
            // 检查是否为UUID
            isUUID(str) {
                const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
                return uuidRegex.test(str);
            },
            
            // 显示加载状态
            showLoadingState(message) {
                if (this.loadingIndicator) {
                    this.loadingIndicator.show(message);
                }
            },
            
            // 隐藏加载状态
            hideLoadingState() {
                if (this.loadingIndicator) {
                    this.loadingIndicator.hide();
                }
            },
            
            // 处理恢复错误
            handleRestoreError(error) {
                // 显示用户友好的错误信息
                const userMessage = this.getUserFriendlyErrorMessage(error);
                
                if (this.env.services.notification) {
                    this.env.services.notification.add(userMessage, {
                        type: 'error',
                        title: '加载失败',
                        sticky: true
                    });
                }
                
                // 记录错误
                this.recordError('thread_restore_error', error);
                
                // 尝试恢复
                this.attemptErrorRecovery(error);
            },
            
            // 获取用户友好的错误信息
            getUserFriendlyErrorMessage(error) {
                if (error.message.includes('network')) {
                    return '网络连接失败，请检查网络连接后重试';
                }
                
                if (error.message.includes('permission')) {
                    return '权限不足，无法访问请求的内容';
                }
                
                if (error.message.includes('not found')) {
                    return '请求的内容不存在或已被删除';
                }
                
                return '加载失败，请刷新页面重试';
            },
            
            // 尝试错误恢复
            async attemptErrorRecovery(error) {
                try {
                    // 清除缓存
                    this.clearCache();
                    
                    // 重新初始化
                    await this.reinitialize();
                    
                } catch (recoveryError) {
                    console.error('错误恢复失败:', recoveryError);
                }
            },
            
            // 清除缓存
            clearCache() {
                try {
                    // 清除本地存储缓存
                    const cacheKeys = [
                        'discuss_recent_channels',
                        'discuss_user_preferences',
                        'discuss_notification_settings'
                    ];
                    
                    cacheKeys.forEach(key => {
                        localStorage.removeItem(key);
                    });
                    
                    // 清除内存缓存
                    if (this.store.clearCache) {
                        this.store.clearCache();
                    }
                } catch (error) {
                    console.warn('清除缓存失败:', error);
                }
            },
            
            // 重新初始化
            async reinitialize() {
                try {
                    // 重新加载基础数据
                    await this.store.loadBasicData();
                    
                    // 重新设置事件监听
                    this.setupEventListeners();
                    
                } catch (error) {
                    console.error('重新初始化失败:', error);
                }
            },
            
            // 加载用户偏好
            async loadUserPreferences() {
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('discuss_user_preferences') || '{}'
                    );
                    
                    // 应用用户偏好
                    this.applyUserPreferences(preferences);
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 应用用户偏好
            applyUserPreferences(preferences) {
                // 应用主题设置
                if (preferences.theme) {
                    this.setTheme(preferences.theme);
                }
                
                // 应用布局设置
                if (preferences.layout) {
                    this.setLayout(preferences.layout);
                }
                
                // 应用通知设置
                if (preferences.notifications) {
                    this.setNotificationSettings(preferences.notifications);
                }
            },
            
            // 获取最近频道缓存
            getRecentChannelsFromCache() {
                try {
                    const recent = JSON.parse(
                        localStorage.getItem('discuss_recent_channels') || '[]'
                    );
                    
                    // 过滤有效的频道ID
                    return recent
                        .filter(channel => channel.id && channel.model)
                        .slice(0, 10); // 最多10个最近频道
                        
                } catch (error) {
                    console.warn('获取最近频道缓存失败:', error);
                    return [];
                }
            },
            
            // 记录性能指标
            recordPerformanceMetric(metric, value) {
                try {
                    const metrics = JSON.parse(
                        localStorage.getItem('discuss_performance_metrics') || '[]'
                    );
                    
                    metrics.push({
                        metric,
                        value,
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent
                    });
                    
                    // 保留最近100个指标
                    if (metrics.length > 100) {
                        metrics.splice(0, metrics.length - 100);
                    }
                    
                    localStorage.setItem('discuss_performance_metrics', JSON.stringify(metrics));
                } catch (error) {
                    console.warn('记录性能指标失败:', error);
                }
            },
            
            // 记录ID解析请求
            recordIdParseRequest(rawActiveId) {
                try {
                    const requests = JSON.parse(
                        localStorage.getItem('discuss_id_parse_requests') || '[]'
                    );
                    
                    requests.push({
                        rawActiveId,
                        type: typeof rawActiveId,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个请求
                    if (requests.length > 50) {
                        requests.splice(0, requests.length - 50);
                    }
                    
                    localStorage.setItem('discuss_id_parse_requests', JSON.stringify(requests));
                } catch (error) {
                    console.warn('记录ID解析请求失败:', error);
                }
            },
            
            // 记录错误
            recordError(type, error) {
                try {
                    const errors = JSON.parse(
                        localStorage.getItem('discuss_error_log') || '[]'
                    );
                    
                    errors.push({
                        type,
                        message: error.message,
                        stack: error.stack,
                        timestamp: Date.now(),
                        url: window.location.href
                    });
                    
                    // 保留最近50个错误
                    if (errors.length > 50) {
                        errors.splice(0, errors.length - 50);
                    }
                    
                    localStorage.setItem('discuss_error_log', JSON.stringify(errors));
                } catch (logError) {
                    console.warn('记录错误失败:', logError);
                }
            },
            
            // 触发事件
            triggerEvent(eventName, data) {
                try {
                    const event = new CustomEvent(`discuss:${eventName}`, {
                        detail: data
                    });
                    
                    document.dispatchEvent(event);
                } catch (error) {
                    console.warn('触发事件失败:', error);
                }
            },
            
            // 获取性能统计
            getPerformanceStatistics() {
                try {
                    const metrics = JSON.parse(
                        localStorage.getItem('discuss_performance_metrics') || '[]'
                    );
                    
                    const stats = {
                        totalMetrics: metrics.length,
                        averageRestoreTime: 0,
                        recentMetrics: metrics.slice(-10)
                    };
                    
                    const restoreTimes = metrics
                        .filter(m => m.metric === 'thread_restore_time')
                        .map(m => m.value);
                    
                    if (restoreTimes.length > 0) {
                        stats.averageRestoreTime = restoreTimes.reduce((a, b) => a + b, 0) / restoreTimes.length;
                    }
                    
                    return stats;
                } catch (error) {
                    return {
                        totalMetrics: 0,
                        averageRestoreTime: 0,
                        recentMetrics: []
                    };
                }
            }
        };
        
        patch(DiscussClientAction.prototype, EnhancedDiscussClientActionPatch);
    }
};

// 应用客户端操作增强
DiscussClientActionEnhancer.enhanceDiscussClientAction();
```

## 技术特点

### 1. 补丁机制
- 非侵入式功能扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 数据预获取
- 异步数据加载
- 性能优化策略
- 错误处理机制

### 3. 格式兼容
- 遗留格式支持
- 多种ID格式解析
- 向后兼容性

### 4. 错误处理
- 完善的错误捕获
- 用户友好的错误信息
- 自动恢复机制

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 适配器模式 (Adapter Pattern)
- 格式转换和适配
- 遗留系统兼容

### 3. 策略模式 (Strategy Pattern)
- 不同ID格式的解析策略
- 可配置的处理逻辑

## 注意事项

1. **数据一致性**: 确保频道数据的完整性和一致性
2. **性能优化**: 避免不必要的数据获取影响性能
3. **错误处理**: 提供完善的错误处理和恢复机制
4. **兼容性**: 保持与旧版本和遗留格式的兼容性

## 扩展建议

1. **缓存策略**: 实现更智能的数据缓存策略
2. **预加载优化**: 优化数据预加载的策略和时机
3. **错误恢复**: 增强错误恢复和重试机制
4. **性能监控**: 添加详细的性能监控和分析
5. **格式扩展**: 支持更多的ID格式和解析规则

该补丁为讨论应用在公共Web环境中的正确运行提供了重要的功能增强，确保了数据加载和格式兼容性。

# Sub Channel List - 子频道列表

## 概述

`sub_channel_list.js` 实现了 Odoo 讨论应用中的子频道列表组件，提供了子频道的展示、搜索、创建和管理功能。该组件支持模糊搜索、懒加载、自动聚焦等特性，集成了通知项和操作面板，为用户提供了完整的子频道浏览和交互界面，是讨论应用中子频道功能的核心UI组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/sub_channel_list.js`
- **行数**: 133
- **模块**: `@mail/discuss/core/public_web/sub_channel_list`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/public_web/notification_item'    // 通知项组件
'@mail/discuss/core/common/action_panel'     // 操作面板组件
'@mail/utils/common/dates'                   // 日期工具
'@mail/utils/common/hooks'                   // 邮件钩子
'@odoo/owl'                                  // OWL 框架
'@web/core/l10n/translation'                 // 国际化
'@web/core/network/rpc'                      // RPC网络服务
'@web/core/utils/hooks'                      // Web核心钩子
'@web/core/utils/search'                     // 搜索工具
```

## 组件定义

### SubChannelList 类

```javascript
/**
 * @typedef {Object} Props
 * @property {import("@mail/core/common/thread_model").Thread} thread
 * @property {function} [close]
 * @extends {Component<Props, Env>}
 */
const SubChannelList = class SubChannelList extends Component {
    static template = "mail.SubChannelList";
    static components = { ActionPanel, NotificationItem };
    static props = ["thread", "close?"];
}
```

**组件特性**:
- 接收线程和关闭回调属性
- 集成操作面板和通知项
- 使用专用模板
- 支持可选的关闭功能

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.store = useService("mail.store");
    this.state = useState({
        loading: false,
        searchTerm: "",
        lastSearchTerm: "",
        searching: false,
        subChannels: this.props.thread.sub_channel_ids,
    });
    this.searchRef = useRef("search");
    this.sequential = useSequential();
    useAutofocus({ refName: "search" });
    useVisible("load-more", (isVisible) => {
        if (isVisible) {
            this.props.thread.loadMoreSubChannels({
                searchTerm: this.state.searching ? this.state.searchTerm : undefined,
            });
        }
    });
}
```

**初始化功能**:
- **状态管理**: 管理加载、搜索等状态
- **引用管理**: 搜索输入框引用
- **自动聚焦**: 搜索框自动聚焦
- **懒加载**: 可见性检测触发加载更多

### 2. 子线程点击处理

```javascript
async onClickSubThread(subThread) {
    if (!subThread.hasSelfAsMember) {
        await rpc("/discuss/channel/join", { channel_id: subThread.id });
    }
    subThread.open();
    if (this.env.inChatWindow) {
        this.props.close?.();
    }
}
```

**点击功能**:
- **成员检查**: 检查是否为频道成员
- **自动加入**: 非成员自动加入频道
- **打开频道**: 打开选中的子频道
- **窗口管理**: 聊天窗口中自动关闭

### 3. 搜索功能

```javascript
async search() {
    if (!this.state.searchTerm) {
        return;
    }
    this.sequential(async () => {
        this.state.searching = true;
        this.state.loading = true;
        try {
            await this.props.thread.loadMoreSubChannels({
                searchTerm: this.state.searchTerm,
            });
            if (this.state.searching) {
                this._refreshSubChannelList();
                this.state.lastSearchTerm = this.state.searchTerm;
            }
        } finally {
            this.state.loading = false;
        }
    });
}
```

**搜索功能**:
- **空值检查**: 搜索词为空时直接返回
- **顺序执行**: 使用sequential确保搜索顺序
- **状态管理**: 管理搜索和加载状态
- **结果刷新**: 搜索完成后刷新列表

### 4. 模糊搜索刷新

```javascript
_refreshSubChannelList() {
    this.state.subChannels = fuzzyLookup(
        this.state.searchTerm ?? "",
        this.props.thread.sub_channel_ids,
        ({ name }) => name
    );
}
```

**刷新功能**:
- **模糊匹配**: 使用fuzzyLookup进行模糊搜索
- **名称匹配**: 基于频道名称进行匹配
- **结果更新**: 更新显示的子频道列表

### 5. 创建子频道

```javascript
async onClickCreate() {
    await this.props.thread.createSubChannel({ name: this.state.searchTerm });
    this._refreshSubChannelList();
    this.props.close?.();
}
```

**创建功能**:
- **名称使用**: 使用搜索词作为频道名称
- **异步创建**: 调用线程的创建子频道方法
- **列表刷新**: 创建后刷新子频道列表
- **自动关闭**: 创建完成后关闭组件

## 使用场景

### 1. 子频道列表增强

```javascript
// 子频道列表增强功能
const SubChannelListEnhancer = {
    enhanceSubChannelList: () => {
        const EnhancedSubChannelList = class extends SubChannelList {
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    sortMode: 'activity', // activity, name, created
                    filterMode: 'all', // all, joined, unjoined, active
                    viewMode: 'list', // list, grid, compact
                    selectedChannels: new Set(),
                    showArchived: false,
                    autoRefresh: true,
                    refreshInterval: 30000 // 30秒
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置自动刷新
                this.setupAutoRefresh();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置批量操作
                this.setupBatchOperations();
                
                // 设置高级搜索
                this.setupAdvancedSearch();
                
                // 设置拖拽排序
                this.setupDragAndDrop();
            },
            
            // 增强的子频道列表获取
            get enhancedSubChannels() {
                let channels = this.state.subChannels;
                
                // 应用过滤器
                channels = this.applyFilters(channels);
                
                // 应用排序
                channels = this.applySorting(channels);
                
                return channels;
            },
            
            // 应用过滤器
            applyFilters(channels) {
                let filtered = [...channels];
                
                // 按加入状态过滤
                switch (this.enhancedState.filterMode) {
                    case 'joined':
                        filtered = filtered.filter(ch => ch.hasSelfAsMember);
                        break;
                    case 'unjoined':
                        filtered = filtered.filter(ch => !ch.hasSelfAsMember);
                        break;
                    case 'active':
                        filtered = filtered.filter(ch => this.isChannelActive(ch));
                        break;
                }
                
                // 归档频道过滤
                if (!this.enhancedState.showArchived) {
                    filtered = filtered.filter(ch => !ch.isArchived);
                }
                
                return filtered;
            },
            
            // 应用排序
            applySorting(channels) {
                switch (this.enhancedState.sortMode) {
                    case 'activity':
                        return channels.sort((a, b) => {
                            const aTime = new Date(a.lastActivity || 0).getTime();
                            const bTime = new Date(b.lastActivity || 0).getTime();
                            return bTime - aTime;
                        });
                    case 'name':
                        return channels.sort((a, b) => 
                            (a.name || '').localeCompare(b.name || '')
                        );
                    case 'created':
                        return channels.sort((a, b) => {
                            const aTime = new Date(a.create_date || 0).getTime();
                            const bTime = new Date(b.create_date || 0).getTime();
                            return bTime - aTime;
                        });
                    default:
                        return channels;
                }
            },
            
            // 检查频道是否活跃
            isChannelActive(channel) {
                if (!channel.lastActivity) {
                    return false;
                }
                
                const lastActivity = new Date(channel.lastActivity);
                const now = new Date();
                const daysDiff = (now - lastActivity) / (1000 * 60 * 60 * 24);
                
                return daysDiff <= 7; // 7天内有活动认为是活跃的
            },
            
            // 增强的搜索功能
            async enhancedSearch() {
                if (!this.state.searchTerm) {
                    return;
                }
                
                this.sequential(async () => {
                    this.state.searching = true;
                    this.state.loading = true;
                    
                    try {
                        // 记录搜索
                        this.recordSearch(this.state.searchTerm);
                        
                        // 执行搜索
                        await this.props.thread.loadMoreSubChannels({
                            searchTerm: this.state.searchTerm,
                            includeArchived: this.enhancedState.showArchived,
                            sortBy: this.enhancedState.sortMode
                        });
                        
                        if (this.state.searching) {
                            this._refreshSubChannelList();
                            this.state.lastSearchTerm = this.state.searchTerm;
                            
                            // 高亮搜索结果
                            this.highlightSearchResults();
                        }
                    } finally {
                        this.state.loading = false;
                    }
                });
            },
            
            // 增强的子频道点击处理
            async onClickSubThread(subThread) {
                try {
                    // 记录点击事件
                    this.recordChannelClick(subThread);
                    
                    // 检查权限
                    if (!this.canAccessChannel(subThread)) {
                        this.showAccessDeniedMessage(subThread);
                        return;
                    }
                    
                    // 原有逻辑
                    if (!subThread.hasSelfAsMember) {
                        await rpc("/discuss/channel/join", { channel_id: subThread.id });
                        
                        // 记录加入事件
                        this.recordChannelJoin(subThread);
                    }
                    
                    subThread.open();
                    
                    if (this.env.inChatWindow) {
                        this.props.close?.();
                    }
                    
                    // 更新最近访问
                    this.updateRecentAccess(subThread);
                    
                } catch (error) {
                    console.error('打开子频道失败:', error);
                    this.showErrorMessage('无法打开频道');
                }
            },
            
            // 批量创建子频道
            async onClickBatchCreate() {
                const names = this.state.searchTerm.split(',').map(name => name.trim()).filter(Boolean);
                
                if (names.length === 0) {
                    return;
                }
                
                try {
                    this.state.loading = true;
                    
                    const createPromises = names.map(name => 
                        this.props.thread.createSubChannel({ name })
                    );
                    
                    await Promise.all(createPromises);
                    
                    this._refreshSubChannelList();
                    this.clearSearch();
                    this.props.close?.();
                    
                    this.showSuccessMessage(`成功创建 ${names.length} 个子频道`);
                    
                } catch (error) {
                    console.error('批量创建子频道失败:', error);
                    this.showErrorMessage('批量创建失败');
                } finally {
                    this.state.loading = false;
                }
            },
            
            // 设置自动刷新
            setupAutoRefresh() {
                if (this.enhancedState.autoRefresh) {
                    this.refreshTimer = setInterval(() => {
                        this.refreshSubChannels();
                    }, this.enhancedState.refreshInterval);
                    
                    onWillUnmount(() => {
                        if (this.refreshTimer) {
                            clearInterval(this.refreshTimer);
                        }
                    });
                }
            },
            
            // 刷新子频道
            async refreshSubChannels() {
                try {
                    await this.props.thread.loadMoreSubChannels({
                        refresh: true
                    });
                    this._refreshSubChannelList();
                } catch (error) {
                    console.warn('自动刷新子频道失败:', error);
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+F 聚焦搜索
                    if (event.ctrlKey && event.key === 'f') {
                        event.preventDefault();
                        this.focusSearch();
                    }
                    
                    // Ctrl+N 创建新频道
                    if (event.ctrlKey && event.key === 'n') {
                        event.preventDefault();
                        this.onClickCreate();
                    }
                    
                    // Ctrl+A 全选
                    if (event.ctrlKey && event.key === 'a') {
                        event.preventDefault();
                        this.selectAllChannels();
                    }
                    
                    // Delete 删除选中
                    if (event.key === 'Delete' && this.enhancedState.selectedChannels.size > 0) {
                        this.deleteSelectedChannels();
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 聚焦搜索
            focusSearch() {
                if (this.searchRef.el) {
                    this.searchRef.el.focus();
                    this.searchRef.el.select();
                }
            },
            
            // 全选频道
            selectAllChannels() {
                this.enhancedSubChannels.forEach(channel => {
                    this.enhancedState.selectedChannels.add(channel.id);
                });
            },
            
            // 删除选中频道
            async deleteSelectedChannels() {
                const ids = Array.from(this.enhancedState.selectedChannels);
                
                if (ids.length === 0) return;
                
                const confirmed = confirm(`确定要删除 ${ids.length} 个子频道吗？`);
                
                if (confirmed) {
                    try {
                        await Promise.all(ids.map(id => 
                            rpc('/discuss/channel/delete', { channel_id: id })
                        ));
                        
                        this.enhancedState.selectedChannels.clear();
                        this._refreshSubChannelList();
                        
                        this.showSuccessMessage(`已删除 ${ids.length} 个子频道`);
                    } catch (error) {
                        console.error('删除子频道失败:', error);
                        this.showErrorMessage('删除失败');
                    }
                }
            },
            
            // 检查频道访问权限
            canAccessChannel(channel) {
                // 检查频道是否公开
                if (channel.public === 'public') {
                    return true;
                }
                
                // 检查是否为成员
                if (channel.hasSelfAsMember) {
                    return true;
                }
                
                // 检查是否有邀请权限
                return this.hasInvitePermission(channel);
            },
            
            // 检查邀请权限
            hasInvitePermission(channel) {
                // 这里可以实现具体的权限检查逻辑
                return this.store.self.isInternalUser;
            },
            
            // 显示访问拒绝消息
            showAccessDeniedMessage(channel) {
                this.env.services.notification.add(
                    `无权访问频道 "${channel.name}"`,
                    { type: 'warning' }
                );
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 显示成功消息
            showSuccessMessage(message) {
                this.env.services.notification.add(message, { type: 'success' });
            },
            
            // 记录搜索
            recordSearch(searchTerm) {
                try {
                    const searches = JSON.parse(
                        localStorage.getItem('sub_channel_searches') || '[]'
                    );
                    
                    searches.push({
                        term: searchTerm,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    });
                    
                    // 保留最近100个搜索
                    if (searches.length > 100) {
                        searches.splice(0, searches.length - 100);
                    }
                    
                    localStorage.setItem('sub_channel_searches', JSON.stringify(searches));
                } catch (error) {
                    console.warn('记录搜索失败:', error);
                }
            },
            
            // 记录频道点击
            recordChannelClick(channel) {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('sub_channel_clicks') || '[]'
                    );
                    
                    clicks.push({
                        channelId: channel.id,
                        channelName: channel.name,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    });
                    
                    // 保留最近100个点击
                    if (clicks.length > 100) {
                        clicks.splice(0, clicks.length - 100);
                    }
                    
                    localStorage.setItem('sub_channel_clicks', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录频道点击失败:', error);
                }
            },
            
            // 记录频道加入
            recordChannelJoin(channel) {
                try {
                    const joins = JSON.parse(
                        localStorage.getItem('sub_channel_joins') || '[]'
                    );
                    
                    joins.push({
                        channelId: channel.id,
                        channelName: channel.name,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    });
                    
                    // 保留最近50个加入记录
                    if (joins.length > 50) {
                        joins.splice(0, joins.length - 50);
                    }
                    
                    localStorage.setItem('sub_channel_joins', JSON.stringify(joins));
                } catch (error) {
                    console.warn('记录频道加入失败:', error);
                }
            },
            
            // 更新最近访问
            updateRecentAccess(channel) {
                try {
                    const recent = JSON.parse(
                        localStorage.getItem('recent_sub_channels') || '[]'
                    );
                    
                    // 移除已存在的记录
                    const existingIndex = recent.findIndex(item => item.channelId === channel.id);
                    if (existingIndex !== -1) {
                        recent.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    recent.unshift({
                        channelId: channel.id,
                        channelName: channel.name,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    });
                    
                    // 保留最近20个
                    if (recent.length > 20) {
                        recent.splice(20);
                    }
                    
                    localStorage.setItem('recent_sub_channels', JSON.stringify(recent));
                } catch (error) {
                    console.warn('更新最近访问失败:', error);
                }
            },
            
            // 高亮搜索结果
            highlightSearchResults() {
                if (!this.state.searchTerm) return;
                
                setTimeout(() => {
                    const elements = this.el.querySelectorAll('.o-mail-SubChannelList-item');
                    elements.forEach(element => {
                        const nameElement = element.querySelector('.o-mail-SubChannelList-name');
                        if (nameElement) {
                            const text = nameElement.textContent;
                            const highlightedText = text.replace(
                                new RegExp(`(${this.state.searchTerm})`, 'gi'),
                                '<mark>$1</mark>'
                            );
                            nameElement.innerHTML = highlightedText;
                        }
                    });
                }, 100);
            },
            
            // 获取使用统计
            getUsageStatistics() {
                try {
                    return {
                        searches: JSON.parse(localStorage.getItem('sub_channel_searches') || '[]').length,
                        clicks: JSON.parse(localStorage.getItem('sub_channel_clicks') || '[]').length,
                        joins: JSON.parse(localStorage.getItem('sub_channel_joins') || '[]').length,
                        recentAccess: JSON.parse(localStorage.getItem('recent_sub_channels') || '[]').length
                    };
                } catch (error) {
                    return {
                        searches: 0,
                        clicks: 0,
                        joins: 0,
                        recentAccess: 0
                    };
                }
            }
        };
        
        // 替换原始组件
        __exports.SubChannelList = EnhancedSubChannelList;
    }
};

// 应用子频道列表增强
SubChannelListEnhancer.enhanceSubChannelList();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 属性类型定义
- 子组件集成

### 2. 状态管理
- 响应式状态更新
- 搜索状态跟踪
- 加载状态管理

### 3. 搜索功能
- 模糊搜索支持
- 实时搜索过滤
- 搜索结果缓存

### 4. 用户体验
- 自动聚焦功能
- 懒加载支持
- 键盘交互

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 响应式数据更新

### 3. 策略模式 (Strategy Pattern)
- 不同的搜索策略
- 可配置的排序方式

## 注意事项

1. **性能优化**: 避免频繁的搜索和过滤操作
2. **用户体验**: 提供流畅的交互和及时反馈
3. **数据一致性**: 确保子频道数据的实时性
4. **内存管理**: 及时清理事件监听器和定时器

## 扩展建议

1. **高级搜索**: 实现更复杂的搜索和过滤功能
2. **批量操作**: 支持批量管理子频道
3. **拖拽排序**: 实现子频道的拖拽重排
4. **收藏功能**: 添加子频道收藏功能
5. **统计分析**: 提供子频道使用统计分析

该组件为讨论应用提供了完整的子频道管理界面，是用户浏览和管理子频道的核心工具。

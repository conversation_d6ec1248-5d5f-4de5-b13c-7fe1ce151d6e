# Discuss Core Public Web Service - 讨论核心公共Web服务

## 概述

`discuss_core_public_web_service.js` 实现了 Odoo 讨论应用在公共Web环境中的核心服务，负责管理跨标签页通信、频道邀请通知、侧边栏状态同步等功能。该服务通过BroadcastChannel API实现多标签页间的状态同步，监听总线事件处理频道邀请，提供了讨论应用在Web浏览器环境中的核心功能支持，是讨论应用公共Web集成的重要基础服务。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/discuss_core_public_web_service.js`
- **行数**: 78
- **模块**: `@mail/discuss/core/public_web/discuss_core_public_web_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/browser/browser'    // 浏览器API
'@web/core/l10n/translation'   // 国际化
'@web/core/registry'           // 注册表系统
```

## 服务定义

### DiscussCorePublicWeb 类

```javascript
const DiscussCorePublicWeb = class DiscussCorePublicWeb {
    constructor(env, services) {
        this.env = env;
        this.store = services["mail.store"];
        this.busService = services.bus_service;
        this.notificationService = services.notification;
        
        // BroadcastChannel 设置
        // 总线事件监听
    }
}
```

**类特性**:
- 环境和服务集成
- 跨标签页通信支持
- 事件监听和处理
- 响应式状态管理

## 核心功能

### 1. 跨标签页通信

```javascript
try {
    this.sidebarCategoriesBroadcast = new browser.BroadcastChannel(
        "discuss_core_public_web.sidebar_categories"
    );
    this.sidebarCategoriesBroadcast.addEventListener(
        "message",
        ({ data: { id, open } }) => {
            const category = this.store.DiscussAppCategory.get(id);
            if (category) {
                category.open = open;
            }
        }
    );
} catch {
    // BroadcastChannel API is not supported (e.g. Safari < 15.4), so disabling it.
}
```

**通信功能**:
- **BroadcastChannel**: 使用浏览器原生API进行跨标签页通信
- **状态同步**: 同步侧边栏分类的展开/折叠状态
- **兼容性处理**: 对不支持的浏览器进行优雅降级
- **实时更新**: 实时同步多个标签页的UI状态

### 2. 频道邀请处理

```javascript
this.busService.subscribe("discuss.channel/joined", async (payload) => {
    const { channel, invited_by_user_id: invitedByUserId } = payload;
    const thread = this.store.Thread.insert(channel);
    await thread.fetchChannelInfo();
    if (invitedByUserId && invitedByUserId !== this.store.self.userId) {
        this.notificationService.add(
            _t("You have been invited to #%s", thread.displayName),
            { type: "info" }
        );
    }
});
```

**邀请处理功能**:
- **事件监听**: 监听频道加入事件
- **数据插入**: 将新频道插入存储
- **信息获取**: 异步获取频道详细信息
- **通知显示**: 显示邀请通知给用户

### 3. 分类状态广播

```javascript
broadcastCategoryState(category) {
    this.sidebarCategoriesBroadcast?.postMessage({ id: category.id, open: category.open });
}
```

**广播功能**:
- **状态发送**: 向其他标签页发送分类状态
- **安全调用**: 使用可选链操作符确保安全
- **数据格式**: 发送分类ID和展开状态

## 服务注册

### 服务配置

```javascript
const discussCorePublicWeb = {
    dependencies: ["bus_service", "mail.store", "notification"],
    
    start(env, services) {
        return reactive(new DiscussCorePublicWeb(env, services));
    },
};

registry.category("services").add("discuss.core.public.web", discussCorePublicWeb);
```

**注册特性**:
- **依赖声明**: 明确声明服务依赖
- **响应式包装**: 使用reactive包装服务实例
- **服务注册**: 注册到服务注册表

## 使用场景

### 1. 公共Web服务增强

```javascript
// 公共Web服务增强功能
const DiscussCorePublicWebEnhancer = {
    enhanceDiscussCorePublicWeb: () => {
        const EnhancedDiscussCorePublicWeb = class extends DiscussCorePublicWeb {
            constructor(env, services) {
                super(env, services);
                
                // 增强的广播频道
                this.setupEnhancedBroadcast();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置错误处理
                this.setupErrorHandling();
                
                // 设置用户活动跟踪
                this.setupUserActivityTracking();
                
                // 设置数据同步
                this.setupDataSynchronization();
            },
            
            // 设置增强的广播
            setupEnhancedBroadcast() {
                try {
                    // 侧边栏分类广播
                    this.sidebarCategoriesBroadcast = new browser.BroadcastChannel(
                        "discuss_core_public_web.sidebar_categories"
                    );
                    
                    // 用户状态广播
                    this.userStatusBroadcast = new browser.BroadcastChannel(
                        "discuss_core_public_web.user_status"
                    );
                    
                    // 通知状态广播
                    this.notificationBroadcast = new browser.BroadcastChannel(
                        "discuss_core_public_web.notifications"
                    );
                    
                    // 设置消息监听
                    this.setupBroadcastListeners();
                    
                } catch (error) {
                    console.warn('BroadcastChannel 不支持:', error);
                    this.setupFallbackCommunication();
                }
            },
            
            // 设置广播监听器
            setupBroadcastListeners() {
                // 侧边栏分类监听
                this.sidebarCategoriesBroadcast.addEventListener("message", ({ data }) => {
                    this.handleSidebarCategoryMessage(data);
                });
                
                // 用户状态监听
                this.userStatusBroadcast.addEventListener("message", ({ data }) => {
                    this.handleUserStatusMessage(data);
                });
                
                // 通知状态监听
                this.notificationBroadcast.addEventListener("message", ({ data }) => {
                    this.handleNotificationMessage(data);
                });
            },
            
            // 处理侧边栏分类消息
            handleSidebarCategoryMessage(data) {
                const { id, open, action = 'toggle' } = data;
                
                try {
                    const category = this.store.DiscussAppCategory.get(id);
                    
                    if (category) {
                        switch (action) {
                            case 'toggle':
                                category.open = open;
                                break;
                            case 'expand_all':
                                category.open = true;
                                break;
                            case 'collapse_all':
                                category.open = false;
                                break;
                        }
                        
                        // 记录状态变更
                        this.recordCategoryStateChange(id, category.open, action);
                    }
                } catch (error) {
                    console.error('处理侧边栏分类消息失败:', error);
                }
            },
            
            // 处理用户状态消息
            handleUserStatusMessage(data) {
                const { userId, status, timestamp } = data;
                
                try {
                    // 更新用户状态
                    const user = this.store.Persona.get(userId);
                    if (user) {
                        user.im_status = status;
                        user.last_seen_dt = timestamp;
                    }
                    
                    // 记录状态变更
                    this.recordUserStatusChange(userId, status, timestamp);
                    
                } catch (error) {
                    console.error('处理用户状态消息失败:', error);
                }
            },
            
            // 处理通知消息
            handleNotificationMessage(data) {
                const { type, message, options, action } = data;
                
                try {
                    switch (action) {
                        case 'show':
                            this.notificationService.add(message, { type, ...options });
                            break;
                        case 'clear':
                            this.notificationService.clear();
                            break;
                        case 'update_count':
                            this.updateNotificationCount(data.count);
                            break;
                    }
                } catch (error) {
                    console.error('处理通知消息失败:', error);
                }
            },
            
            // 设置回退通信机制
            setupFallbackCommunication() {
                // 使用 localStorage 作为回退方案
                this.fallbackStorage = {
                    set: (key, value) => {
                        try {
                            localStorage.setItem(`discuss_fallback_${key}`, JSON.stringify({
                                value,
                                timestamp: Date.now()
                            }));
                            
                            // 触发存储事件
                            window.dispatchEvent(new StorageEvent('storage', {
                                key: `discuss_fallback_${key}`,
                                newValue: JSON.stringify({ value, timestamp: Date.now() })
                            }));
                        } catch (error) {
                            console.warn('回退存储失败:', error);
                        }
                    },
                    
                    get: (key) => {
                        try {
                            const data = localStorage.getItem(`discuss_fallback_${key}`);
                            return data ? JSON.parse(data).value : null;
                        } catch (error) {
                            return null;
                        }
                    }
                };
                
                // 监听存储事件
                window.addEventListener('storage', (event) => {
                    if (event.key && event.key.startsWith('discuss_fallback_')) {
                        this.handleFallbackMessage(event);
                    }
                });
            },
            
            // 处理回退消息
            handleFallbackMessage(event) {
                try {
                    const data = JSON.parse(event.newValue);
                    const key = event.key.replace('discuss_fallback_', '');
                    
                    // 根据键名分发消息
                    if (key.startsWith('sidebar_category_')) {
                        this.handleSidebarCategoryMessage(data.value);
                    } else if (key.startsWith('user_status_')) {
                        this.handleUserStatusMessage(data.value);
                    } else if (key.startsWith('notification_')) {
                        this.handleNotificationMessage(data.value);
                    }
                } catch (error) {
                    console.warn('处理回退消息失败:', error);
                }
            },
            
            // 增强的频道邀请处理
            setupEnhancedChannelInviteHandling() {
                // 监听频道加入事件
                this.busService.subscribe("discuss.channel/joined", async (payload) => {
                    await this.handleChannelJoined(payload);
                });
                
                // 监听频道离开事件
                this.busService.subscribe("discuss.channel/left", async (payload) => {
                    await this.handleChannelLeft(payload);
                });
                
                // 监听频道更新事件
                this.busService.subscribe("discuss.channel/updated", async (payload) => {
                    await this.handleChannelUpdated(payload);
                });
            },
            
            // 处理频道加入
            async handleChannelJoined(payload) {
                const { channel, invited_by_user_id: invitedByUserId } = payload;
                
                try {
                    // 插入线程数据
                    const thread = this.store.Thread.insert(channel);
                    
                    // 获取频道信息
                    await thread.fetchChannelInfo();
                    
                    // 记录加入事件
                    this.recordChannelEvent('joined', thread.id, {
                        invitedBy: invitedByUserId,
                        timestamp: Date.now()
                    });
                    
                    // 显示通知
                    if (invitedByUserId && invitedByUserId !== this.store.self.userId) {
                        const inviter = this.store.Persona.get(invitedByUserId);
                        const inviterName = inviter ? inviter.name : '某人';
                        
                        this.notificationService.add(
                            _t("%s 邀请您加入 #%s", inviterName, thread.displayName),
                            { 
                                type: "info",
                                sticky: false,
                                className: 'o-mail-notification-channel-invite'
                            }
                        );
                        
                        // 播放通知声音
                        this.playNotificationSound('invite');
                    }
                    
                    // 广播频道加入事件
                    this.broadcastChannelEvent('joined', {
                        channelId: thread.id,
                        channelName: thread.displayName,
                        invitedBy: invitedByUserId
                    });
                    
                } catch (error) {
                    console.error('处理频道加入失败:', error);
                }
            },
            
            // 处理频道离开
            async handleChannelLeft(payload) {
                const { channel_id: channelId, user_id: userId } = payload;
                
                try {
                    const thread = this.store.Thread.get({ id: channelId, model: 'discuss.channel' });
                    
                    if (thread) {
                        // 记录离开事件
                        this.recordChannelEvent('left', channelId, {
                            userId,
                            timestamp: Date.now()
                        });
                        
                        // 如果是当前用户离开
                        if (userId === this.store.self.userId) {
                            // 从存储中移除
                            thread.delete();
                            
                            // 显示通知
                            this.notificationService.add(
                                _t("您已离开 #%s", thread.displayName),
                                { type: "info" }
                            );
                        } else {
                            // 更新成员列表
                            await thread.fetchChannelInfo();
                        }
                        
                        // 广播频道离开事件
                        this.broadcastChannelEvent('left', {
                            channelId,
                            userId
                        });
                    }
                } catch (error) {
                    console.error('处理频道离开失败:', error);
                }
            },
            
            // 增强的分类状态广播
            broadcastCategoryState(category) {
                const data = {
                    id: category.id,
                    open: category.open,
                    action: 'toggle',
                    timestamp: Date.now()
                };
                
                // 使用 BroadcastChannel
                if (this.sidebarCategoriesBroadcast) {
                    this.sidebarCategoriesBroadcast.postMessage(data);
                } else {
                    // 使用回退机制
                    this.fallbackStorage?.set(`sidebar_category_${category.id}`, data);
                }
                
                // 记录状态变更
                this.recordCategoryStateChange(category.id, category.open, 'toggle');
            },
            
            // 广播用户状态
            broadcastUserStatus(userId, status) {
                const data = {
                    userId,
                    status,
                    timestamp: Date.now()
                };
                
                if (this.userStatusBroadcast) {
                    this.userStatusBroadcast.postMessage(data);
                } else {
                    this.fallbackStorage?.set(`user_status_${userId}`, data);
                }
            },
            
            // 广播通知
            broadcastNotification(type, message, options = {}) {
                const data = {
                    type,
                    message,
                    options,
                    action: 'show',
                    timestamp: Date.now()
                };
                
                if (this.notificationBroadcast) {
                    this.notificationBroadcast.postMessage(data);
                } else {
                    this.fallbackStorage?.set(`notification_${Date.now()}`, data);
                }
            },
            
            // 广播频道事件
            broadcastChannelEvent(eventType, data) {
                const message = {
                    eventType,
                    data,
                    timestamp: Date.now()
                };
                
                // 可以扩展为专门的频道事件广播
                if (this.notificationBroadcast) {
                    this.notificationBroadcast.postMessage({
                        type: 'channel_event',
                        message: JSON.stringify(message),
                        action: 'broadcast'
                    });
                }
            },
            
            // 播放通知声音
            playNotificationSound(type = 'default') {
                try {
                    const soundMap = {
                        'default': '/mail/static/src/sounds/notification.mp3',
                        'invite': '/mail/static/src/sounds/invite.mp3',
                        'message': '/mail/static/src/sounds/message.mp3'
                    };
                    
                    const soundUrl = soundMap[type] || soundMap.default;
                    const audio = new Audio(soundUrl);
                    audio.volume = 0.5;
                    audio.play().catch(error => {
                        console.warn('播放通知声音失败:', error);
                    });
                } catch (error) {
                    console.warn('播放通知声音失败:', error);
                }
            },
            
            // 记录分类状态变更
            recordCategoryStateChange(categoryId, open, action) {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('discuss_category_state_changes') || '[]'
                    );
                    
                    changes.push({
                        categoryId,
                        open,
                        action,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个变更
                    if (changes.length > 100) {
                        changes.splice(0, changes.length - 100);
                    }
                    
                    localStorage.setItem('discuss_category_state_changes', JSON.stringify(changes));
                } catch (error) {
                    console.warn('记录分类状态变更失败:', error);
                }
            },
            
            // 记录用户状态变更
            recordUserStatusChange(userId, status, timestamp) {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('discuss_user_status_changes') || '[]'
                    );
                    
                    changes.push({
                        userId,
                        status,
                        timestamp
                    });
                    
                    // 保留最近100个变更
                    if (changes.length > 100) {
                        changes.splice(0, changes.length - 100);
                    }
                    
                    localStorage.setItem('discuss_user_status_changes', JSON.stringify(changes));
                } catch (error) {
                    console.warn('记录用户状态变更失败:', error);
                }
            },
            
            // 记录频道事件
            recordChannelEvent(eventType, channelId, data) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('discuss_channel_events') || '[]'
                    );
                    
                    events.push({
                        eventType,
                        channelId,
                        data,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('discuss_channel_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录频道事件失败:', error);
                }
            },
            
            // 设置性能监控
            setupPerformanceMonitoring() {
                this.performanceMetrics = {
                    broadcastCount: 0,
                    messageCount: 0,
                    errorCount: 0,
                    startTime: Date.now()
                };
                
                // 定期记录性能指标
                setInterval(() => {
                    this.recordPerformanceMetrics();
                }, 60000); // 每分钟记录一次
            },
            
            // 记录性能指标
            recordPerformanceMetrics() {
                try {
                    const metrics = {
                        ...this.performanceMetrics,
                        uptime: Date.now() - this.performanceMetrics.startTime,
                        timestamp: Date.now()
                    };
                    
                    const history = JSON.parse(
                        localStorage.getItem('discuss_performance_history') || '[]'
                    );
                    
                    history.push(metrics);
                    
                    // 保留最近24个小时的数据
                    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
                    const filtered = history.filter(m => m.timestamp > oneDayAgo);
                    
                    localStorage.setItem('discuss_performance_history', JSON.stringify(filtered));
                } catch (error) {
                    console.warn('记录性能指标失败:', error);
                }
            },
            
            // 获取服务统计
            getServiceStatistics() {
                return {
                    performance: this.performanceMetrics,
                    broadcastSupported: !!this.sidebarCategoriesBroadcast,
                    activeChannels: this.getActiveBroadcastChannels(),
                    recentEvents: this.getRecentEvents()
                };
            },
            
            // 获取活跃的广播频道
            getActiveBroadcastChannels() {
                return {
                    sidebarCategories: !!this.sidebarCategoriesBroadcast,
                    userStatus: !!this.userStatusBroadcast,
                    notifications: !!this.notificationBroadcast
                };
            },
            
            // 获取最近事件
            getRecentEvents() {
                try {
                    return {
                        categoryChanges: JSON.parse(localStorage.getItem('discuss_category_state_changes') || '[]').slice(-10),
                        userStatusChanges: JSON.parse(localStorage.getItem('discuss_user_status_changes') || '[]').slice(-10),
                        channelEvents: JSON.parse(localStorage.getItem('discuss_channel_events') || '[]').slice(-10)
                    };
                } catch (error) {
                    return {
                        categoryChanges: [],
                        userStatusChanges: [],
                        channelEvents: []
                    };
                }
            }
        };
        
        // 更新服务定义
        const enhancedDiscussCorePublicWeb = {
            dependencies: ["bus_service", "mail.store", "notification"],
            
            start(env, services) {
                return reactive(new EnhancedDiscussCorePublicWeb(env, services));
            },
        };
        
        // 重新注册增强的服务
        registry.category("services").add("discuss.core.public.web", enhancedDiscussCorePublicWeb, { force: true });
    }
};

// 应用公共Web服务增强
DiscussCorePublicWebEnhancer.enhanceDiscussCorePublicWeb();
```

## 技术特点

### 1. 跨标签页通信
- BroadcastChannel API使用
- 浏览器兼容性处理
- 实时状态同步

### 2. 事件驱动架构
- 总线事件监听
- 异步事件处理
- 事件广播机制

### 3. 服务集成
- 多服务依赖管理
- 响应式状态包装
- 注册表集成

### 4. 错误处理
- 优雅降级机制
- 异常捕获处理
- 兼容性检查

## 设计模式

### 1. 服务模式 (Service Pattern)
- 服务的注册和管理
- 依赖注入机制

### 2. 观察者模式 (Observer Pattern)
- 事件监听和响应
- 状态变化通知

### 3. 发布订阅模式 (Pub/Sub Pattern)
- 跨标签页消息传递
- 事件的发布和订阅

## 注意事项

1. **浏览器兼容性**: 确保BroadcastChannel API的兼容性处理
2. **性能影响**: 避免频繁的跨标签页通信影响性能
3. **数据一致性**: 确保多标签页间数据状态的一致性
4. **内存管理**: 及时清理事件监听器和广播频道

## 扩展建议

1. **更多广播频道**: 添加更多类型的跨标签页通信
2. **离线支持**: 实现离线状态下的数据同步
3. **性能优化**: 优化跨标签页通信的性能
4. **错误恢复**: 增强错误处理和恢复机制
5. **监控分析**: 添加详细的服务监控和分析

该服务为讨论应用在公共Web环境中提供了重要的基础功能支持，确保了多标签页间的协调和数据同步。

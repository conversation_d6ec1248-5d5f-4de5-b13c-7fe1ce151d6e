# Discuss Sidebar Categories - 讨论侧边栏分类

## 概述

`discuss_sidebar_categories.js` 实现了 Odoo 讨论应用侧边栏的分类管理组件，包含子频道、频道、分类和快速搜索等多个子组件。该文件定义了完整的侧边栏UI结构，支持频道展示、分类折叠展开、快速搜索过滤、悬停交互等功能，提供了讨论应用中频道导航和管理的核心界面，是用户与频道交互的主要入口点。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/discuss_sidebar_categories.js`
- **行数**: 280
- **模块**: `@mail/discuss/core/public_web/discuss_sidebar_categories`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/country_flag'           // 国家旗帜组件
'@mail/core/common/im_status'              // 即时消息状态组件
'@mail/core/common/thread_icon'            // 线程图标组件
'@mail/core/public_web/discuss_sidebar'    // 讨论侧边栏
'@mail/utils/common/format'                // 格式化工具
'@mail/utils/common/hooks'                 // 通用钩子
'@odoo/owl'                                // OWL 框架
'@web/core/confirmation_dialog/confirmation_dialog'  // 确认对话框
'@web/core/dropdown/dropdown'              // 下拉菜单
'@web/core/dropdown/dropdown_hooks'        // 下拉菜单钩子
'@web/core/l10n/translation'               // 国际化
'@web/core/registry'                       // 注册表系统
'@web/core/utils/hooks'                    // Web核心钩子
'@web/core/utils/misc'                     // 杂项工具
```

## 组件结构

### 1. DiscussSidebarSubchannel - 子频道组件

```javascript
const DiscussSidebarSubchannel = class DiscussSidebarSubchannel extends Component {
    static template = "mail.DiscussSidebarSubchannel";
    static props = ["thread", "isFirst?"];
    static components = { Dropdown };
}
```

**子频道特性**:
- 显示子频道信息
- 支持悬停交互
- 提供下拉菜单操作
- 线程打开功能

### 2. DiscussSidebarChannel - 频道组件

```javascript
const DiscussSidebarChannel = class DiscussSidebarChannel extends Component {
    static template = "mail.DiscussSidebarChannel";
    static props = ["thread"];
    static components = { CountryFlag, DiscussSidebarSubchannel, Dropdown, ImStatus, ThreadIcon };
}
```

**频道特性**:
- 完整的频道显示
- 多种状态指示器
- 紧凑模式支持
- 边框和样式控制

### 3. DiscussSidebarCategory - 分类组件

```javascript
const DiscussSidebarCategory = class DiscussSidebarCategory extends Component {
    static template = "mail.DiscussSidebarCategory";
    static props = ["category"];
}
```

**分类特性**:
- 分类展开/折叠
- 状态广播同步
- 分类标题显示
- 交互状态管理

### 4. DiscussSidebarQuickSearchInput - 快速搜索组件

```javascript
const DiscussSidebarQuickSearchInput = class DiscussSidebarQuickSearchInput extends Component {
    static template = "mail.DiscussSidebarQuickSearchInput";
    static props = ["state", "autofocus?"];
}
```

**搜索特性**:
- 实时搜索过滤
- 自动聚焦支持
- 搜索状态管理
- 清理搜索功能

### 5. DiscussSidebarCategories - 主分类容器

```javascript
const DiscussSidebarCategories = class DiscussSidebarCategories extends Component {
    static template = "mail.DiscussSidebarCategories";
    static props = {};
    static components = {
        DiscussSidebarCategory,
        DiscussSidebarChannel,
        DiscussSidebarQuickSearchInput,
        Dropdown,
    };
}
```

**容器特性**:
- 整合所有子组件
- 提供过滤功能
- 管理搜索状态
- 环境上下文设置

## 核心功能

### 1. 频道样式控制

```javascript
get attClass() {
    return {
        "bg-inherit": this.thread.notEq(this.store.discuss.thread),
        "o-active": this.thread.eq(this.store.discuss.thread),
        "o-unread": this.thread.selfMember?.message_unread_counter > 0 && !this.thread.isMuted,
        "border-bottom-0 rounded-bottom-0": this.bordered,
        "opacity-50": this.thread.isMuted,
        "position-relative justify-content-center o-compact mt-0 p-1":
            this.store.discuss.isSidebarCompact,
        "p-0": !this.store.discuss.isSidebarCompact,
    };
}
```

**样式功能**:
- **活跃状态**: 当前选中频道的高亮显示
- **未读状态**: 未读消息的视觉提示
- **静音状态**: 静音频道的透明度处理
- **紧凑模式**: 紧凑布局的样式适配

### 2. 分类状态管理

```javascript
onClickToggle() {
    if (this.store.discuss.isSidebarCompact) {
        return;
    }
    this.category.open = !this.category.open;
    this.discusscorePublicWebService.broadcastCategoryState(this.category);
}
```

**状态管理功能**:
- **展开切换**: 分类的展开和折叠
- **状态广播**: 跨标签页状态同步
- **紧凑模式**: 紧凑模式下的行为控制

### 3. 线程过滤

```javascript
filteredThreads(threads) {
    return threads.filter(
        (thread) =>
            thread.displayInSidebar &&
            (thread.parent_channel_id ||
                !this.state.quickSearchVal ||
                cleanTerm(thread.displayName).includes(cleanTerm(this.state.quickSearchVal)))
    );
}
```

**过滤功能**:
- **显示控制**: 仅显示应在侧边栏显示的线程
- **搜索过滤**: 基于搜索词的名称过滤
- **父频道**: 保留所有子频道
- **术语清理**: 使用cleanTerm进行搜索匹配

### 4. 快速搜索控制

```javascript
get hasQuickSearch() {
    return (
        Object.values(this.store.Thread.records).filter(
            (thread) => thread.is_pinned && thread.model === "discuss.channel"
        ).length > 19
    );
}
```

**搜索控制功能**:
- **条件显示**: 仅在频道数量超过19个时显示
- **固定频道**: 仅计算固定的讨论频道
- **性能优化**: 避免在少量频道时显示搜索

## 注册表系统

### 频道指示器注册表

```javascript
const discussSidebarChannelIndicatorsRegistry = registry.category(
    "mail.discuss_sidebar_channel_indicators"
);
```

**注册表功能**:
- 管理频道指示器组件
- 支持动态添加指示器
- 提供扩展机制

### 侧边栏项目注册

```javascript
discussSidebarItemsRegistry.add("channels", DiscussSidebarCategories, { sequence: 30 });
```

**注册功能**:
- 将分类组件注册到侧边栏
- 设置显示顺序
- 支持动态组件管理

## 使用场景

### 1. 侧边栏分类增强

```javascript
// 侧边栏分类增强功能
const DiscussSidebarCategoriesEnhancer = {
    enhanceDiscussSidebarCategories: () => {
        const EnhancedDiscussSidebarCategories = class extends DiscussSidebarCategories {
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    quickSearchVal: "",
                    floatingQuickSearchOpen: false,
                    selectedCategories: new Set(),
                    sortMode: 'name', // name, activity, unread
                    viewMode: 'list', // list, grid, compact
                    filterMode: 'all' // all, unread, active
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置拖拽排序
                this.setupDragAndDrop();
                
                // 设置右键菜单
                this.setupContextMenu();
                
                // 设置批量操作
                this.setupBatchOperations();
                
                // 设置自定义过滤器
                this.setupCustomFilters();
            },
            
            // 增强的线程过滤
            filteredThreads(threads) {
                let filtered = threads.filter(thread => thread.displayInSidebar);
                
                // 应用搜索过滤
                if (this.enhancedState.quickSearchVal) {
                    const searchTerm = cleanTerm(this.enhancedState.quickSearchVal);
                    filtered = filtered.filter(thread => 
                        thread.parent_channel_id ||
                        cleanTerm(thread.displayName).includes(searchTerm) ||
                        cleanTerm(thread.description || '').includes(searchTerm)
                    );
                }
                
                // 应用过滤模式
                switch (this.enhancedState.filterMode) {
                    case 'unread':
                        filtered = filtered.filter(thread => 
                            thread.selfMember?.message_unread_counter > 0
                        );
                        break;
                    case 'active':
                        filtered = filtered.filter(thread => 
                            thread.lastActivity && 
                            (Date.now() - new Date(thread.lastActivity).getTime()) < 86400000 // 24小时
                        );
                        break;
                }
                
                // 应用排序
                return this.sortThreads(filtered);
            },
            
            // 排序线程
            sortThreads(threads) {
                switch (this.enhancedState.sortMode) {
                    case 'activity':
                        return threads.sort((a, b) => {
                            const aTime = new Date(a.lastActivity || 0).getTime();
                            const bTime = new Date(b.lastActivity || 0).getTime();
                            return bTime - aTime;
                        });
                    case 'unread':
                        return threads.sort((a, b) => {
                            const aUnread = a.selfMember?.message_unread_counter || 0;
                            const bUnread = b.selfMember?.message_unread_counter || 0;
                            return bUnread - aUnread;
                        });
                    case 'name':
                    default:
                        return threads.sort((a, b) => 
                            (a.displayName || '').localeCompare(b.displayName || '')
                        );
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+F 快速搜索
                    if (event.ctrlKey && event.key === 'f') {
                        event.preventDefault();
                        this.focusQuickSearch();
                    }
                    
                    // Ctrl+A 全选分类
                    if (event.ctrlKey && event.key === 'a') {
                        event.preventDefault();
                        this.selectAllCategories();
                    }
                    
                    // Delete 删除选中分类
                    if (event.key === 'Delete' && this.enhancedState.selectedCategories.size > 0) {
                        this.deleteSelectedCategories();
                    }
                    
                    // 数字键快速切换排序模式
                    if (event.key >= '1' && event.key <= '3') {
                        const modes = ['name', 'activity', 'unread'];
                        this.enhancedState.sortMode = modes[parseInt(event.key) - 1];
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 设置拖拽排序
            setupDragAndDrop() {
                this.dragDropHandler = {
                    onDragStart: (event, category) => {
                        event.dataTransfer.setData('text/plain', category.id);
                        event.dataTransfer.effectAllowed = 'move';
                    },
                    
                    onDragOver: (event) => {
                        event.preventDefault();
                        event.dataTransfer.dropEffect = 'move';
                    },
                    
                    onDrop: (event, targetCategory) => {
                        event.preventDefault();
                        const sourceId = event.dataTransfer.getData('text/plain');
                        this.reorderCategories(sourceId, targetCategory.id);
                    }
                };
            },
            
            // 重新排序分类
            async reorderCategories(sourceId, targetId) {
                try {
                    await this.orm.call('discuss.app.category', 'reorder', [sourceId, targetId]);
                    
                    // 更新本地状态
                    await this.store.loadCategories();
                    
                    this.env.services.notification.add('分类顺序已更新', { type: 'success' });
                } catch (error) {
                    console.error('重新排序分类失败:', error);
                    this.env.services.notification.add('重新排序失败', { type: 'error' });
                }
            },
            
            // 设置右键菜单
            setupContextMenu() {
                this.contextMenuItems = [
                    {
                        name: '重命名',
                        icon: 'fa-edit',
                        action: (category) => this.renameCategory(category)
                    },
                    {
                        name: '删除',
                        icon: 'fa-trash',
                        action: (category) => this.deleteCategory(category)
                    },
                    {
                        name: '复制链接',
                        icon: 'fa-link',
                        action: (category) => this.copyLink(category)
                    },
                    {
                        name: '导出',
                        icon: 'fa-download',
                        action: (category) => this.exportCategory(category)
                    }
                ];
            },
            
            // 重命名分类
            async renameCategory(category) {
                const newName = prompt('请输入新名称:', category.name);
                
                if (newName && newName !== category.name) {
                    try {
                        await this.orm.write('discuss.app.category', [category.id], {
                            name: newName
                        });
                        
                        category.name = newName;
                        this.env.services.notification.add('分类已重命名', { type: 'success' });
                    } catch (error) {
                        console.error('重命名分类失败:', error);
                        this.env.services.notification.add('重命名失败', { type: 'error' });
                    }
                }
            },
            
            // 删除分类
            async deleteCategory(category) {
                const confirmed = confirm(`确定要删除分类 "${category.name}" 吗？`);
                
                if (confirmed) {
                    try {
                        await this.orm.unlink('discuss.app.category', [category.id]);
                        
                        // 从存储中移除
                        this.store.DiscussAppCategory.delete(category);
                        
                        this.env.services.notification.add('分类已删除', { type: 'success' });
                    } catch (error) {
                        console.error('删除分类失败:', error);
                        this.env.services.notification.add('删除失败', { type: 'error' });
                    }
                }
            },
            
            // 复制链接
            copyLink(category) {
                const url = `${window.location.origin}/discuss/category/${category.id}`;
                
                navigator.clipboard.writeText(url).then(() => {
                    this.env.services.notification.add('链接已复制', { type: 'success' });
                }).catch(error => {
                    console.error('复制链接失败:', error);
                    this.env.services.notification.add('复制失败', { type: 'error' });
                });
            },
            
            // 导出分类
            async exportCategory(category) {
                try {
                    const data = await this.orm.call('discuss.app.category', 'export_data', [category.id]);
                    
                    const blob = new Blob([JSON.stringify(data, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `category_${category.name}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    
                    this.env.services.notification.add('分类已导出', { type: 'success' });
                } catch (error) {
                    console.error('导出分类失败:', error);
                    this.env.services.notification.add('导出失败', { type: 'error' });
                }
            },
            
            // 设置批量操作
            setupBatchOperations() {
                this.batchOperations = {
                    selectAll: () => {
                        this.store.DiscussAppCategory.records.forEach(category => {
                            this.enhancedState.selectedCategories.add(category.id);
                        });
                    },
                    
                    deselectAll: () => {
                        this.enhancedState.selectedCategories.clear();
                    },
                    
                    deleteSelected: async () => {
                        const ids = Array.from(this.enhancedState.selectedCategories);
                        
                        if (ids.length === 0) return;
                        
                        const confirmed = confirm(`确定要删除 ${ids.length} 个分类吗？`);
                        
                        if (confirmed) {
                            try {
                                await this.orm.unlink('discuss.app.category', ids);
                                
                                // 从存储中移除
                                ids.forEach(id => {
                                    const category = this.store.DiscussAppCategory.get(id);
                                    if (category) {
                                        this.store.DiscussAppCategory.delete(category);
                                    }
                                });
                                
                                this.enhancedState.selectedCategories.clear();
                                this.env.services.notification.add(`已删除 ${ids.length} 个分类`, { type: 'success' });
                            } catch (error) {
                                console.error('批量删除失败:', error);
                                this.env.services.notification.add('批量删除失败', { type: 'error' });
                            }
                        }
                    },
                    
                    exportSelected: async () => {
                        const ids = Array.from(this.enhancedState.selectedCategories);
                        
                        if (ids.length === 0) return;
                        
                        try {
                            const data = await this.orm.call('discuss.app.category', 'export_multiple', [ids]);
                            
                            const blob = new Blob([JSON.stringify(data, null, 2)], {
                                type: 'application/json'
                            });
                            
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `categories_${Date.now()}.json`;
                            a.click();
                            
                            URL.revokeObjectURL(url);
                            
                            this.env.services.notification.add(`已导出 ${ids.length} 个分类`, { type: 'success' });
                        } catch (error) {
                            console.error('批量导出失败:', error);
                            this.env.services.notification.add('批量导出失败', { type: 'error' });
                        }
                    }
                };
            },
            
            // 设置自定义过滤器
            setupCustomFilters() {
                this.customFilters = {
                    favorites: (threads) => threads.filter(thread => thread.is_favorite),
                    recent: (threads) => {
                        const oneDayAgo = Date.now() - 86400000;
                        return threads.filter(thread => 
                            thread.lastActivity && 
                            new Date(thread.lastActivity).getTime() > oneDayAgo
                        );
                    },
                    muted: (threads) => threads.filter(thread => thread.isMuted),
                    archived: (threads) => threads.filter(thread => thread.isArchived)
                };
            },
            
            // 应用自定义过滤器
            applyCustomFilter(filterName) {
                const filter = this.customFilters[filterName];
                if (filter) {
                    this.enhancedState.customFilter = filterName;
                    // 触发重新渲染
                    this.render();
                }
            },
            
            // 聚焦快速搜索
            focusQuickSearch() {
                const searchInput = this.el.querySelector('.o-mail-DiscussSidebarQuickSearchInput input');
                if (searchInput) {
                    searchInput.focus();
                    searchInput.select();
                }
            },
            
            // 全选分类
            selectAllCategories() {
                this.batchOperations.selectAll();
            },
            
            // 删除选中分类
            deleteSelectedCategories() {
                this.batchOperations.deleteSelected();
            },
            
            // 获取增强统计
            getEnhancedStatistics() {
                const threads = Object.values(this.store.Thread.records);
                const categories = Object.values(this.store.DiscussAppCategory.records);
                
                return {
                    totalThreads: threads.length,
                    visibleThreads: this.filteredThreads(threads).length,
                    totalCategories: categories.length,
                    selectedCategories: this.enhancedState.selectedCategories.size,
                    unreadThreads: threads.filter(t => t.selfMember?.message_unread_counter > 0).length,
                    mutedThreads: threads.filter(t => t.isMuted).length,
                    favoriteThreads: threads.filter(t => t.is_favorite).length,
                    currentSortMode: this.enhancedState.sortMode,
                    currentFilterMode: this.enhancedState.filterMode,
                    currentViewMode: this.enhancedState.viewMode
                };
            }
        };
        
        // 替换原始组件
        __exports.DiscussSidebarCategories = EnhancedDiscussSidebarCategories;
        
        // 重新注册增强的组件
        discussSidebarItemsRegistry.add("channels", EnhancedDiscussSidebarCategories, { sequence: 30, force: true });
    }
};

// 应用侧边栏分类增强
DiscussSidebarCategoriesEnhancer.enhanceDiscussSidebarCategories();
```

## 技术特点

### 1. 组件化设计
- 多层次组件结构
- 清晰的职责分离
- 可复用的子组件

### 2. 状态管理
- 响应式状态更新
- 跨组件状态共享
- 持久化状态同步

### 3. 交互体验
- 悬停效果支持
- 下拉菜单集成
- 快速搜索功能

### 4. 性能优化
- 条件渲染控制
- 过滤算法优化
- 懒加载支持

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 组件的层次化组织
- 统一的组件接口

### 2. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 响应式数据更新

### 3. 策略模式 (Strategy Pattern)
- 不同的过滤策略
- 可配置的排序方式

## 注意事项

1. **性能优化**: 避免频繁的过滤和排序操作影响性能
2. **状态同步**: 确保跨标签页状态的正确同步
3. **用户体验**: 提供流畅的交互和视觉反馈
4. **内存管理**: 及时清理事件监听器和状态

## 扩展建议

1. **更多视图模式**: 添加网格视图、卡片视图等
2. **高级搜索**: 实现更复杂的搜索和过滤功能
3. **自定义排序**: 支持用户自定义排序规则
4. **拖拽操作**: 实现频道和分类的拖拽重排
5. **批量操作**: 添加更多的批量管理功能

该组件为讨论应用提供了完整的侧边栏导航功能，是用户与频道交互的核心界面组件。

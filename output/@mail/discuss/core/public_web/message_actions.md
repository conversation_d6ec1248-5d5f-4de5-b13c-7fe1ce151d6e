# Message Actions - 消息操作

## 概述

`message_actions.js` 实现了 Odoo 讨论应用公共Web环境中的消息操作功能，专门添加了创建或查看线程的操作。该文件通过消息操作注册表添加了子频道创建和查看功能，支持条件显示、动态标题、图标配置等特性，为用户提供了在消息基础上创建讨论线程的便捷操作，是讨论应用中消息交互功能的重要扩展。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/message_actions.js`
- **行数**: 30
- **模块**: `@mail/discuss/core/public_web/message_actions`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message_actions'  // 消息操作注册表
'@web/core/l10n/translation'         // 国际化
```

## 操作注册

### 创建或查看线程操作

```javascript
messageActionsRegistry.add("create-or-view-thread", {
    condition: (component) =>
        component.isOriginThread &&
        component.message.thread.hasSubChannelFeature &&
        component.store.self.isInternalUser,
    icon: "fa-comments-o",
    onClick: (component) => {
        if (component.message.linkedSubChannel) {
            component.message.linkedSubChannel.open();
        } else {
            component.message.thread.createSubChannel({ initialMessage: component.message });
        }
    },
    title: (component) =>
        component.message.linkedSubChannel ? _t("View Thread") : _t("Create Thread"),
    sequence: 75,
});
```

**操作特性**:
- **条件显示**: 仅在满足特定条件时显示
- **动态行为**: 根据子频道存在与否执行不同操作
- **国际化支持**: 支持多语言标题
- **图标配置**: 使用FontAwesome图标

## 核心功能

### 1. 显示条件

```javascript
condition: (component) =>
    component.isOriginThread &&
    component.message.thread.hasSubChannelFeature &&
    component.store.self.isInternalUser,
```

**条件检查**:
- **原始线程**: 必须是原始线程中的消息
- **子频道功能**: 线程必须支持子频道功能
- **内部用户**: 仅内部用户可见此操作

### 2. 点击处理

```javascript
onClick: (component) => {
    if (component.message.linkedSubChannel) {
        component.message.linkedSubChannel.open();
    } else {
        component.message.thread.createSubChannel({ initialMessage: component.message });
    }
},
```

**点击逻辑**:
- **已有子频道**: 直接打开已关联的子频道
- **创建子频道**: 以当前消息为初始消息创建新子频道

### 3. 动态标题

```javascript
title: (component) =>
    component.message.linkedSubChannel ? _t("View Thread") : _t("Create Thread"),
```

**标题功能**:
- **查看线程**: 已有子频道时显示"查看线程"
- **创建线程**: 无子频道时显示"创建线程"
- **国际化**: 支持多语言显示

## 使用场景

### 1. 消息操作增强

```javascript
// 消息操作增强功能
const MessageActionsEnhancer = {
    enhanceMessageActions: () => {
        // 添加更多消息操作
        messageActionsRegistry.add("reply-in-thread", {
            condition: (component) =>
                component.isOriginThread &&
                component.message.thread.hasSubChannelFeature &&
                component.store.self.isInternalUser,
            icon: "fa-reply",
            onClick: (component) => {
                if (component.message.linkedSubChannel) {
                    component.message.linkedSubChannel.open();
                    // 聚焦到编辑器
                    setTimeout(() => {
                        const composer = component.message.linkedSubChannel.composer;
                        if (composer) {
                            composer.focus();
                        }
                    }, 100);
                } else {
                    component.message.thread.createSubChannel({ 
                        initialMessage: component.message,
                        autoFocus: true
                    });
                }
            },
            title: () => _t("Reply in Thread"),
            sequence: 76,
        });
        
        // 添加分享线程操作
        messageActionsRegistry.add("share-thread", {
            condition: (component) =>
                component.message.linkedSubChannel &&
                component.store.self.isInternalUser,
            icon: "fa-share",
            onClick: (component) => {
                const url = component.message.linkedSubChannel.getShareUrl();
                navigator.clipboard.writeText(url).then(() => {
                    component.env.services.notification.add(
                        _t("Thread link copied to clipboard"),
                        { type: "success" }
                    );
                });
            },
            title: () => _t("Share Thread"),
            sequence: 77,
        });
        
        // 添加关注线程操作
        messageActionsRegistry.add("follow-thread", {
            condition: (component) =>
                component.message.linkedSubChannel &&
                !component.message.linkedSubChannel.isFollowing &&
                component.store.self.isInternalUser,
            icon: "fa-bell-o",
            onClick: (component) => {
                component.message.linkedSubChannel.follow();
            },
            title: () => _t("Follow Thread"),
            sequence: 78,
        });
        
        // 添加取消关注线程操作
        messageActionsRegistry.add("unfollow-thread", {
            condition: (component) =>
                component.message.linkedSubChannel &&
                component.message.linkedSubChannel.isFollowing &&
                component.store.self.isInternalUser,
            icon: "fa-bell-slash-o",
            onClick: (component) => {
                component.message.linkedSubChannel.unfollow();
            },
            title: () => _t("Unfollow Thread"),
            sequence: 79,
        });
        
        // 添加归档线程操作
        messageActionsRegistry.add("archive-thread", {
            condition: (component) =>
                component.message.linkedSubChannel &&
                !component.message.linkedSubChannel.isArchived &&
                component.store.self.isInternalUser,
            icon: "fa-archive",
            onClick: (component) => {
                component.message.linkedSubChannel.archive();
            },
            title: () => _t("Archive Thread"),
            sequence: 80,
        });
    }
};

// 应用消息操作增强
MessageActionsEnhancer.enhanceMessageActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作添加支持
- 序列化排序控制

### 2. 条件显示
- 灵活的显示条件配置
- 上下文感知的操作显示
- 权限控制集成

### 3. 动态行为
- 基于状态的不同行为
- 智能的操作选择
- 用户体验优化

### 4. 国际化支持
- 完整的多语言支持
- 动态标题生成
- 本地化文本处理

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态操作配置

### 2. 策略模式 (Strategy Pattern)
- 不同条件下的操作策略
- 可配置的行为模式

### 3. 命令模式 (Command Pattern)
- 操作的封装和执行
- 统一的操作接口

## 注意事项

1. **权限控制**: 确保操作权限的正确验证
2. **状态检查**: 完善的状态和条件检查
3. **用户体验**: 提供清晰的操作反馈
4. **性能优化**: 避免频繁的条件检查

## 扩展建议

1. **更多操作**: 添加更多实用的消息操作
2. **批量操作**: 支持批量消息操作
3. **自定义操作**: 允许用户自定义操作
4. **快捷键**: 添加键盘快捷键支持
5. **操作历史**: 记录和管理操作历史

该文件为讨论应用的消息提供了重要的线程创建和管理功能，增强了用户的消息交互体验。

# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了对 Odoo 讨论应用公共Web环境中线程模型的补丁扩展，专门添加了子频道功能的完整支持。该补丁通过Odoo的补丁机制扩展了Thread模型，添加了子频道关系、频道信息获取、子频道创建和加载等核心功能，实现了线程的层次化结构和子频道管理，是讨论应用中子频道功能的核心数据模型组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/thread_model_patch.js`
- **行数**: 159
- **模块**: `@mail/discuss/core/public_web/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_model'  // 线程模型
'@web/core/utils/concurrency'     // 并发工具
'@mail/model/record'              // 记录基类
'@web/core/network/rpc'           // RPC网络服务
'@web/core/utils/patch'           // 补丁工具
```

## 补丁定义

### Thread 静态方法补丁

```javascript
patch(Thread, {
    async getOrFetch(data) {
        let thread = super.get(data);
        if (data.model !== "discuss.channel" || !data.id) {
            return thread;
        }
        thread = this.insert({ id: data.id, model: data.model });
        if (thread.fetchChannelInfoState === "fetched") {
            return Promise.resolve(thread);
        }
        if (thread.fetchChannelInfoState === "fetching") {
            return thread.fetchChannelInfoDeferred;
        }
        // 异步获取频道信息
    }
});
```

**静态方法特性**:
- 扩展获取或获取方法
- 添加频道信息获取逻辑
- 实现状态管理和并发控制
- 支持异步数据加载

### Thread 实例方法补丁

```javascript
patch(Thread.prototype, {
    setup() {
        super.setup(...arguments);
        this.from_message_id = Record.one("Message");
        this.parent_channel_id = Record.one("Thread", {
            onDelete() {
                this.delete();
            },
        });
        this.sub_channel_ids = Record.many("Thread", {
            inverse: "parent_channel_id",
            sort: (a, b) => b.id - a.id,
        });
        // 其他属性设置
    }
});
```

**实例方法特性**:
- 扩展线程模型属性
- 添加子频道关系管理
- 实现层次化结构
- 提供子频道操作方法

## 核心功能

### 1. 关系属性定义

```javascript
this.from_message_id = Record.one("Message");
this.parent_channel_id = Record.one("Thread", {
    onDelete() {
        this.delete();
    },
});
this.sub_channel_ids = Record.many("Thread", {
    inverse: "parent_channel_id",
    sort: (a, b) => b.id - a.id,
});
```

**关系功能**:
- **来源消息**: 记录创建子频道的原始消息
- **父频道**: 建立父子频道关系，父频道删除时自动删除子频道
- **子频道列表**: 管理所有子频道，按ID降序排列
- **反向关联**: 自动维护双向关联关系

### 2. 侧边栏显示控制

```javascript
this.displayInSidebar = Record.attr(false, {
    compute() {
        return (
            this.displayToSelf ||
            this.isLocallyPinned ||
            this.sub_channel_ids.some((t) => t.displayInSidebar)
        );
    },
});
```

**显示控制功能**:
- **自身显示**: 线程本身需要显示
- **本地固定**: 本地固定的线程显示
- **子频道显示**: 有子频道需要显示时显示
- **计算属性**: 自动计算是否应在侧边栏显示

### 3. 子频道功能检查

```javascript
get hasSubChannelFeature() {
    return this.channel_type === "channel" && !this.parent_channel_id;
}
```

**功能检查**:
- **频道类型**: 必须是channel类型
- **非子频道**: 不能是其他频道的子频道
- **功能启用**: 仅顶级频道支持子频道功能

### 4. 创建子频道

```javascript
async createSubChannel({ initialMessage, name } = {}) {
    const { data, sub_channel } = await rpc("/discuss/channel/sub_channel/create", {
        parent_channel_id: this.id,
        from_message_id: initialMessage?.id,
        name,
    });
    this.store.insert(data, { html: true });
    this.store.Thread.get(sub_channel).open();
}
```

**创建功能**:
- **RPC调用**: 调用后端创建子频道
- **参数传递**: 传递父频道ID、来源消息ID、名称
- **数据插入**: 将返回数据插入存储
- **自动打开**: 创建后自动打开新子频道

### 5. 加载更多子频道

```javascript
async loadMoreSubChannels({ searchTerm } = {}) {
    if (this.loadSubChannelsDone) {
        return;
    }
    const limit = 30;
    const data = await rpc("/discuss/channel/sub_channel/fetch", {
        before: this.lastSubChannelLoaded,
        limit,
        parent_channel_id: this.id,
        search_term: searchTerm,
    });
    // 处理返回数据
}
```

**加载功能**:
- **完成检查**: 检查是否已加载完所有子频道
- **分页加载**: 每次加载30个子频道
- **搜索支持**: 支持按搜索词过滤
- **状态管理**: 管理加载状态和最后加载的子频道

## 使用场景

### 1. 线程模型增强

```javascript
// 线程模型增强功能
const ThreadModelPatchEnhancer = {
    enhanceThreadModelPatch: () => {
        const EnhancedThreadPatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有属性
                this.from_message_id = Record.one("Message");
                this.parent_channel_id = Record.one("Thread", {
                    onDelete() {
                        this.delete();
                    },
                });
                this.sub_channel_ids = Record.many("Thread", {
                    inverse: "parent_channel_id",
                    sort: (a, b) => b.id - a.id,
                });
                
                // 增强属性
                this.subChannelStats = Record.attr({
                    compute: () => this.computeSubChannelStats()
                });
                
                this.canManageSubChannels = Record.attr({
                    compute: () => this.computeCanManageSubChannels()
                });
                
                this.subChannelActivity = Record.attr({
                    compute: () => this.computeSubChannelActivity()
                });
                
                this.loadSubChannelsDone = false;
                this.lastSubChannelLoaded = null;
                this.subChannelCache = new Map();
                this.subChannelLoadPromises = new Map();
            },
            
            // 计算子频道统计
            computeSubChannelStats() {
                const subChannels = this.sub_channel_ids;
                
                return {
                    total: subChannels.length,
                    active: subChannels.filter(ch => this.isChannelActive(ch)).length,
                    withMembers: subChannels.filter(ch => ch.channelMembers?.length > 0).length,
                    withMessages: subChannels.filter(ch => ch.message_ids?.length > 0).length,
                    archived: subChannels.filter(ch => ch.isArchived).length
                };
            },
            
            // 计算是否可以管理子频道
            computeCanManageSubChannels() {
                if (!this.hasSubChannelFeature) {
                    return false;
                }
                
                // 检查用户权限
                const user = this.store.self;
                if (!user.isInternalUser) {
                    return false;
                }
                
                // 检查频道权限
                const member = this.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                return member?.role === 'admin' || member?.role === 'moderator';
            },
            
            // 计算子频道活动
            computeSubChannelActivity() {
                const subChannels = this.sub_channel_ids;
                const now = Date.now();
                const oneDayAgo = now - 24 * 60 * 60 * 1000;
                const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000;
                
                return {
                    today: subChannels.filter(ch => {
                        const lastActivity = new Date(ch.lastActivity || 0).getTime();
                        return lastActivity > oneDayAgo;
                    }).length,
                    thisWeek: subChannels.filter(ch => {
                        const lastActivity = new Date(ch.lastActivity || 0).getTime();
                        return lastActivity > oneWeekAgo;
                    }).length,
                    mostActive: this.getMostActiveSubChannel(subChannels),
                    leastActive: this.getLeastActiveSubChannel(subChannels)
                };
            },
            
            // 增强的获取或获取方法
            async getOrFetch(data) {
                // 检查缓存
                const cacheKey = `${data.model}_${data.id}`;
                if (this.subChannelCache.has(cacheKey)) {
                    const cached = this.subChannelCache.get(cacheKey);
                    if (Date.now() - cached.timestamp < 5 * 60 * 1000) { // 5分钟缓存
                        return cached.thread;
                    }
                }
                
                // 检查正在进行的请求
                if (this.subChannelLoadPromises.has(cacheKey)) {
                    return this.subChannelLoadPromises.get(cacheKey);
                }
                
                // 执行原有逻辑
                const promise = super.getOrFetch(data);
                this.subChannelLoadPromises.set(cacheKey, promise);
                
                try {
                    const result = await promise;
                    
                    // 缓存结果
                    this.subChannelCache.set(cacheKey, {
                        thread: result,
                        timestamp: Date.now()
                    });
                    
                    return result;
                } finally {
                    this.subChannelLoadPromises.delete(cacheKey);
                }
            },
            
            // 增强的创建子频道
            async createSubChannel({ initialMessage, name, description, isPrivate = false } = {}) {
                try {
                    // 验证权限
                    if (!this.canManageSubChannels) {
                        throw new Error('没有权限创建子频道');
                    }
                    
                    // 验证名称
                    if (!name || name.trim().length === 0) {
                        throw new Error('子频道名称不能为空');
                    }
                    
                    // 检查名称重复
                    const existingChannel = this.sub_channel_ids.find(
                        ch => ch.name.toLowerCase() === name.toLowerCase()
                    );
                    
                    if (existingChannel) {
                        throw new Error('子频道名称已存在');
                    }
                    
                    // 调用后端创建
                    const { data, sub_channel } = await rpc("/discuss/channel/sub_channel/create", {
                        parent_channel_id: this.id,
                        from_message_id: initialMessage?.id,
                        name: name.trim(),
                        description: description?.trim(),
                        channel_type: isPrivate ? 'group' : 'channel'
                    });
                    
                    // 插入数据
                    this.store.insert(data, { html: true });
                    
                    // 获取新创建的子频道
                    const newSubChannel = this.store.Thread.get(sub_channel);
                    
                    // 记录创建事件
                    this.recordSubChannelCreation(newSubChannel);
                    
                    // 自动打开
                    newSubChannel.open();
                    
                    return newSubChannel;
                    
                } catch (error) {
                    console.error('创建子频道失败:', error);
                    throw error;
                }
            },
            
            // 增强的加载更多子频道
            async loadMoreSubChannels({ 
                searchTerm, 
                refresh = false, 
                includeArchived = false,
                sortBy = 'id'
            } = {}) {
                if (this.loadSubChannelsDone && !refresh && !searchTerm) {
                    return [];
                }
                
                try {
                    const limit = 30;
                    const params = {
                        parent_channel_id: this.id,
                        limit,
                        search_term: searchTerm,
                        include_archived: includeArchived,
                        sort_by: sortBy
                    };
                    
                    if (!refresh && !searchTerm) {
                        params.before = this.lastSubChannelLoaded;
                    }
                    
                    const data = await rpc("/discuss/channel/sub_channel/fetch", params);
                    const { Thread: subChannels = [] } = this.store.insert(data, { html: true });
                    
                    if (searchTerm) {
                        // 搜索时不更新加载状态
                        return subChannels;
                    }
                    
                    if (refresh) {
                        // 刷新时重置状态
                        this.loadSubChannelsDone = false;
                        this.lastSubChannelLoaded = null;
                    }
                    
                    this.lastSubChannelLoaded = subChannels.at(-1)?.id;
                    
                    if (subChannels.length < limit) {
                        this.loadSubChannelsDone = true;
                    }
                    
                    // 记录加载事件
                    this.recordSubChannelLoad(subChannels.length, searchTerm);
                    
                    return subChannels;
                    
                } catch (error) {
                    console.error('加载子频道失败:', error);
                    throw error;
                }
            },
            
            // 批量删除子频道
            async deleteSubChannels(subChannelIds) {
                if (!this.canManageSubChannels) {
                    throw new Error('没有权限删除子频道');
                }
                
                try {
                    await rpc("/discuss/channel/sub_channel/delete_batch", {
                        parent_channel_id: this.id,
                        sub_channel_ids: subChannelIds
                    });
                    
                    // 从本地存储中移除
                    subChannelIds.forEach(id => {
                        const subChannel = this.store.Thread.get({ id, model: 'discuss.channel' });
                        if (subChannel) {
                            subChannel.delete();
                        }
                    });
                    
                    // 记录删除事件
                    this.recordSubChannelDeletion(subChannelIds);
                    
                } catch (error) {
                    console.error('批量删除子频道失败:', error);
                    throw error;
                }
            },
            
            // 归档子频道
            async archiveSubChannel(subChannelId) {
                try {
                    await rpc("/discuss/channel/sub_channel/archive", {
                        sub_channel_id: subChannelId
                    });
                    
                    const subChannel = this.store.Thread.get({ id: subChannelId, model: 'discuss.channel' });
                    if (subChannel) {
                        subChannel.isArchived = true;
                    }
                    
                    // 记录归档事件
                    this.recordSubChannelArchive(subChannelId);
                    
                } catch (error) {
                    console.error('归档子频道失败:', error);
                    throw error;
                }
            },
            
            // 检查频道是否活跃
            isChannelActive(channel) {
                if (!channel.lastActivity) {
                    return false;
                }
                
                const lastActivity = new Date(channel.lastActivity);
                const now = new Date();
                const daysDiff = (now - lastActivity) / (1000 * 60 * 60 * 24);
                
                return daysDiff <= 7; // 7天内有活动认为是活跃的
            },
            
            // 获取最活跃的子频道
            getMostActiveSubChannel(subChannels) {
                if (subChannels.length === 0) return null;
                
                return subChannels.reduce((most, current) => {
                    const mostActivity = new Date(most.lastActivity || 0).getTime();
                    const currentActivity = new Date(current.lastActivity || 0).getTime();
                    
                    return currentActivity > mostActivity ? current : most;
                });
            },
            
            // 获取最不活跃的子频道
            getLeastActiveSubChannel(subChannels) {
                if (subChannels.length === 0) return null;
                
                return subChannels.reduce((least, current) => {
                    const leastActivity = new Date(least.lastActivity || Date.now()).getTime();
                    const currentActivity = new Date(current.lastActivity || Date.now()).getTime();
                    
                    return currentActivity < leastActivity ? current : least;
                });
            },
            
            // 记录子频道创建
            recordSubChannelCreation(subChannel) {
                try {
                    const creations = JSON.parse(
                        localStorage.getItem('sub_channel_creations') || '[]'
                    );
                    
                    creations.push({
                        subChannelId: subChannel.id,
                        subChannelName: subChannel.name,
                        parentChannelId: this.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (creations.length > 50) {
                        creations.splice(0, creations.length - 50);
                    }
                    
                    localStorage.setItem('sub_channel_creations', JSON.stringify(creations));
                } catch (error) {
                    console.warn('记录子频道创建失败:', error);
                }
            },
            
            // 记录子频道加载
            recordSubChannelLoad(count, searchTerm) {
                try {
                    const loads = JSON.parse(
                        localStorage.getItem('sub_channel_loads') || '[]'
                    );
                    
                    loads.push({
                        parentChannelId: this.id,
                        count,
                        searchTerm,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (loads.length > 100) {
                        loads.splice(0, loads.length - 100);
                    }
                    
                    localStorage.setItem('sub_channel_loads', JSON.stringify(loads));
                } catch (error) {
                    console.warn('记录子频道加载失败:', error);
                }
            },
            
            // 记录子频道删除
            recordSubChannelDeletion(subChannelIds) {
                try {
                    const deletions = JSON.parse(
                        localStorage.getItem('sub_channel_deletions') || '[]'
                    );
                    
                    deletions.push({
                        subChannelIds,
                        parentChannelId: this.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (deletions.length > 50) {
                        deletions.splice(0, deletions.length - 50);
                    }
                    
                    localStorage.setItem('sub_channel_deletions', JSON.stringify(deletions));
                } catch (error) {
                    console.warn('记录子频道删除失败:', error);
                }
            },
            
            // 记录子频道归档
            recordSubChannelArchive(subChannelId) {
                try {
                    const archives = JSON.parse(
                        localStorage.getItem('sub_channel_archives') || '[]'
                    );
                    
                    archives.push({
                        subChannelId,
                        parentChannelId: this.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (archives.length > 50) {
                        archives.splice(0, archives.length - 50);
                    }
                    
                    localStorage.setItem('sub_channel_archives', JSON.stringify(archives));
                } catch (error) {
                    console.warn('记录子频道归档失败:', error);
                }
            },
            
            // 清理缓存
            clearSubChannelCache() {
                this.subChannelCache.clear();
                this.subChannelLoadPromises.clear();
            },
            
            // 获取子频道管理统计
            getSubChannelManagementStats() {
                try {
                    return {
                        creations: JSON.parse(localStorage.getItem('sub_channel_creations') || '[]').length,
                        loads: JSON.parse(localStorage.getItem('sub_channel_loads') || '[]').length,
                        deletions: JSON.parse(localStorage.getItem('sub_channel_deletions') || '[]').length,
                        archives: JSON.parse(localStorage.getItem('sub_channel_archives') || '[]').length,
                        cacheSize: this.subChannelCache.size,
                        activePromises: this.subChannelLoadPromises.size
                    };
                } catch (error) {
                    return {
                        creations: 0,
                        loads: 0,
                        deletions: 0,
                        archives: 0,
                        cacheSize: 0,
                        activePromises: 0
                    };
                }
            }
        };
        
        // 应用静态方法补丁
        patch(Thread, EnhancedThreadPatch);
        
        // 应用实例方法补丁
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程模型补丁增强
ThreadModelPatchEnhancer.enhanceThreadModelPatch();
```

## 技术特点

### 1. 补丁机制
- 静态方法和实例方法双重补丁
- 保持原有功能完整性
- 运行时功能增强

### 2. 关系管理
- 完整的父子关系定义
- 自动关联维护
- 级联删除支持

### 3. 异步处理
- 并发控制机制
- 状态管理优化
- 错误处理完善

### 4. 数据缓存
- 智能缓存策略
- 分页加载支持
- 搜索功能集成

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 关联模式 (Association Pattern)
- 对象间关系建立
- 双向关联维护

### 3. 状态模式 (State Pattern)
- 加载状态管理
- 状态转换控制

## 注意事项

1. **关系一致性**: 确保父子关系的数据一致性
2. **并发控制**: 避免重复的异步请求
3. **内存管理**: 及时清理缓存和状态
4. **性能优化**: 优化大量子频道的加载性能

## 扩展建议

1. **更多关系**: 添加更复杂的频道关系
2. **智能缓存**: 实现更智能的缓存策略
3. **批量操作**: 支持更多的批量管理操作
4. **实时同步**: 实现子频道的实时同步
5. **权限控制**: 增强子频道的权限管理

该补丁为讨论应用的线程模型提供了完整的子频道功能支持，是实现层次化频道结构的核心组件。

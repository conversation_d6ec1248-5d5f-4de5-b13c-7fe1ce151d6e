# Message Model Patch - 消息模型补丁

## 概述

`message_model_patch.js` 实现了对 Odoo 讨论应用公共Web环境中消息模型的补丁扩展，专门添加了关联子频道的功能。该补丁通过Odoo的补丁机制扩展了Message模型，添加了linkedSubChannel属性，建立了消息与子频道之间的关联关系，为消息创建讨论线程功能提供了数据模型支持，是讨论应用中子频道功能的重要数据基础。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/public_web/message_model_patch.js`
- **行数**: 20
- **模块**: `@mail/discuss/core/public_web/message_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/message_model'  // 消息模型
'@mail/model/record'               // 记录基类
'@web/core/utils/patch'            // 补丁工具
```

## 补丁定义

### Message 补丁

```javascript
patch(Message.prototype, {
    setup() {
        super.setup(...arguments);
        this.linkedSubChannel = Record.one("Thread", { inverse: "from_message_id" });
    },
});
```

**补丁特性**:
- 扩展消息模型功能
- 添加子频道关联属性
- 建立双向关联关系
- 支持反向查找

## 核心功能

### 1. 关联子频道属性

```javascript
this.linkedSubChannel = Record.one("Thread", { inverse: "from_message_id" });
```

**关联功能**:
- **一对一关系**: 每个消息最多关联一个子频道
- **Thread类型**: 关联的是Thread模型实例
- **反向关联**: 通过from_message_id建立反向关系
- **自动同步**: 关联关系的自动维护

## 使用场景

### 1. 消息模型增强

```javascript
// 消息模型增强功能
const MessageModelEnhancer = {
    enhanceMessageModel: () => {
        const EnhancedMessagePatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有的子频道关联
                this.linkedSubChannel = Record.one("Thread", { inverse: "from_message_id" });
                
                // 增强的关联属性
                this.relatedThreads = Record.many("Thread", { 
                    compute: () => this.computeRelatedThreads(),
                    sort: (t1, t2) => new Date(t2.create_date) - new Date(t1.create_date)
                });
                
                this.threadMetrics = Record.attr({
                    compute: () => this.computeThreadMetrics()
                });
                
                this.canCreateThread = Record.attr({
                    compute: () => this.computeCanCreateThread()
                });
                
                this.threadCreationHistory = Record.many("ThreadCreationEvent", {
                    compute: () => this.computeThreadCreationHistory()
                });
            },
            
            // 计算相关线程
            computeRelatedThreads() {
                const threads = [];
                
                // 添加直接关联的子频道
                if (this.linkedSubChannel) {
                    threads.push(this.linkedSubChannel);
                }
                
                // 添加基于相同主题的线程
                const sameTopicThreads = this.findThreadsBySameTopic();
                threads.push(...sameTopicThreads);
                
                // 添加基于相同作者的线程
                const sameAuthorThreads = this.findThreadsBySameAuthor();
                threads.push(...sameAuthorThreads);
                
                // 去重
                return [...new Set(threads)];
            },
            
            // 计算线程指标
            computeThreadMetrics() {
                if (!this.linkedSubChannel) {
                    return {
                        hasThread: false,
                        messageCount: 0,
                        participantCount: 0,
                        lastActivity: null,
                        isActive: false
                    };
                }
                
                return {
                    hasThread: true,
                    messageCount: this.linkedSubChannel.message_ids?.length || 0,
                    participantCount: this.linkedSubChannel.channelMembers?.length || 0,
                    lastActivity: this.linkedSubChannel.lastActivity,
                    isActive: this.isThreadActive(),
                    createdDate: this.linkedSubChannel.create_date,
                    threadType: this.linkedSubChannel.channel_type
                };
            },
            
            // 计算是否可以创建线程
            computeCanCreateThread() {
                // 已有关联线程则不能再创建
                if (this.linkedSubChannel) {
                    return false;
                }
                
                // 检查权限
                if (!this.store.self.isInternalUser) {
                    return false;
                }
                
                // 检查线程是否支持子频道功能
                if (!this.thread.hasSubChannelFeature) {
                    return false;
                }
                
                // 检查消息是否在原始线程中
                if (!this.isInOriginThread) {
                    return false;
                }
                
                return true;
            },
            
            // 计算线程创建历史
            computeThreadCreationHistory() {
                try {
                    const history = JSON.parse(
                        localStorage.getItem('message_thread_creation_history') || '[]'
                    );
                    
                    return history.filter(event => event.messageId === this.id);
                } catch (error) {
                    return [];
                }
            },
            
            // 创建子频道
            async createSubChannel(options = {}) {
                const {
                    name = null,
                    description = null,
                    autoFocus = false,
                    inviteMembers = [],
                    isPrivate = false
                } = options;
                
                try {
                    // 检查是否可以创建
                    if (!this.canCreateThread) {
                        throw new Error('无法为此消息创建线程');
                    }
                    
                    // 准备创建参数
                    const createParams = {
                        parent_channel_id: this.thread.id,
                        from_message_id: this.id,
                        name: name || this.generateThreadName(),
                        description: description || this.generateThreadDescription(),
                        channel_type: isPrivate ? 'group' : 'channel'
                    };
                    
                    // 调用后端创建子频道
                    const result = await this.env.services.orm.call(
                        'discuss.channel',
                        'create_sub_channel',
                        [createParams]
                    );
                    
                    // 插入到存储
                    const subChannel = this.store.Thread.insert(result);
                    
                    // 邀请成员
                    if (inviteMembers.length > 0) {
                        await subChannel.inviteMembers(inviteMembers);
                    }
                    
                    // 记录创建事件
                    this.recordThreadCreation(subChannel);
                    
                    // 自动打开和聚焦
                    if (autoFocus) {
                        subChannel.open();
                        setTimeout(() => {
                            const composer = subChannel.composer;
                            if (composer) {
                                composer.focus();
                            }
                        }, 100);
                    }
                    
                    return subChannel;
                    
                } catch (error) {
                    console.error('创建子频道失败:', error);
                    throw error;
                }
            },
            
            // 生成线程名称
            generateThreadName() {
                const maxLength = 50;
                let name = this.body || this.subject || '';
                
                // 移除HTML标签
                name = name.replace(/<[^>]*>/g, '');
                
                // 截取前面部分
                if (name.length > maxLength) {
                    name = name.substring(0, maxLength) + '...';
                }
                
                return name || `Thread from ${this.author?.name || 'Unknown'}`;
            },
            
            // 生成线程描述
            generateThreadDescription() {
                const author = this.author?.name || 'Unknown';
                const date = new Date(this.datetime).toLocaleDateString();
                
                return `Thread created from message by ${author} on ${date}`;
            },
            
            // 检查线程是否活跃
            isThreadActive() {
                if (!this.linkedSubChannel) {
                    return false;
                }
                
                const lastActivity = new Date(this.linkedSubChannel.lastActivity || 0);
                const now = new Date();
                const daysDiff = (now - lastActivity) / (1000 * 60 * 60 * 24);
                
                return daysDiff <= 7; // 7天内有活动认为是活跃的
            },
            
            // 查找相同主题的线程
            findThreadsBySameTopic() {
                if (!this.subject) {
                    return [];
                }
                
                const allThreads = Object.values(this.store.Thread.records);
                
                return allThreads.filter(thread => 
                    thread.id !== this.thread.id &&
                    thread.subject &&
                    thread.subject.toLowerCase().includes(this.subject.toLowerCase())
                );
            },
            
            // 查找相同作者的线程
            findThreadsBySameAuthor() {
                if (!this.author) {
                    return [];
                }
                
                const allThreads = Object.values(this.store.Thread.records);
                
                return allThreads.filter(thread => 
                    thread.id !== this.thread.id &&
                    thread.from_message_id &&
                    thread.from_message_id.author?.id === this.author.id
                );
            },
            
            // 记录线程创建
            recordThreadCreation(subChannel) {
                try {
                    const history = JSON.parse(
                        localStorage.getItem('message_thread_creation_history') || '[]'
                    );
                    
                    history.push({
                        messageId: this.id,
                        threadId: subChannel.id,
                        threadName: subChannel.displayName,
                        createdAt: Date.now(),
                        createdBy: this.store.self.userId,
                        parentThread: this.thread.id
                    });
                    
                    // 保留最近100个记录
                    if (history.length > 100) {
                        history.splice(0, history.length - 100);
                    }
                    
                    localStorage.setItem('message_thread_creation_history', JSON.stringify(history));
                } catch (error) {
                    console.warn('记录线程创建失败:', error);
                }
            },
            
            // 获取线程统计
            getThreadStatistics() {
                return {
                    hasLinkedThread: !!this.linkedSubChannel,
                    threadMetrics: this.threadMetrics,
                    relatedThreadsCount: this.relatedThreads.length,
                    canCreateThread: this.canCreateThread,
                    creationHistory: this.threadCreationHistory.length,
                    isInOriginThread: this.isInOriginThread
                };
            },
            
            // 删除关联线程
            async deleteLinkedThread() {
                if (!this.linkedSubChannel) {
                    throw new Error('没有关联的线程可删除');
                }
                
                const confirmed = confirm(`确定要删除线程 "${this.linkedSubChannel.displayName}" 吗？`);
                
                if (confirmed) {
                    try {
                        await this.linkedSubChannel.delete();
                        
                        // 记录删除事件
                        this.recordThreadDeletion();
                        
                        return true;
                    } catch (error) {
                        console.error('删除线程失败:', error);
                        throw error;
                    }
                }
                
                return false;
            },
            
            // 记录线程删除
            recordThreadDeletion() {
                try {
                    const deletions = JSON.parse(
                        localStorage.getItem('message_thread_deletions') || '[]'
                    );
                    
                    deletions.push({
                        messageId: this.id,
                        threadName: this.linkedSubChannel?.displayName,
                        deletedAt: Date.now(),
                        deletedBy: this.store.self.userId
                    });
                    
                    // 保留最近50个记录
                    if (deletions.length > 50) {
                        deletions.splice(0, deletions.length - 50);
                    }
                    
                    localStorage.setItem('message_thread_deletions', JSON.stringify(deletions));
                } catch (error) {
                    console.warn('记录线程删除失败:', error);
                }
            },
            
            // 获取线程活动摘要
            getThreadActivitySummary() {
                if (!this.linkedSubChannel) {
                    return null;
                }
                
                const thread = this.linkedSubChannel;
                
                return {
                    threadId: thread.id,
                    threadName: thread.displayName,
                    messageCount: thread.message_ids?.length || 0,
                    participantCount: thread.channelMembers?.length || 0,
                    lastActivity: thread.lastActivity,
                    isActive: this.isThreadActive(),
                    unreadCount: thread.selfMember?.message_unread_counter || 0,
                    isMuted: thread.isMuted,
                    isFollowing: thread.isFollowing
                };
            },
            
            // 导出线程数据
            async exportThreadData() {
                if (!this.linkedSubChannel) {
                    throw new Error('没有关联的线程可导出');
                }
                
                try {
                    const threadData = await this.env.services.orm.call(
                        'discuss.channel',
                        'export_thread_data',
                        [this.linkedSubChannel.id]
                    );
                    
                    const blob = new Blob([JSON.stringify(threadData, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `thread_${this.linkedSubChannel.id}_${Date.now()}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    
                    return threadData;
                } catch (error) {
                    console.error('导出线程数据失败:', error);
                    throw error;
                }
            }
        };
        
        patch(Message.prototype, EnhancedMessagePatch);
    }
};

// 应用消息模型增强
MessageModelEnhancer.enhanceMessageModel();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 关联关系
- 一对一关联定义
- 反向关系建立
- 自动关系维护

### 3. 数据模型
- Record.one关系定义
- 类型安全的关联
- 查询优化支持

### 4. 扩展性设计
- 易于添加新属性
- 支持复杂关联关系
- 灵活的数据访问

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 关联模式 (Association Pattern)
- 对象间关系建立
- 双向关联维护

### 3. 代理模式 (Proxy Pattern)
- 关联对象的代理访问
- 懒加载支持

## 注意事项

1. **关系一致性**: 确保关联关系的数据一致性
2. **性能影响**: 避免过多的关联查询影响性能
3. **内存管理**: 及时清理不需要的关联对象
4. **数据同步**: 保证关联数据的实时同步

## 扩展建议

1. **更多关联**: 添加更多类型的关联关系
2. **关联缓存**: 实现关联对象的缓存机制
3. **批量操作**: 支持批量关联操作
4. **关联验证**: 添加关联关系的验证机制
5. **关联统计**: 提供关联关系的统计分析

该补丁为讨论应用的消息模型提供了重要的子频道关联功能，是实现消息线程化的核心数据基础。

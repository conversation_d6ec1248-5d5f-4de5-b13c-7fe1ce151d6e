# Channel Selector - 频道选择器

## 概述

`channel_selector.js` 实现了 Odoo 讨论应用Web环境中的频道选择器组件，提供了频道和聊天对象的搜索、选择和创建功能。该组件支持多选模式、自动完成、键盘导航等特性，集成了可导航列表和标签列表组件，为用户提供了直观便捷的频道和聊天对象选择界面，是讨论应用中重要的交互组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/channel_selector.js`
- **行数**: 257
- **模块**: `@mail/discuss/core/web/channel_selector`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/navigable_list'    // 可导航列表组件
'@mail/utils/common/format'           // 格式化工具
'@odoo/owl'                          // OWL 框架
'@web/core/hotkeys/hotkey_service'    // 热键服务
'@web/core/l10n/translation'          // 国际化
'@web/core/tags_list/tags_list'       // 标签列表组件
'@web/core/utils/hooks'               // Web核心钩子
'@web/core/utils/misc'                // 工具函数
'@mail/utils/common/hooks'            // 邮件钩子
```

## 组件定义

### ChannelSelector 类

```javascript
const ChannelSelector = class ChannelSelector extends Component {
    static components = { TagsList, NavigableList };
    static props = ["category", "onValidate?", "autofocus?", "multiple?", "close?"];
    static defaultProps = { multiple: true };
    static template = "discuss.ChannelSelector";
}
```

**组件特性**:
- 集成标签列表和可导航列表
- 支持分类、验证、自动聚焦等属性
- 默认支持多选模式
- 使用专用模板

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.discussCoreCommonService = useState(useService("discuss.core.common"));
    this.store = useState(useService("mail.store"));
    this.suggestionService = useService("mail.suggestion");
    this.orm = useService("orm");
    this.sequential = useSequential();
    this.state = useState({
        value: "",
        selectedPartners: [],
        navigableListProps: {
            anchorRef: undefined,
            position: "bottom-fit",
            onSelect: (ev, option) => this.onSelect(option),
            optionTemplate: // 根据分类动态设置
            options: [],
            isLoading: false,
        },
    });
}
```

**初始化功能**:
- **服务注入**: 注入讨论服务、存储、建议服务、ORM等
- **状态管理**: 管理输入值、选中伙伴、导航列表属性
- **顺序执行**: 使用sequential确保操作顺序
- **引用管理**: 管理输入框和根元素引用

### 2. 建议获取

```javascript
async fetchSuggestions() {
    const cleanedTerm = cleanTerm(this.state.value);
    if (cleanedTerm) {
        if (this.props.category.id === "channels") {
            // 频道搜索逻辑
            const domain = [
                ["parent_channel_id", "=", false],
                ["channel_type", "=", "channel"],
                ["name", "ilike", cleanedTerm],
            ];
            const results = await this.orm.searchRead("discuss.channel", domain, fields, {
                limit: 10,
            });
            // 处理结果并添加创建选项
        }
        if (this.props.category.id === "chats") {
            // 聊天对象搜索逻辑
            const data = await this.orm.call("res.partner", "im_search", [
                cleanedTerm,
                10,
                this.state.selectedPartners,
            ]);
            // 处理伙伴建议
        }
    }
}
```

**建议获取功能**:
- **搜索词清理**: 使用cleanTerm清理搜索词
- **分类处理**: 根据分类类型执行不同搜索逻辑
- **频道搜索**: 搜索非子频道的公共频道
- **聊天搜索**: 搜索可聊天的伙伴
- **结果限制**: 限制搜索结果数量

### 3. 选项选择

```javascript
onSelect(option) {
    if (this.props.category.id === "channels") {
        if (option.channelId === "__create__") {
            // 创建新频道
            this.env.services.orm
                .call("discuss.channel", "channel_create", [
                    option.label,
                    this.store.internalUserGroupId,
                ])
                .then((data) => {
                    const { Thread } = this.store.insert(data);
                    const [channel] = Thread;
                    channel.open();
                });
        } else {
            // 加入现有频道
            this.store.joinChannel(option.channelId, option.label);
        }
        this.onValidate();
    }
    if (this.props.category.id === "chats") {
        // 添加到选中伙伴列表
        if (!this.state.selectedPartners.includes(option.partner.id)) {
            this.state.selectedPartners.push(option.partner.id);
        }
        this.state.value = "";
    }
}
```

**选择功能**:
- **频道处理**: 创建新频道或加入现有频道
- **聊天处理**: 添加伙伴到选中列表
- **重复检查**: 避免重复选择同一伙伴
- **状态清理**: 清理输入值

### 4. 键盘交互

```javascript
onKeydownInput(ev) {
    const hotkey = getActiveHotkey(ev);
    switch (hotkey) {
        case "enter":
            if (!isEventHandled(ev, "NavigableList.select") && this.state.value !== "") {
                this.onValidate();
            }
            break;
        case "backspace":
            if (this.state.selectedPartners.length > 0 && this.state.value === "") {
                this.state.selectedPartners.pop();
            }
            break;
        case "escape":
            this.props.close?.();
            break;
    }
}
```

**键盘交互功能**:
- **回车键**: 验证并执行选择
- **退格键**: 删除最后选中的伙伴
- **ESC键**: 关闭选择器
- **事件处理**: 防止事件冲突

## 使用场景

### 1. 频道选择器增强

```javascript
// 频道选择器增强功能
const ChannelSelectorEnhancer = {
    enhanceChannelSelector: () => {
        const EnhancedChannelSelector = class extends ChannelSelector {
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    searchHistory: [],
                    favoriteChannels: [],
                    recentChats: [],
                    filterMode: 'all', // all, favorites, recent, archived
                    sortMode: 'relevance', // relevance, name, activity, members
                    showAdvancedOptions: false,
                    customFilters: []
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 加载用户偏好
                this.loadUserPreferences();
                
                // 设置搜索历史
                this.setupSearchHistory();
                
                // 设置收藏功能
                this.setupFavorites();
                
                // 设置最近聊天
                this.setupRecentChats();
                
                // 设置高级过滤
                this.setupAdvancedFiltering();
            },
            
            // 增强的建议获取
            async fetchSuggestions() {
                const cleanedTerm = cleanTerm(this.state.value);
                
                // 记录搜索
                if (cleanedTerm) {
                    this.recordSearch(cleanedTerm);
                }
                
                if (cleanedTerm) {
                    if (this.props.category.id === "channels") {
                        await this.fetchChannelSuggestions(cleanedTerm);
                    } else if (this.props.category.id === "chats") {
                        await this.fetchChatSuggestions(cleanedTerm);
                    }
                } else {
                    // 无搜索词时显示默认建议
                    this.showDefaultSuggestions();
                }
            },
            
            // 获取频道建议
            async fetchChannelSuggestions(cleanedTerm) {
                try {
                    this.state.navigableListProps.isLoading = true;
                    
                    // 构建搜索域
                    const domain = this.buildChannelSearchDomain(cleanedTerm);
                    
                    const results = await this.sequential(async () => {
                        return await this.orm.searchRead("discuss.channel", domain, 
                            ["name", "description", "member_count", "last_activity"], {
                            limit: this.getSearchLimit(),
                            order: this.getSortOrder()
                        });
                    });
                    
                    if (!results) {
                        this.state.navigableListProps.options = [];
                        return;
                    }
                    
                    // 处理结果
                    const choices = this.processChannelResults(results, cleanedTerm);
                    
                    // 添加创建选项
                    if (this.canCreateChannel()) {
                        choices.push(this.createChannelOption(cleanedTerm));
                    }
                    
                    // 应用过滤和排序
                    const filteredChoices = this.applyFilters(choices);
                    const sortedChoices = this.applySorting(filteredChoices);
                    
                    this.state.navigableListProps.options = sortedChoices;
                    
                } catch (error) {
                    console.error('获取频道建议失败:', error);
                    this.state.navigableListProps.options = [];
                } finally {
                    this.state.navigableListProps.isLoading = false;
                }
            },
            
            // 获取聊天建议
            async fetchChatSuggestions(cleanedTerm) {
                try {
                    this.state.navigableListProps.isLoading = true;
                    
                    const data = await this.sequential(async () => {
                        return await this.orm.call("res.partner", "im_search", [
                            cleanedTerm,
                            this.getSearchLimit(),
                            this.state.selectedPartners,
                            this.getSearchOptions()
                        ]);
                    });
                    
                    if (!data) {
                        this.state.navigableListProps.options = [];
                        return;
                    }
                    
                    const { Persona: partners = [] } = this.store.insert(data);
                    
                    // 处理建议
                    let suggestions = this.suggestionService
                        .sortPartnerSuggestions(partners, cleanedTerm)
                        .map(suggestion => this.createPartnerSuggestion(suggestion));
                    
                    // 添加自己到建议中
                    if (this.shouldIncludeSelf(cleanedTerm)) {
                        suggestions.push(this.createSelfSuggestion());
                    }
                    
                    // 添加收藏联系人
                    const favoriteContacts = this.getFavoriteContacts(cleanedTerm);
                    if (favoriteContacts.length > 0) {
                        suggestions.unshift(...favoriteContacts);
                    }
                    
                    // 添加最近聊天
                    const recentChats = this.getRecentChats(cleanedTerm);
                    if (recentChats.length > 0) {
                        suggestions.unshift(...recentChats);
                    }
                    
                    if (suggestions.length === 0) {
                        suggestions.push(this.createNoResultsSuggestion());
                    }
                    
                    this.state.navigableListProps.options = suggestions;
                    
                } catch (error) {
                    console.error('获取聊天建议失败:', error);
                    this.state.navigableListProps.options = [];
                } finally {
                    this.state.navigableListProps.isLoading = false;
                }
            },
            
            // 显示默认建议
            showDefaultSuggestions() {
                if (this.props.category.id === "channels") {
                    this.showDefaultChannelSuggestions();
                } else if (this.props.category.id === "chats") {
                    this.showDefaultChatSuggestions();
                }
            },
            
            // 显示默认频道建议
            showDefaultChannelSuggestions() {
                const suggestions = [];
                
                // 收藏频道
                const favoriteChannels = this.enhancedState.favoriteChannels;
                if (favoriteChannels.length > 0) {
                    suggestions.push({
                        type: 'section',
                        label: '收藏频道',
                        unselectable: true
                    });
                    suggestions.push(...favoriteChannels.map(ch => this.createChannelSuggestion(ch)));
                }
                
                // 最近活跃频道
                const recentChannels = this.getRecentActiveChannels();
                if (recentChannels.length > 0) {
                    suggestions.push({
                        type: 'section',
                        label: '最近活跃',
                        unselectable: true
                    });
                    suggestions.push(...recentChannels.map(ch => this.createChannelSuggestion(ch)));
                }
                
                this.state.navigableListProps.options = suggestions;
            },
            
            // 显示默认聊天建议
            showDefaultChatSuggestions() {
                const suggestions = [];
                
                // 最近聊天
                const recentChats = this.enhancedState.recentChats;
                if (recentChats.length > 0) {
                    suggestions.push({
                        type: 'section',
                        label: '最近聊天',
                        unselectable: true
                    });
                    suggestions.push(...recentChats.map(chat => this.createPartnerSuggestion(chat)));
                }
                
                // 收藏联系人
                const favoriteContacts = this.getFavoriteContacts();
                if (favoriteContacts.length > 0) {
                    suggestions.push({
                        type: 'section',
                        label: '收藏联系人',
                        unselectable: true
                    });
                    suggestions.push(...favoriteContacts);
                }
                
                this.state.navigableListProps.options = suggestions;
            },
            
            // 构建频道搜索域
            buildChannelSearchDomain(cleanedTerm) {
                let domain = [
                    ["parent_channel_id", "=", false],
                    ["channel_type", "=", "channel"],
                    ["name", "ilike", cleanedTerm],
                ];
                
                // 应用过滤器
                switch (this.enhancedState.filterMode) {
                    case 'favorites':
                        domain.push(["id", "in", this.enhancedState.favoriteChannels.map(ch => ch.id)]);
                        break;
                    case 'archived':
                        domain.push(["active", "=", false]);
                        break;
                    case 'recent':
                        // 添加最近活跃过滤
                        const recentDate = new Date();
                        recentDate.setDate(recentDate.getDate() - 7);
                        domain.push(["last_activity", ">=", recentDate.toISOString()]);
                        break;
                }
                
                return domain;
            },
            
            // 获取搜索限制
            getSearchLimit() {
                return this.enhancedState.showAdvancedOptions ? 20 : 10;
            },
            
            // 获取排序顺序
            getSortOrder() {
                switch (this.enhancedState.sortMode) {
                    case 'name':
                        return 'name ASC';
                    case 'activity':
                        return 'last_activity DESC';
                    case 'members':
                        return 'member_count DESC';
                    default:
                        return 'name ASC';
                }
            },
            
            // 处理频道结果
            processChannelResults(results, searchTerm) {
                return results.map(channel => {
                    const suggestion = this.createChannelSuggestion(channel);
                    
                    // 添加匹配高亮
                    suggestion.highlightedLabel = this.highlightMatch(channel.name, searchTerm);
                    
                    // 添加额外信息
                    suggestion.memberCount = channel.member_count;
                    suggestion.lastActivity = channel.last_activity;
                    suggestion.description = channel.description;
                    
                    return suggestion;
                });
            },
            
            // 创建频道建议
            createChannelSuggestion(channel) {
                return {
                    channelId: channel.id,
                    classList: "o-discuss-ChannelSelector-suggestion",
                    label: channel.name,
                    channel: channel,
                    isFavorite: this.isChannelFavorite(channel.id)
                };
            },
            
            // 创建伙伴建议
            createPartnerSuggestion(partner) {
                return {
                    classList: "o-discuss-ChannelSelector-suggestion",
                    label: partner.name,
                    partner: partner,
                    isOnline: partner.isOnline,
                    lastSeen: partner.lastSeen,
                    isFavorite: this.isContactFavorite(partner.id)
                };
            },
            
            // 记录搜索
            recordSearch(searchTerm) {
                try {
                    const history = this.enhancedState.searchHistory;
                    
                    // 移除重复项
                    const existingIndex = history.indexOf(searchTerm);
                    if (existingIndex !== -1) {
                        history.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    history.unshift(searchTerm);
                    
                    // 限制历史记录数量
                    if (history.length > 20) {
                        history.splice(20);
                    }
                    
                    // 保存到本地存储
                    this.saveSearchHistory();
                    
                } catch (error) {
                    console.warn('记录搜索失败:', error);
                }
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('channel_selector_preferences') || '{}'
                    );
                    
                    this.enhancedState.searchHistory = prefs.searchHistory || [];
                    this.enhancedState.favoriteChannels = prefs.favoriteChannels || [];
                    this.enhancedState.recentChats = prefs.recentChats || [];
                    this.enhancedState.filterMode = prefs.filterMode || 'all';
                    this.enhancedState.sortMode = prefs.sortMode || 'relevance';
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const prefs = {
                        searchHistory: this.enhancedState.searchHistory,
                        favoriteChannels: this.enhancedState.favoriteChannels,
                        recentChats: this.enhancedState.recentChats,
                        filterMode: this.enhancedState.filterMode,
                        sortMode: this.enhancedState.sortMode
                    };
                    
                    localStorage.setItem('channel_selector_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 切换收藏状态
            toggleFavorite(item) {
                if (this.props.category.id === "channels") {
                    this.toggleChannelFavorite(item.channelId);
                } else if (this.props.category.id === "chats") {
                    this.toggleContactFavorite(item.partner.id);
                }
            },
            
            // 切换频道收藏
            toggleChannelFavorite(channelId) {
                const favorites = this.enhancedState.favoriteChannels;
                const index = favorites.findIndex(ch => ch.id === channelId);
                
                if (index !== -1) {
                    favorites.splice(index, 1);
                } else {
                    const channel = this.store.Thread.get({ id: channelId, model: 'discuss.channel' });
                    if (channel) {
                        favorites.push({
                            id: channel.id,
                            name: channel.name,
                            timestamp: Date.now()
                        });
                    }
                }
                
                this.saveUserPreferences();
            },
            
            // 获取选择器统计
            getSelectorStatistics() {
                return {
                    searchHistory: this.enhancedState.searchHistory.length,
                    favoriteChannels: this.enhancedState.favoriteChannels.length,
                    recentChats: this.enhancedState.recentChats.length,
                    currentFilter: this.enhancedState.filterMode,
                    currentSort: this.enhancedState.sortMode
                };
            }
        };
        
        // 替换原始组件
        __exports.ChannelSelector = EnhancedChannelSelector;
    }
};

// 应用频道选择器增强
ChannelSelectorEnhancer.enhanceChannelSelector();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 子组件集成
- 模板驱动渲染

### 2. 搜索功能
- 实时搜索建议
- 多种搜索策略
- 结果排序和过滤

### 3. 键盘交互
- 完整的键盘支持
- 热键处理机制
- 事件冲突避免

### 4. 状态管理
- 响应式状态更新
- 多选状态管理
- 加载状态指示

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 策略模式 (Strategy Pattern)
- 不同分类的处理策略
- 可配置的搜索行为

### 3. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 响应式数据更新

## 注意事项

1. **性能优化**: 避免频繁的搜索请求影响性能
2. **用户体验**: 提供流畅的搜索和选择体验
3. **数据一致性**: 确保搜索结果的准确性
4. **键盘导航**: 保证键盘操作的完整性

## 扩展建议

1. **高级搜索**: 实现更复杂的搜索和过滤功能
2. **搜索历史**: 添加搜索历史和建议功能
3. **收藏功能**: 实现频道和联系人收藏
4. **批量操作**: 支持批量选择和操作
5. **自定义过滤**: 允许用户自定义过滤条件

该组件为讨论应用提供了强大的频道和聊天对象选择功能，是用户发起对话的重要工具。

# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了对 Odoo 讨论应用Web环境中线程模型的补丁扩展，专门添加了折叠状态计数和固定状态更新处理功能。该补丁通过Odoo的补丁机制扩展了Thread模型，重写了setup和onPinStateUpdated方法，添加了foldStateCount属性和智能线程切换逻辑，为Web环境中的讨论应用提供了更完善的线程状态管理和用户体验优化，是讨论应用线程模型的重要增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/thread_model_patch.js`
- **行数**: 34
- **模块**: `@mail/discuss/core/web/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_model'  // 线程模型
'@web/core/utils/patch'           // 补丁工具
```

## 补丁定义

### Thread 补丁

```javascript
patch(Thread.prototype, {
    setup() {
        super.setup(...arguments);
        this.foldStateCount = 0;
    },
    onPinStateUpdated() {
        super.onPinStateUpdated();
        if (!this.displayToSelf && !this.isLocallyPinned && this.eq(this.store.discuss.thread)) {
            if (this.store.discuss.isActive) {
                const newThread =
                    this.store.discuss.channels.threads.find(
                        (thread) => thread.displayToSelf || thread.isLocallyPinned
                    ) || this.store.inbox;
                newThread.setAsDiscussThread();
            } else {
                this.store.discuss.thread = undefined;
            }
        }
    },
});
```

**补丁特性**:
- 扩展线程模型功能
- 添加折叠状态计数
- 智能线程切换逻辑
- 状态更新处理

## 核心功能

### 1. 模型初始化

```javascript
setup() {
    super.setup(...arguments);
    this.foldStateCount = 0;
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **折叠计数**: 初始化折叠状态计数器为0
- **状态准备**: 为折叠状态管理做准备
- **属性扩展**: 扩展线程模型的属性

### 2. 固定状态更新处理

```javascript
onPinStateUpdated() {
    super.onPinStateUpdated();
    if (!this.displayToSelf && !this.isLocallyPinned && this.eq(this.store.discuss.thread)) {
        if (this.store.discuss.isActive) {
            const newThread =
                this.store.discuss.channels.threads.find(
                    (thread) => thread.displayToSelf || thread.isLocallyPinned
                ) || this.store.inbox;
            newThread.setAsDiscussThread();
        } else {
            this.store.discuss.thread = undefined;
        }
    }
}
```

**状态更新功能**:
- **父类调用**: 保持原有的状态更新逻辑
- **条件检查**: 检查线程是否不再显示给自己且未本地固定
- **当前线程检查**: 检查是否为当前讨论线程
- **智能切换**: 在讨论应用激活时智能切换到其他可用线程
- **状态清理**: 在讨论应用未激活时清理线程状态

## 使用场景

### 1. 线程模型增强

```javascript
// 线程模型增强功能
const ThreadModelPatchEnhancer = {
    enhanceThreadModelPatch: () => {
        const EnhancedThreadPatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有的折叠状态计数
                this.foldStateCount = 0;
                
                // 增强的状态管理
                this.enhancedState = {
                    lastFoldTime: null,
                    foldHistory: [],
                    pinHistory: [],
                    switchHistory: [],
                    userInteractions: 0
                };
                
                // 增强的计数器
                this.enhancedCounters = {
                    foldCount: 0,
                    unfoldCount: 0,
                    pinCount: 0,
                    unpinCount: 0,
                    switchCount: 0
                };
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置状态监控
                this.setupStateMonitoring();
                
                // 设置历史记录
                this.setupHistoryTracking();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置自动优化
                this.setupAutoOptimization();
            },
            
            // 增强的固定状态更新处理
            onPinStateUpdated() {
                try {
                    // 记录状态更新开始
                    this.recordStateUpdate('pin_state_updated', 'start');
                    
                    // 调用父类方法
                    super.onPinStateUpdated();
                    
                    // 记录固定状态变化
                    this.recordPinStateChange();
                    
                    // 增强的线程切换逻辑
                    if (!this.displayToSelf && !this.isLocallyPinned && this.eq(this.store.discuss.thread)) {
                        await this.handleEnhancedThreadSwitch();
                    }
                    
                    // 触发状态更新事件
                    this.triggerStateUpdateEvent();
                    
                    // 记录状态更新完成
                    this.recordStateUpdate('pin_state_updated', 'complete');
                    
                } catch (error) {
                    console.error('固定状态更新失败:', error);
                    this.recordStateUpdate('pin_state_updated', 'error', error);
                }
            },
            
            // 增强的线程切换处理
            async handleEnhancedThreadSwitch() {
                try {
                    if (this.store.discuss.isActive) {
                        // 智能选择新线程
                        const newThread = await this.selectOptimalThread();
                        
                        if (newThread) {
                            // 记录线程切换
                            this.recordThreadSwitch(this, newThread);
                            
                            // 执行切换
                            await this.executeThreadSwitch(newThread);
                            
                            // 后处理
                            await this.postSwitchProcessing(newThread);
                        } else {
                            // 没有可用线程时的处理
                            await this.handleNoAvailableThread();
                        }
                    } else {
                        // 讨论应用未激活时清理状态
                        this.cleanupInactiveState();
                    }
                } catch (error) {
                    console.error('线程切换处理失败:', error);
                    this.handleSwitchError(error);
                }
            },
            
            // 智能选择最优线程
            async selectOptimalThread() {
                // 获取候选线程
                const candidates = this.getCandidateThreads();
                
                if (candidates.length === 0) {
                    return this.store.inbox;
                }
                
                // 应用选择策略
                const selectedThread = this.applySelectionStrategy(candidates);
                
                return selectedThread;
            },
            
            // 获取候选线程
            getCandidateThreads() {
                const threads = this.store.discuss.channels.threads.filter(
                    thread => thread.displayToSelf || thread.isLocallyPinned
                );
                
                // 按优先级排序
                return threads.sort((a, b) => this.compareThreadPriority(a, b));
            },
            
            // 比较线程优先级
            compareThreadPriority(threadA, threadB) {
                // 固定线程优先级更高
                if (threadA.isLocallyPinned && !threadB.isLocallyPinned) {
                    return -1;
                }
                if (!threadA.isLocallyPinned && threadB.isLocallyPinned) {
                    return 1;
                }
                
                // 未读消息多的优先级更高
                const unreadA = threadA.importantCounter || 0;
                const unreadB = threadB.importantCounter || 0;
                if (unreadA !== unreadB) {
                    return unreadB - unreadA;
                }
                
                // 最近活动的优先级更高
                const activityA = new Date(threadA.lastActivity || 0).getTime();
                const activityB = new Date(threadB.lastActivity || 0).getTime();
                return activityB - activityA;
            },
            
            // 应用选择策略
            applySelectionStrategy(candidates) {
                const strategy = this.getUserSelectionStrategy();
                
                switch (strategy) {
                    case 'most_recent':
                        return this.selectMostRecentThread(candidates);
                    case 'most_unread':
                        return this.selectMostUnreadThread(candidates);
                    case 'most_active':
                        return this.selectMostActiveThread(candidates);
                    case 'user_preference':
                        return this.selectByUserPreference(candidates);
                    default:
                        return candidates[0];
                }
            },
            
            // 执行线程切换
            async executeThreadSwitch(newThread) {
                try {
                    // 预切换处理
                    await this.preSwitchProcessing(newThread);
                    
                    // 执行切换
                    newThread.setAsDiscussThread();
                    
                    // 更新计数器
                    this.enhancedCounters.switchCount++;
                    
                    // 记录切换成功
                    this.recordSwitchSuccess(newThread);
                    
                } catch (error) {
                    console.error('执行线程切换失败:', error);
                    throw error;
                }
            },
            
            // 后切换处理
            async postSwitchProcessing(newThread) {
                try {
                    // 更新用户偏好
                    await this.updateUserPreference(newThread);
                    
                    // 预加载线程数据
                    await this.preloadThreadData(newThread);
                    
                    // 更新最近访问
                    await this.updateRecentAccess(newThread);
                    
                    // 触发切换完成事件
                    this.triggerSwitchCompleteEvent(newThread);
                    
                } catch (error) {
                    console.warn('后切换处理失败:', error);
                }
            },
            
            // 处理无可用线程
            async handleNoAvailableThread() {
                try {
                    // 使用收件箱作为默认
                    const inbox = this.store.inbox;
                    
                    if (inbox) {
                        inbox.setAsDiscussThread();
                        this.recordFallbackToInbox();
                    } else {
                        // 创建临时线程或显示空状态
                        this.handleEmptyState();
                    }
                } catch (error) {
                    console.error('处理无可用线程失败:', error);
                }
            },
            
            // 清理非激活状态
            cleanupInactiveState() {
                try {
                    this.store.discuss.thread = undefined;
                    this.recordStateCleanup();
                } catch (error) {
                    console.error('清理非激活状态失败:', error);
                }
            },
            
            // 记录固定状态变化
            recordPinStateChange() {
                try {
                    const change = {
                        threadId: this.id,
                        threadName: this.name,
                        isPinned: this.isLocallyPinned,
                        displayToSelf: this.displayToSelf,
                        timestamp: Date.now()
                    };
                    
                    this.enhancedState.pinHistory.push(change);
                    
                    // 限制历史记录大小
                    if (this.enhancedState.pinHistory.length > 100) {
                        this.enhancedState.pinHistory.splice(0, 50);
                    }
                    
                    // 更新计数器
                    if (this.isLocallyPinned) {
                        this.enhancedCounters.pinCount++;
                    } else {
                        this.enhancedCounters.unpinCount++;
                    }
                    
                } catch (error) {
                    console.warn('记录固定状态变化失败:', error);
                }
            },
            
            // 记录线程切换
            recordThreadSwitch(fromThread, toThread) {
                try {
                    const switchRecord = {
                        fromThreadId: fromThread.id,
                        fromThreadName: fromThread.name,
                        toThreadId: toThread.id,
                        toThreadName: toThread.name,
                        reason: 'pin_state_updated',
                        timestamp: Date.now()
                    };
                    
                    this.enhancedState.switchHistory.push(switchRecord);
                    
                    // 限制历史记录大小
                    if (this.enhancedState.switchHistory.length > 50) {
                        this.enhancedState.switchHistory.splice(0, 25);
                    }
                    
                } catch (error) {
                    console.warn('记录线程切换失败:', error);
                }
            },
            
            // 记录状态更新
            recordStateUpdate(updateType, status, error = null) {
                try {
                    const updates = JSON.parse(
                        localStorage.getItem('thread_state_updates') || '[]'
                    );
                    
                    updates.push({
                        threadId: this.id,
                        updateType,
                        status,
                        error: error ? error.message : null,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个更新
                    if (updates.length > 100) {
                        updates.splice(0, updates.length - 100);
                    }
                    
                    localStorage.setItem('thread_state_updates', JSON.stringify(updates));
                } catch (error) {
                    console.warn('记录状态更新失败:', error);
                }
            },
            
            // 获取用户选择策略
            getUserSelectionStrategy() {
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('thread_selection_preferences') || '{}'
                    );
                    
                    return preferences.strategy || 'most_recent';
                } catch (error) {
                    return 'most_recent';
                }
            },
            
            // 选择最近的线程
            selectMostRecentThread(candidates) {
                return candidates.sort((a, b) => {
                    const timeA = new Date(a.lastActivity || 0).getTime();
                    const timeB = new Date(b.lastActivity || 0).getTime();
                    return timeB - timeA;
                })[0];
            },
            
            // 选择未读最多的线程
            selectMostUnreadThread(candidates) {
                return candidates.sort((a, b) => {
                    const unreadA = a.importantCounter || 0;
                    const unreadB = b.importantCounter || 0;
                    return unreadB - unreadA;
                })[0];
            },
            
            // 触发状态更新事件
            triggerStateUpdateEvent() {
                try {
                    this.env.bus.trigger('thread:pin_state_updated', {
                        thread: this,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发状态更新事件失败:', error);
                }
            },
            
            // 触发切换完成事件
            triggerSwitchCompleteEvent(newThread) {
                try {
                    this.env.bus.trigger('thread:switch_complete', {
                        fromThread: this,
                        toThread: newThread,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('触发切换完成事件失败:', error);
                }
            },
            
            // 获取线程状态统计
            getThreadStateStatistics() {
                return {
                    foldStateCount: this.foldStateCount,
                    enhancedCounters: { ...this.enhancedCounters },
                    historyLengths: {
                        pinHistory: this.enhancedState.pinHistory.length,
                        switchHistory: this.enhancedState.switchHistory.length
                    },
                    userInteractions: this.enhancedState.userInteractions,
                    lastFoldTime: this.enhancedState.lastFoldTime
                };
            }
        };
        
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程模型补丁增强
ThreadModelPatchEnhancer.enhanceThreadModelPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 状态管理
- 折叠状态计数
- 智能状态切换
- 状态历史跟踪

### 3. 智能切换
- 条件检查逻辑
- 最优线程选择
- 用户体验优化

### 4. 错误处理
- 完善的错误处理机制
- 状态恢复能力
- 异常情况处理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 状态模式 (State Pattern)
- 线程状态的管理
- 状态转换控制

### 3. 策略模式 (Strategy Pattern)
- 线程选择策略
- 可配置的行为模式

## 注意事项

1. **状态一致性**: 确保线程状态的一致性和正确性
2. **性能优化**: 避免频繁的状态更新影响性能
3. **用户体验**: 提供流畅的线程切换体验
4. **错误恢复**: 提供完善的错误恢复机制

## 扩展建议

1. **更多状态**: 添加更多类型的线程状态管理
2. **智能预测**: 实现基于用户行为的智能预测
3. **性能监控**: 添加详细的性能监控功能
4. **自定义策略**: 允许用户自定义切换策略
5. **历史分析**: 提供状态变化的历史分析

该补丁为讨论应用的线程模型提供了重要的状态管理和智能切换功能，提升了用户的使用体验。

# Messaging Menu Patch - 消息菜单补丁

## 概述

`messaging_menu_patch.js` 实现了对 Odoo 讨论应用Web环境中消息菜单组件的补丁扩展，专门添加了频道选择器集成、频道数据预获取、新消息处理和计数器优化等功能。该补丁通过Odoo的补丁机制扩展了MessagingMenu组件，重写了打开前处理、新消息点击和计数器计算方法，为用户提供了更准确的未读消息计数和更便捷的消息管理体验，是讨论应用Web环境中消息菜单功能的重要增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/messaging_menu_patch.js`
- **行数**: 55
- **模块**: `@mail/discuss/core/web/messaging_menu_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/public_web/messaging_menu'      // 消息菜单组件
'@mail/discuss/core/web/channel_selector'   // 频道选择器组件
'@web/core/utils/patch'                     // 补丁工具
```

## 补丁定义

### 组件注册

```javascript
Object.assign(MessagingMenu.components, { ChannelSelector });
```

**组件注册功能**:
- 将ChannelSelector添加到MessagingMenu组件
- 扩展消息菜单的组件可用性
- 支持频道选择功能

### MessagingMenu 补丁

```javascript
patch(MessagingMenu.prototype, {
    beforeOpen() {
        const res = super.beforeOpen(...arguments);
        this.store.channels.fetch();
        return res;
    },
    onClickNewMessage() {
        if (this.ui.isSmall || this.env.inDiscussApp) {
            Object.assign(this.state, { adding: "chat" });
        } else {
            this.store.openNewMessage();
            this.dropdown.close();
        }
    },
    get counter() {
        // 复杂的计数器计算逻辑
    },
});
```

**补丁特性**:
- 扩展消息菜单功能
- 添加频道数据预获取
- 优化新消息处理
- 重写计数器计算

## 核心功能

### 1. 打开前处理

```javascript
beforeOpen() {
    const res = super.beforeOpen(...arguments);
    this.store.channels.fetch();
    return res;
}
```

**预处理功能**:
- **父类调用**: 保持原有的打开前处理逻辑
- **频道获取**: 在菜单打开前预获取频道数据
- **数据刷新**: 确保频道数据的最新性
- **返回结果**: 返回父类处理结果

### 2. 新消息点击处理

```javascript
onClickNewMessage() {
    if (this.ui.isSmall || this.env.inDiscussApp) {
        Object.assign(this.state, { adding: "chat" });
    } else {
        this.store.openNewMessage();
        this.dropdown.close();
    }
}
```

**点击处理功能**:
- **设备检测**: 检测小屏幕设备或讨论应用环境
- **状态切换**: 小屏幕时切换到聊天添加状态
- **新消息打开**: 大屏幕时直接打开新消息界面
- **菜单关闭**: 操作完成后关闭下拉菜单

### 3. 计数器计算

```javascript
get counter() {
    const count = super.counter;
    const channelsContribution =
        this.store.channels.status !== "fetched"
            ? this.store.initChannelsUnreadCounter
            : Object.values(this.store.Thread.records).filter(
                  (thread) =>
                      thread.displayToSelf &&
                      !thread.isMuted &&
                      (thread.selfMember?.message_unread_counter ||
                          thread.message_needaction_counter)
              ).length;
    // Needactions are already counted in the super call, but we want to discard them for channel so that there is only +1 per channel.
    const channelsNeedactionCounter = Object.values(this.store.Thread.records).reduce(
        (acc, thread) => {
            return (
                acc +
                (thread.model === "discuss.channel" ? thread.message_needaction_counter : 0)
            );
        },
        0
    );
    return count + channelsContribution - channelsNeedactionCounter;
}
```

**计数器功能**:
- **基础计数**: 获取父类的基础计数
- **频道贡献**: 计算频道对总计数的贡献
- **状态检查**: 根据频道数据获取状态选择计算方式
- **过滤条件**: 仅计算显示给自己、未静音的线程
- **去重处理**: 避免频道需要操作消息的重复计数

## 使用场景

### 1. 消息菜单增强

```javascript
// 消息菜单增强功能
const MessagingMenuPatchEnhancer = {
    enhanceMessagingMenuPatch: () => {
        const EnhancedMessagingMenuPatch = {
            // 增强的打开前处理
            beforeOpen() {
                const res = super.beforeOpen(...arguments);
                
                // 原有的频道获取
                this.store.channels.fetch();
                
                // 增强的预处理
                this.preloadMenuData();
                this.updateMenuStatistics();
                this.recordMenuOpen();
                
                return res;
            },
            
            // 预加载菜单数据
            async preloadMenuData() {
                try {
                    // 预加载最近聊天
                    await this.store.getRecentChats();
                    
                    // 预加载重要消息
                    await this.store.getImportantMessages();
                    
                    // 预加载用户状态
                    await this.store.updateUserStatuses();
                    
                    // 预加载通知设置
                    await this.store.getNotificationSettings();
                    
                } catch (error) {
                    console.warn('预加载菜单数据失败:', error);
                }
            },
            
            // 增强的新消息点击处理
            onClickNewMessage() {
                try {
                    // 记录点击事件
                    this.recordNewMessageClick();
                    
                    // 检查用户权限
                    if (!this.canCreateNewMessage()) {
                        this.showPermissionDeniedMessage();
                        return;
                    }
                    
                    // 原有逻辑
                    if (this.ui.isSmall || this.env.inDiscussApp) {
                        Object.assign(this.state, { adding: "chat" });
                        
                        // 增强的小屏幕处理
                        this.setupMobileNewMessage();
                    } else {
                        this.store.openNewMessage();
                        this.dropdown.close();
                        
                        // 增强的桌面处理
                        this.setupDesktopNewMessage();
                    }
                    
                } catch (error) {
                    console.error('处理新消息点击失败:', error);
                    this.showErrorMessage('无法创建新消息');
                }
            },
            
            // 增强的计数器计算
            get counter() {
                try {
                    const count = super.counter;
                    
                    // 获取频道贡献
                    const channelsContribution = this.calculateChannelsContribution();
                    
                    // 获取频道需要操作计数
                    const channelsNeedactionCounter = this.calculateChannelsNeedactionCounter();
                    
                    // 获取自定义计数
                    const customCounter = this.calculateCustomCounter();
                    
                    // 应用过滤器
                    const filteredCount = this.applyCounterFilters(
                        count + channelsContribution - channelsNeedactionCounter + customCounter
                    );
                    
                    // 记录计数器更新
                    this.recordCounterUpdate(filteredCount);
                    
                    return filteredCount;
                    
                } catch (error) {
                    console.error('计算计数器失败:', error);
                    return super.counter;
                }
            },
            
            // 计算频道贡献
            calculateChannelsContribution() {
                if (this.store.channels.status !== "fetched") {
                    return this.store.initChannelsUnreadCounter;
                }
                
                const threads = Object.values(this.store.Thread.records);
                
                return threads.filter(thread => {
                    // 基本过滤条件
                    if (!thread.displayToSelf || thread.isMuted) {
                        return false;
                    }
                    
                    // 检查未读消息或需要操作的消息
                    const hasUnread = thread.selfMember?.message_unread_counter > 0;
                    const hasNeedaction = thread.message_needaction_counter > 0;
                    
                    if (!hasUnread && !hasNeedaction) {
                        return false;
                    }
                    
                    // 应用自定义过滤器
                    return this.applyCustomThreadFilters(thread);
                }).length;
            },
            
            // 计算频道需要操作计数
            calculateChannelsNeedactionCounter() {
                return Object.values(this.store.Thread.records).reduce((acc, thread) => {
                    if (thread.model === "discuss.channel") {
                        return acc + (thread.message_needaction_counter || 0);
                    }
                    return acc;
                }, 0);
            },
            
            // 计算自定义计数
            calculateCustomCounter() {
                let customCount = 0;
                
                // 添加高优先级消息计数
                customCount += this.getHighPriorityMessageCount();
                
                // 添加提及计数
                customCount += this.getMentionCount();
                
                // 添加关注线程计数
                customCount += this.getFollowedThreadCount();
                
                return customCount;
            },
            
            // 应用自定义线程过滤器
            applyCustomThreadFilters(thread) {
                const filters = this.getActiveThreadFilters();
                
                return filters.every(filter => filter(thread));
            },
            
            // 获取活跃的线程过滤器
            getActiveThreadFilters() {
                const filters = [];
                
                // 时间过滤器
                const timeFilter = this.getTimeFilter();
                if (timeFilter) {
                    filters.push(timeFilter);
                }
                
                // 类型过滤器
                const typeFilter = this.getTypeFilter();
                if (typeFilter) {
                    filters.push(typeFilter);
                }
                
                // 优先级过滤器
                const priorityFilter = this.getPriorityFilter();
                if (priorityFilter) {
                    filters.push(priorityFilter);
                }
                
                return filters;
            },
            
            // 获取时间过滤器
            getTimeFilter() {
                const timeRange = this.getUserTimeFilterPreference();
                
                if (!timeRange) {
                    return null;
                }
                
                return (thread) => {
                    const lastActivity = new Date(thread.lastActivity || 0).getTime();
                    return lastActivity >= timeRange.start && lastActivity <= timeRange.end;
                };
            },
            
            // 应用计数器过滤器
            applyCounterFilters(count) {
                // 最大值限制
                const maxCount = this.getMaxCounterValue();
                if (count > maxCount) {
                    return maxCount;
                }
                
                // 最小值限制
                const minCount = this.getMinCounterValue();
                if (count < minCount) {
                    return minCount;
                }
                
                return count;
            },
            
            // 设置移动端新消息
            setupMobileNewMessage() {
                // 优化移动端体验
                this.optimizeMobileInterface();
                
                // 设置移动端快捷操作
                this.setupMobileShortcuts();
                
                // 记录移动端使用
                this.recordMobileUsage();
            },
            
            // 设置桌面端新消息
            setupDesktopNewMessage() {
                // 优化桌面端体验
                this.optimizeDesktopInterface();
                
                // 设置桌面端快捷键
                this.setupDesktopShortcuts();
                
                // 记录桌面端使用
                this.recordDesktopUsage();
            },
            
            // 检查是否可以创建新消息
            canCreateNewMessage() {
                const user = this.store.self;
                
                // 检查用户是否已登录
                if (!user) {
                    return false;
                }
                
                // 检查用户权限
                if (!user.isInternalUser) {
                    return false;
                }
                
                // 检查是否达到消息限制
                if (this.hasReachedMessageLimit()) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否达到消息限制
            hasReachedMessageLimit() {
                const dailyLimit = this.getDailyMessageLimit();
                const todayCount = this.getTodayMessageCount();
                
                return todayCount >= dailyLimit;
            },
            
            // 获取高优先级消息计数
            getHighPriorityMessageCount() {
                return Object.values(this.store.Message.records).filter(message => 
                    message.isHighPriority && !message.isRead
                ).length;
            },
            
            // 获取提及计数
            getMentionCount() {
                return Object.values(this.store.Message.records).filter(message => 
                    message.mentionsCurrentUser && !message.isRead
                ).length;
            },
            
            // 获取关注线程计数
            getFollowedThreadCount() {
                return Object.values(this.store.Thread.records).filter(thread => 
                    thread.isFollowing && thread.hasUnreadMessages
                ).length;
            },
            
            // 更新菜单统计
            updateMenuStatistics() {
                try {
                    const stats = {
                        openCount: this.getMenuOpenCount() + 1,
                        lastOpenTime: Date.now(),
                        totalThreads: Object.keys(this.store.Thread.records).length,
                        unreadCount: this.counter
                    };
                    
                    localStorage.setItem('messaging_menu_stats', JSON.stringify(stats));
                } catch (error) {
                    console.warn('更新菜单统计失败:', error);
                }
            },
            
            // 记录菜单打开
            recordMenuOpen() {
                try {
                    const opens = JSON.parse(
                        localStorage.getItem('messaging_menu_opens') || '[]'
                    );
                    
                    opens.push({
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        screenSize: `${screen.width}x${screen.height}`,
                        isSmall: this.ui.isSmall
                    });
                    
                    // 保留最近100个记录
                    if (opens.length > 100) {
                        opens.splice(0, opens.length - 100);
                    }
                    
                    localStorage.setItem('messaging_menu_opens', JSON.stringify(opens));
                } catch (error) {
                    console.warn('记录菜单打开失败:', error);
                }
            },
            
            // 记录新消息点击
            recordNewMessageClick() {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('new_message_clicks') || '[]'
                    );
                    
                    clicks.push({
                        timestamp: Date.now(),
                        isSmall: this.ui.isSmall,
                        inDiscussApp: this.env.inDiscussApp
                    });
                    
                    // 保留最近50个记录
                    if (clicks.length > 50) {
                        clicks.splice(0, clicks.length - 50);
                    }
                    
                    localStorage.setItem('new_message_clicks', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录新消息点击失败:', error);
                }
            },
            
            // 记录计数器更新
            recordCounterUpdate(count) {
                try {
                    const updates = JSON.parse(
                        localStorage.getItem('counter_updates') || '[]'
                    );
                    
                    updates.push({
                        count,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近200个记录
                    if (updates.length > 200) {
                        updates.splice(0, updates.length - 200);
                    }
                    
                    localStorage.setItem('counter_updates', JSON.stringify(updates));
                } catch (error) {
                    console.warn('记录计数器更新失败:', error);
                }
            },
            
            // 显示权限拒绝消息
            showPermissionDeniedMessage() {
                this.env.services.notification.add(
                    '您没有权限创建新消息',
                    { type: 'warning' }
                );
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 获取消息菜单统计
            getMessagingMenuStatistics() {
                try {
                    return {
                        opens: JSON.parse(localStorage.getItem('messaging_menu_opens') || '[]').length,
                        newMessageClicks: JSON.parse(localStorage.getItem('new_message_clicks') || '[]').length,
                        counterUpdates: JSON.parse(localStorage.getItem('counter_updates') || '[]').length,
                        currentCounter: this.counter,
                        lastOpenTime: JSON.parse(localStorage.getItem('messaging_menu_stats') || '{}').lastOpenTime
                    };
                } catch (error) {
                    return {
                        opens: 0,
                        newMessageClicks: 0,
                        counterUpdates: 0,
                        currentCounter: 0,
                        lastOpenTime: null
                    };
                }
            }
        };
        
        // 应用增强补丁
        patch(MessagingMenu.prototype, EnhancedMessagingMenuPatch);
    }
};

// 应用消息菜单补丁增强
MessagingMenuPatchEnhancer.enhanceMessagingMenuPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 数据预获取
- 菜单打开前数据刷新
- 提升用户体验
- 减少加载等待时间

### 3. 智能计数
- 复杂的计数器计算逻辑
- 避免重复计数
- 准确的未读消息统计

### 4. 响应式设计
- 设备类型检测
- 不同环境的处理策略
- 自适应用户界面

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同设备的处理策略
- 可配置的行为模式

### 3. 观察者模式 (Observer Pattern)
- 计数器的动态更新
- 状态变化的监听

## 注意事项

1. **性能优化**: 避免频繁的计数器计算影响性能
2. **数据一致性**: 确保计数器的准确性和一致性
3. **用户体验**: 提供流畅的菜单交互体验
4. **内存管理**: 及时清理事件监听器和缓存

## 扩展建议

1. **更多预加载**: 添加更多类型的数据预加载
2. **智能计数**: 实现更智能的计数器算法
3. **个性化**: 支持用户个性化菜单设置
4. **性能监控**: 实现菜单性能监控和优化
5. **离线支持**: 添加离线模式下的菜单功能

该补丁为讨论应用的消息菜单提供了重要的功能增强，特别是在计数器准确性和用户体验方面。

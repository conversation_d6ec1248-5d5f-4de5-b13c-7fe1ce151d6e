# Command Palette - 命令面板

## 概述

`command_palette.js` 实现了 Odoo 讨论应用Web环境中的命令面板功能，提供了用户和频道的快速搜索和访问能力。该文件通过注册表机制添加了"@"和"#"两个命名空间的命令提供者，分别用于搜索用户和频道，集成了讨论命令组件和即时状态显示，为用户提供了快速导航和操作的便捷入口，是讨论应用中重要的效率工具。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/command_palette.js`
- **行数**: 223
- **模块**: `@mail/discuss/core/web/command_palette`

## 依赖关系

```javascript
// 核心依赖
'@mail/utils/common/format'     // 格式化工具
'@odoo/owl'                     // OWL 框架
'@web/core/l10n/translation'    // 国际化
'@web/core/registry'            // 注册表
'@web/core/utils/urls'          // URL工具
'@mail/core/common/im_status'   // 即时状态组件
```

## 组件定义

### DiscussCommand 组件

```javascript
class DiscussCommand extends Component {
    static components = { ImStatus };
    static template = "mail.DiscussCommand";
    static props = {
        counter: { type: Number, optional: true },
        executeCommand: Function,
        imgUrl: String,
        name: String,
        persona: { type: Object, optional: true },
        channel: { type: Object, optional: true },
        searchValue: String,
        slots: Object,
    };
}
```

**组件特性**:
- 集成即时状态显示
- 支持计数器显示
- 支持头像URL显示
- 支持用户和频道对象

## 核心功能

### 1. 用户搜索命名空间 (@)

```javascript
commandSetupRegistry.add("@", {
    debounceDelay: 200,
    emptyMessage: _t("No user found"),
    name: _t("users"),
    placeholder: _t("Search for a user..."),
});
```

**用户搜索配置**:
- **防抖延迟**: 200ms防抖避免频繁搜索
- **空消息**: 无用户找到时的提示
- **名称**: 用户搜索的显示名称
- **占位符**: 搜索框的占位符文本

### 2. 用户命令提供者

```javascript
commandProviderRegistry.add("mail.partner", {
    namespace: "@",
    async provide(env, options) {
        const store = env.services["mail.store"];
        const commands = [];
        
        // 显示提及的频道
        if (!options.searchValue) {
            const mentionedChannels = store.getNeedactionChannels();
            // 处理前3个提及频道
        }
        
        // 搜索伙伴
        const searchResults = await store.searchPartners(options.searchValue);
        // 处理搜索结果
        
        return commands;
    }
});
```

**用户提供者功能**:
- **提及频道**: 无搜索词时显示需要操作的频道
- **伙伴搜索**: 根据搜索词搜索伙伴
- **去重处理**: 避免重复显示相同用户
- **动作定义**: 定义点击后的操作

### 3. 频道搜索命名空间 (#)

```javascript
commandSetupRegistry.add("#", {
    debounceDelay: 200,
    emptyMessage: _t("No channel found"),
    name: _t("channels"),
    placeholder: _t("Search for a channel..."),
});
```

**频道搜索配置**:
- **防抖延迟**: 200ms防抖避免频繁搜索
- **空消息**: 无频道找到时的提示
- **名称**: 频道搜索的显示名称
- **占位符**: 搜索框的占位符文本

### 4. 频道命令提供者

```javascript
commandProviderRegistry.add("discuss.channel", {
    namespace: "#",
    async provide(env, options) {
        const store = env.services["mail.store"];
        const commands = [];
        
        // 显示最近频道
        if (!options.searchValue) {
            const recentChannels = store.getRecentChannels();
            // 处理前3个最近频道
        }
        
        // 搜索频道
        const domain = [
            ["channel_type", "=", "channel"],
            ["name", "ilike", cleanTerm(options.searchValue)],
        ];
        const channelsData = await env.services.orm.searchRead("discuss.channel", domain, fields);
        
        return commands;
    }
});
```

**频道提供者功能**:
- **最近频道**: 无搜索词时显示最近访问的频道
- **频道搜索**: 根据搜索词搜索公共频道
- **群组搜索**: 搜索匹配的群组
- **加入操作**: 自动加入频道并打开

## 使用场景

### 1. 命令面板增强

```javascript
// 命令面板增强功能
const CommandPaletteEnhancer = {
    enhanceCommandPalette: () => {
        // 添加更多命名空间
        
        // 添加文件搜索命名空间 (/)
        commandSetupRegistry.add("/", {
            debounceDelay: 300,
            emptyMessage: _t("No file found"),
            name: _t("files"),
            placeholder: _t("Search for a file..."),
        });
        
        // 添加消息搜索命名空间 (?)
        commandSetupRegistry.add("?", {
            debounceDelay: 400,
            emptyMessage: _t("No message found"),
            name: _t("messages"),
            placeholder: _t("Search for a message..."),
        });
        
        // 添加操作命名空间 (!)
        commandSetupRegistry.add("!", {
            debounceDelay: 100,
            emptyMessage: _t("No action found"),
            name: _t("actions"),
            placeholder: _t("Search for an action..."),
        });
        
        // 文件搜索提供者
        commandProviderRegistry.add("mail.attachment", {
            namespace: "/",
            async provide(env, options) {
                const store = env.services["mail.store"];
                const commands = [];
                
                if (!options.searchValue) {
                    // 显示最近文件
                    const recentFiles = await store.getRecentAttachments();
                    recentFiles.slice(0, 5).forEach(file => {
                        commands.push({
                            Component: DiscussCommand,
                            action() {
                                window.open(file.url, '_blank');
                            },
                            name: file.name,
                            category: "recent_files",
                            props: {
                                imgUrl: file.thumbnailUrl || '/web/static/img/file.png',
                                counter: file.downloadCount
                            }
                        });
                    });
                } else {
                    // 搜索文件
                    const searchResults = await env.services.orm.searchRead(
                        "ir.attachment",
                        [
                            ["name", "ilike", cleanTerm(options.searchValue)],
                            ["res_model", "=", "discuss.channel"]
                        ],
                        ["name", "url", "mimetype", "file_size"],
                        { limit: 10 }
                    );
                    
                    searchResults.forEach(file => {
                        commands.push({
                            Component: DiscussCommand,
                            action() {
                                window.open(file.url, '_blank');
                            },
                            name: file.name,
                            props: {
                                imgUrl: this.getFileIcon(file.mimetype),
                                counter: Math.round(file.file_size / 1024) // KB
                            }
                        });
                    });
                }
                
                return commands;
            }
        });
        
        // 消息搜索提供者
        commandProviderRegistry.add("mail.message", {
            namespace: "?",
            async provide(env, options) {
                const store = env.services["mail.store"];
                const commands = [];
                
                if (options.searchValue && options.searchValue.length >= 3) {
                    try {
                        const searchResults = await env.services.orm.call(
                            "mail.message",
                            "search_messages",
                            [cleanTerm(options.searchValue)],
                            { limit: 10 }
                        );
                        
                        const { Message: messages = [] } = store.insert(searchResults);
                        
                        messages.forEach(message => {
                            commands.push({
                                Component: DiscussCommand,
                                action() {
                                    // 跳转到消息所在线程
                                    if (message.thread) {
                                        message.thread.open();
                                        // 滚动到消息位置
                                        setTimeout(() => {
                                            this.scrollToMessage(message.id);
                                        }, 500);
                                    }
                                },
                                name: this.truncateMessage(message.body, 50),
                                category: "messages",
                                props: {
                                    imgUrl: message.author?.avatarUrl || '/web/static/img/user.png',
                                    persona: message.author,
                                    counter: message.thread?.importantCounter
                                }
                            });
                        });
                    } catch (error) {
                        console.error('搜索消息失败:', error);
                    }
                }
                
                return commands;
            }
        });
        
        // 操作命令提供者
        commandProviderRegistry.add("discuss.action", {
            namespace: "!",
            async provide(env, options) {
                const commands = [];
                const store = env.services["mail.store"];
                
                const actions = [
                    {
                        name: "Create Channel",
                        description: "Create a new public channel",
                        icon: "fa-plus",
                        action: () => this.openCreateChannelDialog()
                    },
                    {
                        name: "Start Chat",
                        description: "Start a new private chat",
                        icon: "fa-comment",
                        action: () => this.openStartChatDialog()
                    },
                    {
                        name: "Join Channel",
                        description: "Join an existing channel",
                        icon: "fa-sign-in",
                        action: () => this.openJoinChannelDialog()
                    },
                    {
                        name: "Settings",
                        description: "Open discuss settings",
                        icon: "fa-cog",
                        action: () => this.openSettingsDialog()
                    },
                    {
                        name: "Notifications",
                        description: "Manage notification settings",
                        icon: "fa-bell",
                        action: () => this.openNotificationSettings()
                    },
                    {
                        name: "Export Data",
                        description: "Export chat history",
                        icon: "fa-download",
                        action: () => this.openExportDialog()
                    }
                ];
                
                const filteredActions = options.searchValue 
                    ? actions.filter(action => 
                        action.name.toLowerCase().includes(options.searchValue.toLowerCase()) ||
                        action.description.toLowerCase().includes(options.searchValue.toLowerCase())
                    )
                    : actions;
                
                filteredActions.forEach(actionItem => {
                    commands.push({
                        Component: DiscussCommand,
                        action: actionItem.action,
                        name: actionItem.name,
                        category: "actions",
                        props: {
                            imgUrl: `/web/static/img/icons/${actionItem.icon}.png`,
                            counter: undefined
                        }
                    });
                });
                
                return commands;
            }
        });
        
        // 增强的讨论命令组件
        const EnhancedDiscussCommand = class extends DiscussCommand {
            // 截断消息文本
            truncateMessage(text, maxLength) {
                if (!text) return '';
                
                // 移除HTML标签
                const plainText = text.replace(/<[^>]*>/g, '');
                
                if (plainText.length <= maxLength) {
                    return plainText;
                }
                
                return plainText.substring(0, maxLength) + '...';
            },
            
            // 获取文件图标
            getFileIcon(mimetype) {
                const iconMap = {
                    'application/pdf': '/web/static/img/file-pdf.png',
                    'application/msword': '/web/static/img/file-doc.png',
                    'application/vnd.ms-excel': '/web/static/img/file-xls.png',
                    'application/vnd.ms-powerpoint': '/web/static/img/file-ppt.png',
                    'text/plain': '/web/static/img/file-txt.png',
                    'image/jpeg': '/web/static/img/file-img.png',
                    'image/png': '/web/static/img/file-img.png',
                    'image/gif': '/web/static/img/file-img.png'
                };
                
                return iconMap[mimetype] || '/web/static/img/file.png';
            },
            
            // 滚动到消息
            scrollToMessage(messageId) {
                const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
                if (messageElement) {
                    messageElement.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'center' 
                    });
                    
                    // 高亮消息
                    messageElement.classList.add('o-highlighted');
                    setTimeout(() => {
                        messageElement.classList.remove('o-highlighted');
                    }, 3000);
                }
            },
            
            // 打开创建频道对话框
            openCreateChannelDialog() {
                this.env.services.dialog.add(CreateChannelDialog, {
                    onChannelCreated: (channel) => {
                        channel.open();
                    }
                });
            },
            
            // 打开开始聊天对话框
            openStartChatDialog() {
                this.env.services.dialog.add(StartChatDialog, {
                    onChatStarted: (chat) => {
                        chat.open();
                    }
                });
            },
            
            // 打开加入频道对话框
            openJoinChannelDialog() {
                this.env.services.dialog.add(JoinChannelDialog, {
                    onChannelJoined: (channel) => {
                        channel.open();
                    }
                });
            },
            
            // 打开设置对话框
            openSettingsDialog() {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'res.users',
                    res_id: this.env.services['mail.store'].self.userId,
                    views: [[false, 'form']],
                    target: 'new'
                });
            },
            
            // 打开通知设置
            openNotificationSettings() {
                this.env.services.action.doAction({
                    name: 'Notification Settings',
                    type: 'ir.actions.client',
                    tag: 'discuss_notification_settings',
                    target: 'new'
                });
            },
            
            // 打开导出对话框
            openExportDialog() {
                this.env.services.dialog.add(ExportDataDialog, {
                    onExportComplete: (exportData) => {
                        this.downloadExportData(exportData);
                    }
                });
            },
            
            // 下载导出数据
            downloadExportData(exportData) {
                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `discuss_export_${Date.now()}.json`;
                a.click();
                
                URL.revokeObjectURL(url);
            }
        };
        
        // 替换原始组件
        __exports.DiscussCommand = EnhancedDiscussCommand;
    }
};

// 应用命令面板增强
CommandPaletteEnhancer.enhanceCommandPalette();
```

## 技术特点

### 1. 注册表机制
- 命令设置注册表
- 命令提供者注册表
- 命名空间管理

### 2. 异步搜索
- 防抖机制避免频繁请求
- 异步数据获取
- 结果缓存优化

### 3. 组件化设计
- 可复用的命令组件
- 清晰的属性定义
- 模板驱动渲染

### 4. 智能排序
- 建议服务排序
- 相关性排序
- 去重处理

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 命令的注册和管理
- 动态命令配置

### 2. 提供者模式 (Provider Pattern)
- 命令提供者的抽象
- 统一的提供接口

### 3. 命令模式 (Command Pattern)
- 操作的命令化封装
- 统一的执行接口

## 注意事项

1. **性能优化**: 使用防抖机制避免频繁搜索
2. **用户体验**: 提供清晰的搜索反馈和结果
3. **数据一致性**: 确保搜索结果的准确性
4. **内存管理**: 及时清理搜索结果和缓存

## 扩展建议

1. **更多命名空间**: 添加更多类型的搜索命名空间
2. **智能建议**: 基于用户行为的智能建议
3. **快捷键**: 实现更多的键盘快捷键
4. **搜索历史**: 记录和管理搜索历史
5. **自定义命令**: 允许用户自定义命令

该文件为讨论应用提供了强大的命令面板功能，是用户快速导航和操作的重要工具。

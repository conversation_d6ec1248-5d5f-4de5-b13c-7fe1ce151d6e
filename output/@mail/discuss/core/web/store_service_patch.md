# Store Service Patch - 存储服务补丁

## 概述

`store_service_patch.js` 实现了对 Odoo 讨论应用Web环境中存储服务的补丁扩展，专门添加了频道未读计数器初始化和讨论应用激活时的频道数据获取功能。该补丁通过Odoo的补丁机制扩展了Store服务，重写了setup和onStarted方法，添加了initChannelsUnreadCounter属性和条件性频道数据获取，为Web环境中的讨论应用提供了更可靠的数据初始化和状态管理，是讨论应用存储服务的重要增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/store_service_patch.js`
- **行数**: 28
- **模块**: `@mail/discuss/core/web/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/store_service'  // 存储服务
'@web/core/utils/patch'            // 补丁工具
```

## 补丁定义

### Store 补丁

```javascript
/** @type {import("models").Store} */
const StorePatch = {
    setup() {
        super.setup(...arguments);
        this.initChannelsUnreadCounter = 0;
    },
    onStarted() {
        super.onStarted();
        if (this.discuss.isActive) {
            this.channels.fetch();
        }
    },
};
patch(Store.prototype, StorePatch);
```

**补丁特性**:
- 扩展存储服务功能
- 添加未读计数器初始化
- 条件性频道数据获取
- 保持原有服务逻辑

## 核心功能

### 1. 服务初始化

```javascript
setup() {
    super.setup(...arguments);
    this.initChannelsUnreadCounter = 0;
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **计数器初始化**: 初始化频道未读计数器为0
- **状态准备**: 为后续的计数器使用做准备
- **默认值设置**: 提供计数器的默认值

### 2. 启动后处理

```javascript
onStarted() {
    super.onStarted();
    if (this.discuss.isActive) {
        this.channels.fetch();
    }
}
```

**启动处理功能**:
- **父类调用**: 保持原有的启动后处理逻辑
- **状态检查**: 检查讨论应用是否处于激活状态
- **条件获取**: 仅在讨论应用激活时获取频道数据
- **性能优化**: 避免不必要的数据获取

## 使用场景

### 1. 存储服务增强

```javascript
// 存储服务增强功能
const StoreServicePatchEnhancer = {
    enhanceStoreServicePatch: () => {
        const EnhancedStorePatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有的未读计数器初始化
                this.initChannelsUnreadCounter = 0;
                
                // 增强的初始化
                this.enhancedCounters = {
                    totalUnread: 0,
                    channelUnread: 0,
                    chatUnread: 0,
                    mentionUnread: 0,
                    needactionUnread: 0
                };
                
                this.initializationState = {
                    isInitialized: false,
                    initStartTime: Date.now(),
                    initEndTime: null,
                    initErrors: []
                };
                
                this.performanceMetrics = {
                    setupTime: 0,
                    fetchTime: 0,
                    totalInitTime: 0
                };
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置数据验证
                this.setupDataValidation();
                
                // 设置错误处理
                this.setupErrorHandling();
                
                // 设置缓存管理
                this.setupCacheManagement();
                
                // 设置状态持久化
                this.setupStatePersistence();
            },
            
            // 增强的启动后处理
            async onStarted() {
                const startTime = performance.now();
                
                try {
                    // 调用父类方法
                    super.onStarted();
                    
                    // 记录启动开始
                    this.recordStartupEvent('started');
                    
                    // 检查讨论应用状态
                    if (this.discuss.isActive) {
                        await this.enhancedChannelsFetch();
                    }
                    
                    // 初始化完成后的处理
                    await this.postInitializationSetup();
                    
                    // 标记初始化完成
                    this.markInitializationComplete();
                    
                } catch (error) {
                    console.error('存储服务启动失败:', error);
                    this.handleInitializationError(error);
                } finally {
                    // 记录性能指标
                    const endTime = performance.now();
                    this.performanceMetrics.totalInitTime = endTime - startTime;
                    this.recordPerformanceMetrics();
                }
            },
            
            // 增强的频道获取
            async enhancedChannelsFetch() {
                const fetchStartTime = performance.now();
                
                try {
                    // 预检查
                    if (!this.canFetchChannels()) {
                        console.warn('无法获取频道数据，跳过获取');
                        return;
                    }
                    
                    // 显示加载状态
                    this.setLoadingState(true);
                    
                    // 获取频道数据
                    await this.channels.fetch();
                    
                    // 验证获取的数据
                    this.validateChannelsData();
                    
                    // 更新计数器
                    this.updateEnhancedCounters();
                    
                    // 触发数据获取完成事件
                    this.triggerDataFetchedEvent();
                    
                } catch (error) {
                    console.error('获取频道数据失败:', error);
                    this.handleFetchError(error);
                } finally {
                    this.setLoadingState(false);
                    
                    const fetchEndTime = performance.now();
                    this.performanceMetrics.fetchTime = fetchEndTime - fetchStartTime;
                }
            },
            
            // 检查是否可以获取频道
            canFetchChannels() {
                // 检查网络状态
                if (!navigator.onLine) {
                    return false;
                }
                
                // 检查用户权限
                if (!this.self || !this.self.isInternalUser) {
                    return false;
                }
                
                // 检查是否已在获取中
                if (this.channels.isLoading) {
                    return false;
                }
                
                return true;
            },
            
            // 验证频道数据
            validateChannelsData() {
                try {
                    const channels = this.channels.records;
                    
                    if (!Array.isArray(channels)) {
                        throw new Error('频道数据格式无效');
                    }
                    
                    // 验证每个频道的基本属性
                    channels.forEach((channel, index) => {
                        if (!channel.id) {
                            throw new Error(`频道 ${index} 缺少ID`);
                        }
                        
                        if (!channel.name) {
                            throw new Error(`频道 ${channel.id} 缺少名称`);
                        }
                        
                        if (typeof channel.importantCounter !== 'number') {
                            console.warn(`频道 ${channel.id} 的重要计数器类型无效`);
                        }
                    });
                    
                    console.log(`成功验证 ${channels.length} 个频道的数据`);
                    
                } catch (error) {
                    console.error('频道数据验证失败:', error);
                    this.initializationState.initErrors.push(error);
                }
            },
            
            // 更新增强计数器
            updateEnhancedCounters() {
                try {
                    const threads = Object.values(this.Thread.records);
                    
                    // 重置计数器
                    this.enhancedCounters = {
                        totalUnread: 0,
                        channelUnread: 0,
                        chatUnread: 0,
                        mentionUnread: 0,
                        needactionUnread: 0
                    };
                    
                    // 计算各类计数器
                    threads.forEach(thread => {
                        if (!thread.displayToSelf || thread.isMuted) {
                            return;
                        }
                        
                        const unreadCount = thread.selfMember?.message_unread_counter || 0;
                        const needactionCount = thread.message_needaction_counter || 0;
                        
                        if (unreadCount > 0 || needactionCount > 0) {
                            this.enhancedCounters.totalUnread++;
                            
                            if (thread.model === 'discuss.channel') {
                                this.enhancedCounters.channelUnread++;
                            } else if (thread.channel_type === 'chat') {
                                this.enhancedCounters.chatUnread++;
                            }
                            
                            if (needactionCount > 0) {
                                this.enhancedCounters.needactionUnread++;
                            }
                            
                            if (this.hasMentions(thread)) {
                                this.enhancedCounters.mentionUnread++;
                            }
                        }
                    });
                    
                    // 更新原有计数器
                    this.initChannelsUnreadCounter = this.enhancedCounters.channelUnread;
                    
                } catch (error) {
                    console.error('更新计数器失败:', error);
                }
            },
            
            // 检查是否有提及
            hasMentions(thread) {
                try {
                    const messages = thread.message_ids || [];
                    return messages.some(message => 
                        message.mentionsCurrentUser && !message.isRead
                    );
                } catch (error) {
                    return false;
                }
            },
            
            // 初始化完成后的设置
            async postInitializationSetup() {
                try {
                    // 设置定期数据同步
                    this.setupPeriodicSync();
                    
                    // 设置离线检测
                    this.setupOfflineDetection();
                    
                    // 设置内存监控
                    this.setupMemoryMonitoring();
                    
                    // 加载用户偏好
                    await this.loadUserPreferences();
                    
                    // 设置自动保存
                    this.setupAutoSave();
                    
                } catch (error) {
                    console.error('初始化后设置失败:', error);
                }
            },
            
            // 标记初始化完成
            markInitializationComplete() {
                this.initializationState.isInitialized = true;
                this.initializationState.initEndTime = Date.now();
                
                const initDuration = this.initializationState.initEndTime - this.initializationState.initStartTime;
                console.log(`存储服务初始化完成，耗时: ${initDuration}ms`);
                
                // 触发初始化完成事件
                this.env.bus.trigger('store:initialized', {
                    duration: initDuration,
                    errors: this.initializationState.initErrors,
                    counters: this.enhancedCounters
                });
            },
            
            // 处理初始化错误
            handleInitializationError(error) {
                this.initializationState.initErrors.push(error);
                
                // 尝试恢复
                this.attemptRecovery(error);
                
                // 记录错误
                this.recordError('initialization', error);
            },
            
            // 处理获取错误
            handleFetchError(error) {
                // 记录错误
                this.recordError('fetch', error);
                
                // 尝试使用缓存数据
                this.tryUseCachedData();
                
                // 设置重试
                this.scheduleRetry();
            },
            
            // 尝试恢复
            attemptRecovery(error) {
                try {
                    // 清理可能损坏的数据
                    this.cleanupCorruptedData();
                    
                    // 重置状态
                    this.resetToSafeState();
                    
                    // 重新初始化关键组件
                    this.reinitializeCriticalComponents();
                    
                } catch (recoveryError) {
                    console.error('恢复失败:', recoveryError);
                }
            },
            
            // 设置定期同步
            setupPeriodicSync() {
                // 每5分钟同步一次数据
                this.syncInterval = setInterval(() => {
                    if (this.discuss.isActive && navigator.onLine) {
                        this.channels.fetch();
                    }
                }, 5 * 60 * 1000);
                
                // 清理定时器
                onWillUnmount(() => {
                    if (this.syncInterval) {
                        clearInterval(this.syncInterval);
                    }
                });
            },
            
            // 设置离线检测
            setupOfflineDetection() {
                this.onlineHandler = () => {
                    console.log('网络连接恢复，同步数据');
                    if (this.discuss.isActive) {
                        this.channels.fetch();
                    }
                };
                
                this.offlineHandler = () => {
                    console.log('网络连接断开，启用离线模式');
                    this.enableOfflineMode();
                };
                
                window.addEventListener('online', this.onlineHandler);
                window.addEventListener('offline', this.offlineHandler);
                
                onWillUnmount(() => {
                    window.removeEventListener('online', this.onlineHandler);
                    window.removeEventListener('offline', this.offlineHandler);
                });
            },
            
            // 记录启动事件
            recordStartupEvent(eventType) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('store_startup_events') || '[]'
                    );
                    
                    events.push({
                        type: eventType,
                        timestamp: Date.now(),
                        discussActive: this.discuss?.isActive || false
                    });
                    
                    // 保留最近50个事件
                    if (events.length > 50) {
                        events.splice(0, events.length - 50);
                    }
                    
                    localStorage.setItem('store_startup_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录启动事件失败:', error);
                }
            },
            
            // 记录性能指标
            recordPerformanceMetrics() {
                try {
                    const metrics = JSON.parse(
                        localStorage.getItem('store_performance_metrics') || '[]'
                    );
                    
                    metrics.push({
                        ...this.performanceMetrics,
                        timestamp: Date.now(),
                        counters: { ...this.enhancedCounters }
                    });
                    
                    // 保留最近100个指标
                    if (metrics.length > 100) {
                        metrics.splice(0, metrics.length - 100);
                    }
                    
                    localStorage.setItem('store_performance_metrics', JSON.stringify(metrics));
                } catch (error) {
                    console.warn('记录性能指标失败:', error);
                }
            },
            
            // 获取存储服务统计
            getStoreServiceStatistics() {
                return {
                    isInitialized: this.initializationState.isInitialized,
                    initDuration: this.initializationState.initEndTime - this.initializationState.initStartTime,
                    initErrors: this.initializationState.initErrors.length,
                    counters: { ...this.enhancedCounters },
                    performance: { ...this.performanceMetrics },
                    channelsCount: Object.keys(this.Thread.records).filter(
                        id => this.Thread.records[id].model === 'discuss.channel'
                    ).length
                };
            }
        };
        
        patch(Store.prototype, EnhancedStorePatch);
    }
};

// 应用存储服务补丁增强
StoreServicePatchEnhancer.enhanceStoreServicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 状态管理
- 计数器初始化
- 条件性数据获取
- 状态检查机制

### 3. 性能优化
- 避免不必要的数据获取
- 条件性执行逻辑
- 资源使用优化

### 4. 可靠性保证
- 初始化状态跟踪
- 错误处理机制
- 数据验证功能

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 初始化模式 (Initialization Pattern)
- 服务的初始化管理
- 状态的准备和设置

### 3. 条件执行模式 (Conditional Execution Pattern)
- 基于状态的条件执行
- 资源的智能使用

## 注意事项

1. **状态检查**: 确保在正确的状态下执行操作
2. **性能影响**: 避免不必要的数据获取影响性能
3. **错误处理**: 提供完善的错误处理和恢复机制
4. **内存管理**: 及时清理定时器和事件监听器

## 扩展建议

1. **更多计数器**: 添加更多类型的计数器
2. **智能同步**: 实现更智能的数据同步策略
3. **缓存管理**: 实现更完善的缓存管理
4. **性能监控**: 添加详细的性能监控
5. **离线支持**: 增强离线模式下的功能

该补丁为讨论应用的存储服务提供了重要的初始化和状态管理功能，确保服务的可靠启动和运行。

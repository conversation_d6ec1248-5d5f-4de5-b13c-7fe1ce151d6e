# Channel Member List Patch - 频道成员列表补丁

## 概述

`channel_member_list_patch.js` 实现了对 Odoo 讨论应用Web环境中频道成员列表组件的补丁扩展，专门添加了头像卡片弹出框功能。该补丁通过Odoo的补丁机制扩展了ChannelMemberList组件，集成了AvatarCardPopover组件，为用户点击成员头像时提供了详细的用户信息弹出框，增强了用户交互体验和信息展示功能，是讨论应用中成员管理功能的重要UI增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/channel_member_list_patch.js`
- **行数**: 35
- **模块**: `@mail/discuss/core/web/channel_member_list_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/core/common/channel_member_list'    // 频道成员列表组件
'@mail/discuss/web/avatar_card/avatar_card_popover' // 头像卡片弹出框组件
'@web/core/popover/popover_hook'                    // 弹出框钩子
'@web/core/utils/patch'                             // 补丁工具
```

## 补丁定义

### ChannelMemberList 补丁

```javascript
patch(ChannelMemberList.prototype, {
    setup() {
        super.setup(...arguments);
        this.avatarCard = usePopover(AvatarCardPopover, {
            position: "right",
        });
    },
    onClickAvatar(ev, member) {
        if (!this.canOpenChatWith(member)) {
            return;
        }
        if (!this.avatarCard.isOpen) {
            this.avatarCard.open(ev.currentTarget, {
                id: member.persona.userId,
            });
        }
    },
});
Object.assign(ChannelMemberList.components, { AvatarCardPopover });
```

**补丁特性**:
- 扩展频道成员列表功能
- 集成头像卡片弹出框
- 添加头像点击处理
- 注册新的子组件

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup(...arguments);
    this.avatarCard = usePopover(AvatarCardPopover, {
        position: "right",
    });
}
```

**初始化功能**:
- **父类调用**: 保持原有初始化逻辑
- **弹出框配置**: 创建头像卡片弹出框实例
- **位置设置**: 设置弹出框在右侧显示
- **钩子使用**: 使用usePopover钩子管理弹出框

### 2. 头像点击处理

```javascript
onClickAvatar(ev, member) {
    if (!this.canOpenChatWith(member)) {
        return;
    }
    if (!this.avatarCard.isOpen) {
        this.avatarCard.open(ev.currentTarget, {
            id: member.persona.userId,
        });
    }
}
```

**点击处理功能**:
- **权限检查**: 检查是否可以与该成员开启聊天
- **状态检查**: 检查弹出框是否已经打开
- **弹出框打开**: 在点击元素位置打开弹出框
- **用户ID传递**: 传递用户ID给头像卡片组件

### 3. 组件注册

```javascript
Object.assign(ChannelMemberList.components, { AvatarCardPopover });
```

**组件注册功能**:
- **组件扩展**: 将AvatarCardPopover添加到组件列表
- **模板可用**: 使组件在模板中可用
- **动态注册**: 运行时动态注册组件

## 使用场景

### 1. 频道成员列表增强

```javascript
// 频道成员列表增强功能
const ChannelMemberListPatchEnhancer = {
    enhanceChannelMemberListPatch: () => {
        const EnhancedChannelMemberListPatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有的头像卡片弹出框
                this.avatarCard = usePopover(AvatarCardPopover, {
                    position: "right",
                });
                
                // 增强的弹出框配置
                this.memberDetailsPopover = usePopover(MemberDetailsPopover, {
                    position: "bottom",
                    closeOnClickAway: true
                });
                
                this.memberActionsPopover = usePopover(MemberActionsPopover, {
                    position: "left",
                    fixedPosition: true
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置成员状态监控
                this.setupMemberStatusMonitoring();
                
                // 设置批量操作
                this.setupBatchOperations();
                
                // 设置搜索过滤
                this.setupMemberSearch();
                
                // 设置排序功能
                this.setupMemberSorting();
                
                // 设置权限管理
                this.setupPermissionManagement();
            },
            
            // 增强的头像点击处理
            onClickAvatar(ev, member) {
                try {
                    // 记录点击事件
                    this.recordMemberClick(member);
                    
                    // 检查权限
                    if (!this.canOpenChatWith(member)) {
                        this.showPermissionDeniedMessage(member);
                        return;
                    }
                    
                    // 检查成员状态
                    if (!this.isMemberActive(member)) {
                        this.showInactiveMemberMessage(member);
                        return;
                    }
                    
                    // 根据点击类型处理
                    if (ev.ctrlKey || ev.metaKey) {
                        // Ctrl+点击显示详细信息
                        this.showMemberDetails(ev, member);
                    } else if (ev.shiftKey) {
                        // Shift+点击显示操作菜单
                        this.showMemberActions(ev, member);
                    } else {
                        // 普通点击显示头像卡片
                        this.showAvatarCard(ev, member);
                    }
                    
                } catch (error) {
                    console.error('处理头像点击失败:', error);
                    this.showErrorMessage('无法显示成员信息');
                }
            },
            
            // 显示头像卡片
            showAvatarCard(ev, member) {
                if (!this.avatarCard.isOpen) {
                    this.avatarCard.open(ev.currentTarget, {
                        id: member.persona.userId,
                        member: member,
                        enhanced: true,
                        onChatClick: () => this.openChatWith(member),
                        onProfileClick: () => this.openMemberProfile(member),
                        onMentionClick: () => this.mentionMember(member)
                    });
                }
            },
            
            // 显示成员详细信息
            showMemberDetails(ev, member) {
                if (!this.memberDetailsPopover.isOpen) {
                    this.memberDetailsPopover.open(ev.currentTarget, {
                        member: member,
                        showStatistics: true,
                        showActivity: true,
                        showPermissions: this.canManageMembers()
                    });
                }
            },
            
            // 显示成员操作菜单
            showMemberActions(ev, member) {
                if (!this.memberActionsPopover.isOpen) {
                    const actions = this.getAvailableActions(member);
                    
                    this.memberActionsPopover.open(ev.currentTarget, {
                        member: member,
                        actions: actions,
                        onActionClick: (action) => this.executeMemberAction(action, member)
                    });
                }
            },
            
            // 获取可用操作
            getAvailableActions(member) {
                const actions = [];
                
                // 基础操作
                if (this.canOpenChatWith(member)) {
                    actions.push({
                        id: 'chat',
                        name: '私聊',
                        icon: 'fa-comment',
                        handler: () => this.openChatWith(member)
                    });
                }
                
                if (this.canMentionMember(member)) {
                    actions.push({
                        id: 'mention',
                        name: '提及',
                        icon: 'fa-at',
                        handler: () => this.mentionMember(member)
                    });
                }
                
                // 管理操作
                if (this.canManageMembers()) {
                    if (member.role !== 'admin') {
                        actions.push({
                            id: 'promote',
                            name: '提升为管理员',
                            icon: 'fa-arrow-up',
                            handler: () => this.promoteMember(member)
                        });
                    }
                    
                    if (member.role === 'admin' && this.canDemoteAdmin(member)) {
                        actions.push({
                            id: 'demote',
                            name: '降级权限',
                            icon: 'fa-arrow-down',
                            handler: () => this.demoteMember(member)
                        });
                    }
                    
                    if (this.canRemoveMember(member)) {
                        actions.push({
                            id: 'remove',
                            name: '移除成员',
                            icon: 'fa-times',
                            class: 'text-danger',
                            handler: () => this.removeMember(member)
                        });
                    }
                }
                
                return actions;
            },
            
            // 执行成员操作
            async executeMemberAction(action, member) {
                try {
                    await action.handler();
                    
                    // 记录操作
                    this.recordMemberAction(action.id, member);
                    
                    // 关闭弹出框
                    this.memberActionsPopover.close();
                    
                    // 显示成功消息
                    this.showSuccessMessage(`操作 "${action.name}" 执行成功`);
                    
                } catch (error) {
                    console.error('执行成员操作失败:', error);
                    this.showErrorMessage(`操作失败: ${error.message}`);
                }
            },
            
            // 打开与成员的聊天
            async openChatWith(member) {
                try {
                    const chat = await this.store.Thread.getOrCreateChat(member.persona);
                    chat.open();
                    
                    // 记录聊天开启
                    this.recordChatOpen(member);
                    
                } catch (error) {
                    console.error('打开聊天失败:', error);
                    throw error;
                }
            },
            
            // 打开成员资料
            openMemberProfile(member) {
                this.env.services.action.doAction({
                    type: 'ir.actions.act_window',
                    res_model: 'res.partner',
                    res_id: member.persona.id,
                    views: [[false, 'form']],
                    target: 'new'
                });
            },
            
            // 提及成员
            mentionMember(member) {
                const composer = this.thread?.composer;
                if (composer) {
                    const mentionText = `@${member.persona.name} `;
                    composer.insertText(mentionText);
                    composer.focus();
                }
            },
            
            // 提升成员权限
            async promoteMember(member) {
                const confirmed = confirm(`确定要将 ${member.persona.name} 提升为管理员吗？`);
                
                if (confirmed) {
                    await rpc('/discuss/channel/member/promote', {
                        channel_id: this.thread.id,
                        member_id: member.id
                    });
                    
                    // 更新本地状态
                    member.role = 'admin';
                }
            },
            
            // 降级成员权限
            async demoteMember(member) {
                const confirmed = confirm(`确定要降级 ${member.persona.name} 的权限吗？`);
                
                if (confirmed) {
                    await rpc('/discuss/channel/member/demote', {
                        channel_id: this.thread.id,
                        member_id: member.id
                    });
                    
                    // 更新本地状态
                    member.role = 'member';
                }
            },
            
            // 移除成员
            async removeMember(member) {
                const confirmed = confirm(`确定要将 ${member.persona.name} 从频道中移除吗？`);
                
                if (confirmed) {
                    await rpc('/discuss/channel/member/remove', {
                        channel_id: this.thread.id,
                        member_id: member.id
                    });
                    
                    // 从本地列表中移除
                    const index = this.thread.channelMembers.indexOf(member);
                    if (index !== -1) {
                        this.thread.channelMembers.splice(index, 1);
                    }
                }
            },
            
            // 检查成员是否活跃
            isMemberActive(member) {
                // 检查成员是否在线或最近活跃
                return member.persona.isOnline || 
                       (member.lastActivity && 
                        Date.now() - new Date(member.lastActivity).getTime() < 24 * 60 * 60 * 1000);
            },
            
            // 检查是否可以提及成员
            canMentionMember(member) {
                return this.thread && this.thread.composer && member.persona.id !== this.store.self.id;
            },
            
            // 检查是否可以管理成员
            canManageMembers() {
                const selfMember = this.thread?.selfMember;
                return selfMember && (selfMember.role === 'admin' || selfMember.role === 'moderator');
            },
            
            // 检查是否可以降级管理员
            canDemoteAdmin(member) {
                // 不能降级自己，不能降级频道创建者
                return member.persona.id !== this.store.self.id && 
                       !member.isChannelOwner;
            },
            
            // 检查是否可以移除成员
            canRemoveMember(member) {
                // 不能移除自己，不能移除频道创建者
                return member.persona.id !== this.store.self.id && 
                       !member.isChannelOwner;
            },
            
            // 显示权限拒绝消息
            showPermissionDeniedMessage(member) {
                this.env.services.notification.add(
                    `无权与 ${member.persona.name} 开启聊天`,
                    { type: 'warning' }
                );
            },
            
            // 显示非活跃成员消息
            showInactiveMemberMessage(member) {
                this.env.services.notification.add(
                    `${member.persona.name} 当前不在线`,
                    { type: 'info' }
                );
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 显示成功消息
            showSuccessMessage(message) {
                this.env.services.notification.add(message, { type: 'success' });
            },
            
            // 记录成员点击
            recordMemberClick(member) {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('member_clicks') || '[]'
                    );
                    
                    clicks.push({
                        memberId: member.id,
                        memberName: member.persona.name,
                        channelId: this.thread?.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个点击
                    if (clicks.length > 100) {
                        clicks.splice(0, clicks.length - 100);
                    }
                    
                    localStorage.setItem('member_clicks', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录成员点击失败:', error);
                }
            },
            
            // 记录成员操作
            recordMemberAction(actionType, member) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('member_actions') || '[]'
                    );
                    
                    actions.push({
                        actionType,
                        memberId: member.id,
                        memberName: member.persona.name,
                        channelId: this.thread?.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个操作
                    if (actions.length > 100) {
                        actions.splice(0, actions.length - 100);
                    }
                    
                    localStorage.setItem('member_actions', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录成员操作失败:', error);
                }
            },
            
            // 记录聊天开启
            recordChatOpen(member) {
                try {
                    const chats = JSON.parse(
                        localStorage.getItem('chat_opens') || '[]'
                    );
                    
                    chats.push({
                        memberId: member.id,
                        memberName: member.persona.name,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (chats.length > 50) {
                        chats.splice(0, chats.length - 50);
                    }
                    
                    localStorage.setItem('chat_opens', JSON.stringify(chats));
                } catch (error) {
                    console.warn('记录聊天开启失败:', error);
                }
            },
            
            // 获取成员交互统计
            getMemberInteractionStats() {
                try {
                    return {
                        clicks: JSON.parse(localStorage.getItem('member_clicks') || '[]').length,
                        actions: JSON.parse(localStorage.getItem('member_actions') || '[]').length,
                        chatOpens: JSON.parse(localStorage.getItem('chat_opens') || '[]').length
                    };
                } catch (error) {
                    return {
                        clicks: 0,
                        actions: 0,
                        chatOpens: 0
                    };
                }
            }
        };
        
        patch(ChannelMemberList.prototype, EnhancedChannelMemberListPatch);
        
        // 扩展组件注册
        Object.assign(ChannelMemberList.components, { 
            AvatarCardPopover,
            MemberDetailsPopover,
            MemberActionsPopover
        });
    }
};

// 应用频道成员列表补丁增强
ChannelMemberListPatchEnhancer.enhanceChannelMemberListPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 弹出框集成
- 弹出框钩子使用
- 位置智能定位
- 状态管理优化

### 3. 组件注册
- 动态组件注册
- 模板可用性扩展
- 运行时组件添加

### 4. 用户交互
- 权限检查机制
- 状态感知处理
- 用户友好反馈

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 原有行为的增强

### 3. 策略模式 (Strategy Pattern)
- 不同交互方式的处理策略
- 可配置的行为模式

## 注意事项

1. **权限检查**: 确保用户有适当的权限进行操作
2. **状态管理**: 正确管理弹出框的打开和关闭状态
3. **性能优化**: 避免频繁的弹出框创建和销毁
4. **用户体验**: 提供清晰的交互反馈

## 扩展建议

1. **更多交互**: 添加更多类型的成员交互功能
2. **批量操作**: 支持批量成员管理操作
3. **搜索过滤**: 实现成员搜索和过滤功能
4. **状态监控**: 实时监控成员在线状态
5. **权限管理**: 更细粒度的权限控制

该补丁为讨论应用的频道成员列表提供了重要的交互增强功能，特别是头像卡片弹出框的集成。

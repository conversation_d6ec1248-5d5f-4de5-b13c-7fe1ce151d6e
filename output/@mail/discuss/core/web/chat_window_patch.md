# Chat Window Patch - 聊天窗口补丁

## 概述

`chat_window_patch.js` 实现了对 Odoo 讨论应用Web环境中聊天窗口组件的补丁扩展，专门添加了频道选择器组件的集成。该补丁通过简单的组件注册机制将ChannelSelector组件添加到ChatWindow组件中，使聊天窗口能够使用频道选择器功能，为用户在聊天窗口中提供了频道和聊天对象的选择能力，是聊天窗口功能增强的重要组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/chat_window_patch.js`
- **行数**: 14
- **模块**: `@mail/discuss/core/web/chat_window_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/core/web/channel_selector'  // 频道选择器组件
'@mail/core/common/chat_window'             // 聊天窗口组件
```

## 补丁定义

### ChatWindow 组件扩展

```javascript
const { ChannelSelector } = require("@mail/discuss/core/web/channel_selector");
const { ChatWindow } = require("@mail/core/common/chat_window");

Object.assign(ChatWindow.components, { ChannelSelector });
```

**补丁特性**:
- 简单的组件注册机制
- 非侵入式功能扩展
- 保持原有组件结构
- 运行时组件添加

## 核心功能

### 1. 组件注册

```javascript
Object.assign(ChatWindow.components, { ChannelSelector });
```

**组件注册功能**:
- **组件扩展**: 将ChannelSelector添加到ChatWindow的组件列表
- **模板可用**: 使频道选择器在聊天窗口模板中可用
- **动态注册**: 运行时动态注册组件
- **非破坏性**: 不影响原有组件功能

## 使用场景

### 1. 聊天窗口增强

```javascript
// 聊天窗口增强功能
const ChatWindowPatchEnhancer = {
    enhanceChatWindowPatch: () => {
        // 扩展更多组件到聊天窗口
        const additionalComponents = {
            ChannelSelector,
            QuickChannelSwitcher,
            ChatWindowToolbar,
            MessageSearchPanel,
            EmojiPicker,
            FileUploader,
            VoiceRecorder,
            VideoCallButton,
            ScreenShareButton,
            ChatWindowSettings
        };
        
        // 批量注册组件
        Object.assign(ChatWindow.components, additionalComponents);
        
        // 添加聊天窗口增强功能
        const EnhancedChatWindowPatch = {
            setup() {
                super.setup(...arguments);
                
                // 增强的状态管理
                this.enhancedState = useState({
                    showChannelSelector: false,
                    showSearchPanel: false,
                    showEmojiPicker: false,
                    showSettings: false,
                    isRecording: false,
                    isInCall: false,
                    isScreenSharing: false,
                    quickSwitchMode: false
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置快捷键
                this.setupKeyboardShortcuts();
                
                // 设置工具栏
                this.setupToolbar();
                
                // 设置拖拽功能
                this.setupDragAndDrop();
                
                // 设置窗口管理
                this.setupWindowManagement();
                
                // 设置通知管理
                this.setupNotificationManagement();
            },
            
            // 切换频道选择器
            toggleChannelSelector() {
                this.enhancedState.showChannelSelector = !this.enhancedState.showChannelSelector;
                
                if (this.enhancedState.showChannelSelector) {
                    // 关闭其他面板
                    this.closeOtherPanels('channelSelector');
                    
                    // 记录打开事件
                    this.recordPanelOpen('channelSelector');
                }
            },
            
            // 切换搜索面板
            toggleSearchPanel() {
                this.enhancedState.showSearchPanel = !this.enhancedState.showSearchPanel;
                
                if (this.enhancedState.showSearchPanel) {
                    this.closeOtherPanels('searchPanel');
                    this.recordPanelOpen('searchPanel');
                }
            },
            
            // 切换表情选择器
            toggleEmojiPicker() {
                this.enhancedState.showEmojiPicker = !this.enhancedState.showEmojiPicker;
                
                if (this.enhancedState.showEmojiPicker) {
                    this.closeOtherPanels('emojiPicker');
                    this.recordPanelOpen('emojiPicker');
                }
            },
            
            // 切换设置面板
            toggleSettings() {
                this.enhancedState.showSettings = !this.enhancedState.showSettings;
                
                if (this.enhancedState.showSettings) {
                    this.closeOtherPanels('settings');
                    this.recordPanelOpen('settings');
                }
            },
            
            // 关闭其他面板
            closeOtherPanels(except) {
                const panels = ['channelSelector', 'searchPanel', 'emojiPicker', 'settings'];
                
                panels.forEach(panel => {
                    if (panel !== except) {
                        const stateKey = `show${panel.charAt(0).toUpperCase() + panel.slice(1)}`;
                        if (this.enhancedState[stateKey]) {
                            this.enhancedState[stateKey] = false;
                        }
                    }
                });
            },
            
            // 开始语音通话
            async startVoiceCall() {
                try {
                    this.enhancedState.isInCall = true;
                    
                    // 调用语音通话服务
                    await this.env.services.voiceCall.start({
                        threadId: this.thread.id,
                        type: 'voice'
                    });
                    
                    // 记录通话开始
                    this.recordCallStart('voice');
                    
                } catch (error) {
                    console.error('开始语音通话失败:', error);
                    this.enhancedState.isInCall = false;
                    this.showErrorMessage('无法开始语音通话');
                }
            },
            
            // 开始视频通话
            async startVideoCall() {
                try {
                    this.enhancedState.isInCall = true;
                    
                    // 调用视频通话服务
                    await this.env.services.videoCall.start({
                        threadId: this.thread.id,
                        type: 'video'
                    });
                    
                    // 记录通话开始
                    this.recordCallStart('video');
                    
                } catch (error) {
                    console.error('开始视频通话失败:', error);
                    this.enhancedState.isInCall = false;
                    this.showErrorMessage('无法开始视频通话');
                }
            },
            
            // 开始屏幕共享
            async startScreenShare() {
                try {
                    this.enhancedState.isScreenSharing = true;
                    
                    // 调用屏幕共享服务
                    await this.env.services.screenShare.start({
                        threadId: this.thread.id
                    });
                    
                    // 记录屏幕共享开始
                    this.recordScreenShareStart();
                    
                } catch (error) {
                    console.error('开始屏幕共享失败:', error);
                    this.enhancedState.isScreenSharing = false;
                    this.showErrorMessage('无法开始屏幕共享');
                }
            },
            
            // 开始录音
            async startRecording() {
                try {
                    this.enhancedState.isRecording = true;
                    
                    // 调用录音服务
                    await this.env.services.voiceRecorder.start({
                        onRecordingComplete: (audioBlob) => {
                            this.handleRecordingComplete(audioBlob);
                        }
                    });
                    
                    // 记录录音开始
                    this.recordRecordingStart();
                    
                } catch (error) {
                    console.error('开始录音失败:', error);
                    this.enhancedState.isRecording = false;
                    this.showErrorMessage('无法开始录音');
                }
            },
            
            // 停止录音
            async stopRecording() {
                try {
                    await this.env.services.voiceRecorder.stop();
                    this.enhancedState.isRecording = false;
                    
                    // 记录录音停止
                    this.recordRecordingStop();
                    
                } catch (error) {
                    console.error('停止录音失败:', error);
                    this.showErrorMessage('停止录音失败');
                }
            },
            
            // 处理录音完成
            async handleRecordingComplete(audioBlob) {
                try {
                    // 上传音频文件
                    const audioFile = new File([audioBlob], 'voice_message.wav', {
                        type: 'audio/wav'
                    });
                    
                    // 发送语音消息
                    await this.thread.sendMessage({
                        attachments: [audioFile],
                        messageType: 'voice'
                    });
                    
                    // 记录语音消息发送
                    this.recordVoiceMessageSent();
                    
                } catch (error) {
                    console.error('发送语音消息失败:', error);
                    this.showErrorMessage('发送语音消息失败');
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+K 打开频道选择器
                    if (event.ctrlKey && event.key === 'k') {
                        event.preventDefault();
                        this.toggleChannelSelector();
                    }
                    
                    // Ctrl+F 打开搜索面板
                    if (event.ctrlKey && event.key === 'f') {
                        event.preventDefault();
                        this.toggleSearchPanel();
                    }
                    
                    // Ctrl+E 打开表情选择器
                    if (event.ctrlKey && event.key === 'e') {
                        event.preventDefault();
                        this.toggleEmojiPicker();
                    }
                    
                    // Ctrl+, 打开设置
                    if (event.ctrlKey && event.key === ',') {
                        event.preventDefault();
                        this.toggleSettings();
                    }
                    
                    // Space 开始/停止录音（长按）
                    if (event.code === 'Space' && !event.repeat) {
                        if (event.type === 'keydown') {
                            this.startRecording();
                        } else if (event.type === 'keyup') {
                            this.stopRecording();
                        }
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                document.addEventListener('keyup', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                    document.removeEventListener('keyup', this.keyboardHandler);
                });
            },
            
            // 设置拖拽功能
            setupDragAndDrop() {
                if (this.rootRef.el) {
                    this.rootRef.el.addEventListener('dragover', this.onDragOver.bind(this));
                    this.rootRef.el.addEventListener('drop', this.onDrop.bind(this));
                }
            },
            
            // 处理拖拽悬停
            onDragOver(event) {
                event.preventDefault();
                event.dataTransfer.dropEffect = 'copy';
                
                // 添加拖拽样式
                this.rootRef.el.classList.add('o-chat-window-drag-over');
            },
            
            // 处理文件拖拽
            async onDrop(event) {
                event.preventDefault();
                
                // 移除拖拽样式
                this.rootRef.el.classList.remove('o-chat-window-drag-over');
                
                const files = Array.from(event.dataTransfer.files);
                
                if (files.length > 0) {
                    try {
                        // 发送文件
                        await this.thread.sendMessage({
                            attachments: files
                        });
                        
                        // 记录文件拖拽发送
                        this.recordFileDragSent(files.length);
                        
                    } catch (error) {
                        console.error('拖拽发送文件失败:', error);
                        this.showErrorMessage('发送文件失败');
                    }
                }
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                this.env.services.notification.add(message, { type: 'error' });
            },
            
            // 显示成功消息
            showSuccessMessage(message) {
                this.env.services.notification.add(message, { type: 'success' });
            },
            
            // 记录面板打开
            recordPanelOpen(panelType) {
                try {
                    const opens = JSON.parse(
                        localStorage.getItem('chat_window_panel_opens') || '[]'
                    );
                    
                    opens.push({
                        panelType,
                        threadId: this.thread?.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (opens.length > 100) {
                        opens.splice(0, opens.length - 100);
                    }
                    
                    localStorage.setItem('chat_window_panel_opens', JSON.stringify(opens));
                } catch (error) {
                    console.warn('记录面板打开失败:', error);
                }
            },
            
            // 记录通话开始
            recordCallStart(callType) {
                try {
                    const calls = JSON.parse(
                        localStorage.getItem('chat_window_calls') || '[]'
                    );
                    
                    calls.push({
                        callType,
                        threadId: this.thread?.id,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (calls.length > 50) {
                        calls.splice(0, calls.length - 50);
                    }
                    
                    localStorage.setItem('chat_window_calls', JSON.stringify(calls));
                } catch (error) {
                    console.warn('记录通话开始失败:', error);
                }
            },
            
            // 获取聊天窗口统计
            getChatWindowStatistics() {
                try {
                    return {
                        panelOpens: JSON.parse(localStorage.getItem('chat_window_panel_opens') || '[]').length,
                        calls: JSON.parse(localStorage.getItem('chat_window_calls') || '[]').length,
                        recordings: JSON.parse(localStorage.getItem('chat_window_recordings') || '[]').length,
                        fileDrags: JSON.parse(localStorage.getItem('chat_window_file_drags') || '[]').length
                    };
                } catch (error) {
                    return {
                        panelOpens: 0,
                        calls: 0,
                        recordings: 0,
                        fileDrags: 0
                    };
                }
            }
        };
        
        // 应用聊天窗口补丁
        patch(ChatWindow.prototype, EnhancedChatWindowPatch);
    }
};

// 应用聊天窗口补丁增强
ChatWindowPatchEnhancer.enhanceChatWindowPatch();
```

## 技术特点

### 1. 简单补丁机制
- 最小化的代码修改
- 非侵入式扩展
- 运行时组件注册

### 2. 组件集成
- 无缝组件集成
- 模板可用性扩展
- 动态功能添加

### 3. 扩展性设计
- 易于添加更多组件
- 灵活的功能扩展
- 保持原有架构

### 4. 向后兼容
- 不影响原有功能
- 保持API一致性
- 渐进式增强

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 组合模式 (Composition Pattern)
- 组件的组合和集成
- 功能的模块化组合

### 3. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 原有行为的增强

## 注意事项

1. **组件依赖**: 确保被注册的组件已正确加载
2. **模板兼容**: 确保模板中正确使用新组件
3. **性能影响**: 避免注册过多不必要的组件
4. **版本兼容**: 确保与不同版本的兼容性

## 扩展建议

1. **更多组件**: 注册更多有用的组件到聊天窗口
2. **条件注册**: 根据条件动态注册组件
3. **组件配置**: 提供组件的配置选项
4. **性能优化**: 实现组件的懒加载机制
5. **插件系统**: 建立插件系统支持第三方组件

该补丁为聊天窗口提供了频道选择器功能，是聊天窗口功能扩展的基础示例。

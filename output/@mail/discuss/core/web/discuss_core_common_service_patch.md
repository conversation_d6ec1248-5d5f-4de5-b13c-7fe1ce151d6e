# Discuss Core Common Service Patch - 讨论核心通用服务补丁

## 概述

`discuss_core_common_service_patch.js` 实现了对 Odoo 讨论应用Web环境中讨论核心通用服务的补丁扩展，专门修复了新消息通知处理中的频道未读计数器可靠性问题。该补丁通过Odoo的补丁机制扩展了DiscussCoreCommon服务，重写了新消息通知处理方法，在处理新消息通知前强制刷新频道数据，确保频道未读计数器的准确性，是讨论应用中消息通知功能的重要修复组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/discuss_core_common_service_patch.js`
- **行数**: 24
- **模块**: `@mail/discuss/core/web/discuss_core_common_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/discuss/core/common/discuss_core_common_service'  // 讨论核心通用服务
'@web/core/utils/patch'                                  // 补丁工具
```

## 补丁定义

### DiscussCoreCommon 补丁

```javascript
/** @type {DiscussCoreCommon} */
const discussCoreCommon = {
    async _handleNotificationNewMessage(...args) {
        // initChannelsUnreadCounter becomes unreliable
        await this.store.channels.fetch();
        return super._handleNotificationNewMessage(...args);
    },
};

patch(DiscussCoreCommon.prototype, discussCoreCommon);
```

**补丁特性**:
- 扩展讨论核心通用服务
- 修复未读计数器可靠性问题
- 强制刷新频道数据
- 保持原有处理逻辑

## 核心功能

### 1. 新消息通知处理

```javascript
async _handleNotificationNewMessage(...args) {
    // initChannelsUnreadCounter becomes unreliable
    await this.store.channels.fetch();
    return super._handleNotificationNewMessage(...args);
}
```

**通知处理功能**:
- **数据刷新**: 在处理新消息通知前刷新频道数据
- **可靠性修复**: 解决initChannelsUnreadCounter不可靠的问题
- **异步处理**: 使用异步方式确保数据获取完成
- **父类调用**: 保持原有的通知处理逻辑

## 使用场景

### 1. 讨论核心服务增强

```javascript
// 讨论核心服务增强功能
const DiscussCoreCommonServicePatchEnhancer = {
    enhanceDiscussCoreCommonServicePatch: () => {
        const EnhancedDiscussCoreCommonPatch = {
            // 增强的新消息通知处理
            async _handleNotificationNewMessage(...args) {
                try {
                    // 记录通知处理开始
                    this.recordNotificationHandling('new_message', 'start');
                    
                    // 检查网络状态
                    if (!navigator.onLine) {
                        this.queueNotificationForLater('new_message', args);
                        return;
                    }
                    
                    // 强制刷新频道数据以确保计数器可靠性
                    await this.refreshChannelsWithRetry();
                    
                    // 预处理通知数据
                    const processedArgs = await this.preprocessNotificationData(args);
                    
                    // 调用父类处理方法
                    const result = await super._handleNotificationNewMessage(...processedArgs);
                    
                    // 后处理操作
                    await this.postProcessNewMessage(args, result);
                    
                    // 记录通知处理完成
                    this.recordNotificationHandling('new_message', 'complete');
                    
                    return result;
                    
                } catch (error) {
                    console.error('处理新消息通知失败:', error);
                    
                    // 记录错误
                    this.recordNotificationHandling('new_message', 'error', error);
                    
                    // 错误恢复
                    await this.handleNotificationError(error, args);
                    
                    // 重新抛出错误
                    throw error;
                }
            },
            
            // 带重试的频道刷新
            async refreshChannelsWithRetry(maxRetries = 3) {
                let retryCount = 0;
                
                while (retryCount < maxRetries) {
                    try {
                        await this.store.channels.fetch();
                        
                        // 验证数据完整性
                        if (this.validateChannelsData()) {
                            return;
                        }
                        
                        throw new Error('频道数据验证失败');
                        
                    } catch (error) {
                        retryCount++;
                        
                        if (retryCount >= maxRetries) {
                            console.error('频道数据刷新失败，已达到最大重试次数:', error);
                            throw error;
                        }
                        
                        // 指数退避重试
                        const delay = Math.pow(2, retryCount) * 1000;
                        await new Promise(resolve => setTimeout(resolve, delay));
                        
                        console.warn(`频道数据刷新失败，${delay}ms后重试 (${retryCount}/${maxRetries}):`, error);
                    }
                }
            },
            
            // 验证频道数据
            validateChannelsData() {
                try {
                    const channels = this.store.channels;
                    
                    // 检查基本数据结构
                    if (!channels || !Array.isArray(channels.records)) {
                        return false;
                    }
                    
                    // 检查关键属性
                    const hasValidChannels = channels.records.every(channel => 
                        channel.id && 
                        typeof channel.name === 'string' &&
                        typeof channel.importantCounter === 'number'
                    );
                    
                    return hasValidChannels;
                    
                } catch (error) {
                    console.warn('验证频道数据时出错:', error);
                    return false;
                }
            },
            
            // 预处理通知数据
            async preprocessNotificationData(args) {
                try {
                    const [notification] = args;
                    
                    if (!notification || !notification.payload) {
                        return args;
                    }
                    
                    // 增强通知数据
                    const enhancedNotification = {
                        ...notification,
                        payload: {
                            ...notification.payload,
                            processedAt: Date.now(),
                            clientVersion: this.getClientVersion(),
                            userTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                        }
                    };
                    
                    // 验证消息数据
                    if (notification.payload.message) {
                        enhancedNotification.payload.message = await this.validateAndEnhanceMessage(
                            notification.payload.message
                        );
                    }
                    
                    return [enhancedNotification, ...args.slice(1)];
                    
                } catch (error) {
                    console.warn('预处理通知数据失败:', error);
                    return args;
                }
            },
            
            // 验证和增强消息
            async validateAndEnhanceMessage(message) {
                try {
                    // 基本验证
                    if (!message.id || !message.body) {
                        throw new Error('消息数据不完整');
                    }
                    
                    // 增强消息数据
                    const enhancedMessage = {
                        ...message,
                        receivedAt: Date.now(),
                        isValid: true
                    };
                    
                    // 检查消息是否重复
                    if (this.isDuplicateMessage(message.id)) {
                        enhancedMessage.isDuplicate = true;
                    }
                    
                    // 检查消息是否来自被屏蔽的用户
                    if (await this.isMessageFromBlockedUser(message.author_id)) {
                        enhancedMessage.isBlocked = true;
                    }
                    
                    return enhancedMessage;
                    
                } catch (error) {
                    console.warn('验证消息失败:', error);
                    return { ...message, isValid: false, validationError: error.message };
                }
            },
            
            // 后处理新消息
            async postProcessNewMessage(args, result) {
                try {
                    const [notification] = args;
                    
                    if (!notification || !notification.payload) {
                        return;
                    }
                    
                    // 更新统计信息
                    await this.updateMessageStatistics(notification.payload);
                    
                    // 触发自定义事件
                    this.triggerCustomEvents(notification.payload);
                    
                    // 更新用户活动状态
                    await this.updateUserActivity(notification.payload);
                    
                    // 检查是否需要发送桌面通知
                    await this.checkDesktopNotification(notification.payload);
                    
                } catch (error) {
                    console.warn('后处理新消息失败:', error);
                }
            },
            
            // 处理通知错误
            async handleNotificationError(error, args) {
                try {
                    // 记录详细错误信息
                    const errorInfo = {
                        error: error.message,
                        stack: error.stack,
                        args: args,
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        url: window.location.href
                    };
                    
                    // 发送错误报告
                    await this.sendErrorReport(errorInfo);
                    
                    // 尝试恢复操作
                    await this.attemptRecovery(error, args);
                    
                } catch (recoveryError) {
                    console.error('错误恢复失败:', recoveryError);
                }
            },
            
            // 离线通知队列
            queueNotificationForLater(type, args) {
                try {
                    const queue = JSON.parse(
                        localStorage.getItem('notification_queue') || '[]'
                    );
                    
                    queue.push({
                        type,
                        args: this.serializeArgs(args),
                        timestamp: Date.now()
                    });
                    
                    // 限制队列大小
                    if (queue.length > 100) {
                        queue.splice(0, queue.length - 100);
                    }
                    
                    localStorage.setItem('notification_queue', JSON.stringify(queue));
                    
                    // 监听网络恢复
                    this.setupNetworkRecoveryListener();
                    
                } catch (error) {
                    console.warn('队列通知失败:', error);
                }
            },
            
            // 设置网络恢复监听
            setupNetworkRecoveryListener() {
                if (this.networkRecoveryListener) {
                    return;
                }
                
                this.networkRecoveryListener = async () => {
                    if (navigator.onLine) {
                        await this.processQueuedNotifications();
                    }
                };
                
                window.addEventListener('online', this.networkRecoveryListener);
            },
            
            // 处理队列中的通知
            async processQueuedNotifications() {
                try {
                    const queue = JSON.parse(
                        localStorage.getItem('notification_queue') || '[]'
                    );
                    
                    if (queue.length === 0) {
                        return;
                    }
                    
                    // 按时间戳排序
                    queue.sort((a, b) => a.timestamp - b.timestamp);
                    
                    // 逐个处理
                    for (const item of queue) {
                        try {
                            const args = this.deserializeArgs(item.args);
                            
                            switch (item.type) {
                                case 'new_message':
                                    await this._handleNotificationNewMessage(...args);
                                    break;
                                // 可以添加其他通知类型
                            }
                        } catch (error) {
                            console.warn('处理队列通知失败:', error);
                        }
                    }
                    
                    // 清空队列
                    localStorage.removeItem('notification_queue');
                    
                } catch (error) {
                    console.error('处理通知队列失败:', error);
                }
            },
            
            // 序列化参数
            serializeArgs(args) {
                try {
                    return JSON.stringify(args);
                } catch (error) {
                    console.warn('序列化参数失败:', error);
                    return '[]';
                }
            },
            
            // 反序列化参数
            deserializeArgs(serializedArgs) {
                try {
                    return JSON.parse(serializedArgs);
                } catch (error) {
                    console.warn('反序列化参数失败:', error);
                    return [];
                }
            },
            
            // 检查重复消息
            isDuplicateMessage(messageId) {
                try {
                    const recentMessages = JSON.parse(
                        sessionStorage.getItem('recent_messages') || '[]'
                    );
                    
                    return recentMessages.includes(messageId);
                } catch (error) {
                    return false;
                }
            },
            
            // 检查是否来自被屏蔽用户
            async isMessageFromBlockedUser(authorId) {
                try {
                    const blockedUsers = await this.getBlockedUsers();
                    return blockedUsers.includes(authorId);
                } catch (error) {
                    return false;
                }
            },
            
            // 获取被屏蔽用户列表
            async getBlockedUsers() {
                try {
                    // 这里可以从服务器获取或从本地缓存获取
                    const cached = localStorage.getItem('blocked_users');
                    if (cached) {
                        const { data, timestamp } = JSON.parse(cached);
                        
                        // 检查缓存是否过期（1小时）
                        if (Date.now() - timestamp < 60 * 60 * 1000) {
                            return data;
                        }
                    }
                    
                    // 从服务器获取
                    const blockedUsers = await this.env.services.orm.call(
                        'res.partner',
                        'get_blocked_users',
                        []
                    );
                    
                    // 缓存结果
                    localStorage.setItem('blocked_users', JSON.stringify({
                        data: blockedUsers,
                        timestamp: Date.now()
                    }));
                    
                    return blockedUsers;
                    
                } catch (error) {
                    console.warn('获取被屏蔽用户失败:', error);
                    return [];
                }
            },
            
            // 记录通知处理
            recordNotificationHandling(type, status, error = null) {
                try {
                    const records = JSON.parse(
                        localStorage.getItem('notification_handling_records') || '[]'
                    );
                    
                    records.push({
                        type,
                        status,
                        error: error ? error.message : null,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (records.length > 100) {
                        records.splice(0, records.length - 100);
                    }
                    
                    localStorage.setItem('notification_handling_records', JSON.stringify(records));
                } catch (error) {
                    console.warn('记录通知处理失败:', error);
                }
            },
            
            // 获取客户端版本
            getClientVersion() {
                return window.odoo?.info?.version || 'unknown';
            },
            
            // 获取通知处理统计
            getNotificationHandlingStatistics() {
                try {
                    const records = JSON.parse(
                        localStorage.getItem('notification_handling_records') || '[]'
                    );
                    
                    const stats = {
                        total: records.length,
                        success: records.filter(r => r.status === 'complete').length,
                        errors: records.filter(r => r.status === 'error').length,
                        byType: {}
                    };
                    
                    records.forEach(record => {
                        if (!stats.byType[record.type]) {
                            stats.byType[record.type] = { total: 0, success: 0, errors: 0 };
                        }
                        
                        stats.byType[record.type].total++;
                        
                        if (record.status === 'complete') {
                            stats.byType[record.type].success++;
                        } else if (record.status === 'error') {
                            stats.byType[record.type].errors++;
                        }
                    });
                    
                    return stats;
                } catch (error) {
                    return { total: 0, success: 0, errors: 0, byType: {} };
                }
            }
        };
        
        patch(DiscussCoreCommon.prototype, EnhancedDiscussCoreCommonPatch);
    }
};

// 应用讨论核心通用服务补丁增强
DiscussCoreCommonServicePatchEnhancer.enhanceDiscussCoreCommonServicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能修复

### 2. 可靠性修复
- 强制数据刷新机制
- 解决计数器不可靠问题
- 异步处理保证

### 3. 错误处理
- 完善的错误处理机制
- 异常情况的优雅处理
- 错误日志记录

### 4. 性能优化
- 最小化的代码修改
- 高效的数据刷新
- 异步操作优化

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能修复
- 非破坏性修改

### 2. 模板方法模式 (Template Method Pattern)
- 通知处理的模板方法
- 可扩展的处理逻辑

### 3. 装饰器模式 (Decorator Pattern)
- 功能的动态增强
- 原有行为的修复

## 注意事项

1. **数据一致性**: 确保频道数据刷新的一致性
2. **性能影响**: 避免频繁的数据刷新影响性能
3. **错误处理**: 提供完善的错误处理和恢复机制
4. **向后兼容**: 确保与原有功能的兼容性

## 扩展建议

1. **更多修复**: 添加更多可靠性修复
2. **性能监控**: 实现性能监控和优化
3. **错误报告**: 建立错误报告和分析系统
4. **自动恢复**: 实现更智能的自动恢复机制
5. **数据验证**: 增强数据验证和完整性检查

该补丁为讨论应用的核心通用服务提供了重要的可靠性修复，确保新消息通知处理的准确性。

# Discuss Core Web Service - 讨论核心Web服务

## 概述

`discuss_core_web_service.js` 实现了 Odoo 讨论应用Web环境中的核心服务，提供了用户连接通知、线程折叠状态管理、消息删除处理等功能。该服务通过总线服务订阅各种事件，集成了通知服务、UI服务、存储服务等，为Web环境中的讨论应用提供了完整的事件处理和状态管理，是讨论应用在Web环境中的核心协调组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/discuss_core_web_service.js`
- **行数**: 82
- **模块**: `@mail/discuss/core/web/discuss_core_web_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/l10n/translation'   // 国际化
'@web/core/registry'           // 注册表
```

## 服务定义

### DiscussCoreWeb 类

```javascript
const DiscussCoreWeb = class DiscussCoreWeb {
    constructor(env, services) {
        this.env = env;
        this.busService = services.bus_service;
        this.notificationService = services.notification;
        this.ui = services.ui;
        this.store = services["mail.store"];
        this.multiTab = services.multi_tab;
    }
}
```

**服务特性**:
- 集成多个核心服务
- 响应式服务实例
- 事件驱动架构
- 多标签页支持

## 核心功能

### 1. 用户连接通知

```javascript
this.busService.subscribe("res.users/connection", async ({ partnerId, username }) => {
    const notification = _t(
        "%(user)s connected. This is their first connection. Wish them luck.",
        { user: username }
    );
    this.notificationService.add(notification, { type: "info" });
    if (!this.multiTab.isOnMainTab()) {
        return;
    }
    const chat = await this.store.getChat({ partnerId });
    if (chat && !this.ui.isSmall) {
        this.store.chatHub.opened.add({ thread: chat });
    }
});
```

**连接通知功能**:
- **新用户连接**: 监听新用户首次连接事件
- **欢迎通知**: 显示用户连接的欢迎信息
- **主标签页检查**: 仅在主标签页中处理
- **自动开启聊天**: 在非小屏幕设备上自动开启聊天

### 2. 线程折叠状态管理

```javascript
this.busService.subscribe("discuss.Thread/fold_state", async (data) => {
    const thread = await this.store.Thread.getOrFetch(data);
    if (data.fold_state && thread && data.foldStateCount > thread.foldStateCount) {
        thread.foldStateCount = data.foldStateCount;
        thread.state = data.fold_state;
        if (thread.state === "closed") {
            const chatWindow = this.store.ChatWindow.get({ thread });
            chatWindow?.close({ notifyState: false });
        }
    }
});
```

**折叠状态功能**:
- **状态同步**: 同步线程的折叠状态
- **计数器检查**: 通过计数器避免过期状态
- **窗口关闭**: 关闭状态时自动关闭聊天窗口
- **静默关闭**: 关闭时不通知状态变化

### 3. 消息删除处理

```javascript
this.env.bus.addEventListener("mail.message/delete", ({ detail: { message } }) => {
    if (message.thread?.model === "discuss.channel") {
        // initChannelsUnreadCounter becomes unreliable
        this.store.channels.fetch();
    }
});
```

**删除处理功能**:
- **消息删除监听**: 监听消息删除事件
- **频道消息检查**: 仅处理频道中的消息删除
- **数据刷新**: 刷新频道数据确保计数器准确性
- **可靠性保证**: 解决未读计数器不可靠问题

## 使用场景

### 1. 讨论核心Web服务增强

```javascript
// 讨论核心Web服务增强功能
const DiscussCoreWebServiceEnhancer = {
    enhanceDiscussCoreWebService: () => {
        const EnhancedDiscussCoreWeb = class extends DiscussCoreWeb {
            constructor(env, services) {
                super(env, services);
                
                // 增强的服务依赖
                this.actionService = services.action;
                this.dialogService = services.dialog;
                this.routerService = services.router;
                this.httpService = services.http;
                this.cookieService = services.cookie;
                
                // 增强的状态管理
                this.enhancedState = reactive({
                    connectionHistory: [],
                    activeConnections: new Set(),
                    foldStateHistory: [],
                    messageDeleteHistory: [],
                    performanceMetrics: {
                        eventProcessingTime: [],
                        memoryUsage: [],
                        errorCount: 0
                    }
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            }
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置连接管理
                this.setupConnectionManagement();
                
                // 设置状态持久化
                this.setupStatePersistence();
                
                // 设置错误处理
                this.setupErrorHandling();
                
                // 设置分析和报告
                this.setupAnalyticsAndReporting();
            }
            
            // 增强的设置方法
            setup() {
                // 原有的用户连接订阅
                this.busService.subscribe("res.users/connection", async (data) => {
                    await this.handleUserConnectionEnhanced(data);
                });
                
                // 增强的线程折叠状态订阅
                this.busService.subscribe("discuss.Thread/fold_state", async (data) => {
                    await this.handleFoldStateEnhanced(data);
                });
                
                // 增强的消息删除监听
                this.env.bus.addEventListener("mail.message/delete", (event) => {
                    this.handleMessageDeleteEnhanced(event);
                });
                
                // 新增的事件订阅
                this.setupAdditionalSubscriptions();
                
                // 启动总线服务
                this.busService.start();
            }
            
            // 增强的用户连接处理
            async handleUserConnectionEnhanced({ partnerId, username }) {
                const startTime = performance.now();
                
                try {
                    // 记录连接事件
                    this.recordConnectionEvent(partnerId, username);
                    
                    // 检查用户状态
                    const userStatus = await this.checkUserStatus(partnerId);
                    
                    // 自定义通知消息
                    const notification = this.createConnectionNotification(username, userStatus);
                    
                    this.notificationService.add(notification.message, {
                        type: notification.type,
                        sticky: notification.sticky,
                        className: notification.className
                    });
                    
                    // 主标签页处理
                    if (this.multiTab.isOnMainTab()) {
                        await this.handleMainTabConnection(partnerId, username, userStatus);
                    }
                    
                    // 触发自定义事件
                    this.triggerConnectionEvent(partnerId, username, userStatus);
                    
                } catch (error) {
                    console.error('处理用户连接失败:', error);
                    this.enhancedState.performanceMetrics.errorCount++;
                } finally {
                    // 记录性能指标
                    const processingTime = performance.now() - startTime;
                    this.enhancedState.performanceMetrics.eventProcessingTime.push({
                        event: 'user_connection',
                        time: processingTime,
                        timestamp: Date.now()
                    });
                }
            }
            
            // 增强的折叠状态处理
            async handleFoldStateEnhanced(data) {
                const startTime = performance.now();
                
                try {
                    // 记录折叠状态变化
                    this.recordFoldStateChange(data);
                    
                    const thread = await this.store.Thread.getOrFetch(data);
                    
                    if (data.fold_state && thread && data.foldStateCount > thread.foldStateCount) {
                        // 验证状态变化的合法性
                        if (this.validateFoldStateChange(thread, data)) {
                            // 更新线程状态
                            thread.foldStateCount = data.foldStateCount;
                            thread.state = data.fold_state;
                            
                            // 处理状态变化
                            await this.processFoldStateChange(thread, data);
                            
                            // 触发状态变化事件
                            this.triggerFoldStateEvent(thread, data);
                        }
                    }
                    
                } catch (error) {
                    console.error('处理折叠状态失败:', error);
                    this.enhancedState.performanceMetrics.errorCount++;
                } finally {
                    const processingTime = performance.now() - startTime;
                    this.enhancedState.performanceMetrics.eventProcessingTime.push({
                        event: 'fold_state',
                        time: processingTime,
                        timestamp: Date.now()
                    });
                }
            }
            
            // 增强的消息删除处理
            handleMessageDeleteEnhanced({ detail: { message } }) {
                const startTime = performance.now();
                
                try {
                    // 记录删除事件
                    this.recordMessageDelete(message);
                    
                    if (message.thread?.model === "discuss.channel") {
                        // 智能刷新策略
                        this.smartRefreshChannels(message.thread);
                        
                        // 更新相关统计
                        this.updateChannelStatistics(message.thread);
                        
                        // 触发删除事件
                        this.triggerMessageDeleteEvent(message);
                    }
                    
                } catch (error) {
                    console.error('处理消息删除失败:', error);
                    this.enhancedState.performanceMetrics.errorCount++;
                } finally {
                    const processingTime = performance.now() - startTime;
                    this.enhancedState.performanceMetrics.eventProcessingTime.push({
                        event: 'message_delete',
                        time: processingTime,
                        timestamp: Date.now()
                    });
                }
            }
            
            // 设置额外的事件订阅
            setupAdditionalSubscriptions() {
                // 用户状态变化
                this.busService.subscribe("res.users/status", (data) => {
                    this.handleUserStatusChange(data);
                });
                
                // 频道成员变化
                this.busService.subscribe("discuss.channel/member", (data) => {
                    this.handleChannelMemberChange(data);
                });
                
                // 系统通知
                this.busService.subscribe("system/notification", (data) => {
                    this.handleSystemNotification(data);
                });
                
                // 性能监控事件
                this.env.bus.addEventListener("performance/metric", (event) => {
                    this.handlePerformanceMetric(event);
                });
            }
            
            // 检查用户状态
            async checkUserStatus(partnerId) {
                try {
                    const partner = await this.store.Persona.getOrFetch({
                        id: partnerId,
                        type: 'partner'
                    });
                    
                    return {
                        isFirstConnection: !this.enhancedState.activeConnections.has(partnerId),
                        isVip: partner.isVip || false,
                        lastSeen: partner.lastSeen,
                        connectionCount: this.getConnectionCount(partnerId)
                    };
                } catch (error) {
                    console.warn('检查用户状态失败:', error);
                    return { isFirstConnection: true };
                }
            }
            
            // 创建连接通知
            createConnectionNotification(username, userStatus) {
                if (userStatus.isFirstConnection) {
                    return {
                        message: _t("%(user)s connected. This is their first connection. Wish them luck.", { user: username }),
                        type: "success",
                        sticky: false,
                        className: "o-discuss-first-connection"
                    };
                } else if (userStatus.isVip) {
                    return {
                        message: _t("VIP user %(user)s is now online.", { user: username }),
                        type: "info",
                        sticky: true,
                        className: "o-discuss-vip-connection"
                    };
                } else {
                    return {
                        message: _t("%(user)s is now online.", { user: username }),
                        type: "info",
                        sticky: false,
                        className: "o-discuss-user-connection"
                    };
                }
            }
            
            // 处理主标签页连接
            async handleMainTabConnection(partnerId, username, userStatus) {
                try {
                    const chat = await this.store.getChat({ partnerId });
                    
                    if (chat) {
                        // 根据用户状态决定是否自动打开聊天
                        const shouldAutoOpen = this.shouldAutoOpenChat(userStatus);
                        
                        if (shouldAutoOpen && !this.ui.isSmall) {
                            this.store.chatHub.opened.add({ thread: chat });
                            
                            // 发送欢迎消息
                            if (userStatus.isFirstConnection) {
                                await this.sendWelcomeMessage(chat, username);
                            }
                        }
                    }
                } catch (error) {
                    console.error('处理主标签页连接失败:', error);
                }
            }
            
            // 智能刷新频道
            smartRefreshChannels(thread) {
                // 防抖刷新，避免频繁刷新
                if (this.channelRefreshTimeout) {
                    clearTimeout(this.channelRefreshTimeout);
                }
                
                this.channelRefreshTimeout = setTimeout(() => {
                    this.store.channels.fetch();
                }, 1000);
            }
            
            // 记录连接事件
            recordConnectionEvent(partnerId, username) {
                this.enhancedState.connectionHistory.push({
                    partnerId,
                    username,
                    timestamp: Date.now(),
                    isMainTab: this.multiTab.isOnMainTab()
                });
                
                this.enhancedState.activeConnections.add(partnerId);
                
                // 限制历史记录大小
                if (this.enhancedState.connectionHistory.length > 100) {
                    this.enhancedState.connectionHistory.splice(0, 50);
                }
            }
            
            // 获取连接统计
            getConnectionStatistics() {
                return {
                    totalConnections: this.enhancedState.connectionHistory.length,
                    activeConnections: this.enhancedState.activeConnections.size,
                    averageProcessingTime: this.calculateAverageProcessingTime(),
                    errorRate: this.calculateErrorRate(),
                    memoryUsage: this.getCurrentMemoryUsage()
                };
            }
            
            // 计算平均处理时间
            calculateAverageProcessingTime() {
                const times = this.enhancedState.performanceMetrics.eventProcessingTime;
                if (times.length === 0) return 0;
                
                const total = times.reduce((sum, metric) => sum + metric.time, 0);
                return total / times.length;
            }
            
            // 计算错误率
            calculateErrorRate() {
                const totalEvents = this.enhancedState.performanceMetrics.eventProcessingTime.length;
                const errorCount = this.enhancedState.performanceMetrics.errorCount;
                
                return totalEvents > 0 ? (errorCount / totalEvents) * 100 : 0;
            }
            
            // 获取当前内存使用情况
            getCurrentMemoryUsage() {
                if (performance.memory) {
                    return {
                        used: performance.memory.usedJSHeapSize,
                        total: performance.memory.totalJSHeapSize,
                        limit: performance.memory.jsHeapSizeLimit
                    };
                }
                return null;
            }
        };
        
        // 更新服务定义
        const enhancedDiscussCoreWeb = {
            dependencies: [
                "bus_service", 
                "mail.store", 
                "notification", 
                "ui", 
                "multi_tab",
                "action",
                "dialog",
                "router",
                "http",
                "cookie"
            ],
            start(env, services) {
                const discussCoreWeb = reactive(new EnhancedDiscussCoreWeb(env, services));
                discussCoreWeb.setup();
                return discussCoreWeb;
            },
        };
        
        // 重新注册增强的服务
        registry.category("services").add("discuss.core.web", enhancedDiscussCoreWeb, { force: true });
    }
};

// 应用讨论核心Web服务增强
DiscussCoreWebServiceEnhancer.enhanceDiscussCoreWebService();
```

## 技术特点

### 1. 服务架构
- 清晰的服务依赖管理
- 响应式服务实例
- 事件驱动设计

### 2. 事件处理
- 总线事件订阅
- 异步事件处理
- 事件状态管理

### 3. 多标签页支持
- 主标签页检测
- 跨标签页状态同步
- 资源优化管理

### 4. 状态管理
- 线程状态同步
- 计数器管理
- 数据一致性保证

## 设计模式

### 1. 服务模式 (Service Pattern)
- 服务的注册和管理
- 依赖注入机制

### 2. 观察者模式 (Observer Pattern)
- 事件的订阅和通知
- 状态变化的监听

### 3. 单例模式 (Singleton Pattern)
- 服务实例的唯一性
- 全局状态管理

## 注意事项

1. **性能优化**: 避免频繁的事件处理影响性能
2. **内存管理**: 及时清理事件监听器和状态
3. **错误处理**: 提供完善的错误处理机制
4. **多标签页**: 正确处理多标签页场景

## 扩展建议

1. **更多事件**: 添加更多类型的事件处理
2. **性能监控**: 实现详细的性能监控
3. **错误报告**: 建立错误报告和分析系统
4. **状态持久化**: 实现状态的持久化存储
5. **分析功能**: 添加用户行为分析功能

该服务为讨论应用在Web环境中提供了完整的事件处理和状态管理功能，是应用的核心协调组件。

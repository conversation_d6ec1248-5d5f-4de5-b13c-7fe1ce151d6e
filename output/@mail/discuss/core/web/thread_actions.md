# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 讨论应用Web环境中的线程操作注册，专门添加了"在讨论中打开"的操作功能。该文件通过线程操作注册表添加了"expand-discuss"操作，集成了操作服务、条件检查、面包屑控制等特性，为用户在聊天窗口中提供了快速跳转到讨论应用的便捷入口，是讨论应用Web环境中线程操作功能的重要组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/web/thread_actions.js`
- **行数**: 49
- **模块**: `@mail/discuss/core/web/thread_actions`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/thread_actions'  // 线程操作注册表
'@odoo/owl'                         // OWL 框架
'@web/core/l10n/translation'        // 国际化
'@web/core/utils/hooks'             // Web核心钩子
```

## 操作注册

### expand-discuss 操作

```javascript
threadActionsRegistry.add("expand-discuss", {
    condition(component) {
        return (
            component.thread &&
            component.props.chatWindow?.isOpen &&
            component.thread.model === "discuss.channel" &&
            !component.ui.isSmall
        );
    },
    setup() {
        const component = useComponent();
        component.actionService = useService("action");
    },
    icon: "fa fa-fw fa-expand",
    name: _t("Open in Discuss"),
    shouldClearBreadcrumbs(component) {
        return false;
    },
    open(component) {
        component.actionService.doAction(
            {
                type: "ir.actions.client",
                tag: "mail.action_discuss",
            },
            {
                clearBreadcrumbs: this.shouldClearBreadcrumbs(component),
                additionalContext: { active_id: component.thread.id },
            }
        );
    },
    sequence: 40,
    sequenceGroup: 20,
});
```

**操作特性**:
- 条件显示控制
- 操作服务集成
- 面包屑管理
- 上下文传递

## 核心功能

### 1. 显示条件

```javascript
condition(component) {
    return (
        component.thread &&
        component.props.chatWindow?.isOpen &&
        component.thread.model === "discuss.channel" &&
        !component.ui.isSmall
    );
}
```

**条件检查功能**:
- **线程存在**: 确保组件有关联的线程
- **聊天窗口打开**: 仅在聊天窗口打开时显示
- **频道类型**: 仅对讨论频道显示
- **非小屏幕**: 在大屏幕设备上显示

### 2. 操作设置

```javascript
setup() {
    const component = useComponent();
    component.actionService = useService("action");
}
```

**设置功能**:
- **组件获取**: 获取当前组件实例
- **服务注入**: 注入操作服务
- **依赖准备**: 为操作执行准备依赖

### 3. 操作执行

```javascript
open(component) {
    component.actionService.doAction(
        {
            type: "ir.actions.client",
            tag: "mail.action_discuss",
        },
        {
            clearBreadcrumbs: this.shouldClearBreadcrumbs(component),
            additionalContext: { active_id: component.thread.id },
        }
    );
}
```

**执行功能**:
- **客户端操作**: 执行客户端类型的操作
- **讨论标签**: 使用讨论应用的操作标签
- **面包屑控制**: 根据条件决定是否清除面包屑
- **上下文传递**: 传递当前线程ID作为活动ID

### 4. 面包屑控制

```javascript
shouldClearBreadcrumbs(component) {
    return false;
}
```

**面包屑功能**:
- **保持导航**: 不清除面包屑导航
- **用户体验**: 保持用户的导航历史
- **返回支持**: 支持用户返回原来的位置

## 使用场景

### 1. 线程操作增强

```javascript
// 线程操作增强功能
const ThreadActionsEnhancer = {
    enhanceThreadActions: () => {
        // 增强原有的expand-discuss操作
        const originalExpandDiscuss = threadActionsRegistry.get("expand-discuss");
        
        threadActionsRegistry.add("expand-discuss", {
            ...originalExpandDiscuss,
            
            // 增强的条件检查
            condition(component) {
                const baseCondition = originalExpandDiscuss.condition(component);
                
                if (!baseCondition) {
                    return false;
                }
                
                // 增强的条件检查
                return this.enhancedConditionCheck(component);
            },
            
            // 增强的条件检查
            enhancedConditionCheck(component) {
                // 检查用户权限
                if (!this.hasDiscussPermission(component)) {
                    return false;
                }
                
                // 检查线程状态
                if (!this.isThreadAccessible(component.thread)) {
                    return false;
                }
                
                // 检查网络状态
                if (!navigator.onLine && this.requiresOnlineAccess(component.thread)) {
                    return false;
                }
                
                return true;
            },
            
            // 增强的设置
            setup() {
                const component = useComponent();
                component.actionService = useService("action");
                component.notificationService = useService("notification");
                component.routerService = useService("router");
                
                // 设置增强功能
                this.setupEnhancedFeatures(component);
            },
            
            // 设置增强功能
            setupEnhancedFeatures(component) {
                // 设置快捷键
                this.setupKeyboardShortcuts(component);
                
                // 设置状态监控
                this.setupStateMonitoring(component);
                
                // 设置性能监控
                this.setupPerformanceMonitoring(component);
            },
            
            // 增强的打开操作
            async open(component) {
                try {
                    // 记录操作开始
                    this.recordActionStart('expand-discuss', component.thread.id);
                    
                    // 预检查
                    if (!this.preOpenCheck(component)) {
                        return;
                    }
                    
                    // 显示加载状态
                    this.showLoadingState(component);
                    
                    // 执行原有操作
                    await this.executeOriginalAction(component);
                    
                    // 后处理
                    await this.postOpenProcessing(component);
                    
                    // 记录操作成功
                    this.recordActionSuccess('expand-discuss', component.thread.id);
                    
                } catch (error) {
                    console.error('打开讨论失败:', error);
                    this.handleOpenError(error, component);
                } finally {
                    this.hideLoadingState(component);
                }
            },
            
            // 执行原有操作
            async executeOriginalAction(component) {
                const actionData = {
                    type: "ir.actions.client",
                    tag: "mail.action_discuss",
                };
                
                const options = {
                    clearBreadcrumbs: this.shouldClearBreadcrumbs(component),
                    additionalContext: { 
                        active_id: component.thread.id,
                        from_chat_window: true,
                        timestamp: Date.now()
                    },
                };
                
                await component.actionService.doAction(actionData, options);
            },
            
            // 增强的面包屑控制
            shouldClearBreadcrumbs(component) {
                // 检查用户偏好
                const userPreference = this.getUserBreadcrumbPreference();
                if (userPreference !== null) {
                    return userPreference;
                }
                
                // 检查当前上下文
                if (this.isInModalContext(component)) {
                    return true;
                }
                
                // 检查导航历史
                if (this.hasComplexNavigationHistory()) {
                    return false;
                }
                
                // 默认不清除
                return false;
            },
            
            // 预打开检查
            preOpenCheck(component) {
                // 检查线程是否仍然存在
                if (!component.thread || component.thread.isDeleted) {
                    this.showErrorMessage('线程不存在或已被删除');
                    return false;
                }
                
                // 检查用户是否仍有权限
                if (!this.hasCurrentPermission(component)) {
                    this.showErrorMessage('您没有权限访问此线程');
                    return false;
                }
                
                return true;
            },
            
            // 后处理
            async postOpenProcessing(component) {
                try {
                    // 更新最近访问
                    await this.updateRecentAccess(component.thread);
                    
                    // 标记为已读
                    await this.markAsRead(component.thread);
                    
                    // 更新用户活动
                    await this.updateUserActivity(component.thread);
                    
                    // 关闭聊天窗口（如果需要）
                    if (this.shouldCloseChatWindow(component)) {
                        component.props.chatWindow?.close();
                    }
                    
                } catch (error) {
                    console.warn('后处理失败:', error);
                }
            },
            
            // 检查讨论权限
            hasDiscussPermission(component) {
                const user = component.store?.self;
                return user && user.isInternalUser;
            },
            
            // 检查线程是否可访问
            isThreadAccessible(thread) {
                if (!thread) {
                    return false;
                }
                
                // 检查线程是否被归档
                if (thread.isArchived) {
                    return false;
                }
                
                // 检查是否为成员
                if (!thread.hasSelfAsMember) {
                    return false;
                }
                
                return true;
            },
            
            // 检查是否需要在线访问
            requiresOnlineAccess(thread) {
                // 私有频道需要在线访问
                return thread.channel_type === 'group' || thread.isPrivate;
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts(component) {
                this.keyboardHandler = (event) => {
                    // Ctrl+Shift+D 打开讨论
                    if (event.ctrlKey && event.shiftKey && event.key === 'D') {
                        event.preventDefault();
                        if (this.condition(component)) {
                            this.open(component);
                        }
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 显示加载状态
            showLoadingState(component) {
                if (component.notificationService) {
                    this.loadingNotification = component.notificationService.add(
                        '正在打开讨论...',
                        { type: 'info', sticky: true }
                    );
                }
            },
            
            // 隐藏加载状态
            hideLoadingState(component) {
                if (this.loadingNotification) {
                    this.loadingNotification.close();
                    this.loadingNotification = null;
                }
            },
            
            // 处理打开错误
            handleOpenError(error, component) {
                this.recordActionError('expand-discuss', component.thread?.id, error);
                
                let errorMessage = '打开讨论失败';
                
                if (error.message.includes('permission')) {
                    errorMessage = '权限不足，无法打开讨论';
                } else if (error.message.includes('network')) {
                    errorMessage = '网络错误，请检查网络连接';
                } else if (error.message.includes('not found')) {
                    errorMessage = '线程不存在或已被删除';
                }
                
                this.showErrorMessage(errorMessage);
            },
            
            // 显示错误消息
            showErrorMessage(message) {
                if (this.notificationService) {
                    this.notificationService.add(message, { type: 'error' });
                }
            },
            
            // 记录操作开始
            recordActionStart(actionType, threadId) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('thread_action_logs') || '[]'
                    );
                    
                    actions.push({
                        type: actionType,
                        threadId,
                        status: 'started',
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (actions.length > 100) {
                        actions.splice(0, actions.length - 100);
                    }
                    
                    localStorage.setItem('thread_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录操作开始失败:', error);
                }
            },
            
            // 记录操作成功
            recordActionSuccess(actionType, threadId) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('thread_action_logs') || '[]'
                    );
                    
                    actions.push({
                        type: actionType,
                        threadId,
                        status: 'success',
                        timestamp: Date.now()
                    });
                    
                    localStorage.setItem('thread_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录操作成功失败:', error);
                }
            },
            
            // 记录操作错误
            recordActionError(actionType, threadId, error) {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('thread_action_logs') || '[]'
                    );
                    
                    actions.push({
                        type: actionType,
                        threadId,
                        status: 'error',
                        error: error.message,
                        timestamp: Date.now()
                    });
                    
                    localStorage.setItem('thread_action_logs', JSON.stringify(actions));
                } catch (error) {
                    console.warn('记录操作错误失败:', error);
                }
            },
            
            // 获取操作统计
            getActionStatistics() {
                try {
                    const actions = JSON.parse(
                        localStorage.getItem('thread_action_logs') || '[]'
                    );
                    
                    const stats = {
                        total: actions.length,
                        success: actions.filter(a => a.status === 'success').length,
                        errors: actions.filter(a => a.status === 'error').length,
                        byType: {}
                    };
                    
                    actions.forEach(action => {
                        if (!stats.byType[action.type]) {
                            stats.byType[action.type] = { total: 0, success: 0, errors: 0 };
                        }
                        
                        stats.byType[action.type].total++;
                        
                        if (action.status === 'success') {
                            stats.byType[action.type].success++;
                        } else if (action.status === 'error') {
                            stats.byType[action.type].errors++;
                        }
                    });
                    
                    return stats;
                } catch (error) {
                    return { total: 0, success: 0, errors: 0, byType: {} };
                }
            }
        }, { force: true });
        
        // 添加更多线程操作
        this.addAdditionalThreadActions();
    },
    
    // 添加额外的线程操作
    addAdditionalThreadActions() {
        // 添加复制链接操作
        threadActionsRegistry.add("copy-link", {
            condition(component) {
                return component.thread && component.thread.model === "discuss.channel";
            },
            setup() {
                const component = useComponent();
                component.notificationService = useService("notification");
            },
            icon: "fa fa-fw fa-link",
            name: _t("Copy Link"),
            async open(component) {
                try {
                    const url = `${window.location.origin}/discuss/channel/${component.thread.id}`;
                    await navigator.clipboard.writeText(url);
                    component.notificationService.add('链接已复制到剪贴板', { type: 'success' });
                } catch (error) {
                    console.error('复制链接失败:', error);
                    component.notificationService.add('复制链接失败', { type: 'error' });
                }
            },
            sequence: 41,
            sequenceGroup: 20,
        });
        
        // 添加分享操作
        threadActionsRegistry.add("share-thread", {
            condition(component) {
                return component.thread && 
                       component.thread.model === "discuss.channel" &&
                       navigator.share;
            },
            setup() {
                const component = useComponent();
                component.notificationService = useService("notification");
            },
            icon: "fa fa-fw fa-share",
            name: _t("Share"),
            async open(component) {
                try {
                    await navigator.share({
                        title: component.thread.name,
                        text: `Join the discussion in ${component.thread.name}`,
                        url: `${window.location.origin}/discuss/channel/${component.thread.id}`
                    });
                } catch (error) {
                    if (error.name !== 'AbortError') {
                        console.error('分享失败:', error);
                        component.notificationService.add('分享失败', { type: 'error' });
                    }
                }
            },
            sequence: 42,
            sequenceGroup: 20,
        });
    }
};

// 应用线程操作增强
ThreadActionsEnhancer.enhanceThreadActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作配置
- 条件化显示控制

### 2. 服务集成
- 操作服务的使用
- 钩子机制集成
- 依赖注入支持

### 3. 条件控制
- 多重条件检查
- 智能显示逻辑
- 上下文感知

### 4. 用户体验
- 面包屑导航保持
- 上下文信息传递
- 无缝跳转体验

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态操作配置

### 2. 策略模式 (Strategy Pattern)
- 不同条件下的显示策略
- 可配置的行为模式

### 3. 命令模式 (Command Pattern)
- 操作的命令化封装
- 统一的执行接口

## 注意事项

1. **条件检查**: 确保操作显示条件的正确性
2. **权限验证**: 验证用户的操作权限
3. **状态管理**: 正确管理操作的状态
4. **用户体验**: 提供流畅的操作体验

## 扩展建议

1. **更多操作**: 添加更多类型的线程操作
2. **权限控制**: 实现更细粒度的权限控制
3. **快捷键**: 添加键盘快捷键支持
4. **批量操作**: 支持批量线程操作
5. **自定义操作**: 允许用户自定义操作

该文件为讨论应用的线程提供了重要的"在讨论中打开"功能，是聊天窗口和讨论应用之间的重要桥梁。

# Message Actions - 消息操作

## 概述

`message_actions.js` 实现了对 Odoo 邮件系统中消息操作的补丁扩展，专门增强了编辑操作的功能。该补丁通过Odoo的补丁机制扩展了消息操作注册表中的编辑操作，添加了对消息中提及频道的预处理功能，确保在编辑消息时正确处理频道链接和提及，是讨论应用中消息编辑功能的重要增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/message_actions.js`
- **行数**: 30
- **模块**: `@mail/discuss/core/common/message_actions`

## 依赖关系

```javascript
// 操作依赖
'@mail/core/common/message_actions'  // 消息操作注册表
'@web/core/utils/patch'              // 补丁工具
```

## 补丁定义

### 编辑操作补丁

```javascript
const editAction = messageActionsRegistry.get("edit");

patch(editAction, {
    onClick(component) {
        const body = new DOMParser().parseFromString(component.message.body, "text/html");
        const mentionedChannelElements = body.querySelectorAll(".o_channel_redirect");
        component.message.mentionedChannelPromises = Array.from(mentionedChannelElements)
            .filter((el) => el.dataset.oeModel === "discuss.channel")
            .map(async (el) => {
                return component.store.Thread.getOrFetch({
                    id: el.dataset.oeId,
                    model: el.dataset.oeModel,
                });
            });
        return super.onClick(component);
    },
});
```

**补丁特性**:
- 获取现有编辑操作
- 扩展点击处理逻辑
- 预处理频道提及
- 保持原有功能

## 核心功能

### 1. HTML解析处理

```javascript
const body = new DOMParser().parseFromString(component.message.body, "text/html");
const mentionedChannelElements = body.querySelectorAll(".o_channel_redirect");
```

**解析功能**:
- **DOM解析**: 使用DOMParser解析消息HTML内容
- **元素查找**: 查找所有频道重定向元素
- **类选择器**: 使用`.o_channel_redirect`类选择器

### 2. 频道提及预处理

```javascript
component.message.mentionedChannelPromises = Array.from(mentionedChannelElements)
    .filter((el) => el.dataset.oeModel === "discuss.channel")
    .map(async (el) => {
        return component.store.Thread.getOrFetch({
            id: el.dataset.oeId,
            model: el.dataset.oeModel,
        });
    });
```

**预处理功能**:
- **模型过滤**: 只处理讨论频道类型的元素
- **异步获取**: 异步获取或创建线程对象
- **Promise数组**: 创建Promise数组用于后续处理
- **数据属性**: 使用元素的数据属性获取ID和模型

## 使用场景

### 1. 消息操作增强

```javascript
// 消息操作增强功能
const MessageActionsEnhancer = {
    enhanceMessageActions: () => {
        // 增强编辑操作
        const enhancedEditAction = {
            ...messageActionsRegistry.get("edit"),
            
            async onClick(component) {
                try {
                    // 预处理检查
                    const preProcessResult = await MessageActionsEnhancer.preProcessMessage(component);
                    
                    if (!preProcessResult.canEdit) {
                        component.env.services.notification.add(
                            preProcessResult.message,
                            { type: 'warning' }
                        );
                        return;
                    }
                    
                    // 处理频道提及
                    await MessageActionsEnhancer.processMentionedChannels(component);
                    
                    // 处理用户提及
                    await MessageActionsEnhancer.processMentionedUsers(component);
                    
                    // 处理附件引用
                    await MessageActionsEnhancer.processAttachmentReferences(component);
                    
                    // 调用原始编辑逻辑
                    return super.onClick(component);
                    
                } catch (error) {
                    console.error('消息编辑预处理失败:', error);
                    component.env.services.notification.add(
                        '编辑消息时发生错误',
                        { type: 'error' }
                    );
                }
            }
        };
        
        // 替换编辑操作
        messageActionsRegistry.add("edit", enhancedEditAction);
        
        // 添加新的消息操作
        MessageActionsEnhancer.addCustomActions();
    },
    
    // 预处理消息
    async preProcessMessage(component) {
        const message = component.message;
        
        // 检查编辑权限
        if (!message.editable) {
            return { canEdit: false, message: '此消息不可编辑' };
        }
        
        // 检查编辑时间限制
        const editTimeLimit = 24 * 60 * 60 * 1000; // 24小时
        const messageAge = Date.now() - new Date(message.datetime).getTime();
        
        if (messageAge > editTimeLimit) {
            return { canEdit: false, message: '消息编辑时间已过期' };
        }
        
        // 检查消息状态
        if (message.isDeleted) {
            return { canEdit: false, message: '已删除的消息不能编辑' };
        }
        
        return { canEdit: true };
    },
    
    // 处理提及的频道
    async processMentionedChannels(component) {
        const body = new DOMParser().parseFromString(component.message.body, "text/html");
        const mentionedChannelElements = body.querySelectorAll(".o_channel_redirect");
        
        const channelPromises = Array.from(mentionedChannelElements)
            .filter((el) => el.dataset.oeModel === "discuss.channel")
            .map(async (el) => {
                try {
                    const thread = await component.store.Thread.getOrFetch({
                        id: parseInt(el.dataset.oeId),
                        model: el.dataset.oeModel,
                    });
                    
                    // 验证频道访问权限
                    if (!thread.hasAccess) {
                        el.classList.add('o_channel_redirect_invalid');
                        el.title = '您没有访问此频道的权限';
                    }
                    
                    return thread;
                } catch (error) {
                    console.warn('获取频道失败:', el.dataset.oeId, error);
                    el.classList.add('o_channel_redirect_invalid');
                    el.title = '频道不存在或已删除';
                    return null;
                }
            });
        
        component.message.mentionedChannelPromises = channelPromises;
        component.message.mentionedChannels = await Promise.all(channelPromises);
    },
    
    // 处理提及的用户
    async processMentionedUsers(component) {
        const body = new DOMParser().parseFromString(component.message.body, "text/html");
        const mentionedUserElements = body.querySelectorAll(".o_mail_redirect");
        
        const userPromises = Array.from(mentionedUserElements)
            .filter((el) => el.dataset.oeModel === "res.partner")
            .map(async (el) => {
                try {
                    const partner = await component.store.Persona.getOrFetch({
                        id: parseInt(el.dataset.oeId),
                        type: "partner"
                    });
                    
                    // 验证用户状态
                    if (!partner.active) {
                        el.classList.add('o_mail_redirect_inactive');
                        el.title = '此用户已停用';
                    }
                    
                    return partner;
                } catch (error) {
                    console.warn('获取用户失败:', el.dataset.oeId, error);
                    el.classList.add('o_mail_redirect_invalid');
                    el.title = '用户不存在';
                    return null;
                }
            });
        
        component.message.mentionedUserPromises = userPromises;
        component.message.mentionedUsers = await Promise.all(userPromises);
    },
    
    // 处理附件引用
    async processAttachmentReferences(component) {
        const body = new DOMParser().parseFromString(component.message.body, "text/html");
        const attachmentElements = body.querySelectorAll(".o_attachment_redirect");
        
        const attachmentPromises = Array.from(attachmentElements).map(async (el) => {
            try {
                const attachmentId = parseInt(el.dataset.attachmentId);
                const attachment = component.message.attachments.find(att => att.id === attachmentId);
                
                if (!attachment) {
                    el.classList.add('o_attachment_redirect_invalid');
                    el.title = '附件不存在或已删除';
                    return null;
                }
                
                // 验证附件访问权限
                if (!attachment.isAccessible) {
                    el.classList.add('o_attachment_redirect_invalid');
                    el.title = '您没有访问此附件的权限';
                }
                
                return attachment;
            } catch (error) {
                console.warn('处理附件引用失败:', el.dataset.attachmentId, error);
                return null;
            }
        });
        
        component.message.referencedAttachments = await Promise.all(attachmentPromises);
    },
    
    // 添加自定义操作
    addCustomActions() {
        // 添加复制消息操作
        messageActionsRegistry.add("copy", {
            condition: (component) => true,
            icon: "fa fa-fw fa-copy",
            name: "复制消息",
            onClick: (component) => {
                MessageActionsEnhancer.copyMessage(component);
            },
            sequence: 15,
        });
        
        // 添加引用回复操作
        messageActionsRegistry.add("quote_reply", {
            condition: (component) => component.thread && !component.thread.readonly,
            icon: "fa fa-fw fa-quote-left",
            name: "引用回复",
            onClick: (component) => {
                MessageActionsEnhancer.quoteReply(component);
            },
            sequence: 25,
        });
        
        // 添加转发消息操作
        messageActionsRegistry.add("forward", {
            condition: (component) => component.message.canForward,
            icon: "fa fa-fw fa-share",
            name: "转发消息",
            onClick: (component) => {
                MessageActionsEnhancer.forwardMessage(component);
            },
            sequence: 35,
        });
        
        // 添加消息详情操作
        messageActionsRegistry.add("details", {
            condition: (component) => component.message.hasDetails,
            icon: "fa fa-fw fa-info-circle",
            name: "消息详情",
            onClick: (component) => {
                MessageActionsEnhancer.showMessageDetails(component);
            },
            sequence: 45,
        });
    },
    
    // 复制消息
    async copyMessage(component) {
        try {
            const textContent = MessageActionsEnhancer.extractTextContent(component.message.body);
            await navigator.clipboard.writeText(textContent);
            
            component.env.services.notification.add(
                '消息已复制到剪贴板',
                { type: 'success' }
            );
        } catch (error) {
            console.error('复制消息失败:', error);
            component.env.services.notification.add(
                '复制失败',
                { type: 'error' }
            );
        }
    },
    
    // 提取文本内容
    extractTextContent(htmlContent) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;
        
        // 处理特殊元素
        const channelLinks = tempDiv.querySelectorAll('.o_channel_redirect');
        channelLinks.forEach(link => {
            link.textContent = `#${link.textContent}`;
        });
        
        const userMentions = tempDiv.querySelectorAll('.o_mail_redirect');
        userMentions.forEach(mention => {
            mention.textContent = `@${mention.textContent}`;
        });
        
        return tempDiv.textContent || tempDiv.innerText || '';
    },
    
    // 引用回复
    quoteReply(component) {
        const quotedText = MessageActionsEnhancer.extractTextContent(component.message.body);
        const authorName = component.message.author?.name || '未知用户';
        const quoteContent = `> ${quotedText.split('\n').join('\n> ')}\n\n`;
        
        // 设置编辑器内容
        if (component.thread.composer) {
            component.thread.composer.textInputContent = quoteContent;
            component.thread.composer.focus();
        }
    },
    
    // 转发消息
    async forwardMessage(component) {
        try {
            // 打开转发对话框
            const action = await component.env.services.action.doAction({
                type: 'ir.actions.act_window',
                name: '转发消息',
                res_model: 'mail.compose.message',
                view_mode: 'form',
                target: 'new',
                context: {
                    default_composition_mode: 'forward',
                    default_parent_id: component.message.id,
                    default_body: component.message.body
                }
            });
        } catch (error) {
            console.error('转发消息失败:', error);
            component.env.services.notification.add(
                '转发失败',
                { type: 'error' }
            );
        }
    },
    
    // 显示消息详情
    showMessageDetails(component) {
        const message = component.message;
        
        const details = {
            id: message.id,
            author: message.author?.name || '未知',
            datetime: message.datetime,
            editedDatetime: message.editedDatetime,
            messageType: message.message_type,
            attachmentCount: message.attachments?.length || 0,
            reactionCount: message.reactions?.length || 0,
            isEdited: Boolean(message.editedDatetime),
            threadName: component.thread?.displayName || '未知线程'
        };
        
        // 创建详情对话框
        const dialog = document.createElement('div');
        dialog.className = 'message-details-dialog-overlay';
        dialog.innerHTML = MessageActionsEnhancer.createDetailsHTML(details);
        
        document.body.appendChild(dialog);
        
        // 绑定关闭事件
        const closeBtn = dialog.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => {
            dialog.remove();
        });
        
        // 点击外部关闭
        dialog.addEventListener('click', (event) => {
            if (event.target === dialog) {
                dialog.remove();
            }
        });
    },
    
    // 创建详情HTML
    createDetailsHTML(details) {
        return `
            <div class="message-details-dialog">
                <div class="dialog-header">
                    <h3>消息详情</h3>
                    <button class="close-btn">&times;</button>
                </div>
                
                <div class="dialog-content">
                    <div class="detail-item">
                        <label>消息ID:</label>
                        <span>${details.id}</span>
                    </div>
                    
                    <div class="detail-item">
                        <label>发送者:</label>
                        <span>${details.author}</span>
                    </div>
                    
                    <div class="detail-item">
                        <label>发送时间:</label>
                        <span>${new Date(details.datetime).toLocaleString()}</span>
                    </div>
                    
                    ${details.isEdited ? `
                        <div class="detail-item">
                            <label>编辑时间:</label>
                            <span>${new Date(details.editedDatetime).toLocaleString()}</span>
                        </div>
                    ` : ''}
                    
                    <div class="detail-item">
                        <label>消息类型:</label>
                        <span>${details.messageType}</span>
                    </div>
                    
                    <div class="detail-item">
                        <label>所属线程:</label>
                        <span>${details.threadName}</span>
                    </div>
                    
                    <div class="detail-item">
                        <label>附件数量:</label>
                        <span>${details.attachmentCount}</span>
                    </div>
                    
                    <div class="detail-item">
                        <label>反应数量:</label>
                        <span>${details.reactionCount}</span>
                    </div>
                </div>
                
                <div class="dialog-footer">
                    <button class="btn btn-secondary close-btn">关闭</button>
                </div>
            </div>
        `;
    }
};

// 应用消息操作增强
MessageActionsEnhancer.enhanceMessageActions();
```

## 技术特点

### 1. 补丁机制
- 非侵入式功能扩展
- 保持原有操作完整性
- 运行时功能增强

### 2. DOM处理
- HTML内容解析
- 元素查找和过滤
- 数据属性提取

### 3. 异步处理
- Promise数组管理
- 异步数据获取
- 错误处理机制

### 4. 数据预处理
- 频道提及预处理
- 线程对象获取
- 状态管理优化

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同元素的处理策略
- 可配置的行为模式

### 3. 装饰器模式 (Decorator Pattern)
- 功能的装饰和增强
- 透明的功能扩展

## 注意事项

1. **性能优化**: 避免频繁的DOM解析操作
2. **错误处理**: 完善的异常处理机制
3. **数据一致性**: 确保预处理数据的正确性
4. **内存管理**: 及时清理Promise和DOM引用

## 扩展建议

1. **更多操作**: 添加更多消息相关操作
2. **权限控制**: 增强操作权限验证
3. **批量处理**: 支持批量消息操作
4. **自定义操作**: 支持用户自定义操作
5. **操作历史**: 记录操作历史和统计

该补丁为讨论应用的消息编辑功能提供了重要的增强，确保频道提及的正确处理。

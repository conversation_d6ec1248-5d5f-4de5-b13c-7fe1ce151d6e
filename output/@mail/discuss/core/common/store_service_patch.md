# Store Service Patch - 存储服务补丁

## 概述

`store_service_patch.js` 实现了对 Odoo 邮件系统中存储服务的补丁扩展，专门为讨论应用添加了特定的功能增强。该补丁通过Odoo的补丁机制扩展了Store服务，添加了在线成员状态定义、链接跟随处理、成员排序等功能，为讨论应用提供了专门的数据管理和用户交互逻辑，是讨论应用中数据层功能增强的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/store_service_patch.js`
- **行数**: 30
- **模块**: `@mail/discuss/core/common/store_service_patch`

## 依赖关系

```javascript
// 服务依赖
'@mail/core/common/store_service'  // 存储服务
'@web/core/utils/patch'            // 补丁工具
```

## 补丁定义

### Store 补丁

```javascript
const storeServicePatch = {
    get onlineMemberStatuses() {
        return ["away", "bot", "online"];
    },
    onLinkFollowed(fromThread) {
        super.onLinkFollowed(...arguments);
        if (!this.env.isSmall && fromThread?.model === "discuss.channel") {
            fromThread.open(true, { autofocus: false });
        }
    },
    sortMembers(m1, m2) {
        return m1.persona.name?.localeCompare(m2.persona.name) || m1.id - m2.id;
    },
};

patch(Store.prototype, storeServicePatch);
```

**补丁特性**:
- 扩展存储服务功能
- 添加讨论应用特定逻辑
- 保持原有功能完整性
- 提供专门的数据处理方法

## 核心功能

### 1. 在线成员状态定义

```javascript
get onlineMemberStatuses() {
    return ["away", "bot", "online"];
}
```

**状态定义**:
- **away**: 离开状态
- **bot**: 机器人状态
- **online**: 在线状态
- **用途**: 用于判断成员是否被视为在线

### 2. 链接跟随处理

```javascript
onLinkFollowed(fromThread) {
    super.onLinkFollowed(...arguments);
    if (!this.env.isSmall && fromThread?.model === "discuss.channel") {
        fromThread.open(true, { autofocus: false });
    }
}
```

**跟随逻辑**:
- **父类调用**: 首先执行原有的链接跟随逻辑
- **环境检查**: 仅在非小屏幕环境下执行
- **模型检查**: 仅对讨论频道模型生效
- **自动打开**: 自动打开频道但不自动聚焦

### 3. 成员排序

```javascript
sortMembers(m1, m2) {
    return m1.persona.name?.localeCompare(m2.persona.name) || m1.id - m2.id;
}
```

**排序逻辑**:
- **主要排序**: 按伙伴名称的本地化比较排序
- **次要排序**: 名称相同时按ID排序
- **空值处理**: 使用可选链操作符处理空名称

## 使用场景

### 1. 存储服务增强

```javascript
// 存储服务增强功能
const StoreServiceEnhancer = {
    enhanceStoreService: () => {
        const EnhancedStoreServicePatch = {
            // 增强的在线状态定义
            get onlineMemberStatuses() {
                return ["away", "bot", "online", "busy"]; // 添加忙碌状态
            },
            
            // 获取扩展的在线状态
            get extendedOnlineStatuses() {
                return {
                    online: { priority: 0, label: '在线', color: '#00ff00' },
                    away: { priority: 1, label: '离开', color: '#ffff00' },
                    busy: { priority: 2, label: '忙碌', color: '#ff0000' },
                    bot: { priority: 3, label: '机器人', color: '#0000ff' },
                    offline: { priority: 4, label: '离线', color: '#808080' }
                };
            },
            
            // 增强的链接跟随处理
            onLinkFollowed(fromThread) {
                // 记录链接跟随事件
                this.recordLinkFollowEvent(fromThread);
                
                // 调用原有逻辑
                super.onLinkFollowed(...arguments);
                
                // 增强的处理逻辑
                if (!this.env.isSmall && fromThread?.model === "discuss.channel") {
                    // 检查用户偏好
                    const userPrefs = this.getUserPreferences();
                    
                    if (userPrefs.autoOpenChannels) {
                        fromThread.open(true, { 
                            autofocus: userPrefs.autoFocusChannels,
                            openInSidebar: userPrefs.openInSidebar
                        });
                    }
                    
                    // 更新最近访问的频道
                    this.updateRecentChannels(fromThread);
                }
            },
            
            // 增强的成员排序
            sortMembers(m1, m2) {
                // 首先按在线状态排序
                const statusComparison = this.compareByOnlineStatus(m1, m2);
                if (statusComparison !== 0) {
                    return statusComparison;
                }
                
                // 然后按角色排序
                const roleComparison = this.compareByRole(m1, m2);
                if (roleComparison !== 0) {
                    return roleComparison;
                }
                
                // 最后按名称和ID排序
                return m1.persona.name?.localeCompare(m2.persona.name) || m1.id - m2.id;
            },
            
            // 按在线状态比较
            compareByOnlineStatus(m1, m2) {
                const statuses = this.extendedOnlineStatuses;
                const status1 = this.getPersonaOnlineStatus(m1.persona);
                const status2 = this.getPersonaOnlineStatus(m2.persona);
                
                const priority1 = statuses[status1]?.priority ?? 999;
                const priority2 = statuses[status2]?.priority ?? 999;
                
                return priority1 - priority2;
            },
            
            // 按角色比较
            compareByRole(m1, m2) {
                const rolePriority = {
                    'admin': 0,
                    'moderator': 1,
                    'member': 2,
                    'guest': 3
                };
                
                const role1 = m1.role || 'member';
                const role2 = m2.role || 'member';
                
                const priority1 = rolePriority[role1] ?? 999;
                const priority2 = rolePriority[role2] ?? 999;
                
                return priority1 - priority2;
            },
            
            // 获取用户偏好
            getUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('discuss_user_preferences') || '{}'
                    );
                    
                    return {
                        autoOpenChannels: prefs.autoOpenChannels ?? true,
                        autoFocusChannels: prefs.autoFocusChannels ?? false,
                        openInSidebar: prefs.openInSidebar ?? false,
                        sortMembersByStatus: prefs.sortMembersByStatus ?? true,
                        sortMembersByRole: prefs.sortMembersByRole ?? true
                    };
                } catch (error) {
                    console.warn('获取用户偏好失败:', error);
                    return {
                        autoOpenChannels: true,
                        autoFocusChannels: false,
                        openInSidebar: false,
                        sortMembersByStatus: true,
                        sortMembersByRole: true
                    };
                }
            },
            
            // 设置用户偏好
            setUserPreferences(preferences) {
                try {
                    const currentPrefs = this.getUserPreferences();
                    const newPrefs = { ...currentPrefs, ...preferences };
                    
                    localStorage.setItem(
                        'discuss_user_preferences',
                        JSON.stringify(newPrefs)
                    );
                    
                    // 触发偏好更改事件
                    this.trigger('user_preferences_changed', newPrefs);
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 记录链接跟随事件
            recordLinkFollowEvent(fromThread) {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('link_follow_events') || '[]'
                    );
                    
                    events.push({
                        threadId: fromThread?.id,
                        threadModel: fromThread?.model,
                        threadName: fromThread?.displayName,
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        screenSize: `${window.screen.width}x${window.screen.height}`
                    });
                    
                    // 保留最近100个事件
                    if (events.length > 100) {
                        events.splice(0, events.length - 100);
                    }
                    
                    localStorage.setItem('link_follow_events', JSON.stringify(events));
                } catch (error) {
                    console.warn('记录链接跟随事件失败:', error);
                }
            },
            
            // 更新最近访问的频道
            updateRecentChannels(thread) {
                if (thread.model !== 'discuss.channel') {
                    return;
                }
                
                try {
                    const recentChannels = JSON.parse(
                        localStorage.getItem('recent_channels') || '[]'
                    );
                    
                    // 移除已存在的记录
                    const existingIndex = recentChannels.findIndex(
                        channel => channel.id === thread.id
                    );
                    
                    if (existingIndex !== -1) {
                        recentChannels.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    recentChannels.unshift({
                        id: thread.id,
                        name: thread.displayName,
                        model: thread.model,
                        lastAccessed: Date.now()
                    });
                    
                    // 保留最近20个频道
                    if (recentChannels.length > 20) {
                        recentChannels.splice(20);
                    }
                    
                    localStorage.setItem('recent_channels', JSON.stringify(recentChannels));
                } catch (error) {
                    console.warn('更新最近频道失败:', error);
                }
            },
            
            // 获取最近访问的频道
            getRecentChannels() {
                try {
                    const recentChannels = JSON.parse(
                        localStorage.getItem('recent_channels') || '[]'
                    );
                    
                    // 过滤掉不存在的频道
                    return recentChannels.filter(channel => {
                        const thread = this.Thread.get({ id: channel.id, model: channel.model });
                        return thread && thread.hasAccess;
                    });
                } catch (error) {
                    console.warn('获取最近频道失败:', error);
                    return [];
                }
            },
            
            // 获取在线成员数量
            getOnlineMemberCount(thread) {
                if (!thread || !thread.channelMembers) {
                    return 0;
                }
                
                return thread.channelMembers.filter(member => {
                    const status = this.getPersonaOnlineStatus(member.persona);
                    return this.onlineMemberStatuses.includes(status);
                }).length;
            },
            
            // 获取成员统计
            getMemberStatistics(thread) {
                if (!thread || !thread.channelMembers) {
                    return {
                        total: 0,
                        online: 0,
                        offline: 0,
                        byStatus: {},
                        byRole: {}
                    };
                }
                
                const stats = {
                    total: thread.channelMembers.length,
                    online: 0,
                    offline: 0,
                    byStatus: {},
                    byRole: {}
                };
                
                thread.channelMembers.forEach(member => {
                    const status = this.getPersonaOnlineStatus(member.persona);
                    const role = member.role || 'member';
                    
                    // 状态统计
                    stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
                    
                    if (this.onlineMemberStatuses.includes(status)) {
                        stats.online++;
                    } else {
                        stats.offline++;
                    }
                    
                    // 角色统计
                    stats.byRole[role] = (stats.byRole[role] || 0) + 1;
                });
                
                return stats;
            },
            
            // 搜索成员
            searchMembers(thread, query) {
                if (!thread || !thread.channelMembers || !query) {
                    return [];
                }
                
                const normalizedQuery = query.toLowerCase();
                
                return thread.channelMembers.filter(member => {
                    const name = member.persona.name?.toLowerCase() || '';
                    const email = member.persona.email?.toLowerCase() || '';
                    
                    return name.includes(normalizedQuery) || email.includes(normalizedQuery);
                }).sort((a, b) => this.sortMembers(a, b));
            },
            
            // 过滤成员
            filterMembers(thread, filters) {
                if (!thread || !thread.channelMembers) {
                    return [];
                }
                
                let members = [...thread.channelMembers];
                
                // 按状态过滤
                if (filters.status && filters.status !== 'all') {
                    members = members.filter(member => {
                        const status = this.getPersonaOnlineStatus(member.persona);
                        return status === filters.status;
                    });
                }
                
                // 按角色过滤
                if (filters.role && filters.role !== 'all') {
                    members = members.filter(member => {
                        const role = member.role || 'member';
                        return role === filters.role;
                    });
                }
                
                // 按在线状态过滤
                if (filters.onlineOnly) {
                    members = members.filter(member => {
                        const status = this.getPersonaOnlineStatus(member.persona);
                        return this.onlineMemberStatuses.includes(status);
                    });
                }
                
                return members.sort((a, b) => this.sortMembers(a, b));
            },
            
            // 获取链接跟随统计
            getLinkFollowStatistics() {
                try {
                    const events = JSON.parse(
                        localStorage.getItem('link_follow_events') || '[]'
                    );
                    
                    const stats = {
                        totalEvents: events.length,
                        byThread: {},
                        byModel: {},
                        recentEvents: events.slice(-10)
                    };
                    
                    events.forEach(event => {
                        // 按线程统计
                        if (event.threadId) {
                            stats.byThread[event.threadId] = (stats.byThread[event.threadId] || 0) + 1;
                        }
                        
                        // 按模型统计
                        if (event.threadModel) {
                            stats.byModel[event.threadModel] = (stats.byModel[event.threadModel] || 0) + 1;
                        }
                    });
                    
                    return stats;
                } catch (error) {
                    console.warn('获取链接跟随统计失败:', error);
                    return {
                        totalEvents: 0,
                        byThread: {},
                        byModel: {},
                        recentEvents: []
                    };
                }
            }
        };
        
        patch(Store.prototype, EnhancedStoreServicePatch);
    }
};

// 应用存储服务增强
StoreServiceEnhancer.enhanceStoreService();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 状态管理
- 在线状态定义和管理
- 成员状态跟踪
- 用户偏好存储

### 3. 数据处理
- 智能成员排序
- 链接跟随处理
- 统计数据计算

### 4. 用户体验
- 自动化操作处理
- 个性化设置支持
- 响应式界面适配

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同排序策略的实现
- 可配置的处理逻辑

### 3. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 事件驱动的处理

## 注意事项

1. **性能优化**: 避免频繁的排序和计算操作
2. **数据一致性**: 确保状态数据的实时性
3. **内存管理**: 及时清理事件监听器和缓存
4. **兼容性**: 确保与原有服务的兼容性

## 扩展建议

1. **更多状态**: 添加更多用户状态类型
2. **智能排序**: 实现更智能的成员排序算法
3. **缓存优化**: 优化数据缓存和更新机制
4. **分析功能**: 添加用户行为分析功能
5. **个性化**: 增强个性化设置和推荐

该补丁为讨论应用的存储服务提供了重要的功能增强，提升了数据管理和用户交互的效率。

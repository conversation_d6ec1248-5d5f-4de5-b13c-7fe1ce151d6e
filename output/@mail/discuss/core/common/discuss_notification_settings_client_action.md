# Discuss Notification Settings Client Action - 讨论通知设置客户端操作

## 概述

`discuss_notification_settings_client_action.js` 实现了 Odoo 讨论应用中通知设置的客户端操作组件，作为通知设置功能的入口点和容器。该组件将DiscussNotificationSettings组件包装为可独立调用的客户端操作，注册到Odoo的操作注册表中，提供了标准的客户端操作接口，使通知设置可以通过菜单、按钮或其他方式独立打开，是讨论应用中通知设置功能的重要桥梁组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/discuss_notification_settings_client_action.js`
- **行数**: 28
- **模块**: `@mail/discuss/core/common/discuss_notification_settings_client_action`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                    // OWL 框架
'@web/core/registry'                                           // 注册表系统
'@mail/discuss/core/common/discuss_notification_settings'     // 通知设置组件
```

## 组件定义

### DiscussNotificationSettingsClientAction 类

```javascript
const DiscussNotificationSettingsClientAction = class DiscussNotificationSettingsClientAction extends Component {
    static components = { DiscussNotificationSettings };
    static props = ["*"];
    static template = xml`
        <div class="o-mail-DiscussNotificationSettingsClientAction mx-3 my-2">
            <DiscussNotificationSettings/>
        </div>
    `;
}
```

**组件特性**:
- 继承自Component基类
- 包含DiscussNotificationSettings子组件
- 接受任意属性
- 使用内联XML模板

## 模板结构

### 内联模板

```xml
<div class="o-mail-DiscussNotificationSettingsClientAction mx-3 my-2">
    <DiscussNotificationSettings/>
</div>
```

**模板特性**:
- **容器样式**: 使用Bootstrap边距类
- **组件包装**: 简单包装通知设置组件
- **布局控制**: 提供适当的间距和布局

## 注册机制

### 操作注册

```javascript
registry
    .category("actions")
    .add("mail.discuss_notification_settings_action", DiscussNotificationSettingsClientAction);
```

**注册特性**:
- **操作类别**: 注册到actions类别
- **唯一标识**: mail.discuss_notification_settings_action
- **组件映射**: 映射到客户端操作组件

## 使用场景

### 1. 客户端操作增强

```javascript
// 客户端操作增强功能
const DiscussNotificationSettingsClientActionEnhancer = {
    enhanceClientAction: () => {
        const EnhancedDiscussNotificationSettingsClientAction = class extends DiscussNotificationSettingsClientAction {
            static components = { 
                DiscussNotificationSettings,
                LoadingSpinner: () => import('@web/core/loading/loading_spinner'),
                ErrorBoundary: () => import('@web/core/error_boundary/error_boundary')
            };
            
            static props = {
                ...DiscussNotificationSettingsClientAction.props,
                context: { type: Object, optional: true },
                options: { type: Object, optional: true }
            };
            
            static template = xml`
                <div class="o-mail-DiscussNotificationSettingsClientAction">
                    <div class="o-mail-settings-header d-flex justify-content-between align-items-center mb-3">
                        <h2 class="mb-0">通知设置</h2>
                        <div class="o-mail-settings-actions">
                            <button class="btn btn-secondary btn-sm me-2" t-on-click="onExportSettings">
                                <i class="fa fa-download me-1"/>导出设置
                            </button>
                            <button class="btn btn-secondary btn-sm me-2" t-on-click="onImportSettings">
                                <i class="fa fa-upload me-1"/>导入设置
                            </button>
                            <button class="btn btn-outline-danger btn-sm" t-on-click="onResetSettings">
                                <i class="fa fa-refresh me-1"/>重置
                            </button>
                        </div>
                    </div>
                    
                    <div class="o-mail-settings-content">
                        <ErrorBoundary>
                            <t t-if="state.isLoading">
                                <LoadingSpinner/>
                            </t>
                            <t t-else="">
                                <DiscussNotificationSettings 
                                    context="props.context"
                                    options="props.options"
                                    onSettingsChange="onSettingsChange"
                                />
                            </t>
                        </ErrorBoundary>
                    </div>
                    
                    <div class="o-mail-settings-footer mt-4 pt-3 border-top">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="o-mail-settings-info">
                                    <h6>设置信息</h6>
                                    <small class="text-muted">
                                        最后更新: <span t-esc="formatDateTime(state.lastUpdated)"/>
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="o-mail-settings-stats">
                                    <small class="text-muted">
                                        设置变更: <span t-esc="state.changeCount"/>次
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            setup() {
                super.setup();
                
                this.state = useState({
                    isLoading: false,
                    lastUpdated: Date.now(),
                    changeCount: 0,
                    hasUnsavedChanges: false
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
                
                // 加载初始数据
                this.loadInitialData();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置快捷键
                this.setupKeyboardShortcuts();
                
                // 设置自动保存
                this.setupAutoSave();
                
                // 设置页面离开确认
                this.setupBeforeUnload();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+S 保存设置
                    if (event.ctrlKey && event.key === 's') {
                        event.preventDefault();
                        this.onSaveSettings();
                    }
                    
                    // Ctrl+R 重置设置
                    if (event.ctrlKey && event.key === 'r') {
                        event.preventDefault();
                        this.onResetSettings();
                    }
                    
                    // Ctrl+E 导出设置
                    if (event.ctrlKey && event.key === 'e') {
                        event.preventDefault();
                        this.onExportSettings();
                    }
                    
                    // Ctrl+I 导入设置
                    if (event.ctrlKey && event.key === 'i') {
                        event.preventDefault();
                        this.onImportSettings();
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 设置自动保存
            setupAutoSave() {
                this.autoSaveInterval = setInterval(() => {
                    if (this.state.hasUnsavedChanges) {
                        this.onSaveSettings(true); // 静默保存
                    }
                }, 30000); // 每30秒自动保存
                
                onWillUnmount(() => {
                    if (this.autoSaveInterval) {
                        clearInterval(this.autoSaveInterval);
                    }
                });
            },
            
            // 设置页面离开确认
            setupBeforeUnload() {
                this.beforeUnloadHandler = (event) => {
                    if (this.state.hasUnsavedChanges) {
                        event.preventDefault();
                        event.returnValue = '您有未保存的设置更改，确定要离开吗？';
                        return event.returnValue;
                    }
                };
                
                window.addEventListener('beforeunload', this.beforeUnloadHandler);
                
                onWillUnmount(() => {
                    window.removeEventListener('beforeunload', this.beforeUnloadHandler);
                });
            },
            
            // 设置性能监控
            setupPerformanceMonitoring() {
                this.performanceMetrics = {
                    loadTime: 0,
                    renderTime: 0,
                    interactionCount: 0,
                    lastInteraction: null
                };
                
                // 记录加载时间
                const startTime = performance.now();
                
                onMounted(() => {
                    this.performanceMetrics.loadTime = performance.now() - startTime;
                    this.recordPerformanceMetric('component_loaded', this.performanceMetrics.loadTime);
                });
            },
            
            // 加载初始数据
            async loadInitialData() {
                this.state.isLoading = true;
                
                try {
                    // 加载设置统计
                    await this.loadSettingsStatistics();
                    
                    // 加载用户偏好
                    await this.loadUserPreferences();
                    
                } catch (error) {
                    console.error('加载初始数据失败:', error);
                    this.env.services.notification.add('加载设置失败', { type: 'error' });
                } finally {
                    this.state.isLoading = false;
                }
            },
            
            // 加载设置统计
            async loadSettingsStatistics() {
                try {
                    const stats = this.getSettingsStatistics();
                    this.state.changeCount = stats.totalChanges;
                    this.state.lastUpdated = stats.lastChange || Date.now();
                } catch (error) {
                    console.warn('加载设置统计失败:', error);
                }
            },
            
            // 加载用户偏好
            async loadUserPreferences() {
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('discuss_notification_client_preferences') || '{}'
                    );
                    
                    // 应用用户偏好
                    if (preferences.autoSave !== undefined) {
                        this.autoSaveEnabled = preferences.autoSave;
                    }
                    
                    if (preferences.confirmBeforeReset !== undefined) {
                        this.confirmBeforeReset = preferences.confirmBeforeReset;
                    }
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 设置变更处理
            onSettingsChange(changes) {
                this.state.hasUnsavedChanges = true;
                this.state.changeCount++;
                this.state.lastUpdated = Date.now();
                
                // 记录交互
                this.performanceMetrics.interactionCount++;
                this.performanceMetrics.lastInteraction = Date.now();
                
                // 记录性能指标
                this.recordPerformanceMetric('settings_changed', {
                    changes,
                    timestamp: Date.now()
                });
            },
            
            // 保存设置
            async onSaveSettings(silent = false) {
                try {
                    this.state.isLoading = true;
                    
                    // 这里可以调用API保存设置
                    await new Promise(resolve => setTimeout(resolve, 500)); // 模拟API调用
                    
                    this.state.hasUnsavedChanges = false;
                    this.state.lastUpdated = Date.now();
                    
                    if (!silent) {
                        this.env.services.notification.add('设置已保存', { type: 'success' });
                    }
                    
                    // 记录保存操作
                    this.recordSettingChange('settings_saved', { silent, timestamp: Date.now() });
                    
                } catch (error) {
                    console.error('保存设置失败:', error);
                    this.env.services.notification.add('保存设置失败', { type: 'error' });
                } finally {
                    this.state.isLoading = false;
                }
            },
            
            // 导出设置
            onExportSettings() {
                try {
                    const settings = this.collectAllSettings();
                    
                    const exportData = {
                        settings,
                        metadata: {
                            exportDate: new Date().toISOString(),
                            version: '1.0',
                            userAgent: navigator.userAgent
                        }
                    };
                    
                    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                        type: 'application/json'
                    });
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `discuss_notification_settings_${new Date().toISOString().split('T')[0]}.json`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    
                    this.env.services.notification.add('设置已导出', { type: 'success' });
                    
                    // 记录导出操作
                    this.recordSettingChange('settings_exported', { timestamp: Date.now() });
                    
                } catch (error) {
                    console.error('导出设置失败:', error);
                    this.env.services.notification.add('导出设置失败', { type: 'error' });
                }
            },
            
            // 导入设置
            onImportSettings() {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = '.json';
                
                input.onchange = (event) => {
                    const file = event.target.files[0];
                    if (file) {
                        this.processImportFile(file);
                    }
                };
                
                input.click();
            },
            
            // 处理导入文件
            async processImportFile(file) {
                try {
                    this.state.isLoading = true;
                    
                    const text = await file.text();
                    const importData = JSON.parse(text);
                    
                    // 验证导入数据
                    if (!this.validateImportData(importData)) {
                        throw new Error('无效的设置文件格式');
                    }
                    
                    // 应用导入的设置
                    await this.applyImportedSettings(importData.settings);
                    
                    this.state.hasUnsavedChanges = true;
                    this.state.lastUpdated = Date.now();
                    
                    this.env.services.notification.add('设置已导入', { type: 'success' });
                    
                    // 记录导入操作
                    this.recordSettingChange('settings_imported', {
                        filename: file.name,
                        timestamp: Date.now()
                    });
                    
                } catch (error) {
                    console.error('导入设置失败:', error);
                    this.env.services.notification.add('导入设置失败: ' + error.message, { type: 'error' });
                } finally {
                    this.state.isLoading = false;
                }
            },
            
            // 验证导入数据
            validateImportData(data) {
                return (
                    data &&
                    typeof data === 'object' &&
                    data.settings &&
                    typeof data.settings === 'object'
                );
            },
            
            // 应用导入的设置
            async applyImportedSettings(settings) {
                // 这里需要与DiscussNotificationSettings组件交互
                // 应用导入的设置
                if (this.notificationSettingsRef) {
                    await this.notificationSettingsRef.applyImportedSettings(settings);
                }
            },
            
            // 重置设置
            async onResetSettings() {
                const confirmed = this.confirmBeforeReset !== false ? 
                    confirm('确定要重置所有通知设置吗？此操作无法撤销。') : true;
                
                if (confirmed) {
                    try {
                        this.state.isLoading = true;
                        
                        // 重置设置
                        if (this.notificationSettingsRef) {
                            await this.notificationSettingsRef.resetAllSettings();
                        }
                        
                        this.state.hasUnsavedChanges = false;
                        this.state.changeCount = 0;
                        this.state.lastUpdated = Date.now();
                        
                        this.env.services.notification.add('设置已重置', { type: 'success' });
                        
                        // 记录重置操作
                        this.recordSettingChange('settings_reset', { timestamp: Date.now() });
                        
                    } catch (error) {
                        console.error('重置设置失败:', error);
                        this.env.services.notification.add('重置设置失败', { type: 'error' });
                    } finally {
                        this.state.isLoading = false;
                    }
                }
            },
            
            // 收集所有设置
            collectAllSettings() {
                // 这里需要从DiscussNotificationSettings组件收集设置
                return {
                    notificationTypes: {},
                    soundSettings: {},
                    scheduleSettings: {},
                    deviceSettings: {},
                    customDuration: 60
                };
            },
            
            // 格式化日期时间
            formatDateTime(timestamp) {
                return new Date(timestamp).toLocaleString();
            },
            
            // 获取设置统计
            getSettingsStatistics() {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('notification_setting_changes') || '[]'
                    );
                    
                    return {
                        totalChanges: changes.length,
                        lastChange: changes.length > 0 ? changes[changes.length - 1].timestamp : null,
                        changesByAction: changes.reduce((acc, change) => {
                            acc[change.action] = (acc[change.action] || 0) + 1;
                            return acc;
                        }, {})
                    };
                } catch (error) {
                    return {
                        totalChanges: 0,
                        lastChange: null,
                        changesByAction: {}
                    };
                }
            },
            
            // 记录设置变更
            recordSettingChange(action, data) {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('notification_setting_changes') || '[]'
                    );
                    
                    changes.push({
                        action,
                        data,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个变更
                    if (changes.length > 100) {
                        changes.splice(0, changes.length - 100);
                    }
                    
                    localStorage.setItem('notification_setting_changes', JSON.stringify(changes));
                } catch (error) {
                    console.warn('记录设置变更失败:', error);
                }
            },
            
            // 记录性能指标
            recordPerformanceMetric(metric, data) {
                try {
                    const metrics = JSON.parse(
                        localStorage.getItem('notification_settings_performance') || '[]'
                    );
                    
                    metrics.push({
                        metric,
                        data,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个指标
                    if (metrics.length > 50) {
                        metrics.splice(0, metrics.length - 50);
                    }
                    
                    localStorage.setItem('notification_settings_performance', JSON.stringify(metrics));
                } catch (error) {
                    console.warn('记录性能指标失败:', error);
                }
            },
            
            // 获取性能统计
            getPerformanceStatistics() {
                return {
                    ...this.performanceMetrics,
                    averageLoadTime: this.calculateAverageLoadTime(),
                    interactionRate: this.calculateInteractionRate()
                };
            },
            
            // 计算平均加载时间
            calculateAverageLoadTime() {
                try {
                    const metrics = JSON.parse(
                        localStorage.getItem('notification_settings_performance') || '[]'
                    );
                    
                    const loadMetrics = metrics.filter(m => m.metric === 'component_loaded');
                    
                    if (loadMetrics.length === 0) return 0;
                    
                    const total = loadMetrics.reduce((sum, m) => sum + m.data, 0);
                    return total / loadMetrics.length;
                } catch (error) {
                    return 0;
                }
            },
            
            // 计算交互率
            calculateInteractionRate() {
                const sessionDuration = Date.now() - (this.performanceMetrics.lastInteraction || Date.now());
                
                if (sessionDuration === 0) return 0;
                
                return (this.performanceMetrics.interactionCount / sessionDuration) * 60000; // 每分钟交互次数
            }
        };
        
        // 替换原始组件
        __exports.DiscussNotificationSettingsClientAction = EnhancedDiscussNotificationSettingsClientAction;
        
        // 重新注册增强的组件
        registry
            .category("actions")
            .add("mail.discuss_notification_settings_action", EnhancedDiscussNotificationSettingsClientAction, { force: true });
    }
};

// 应用客户端操作增强
DiscussNotificationSettingsClientActionEnhancer.enhanceClientAction();
```

## 技术特点

### 1. 客户端操作
- 标准的Odoo客户端操作接口
- 独立的操作入口点
- 注册表集成

### 2. 组件包装
- 简洁的组件包装设计
- 布局和样式控制
- 子组件集成

### 3. 模板设计
- 内联XML模板
- 响应式布局
- 清晰的结构层次

### 4. 注册机制
- 操作注册表集成
- 唯一标识符
- 动态组件映射

## 设计模式

### 1. 包装器模式 (Wrapper Pattern)
- 组件功能的包装
- 接口的适配和转换

### 2. 注册表模式 (Registry Pattern)
- 操作的注册和管理
- 动态组件查找

### 3. 组合模式 (Composite Pattern)
- 组件的组合和嵌套
- 统一的组件接口

## 注意事项

1. **组件生命周期**: 确保子组件的正确生命周期管理
2. **属性传递**: 正确处理属性的传递和转换
3. **样式隔离**: 避免样式冲突和污染
4. **性能优化**: 避免不必要的重渲染

## 扩展建议

1. **更多操作**: 添加更多的操作按钮和功能
2. **布局优化**: 实现更灵活的布局配置
3. **主题支持**: 支持多种主题和样式
4. **快捷键**: 添加键盘快捷键支持
5. **状态管理**: 增强状态管理和持久化

该客户端操作为讨论应用的通知设置提供了标准的入口点，是系统集成的重要桥梁组件。

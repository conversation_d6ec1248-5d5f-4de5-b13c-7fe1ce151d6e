# Thread Actions - 线程操作

## 概述

`thread_actions.js` 实现了 Odoo 讨论应用中的线程操作注册，定义了讨论频道相关的各种操作。该文件通过线程操作注册表添加了通知设置、附件面板、邀请用户、成员列表等四个核心操作，每个操作都包含条件检查、组件配置、弹出框管理、图标设置等完整功能，提供了讨论应用中线程管理的核心操作集合，是用户界面操作系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/thread_actions.js`
- **行数**: 142
- **模块**: `@mail/discuss/core/common/thread_actions`

## 依赖关系

```javascript
// 操作注册表
'@mail/core/common/thread_actions'         // 线程操作注册表

// 组件依赖
'@mail/discuss/core/common/attachment_panel'    // 附件面板
'@mail/discuss/core/common/channel_invitation'  // 频道邀请
'@mail/discuss/core/common/channel_member_list' // 频道成员列表
'@mail/discuss/core/common/notification_settings' // 通知设置

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/l10n/translation'               // 国际化
'@web/core/popover/popover_hook'           // 弹出框钩子
```

## 操作注册

### 1. 通知设置操作

```javascript
threadActionsRegistry.add("notification-settings", {
    condition(component) {
        return (
            component.thread?.model === "discuss.channel" &&
            component.store.self.type !== "guest" &&
            (!component.props.chatWindow || component.props.chatWindow.isOpen)
        );
    },
    setup(action) {
        const component = useComponent();
        if (!component.props.chatWindow) {
            action.popover = usePopover(NotificationSettings, {
                onClose: () => action.close(),
                position: "bottom-end",
                fixedPosition: true,
                popoverClass: action.panelOuterClass,
            });
        }
    },
    open(component, action) {
        action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), {
            hasSizeConstraints: true,
            thread: component.thread,
        });
    },
    close(component, action) {
        action.popover?.close();
    },
    component: NotificationSettings,
    icon(component) {
        return component.thread.isMuted
            ? "fa fa-fw text-danger fa-bell-slash"
            : "fa fa-fw fa-bell";
    },
    iconLarge(component) {
        return component.thread.isMuted
            ? "fa fa-fw fa-lg text-danger fa-bell-slash"
            : "fa fa-fw fa-lg fa-bell";
    },
    name: _t("Notification Settings"),
    sequence: 10,
    sequenceGroup: 30,
    toggle: true,
});
```

**操作特性**:
- **条件检查**: 仅讨论频道且非访客用户
- **弹出框管理**: 使用弹出框钩子管理界面
- **动态图标**: 根据静音状态显示不同图标
- **切换操作**: 支持开关式操作

### 2. 附件面板操作

```javascript
threadActionsRegistry.add("attachments", {
    condition: (component) =>
        component.thread?.hasAttachmentPanel &&
        (!component.props.chatWindow || component.props.chatWindow.isOpen),
    component: AttachmentPanel,
    icon: "fa fa-fw fa-paperclip",
    iconLarge: "fa fa-fw fa-lg fa-paperclip",
    name: _t("Attachments"),
    sequence: 10,
    sequenceGroup: 10,
    toggle: true,
});
```

**操作特性**:
- **条件检查**: 线程支持附件面板
- **组件集成**: 直接使用AttachmentPanel组件
- **固定图标**: 使用回形针图标
- **分组排序**: 属于第10序列组

### 3. 邀请用户操作

```javascript
threadActionsRegistry.add("invite-people", {
    close(component, action) {
        action.popover?.close();
    },
    component: ChannelInvitation,
    componentProps(action) {
        return { close: () => action.close() };
    },
    condition(component) {
        return (
            component.thread?.model === "discuss.channel" &&
            (!component.props.chatWindow || component.props.chatWindow.isOpen)
        );
    },
    panelOuterClass(component) {
        return `o-discuss-ChannelInvitation ${component.props.chatWindow ? "bg-inherit" : ""}`;
    },
    icon: "fa fa-fw fa-user-plus",
    iconLarge: "fa fa-fw fa-lg fa-user-plus",
    name: _t("Invite People"),
    open(component, action) {
        action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), {
            hasSizeConstraints: true,
            thread: component.thread,
        });
    },
    sequence: 10,
    sequenceGroup: 20,
    setup(action) {
        const component = useComponent();
        if (!component.props.chatWindow) {
            action.popover = usePopover(ChannelInvitation, {
                onClose: () => action.close(),
                popoverClass: action.panelOuterClass,
            });
        }
    },
    toggle: true,
});
```

**操作特性**:
- **组件属性**: 传递关闭回调给组件
- **样式类**: 动态计算面板外部样式类
- **弹出框配置**: 完整的弹出框生命周期管理
- **用户图标**: 使用用户添加图标

### 4. 成员列表操作

```javascript
threadActionsRegistry.add("member-list", {
    component: ChannelMemberList,
    condition(component) {
        return (
            component.thread?.hasMemberList &&
            (!component.props.chatWindow || component.props.chatWindow.isOpen)
        );
    },
    componentProps(action, component) {
        return {
            openChannelInvitePanel({ keepPrevious } = {}) {
                component.threadActions.actions
                    .find(({ id }) => id === "invite-people")
                    ?.open({ keepPrevious });
            },
        };
    },
    panelOuterClass: "o-discuss-ChannelMemberList bg-inherit",
    icon: "fa fa-fw fa-users",
    iconLarge: "fa fa-fw fa-lg fa-users",
    name: _t("Members"),
    sequence: 30,
    sequenceGroup: 10,
    toggle: true,
});
```

**操作特性**:
- **交互集成**: 提供打开邀请面板的方法
- **条件检查**: 线程支持成员列表
- **固定样式**: 使用固定的面板样式类
- **用户组图标**: 使用多用户图标

## 使用场景

### 1. 线程操作增强

```javascript
// 线程操作增强功能
const ThreadActionsEnhancer = {
    enhanceThreadActions: () => {
        // 添加频道设置操作
        threadActionsRegistry.add("channel-settings", {
            condition(component) {
                return (
                    component.thread?.model === "discuss.channel" &&
                    component.thread.canManage &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            setup(action) {
                const component = useComponent();
                action.popover = usePopover(ChannelSettings, {
                    onClose: () => action.close(),
                    position: "bottom-end",
                    fixedPosition: true,
                });
            },
            open(component, action) {
                action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), {
                    thread: component.thread,
                });
            },
            close(component, action) {
                action.popover?.close();
            },
            icon: "fa fa-fw fa-cog",
            iconLarge: "fa fa-fw fa-lg fa-cog",
            name: _t("Channel Settings"),
            sequence: 20,
            sequenceGroup: 30,
            toggle: true,
        });
        
        // 添加频道统计操作
        threadActionsRegistry.add("channel-stats", {
            condition(component) {
                return (
                    component.thread?.model === "discuss.channel" &&
                    component.thread.hasStats &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            setup(action) {
                const component = useComponent();
                action.popover = usePopover(ChannelStats, {
                    onClose: () => action.close(),
                    position: "bottom-end",
                });
            },
            open(component, action) {
                action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), {
                    thread: component.thread,
                });
            },
            close(component, action) {
                action.popover?.close();
            },
            icon: "fa fa-fw fa-chart-bar",
            iconLarge: "fa fa-fw fa-lg fa-chart-bar",
            name: _t("Statistics"),
            sequence: 40,
            sequenceGroup: 10,
            toggle: true,
        });
        
        // 添加搜索消息操作
        threadActionsRegistry.add("search-messages", {
            condition(component) {
                return (
                    component.thread?.hasMessages &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            setup(action) {
                const component = useComponent();
                action.popover = usePopover(MessageSearch, {
                    onClose: () => action.close(),
                    position: "bottom-end",
                });
            },
            open(component, action) {
                action.popover?.open(component.root.el.querySelector(`[name="${action.id}"]`), {
                    thread: component.thread,
                });
            },
            close(component, action) {
                action.popover?.close();
            },
            icon: "fa fa-fw fa-search",
            iconLarge: "fa fa-fw fa-lg fa-search",
            name: _t("Search Messages"),
            sequence: 20,
            sequenceGroup: 10,
            toggle: true,
        });
        
        // 添加导出聊天记录操作
        threadActionsRegistry.add("export-chat", {
            condition(component) {
                return (
                    component.thread?.canExport &&
                    component.store.self.isInternalUser &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            async onClick(component) {
                try {
                    const response = await component.env.services.orm.call(
                        'discuss.channel',
                        'export_chat_history',
                        [component.thread.id]
                    );
                    
                    if (response.download_url) {
                        window.open(response.download_url, '_blank');
                    }
                    
                    component.env.services.notification.add(
                        _t("Chat history exported successfully"),
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('导出聊天记录失败:', error);
                    component.env.services.notification.add(
                        _t("Failed to export chat history"),
                        { type: 'error' }
                    );
                }
            },
            icon: "fa fa-fw fa-download",
            iconLarge: "fa fa-fw fa-lg fa-download",
            name: _t("Export Chat"),
            sequence: 50,
            sequenceGroup: 30,
        });
        
        // 添加频道归档操作
        threadActionsRegistry.add("archive-channel", {
            condition(component) {
                return (
                    component.thread?.model === "discuss.channel" &&
                    component.thread.canArchive &&
                    !component.thread.isArchived &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            async onClick(component) {
                const confirmed = confirm(_t("Are you sure you want to archive this channel?"));
                
                if (!confirmed) {
                    return;
                }
                
                try {
                    await component.env.services.orm.call(
                        'discuss.channel',
                        'action_archive',
                        [component.thread.id]
                    );
                    
                    component.env.services.notification.add(
                        _t("Channel archived successfully"),
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('归档频道失败:', error);
                    component.env.services.notification.add(
                        _t("Failed to archive channel"),
                        { type: 'error' }
                    );
                }
            },
            icon: "fa fa-fw fa-archive",
            iconLarge: "fa fa-fw fa-lg fa-archive",
            name: _t("Archive Channel"),
            sequence: 60,
            sequenceGroup: 30,
        });
        
        // 添加离开频道操作
        threadActionsRegistry.add("leave-channel", {
            condition(component) {
                return (
                    component.thread?.model === "discuss.channel" &&
                    component.thread.canLeave &&
                    (!component.props.chatWindow || component.props.chatWindow.isOpen)
                );
            },
            async onClick(component) {
                const confirmed = confirm(_t("Are you sure you want to leave this channel?"));
                
                if (!confirmed) {
                    return;
                }
                
                try {
                    await component.env.services.orm.call(
                        'discuss.channel',
                        'action_unfollow',
                        [component.thread.id]
                    );
                    
                    component.env.services.notification.add(
                        _t("Left channel successfully"),
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('离开频道失败:', error);
                    component.env.services.notification.add(
                        _t("Failed to leave channel"),
                        { type: 'error' }
                    );
                }
            },
            icon: "fa fa-fw fa-sign-out-alt text-danger",
            iconLarge: "fa fa-fw fa-lg fa-sign-out-alt text-danger",
            name: _t("Leave Channel"),
            sequence: 70,
            sequenceGroup: 30,
        });
        
        // 增强现有操作
        ThreadActionsEnhancer.enhanceExistingActions();
    },
    
    // 增强现有操作
    enhanceExistingActions() {
        // 增强通知设置操作
        const notificationAction = threadActionsRegistry.get("notification-settings");
        const originalSetup = notificationAction.setup;
        
        notificationAction.setup = function(action) {
            originalSetup?.call(this, action);
            
            // 添加快捷键支持
            const component = useComponent();
            
            onMounted(() => {
                const handleKeydown = (event) => {
                    if (event.ctrlKey && event.key === 'n') {
                        event.preventDefault();
                        if (notificationAction.condition(component)) {
                            action.toggle();
                        }
                    }
                };
                
                document.addEventListener('keydown', handleKeydown);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', handleKeydown);
                });
            });
        };
        
        // 增强邀请操作
        const inviteAction = threadActionsRegistry.get("invite-people");
        const originalInviteOpen = inviteAction.open;
        
        inviteAction.open = function(component, action) {
            // 记录邀请面板打开
            ThreadActionsEnhancer.recordActionUsage('invite-people', component.thread.id);
            
            return originalInviteOpen.call(this, component, action);
        };
        
        // 增强成员列表操作
        const memberListAction = threadActionsRegistry.get("member-list");
        const originalMemberListProps = memberListAction.componentProps;
        
        memberListAction.componentProps = function(action, component) {
            const originalProps = originalMemberListProps?.call(this, action, component) || {};
            
            return {
                ...originalProps,
                onMemberAction: (actionType, member) => {
                    ThreadActionsEnhancer.handleMemberAction(actionType, member, component);
                },
                onBulkAction: (actionType, members) => {
                    ThreadActionsEnhancer.handleBulkMemberAction(actionType, members, component);
                }
            };
        };
    },
    
    // 记录操作使用情况
    recordActionUsage(actionId, threadId) {
        try {
            const usage = JSON.parse(localStorage.getItem('thread_action_usage') || '{}');
            const key = `${actionId}_${threadId}`;
            
            if (!usage[key]) {
                usage[key] = { count: 0, lastUsed: null };
            }
            
            usage[key].count++;
            usage[key].lastUsed = Date.now();
            
            localStorage.setItem('thread_action_usage', JSON.stringify(usage));
        } catch (error) {
            console.warn('记录操作使用情况失败:', error);
        }
    },
    
    // 处理成员操作
    handleMemberAction(actionType, member, component) {
        switch (actionType) {
            case 'promote':
                ThreadActionsEnhancer.promoteMember(member, component);
                break;
            case 'demote':
                ThreadActionsEnhancer.demoteMember(member, component);
                break;
            case 'remove':
                ThreadActionsEnhancer.removeMember(member, component);
                break;
            case 'mute':
                ThreadActionsEnhancer.muteMember(member, component);
                break;
        }
    },
    
    // 处理批量成员操作
    handleBulkMemberAction(actionType, members, component) {
        switch (actionType) {
            case 'remove':
                ThreadActionsEnhancer.removeMembersInBulk(members, component);
                break;
            case 'change_role':
                ThreadActionsEnhancer.changeMemberRolesInBulk(members, component);
                break;
        }
    },
    
    // 提升成员
    async promoteMember(member, component) {
        try {
            await component.env.services.orm.call(
                'discuss.channel',
                'promote_member',
                [component.thread.id, member.id]
            );
            
            component.env.services.notification.add(
                _t("Member promoted successfully"),
                { type: 'success' }
            );
        } catch (error) {
            console.error('提升成员失败:', error);
            component.env.services.notification.add(
                _t("Failed to promote member"),
                { type: 'error' }
            );
        }
    },
    
    // 降级成员
    async demoteMember(member, component) {
        try {
            await component.env.services.orm.call(
                'discuss.channel',
                'demote_member',
                [component.thread.id, member.id]
            );
            
            component.env.services.notification.add(
                _t("Member demoted successfully"),
                { type: 'success' }
            );
        } catch (error) {
            console.error('降级成员失败:', error);
            component.env.services.notification.add(
                _t("Failed to demote member"),
                { type: 'error' }
            );
        }
    },
    
    // 移除成员
    async removeMember(member, component) {
        const confirmed = confirm(_t("Are you sure you want to remove this member?"));
        
        if (!confirmed) {
            return;
        }
        
        try {
            await component.env.services.orm.call(
                'discuss.channel',
                'remove_members',
                [[component.thread.id]],
                { member_ids: [member.id] }
            );
            
            component.env.services.notification.add(
                _t("Member removed successfully"),
                { type: 'success' }
            );
        } catch (error) {
            console.error('移除成员失败:', error);
            component.env.services.notification.add(
                _t("Failed to remove member"),
                { type: 'error' }
            );
        }
    },
    
    // 静音成员
    async muteMember(member, component) {
        try {
            await component.env.services.orm.call(
                'discuss.channel',
                'mute_member',
                [component.thread.id, member.id]
            );
            
            component.env.services.notification.add(
                _t("Member muted successfully"),
                { type: 'success' }
            );
        } catch (error) {
            console.error('静音成员失败:', error);
            component.env.services.notification.add(
                _t("Failed to mute member"),
                { type: 'error' }
            );
        }
    },
    
    // 批量移除成员
    async removeMembersInBulk(members, component) {
        const confirmed = confirm(_t("Are you sure you want to remove %s members?", members.length));
        
        if (!confirmed) {
            return;
        }
        
        try {
            const memberIds = members.map(m => m.id);
            await component.env.services.orm.call(
                'discuss.channel',
                'remove_members',
                [[component.thread.id]],
                { member_ids: memberIds }
            );
            
            component.env.services.notification.add(
                _t("%s members removed successfully", members.length),
                { type: 'success' }
            );
        } catch (error) {
            console.error('批量移除成员失败:', error);
            component.env.services.notification.add(
                _t("Failed to remove members"),
                { type: 'error' }
            );
        }
    },
    
    // 批量更改成员角色
    async changeMemberRolesInBulk(members, component) {
        // 这里可以实现角色选择对话框
        const newRole = prompt(_t("Enter new role (admin/moderator/member):"));
        
        if (!newRole || !['admin', 'moderator', 'member'].includes(newRole)) {
            return;
        }
        
        try {
            const memberIds = members.map(m => m.id);
            await component.env.services.orm.call(
                'discuss.channel',
                'change_member_roles',
                [[component.thread.id]],
                { member_ids: memberIds, role: newRole }
            );
            
            component.env.services.notification.add(
                _t("%s member roles changed successfully", members.length),
                { type: 'success' }
            );
        } catch (error) {
            console.error('批量更改角色失败:', error);
            component.env.services.notification.add(
                _t("Failed to change member roles"),
                { type: 'error' }
            );
        }
    },
    
    // 获取操作使用统计
    getActionUsageStats() {
        try {
            const usage = JSON.parse(localStorage.getItem('thread_action_usage') || '{}');
            
            const stats = {};
            Object.entries(usage).forEach(([key, data]) => {
                const [actionId, threadId] = key.split('_');
                
                if (!stats[actionId]) {
                    stats[actionId] = { totalCount: 0, threads: {} };
                }
                
                stats[actionId].totalCount += data.count;
                stats[actionId].threads[threadId] = data;
            });
            
            return stats;
        } catch (error) {
            console.warn('获取操作使用统计失败:', error);
            return {};
        }
    }
};

// 应用线程操作增强
ThreadActionsEnhancer.enhanceThreadActions();
```

## 技术特点

### 1. 注册表机制
- 统一的操作注册管理
- 动态操作添加和配置
- 序列化排序支持

### 2. 条件检查
- 灵活的显示条件控制
- 多重条件组合判断
- 运行时条件评估

### 3. 弹出框管理
- 完整的弹出框生命周期
- 位置和样式配置
- 自动关闭机制

### 4. 组件集成
- 无缝的组件集成
- 属性传递机制
- 回调函数支持

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 统一的操作注册和管理
- 动态操作配置

### 2. 策略模式 (Strategy Pattern)
- 不同操作的处理策略
- 可配置的操作行为

### 3. 观察者模式 (Observer Pattern)
- 操作状态变化的监听
- 事件驱动的操作执行

## 注意事项

1. **性能优化**: 避免频繁的条件检查
2. **内存管理**: 及时清理弹出框和事件监听器
3. **用户体验**: 提供清晰的操作反馈
4. **权限控制**: 确保操作权限的正确性

## 扩展建议

1. **更多操作**: 添加更多线程相关操作
2. **权限控制**: 增强操作权限验证
3. **快捷键**: 添加键盘快捷键支持
4. **操作历史**: 记录操作历史和统计
5. **自定义操作**: 支持用户自定义操作

该文件为讨论应用提供了完整的线程操作集合，是用户界面操作系统的核心组成部分。

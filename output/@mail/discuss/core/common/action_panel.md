# Action Panel - 操作面板

## 概述

`action_panel.js` 实现了 Odoo 讨论应用中的操作面板组件，提供了一个可调整大小的侧边面板容器。该组件支持标题显示、图标配置、可调整大小、插槽内容、宽度控制等功能，集成了可调整面板组件和邮件存储服务，提供了灵活的面板布局解决方案，是讨论应用中侧边栏和面板系统的重要基础组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/action_panel.js`
- **行数**: 37
- **模块**: `@mail/discuss/core/common/action_panel`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/resizable_panel/resizable_panel'   // 可调整面板组件
'@web/core/utils/hooks'                        // Web 核心钩子
```

## 组件定义

### ActionPanel 类

```javascript
const ActionPanel = class ActionPanel extends Component {
    static template = "mail.ActionPanel";
    static components = { ResizablePanel };
    static props = ["icon?", "title?", "resizable?", "slots?", "initialWidth?", "minWidth?"];
    static defaultProps = { resizable: true };
}
```

**组件特性**:
- 继承自Component基类
- 使用专用模板
- 集成可调整面板组件
- 支持多种可选属性
- 默认启用可调整大小

## 属性定义

### Props 接口

```javascript
/**
 * @typedef {Object} Props
 * @prop {string} title
 * @prop {Object} [slots]
 */
static props = ["icon?", "title?", "resizable?", "slots?", "initialWidth?", "minWidth?"];
```

**属性说明**:
- **icon**: 可选图标
- **title**: 可选标题
- **resizable**: 是否可调整大小
- **slots**: 插槽内容
- **initialWidth**: 初始宽度
- **minWidth**: 最小宽度

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**初始化内容**:
- 调用父类setup方法
- 注入邮件存储服务
- 使用响应式状态管理

## 样式计算

### classNames 获取器

```javascript
get classNames() {
    return `o-mail-ActionPanel overflow-auto d-flex flex-column flex-shrink-0 position-relative py-2 pt-0 h-100 bg-inherit ${
        !this.env.inChatter ? " px-2" : " o-mail-ActionPanel-chatter"
    } ${this.env.inDiscussApp ? " o-mail-discussSidebarBgColor" : ""}`;
}
```

**样式逻辑**:
- **基础样式**: 固定的面板样式类
- **聊天器模式**: 根据inChatter环境变量调整样式
- **讨论应用**: 根据inDiscussApp环境变量添加侧边栏背景色

## 使用场景

### 1. 面板功能增强

```javascript
// 面板功能增强
const ActionPanelEnhancer = {
    enhanceActionPanel: () => {
        const EnhancedActionPanel = class extends ActionPanel {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.panelState = useState({
                    isCollapsed: false,
                    isFloating: false,
                    isDocked: true,
                    currentWidth: this.props.initialWidth || 300,
                    isAnimating: false,
                    lastPosition: null
                });
                
                // 面板配置
                this.panelConfig = {
                    enableCollapse: true,
                    enableFloat: true,
                    enableDock: true,
                    animationDuration: 300,
                    snapThreshold: 50,
                    autoHide: false
                };
                
                // 设置面板增强功能
                this.setupPanelEnhancements();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置面板持久化
                this.setupPanelPersistence();
            },
            
            // 设置面板增强功能
            setupPanelEnhancements() {
                // 监听窗口大小变化
                onMounted(() => {
                    this.setupResizeObserver();
                    this.setupDragAndDrop();
                    this.loadPanelState();
                });
                
                onWillUnmount(() => {
                    this.savePanelState();
                    this.cleanupEventListeners();
                });
            },
            
            // 设置大小观察器
            setupResizeObserver() {
                if (!window.ResizeObserver) return;
                
                this.resizeObserver = new ResizeObserver((entries) => {
                    for (const entry of entries) {
                        this.handlePanelResize(entry);
                    }
                });
                
                if (this.el) {
                    this.resizeObserver.observe(this.el);
                }
            },
            
            // 处理面板大小变化
            handlePanelResize(entry) {
                const { width, height } = entry.contentRect;
                
                // 更新面板状态
                this.panelState.currentWidth = width;
                
                // 检查是否需要自动折叠
                if (this.panelConfig.autoHide && width < 200) {
                    this.collapsePanel();
                }
                
                // 触发大小变化事件
                this.trigger('panel_resized', { width, height });
            },
            
            // 设置拖拽功能
            setupDragAndDrop() {
                if (!this.panelConfig.enableFloat) return;
                
                const header = this.el.querySelector('.panel-header');
                if (!header) return;
                
                header.style.cursor = 'move';
                header.addEventListener('mousedown', this.handleDragStart.bind(this));
            },
            
            // 处理拖拽开始
            handleDragStart(event) {
                if (!this.panelConfig.enableFloat) return;
                
                event.preventDefault();
                
                this.dragState = {
                    isDragging: true,
                    startX: event.clientX,
                    startY: event.clientY,
                    startLeft: this.el.offsetLeft,
                    startTop: this.el.offsetTop
                };
                
                document.addEventListener('mousemove', this.handleDragMove.bind(this));
                document.addEventListener('mouseup', this.handleDragEnd.bind(this));
                
                this.el.classList.add('dragging');
            },
            
            // 处理拖拽移动
            handleDragMove(event) {
                if (!this.dragState?.isDragging) return;
                
                const deltaX = event.clientX - this.dragState.startX;
                const deltaY = event.clientY - this.dragState.startY;
                
                const newLeft = this.dragState.startLeft + deltaX;
                const newTop = this.dragState.startTop + deltaY;
                
                // 边界检查
                const maxLeft = window.innerWidth - this.el.offsetWidth;
                const maxTop = window.innerHeight - this.el.offsetHeight;
                
                this.el.style.left = Math.max(0, Math.min(newLeft, maxLeft)) + 'px';
                this.el.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
                
                // 检查停靠区域
                this.checkDockingZones(event.clientX, event.clientY);
            },
            
            // 处理拖拽结束
            handleDragEnd(event) {
                if (!this.dragState?.isDragging) return;
                
                document.removeEventListener('mousemove', this.handleDragMove.bind(this));
                document.removeEventListener('mouseup', this.handleDragEnd.bind(this));
                
                this.el.classList.remove('dragging');
                
                // 检查是否需要停靠
                this.handleDocking(event.clientX, event.clientY);
                
                this.dragState = null;
            },
            
            // 检查停靠区域
            checkDockingZones(x, y) {
                const threshold = this.panelConfig.snapThreshold;
                
                // 左侧停靠
                if (x < threshold) {
                    this.showDockingIndicator('left');
                }
                // 右侧停靠
                else if (x > window.innerWidth - threshold) {
                    this.showDockingIndicator('right');
                }
                // 顶部停靠
                else if (y < threshold) {
                    this.showDockingIndicator('top');
                }
                // 底部停靠
                else if (y > window.innerHeight - threshold) {
                    this.showDockingIndicator('bottom');
                }
                else {
                    this.hideDockingIndicator();
                }
            },
            
            // 显示停靠指示器
            showDockingIndicator(position) {
                let indicator = document.querySelector('.docking-indicator');
                
                if (!indicator) {
                    indicator = document.createElement('div');
                    indicator.className = 'docking-indicator';
                    document.body.appendChild(indicator);
                }
                
                indicator.className = `docking-indicator docking-${position}`;
                indicator.style.display = 'block';
            },
            
            // 隐藏停靠指示器
            hideDockingIndicator() {
                const indicator = document.querySelector('.docking-indicator');
                if (indicator) {
                    indicator.style.display = 'none';
                }
            },
            
            // 处理停靠
            handleDocking(x, y) {
                const threshold = this.panelConfig.snapThreshold;
                
                if (x < threshold) {
                    this.dockPanel('left');
                } else if (x > window.innerWidth - threshold) {
                    this.dockPanel('right');
                } else if (y < threshold) {
                    this.dockPanel('top');
                } else if (y > window.innerHeight - threshold) {
                    this.dockPanel('bottom');
                } else {
                    this.floatPanel();
                }
                
                this.hideDockingIndicator();
            },
            
            // 停靠面板
            dockPanel(position) {
                this.panelState.isDocked = true;
                this.panelState.isFloating = false;
                this.panelState.lastPosition = position;
                
                this.el.classList.remove('floating');
                this.el.classList.add('docked', `docked-${position}`);
                
                // 重置位置样式
                this.el.style.left = '';
                this.el.style.top = '';
                this.el.style.position = '';
                
                this.trigger('panel_docked', { position });
            },
            
            // 浮动面板
            floatPanel() {
                this.panelState.isDocked = false;
                this.panelState.isFloating = true;
                
                this.el.classList.remove('docked');
                this.el.classList.add('floating');
                this.el.style.position = 'fixed';
                
                this.trigger('panel_floated');
            },
            
            // 折叠面板
            collapsePanel() {
                if (this.panelState.isAnimating) return;
                
                this.panelState.isAnimating = true;
                this.panelState.isCollapsed = true;
                
                this.el.classList.add('collapsing');
                
                setTimeout(() => {
                    this.el.classList.remove('collapsing');
                    this.el.classList.add('collapsed');
                    this.panelState.isAnimating = false;
                    
                    this.trigger('panel_collapsed');
                }, this.panelConfig.animationDuration);
            },
            
            // 展开面板
            expandPanel() {
                if (this.panelState.isAnimating) return;
                
                this.panelState.isAnimating = true;
                this.panelState.isCollapsed = false;
                
                this.el.classList.remove('collapsed');
                this.el.classList.add('expanding');
                
                setTimeout(() => {
                    this.el.classList.remove('expanding');
                    this.panelState.isAnimating = false;
                    
                    this.trigger('panel_expanded');
                }, this.panelConfig.animationDuration);
            },
            
            // 切换折叠状态
            toggleCollapse() {
                if (this.panelState.isCollapsed) {
                    this.expandPanel();
                } else {
                    this.collapsePanel();
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
            },
            
            // 处理键盘快捷键
            handleKeyboardShortcut(event) {
                // Ctrl+Shift+P: 切换面板折叠
                if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                    event.preventDefault();
                    this.toggleCollapse();
                }
                
                // Ctrl+Shift+F: 切换浮动模式
                if (event.ctrlKey && event.shiftKey && event.key === 'F') {
                    event.preventDefault();
                    this.toggleFloat();
                }
                
                // Escape: 如果是浮动状态，返回停靠
                if (event.key === 'Escape' && this.panelState.isFloating) {
                    event.preventDefault();
                    this.dockPanel(this.panelState.lastPosition || 'right');
                }
            },
            
            // 切换浮动状态
            toggleFloat() {
                if (this.panelState.isFloating) {
                    this.dockPanel(this.panelState.lastPosition || 'right');
                } else {
                    this.floatPanel();
                }
            },
            
            // 设置面板持久化
            setupPanelPersistence() {
                this.persistenceKey = `action_panel_${this.props.title || 'default'}`;
            },
            
            // 保存面板状态
            savePanelState() {
                try {
                    const state = {
                        isCollapsed: this.panelState.isCollapsed,
                        isFloating: this.panelState.isFloating,
                        isDocked: this.panelState.isDocked,
                        currentWidth: this.panelState.currentWidth,
                        lastPosition: this.panelState.lastPosition,
                        position: {
                            left: this.el.style.left,
                            top: this.el.style.top
                        }
                    };
                    
                    localStorage.setItem(this.persistenceKey, JSON.stringify(state));
                } catch (error) {
                    console.warn('保存面板状态失败:', error);
                }
            },
            
            // 加载面板状态
            loadPanelState() {
                try {
                    const saved = localStorage.getItem(this.persistenceKey);
                    if (saved) {
                        const state = JSON.parse(saved);
                        
                        // 恢复面板状态
                        Object.assign(this.panelState, state);
                        
                        // 应用保存的状态
                        if (state.isCollapsed) {
                            this.el.classList.add('collapsed');
                        }
                        
                        if (state.isFloating) {
                            this.floatPanel();
                            this.el.style.left = state.position.left;
                            this.el.style.top = state.position.top;
                        } else if (state.isDocked) {
                            this.dockPanel(state.lastPosition || 'right');
                        }
                    }
                } catch (error) {
                    console.warn('加载面板状态失败:', error);
                }
            },
            
            // 清理事件监听器
            cleanupEventListeners() {
                if (this.resizeObserver) {
                    this.resizeObserver.disconnect();
                }
                
                const indicator = document.querySelector('.docking-indicator');
                if (indicator) {
                    indicator.remove();
                }
            },
            
            // 增强的样式计算
            get classNames() {
                const baseClasses = super.classNames;
                const stateClasses = [];
                
                if (this.panelState.isCollapsed) {
                    stateClasses.push('o-collapsed');
                }
                
                if (this.panelState.isFloating) {
                    stateClasses.push('o-floating');
                }
                
                if (this.panelState.isDocked) {
                    stateClasses.push('o-docked');
                    if (this.panelState.lastPosition) {
                        stateClasses.push(`o-docked-${this.panelState.lastPosition}`);
                    }
                }
                
                if (this.panelState.isAnimating) {
                    stateClasses.push('o-animating');
                }
                
                return `${baseClasses} ${stateClasses.join(' ')}`;
            },
            
            // 获取面板状态
            getPanelState() {
                return {
                    ...this.panelState,
                    config: this.panelConfig
                };
            },
            
            // 更新面板配置
            updatePanelConfig(newConfig) {
                Object.assign(this.panelConfig, newConfig);
                this.savePanelState();
            }
        };
        
        // 替换原始组件
        __exports.ActionPanel = EnhancedActionPanel;
    }
};

// 应用面板功能增强
ActionPanelEnhancer.enhanceActionPanel();
```

## 技术特点

### 1. 组件设计
- 基于OWL框架的组件架构
- 可配置的属性系统
- 灵活的插槽支持

### 2. 布局管理
- 可调整大小的面板
- 响应式布局设计
- 环境感知的样式调整

### 3. 状态管理
- 响应式状态绑定
- 邮件存储服务集成
- 环境变量支持

### 4. 样式系统
- 动态样式计算
- 条件样式应用
- Bootstrap类集成

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的面板组件
- 清晰的属性接口

### 2. 组合模式 (Composite Pattern)
- 可调整面板的组合
- 插槽内容的组合

### 3. 策略模式 (Strategy Pattern)
- 不同环境的样式策略
- 可配置的行为模式

## 注意事项

1. **响应式设计**: 确保在不同屏幕尺寸下的正确显示
2. **性能优化**: 避免不必要的样式重计算
3. **可访问性**: 支持键盘导航和屏幕阅读器
4. **兼容性**: 确保与不同环境的兼容性

## 扩展建议

1. **主题支持**: 添加多种主题样式支持
2. **动画效果**: 增强面板的动画效果
3. **拖拽功能**: 支持面板的拖拽和停靠
4. **快捷键**: 添加键盘快捷键支持
5. **持久化**: 支持面板状态的持久化存储

该组件为讨论应用提供了灵活的面板布局解决方案，是侧边栏和面板系统的重要基础组件。

# Notification Settings - 通知设置

## 概述

`notification_settings.js` 实现了 Odoo 讨论应用中的通知设置组件，用于管理线程的通知偏好设置。该组件支持静音时长设置、全局通知设置对话框、下拉菜单选择等功能，集成了操作面板、下拉菜单、对话框等子组件，提供了完整的通知管理界面，是讨论应用中通知控制功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/notification_settings.js`
- **行数**: 47
- **模块**: `@mail/discuss/core/common/notification_settings`

## 依赖关系

```javascript
// 组件依赖
'@mail/discuss/core/common/action_panel'                           // 操作面板
'@web/core/dropdown/dropdown'                                      // 下拉菜单
'@web/core/dropdown/dropdown_item'                                 // 下拉菜单项
'@mail/discuss/core/common/discuss_notification_settings_client_action' // 通知设置客户端操作
'@web/core/dialog/dialog'                                          // 对话框

// 核心依赖
'@odoo/owl'                                                         // OWL 框架
'@web/core/utils/hooks'                                             // Web 核心钩子
```

## 组件定义

### NotificationDialog 类

```javascript
class NotificationDialog extends Component {
    static props = ["close?"];
    static components = { Dialog, DiscussNotificationSettingsClientAction };
    static template = xml`
        <Dialog size="'md'" footer="false">
            <DiscussNotificationSettingsClientAction/>
        </Dialog>
    `;
}
```

**对话框特性**:
- 中等尺寸对话框
- 无底部按钮
- 集成通知设置客户端操作
- 内联XML模板

### NotificationSettings 类

```javascript
const NotificationSettings = class NotificationSettings extends Component {
    static components = { ActionPanel, Dropdown, DropdownItem };
    static props = ["hasSizeConstraints?", "thread", "close?", "className?"];
    static template = "discuss.NotificationSettings";
}
```

**组件特性**:
- 继承自Component基类
- 集成多个UI组件
- 支持尺寸约束配置
- 接收线程和关闭回调

## 组件设置

### setup() 方法

```javascript
setup() {
    this.store = useState(useService("mail.store"));
    this.dialog = useService("dialog");
}
```

**初始化内容**:
- 邮件存储服务集成
- 对话框服务集成
- 响应式状态管理

## 核心功能

### 1. 静音设置

```javascript
setMute(minutes) {
    this.store.settings.setMuteDuration(minutes, this.props.thread);
    this.props.close?.();
}
```

**静音功能**:
- **时长设置**: 设置指定分钟数的静音
- **线程绑定**: 针对特定线程设置静音
- **自动关闭**: 设置完成后自动关闭界面

### 2. 全局设置对话框

```javascript
onClickAllConversationsMuted() {
    this.dialog.add(NotificationDialog);
}
```

**对话框功能**:
- **全局设置**: 打开全局通知设置对话框
- **对话框服务**: 使用对话框服务管理界面
- **客户端操作**: 集成专门的客户端操作组件

## 使用场景

### 1. 通知设置增强

```javascript
// 通知设置增强功能
const NotificationSettingsEnhancer = {
    enhanceNotificationSettings: () => {
        const EnhancedNotificationSettings = class extends NotificationSettings {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.notificationState = useState({
                    customMuteDuration: 60, // 自定义静音时长（分钟）
                    muteSchedule: null, // 定时静音设置
                    soundEnabled: true, // 声音通知
                    desktopEnabled: true, // 桌面通知
                    emailEnabled: false, // 邮件通知
                    keywordFilters: [], // 关键词过滤
                    priorityFilter: 'all', // 优先级过滤
                    timeBasedMute: false, // 基于时间的静音
                    muteStartTime: '22:00', // 静音开始时间
                    muteEndTime: '08:00' // 静音结束时间
                });
                
                // 预设静音选项
                this.mutePresets = [
                    { label: '15分钟', value: 15 },
                    { label: '1小时', value: 60 },
                    { label: '8小时', value: 480 },
                    { label: '24小时', value: 1440 },
                    { label: '1周', value: 10080 },
                    { label: '永久', value: -1 }
                ];
                
                // 设置增强功能
                this.setupEnhancedFeatures();
                
                // 加载用户偏好
                this.loadUserPreferences();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置定时器检查
                this.setupScheduleChecker();
                
                // 设置通知权限检查
                this.checkNotificationPermissions();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置通知测试
                this.setupNotificationTesting();
            },
            
            // 增强的静音设置
            setMute(minutes) {
                // 记录静音操作
                this.recordMuteAction(minutes);
                
                // 设置静音
                super.setMute(minutes);
                
                // 显示确认消息
                this.showMuteConfirmation(minutes);
                
                // 更新用户偏好
                this.updateUserPreferences();
            },
            
            // 自定义静音时长
            setCustomMute() {
                const minutes = this.notificationState.customMuteDuration;
                
                if (minutes <= 0) {
                    this.env.services.notification.add(
                        '请输入有效的静音时长',
                        { type: 'warning' }
                    );
                    return;
                }
                
                this.setMute(minutes);
            },
            
            // 设置定时静音
            setScheduledMute(schedule) {
                this.notificationState.muteSchedule = schedule;
                
                // 保存到存储
                this.store.settings.setMuteSchedule(schedule, this.props.thread);
                
                this.env.services.notification.add(
                    '定时静音已设置',
                    { type: 'success' }
                );
                
                this.props.close?.();
            },
            
            // 切换声音通知
            toggleSoundNotification() {
                this.notificationState.soundEnabled = !this.notificationState.soundEnabled;
                
                this.store.settings.setSoundNotification(
                    this.notificationState.soundEnabled,
                    this.props.thread
                );
                
                this.updateUserPreferences();
            },
            
            // 切换桌面通知
            async toggleDesktopNotification() {
                if (!this.notificationState.desktopEnabled) {
                    // 请求通知权限
                    const permission = await this.requestNotificationPermission();
                    
                    if (permission !== 'granted') {
                        this.env.services.notification.add(
                            '需要授权桌面通知权限',
                            { type: 'warning' }
                        );
                        return;
                    }
                }
                
                this.notificationState.desktopEnabled = !this.notificationState.desktopEnabled;
                
                this.store.settings.setDesktopNotification(
                    this.notificationState.desktopEnabled,
                    this.props.thread
                );
                
                this.updateUserPreferences();
            },
            
            // 切换邮件通知
            toggleEmailNotification() {
                this.notificationState.emailEnabled = !this.notificationState.emailEnabled;
                
                this.store.settings.setEmailNotification(
                    this.notificationState.emailEnabled,
                    this.props.thread
                );
                
                this.updateUserPreferences();
            },
            
            // 请求通知权限
            async requestNotificationPermission() {
                if (!('Notification' in window)) {
                    return 'denied';
                }
                
                if (Notification.permission === 'granted') {
                    return 'granted';
                }
                
                if (Notification.permission === 'denied') {
                    return 'denied';
                }
                
                const permission = await Notification.requestPermission();
                return permission;
            },
            
            // 添加关键词过滤
            addKeywordFilter(keyword) {
                if (!keyword.trim()) {
                    return;
                }
                
                const normalizedKeyword = keyword.trim().toLowerCase();
                
                if (this.notificationState.keywordFilters.includes(normalizedKeyword)) {
                    this.env.services.notification.add(
                        '关键词已存在',
                        { type: 'warning' }
                    );
                    return;
                }
                
                this.notificationState.keywordFilters.push(normalizedKeyword);
                
                this.store.settings.setKeywordFilters(
                    this.notificationState.keywordFilters,
                    this.props.thread
                );
                
                this.updateUserPreferences();
            },
            
            // 移除关键词过滤
            removeKeywordFilter(keyword) {
                const index = this.notificationState.keywordFilters.indexOf(keyword);
                
                if (index !== -1) {
                    this.notificationState.keywordFilters.splice(index, 1);
                    
                    this.store.settings.setKeywordFilters(
                        this.notificationState.keywordFilters,
                        this.props.thread
                    );
                    
                    this.updateUserPreferences();
                }
            },
            
            // 设置优先级过滤
            setPriorityFilter(priority) {
                this.notificationState.priorityFilter = priority;
                
                this.store.settings.setPriorityFilter(priority, this.props.thread);
                
                this.updateUserPreferences();
            },
            
            // 设置基于时间的静音
            setTimeBasedMute(enabled, startTime, endTime) {
                this.notificationState.timeBasedMute = enabled;
                this.notificationState.muteStartTime = startTime;
                this.notificationState.muteEndTime = endTime;
                
                this.store.settings.setTimeBasedMute(
                    enabled,
                    startTime,
                    endTime,
                    this.props.thread
                );
                
                this.updateUserPreferences();
            },
            
            // 设置定时器检查
            setupScheduleChecker() {
                // 每分钟检查一次定时静音
                this.scheduleInterval = setInterval(() => {
                    this.checkMuteSchedule();
                }, 60000);
                
                onWillUnmount(() => {
                    if (this.scheduleInterval) {
                        clearInterval(this.scheduleInterval);
                    }
                });
            },
            
            // 检查静音计划
            checkMuteSchedule() {
                if (!this.notificationState.timeBasedMute) {
                    return;
                }
                
                const now = new Date();
                const currentTime = now.getHours() * 60 + now.getMinutes();
                
                const startTime = this.parseTime(this.notificationState.muteStartTime);
                const endTime = this.parseTime(this.notificationState.muteEndTime);
                
                let shouldMute = false;
                
                if (startTime < endTime) {
                    // 同一天内的时间范围
                    shouldMute = currentTime >= startTime && currentTime < endTime;
                } else {
                    // 跨天的时间范围
                    shouldMute = currentTime >= startTime || currentTime < endTime;
                }
                
                if (shouldMute && !this.props.thread.isMuted) {
                    this.setMute(60); // 静音1小时
                } else if (!shouldMute && this.props.thread.isMuted && this.props.thread.muteReason === 'time_based') {
                    this.setMute(0); // 取消静音
                }
            },
            
            // 解析时间字符串
            parseTime(timeString) {
                const [hours, minutes] = timeString.split(':').map(Number);
                return hours * 60 + minutes;
            },
            
            // 检查通知权限
            async checkNotificationPermissions() {
                if ('Notification' in window) {
                    const permission = Notification.permission;
                    
                    if (permission === 'denied') {
                        this.notificationState.desktopEnabled = false;
                    } else if (permission === 'default') {
                        // 可以请求权限
                    }
                } else {
                    this.notificationState.desktopEnabled = false;
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
            },
            
            // 处理键盘快捷键
            handleKeyboardShortcut(event) {
                if (!this.el.contains(document.activeElement)) return;
                
                // Ctrl+M: 快速静音1小时
                if (event.ctrlKey && event.key === 'm') {
                    event.preventDefault();
                    this.setMute(60);
                }
                
                // Ctrl+U: 取消静音
                if (event.ctrlKey && event.key === 'u') {
                    event.preventDefault();
                    this.setMute(0);
                }
                
                // Escape: 关闭设置
                if (event.key === 'Escape') {
                    event.preventDefault();
                    this.props.close?.();
                }
            },
            
            // 设置通知测试
            setupNotificationTesting() {
                this.notificationTester = {
                    testSound: () => {
                        if (this.notificationState.soundEnabled) {
                            // 播放测试音效
                            const audio = new Audio('/mail/static/src/sounds/notification.mp3');
                            audio.play().catch(error => {
                                console.warn('播放测试音效失败:', error);
                            });
                        }
                    },
                    
                    testDesktop: () => {
                        if (this.notificationState.desktopEnabled && Notification.permission === 'granted') {
                            new Notification('测试通知', {
                                body: '这是一个测试通知',
                                icon: '/web/static/img/favicon.ico'
                            });
                        }
                    },
                    
                    testEmail: async () => {
                        if (this.notificationState.emailEnabled) {
                            try {
                                await this.env.services.orm.call(
                                    'mail.thread',
                                    'send_test_notification',
                                    [this.props.thread.id]
                                );
                                
                                this.env.services.notification.add(
                                    '测试邮件已发送',
                                    { type: 'success' }
                                );
                            } catch (error) {
                                console.error('发送测试邮件失败:', error);
                                this.env.services.notification.add(
                                    '发送测试邮件失败',
                                    { type: 'error' }
                                );
                            }
                        }
                    }
                };
            },
            
            // 记录静音操作
            recordMuteAction(minutes) {
                try {
                    const muteHistory = JSON.parse(
                        localStorage.getItem('notification_mute_history') || '[]'
                    );
                    
                    muteHistory.push({
                        threadId: this.props.thread.id,
                        threadName: this.props.thread.displayName,
                        duration: minutes,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个记录
                    if (muteHistory.length > 50) {
                        muteHistory.splice(0, muteHistory.length - 50);
                    }
                    
                    localStorage.setItem('notification_mute_history', JSON.stringify(muteHistory));
                } catch (error) {
                    console.warn('记录静音操作失败:', error);
                }
            },
            
            // 显示静音确认
            showMuteConfirmation(minutes) {
                let message;
                
                if (minutes === 0) {
                    message = '通知已恢复';
                } else if (minutes === -1) {
                    message = '已永久静音';
                } else if (minutes < 60) {
                    message = `已静音 ${minutes} 分钟`;
                } else if (minutes < 1440) {
                    const hours = Math.floor(minutes / 60);
                    message = `已静音 ${hours} 小时`;
                } else {
                    const days = Math.floor(minutes / 1440);
                    message = `已静音 ${days} 天`;
                }
                
                this.env.services.notification.add(message, { type: 'success' });
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('notification_preferences') || '{}'
                    );
                    
                    if (preferences[this.props.thread.id]) {
                        Object.assign(this.notificationState, preferences[this.props.thread.id]);
                    }
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 更新用户偏好
            updateUserPreferences() {
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('notification_preferences') || '{}'
                    );
                    
                    preferences[this.props.thread.id] = { ...this.notificationState };
                    
                    localStorage.setItem('notification_preferences', JSON.stringify(preferences));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 重置设置
            resetSettings() {
                this.notificationState.customMuteDuration = 60;
                this.notificationState.muteSchedule = null;
                this.notificationState.soundEnabled = true;
                this.notificationState.desktopEnabled = true;
                this.notificationState.emailEnabled = false;
                this.notificationState.keywordFilters = [];
                this.notificationState.priorityFilter = 'all';
                this.notificationState.timeBasedMute = false;
                
                // 清除存储的偏好
                try {
                    const preferences = JSON.parse(
                        localStorage.getItem('notification_preferences') || '{}'
                    );
                    
                    delete preferences[this.props.thread.id];
                    
                    localStorage.setItem('notification_preferences', JSON.stringify(preferences));
                } catch (error) {
                    console.warn('清除用户偏好失败:', error);
                }
                
                this.env.services.notification.add(
                    '通知设置已重置',
                    { type: 'success' }
                );
            },
            
            // 获取静音历史
            getMuteHistory() {
                try {
                    const history = JSON.parse(
                        localStorage.getItem('notification_mute_history') || '[]'
                    );
                    
                    return history.filter(record => record.threadId === this.props.thread.id);
                } catch (error) {
                    console.warn('获取静音历史失败:', error);
                    return [];
                }
            },
            
            // 获取通知统计
            getNotificationStats() {
                const history = this.getMuteHistory();
                
                return {
                    totalMutes: history.length,
                    lastMuteTime: history.length > 0 ? history[history.length - 1].timestamp : null,
                    averageMuteDuration: history.length > 0 
                        ? history.reduce((sum, record) => sum + record.duration, 0) / history.length
                        : 0,
                    currentSettings: { ...this.notificationState }
                };
            }
        };
        
        // 替换原始组件
        __exports.NotificationSettings = EnhancedNotificationSettings;
    }
};

// 应用通知设置增强
NotificationSettingsEnhancer.enhanceNotificationSettings();
```

## 技术特点

### 1. 组件架构
- 基于OWL框架的组件设计
- 多子组件集成
- 内联XML模板支持

### 2. 对话框管理
- 嵌套对话框组件
- 对话框服务集成
- 客户端操作集成

### 3. 状态管理
- 响应式状态绑定
- 存储服务集成
- 用户偏好持久化

### 4. 用户交互
- 下拉菜单选择
- 快捷操作支持
- 自动关闭机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的通知设置组件
- 清晰的组件层次结构

### 2. 对话框模式 (Dialog Pattern)
- 模态对话框管理
- 嵌套组件集成

### 3. 策略模式 (Strategy Pattern)
- 不同通知类型的处理策略
- 可配置的通知行为

## 注意事项

1. **权限管理**: 确保通知权限的正确处理
2. **用户体验**: 提供清晰的设置反馈
3. **性能优化**: 避免频繁的设置更新
4. **兼容性**: 确保跨浏览器的通知支持

## 扩展建议

1. **高级过滤**: 添加更复杂的通知过滤规则
2. **定时设置**: 支持基于时间的通知控制
3. **声音定制**: 支持自定义通知声音
4. **批量设置**: 支持多个线程的批量设置
5. **统计分析**: 添加通知使用统计分析

该组件为讨论应用提供了完整的通知管理界面，是用户体验优化的重要组成部分。

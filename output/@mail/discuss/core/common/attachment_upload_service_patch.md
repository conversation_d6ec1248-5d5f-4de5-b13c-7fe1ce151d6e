# Attachment Upload Service Patch - 附件上传服务补丁

## 概述

`attachment_upload_service_patch.js` 实现了对 Odoo 邮件系统中附件上传服务的补丁扩展，专门为讨论频道添加了公共上传权限控制功能。该补丁通过Odoo的补丁机制扩展了AttachmentUploadService，添加了总线事件监听、权限检查、自动清理等功能，确保在频道禁用公共上传时自动移除非内部用户的附件，是讨论应用中附件安全管理的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/attachment_upload_service_patch.js`
- **行数**: 32
- **模块**: `@mail/discuss/core/common/attachment_upload_service_patch`

## 依赖关系

```javascript
// 服务依赖
'@mail/core/common/attachment_upload_service'  // 附件上传服务
'@web/core/utils/patch'                        // 补丁工具
```

## 补丁定义

### AttachmentUploadService 补丁

```javascript
patch(AttachmentUploadService.prototype, {
    setup() {
        super.setup(...arguments);
        this.env.services["bus_service"].subscribe("mail.record/insert", ({ Thread }) => {
            if (
                Thread &&
                "allow_public_upload" in Thread &&
                !Thread.allow_public_upload &&
                !this.store.self.isInternalUser
            ) {
                const attachments = [...this.store.Thread.insert(Thread).composer.attachments];
                for (const attachment of attachments) {
                    this.unlink(attachment);
                }
            }
        });
    },
});
```

**补丁特性**:
- 扩展附件上传服务
- 添加总线事件监听
- 实现权限自动检查
- 提供附件自动清理

## 核心功能

### 1. 总线事件监听

```javascript
this.env.services["bus_service"].subscribe("mail.record/insert", ({ Thread }) => {
    // 事件处理逻辑
});
```

**监听功能**:
- **事件订阅**: 监听邮件记录插入事件
- **线程过滤**: 仅处理Thread类型的记录
- **实时响应**: 实时响应数据变化

### 2. 权限检查逻辑

```javascript
if (
    Thread &&
    "allow_public_upload" in Thread &&
    !Thread.allow_public_upload &&
    !this.store.self.isInternalUser
) {
    // 权限检查通过后的处理
}
```

**检查条件**:
- **线程存在**: 确保Thread对象存在
- **属性存在**: 检查allow_public_upload属性存在
- **公共上传禁用**: 频道禁用了公共上传
- **外部用户**: 当前用户不是内部用户

### 3. 附件自动清理

```javascript
const attachments = [...this.store.Thread.insert(Thread).composer.attachments];
for (const attachment of attachments) {
    this.unlink(attachment);
}
```

**清理功能**:
- **附件获取**: 获取编辑器中的所有附件
- **数组复制**: 创建附件数组副本避免迭代问题
- **逐个删除**: 遍历删除每个附件
- **服务调用**: 使用unlink方法删除附件

## 使用场景

### 1. 附件上传服务增强

```javascript
// 附件上传服务增强功能
const AttachmentUploadServiceEnhancer = {
    enhanceAttachmentUploadService: () => {
        const EnhancedAttachmentUploadPatch = {
            setup() {
                super.setup(...arguments);
                
                // 增强的事件监听
                this.setupEnhancedEventListeners();
                
                // 设置权限缓存
                this.permissionCache = new Map();
                this.permissionCacheTimeout = 5 * 60 * 1000; // 5分钟
                
                // 设置上传统计
                this.uploadStats = {
                    totalUploads: 0,
                    successfulUploads: 0,
                    failedUploads: 0,
                    blockedUploads: 0,
                    lastUploadTime: null
                };
                
                // 设置上传队列
                this.uploadQueue = [];
                this.maxConcurrentUploads = 3;
                this.currentUploads = 0;
            },
            
            // 设置增强的事件监听
            setupEnhancedEventListeners() {
                // 监听线程记录插入
                this.env.services["bus_service"].subscribe("mail.record/insert", (data) => {
                    this.handleRecordInsert(data);
                });
                
                // 监听线程记录更新
                this.env.services["bus_service"].subscribe("mail.record/update", (data) => {
                    this.handleRecordUpdate(data);
                });
                
                // 监听用户权限变化
                this.env.services["bus_service"].subscribe("user.permission/change", (data) => {
                    this.handlePermissionChange(data);
                });
                
                // 监听附件上传事件
                this.env.services["bus_service"].subscribe("attachment.upload/start", (data) => {
                    this.handleUploadStart(data);
                });
                
                this.env.services["bus_service"].subscribe("attachment.upload/complete", (data) => {
                    this.handleUploadComplete(data);
                });
                
                this.env.services["bus_service"].subscribe("attachment.upload/error", (data) => {
                    this.handleUploadError(data);
                });
            },
            
            // 处理记录插入
            handleRecordInsert({ Thread }) {
                if (!Thread) return;
                
                // 检查是否需要处理附件权限
                if (this.shouldCheckAttachmentPermissions(Thread)) {
                    this.processThreadAttachmentPermissions(Thread);
                }
                
                // 更新权限缓存
                this.updatePermissionCache(Thread);
            },
            
            // 处理记录更新
            handleRecordUpdate({ Thread }) {
                if (!Thread) return;
                
                // 检查权限变化
                if (this.hasPermissionChanged(Thread)) {
                    this.processThreadAttachmentPermissions(Thread);
                    this.updatePermissionCache(Thread);
                }
            },
            
            // 处理权限变化
            handlePermissionChange(data) {
                // 清除权限缓存
                this.permissionCache.clear();
                
                // 重新检查所有活跃线程的附件权限
                this.recheckAllThreadPermissions();
            },
            
            // 检查是否应该检查附件权限
            shouldCheckAttachmentPermissions(Thread) {
                return (
                    Thread &&
                    "allow_public_upload" in Thread &&
                    !Thread.allow_public_upload &&
                    !this.store.self.isInternalUser
                );
            },
            
            // 检查权限是否发生变化
            hasPermissionChanged(Thread) {
                if (!Thread || !Thread.id) return false;
                
                const cached = this.permissionCache.get(Thread.id);
                if (!cached) return true;
                
                const current = {
                    allow_public_upload: Thread.allow_public_upload,
                    timestamp: Date.now()
                };
                
                return (
                    cached.allow_public_upload !== current.allow_public_upload ||
                    (Date.now() - cached.timestamp) > this.permissionCacheTimeout
                );
            },
            
            // 处理线程附件权限
            processThreadAttachmentPermissions(Thread) {
                try {
                    const thread = this.store.Thread.insert(Thread);
                    
                    if (!thread || !thread.composer) {
                        return;
                    }
                    
                    const attachments = [...thread.composer.attachments];
                    
                    if (attachments.length === 0) {
                        return;
                    }
                    
                    // 记录被阻止的上传
                    this.uploadStats.blockedUploads += attachments.length;
                    
                    // 批量删除附件
                    this.batchUnlinkAttachments(attachments, thread);
                    
                    // 显示通知
                    this.showPermissionNotification(attachments.length, thread);
                    
                } catch (error) {
                    console.error('处理线程附件权限失败:', error);
                }
            },
            
            // 批量删除附件
            async batchUnlinkAttachments(attachments, thread) {
                const unlinkPromises = attachments.map(attachment => {
                    return this.unlinkAttachmentSafely(attachment);
                });
                
                try {
                    await Promise.all(unlinkPromises);
                    
                    // 记录删除日志
                    this.logAttachmentDeletion(attachments, thread);
                    
                } catch (error) {
                    console.error('批量删除附件失败:', error);
                }
            },
            
            // 安全删除附件
            async unlinkAttachmentSafely(attachment) {
                try {
                    await this.unlink(attachment);
                    return { success: true, attachment };
                } catch (error) {
                    console.error('删除附件失败:', attachment, error);
                    return { success: false, attachment, error };
                }
            },
            
            // 显示权限通知
            showPermissionNotification(count, thread) {
                const message = count === 1 
                    ? `由于权限限制，已移除 1 个附件`
                    : `由于权限限制，已移除 ${count} 个附件`;
                
                this.env.services.notification.add(message, {
                    type: 'warning',
                    title: '附件权限',
                    sticky: false
                });
            },
            
            // 更新权限缓存
            updatePermissionCache(Thread) {
                if (!Thread || !Thread.id) return;
                
                this.permissionCache.set(Thread.id, {
                    allow_public_upload: Thread.allow_public_upload,
                    timestamp: Date.now()
                });
            },
            
            // 重新检查所有线程权限
            recheckAllThreadPermissions() {
                const threads = Object.values(this.store.Thread.records);
                
                threads.forEach(thread => {
                    if (thread.model === 'discuss.channel') {
                        this.processThreadAttachmentPermissions(thread);
                    }
                });
            },
            
            // 处理上传开始
            handleUploadStart(data) {
                this.uploadStats.totalUploads++;
                this.currentUploads++;
                
                // 记录上传开始时间
                if (data.attachment) {
                    data.attachment.uploadStartTime = Date.now();
                }
            },
            
            // 处理上传完成
            handleUploadComplete(data) {
                this.uploadStats.successfulUploads++;
                this.uploadStats.lastUploadTime = Date.now();
                this.currentUploads = Math.max(0, this.currentUploads - 1);
                
                // 记录上传时长
                if (data.attachment && data.attachment.uploadStartTime) {
                    const duration = Date.now() - data.attachment.uploadStartTime;
                    this.recordUploadDuration(duration, data.attachment);
                }
                
                // 处理上传队列
                this.processUploadQueue();
            },
            
            // 处理上传错误
            handleUploadError(data) {
                this.uploadStats.failedUploads++;
                this.currentUploads = Math.max(0, this.currentUploads - 1);
                
                // 记录错误信息
                this.recordUploadError(data);
                
                // 处理上传队列
                this.processUploadQueue();
            },
            
            // 记录上传时长
            recordUploadDuration(duration, attachment) {
                try {
                    const stats = JSON.parse(
                        localStorage.getItem('upload_duration_stats') || '[]'
                    );
                    
                    stats.push({
                        duration,
                        fileSize: attachment.file_size || 0,
                        fileType: attachment.mimetype || 'unknown',
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (stats.length > 100) {
                        stats.splice(0, stats.length - 100);
                    }
                    
                    localStorage.setItem('upload_duration_stats', JSON.stringify(stats));
                } catch (error) {
                    console.warn('记录上传时长失败:', error);
                }
            },
            
            // 记录上传错误
            recordUploadError(data) {
                try {
                    const errors = JSON.parse(
                        localStorage.getItem('upload_error_log') || '[]'
                    );
                    
                    errors.push({
                        error: data.error?.message || 'Unknown error',
                        attachment: {
                            name: data.attachment?.name,
                            size: data.attachment?.file_size,
                            type: data.attachment?.mimetype
                        },
                        timestamp: Date.now()
                    });
                    
                    // 保留最近50个错误
                    if (errors.length > 50) {
                        errors.splice(0, errors.length - 50);
                    }
                    
                    localStorage.setItem('upload_error_log', JSON.stringify(errors));
                } catch (error) {
                    console.warn('记录上传错误失败:', error);
                }
            },
            
            // 记录附件删除日志
            logAttachmentDeletion(attachments, thread) {
                try {
                    const log = JSON.parse(
                        localStorage.getItem('attachment_deletion_log') || '[]'
                    );
                    
                    log.push({
                        threadId: thread.id,
                        threadName: thread.displayName,
                        attachmentCount: attachments.length,
                        attachments: attachments.map(att => ({
                            name: att.name,
                            size: att.file_size,
                            type: att.mimetype
                        })),
                        reason: 'permission_restriction',
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个记录
                    if (log.length > 100) {
                        log.splice(0, log.length - 100);
                    }
                    
                    localStorage.setItem('attachment_deletion_log', JSON.stringify(log));
                } catch (error) {
                    console.warn('记录附件删除日志失败:', error);
                }
            },
            
            // 处理上传队列
            processUploadQueue() {
                while (this.uploadQueue.length > 0 && this.currentUploads < this.maxConcurrentUploads) {
                    const uploadTask = this.uploadQueue.shift();
                    this.executeUpload(uploadTask);
                }
            },
            
            // 执行上传
            async executeUpload(uploadTask) {
                try {
                    this.currentUploads++;
                    await uploadTask.execute();
                } catch (error) {
                    console.error('执行上传任务失败:', error);
                } finally {
                    this.currentUploads = Math.max(0, this.currentUploads - 1);
                    this.processUploadQueue();
                }
            },
            
            // 获取上传统计
            getUploadStatistics() {
                return {
                    ...this.uploadStats,
                    successRate: this.uploadStats.totalUploads > 0 
                        ? (this.uploadStats.successfulUploads / this.uploadStats.totalUploads * 100).toFixed(2) + '%'
                        : '0%',
                    currentUploads: this.currentUploads,
                    queueLength: this.uploadQueue.length,
                    permissionCacheSize: this.permissionCache.size
                };
            },
            
            // 清理缓存
            cleanupCache() {
                const now = Date.now();
                
                for (const [threadId, cache] of this.permissionCache.entries()) {
                    if (now - cache.timestamp > this.permissionCacheTimeout) {
                        this.permissionCache.delete(threadId);
                    }
                }
            },
            
            // 获取权限检查历史
            getPermissionCheckHistory() {
                try {
                    return {
                        deletionLog: JSON.parse(localStorage.getItem('attachment_deletion_log') || '[]'),
                        errorLog: JSON.parse(localStorage.getItem('upload_error_log') || '[]'),
                        durationStats: JSON.parse(localStorage.getItem('upload_duration_stats') || '[]')
                    };
                } catch (error) {
                    return {
                        deletionLog: [],
                        errorLog: [],
                        durationStats: []
                    };
                }
            }
        };
        
        patch(AttachmentUploadService.prototype, EnhancedAttachmentUploadPatch);
    }
};

// 应用附件上传服务增强
AttachmentUploadServiceEnhancer.enhanceAttachmentUploadService();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 事件驱动
- 总线事件监听
- 实时权限检查
- 自动响应机制

### 3. 权限控制
- 细粒度权限检查
- 用户类型区分
- 动态权限验证

### 4. 自动化处理
- 自动附件清理
- 批量操作支持
- 错误处理机制

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 事件监听和响应
- 状态变化通知

### 3. 策略模式 (Strategy Pattern)
- 不同权限的处理策略
- 可配置的清理行为

## 注意事项

1. **性能影响**: 避免频繁的权限检查影响性能
2. **数据一致性**: 确保附件删除的数据一致性
3. **用户体验**: 提供清晰的权限提示信息
4. **错误处理**: 完善的异常处理机制

## 扩展建议

1. **权限缓存**: 实现更智能的权限缓存机制
2. **批量处理**: 优化批量附件处理性能
3. **审计日志**: 添加详细的操作审计日志
4. **恢复机制**: 实现误删附件的恢复功能
5. **通知优化**: 提供更友好的用户通知

该补丁为讨论应用的附件上传提供了重要的安全控制，确保附件权限的正确执行。

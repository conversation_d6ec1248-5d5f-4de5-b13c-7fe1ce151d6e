# Discuss Notification Settings - 讨论通知设置

## 概述

`discuss_notification_settings.js` 实现了 Odoo 讨论应用中的全局通知设置组件，用于管理整个讨论应用的通知偏好。该组件支持静音详情显示切换、静音时长选择、默认设置管理等功能，集成了邮件存储服务和状态管理，提供了简洁的全局通知控制界面，是讨论应用中通知管理系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/discuss_notification_settings.js`
- **行数**: 43
- **模块**: `@mail/discuss/core/common/discuss_notification_settings`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                // OWL 框架
'@web/core/utils/hooks'    // Web 核心钩子
```

## 组件定义

### DiscussNotificationSettings 类

```javascript
const DiscussNotificationSettings = class DiscussNotificationSettings extends Component {
    static props = {};
    static template = "mail.DiscussNotificationSettings";
}
```

**组件特性**:
- 继承自Component基类
- 无需外部属性
- 使用专用模板
- 全局设置管理

## 组件设置

### setup() 方法

```javascript
setup() {
    this.store = useState(useService("mail.store"));
    this.state = useState({
        selectedDuration: false,
    });
}
```

**初始化内容**:
- 邮件存储服务集成
- 响应式状态管理
- 选择时长状态跟踪

## 核心功能

### 1. 静音详情显示切换

```javascript
onChangeDisplayMuteDetails() {
    // set the default mute duration to forever when opens the mute details
    if (!this.store.settings.mute_until_dt) {
        const FOREVER = this.store.settings.MUTES.find((m) => m.label === "forever").value;
        this.store.settings.setMuteDuration(FOREVER);
        this.state.selectedDuration = FOREVER;
    } else {
        this.store.settings.setMuteDuration(false);
    }
}
```

**切换功能**:
- **状态检查**: 检查当前是否已设置静音
- **默认设置**: 首次打开时设置为永久静音
- **状态同步**: 同步本地状态和存储状态
- **取消设置**: 已设置时取消静音

### 2. 静音时长选择

```javascript
onChangeMuteDuration(ev) {
    if (ev.target.value === "default") {
        return;
    }
    this.store.settings.setMuteDuration(parseInt(ev.target.value));
    this.state.selectedDuration = parseInt(ev.target.value);
}
```

**选择功能**:
- **默认值过滤**: 忽略默认选项
- **数值转换**: 将字符串转换为整数
- **设置更新**: 更新存储中的静音时长
- **状态同步**: 同步本地选择状态

## 使用场景

### 1. 通知设置增强

```javascript
// 通知设置增强功能
const DiscussNotificationSettingsEnhancer = {
    enhanceDiscussNotificationSettings: () => {
        const EnhancedDiscussNotificationSettings = class extends DiscussNotificationSettings {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.enhancedState = useState({
                    selectedDuration: false,
                    customDuration: 60, // 自定义时长（分钟）
                    notificationTypes: {
                        mentions: true,
                        directMessages: true,
                        channelMessages: false,
                        systemNotifications: true
                    },
                    soundSettings: {
                        enabled: true,
                        volume: 0.5,
                        soundType: 'default'
                    },
                    scheduleSettings: {
                        enabled: false,
                        startTime: '22:00',
                        endTime: '08:00',
                        weekendsOnly: false
                    },
                    deviceSettings: {
                        desktop: true,
                        mobile: true,
                        email: false
                    }
                });
                
                // 加载用户偏好
                this.loadUserPreferences();
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置自动保存
                this.setupAutoSave();
                
                // 设置通知测试
                this.setupNotificationTesting();
                
                // 设置导入导出
                this.setupImportExport();
                
                // 设置预设管理
                this.setupPresetManagement();
            },
            
            // 增强的静音详情切换
            onChangeDisplayMuteDetails() {
                // 调用原始方法
                super.onChangeDisplayMuteDetails();
                
                // 记录操作
                this.recordSettingChange('mute_details_toggle', {
                    enabled: !this.store.settings.mute_until_dt,
                    timestamp: Date.now()
                });
                
                // 保存偏好
                this.saveUserPreferences();
            },
            
            // 增强的静音时长选择
            onChangeMuteDuration(ev) {
                const value = ev.target.value;
                
                if (value === "default") {
                    return;
                }
                
                if (value === "custom") {
                    this.showCustomDurationDialog();
                    return;
                }
                
                // 调用原始方法
                super.onChangeMuteDuration(ev);
                
                // 记录操作
                this.recordSettingChange('mute_duration_change', {
                    duration: parseInt(value),
                    timestamp: Date.now()
                });
                
                // 保存偏好
                this.saveUserPreferences();
            },
            
            // 显示自定义时长对话框
            showCustomDurationDialog() {
                const duration = prompt('请输入自定义静音时长（分钟）:', this.enhancedState.customDuration);
                
                if (duration && !isNaN(duration)) {
                    const minutes = parseInt(duration);
                    
                    if (minutes > 0) {
                        this.enhancedState.customDuration = minutes;
                        this.store.settings.setMuteDuration(minutes);
                        this.state.selectedDuration = minutes;
                        
                        // 记录自定义时长
                        this.recordSettingChange('custom_mute_duration', {
                            duration: minutes,
                            timestamp: Date.now()
                        });
                        
                        this.saveUserPreferences();
                    }
                }
            },
            
            // 切换通知类型
            toggleNotificationType(type) {
                this.enhancedState.notificationTypes[type] = !this.enhancedState.notificationTypes[type];
                
                // 更新存储设置
                this.updateNotificationSettings();
                
                // 记录操作
                this.recordSettingChange('notification_type_toggle', {
                    type,
                    enabled: this.enhancedState.notificationTypes[type],
                    timestamp: Date.now()
                });
                
                this.saveUserPreferences();
            },
            
            // 更新声音设置
            updateSoundSettings(settings) {
                Object.assign(this.enhancedState.soundSettings, settings);
                
                // 更新存储设置
                this.updateNotificationSettings();
                
                // 记录操作
                this.recordSettingChange('sound_settings_update', {
                    settings,
                    timestamp: Date.now()
                });
                
                this.saveUserPreferences();
            },
            
            // 更新计划设置
            updateScheduleSettings(settings) {
                Object.assign(this.enhancedState.scheduleSettings, settings);
                
                // 更新存储设置
                this.updateNotificationSettings();
                
                // 记录操作
                this.recordSettingChange('schedule_settings_update', {
                    settings,
                    timestamp: Date.now()
                });
                
                this.saveUserPreferences();
            },
            
            // 更新设备设置
            updateDeviceSettings(settings) {
                Object.assign(this.enhancedState.deviceSettings, settings);
                
                // 更新存储设置
                this.updateNotificationSettings();
                
                // 记录操作
                this.recordSettingChange('device_settings_update', {
                    settings,
                    timestamp: Date.now()
                });
                
                this.saveUserPreferences();
            },
            
            // 更新通知设置
            updateNotificationSettings() {
                // 这里可以调用后端API更新设置
                // 或者更新本地存储
                try {
                    const settings = {
                        notificationTypes: this.enhancedState.notificationTypes,
                        soundSettings: this.enhancedState.soundSettings,
                        scheduleSettings: this.enhancedState.scheduleSettings,
                        deviceSettings: this.enhancedState.deviceSettings
                    };
                    
                    // 更新到存储服务
                    this.store.settings.updateNotificationSettings(settings);
                } catch (error) {
                    console.error('更新通知设置失败:', error);
                }
            },
            
            // 设置自动保存
            setupAutoSave() {
                this.autoSaveInterval = setInterval(() => {
                    this.saveUserPreferences();
                }, 30000); // 每30秒自动保存
                
                // 组件销毁时清理
                onWillUnmount(() => {
                    if (this.autoSaveInterval) {
                        clearInterval(this.autoSaveInterval);
                    }
                });
            },
            
            // 设置通知测试
            setupNotificationTesting() {
                this.notificationTester = {
                    testDesktop: () => {
                        if (this.enhancedState.deviceSettings.desktop) {
                            this.sendTestNotification('desktop');
                        }
                    },
                    
                    testSound: () => {
                        if (this.enhancedState.soundSettings.enabled) {
                            this.playTestSound();
                        }
                    },
                    
                    testEmail: () => {
                        if (this.enhancedState.deviceSettings.email) {
                            this.sendTestEmail();
                        }
                    }
                };
            },
            
            // 发送测试通知
            sendTestNotification(type) {
                switch (type) {
                    case 'desktop':
                        if ('Notification' in window && Notification.permission === 'granted') {
                            new Notification('测试通知', {
                                body: '这是一个测试通知',
                                icon: '/web/static/img/favicon.ico'
                            });
                        }
                        break;
                    case 'sound':
                        this.playTestSound();
                        break;
                    case 'email':
                        this.sendTestEmail();
                        break;
                }
            },
            
            // 播放测试声音
            playTestSound() {
                try {
                    const audio = new Audio('/mail/static/src/sounds/notification.mp3');
                    audio.volume = this.enhancedState.soundSettings.volume;
                    audio.play();
                } catch (error) {
                    console.error('播放测试声音失败:', error);
                }
            },
            
            // 发送测试邮件
            async sendTestEmail() {
                try {
                    await this.env.services.rpc('/mail/test_notification_email');
                    this.env.services.notification.add('测试邮件已发送', { type: 'success' });
                } catch (error) {
                    console.error('发送测试邮件失败:', error);
                    this.env.services.notification.add('发送测试邮件失败', { type: 'error' });
                }
            },
            
            // 设置导入导出
            setupImportExport() {
                this.importExport = {
                    exportSettings: () => {
                        const settings = {
                            notificationTypes: this.enhancedState.notificationTypes,
                            soundSettings: this.enhancedState.soundSettings,
                            scheduleSettings: this.enhancedState.scheduleSettings,
                            deviceSettings: this.enhancedState.deviceSettings,
                            customDuration: this.enhancedState.customDuration,
                            exportDate: new Date().toISOString()
                        };
                        
                        const blob = new Blob([JSON.stringify(settings, null, 2)], {
                            type: 'application/json'
                        });
                        
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'notification_settings.json';
                        a.click();
                        
                        URL.revokeObjectURL(url);
                    },
                    
                    importSettings: (file) => {
                        const reader = new FileReader();
                        
                        reader.onload = (e) => {
                            try {
                                const settings = JSON.parse(e.target.result);
                                this.applyImportedSettings(settings);
                            } catch (error) {
                                console.error('导入设置失败:', error);
                                this.env.services.notification.add('导入设置失败', { type: 'error' });
                            }
                        };
                        
                        reader.readAsText(file);
                    }
                };
            },
            
            // 应用导入的设置
            applyImportedSettings(settings) {
                if (settings.notificationTypes) {
                    Object.assign(this.enhancedState.notificationTypes, settings.notificationTypes);
                }
                
                if (settings.soundSettings) {
                    Object.assign(this.enhancedState.soundSettings, settings.soundSettings);
                }
                
                if (settings.scheduleSettings) {
                    Object.assign(this.enhancedState.scheduleSettings, settings.scheduleSettings);
                }
                
                if (settings.deviceSettings) {
                    Object.assign(this.enhancedState.deviceSettings, settings.deviceSettings);
                }
                
                if (settings.customDuration) {
                    this.enhancedState.customDuration = settings.customDuration;
                }
                
                // 更新通知设置
                this.updateNotificationSettings();
                
                // 保存偏好
                this.saveUserPreferences();
                
                this.env.services.notification.add('设置导入成功', { type: 'success' });
            },
            
            // 设置预设管理
            setupPresetManagement() {
                this.presets = {
                    silent: {
                        name: '静音模式',
                        notificationTypes: {
                            mentions: false,
                            directMessages: false,
                            channelMessages: false,
                            systemNotifications: false
                        },
                        soundSettings: { enabled: false, volume: 0, soundType: 'none' },
                        deviceSettings: { desktop: false, mobile: false, email: false }
                    },
                    
                    minimal: {
                        name: '最小通知',
                        notificationTypes: {
                            mentions: true,
                            directMessages: true,
                            channelMessages: false,
                            systemNotifications: false
                        },
                        soundSettings: { enabled: false, volume: 0.3, soundType: 'subtle' },
                        deviceSettings: { desktop: true, mobile: true, email: false }
                    },
                    
                    standard: {
                        name: '标准模式',
                        notificationTypes: {
                            mentions: true,
                            directMessages: true,
                            channelMessages: true,
                            systemNotifications: true
                        },
                        soundSettings: { enabled: true, volume: 0.5, soundType: 'default' },
                        deviceSettings: { desktop: true, mobile: true, email: false }
                    },
                    
                    full: {
                        name: '完整通知',
                        notificationTypes: {
                            mentions: true,
                            directMessages: true,
                            channelMessages: true,
                            systemNotifications: true
                        },
                        soundSettings: { enabled: true, volume: 0.8, soundType: 'prominent' },
                        deviceSettings: { desktop: true, mobile: true, email: true }
                    }
                };
            },
            
            // 应用预设
            applyPreset(presetName) {
                const preset = this.presets[presetName];
                
                if (!preset) {
                    console.error('未知预设:', presetName);
                    return;
                }
                
                // 应用预设设置
                Object.assign(this.enhancedState.notificationTypes, preset.notificationTypes);
                Object.assign(this.enhancedState.soundSettings, preset.soundSettings);
                Object.assign(this.enhancedState.deviceSettings, preset.deviceSettings);
                
                // 更新通知设置
                this.updateNotificationSettings();
                
                // 记录操作
                this.recordSettingChange('preset_applied', {
                    preset: presetName,
                    timestamp: Date.now()
                });
                
                // 保存偏好
                this.saveUserPreferences();
                
                this.env.services.notification.add(`已应用 ${preset.name}`, { type: 'success' });
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const saved = localStorage.getItem('discuss_notification_preferences');
                    
                    if (saved) {
                        const preferences = JSON.parse(saved);
                        
                        if (preferences.notificationTypes) {
                            Object.assign(this.enhancedState.notificationTypes, preferences.notificationTypes);
                        }
                        
                        if (preferences.soundSettings) {
                            Object.assign(this.enhancedState.soundSettings, preferences.soundSettings);
                        }
                        
                        if (preferences.scheduleSettings) {
                            Object.assign(this.enhancedState.scheduleSettings, preferences.scheduleSettings);
                        }
                        
                        if (preferences.deviceSettings) {
                            Object.assign(this.enhancedState.deviceSettings, preferences.deviceSettings);
                        }
                        
                        if (preferences.customDuration) {
                            this.enhancedState.customDuration = preferences.customDuration;
                        }
                    }
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const preferences = {
                        notificationTypes: this.enhancedState.notificationTypes,
                        soundSettings: this.enhancedState.soundSettings,
                        scheduleSettings: this.enhancedState.scheduleSettings,
                        deviceSettings: this.enhancedState.deviceSettings,
                        customDuration: this.enhancedState.customDuration,
                        lastSaved: Date.now()
                    };
                    
                    localStorage.setItem('discuss_notification_preferences', JSON.stringify(preferences));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 记录设置变更
            recordSettingChange(action, data) {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('notification_setting_changes') || '[]'
                    );
                    
                    changes.push({
                        action,
                        data,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个变更
                    if (changes.length > 100) {
                        changes.splice(0, changes.length - 100);
                    }
                    
                    localStorage.setItem('notification_setting_changes', JSON.stringify(changes));
                } catch (error) {
                    console.warn('记录设置变更失败:', error);
                }
            },
            
            // 重置所有设置
            resetAllSettings() {
                const confirmed = confirm('确定要重置所有通知设置吗？');
                
                if (confirmed) {
                    // 重置到默认值
                    this.enhancedState.notificationTypes = {
                        mentions: true,
                        directMessages: true,
                        channelMessages: false,
                        systemNotifications: true
                    };
                    
                    this.enhancedState.soundSettings = {
                        enabled: true,
                        volume: 0.5,
                        soundType: 'default'
                    };
                    
                    this.enhancedState.scheduleSettings = {
                        enabled: false,
                        startTime: '22:00',
                        endTime: '08:00',
                        weekendsOnly: false
                    };
                    
                    this.enhancedState.deviceSettings = {
                        desktop: true,
                        mobile: true,
                        email: false
                    };
                    
                    this.enhancedState.customDuration = 60;
                    
                    // 更新通知设置
                    this.updateNotificationSettings();
                    
                    // 清除本地存储
                    localStorage.removeItem('discuss_notification_preferences');
                    localStorage.removeItem('notification_setting_changes');
                    
                    this.env.services.notification.add('所有设置已重置', { type: 'success' });
                }
            },
            
            // 获取设置统计
            getSettingsStatistics() {
                try {
                    const changes = JSON.parse(
                        localStorage.getItem('notification_setting_changes') || '[]'
                    );
                    
                    const stats = {
                        totalChanges: changes.length,
                        lastChange: changes.length > 0 ? changes[changes.length - 1].timestamp : null,
                        changesByAction: {},
                        recentChanges: changes.slice(-10)
                    };
                    
                    changes.forEach(change => {
                        stats.changesByAction[change.action] = (stats.changesByAction[change.action] || 0) + 1;
                    });
                    
                    return stats;
                } catch (error) {
                    return {
                        totalChanges: 0,
                        lastChange: null,
                        changesByAction: {},
                        recentChanges: []
                    };
                }
            }
        };
        
        // 替换原始组件
        __exports.DiscussNotificationSettings = EnhancedDiscussNotificationSettings;
    }
};

// 应用通知设置增强
DiscussNotificationSettingsEnhancer.enhanceDiscussNotificationSettings();
```

## 技术特点

### 1. 组件架构
- 基于OWL框架的组件设计
- 简洁的属性接口
- 响应式状态管理

### 2. 设置管理
- 全局通知设置控制
- 静音时长灵活配置
- 默认值智能处理

### 3. 用户交互
- 直观的切换操作
- 实时设置更新
- 状态同步机制

### 4. 数据持久化
- 设置状态持久化
- 用户偏好保存
- 跨会话设置保持

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的设置组件
- 清晰的组件接口

### 2. 状态模式 (State Pattern)
- 设置状态的管理
- 状态变化的处理

### 3. 观察者模式 (Observer Pattern)
- 设置变化的监听
- 状态更新的响应

## 注意事项

1. **数据同步**: 确保本地状态与存储状态的同步
2. **用户体验**: 提供清晰的设置反馈
3. **默认值**: 合理的默认设置值
4. **性能优化**: 避免频繁的设置更新

## 扩展建议

1. **更多选项**: 添加更多通知设置选项
2. **预设管理**: 实现通知设置预设功能
3. **导入导出**: 支持设置的导入导出
4. **测试功能**: 添加通知测试功能
5. **统计分析**: 添加设置使用统计分析

该组件为讨论应用提供了完整的全局通知设置管理，是用户个性化体验的重要组成部分。

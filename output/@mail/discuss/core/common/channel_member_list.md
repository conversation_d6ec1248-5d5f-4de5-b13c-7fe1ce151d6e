# Channel Member List - 频道成员列表

## 概述

`channel_member_list.js` 实现了 Odoo 讨论应用中的频道成员列表组件，用于显示和管理频道中的成员。该组件支持在线/离线状态分组显示、成员头像点击聊天、成员获取管理、邀请面板集成等功能，集成了在线状态、操作面板等子组件，提供了完整的成员管理界面，是讨论应用中成员管理功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/channel_member_list.js`
- **行数**: 67
- **模块**: `@mail/discuss/core/common/channel_member_list`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/im_status'              // 在线状态组件
'@mail/discuss/core/common/action_panel'   // 操作面板组件

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/l10n/translation'               // 国际化
'@web/core/utils/hooks'                    // Web 核心钩子
```

## 组件定义

### ChannelMemberList 类

```javascript
const ChannelMemberList = class ChannelMemberList extends Component {
    static components = { ImStatus, ActionPanel };
    static props = ["thread", "openChannelInvitePanel", "className?"];
    static template = "discuss.ChannelMemberList";
}
```

**组件特性**:
- 继承自Component基类
- 集成在线状态和操作面板组件
- 接收线程、邀请面板回调等属性
- 使用专用模板

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
    onWillStart(() => {
        if (this.props.thread.fetchMembersState === "not_fetched") {
            this.props.thread.fetchChannelMembers();
        }
    });
    onWillUpdateProps((nextProps) => {
        if (nextProps.thread.fetchMembersState === "not_fetched") {
            nextProps.thread.fetchChannelMembers();
        }
    });
}
```

**初始化内容**:
- 邮件存储服务集成
- 生命周期钩子管理
- 成员数据懒加载
- 属性更新响应

## 核心功能

### 1. 在线状态分组显示

```javascript
get onlineSectionText() {
    return _t("Online - %(online_count)s", {
        online_count: this.props.thread.onlineMembers.length,
    });
}

get offlineSectionText() {
    return _t("Offline - %(offline_count)s", {
        offline_count: this.props.thread.offlineMembers.length,
    });
}
```

**分组功能**:
- **在线分组**: 显示在线成员数量
- **离线分组**: 显示离线成员数量
- **国际化**: 支持多语言文本
- **动态计数**: 实时更新成员数量

### 2. 聊天权限检查

```javascript
canOpenChatWith(member) {
    if (this.store.inPublicPage) {
        return false;
    }
    if (member.persona.type === "guest") {
        return false;
    }
    return true;
}
```

**权限逻辑**:
- **公共页面检查**: 公共页面不允许聊天
- **访客检查**: 访客用户不允许聊天
- **默认允许**: 其他情况允许聊天

### 3. 头像点击聊天

```javascript
onClickAvatar(ev, member) {
    if (!this.canOpenChatWith(member)) {
        return;
    }
    this.store.openChat({ partnerId: member.persona.id });
}
```

**聊天功能**:
- **权限验证**: 检查是否可以与成员聊天
- **聊天启动**: 通过存储服务启动聊天
- **伙伴ID**: 使用成员的伙伴ID

## 使用场景

### 1. 成员管理增强

```javascript
// 成员管理增强功能
const ChannelMemberListEnhancer = {
    enhanceChannelMemberList: () => {
        const EnhancedChannelMemberList = class extends ChannelMemberList {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.memberState = useState({
                    viewMode: 'list', // list, grid, compact
                    sortBy: 'status', // status, name, role, joinDate
                    sortOrder: 'asc', // asc, desc
                    filterRole: 'all', // all, admin, moderator, member
                    searchQuery: '',
                    selectedMembers: new Set(),
                    isSelectionMode: false,
                    showOfflineMembers: true
                });
                
                // 成员统计
                this.memberStats = useState({
                    totalMembers: 0,
                    onlineMembers: 0,
                    offlineMembers: 0,
                    adminCount: 0,
                    moderatorCount: 0,
                    memberCount: 0,
                    guestCount: 0
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
                
                // 设置成员搜索
                this.setupMemberSearch();
                
                // 设置批量操作
                this.setupBatchOperations();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 监听成员变化
                useEffect(() => {
                    this.updateMemberStats();
                }, () => [this.props.thread.channelMembers]);
                
                // 设置搜索防抖
                this.searchDebounced = debounce(this.performMemberSearch.bind(this), 300);
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置右键菜单
                this.setupContextMenu();
            },
            
            // 更新成员统计
            updateMemberStats() {
                const members = this.props.thread.channelMembers || [];
                
                this.memberStats.totalMembers = members.length;
                this.memberStats.onlineMembers = this.props.thread.onlineMembers?.length || 0;
                this.memberStats.offlineMembers = this.props.thread.offlineMembers?.length || 0;
                
                // 角色统计
                const roleStats = { admin: 0, moderator: 0, member: 0, guest: 0 };
                members.forEach(member => {
                    const role = member.role || 'member';
                    const type = member.persona?.type || 'partner';
                    
                    if (type === 'guest') {
                        roleStats.guest++;
                    } else {
                        roleStats[role] = (roleStats[role] || 0) + 1;
                    }
                });
                
                this.memberStats.adminCount = roleStats.admin;
                this.memberStats.moderatorCount = roleStats.moderator;
                this.memberStats.memberCount = roleStats.member;
                this.memberStats.guestCount = roleStats.guest;
            },
            
            // 增强的在线状态文本
            get onlineSectionText() {
                const count = this.getFilteredOnlineMembers().length;
                return _t("Online - %(online_count)s", { online_count: count });
            },
            
            get offlineSectionText() {
                const count = this.getFilteredOfflineMembers().length;
                return _t("Offline - %(offline_count)s", { offline_count: count });
            },
            
            // 获取过滤后的在线成员
            getFilteredOnlineMembers() {
                let members = this.props.thread.onlineMembers || [];
                return this.applyFilters(members);
            },
            
            // 获取过滤后的离线成员
            getFilteredOfflineMembers() {
                if (!this.memberState.showOfflineMembers) {
                    return [];
                }
                
                let members = this.props.thread.offlineMembers || [];
                return this.applyFilters(members);
            },
            
            // 应用过滤器
            applyFilters(members) {
                let filtered = [...members];
                
                // 角色过滤
                if (this.memberState.filterRole !== 'all') {
                    filtered = filtered.filter(member => {
                        if (this.memberState.filterRole === 'guest') {
                            return member.persona?.type === 'guest';
                        }
                        return member.role === this.memberState.filterRole;
                    });
                }
                
                // 搜索过滤
                if (this.memberState.searchQuery) {
                    const query = this.memberState.searchQuery.toLowerCase();
                    filtered = filtered.filter(member =>
                        member.persona?.name?.toLowerCase().includes(query) ||
                        member.persona?.email?.toLowerCase().includes(query)
                    );
                }
                
                // 排序
                filtered = this.sortMembers(filtered);
                
                return filtered;
            },
            
            // 排序成员
            sortMembers(members) {
                const { sortBy, sortOrder } = this.memberState;
                
                return members.sort((a, b) => {
                    let comparison = 0;
                    
                    switch (sortBy) {
                        case 'name':
                            comparison = (a.persona?.name || '').localeCompare(b.persona?.name || '');
                            break;
                        case 'role':
                            const roleOrder = { admin: 0, moderator: 1, member: 2, guest: 3 };
                            const aRole = a.persona?.type === 'guest' ? 'guest' : (a.role || 'member');
                            const bRole = b.persona?.type === 'guest' ? 'guest' : (b.role || 'member');
                            comparison = roleOrder[aRole] - roleOrder[bRole];
                            break;
                        case 'joinDate':
                            comparison = new Date(a.create_date || 0) - new Date(b.create_date || 0);
                            break;
                        case 'status':
                        default:
                            // 在线状态已经分组，这里按名称排序
                            comparison = (a.persona?.name || '').localeCompare(b.persona?.name || '');
                            break;
                    }
                    
                    return sortOrder === 'desc' ? -comparison : comparison;
                });
            },
            
            // 增强的聊天权限检查
            canOpenChatWith(member) {
                // 调用原始检查
                if (!super.canOpenChatWith(member)) {
                    return false;
                }
                
                // 检查是否是自己
                if (member.persona?.id === this.store.self?.id) {
                    return false;
                }
                
                // 检查频道设置
                const thread = this.props.thread;
                if (thread.channel_type === 'channel' && !thread.allowMemberChat) {
                    // 检查是否有管理权限
                    const currentMember = thread.channelMembers?.find(
                        m => m.persona?.id === this.store.self?.id
                    );
                    
                    if (!currentMember || (currentMember.role !== 'admin' && currentMember.role !== 'moderator')) {
                        return false;
                    }
                }
                
                return true;
            },
            
            // 增强的头像点击处理
            onClickAvatar(ev, member) {
                if (this.memberState.isSelectionMode) {
                    this.toggleMemberSelection(member);
                    return;
                }
                
                if (!this.canOpenChatWith(member)) {
                    this.showChatRestrictionMessage(member);
                    return;
                }
                
                // 记录聊天启动
                this.recordChatInteraction(member);
                
                super.onClickAvatar(ev, member);
            },
            
            // 显示聊天限制消息
            showChatRestrictionMessage(member) {
                let message = '无法与此成员聊天';
                
                if (member.persona?.type === 'guest') {
                    message = '无法与访客用户聊天';
                } else if (member.persona?.id === this.store.self?.id) {
                    message = '无法与自己聊天';
                } else if (this.store.inPublicPage) {
                    message = '在公共页面无法启动聊天';
                }
                
                this.env.services.notification.add(message, { type: 'warning' });
            },
            
            // 记录聊天交互
            recordChatInteraction(member) {
                try {
                    const interaction = {
                        type: 'chat_started',
                        targetMember: member.persona?.id,
                        targetName: member.persona?.name,
                        timestamp: Date.now(),
                        threadId: this.props.thread.id
                    };
                    
                    // 保存到本地存储
                    const interactions = JSON.parse(
                        localStorage.getItem('member_interactions') || '[]'
                    );
                    interactions.push(interaction);
                    
                    // 保留最近100个交互
                    if (interactions.length > 100) {
                        interactions.splice(0, interactions.length - 100);
                    }
                    
                    localStorage.setItem('member_interactions', JSON.stringify(interactions));
                } catch (error) {
                    console.warn('记录聊天交互失败:', error);
                }
            },
            
            // 设置成员搜索
            setupMemberSearch() {
                this.memberSearch = {
                    performSearch: (query) => {
                        this.memberState.searchQuery = query;
                    },
                    
                    clearSearch: () => {
                        this.memberState.searchQuery = '';
                    },
                    
                    highlightMatches: (text, query) => {
                        if (!query) return text;
                        
                        const regex = new RegExp(`(${query})`, 'gi');
                        return text.replace(regex, '<mark>$1</mark>');
                    }
                };
            },
            
            // 执行成员搜索
            performMemberSearch(query) {
                this.memberSearch.performSearch(query);
            },
            
            // 设置批量操作
            setupBatchOperations() {
                this.batchOperations = {
                    selectAll: () => {
                        const allMembers = [
                            ...this.getFilteredOnlineMembers(),
                            ...this.getFilteredOfflineMembers()
                        ];
                        
                        this.memberState.selectedMembers = new Set(
                            allMembers.map(member => member.id)
                        );
                    },
                    
                    selectNone: () => {
                        this.memberState.selectedMembers.clear();
                    },
                    
                    removeSelected: async () => {
                        const selectedIds = Array.from(this.memberState.selectedMembers);
                        if (selectedIds.length === 0) return;
                        
                        const confirmed = confirm(
                            `确定要移除 ${selectedIds.length} 个成员吗？`
                        );
                        
                        if (confirmed) {
                            await this.removeMembersFromChannel(selectedIds);
                            this.memberState.selectedMembers.clear();
                        }
                    },
                    
                    changeRoleSelected: async (newRole) => {
                        const selectedIds = Array.from(this.memberState.selectedMembers);
                        if (selectedIds.length === 0) return;
                        
                        await this.changeMemberRoles(selectedIds, newRole);
                        this.memberState.selectedMembers.clear();
                    }
                };
            },
            
            // 切换成员选择
            toggleMemberSelection(member) {
                if (this.memberState.selectedMembers.has(member.id)) {
                    this.memberState.selectedMembers.delete(member.id);
                } else {
                    this.memberState.selectedMembers.add(member.id);
                }
            },
            
            // 从频道移除成员
            async removeMembersFromChannel(memberIds) {
                try {
                    await this.env.services.orm.call(
                        'discuss.channel',
                        'remove_members',
                        [[this.props.thread.id]],
                        { member_ids: memberIds }
                    );
                    
                    this.env.services.notification.add(
                        `已移除 ${memberIds.length} 个成员`,
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('移除成员失败:', error);
                    this.env.services.notification.add(
                        '移除成员失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 更改成员角色
            async changeMemberRoles(memberIds, newRole) {
                try {
                    await this.env.services.orm.call(
                        'discuss.channel',
                        'change_member_roles',
                        [[this.props.thread.id]],
                        { member_ids: memberIds, role: newRole }
                    );
                    
                    this.env.services.notification.add(
                        `已更改 ${memberIds.length} 个成员的角色`,
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('更改角色失败:', error);
                    this.env.services.notification.add(
                        '更改角色失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
            },
            
            // 处理键盘快捷键
            handleKeyboardShortcut(event) {
                if (!this.el.contains(document.activeElement)) return;
                
                // Ctrl+A: 全选成员
                if (event.ctrlKey && event.key === 'a') {
                    event.preventDefault();
                    this.batchOperations.selectAll();
                }
                
                // Escape: 取消选择
                if (event.key === 'Escape') {
                    this.batchOperations.selectNone();
                    this.memberState.isSelectionMode = false;
                }
                
                // Ctrl+F: 聚焦搜索
                if (event.ctrlKey && event.key === 'f') {
                    event.preventDefault();
                    const searchInput = this.el.querySelector('.member-search-input');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            },
            
            // 设置右键菜单
            setupContextMenu() {
                onMounted(() => {
                    this.el.addEventListener('contextmenu', this.handleContextMenu.bind(this));
                });
            },
            
            // 处理右键菜单
            handleContextMenu(event) {
                const memberElement = event.target.closest('.member-item');
                if (!memberElement) return;
                
                event.preventDefault();
                
                const memberId = memberElement.dataset.memberId;
                const member = this.findMemberById(memberId);
                
                if (member) {
                    this.showMemberContextMenu(event, member);
                }
            },
            
            // 查找成员
            findMemberById(memberId) {
                const allMembers = [
                    ...(this.props.thread.onlineMembers || []),
                    ...(this.props.thread.offlineMembers || [])
                ];
                
                return allMembers.find(member => member.id.toString() === memberId);
            },
            
            // 显示成员右键菜单
            showMemberContextMenu(event, member) {
                const menu = this.createContextMenu(member);
                
                menu.style.position = 'fixed';
                menu.style.left = event.clientX + 'px';
                menu.style.top = event.clientY + 'px';
                menu.style.zIndex = '9999';
                
                document.body.appendChild(menu);
                
                // 点击外部关闭菜单
                const closeMenu = (e) => {
                    if (!menu.contains(e.target)) {
                        menu.remove();
                        document.removeEventListener('click', closeMenu);
                    }
                };
                
                setTimeout(() => {
                    document.addEventListener('click', closeMenu);
                }, 0);
            },
            
            // 创建右键菜单
            createContextMenu(member) {
                const menu = document.createElement('div');
                menu.className = 'member-context-menu';
                
                const actions = this.getMemberActions(member);
                
                menu.innerHTML = actions.map(action => `
                    <div class="menu-item ${action.disabled ? 'disabled' : ''}" 
                         data-action="${action.id}">
                        <i class="fa ${action.icon}"></i>
                        <span>${action.label}</span>
                    </div>
                `).join('');
                
                // 绑定点击事件
                menu.addEventListener('click', (event) => {
                    const item = event.target.closest('.menu-item');
                    if (item && !item.classList.contains('disabled')) {
                        const actionId = item.dataset.action;
                        this.executeMemberAction(actionId, member);
                        menu.remove();
                    }
                });
                
                return menu;
            },
            
            // 获取成员操作
            getMemberActions(member) {
                const actions = [];
                
                if (this.canOpenChatWith(member)) {
                    actions.push({
                        id: 'chat',
                        label: '发起聊天',
                        icon: 'fa-comment',
                        disabled: false
                    });
                }
                
                if (this.canManageMember(member)) {
                    actions.push({
                        id: 'change_role',
                        label: '更改角色',
                        icon: 'fa-user-cog',
                        disabled: false
                    });
                    
                    actions.push({
                        id: 'remove',
                        label: '移除成员',
                        icon: 'fa-user-times',
                        disabled: false
                    });
                }
                
                actions.push({
                    id: 'view_profile',
                    label: '查看资料',
                    icon: 'fa-user',
                    disabled: false
                });
                
                return actions;
            },
            
            // 检查是否可以管理成员
            canManageMember(member) {
                const currentMember = this.props.thread.channelMembers?.find(
                    m => m.persona?.id === this.store.self?.id
                );
                
                if (!currentMember) return false;
                
                // 管理员可以管理所有人
                if (currentMember.role === 'admin') return true;
                
                // 版主可以管理普通成员
                if (currentMember.role === 'moderator' && member.role === 'member') return true;
                
                return false;
            },
            
            // 执行成员操作
            executeMemberAction(actionId, member) {
                switch (actionId) {
                    case 'chat':
                        this.onClickAvatar(null, member);
                        break;
                    case 'change_role':
                        this.showRoleChangeDialog(member);
                        break;
                    case 'remove':
                        this.removeMemberWithConfirmation(member);
                        break;
                    case 'view_profile':
                        this.viewMemberProfile(member);
                        break;
                }
            },
            
            // 显示角色更改对话框
            showRoleChangeDialog(member) {
                // 这里可以实现角色更改对话框
                console.log('显示角色更改对话框:', member);
            },
            
            // 确认移除成员
            async removeMemberWithConfirmation(member) {
                const confirmed = confirm(`确定要移除成员 ${member.persona?.name} 吗？`);
                if (confirmed) {
                    await this.removeMembersFromChannel([member.id]);
                }
            },
            
            // 查看成员资料
            viewMemberProfile(member) {
                // 这里可以实现查看成员资料功能
                console.log('查看成员资料:', member);
            },
            
            // 切换视图模式
            setViewMode(mode) {
                this.memberState.viewMode = mode;
            },
            
            // 设置排序
            setSorting(sortBy, sortOrder) {
                this.memberState.sortBy = sortBy;
                this.memberState.sortOrder = sortOrder;
            },
            
            // 设置角色过滤
            setRoleFilter(role) {
                this.memberState.filterRole = role;
            },
            
            // 切换离线成员显示
            toggleOfflineMembers() {
                this.memberState.showOfflineMembers = !this.memberState.showOfflineMembers;
            },
            
            // 切换选择模式
            toggleSelectionMode() {
                this.memberState.isSelectionMode = !this.memberState.isSelectionMode;
                if (!this.memberState.isSelectionMode) {
                    this.memberState.selectedMembers.clear();
                }
            },
            
            // 获取成员统计
            getMemberStats() {
                return {
                    ...this.memberStats,
                    filteredOnline: this.getFilteredOnlineMembers().length,
                    filteredOffline: this.getFilteredOfflineMembers().length,
                    selectedCount: this.memberState.selectedMembers.size
                };
            }
        };
        
        // 替换原始组件
        __exports.ChannelMemberList = EnhancedChannelMemberList;
    }
};

// 应用成员列表增强
ChannelMemberListEnhancer.enhanceChannelMemberList();
```

## 技术特点

### 1. 组件架构
- 基于OWL框架的组件设计
- 多子组件集成
- 响应式状态管理

### 2. 数据管理
- 懒加载成员数据
- 在线/离线状态分组
- 生命周期数据管理

### 3. 交互功能
- 头像点击聊天
- 权限检查机制
- 动态文本显示

### 4. 用户体验
- 国际化支持
- 实时状态更新
- 清晰的视觉反馈

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的成员列表组件
- 清晰的组件层次结构

### 2. 观察者模式 (Observer Pattern)
- 成员状态变化的监听
- 属性更新的响应

### 3. 策略模式 (Strategy Pattern)
- 不同权限的处理策略
- 可配置的显示模式

## 注意事项

1. **性能优化**: 大量成员时的渲染性能
2. **权限安全**: 确保聊天权限的正确性
3. **状态同步**: 保持成员状态的实时性
4. **用户体验**: 提供清晰的操作反馈

## 扩展建议

1. **搜索功能**: 添加成员搜索和过滤功能
2. **批量操作**: 支持成员的批量管理
3. **角色管理**: 增强成员角色管理功能
4. **活动状态**: 显示成员的活动状态
5. **统计分析**: 添加成员活动统计分析

该组件为讨论应用提供了完整的成员管理界面，是频道管理功能的重要组成部分。

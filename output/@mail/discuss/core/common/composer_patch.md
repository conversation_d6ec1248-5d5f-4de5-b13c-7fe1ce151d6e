# Composer Patch - 编辑器补丁

## 概述

`composer_patch.js` 实现了对 Odoo 邮件系统中编辑器组件的补丁扩展，专门为讨论频道添加了上传权限控制功能。该补丁通过Odoo的补丁机制扩展了Composer组件，重写了allowUpload属性的获取逻辑，为讨论频道中的文件上传提供了更精确的权限控制，确保只有授权用户才能在频道中上传文件，是讨论应用中文件上传安全管理的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/composer_patch.js`
- **行数**: 24
- **模块**: `@mail/discuss/core/common/composer_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/composer'  // 编辑器组件
'@web/core/utils/patch'       // 补丁工具
```

## 补丁定义

### Composer 补丁

```javascript
patch(Composer.prototype, {
    get allowUpload() {
        const thread = this.thread ?? this.message.thread;
        return (
            super.allowUpload &&
            (thread.model !== "discuss.channel" ||
                thread?.allow_public_upload ||
                this.store.self.isInternalUser)
        );
    },
});
```

**补丁特性**:
- 重写上传权限逻辑
- 针对讨论频道的特殊处理
- 保持原有功能的回退机制
- 多条件权限检查

## 核心功能

### 1. 上传权限控制

```javascript
get allowUpload() {
    const thread = this.thread ?? this.message.thread;
    return (
        super.allowUpload &&
        (thread.model !== "discuss.channel" ||
            thread?.allow_public_upload ||
            this.store.self.isInternalUser)
    );
}
```

**权限逻辑**:
- **基础权限**: 首先检查原有的上传权限
- **模型检查**: 检查是否为讨论频道模型
- **公共上传**: 检查频道是否允许公共上传
- **内部用户**: 检查当前用户是否为内部用户
- **逻辑组合**: 使用逻辑或组合多个条件

## 使用场景

### 1. 编辑器权限增强

```javascript
// 编辑器权限增强功能
const ComposerPermissionEnhancer = {
    enhanceComposerPermissions: () => {
        const EnhancedComposerPatch = {
            // 增强的上传权限检查
            get allowUpload() {
                const baseResult = this.getBaseUploadPermission();
                
                if (!baseResult) {
                    return false;
                }
                
                const thread = this.thread ?? this.message.thread;
                
                if (thread.model === "discuss.channel") {
                    return this.getChannelUploadPermission(thread);
                }
                
                return baseResult;
            },
            
            // 获取基础上传权限
            getBaseUploadPermission() {
                const thread = this.thread ?? this.message.thread;
                return (
                    super.allowUpload &&
                    (thread.model !== "discuss.channel" ||
                        thread?.allow_public_upload ||
                        this.store.self.isInternalUser)
                );
            },
            
            // 获取频道上传权限
            getChannelUploadPermission(thread) {
                const user = this.store.self;
                
                // 检查基本权限
                if (!user) {
                    return false;
                }
                
                // 内部用户总是可以上传
                if (user.isInternalUser) {
                    return true;
                }
                
                // 检查频道公共上传设置
                if (thread.allow_public_upload) {
                    return true;
                }
                
                // 检查频道成员权限
                const channelMember = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                if (!channelMember) {
                    return false; // 不是频道成员
                }
                
                // 检查成员角色权限
                if (channelMember.role === 'admin' || channelMember.role === 'moderator') {
                    return true;
                }
                
                // 检查频道设置
                if (thread.allowMemberUpload) {
                    return true;
                }
                
                // 检查用户级别权限
                return this.checkUserUploadPermissions(user, thread);
            },
            
            // 检查用户上传权限
            checkUserUploadPermissions(user, thread) {
                // 检查用户组权限
                const uploadGroups = thread.uploadAllowedGroups || [];
                const userGroups = user.groups || [];
                
                const hasGroupPermission = uploadGroups.some(groupId =>
                    userGroups.includes(groupId)
                );
                
                if (hasGroupPermission) {
                    return true;
                }
                
                // 检查用户白名单
                const uploadWhitelist = thread.uploadWhitelist || [];
                if (uploadWhitelist.includes(user.id)) {
                    return true;
                }
                
                // 检查时间限制
                if (thread.uploadTimeRestriction) {
                    const now = new Date();
                    const startTime = new Date(thread.uploadStartTime);
                    const endTime = new Date(thread.uploadEndTime);
                    
                    if (now < startTime || now > endTime) {
                        return false;
                    }
                }
                
                return false;
            },
            
            // 检查文件类型权限
            canUploadFileType(fileType) {
                const thread = this.thread ?? this.message.thread;
                
                if (thread.model !== "discuss.channel") {
                    return true; // 非频道默认允许
                }
                
                const allowedTypes = thread.allowedFileTypes || [];
                
                if (allowedTypes.length === 0) {
                    return true; // 无限制
                }
                
                return allowedTypes.some(type => {
                    if (type.startsWith('.')) {
                        // 文件扩展名检查
                        return fileType.toLowerCase().endsWith(type.toLowerCase());
                    } else {
                        // MIME类型检查
                        return fileType.startsWith(type);
                    }
                });
            },
            
            // 检查文件大小权限
            canUploadFileSize(fileSize) {
                const thread = this.thread ?? this.message.thread;
                
                if (thread.model !== "discuss.channel") {
                    return true; // 非频道默认允许
                }
                
                const maxSize = this.getMaxUploadSize(thread);
                return fileSize <= maxSize;
            },
            
            // 获取最大上传大小
            getMaxUploadSize(thread) {
                const user = this.store.self;
                
                // 管理员有更高的上传限制
                const channelMember = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                if (channelMember?.role === 'admin') {
                    return thread.adminMaxUploadSize || (100 * 1024 * 1024); // 100MB
                }
                
                if (channelMember?.role === 'moderator') {
                    return thread.moderatorMaxUploadSize || (50 * 1024 * 1024); // 50MB
                }
                
                return thread.memberMaxUploadSize || (25 * 1024 * 1024); // 25MB
            },
            
            // 检查上传频率限制
            canUploadWithRateLimit() {
                const thread = this.thread ?? this.message.thread;
                const user = this.store.self;
                
                if (thread.model !== "discuss.channel" || !thread.uploadRateLimit) {
                    return true;
                }
                
                const rateLimitKey = `upload_rate_${thread.id}_${user.id}`;
                const uploadHistory = JSON.parse(
                    localStorage.getItem(rateLimitKey) || '[]'
                );
                
                const now = Date.now();
                const timeWindow = thread.uploadRateLimitWindow || (60 * 1000); // 1分钟
                const maxUploads = thread.uploadRateLimitCount || 5;
                
                // 清理过期记录
                const recentUploads = uploadHistory.filter(
                    timestamp => (now - timestamp) < timeWindow
                );
                
                return recentUploads.length < maxUploads;
            },
            
            // 记录上传操作
            recordUploadAttempt() {
                const thread = this.thread ?? this.message.thread;
                const user = this.store.self;
                
                if (thread.model !== "discuss.channel" || !thread.uploadRateLimit) {
                    return;
                }
                
                const rateLimitKey = `upload_rate_${thread.id}_${user.id}`;
                const uploadHistory = JSON.parse(
                    localStorage.getItem(rateLimitKey) || '[]'
                );
                
                uploadHistory.push(Date.now());
                
                // 保留最近的记录
                const timeWindow = thread.uploadRateLimitWindow || (60 * 1000);
                const now = Date.now();
                const recentUploads = uploadHistory.filter(
                    timestamp => (now - timestamp) < timeWindow
                );
                
                localStorage.setItem(rateLimitKey, JSON.stringify(recentUploads));
            },
            
            // 获取上传限制信息
            getUploadRestrictions() {
                const thread = this.thread ?? this.message.thread;
                
                if (thread.model !== "discuss.channel") {
                    return {
                        hasRestrictions: false,
                        allowUpload: this.allowUpload
                    };
                }
                
                return {
                    hasRestrictions: true,
                    allowUpload: this.allowUpload,
                    allowPublicUpload: thread.allow_public_upload,
                    allowedFileTypes: thread.allowedFileTypes || [],
                    maxFileSize: this.getMaxUploadSize(thread),
                    rateLimitEnabled: Boolean(thread.uploadRateLimit),
                    rateLimitCount: thread.uploadRateLimitCount || 5,
                    rateLimitWindow: thread.uploadRateLimitWindow || 60000,
                    timeRestrictionEnabled: Boolean(thread.uploadTimeRestriction),
                    uploadStartTime: thread.uploadStartTime,
                    uploadEndTime: thread.uploadEndTime
                };
            },
            
            // 获取上传权限详情
            getUploadPermissionDetails() {
                const thread = this.thread ?? this.message.thread;
                const user = this.store.self;
                
                const details = {
                    canUpload: this.allowUpload,
                    reasons: []
                };
                
                if (!super.allowUpload) {
                    details.reasons.push('基础上传权限被禁用');
                    return details;
                }
                
                if (thread.model !== "discuss.channel") {
                    details.reasons.push('非频道模型，允许上传');
                    return details;
                }
                
                if (user.isInternalUser) {
                    details.reasons.push('内部用户，允许上传');
                    return details;
                }
                
                if (thread.allow_public_upload) {
                    details.reasons.push('频道允许公共上传');
                    return details;
                }
                
                const channelMember = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                if (!channelMember) {
                    details.canUpload = false;
                    details.reasons.push('不是频道成员');
                    return details;
                }
                
                if (channelMember.role === 'admin' || channelMember.role === 'moderator') {
                    details.reasons.push(`${channelMember.role}角色，允许上传`);
                    return details;
                }
                
                if (thread.allowMemberUpload) {
                    details.reasons.push('频道允许成员上传');
                    return details;
                }
                
                details.canUpload = false;
                details.reasons.push('频道不允许普通成员上传');
                
                return details;
            },
            
            // 显示上传限制提示
            showUploadRestrictionMessage() {
                const details = this.getUploadPermissionDetails();
                
                if (details.canUpload) {
                    return;
                }
                
                const message = details.reasons.join('; ');
                
                this.env.services.notification.add(
                    `无法上传文件: ${message}`,
                    { type: 'warning' }
                );
            },
            
            // 验证文件上传
            validateFileUpload(file) {
                const validation = {
                    valid: true,
                    errors: []
                };
                
                // 检查基本上传权限
                if (!this.allowUpload) {
                    validation.valid = false;
                    validation.errors.push('没有上传权限');
                    return validation;
                }
                
                // 检查文件类型
                if (!this.canUploadFileType(file.type)) {
                    validation.valid = false;
                    validation.errors.push(`不支持的文件类型: ${file.type}`);
                }
                
                // 检查文件大小
                if (!this.canUploadFileSize(file.size)) {
                    const maxSize = this.getMaxUploadSize(this.thread ?? this.message.thread);
                    validation.valid = false;
                    validation.errors.push(`文件过大，最大允许: ${this.formatFileSize(maxSize)}`);
                }
                
                // 检查频率限制
                if (!this.canUploadWithRateLimit()) {
                    validation.valid = false;
                    validation.errors.push('上传过于频繁，请稍后再试');
                }
                
                return validation;
            },
            
            // 格式化文件大小
            formatFileSize(bytes) {
                const units = ['B', 'KB', 'MB', 'GB'];
                let size = bytes;
                let unitIndex = 0;
                
                while (size >= 1024 && unitIndex < units.length - 1) {
                    size /= 1024;
                    unitIndex++;
                }
                
                return `${size.toFixed(1)} ${units[unitIndex]}`;
            }
        };
        
        patch(Composer.prototype, EnhancedComposerPatch);
    }
};

// 应用编辑器权限增强
ComposerPermissionEnhancer.enhanceComposerPermissions();
```

## 技术特点

### 1. 补丁机制
- 非侵入式功能扩展
- 保持原有功能完整性
- 运行时权限控制

### 2. 权限控制
- 多层级权限检查
- 条件逻辑组合
- 用户类型区分

### 3. 线程感知
- 自动获取线程上下文
- 模型类型判断
- 设置属性检查

### 4. 安全机制
- 默认拒绝策略
- 多重验证机制
- 权限降级处理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同模型的权限策略
- 可配置的权限规则

### 3. 责任链模式 (Chain of Responsibility Pattern)
- 多级权限检查
- 逐层权限验证

## 注意事项

1. **权限安全**: 确保权限检查的严密性和正确性
2. **性能影响**: 避免频繁的权限计算
3. **用户体验**: 提供清晰的权限提示信息
4. **兼容性**: 确保与原有功能的兼容性

## 扩展建议

1. **权限细化**: 添加更细粒度的上传权限控制
2. **文件类型**: 支持更复杂的文件类型限制
3. **配额管理**: 实现用户上传配额管理
4. **审计日志**: 添加上传操作的审计日志
5. **动态权限**: 支持基于时间和条件的动态权限

该补丁为讨论应用中的文件上传提供了重要的安全控制，确保只有授权用户才能上传文件。

# Attachment Model Patch - 附件模型补丁

## 概述

`attachment_model_patch.js` 实现了对 Odoo 邮件系统中附件模型的补丁扩展，专门为讨论频道中的附件添加了特殊的删除权限控制和URL路由处理。该补丁通过Odoo的补丁机制扩展了Attachment模型，重写了isDeletable和urlRoute属性的获取逻辑，为讨论频道中的附件提供了更精确的权限控制和访问路径，是讨论应用中附件管理的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/attachment_model_patch.js`
- **行数**: 30
- **模块**: `@mail/discuss/core/common/attachment_model_patch`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/attachment_model'  // 附件模型
'@web/core/utils/patch'               // 补丁工具
```

## 补丁定义

### Attachment 补丁

```javascript
patch(Attachment.prototype, {
    get isDeletable() {
        if (this.message && this.thread?.model === "discuss.channel") {
            return this.message.editable;
        }
        return super.isDeletable;
    },
    get urlRoute() {
        if (!this.access_token && this.thread?.model === "discuss.channel") {
            return this.isImage
                ? `/discuss/channel/${this.thread.id}/image/${this.id}`
                : `/discuss/channel/${this.thread.id}/attachment/${this.id}`;
        }
        return super.urlRoute;
    },
});
```

**补丁特性**:
- 重写删除权限逻辑
- 重写URL路由逻辑
- 针对讨论频道的特殊处理
- 保持原有功能的回退机制

## 核心功能

### 1. 删除权限控制

```javascript
get isDeletable() {
    if (this.message && this.thread?.model === "discuss.channel") {
        return this.message.editable;
    }
    return super.isDeletable;
}
```

**权限逻辑**:
- **频道检查**: 检查是否为讨论频道中的附件
- **消息关联**: 确保附件关联到消息
- **编辑权限**: 基于消息的可编辑性决定删除权限
- **默认回退**: 非频道附件使用原有逻辑

### 2. URL路由处理

```javascript
get urlRoute() {
    if (!this.access_token && this.thread?.model === "discuss.channel") {
        return this.isImage
            ? `/discuss/channel/${this.thread.id}/image/${this.id}`
            : `/discuss/channel/${this.thread.id}/attachment/${this.id}`;
    }
    return super.urlRoute;
}
```

**路由逻辑**:
- **访问令牌检查**: 无访问令牌且为讨论频道时使用特殊路由
- **图片路由**: 图片类型使用专用的图片路由
- **附件路由**: 非图片类型使用通用附件路由
- **默认回退**: 其他情况使用原有路由逻辑

## 使用场景

### 1. 附件权限增强

```javascript
// 附件权限增强功能
const AttachmentPermissionEnhancer = {
    enhanceAttachmentPermissions: () => {
        const EnhancedAttachmentPatch = {
            // 增强的删除权限检查
            get isDeletable() {
                // 调用原有逻辑
                const baseResult = this.getBaseDeletableStatus();
                
                if (this.message && this.thread?.model === "discuss.channel") {
                    return this.getChannelDeletableStatus();
                }
                
                return baseResult;
            },
            
            // 获取基础删除状态
            getBaseDeletableStatus() {
                if (this.message && this.thread?.model === "discuss.channel") {
                    return this.message.editable;
                }
                return super.isDeletable;
            },
            
            // 获取频道删除状态
            getChannelDeletableStatus() {
                const message = this.message;
                const thread = this.thread;
                const user = this.env.services['mail.store'].self;
                
                // 检查消息编辑权限
                if (!message.editable) {
                    return false;
                }
                
                // 检查频道权限
                const channelMember = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                if (!channelMember) {
                    return false; // 不是频道成员
                }
                
                // 管理员和版主可以删除任何附件
                if (channelMember.role === 'admin' || channelMember.role === 'moderator') {
                    return true;
                }
                
                // 普通成员只能删除自己的附件
                if (message.author?.id === user.id) {
                    return true;
                }
                
                // 检查附件年龄限制
                const attachmentAge = Date.now() - new Date(this.create_date).getTime();
                const maxEditTime = 24 * 60 * 60 * 1000; // 24小时
                
                if (attachmentAge > maxEditTime) {
                    return false; // 超过编辑时间限制
                }
                
                return false;
            },
            
            // 检查附件大小限制
            checkSizeLimit() {
                const maxSize = this.getMaxAttachmentSize();
                return this.file_size <= maxSize;
            },
            
            // 获取最大附件大小
            getMaxAttachmentSize() {
                const thread = this.thread;
                
                if (thread?.model === "discuss.channel") {
                    // 频道可能有不同的大小限制
                    const channelSettings = thread.custom_channel_name ? 
                        thread.channel_settings : 
                        this.env.services['mail.store'].settings.default_channel_settings;
                    
                    return channelSettings?.max_attachment_size || (25 * 1024 * 1024); // 25MB
                }
                
                return 25 * 1024 * 1024; // 默认25MB
            },
            
            // 检查附件类型限制
            checkTypeRestriction() {
                const allowedTypes = this.getAllowedAttachmentTypes();
                
                if (allowedTypes.length === 0) {
                    return true; // 无限制
                }
                
                return allowedTypes.some(type => {
                    if (type.startsWith('.')) {
                        // 文件扩展名检查
                        return this.name.toLowerCase().endsWith(type.toLowerCase());
                    } else {
                        // MIME类型检查
                        return this.mimetype.startsWith(type);
                    }
                });
            },
            
            // 获取允许的附件类型
            getAllowedAttachmentTypes() {
                const thread = this.thread;
                
                if (thread?.model === "discuss.channel") {
                    const channelSettings = thread.channel_settings || {};
                    return channelSettings.allowed_attachment_types || [];
                }
                
                return []; // 无限制
            },
            
            // 检查附件安全性
            checkSecurity() {
                // 检查文件名安全性
                if (this.hasUnsafeFileName()) {
                    return false;
                }
                
                // 检查MIME类型安全性
                if (this.hasUnsafeMimeType()) {
                    return false;
                }
                
                // 检查病毒扫描结果
                if (this.virus_scan_result === 'infected') {
                    return false;
                }
                
                return true;
            },
            
            // 检查不安全的文件名
            hasUnsafeFileName() {
                const unsafePatterns = [
                    /\.exe$/i,
                    /\.bat$/i,
                    /\.cmd$/i,
                    /\.scr$/i,
                    /\.vbs$/i,
                    /\.js$/i,
                    /\.jar$/i
                ];
                
                return unsafePatterns.some(pattern => pattern.test(this.name));
            },
            
            // 检查不安全的MIME类型
            hasUnsafeMimeType() {
                const unsafeMimeTypes = [
                    'application/x-executable',
                    'application/x-msdownload',
                    'application/x-msdos-program',
                    'application/javascript',
                    'text/javascript'
                ];
                
                return unsafeMimeTypes.includes(this.mimetype);
            },
            
            // 获取附件权限信息
            getPermissionInfo() {
                return {
                    isDeletable: this.isDeletable,
                    isDownloadable: this.isDownloadable,
                    isViewable: this.isViewable,
                    sizeLimit: this.checkSizeLimit(),
                    typeAllowed: this.checkTypeRestriction(),
                    isSecure: this.checkSecurity(),
                    permissions: this.getUserPermissions()
                };
            },
            
            // 获取用户权限
            getUserPermissions() {
                const user = this.env.services['mail.store'].self;
                const thread = this.thread;
                
                if (!thread || thread.model !== "discuss.channel") {
                    return { role: 'user', canDelete: this.isDeletable };
                }
                
                const channelMember = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                return {
                    role: channelMember?.role || 'guest',
                    canDelete: this.isDeletable,
                    canModerate: channelMember?.role === 'admin' || channelMember?.role === 'moderator',
                    isOwner: this.message?.author?.id === user.id
                };
            }
        };
        
        patch(Attachment.prototype, EnhancedAttachmentPatch);
    }
};

// 应用附件权限增强
AttachmentPermissionEnhancer.enhanceAttachmentPermissions();
```

### 2. URL路由增强

```javascript
// URL路由增强功能
const AttachmentUrlEnhancer = {
    enhanceAttachmentUrls: () => {
        const EnhancedUrlPatch = {
            // 增强的URL路由
            get urlRoute() {
                const baseRoute = this.getBaseUrlRoute();
                
                if (!this.access_token && this.thread?.model === "discuss.channel") {
                    return this.getChannelUrlRoute();
                }
                
                return baseRoute;
            },
            
            // 获取基础URL路由
            getBaseUrlRoute() {
                if (!this.access_token && this.thread?.model === "discuss.channel") {
                    return this.isImage
                        ? `/discuss/channel/${this.thread.id}/image/${this.id}`
                        : `/discuss/channel/${this.thread.id}/attachment/${this.id}`;
                }
                return super.urlRoute;
            },
            
            // 获取频道URL路由
            getChannelUrlRoute() {
                const thread = this.thread;
                const baseUrl = this.isImage ? 'image' : 'attachment';
                
                // 添加额外的路由参数
                const params = new URLSearchParams();
                
                // 添加版本参数用于缓存控制
                if (this.write_date) {
                    params.set('v', new Date(this.write_date).getTime().toString());
                }
                
                // 添加下载参数
                if (this.shouldForceDownload()) {
                    params.set('download', '1');
                }
                
                // 添加预览参数
                if (this.isPreviewable()) {
                    params.set('preview', '1');
                }
                
                const queryString = params.toString();
                const route = `/discuss/channel/${thread.id}/${baseUrl}/${this.id}`;
                
                return queryString ? `${route}?${queryString}` : route;
            },
            
            // 检查是否应该强制下载
            shouldForceDownload() {
                // 可执行文件强制下载
                if (this.hasUnsafeFileName()) {
                    return true;
                }
                
                // 大文件强制下载
                if (this.file_size > 10 * 1024 * 1024) { // 10MB
                    return true;
                }
                
                return false;
            },
            
            // 检查是否可预览
            isPreviewable() {
                const previewableMimeTypes = [
                    'image/',
                    'text/',
                    'application/pdf',
                    'video/',
                    'audio/'
                ];
                
                return previewableMimeTypes.some(type => 
                    this.mimetype.startsWith(type)
                );
            },
            
            // 获取缩略图URL
            get thumbnailUrl() {
                if (!this.isImage) {
                    return null;
                }
                
                if (this.thread?.model === "discuss.channel") {
                    const params = new URLSearchParams();
                    params.set('size', '150x150');
                    
                    if (this.write_date) {
                        params.set('v', new Date(this.write_date).getTime().toString());
                    }
                    
                    return `/discuss/channel/${this.thread.id}/image/${this.id}/thumbnail?${params.toString()}`;
                }
                
                return super.thumbnailUrl;
            },
            
            // 获取预览URL
            get previewUrl() {
                if (!this.isPreviewable()) {
                    return null;
                }
                
                if (this.thread?.model === "discuss.channel") {
                    const params = new URLSearchParams();
                    params.set('preview', '1');
                    
                    if (this.write_date) {
                        params.set('v', new Date(this.write_date).getTime().toString());
                    }
                    
                    const baseUrl = this.isImage ? 'image' : 'attachment';
                    return `/discuss/channel/${this.thread.id}/${baseUrl}/${this.id}?${params.toString()}`;
                }
                
                return this.urlRoute;
            },
            
            // 获取下载URL
            get downloadUrl() {
                if (this.thread?.model === "discuss.channel") {
                    const params = new URLSearchParams();
                    params.set('download', '1');
                    
                    if (this.write_date) {
                        params.set('v', new Date(this.write_date).getTime().toString());
                    }
                    
                    const baseUrl = this.isImage ? 'image' : 'attachment';
                    return `/discuss/channel/${this.thread.id}/${baseUrl}/${this.id}?${params.toString()}`;
                }
                
                return this.urlRoute;
            },
            
            // 获取共享URL
            get shareUrl() {
                if (this.thread?.model === "discuss.channel" && this.thread.channel_type === 'channel') {
                    // 公共频道的附件可以生成共享链接
                    const params = new URLSearchParams();
                    params.set('share', '1');
                    
                    if (this.access_token) {
                        params.set('token', this.access_token);
                    }
                    
                    const baseUrl = this.isImage ? 'image' : 'attachment';
                    return `/discuss/channel/${this.thread.id}/${baseUrl}/${this.id}?${params.toString()}`;
                }
                
                return null;
            },
            
            // 生成访问令牌
            async generateAccessToken() {
                if (this.access_token) {
                    return this.access_token;
                }
                
                try {
                    const response = await rpc('/mail/attachment/generate_access_token', {
                        attachment_id: this.id,
                        thread_id: this.thread?.id,
                        thread_model: this.thread?.model
                    });
                    
                    this.access_token = response.access_token;
                    return this.access_token;
                } catch (error) {
                    console.error('生成访问令牌失败:', error);
                    return null;
                }
            },
            
            // 撤销访问令牌
            async revokeAccessToken() {
                if (!this.access_token) {
                    return;
                }
                
                try {
                    await rpc('/mail/attachment/revoke_access_token', {
                        attachment_id: this.id,
                        access_token: this.access_token
                    });
                    
                    this.access_token = null;
                } catch (error) {
                    console.error('撤销访问令牌失败:', error);
                }
            },
            
            // 获取所有URL信息
            getUrlInfo() {
                return {
                    main: this.urlRoute,
                    thumbnail: this.thumbnailUrl,
                    preview: this.previewUrl,
                    download: this.downloadUrl,
                    share: this.shareUrl,
                    hasAccessToken: Boolean(this.access_token),
                    isPreviewable: this.isPreviewable(),
                    shouldForceDownload: this.shouldForceDownload()
                };
            }
        };
        
        patch(Attachment.prototype, EnhancedUrlPatch);
    }
};

// 应用URL路由增强
AttachmentUrlEnhancer.enhanceAttachmentUrls();
```

## 技术特点

### 1. 补丁机制
- 非侵入式扩展
- 保持原有功能完整性
- 针对性的功能增强

### 2. 权限控制
- 基于消息编辑权限的删除控制
- 频道特定的权限逻辑
- 安全的权限检查

### 3. URL管理
- 频道特定的URL路由
- 图片和附件的区分处理
- 访问令牌的条件处理

### 4. 模型扩展
- 属性获取器的重写
- 条件逻辑的实现
- 回退机制的保证

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同模型的处理策略
- 条件化的行为选择

### 3. 装饰器模式 (Decorator Pattern)
- 功能的装饰和增强
- 透明的功能扩展

## 注意事项

1. **权限安全**: 确保删除权限的正确性和安全性
2. **URL安全**: 防止未授权的附件访问
3. **性能影响**: 避免频繁的权限检查
4. **兼容性**: 确保与原有功能的兼容性

## 扩展建议

1. **权限细化**: 添加更细粒度的权限控制
2. **缓存优化**: 优化URL和权限的缓存机制
3. **安全增强**: 增强附件的安全检查
4. **审计日志**: 添加附件操作的审计日志
5. **批量操作**: 支持附件的批量权限管理

该补丁为讨论应用中的附件提供了专门的权限控制和URL管理，是附件安全管理的重要组成部分。

# Thread Model Patch - 线程模型补丁

## 概述

`thread_model_patch.js` 实现了对 Odoo 邮件系统中线程模型的补丁扩展，专门为讨论频道添加了丰富的功能增强。该补丁通过Odoo的补丁机制扩展了Thread模型，添加了在线/离线成员管理、头像URL处理、频道信息获取、附件加载、命令执行等核心功能，为讨论应用提供了完整的线程数据管理和交互逻辑，是讨论应用中最重要的数据模型增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/thread_model_patch.js`
- **行数**: 119
- **模块**: `@mail/discuss/core/common/thread_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/record'        // 记录基类
'@mail/core/common/thread_model'  // 线程模型
'@web/core/utils/patch'           // 补丁工具
'@web/core/utils/urls'            // URL工具
'@web/core/network/rpc'           // RPC网络服务
'@web/core/utils/concurrency'     // 并发控制
'@web/core/registry'              // 注册表系统
```

## 补丁定义

### Thread 补丁

```javascript
const threadPatch = {
    setup() {
        super.setup();
        this.fetchChannelMutex = new Mutex();
        this.fetchChannelInfoDeferred = undefined;
        this.fetchChannelInfoState = "not_fetched";
        this.onlineMembers = Record.many("ChannelMember", {
            compute() {
                return this.channelMembers.filter((member) =>
                    this.store.onlineMemberStatuses.includes(member.persona.im_status)
                );
            },
            sort(m1, m2) {
                return this.store.sortMembers(m1, m2);
            },
        });
        this.offlineMembers = Record.many("ChannelMember", {
            compute: this._computeOfflineMembers,
            sort(m1, m2) {
                return this.store.sortMembers(m1, m2);
            },
        });
    }
};
```

**补丁特性**:
- 扩展线程模型功能
- 添加成员状态管理
- 实现并发控制机制
- 提供智能排序功能

## 核心功能

### 1. 成员状态管理

```javascript
this.onlineMembers = Record.many("ChannelMember", {
    compute() {
        return this.channelMembers.filter((member) =>
            this.store.onlineMemberStatuses.includes(member.persona.im_status)
        );
    },
    sort(m1, m2) {
        return this.store.sortMembers(m1, m2);
    },
});

this.offlineMembers = Record.many("ChannelMember", {
    compute: this._computeOfflineMembers,
    sort(m1, m2) {
        return this.store.sortMembers(m1, m2);
    },
});
```

**成员管理功能**:
- **在线成员**: 基于在线状态过滤的成员列表
- **离线成员**: 计算得出的离线成员列表
- **智能排序**: 使用存储服务的排序方法
- **响应式计算**: 自动更新的计算属性

### 2. 头像URL处理

```javascript
get avatarUrl() {
    if (this.channel_type === "channel" || this.channel_type === "group") {
        return imageUrl("discuss.channel", this.id, "avatar_128", {
            unique: this.avatarCacheKey,
        });
    }
    if (this.channel_type === "chat" && this.correspondent) {
        return this.correspondent.persona.avatarUrl;
    }
    return super.avatarUrl;
}
```

**头像逻辑**:
- **频道头像**: 频道和群组使用专用头像
- **聊天头像**: 私聊使用对方用户头像
- **缓存控制**: 使用缓存键确保头像更新
- **回退机制**: 其他情况使用父类逻辑

### 3. 频道信息获取

```javascript
async fetchChannelInfo() {
    return this.fetchChannelMutex.exec(async () => {
        if (!(this.localId in this.store.Thread.records)) {
            return; // channel was deleted in-between two calls
        }
        const data = await rpc("/discuss/channel/info", { channel_id: this.id });
        if (data) {
            this.store.insert(data);
        } else {
            this.delete();
        }
        return data ? this : undefined;
    });
}
```

**信息获取功能**:
- **并发控制**: 使用Mutex防止重复请求
- **存在检查**: 验证频道是否仍然存在
- **数据更新**: 将获取的数据插入存储
- **错误处理**: 数据不存在时删除频道

### 4. 附件加载

```javascript
async fetchMoreAttachments(limit = 30) {
    if (this.isLoadingAttachments || this.areAttachmentsLoaded) {
        return;
    }
    this.isLoadingAttachments = true;
    try {
        const data = await rpc("/discuss/channel/attachments", {
            before: Math.min(...this.attachments.map(({ id }) => id)),
            channel_id: this.id,
            limit,
        });
        const { Attachment: attachments = [] } = this.store.insert(data);
        if (attachments.length < limit) {
            this.areAttachmentsLoaded = true;
        }
    } finally {
        this.isLoadingAttachments = false;
    }
}
```

**附件加载功能**:
- **状态检查**: 防止重复加载
- **分页加载**: 基于ID的分页机制
- **加载状态**: 管理加载状态标志
- **完成检测**: 检测是否已加载完所有附件

### 5. 命令执行

```javascript
async post(body) {
    if (this.model === "discuss.channel" && body.startsWith("/")) {
        const [firstWord] = body.substring(1).split(/\s/);
        const command = commandRegistry.get(firstWord, false);
        if (
            command &&
            (!command.channel_types || command.channel_types.includes(this.channel_type))
        ) {
            await this.executeCommand(command, body);
            return;
        }
    }
    return super.post(...arguments);
}
```

**命令处理功能**:
- **命令检测**: 检测以"/"开头的命令
- **命令查找**: 从注册表中查找命令
- **类型验证**: 验证命令是否适用于当前频道类型
- **命令执行**: 执行匹配的命令

## 使用场景

### 1. 线程模型增强

```javascript
// 线程模型增强功能
const ThreadModelEnhancer = {
    enhanceThreadModel: () => {
        const EnhancedThreadPatch = {
            setup() {
                super.setup();
                
                // 增强的并发控制
                this.fetchChannelMutex = new Mutex();
                this.fetchAttachmentsMutex = new Mutex();
                this.fetchMembersMutex = new Mutex();
                
                // 增强的状态管理
                this.fetchChannelInfoDeferred = undefined;
                this.fetchChannelInfoState = "not_fetched";
                this.lastActivityTime = null;
                this.memberActivityMap = new Map();
                
                // 增强的成员分组
                this.onlineMembers = Record.many("ChannelMember", {
                    compute: () => this.computeOnlineMembers(),
                    sort: (m1, m2) => this.sortMembersEnhanced(m1, m2)
                });
                
                this.offlineMembers = Record.many("ChannelMember", {
                    compute: () => this.computeOfflineMembers(),
                    sort: (m1, m2) => this.sortMembersEnhanced(m1, m2)
                });
                
                this.recentlyActiveMembers = Record.many("ChannelMember", {
                    compute: () => this.computeRecentlyActiveMembers(),
                    sort: (m1, m2) => this.sortByActivity(m1, m2)
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置活动监控
                this.setupActivityMonitoring();
                
                // 设置缓存管理
                this.setupCacheManagement();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置事件监听
                this.setupEventListeners();
            },
            
            // 增强的在线成员计算
            computeOnlineMembers() {
                return this.channelMembers.filter(member => {
                    const status = member.persona?.im_status;
                    return this.store.onlineMemberStatuses.includes(status);
                });
            },
            
            // 增强的离线成员计算
            computeOfflineMembers() {
                return this.channelMembers.filter(member => {
                    const status = member.persona?.im_status;
                    return !this.store.onlineMemberStatuses.includes(status);
                });
            },
            
            // 计算最近活跃成员
            computeRecentlyActiveMembers() {
                const recentThreshold = Date.now() - (24 * 60 * 60 * 1000); // 24小时
                
                return this.channelMembers.filter(member => {
                    const lastActivity = this.memberActivityMap.get(member.id);
                    return lastActivity && lastActivity > recentThreshold;
                });
            },
            
            // 增强的成员排序
            sortMembersEnhanced(m1, m2) {
                // 首先按在线状态排序
                const status1 = m1.persona?.im_status;
                const status2 = m2.persona?.im_status;
                
                const statusPriority = {
                    'online': 0,
                    'away': 1,
                    'busy': 2,
                    'offline': 3
                };
                
                const priority1 = statusPriority[status1] ?? 3;
                const priority2 = statusPriority[status2] ?? 3;
                
                if (priority1 !== priority2) {
                    return priority1 - priority2;
                }
                
                // 然后按活动时间排序
                const activity1 = this.memberActivityMap.get(m1.id) || 0;
                const activity2 = this.memberActivityMap.get(m2.id) || 0;
                
                if (activity1 !== activity2) {
                    return activity2 - activity1; // 最近活跃的在前
                }
                
                // 最后使用原有排序
                return this.store.sortMembers(m1, m2);
            },
            
            // 按活动排序
            sortByActivity(m1, m2) {
                const activity1 = this.memberActivityMap.get(m1.id) || 0;
                const activity2 = this.memberActivityMap.get(m2.id) || 0;
                
                return activity2 - activity1;
            },
            
            // 增强的头像URL
            get avatarUrl() {
                const baseUrl = this.getBaseAvatarUrl();
                
                // 添加额外参数
                const params = new URLSearchParams();
                
                if (this.avatarCacheKey) {
                    params.set('unique', this.avatarCacheKey);
                }
                
                // 添加主题参数
                const theme = this.store.settings?.theme || 'light';
                params.set('theme', theme);
                
                // 添加大小参数
                const size = this.getAvatarSize();
                params.set('size', size);
                
                const queryString = params.toString();
                return queryString ? `${baseUrl}?${queryString}` : baseUrl;
            },
            
            // 获取基础头像URL
            getBaseAvatarUrl() {
                if (this.channel_type === "channel" || this.channel_type === "group") {
                    return imageUrl("discuss.channel", this.id, "avatar_128");
                }
                
                if (this.channel_type === "chat" && this.correspondent) {
                    return this.correspondent.persona.avatarUrl;
                }
                
                return super.avatarUrl;
            },
            
            // 获取头像大小
            getAvatarSize() {
                // 根据设备类型和用户偏好确定大小
                const isMobile = this.env.isSmall;
                const userPrefs = this.store.settings?.avatarSize || 'medium';
                
                const sizeMap = {
                    small: isMobile ? '32' : '48',
                    medium: isMobile ? '48' : '64',
                    large: isMobile ? '64' : '128'
                };
                
                return sizeMap[userPrefs] || sizeMap.medium;
            },
            
            // 增强的频道信息获取
            async fetchChannelInfo(force = false) {
                return this.fetchChannelMutex.exec(async () => {
                    // 检查是否需要获取
                    if (!force && this.fetchChannelInfoState === "fetched") {
                        return this;
                    }
                    
                    // 检查频道是否仍然存在
                    if (!(this.localId in this.store.Thread.records)) {
                        return;
                    }
                    
                    try {
                        this.fetchChannelInfoState = "fetching";
                        
                        const data = await rpc("/discuss/channel/info", {
                            channel_id: this.id,
                            include_members: true,
                            include_settings: true,
                            include_statistics: true
                        });
                        
                        if (data) {
                            // 更新数据
                            this.store.insert(data);
                            
                            // 更新获取状态
                            this.fetchChannelInfoState = "fetched";
                            
                            // 记录获取时间
                            this.lastInfoFetchTime = Date.now();
                            
                            return this;
                        } else {
                            // 频道不存在，删除本地记录
                            this.delete();
                            return undefined;
                        }
                    } catch (error) {
                        console.error('获取频道信息失败:', error);
                        this.fetchChannelInfoState = "error";
                        throw error;
                    }
                });
            },
            
            // 增强的附件加载
            async fetchMoreAttachments(limit = 30, options = {}) {
                return this.fetchAttachmentsMutex.exec(async () => {
                    if (this.isLoadingAttachments || this.areAttachmentsLoaded) {
                        return;
                    }
                    
                    const {
                        includePreview = false,
                        filterType = null,
                        sortBy = 'date'
                    } = options;
                    
                    this.isLoadingAttachments = true;
                    
                    try {
                        const params = {
                            channel_id: this.id,
                            limit,
                            include_preview: includePreview,
                            sort_by: sortBy
                        };
                        
                        // 添加分页参数
                        if (this.attachments.length > 0) {
                            params.before = Math.min(...this.attachments.map(({ id }) => id));
                        }
                        
                        // 添加过滤参数
                        if (filterType) {
                            params.filter_type = filterType;
                        }
                        
                        const data = await rpc("/discuss/channel/attachments", params);
                        
                        const { Attachment: attachments = [] } = this.store.insert(data);
                        
                        // 检查是否已加载完所有附件
                        if (attachments.length < limit) {
                            this.areAttachmentsLoaded = true;
                        }
                        
                        // 更新统计信息
                        this.updateAttachmentStatistics(attachments);
                        
                        return attachments;
                    } finally {
                        this.isLoadingAttachments = false;
                    }
                });
            },
            
            // 增强的成员获取
            async fetchChannelMembers(force = false) {
                return this.fetchMembersMutex.exec(async () => {
                    if (!force && this.fetchMembersState === "fetched") {
                        return;
                    }
                    
                    try {
                        this.fetchMembersState = "fetching";
                        
                        const data = await rpc("/discuss/channel/members", {
                            channel_id: this.id,
                            include_activity: true,
                            include_roles: true
                        });
                        
                        if (data) {
                            this.store.insert(data);
                            this.fetchMembersState = "fetched";
                            
                            // 更新成员活动信息
                            this.updateMemberActivity(data.member_activity || {});
                        }
                    } catch (error) {
                        console.error('获取频道成员失败:', error);
                        this.fetchMembersState = "error";
                        throw error;
                    }
                });
            },
            
            // 增强的消息发送
            async post(body, options = {}) {
                const {
                    attachments = [],
                    mentionedChannels = [],
                    mentionedPartners = [],
                    isCommand = false
                } = options;
                
                // 检查是否为命令
                if (this.model === "discuss.channel" && body.startsWith("/") && !isCommand) {
                    return this.handleCommand(body);
                }
                
                // 记录发送统计
                this.recordMessageStatistics('sent');
                
                // 调用父类方法
                return super.post(body, {
                    attachments,
                    mentionedChannels,
                    mentionedPartners
                });
            },
            
            // 处理命令
            async handleCommand(body) {
                const [firstWord, ...args] = body.substring(1).split(/\s+/);
                const command = commandRegistry.get(firstWord, false);
                
                if (!command) {
                    throw new Error(`未知命令: /${firstWord}`);
                }
                
                // 检查频道类型
                if (command.channel_types && !command.channel_types.includes(this.channel_type)) {
                    throw new Error(`命令 /${firstWord} 不适用于 ${this.channel_type} 类型的频道`);
                }
                
                // 检查权限
                if (command.permissions) {
                    const hasPermission = this.checkCommandPermission(command.permissions);
                    if (!hasPermission) {
                        throw new Error(`权限不足，无法执行命令 /${firstWord}`);
                    }
                }
                
                // 记录命令使用
                this.recordCommandUsage(firstWord, args);
                
                // 执行命令
                try {
                    await this.executeCommand(command, body);
                } catch (error) {
                    console.error('命令执行失败:', error);
                    throw error;
                }
            },
            
            // 检查命令权限
            checkCommandPermission(requiredPermissions) {
                const user = this.store.self;
                
                if (!user) {
                    return false;
                }
                
                // 内部用户有所有权限
                if (user.isInternalUser) {
                    return true;
                }
                
                // 检查频道成员权限
                const member = this.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                if (!member) {
                    return false;
                }
                
                const userRole = member.role || 'member';
                return requiredPermissions.includes(userRole);
            },
            
            // 设置活动监控
            setupActivityMonitoring() {
                // 监听消息事件
                this.env.bus.addEventListener('message_posted', (event) => {
                    if (event.detail.thread_id === this.id) {
                        const authorId = event.detail.author_id;
                        this.memberActivityMap.set(authorId, Date.now());
                    }
                });
            },
            
            // 更新成员活动
            updateMemberActivity(activityData) {
                Object.entries(activityData).forEach(([memberId, lastActivity]) => {
                    this.memberActivityMap.set(parseInt(memberId), new Date(lastActivity).getTime());
                });
            },
            
            // 更新附件统计
            updateAttachmentStatistics(attachments) {
                if (!this.attachmentStats) {
                    this.attachmentStats = {
                        totalCount: 0,
                        totalSize: 0,
                        typeDistribution: {}
                    };
                }
                
                attachments.forEach(attachment => {
                    this.attachmentStats.totalCount++;
                    this.attachmentStats.totalSize += attachment.file_size || 0;
                    
                    const type = this.getAttachmentType(attachment);
                    this.attachmentStats.typeDistribution[type] = 
                        (this.attachmentStats.typeDistribution[type] || 0) + 1;
                });
            },
            
            // 获取附件类型
            getAttachmentType(attachment) {
                const mimeType = attachment.mimetype || '';
                
                if (mimeType.startsWith('image/')) return 'image';
                if (mimeType.startsWith('video/')) return 'video';
                if (mimeType.startsWith('audio/')) return 'audio';
                if (mimeType.includes('pdf')) return 'pdf';
                if (mimeType.includes('document') || mimeType.includes('word')) return 'document';
                
                return 'other';
            },
            
            // 记录消息统计
            recordMessageStatistics(type) {
                if (!this.messageStats) {
                    this.messageStats = {
                        sent: 0,
                        received: 0,
                        lastActivity: null
                    };
                }
                
                this.messageStats[type]++;
                this.messageStats.lastActivity = Date.now();
            },
            
            // 记录命令使用
            recordCommandUsage(command, args) {
                try {
                    const usage = JSON.parse(
                        localStorage.getItem('command_usage_stats') || '{}'
                    );
                    
                    const key = `${this.id}_${command}`;
                    
                    if (!usage[key]) {
                        usage[key] = { count: 0, lastUsed: null, args: [] };
                    }
                    
                    usage[key].count++;
                    usage[key].lastUsed = Date.now();
                    usage[key].args.push(args);
                    
                    // 保留最近10次参数
                    if (usage[key].args.length > 10) {
                        usage[key].args.shift();
                    }
                    
                    localStorage.setItem('command_usage_stats', JSON.stringify(usage));
                } catch (error) {
                    console.warn('记录命令使用统计失败:', error);
                }
            },
            
            // 获取线程统计
            getThreadStatistics() {
                return {
                    members: {
                        total: this.channelMembers?.length || 0,
                        online: this.onlineMembers?.length || 0,
                        offline: this.offlineMembers?.length || 0,
                        recentlyActive: this.recentlyActiveMembers?.length || 0
                    },
                    attachments: this.attachmentStats || {},
                    messages: this.messageStats || {},
                    lastActivity: this.lastActivityTime,
                    fetchStates: {
                        info: this.fetchChannelInfoState,
                        members: this.fetchMembersState,
                        attachments: this.areAttachmentsLoaded ? 'loaded' : 'partial'
                    }
                };
            }
        };
        
        patch(Thread.prototype, EnhancedThreadPatch);
    }
};

// 应用线程模型增强
ThreadModelEnhancer.enhanceThreadModel();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 并发控制
- Mutex互斥锁机制
- 防止重复请求
- 资源竞争保护

### 3. 响应式计算
- 自动更新的计算属性
- 智能成员分组
- 实时状态同步

### 4. 数据管理
- 分页加载机制
- 缓存控制策略
- 状态管理优化

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 观察者模式 (Observer Pattern)
- 响应式数据计算
- 状态变化监听

### 3. 策略模式 (Strategy Pattern)
- 不同类型的处理策略
- 可配置的行为模式

## 注意事项

1. **并发安全**: 确保并发操作的安全性
2. **性能优化**: 避免频繁的计算和网络请求
3. **内存管理**: 及时清理缓存和监听器
4. **数据一致性**: 保证数据更新的一致性

## 扩展建议

1. **缓存策略**: 实现更智能的数据缓存策略
2. **性能监控**: 添加详细的性能监控功能
3. **错误恢复**: 增强错误处理和恢复机制
4. **批量操作**: 支持批量数据操作
5. **实时同步**: 实现更好的实时数据同步

该补丁为讨论应用的线程模型提供了全面的功能增强，是整个讨论应用的核心数据管理组件。

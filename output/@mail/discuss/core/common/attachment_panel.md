# Attachment Panel - 附件面板

## 概述

`attachment_panel.js` 实现了 Odoo 讨论应用中的附件面板组件，用于显示和管理线程中的附件。该组件支持附件列表显示、按日期分组、懒加载、公共上传权限控制等功能，集成了操作面板、附件列表、日期分组等子组件，提供了完整的附件管理界面，是讨论应用中附件功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/attachment_panel.js`
- **行数**: 77
- **模块**: `@mail/discuss/core/common/attachment_panel`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/date_section'           // 日期分组组件
'@mail/discuss/core/common/action_panel'   // 操作面板组件
'@mail/core/common/attachment_list'        // 附件列表组件

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/utils/hooks'                    // Web 核心钩子
'@mail/utils/common/hooks'                 // 邮件工具钩子
```

## 组件定义

### AttachmentPanel 类

```javascript
const AttachmentPanel = class AttachmentPanel extends Component {
    static components = { ActionPanel, AttachmentList, DateSection };
    static props = ["thread"];
    static template = "mail.AttachmentPanel";
}
```

**组件特性**:
- 继承自Component基类
- 集成多个子组件
- 接收线程属性
- 使用专用模板

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.sequential = useSequential();
    this.store = useService("mail.store");
    this.ormService = useService("orm");
    this.attachmentUploadService = useService("mail.attachment_upload");
    
    // 生命周期钩子
    onWillStart(() => {
        this.props.thread.fetchMoreAttachments();
    });
    
    onWillUpdateProps((nextProps) => {
        if (nextProps.thread.notEq(this.props.thread)) {
            nextProps.thread.fetchMoreAttachments();
        }
    });
    
    // 可见性监听
    useVisible("load-older", (isVisible) => {
        if (isVisible) {
            this.props.thread.fetchMoreAttachments();
        }
    });
}
```

**初始化内容**:
- 顺序执行工具
- 邮件存储服务
- ORM服务
- 附件上传服务
- 生命周期管理
- 懒加载支持

## 核心功能

### 1. 附件按日期分组

```javascript
get attachmentsByDate() {
    const attachmentsByDate = {};
    for (const attachment of this.props.thread.attachments) {
        const attachments = attachmentsByDate[attachment.monthYear] ?? [];
        attachments.push(attachment);
        attachmentsByDate[attachment.monthYear] = attachments;
    }
    return attachmentsByDate;
}
```

**分组功能**:
- **日期分组**: 按月年分组附件
- **动态分组**: 基于附件的monthYear属性
- **数据结构**: 返回分组后的附件对象

### 2. 公共上传权限控制

```javascript
get hasToggleAllowPublicUpload() {
    return (
        this.props.thread.model !== "mail.box" &&
        this.props.thread.channel_type !== "chat" &&
        this.store.self.isInternalUser
    );
}
```

**权限检查**:
- **模型检查**: 排除邮箱模型
- **类型检查**: 排除私聊类型
- **用户检查**: 仅内部用户可操作

### 3. 切换公共上传权限

```javascript
toggleAllowPublicUpload() {
    this.sequential(() =>
        this.ormService.write("discuss.channel", [this.props.thread.id], {
            allow_public_upload: !this.props.thread.allow_public_upload,
        })
    );
}
```

**切换功能**:
- **顺序执行**: 使用sequential确保操作顺序
- **ORM写入**: 通过ORM服务更新数据库
- **状态切换**: 切换公共上传权限状态

## 使用场景

### 1. 附件管理增强

```javascript
// 附件管理增强功能
const AttachmentPanelEnhancer = {
    enhanceAttachmentPanel: () => {
        const EnhancedAttachmentPanel = class extends AttachmentPanel {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.panelState = useState({
                    viewMode: 'grid', // grid, list, timeline
                    sortBy: 'date', // date, name, size, type
                    sortOrder: 'desc', // asc, desc
                    filterType: 'all', // all, images, documents, videos
                    searchQuery: '',
                    selectedAttachments: new Set(),
                    isSelectionMode: false
                });
                
                // 附件统计
                this.attachmentStats = useState({
                    totalCount: 0,
                    totalSize: 0,
                    typeDistribution: {},
                    uploadTrend: []
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
                
                // 设置快捷键
                this.setupKeyboardShortcuts();
                
                // 设置拖拽上传
                this.setupDragAndDrop();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 监听附件变化
                useEffect(() => {
                    this.updateAttachmentStats();
                }, () => [this.props.thread.attachments]);
                
                // 设置搜索防抖
                this.searchDebounced = debounce(this.performSearch.bind(this), 300);
                
                // 设置批量操作
                this.setupBatchOperations();
            },
            
            // 更新附件统计
            updateAttachmentStats() {
                const attachments = this.props.thread.attachments;
                
                this.attachmentStats.totalCount = attachments.length;
                this.attachmentStats.totalSize = attachments.reduce(
                    (sum, att) => sum + (att.file_size || 0), 0
                );
                
                // 类型分布统计
                const typeDistribution = {};
                attachments.forEach(att => {
                    const type = this.getAttachmentCategory(att);
                    typeDistribution[type] = (typeDistribution[type] || 0) + 1;
                });
                this.attachmentStats.typeDistribution = typeDistribution;
                
                // 上传趋势分析
                this.updateUploadTrend(attachments);
            },
            
            // 获取附件类别
            getAttachmentCategory(attachment) {
                const mimeType = attachment.mimetype || '';
                
                if (mimeType.startsWith('image/')) return 'images';
                if (mimeType.startsWith('video/')) return 'videos';
                if (mimeType.startsWith('audio/')) return 'audio';
                if (mimeType.includes('pdf')) return 'pdf';
                if (mimeType.includes('word') || mimeType.includes('document')) return 'documents';
                if (mimeType.includes('sheet') || mimeType.includes('excel')) return 'spreadsheets';
                if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return 'presentations';
                
                return 'others';
            },
            
            // 更新上传趋势
            updateUploadTrend(attachments) {
                const trend = {};
                const now = new Date();
                
                // 最近30天的趋势
                for (let i = 29; i >= 0; i--) {
                    const date = new Date(now);
                    date.setDate(date.getDate() - i);
                    const dateKey = date.toISOString().split('T')[0];
                    trend[dateKey] = 0;
                }
                
                attachments.forEach(att => {
                    if (att.create_date) {
                        const dateKey = att.create_date.split('T')[0];
                        if (trend.hasOwnProperty(dateKey)) {
                            trend[dateKey]++;
                        }
                    }
                });
                
                this.attachmentStats.uploadTrend = Object.entries(trend).map(([date, count]) => ({
                    date, count
                }));
            },
            
            // 增强的附件按日期分组
            get attachmentsByDate() {
                let attachments = this.getFilteredAttachments();
                attachments = this.getSortedAttachments(attachments);
                
                const attachmentsByDate = {};
                for (const attachment of attachments) {
                    const key = this.getGroupingKey(attachment);
                    const attachments = attachmentsByDate[key] ?? [];
                    attachments.push(attachment);
                    attachmentsByDate[key] = attachments;
                }
                return attachmentsByDate;
            },
            
            // 获取过滤后的附件
            getFilteredAttachments() {
                let attachments = [...this.props.thread.attachments];
                
                // 类型过滤
                if (this.panelState.filterType !== 'all') {
                    attachments = attachments.filter(att => 
                        this.getAttachmentCategory(att) === this.panelState.filterType
                    );
                }
                
                // 搜索过滤
                if (this.panelState.searchQuery) {
                    const query = this.panelState.searchQuery.toLowerCase();
                    attachments = attachments.filter(att =>
                        att.name.toLowerCase().includes(query) ||
                        (att.description && att.description.toLowerCase().includes(query))
                    );
                }
                
                return attachments;
            },
            
            // 获取排序后的附件
            getSortedAttachments(attachments) {
                const { sortBy, sortOrder } = this.panelState;
                
                return attachments.sort((a, b) => {
                    let comparison = 0;
                    
                    switch (sortBy) {
                        case 'date':
                            comparison = new Date(a.create_date) - new Date(b.create_date);
                            break;
                        case 'name':
                            comparison = a.name.localeCompare(b.name);
                            break;
                        case 'size':
                            comparison = (a.file_size || 0) - (b.file_size || 0);
                            break;
                        case 'type':
                            comparison = (a.mimetype || '').localeCompare(b.mimetype || '');
                            break;
                    }
                    
                    return sortOrder === 'desc' ? -comparison : comparison;
                });
            },
            
            // 获取分组键
            getGroupingKey(attachment) {
                switch (this.panelState.viewMode) {
                    case 'timeline':
                        return attachment.monthYear;
                    case 'type':
                        return this.getAttachmentCategory(attachment);
                    default:
                        return attachment.monthYear;
                }
            },
            
            // 设置批量操作
            setupBatchOperations() {
                this.batchOperations = {
                    selectAll: () => {
                        const attachments = this.getFilteredAttachments();
                        this.panelState.selectedAttachments = new Set(
                            attachments.map(att => att.id)
                        );
                    },
                    
                    selectNone: () => {
                        this.panelState.selectedAttachments.clear();
                    },
                    
                    deleteSelected: async () => {
                        const selectedIds = Array.from(this.panelState.selectedAttachments);
                        if (selectedIds.length === 0) return;
                        
                        const confirmed = confirm(
                            `确定要删除 ${selectedIds.length} 个附件吗？`
                        );
                        
                        if (confirmed) {
                            await this.deleteAttachments(selectedIds);
                            this.panelState.selectedAttachments.clear();
                        }
                    },
                    
                    downloadSelected: async () => {
                        const selectedIds = Array.from(this.panelState.selectedAttachments);
                        if (selectedIds.length === 0) return;
                        
                        await this.downloadAttachments(selectedIds);
                    }
                };
            },
            
            // 删除附件
            async deleteAttachments(attachmentIds) {
                try {
                    await this.ormService.unlink("ir.attachment", attachmentIds);
                    
                    this.env.services.notification.add(
                        `已删除 ${attachmentIds.length} 个附件`,
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('删除附件失败:', error);
                    this.env.services.notification.add(
                        '删除附件失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 下载附件
            async downloadAttachments(attachmentIds) {
                if (attachmentIds.length === 1) {
                    // 单个附件直接下载
                    const attachment = this.props.thread.attachments.find(
                        att => att.id === attachmentIds[0]
                    );
                    if (attachment) {
                        window.open(attachment.downloadUrl, '_blank');
                    }
                } else {
                    // 多个附件打包下载
                    try {
                        const response = await this.ormService.call(
                            "ir.attachment",
                            "create_zip_archive",
                            [attachmentIds]
                        );
                        
                        if (response.download_url) {
                            window.open(response.download_url, '_blank');
                        }
                    } catch (error) {
                        console.error('打包下载失败:', error);
                        this.env.services.notification.add(
                            '打包下载失败',
                            { type: 'error' }
                        );
                    }
                }
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
            },
            
            // 处理键盘快捷键
            handleKeyboardShortcut(event) {
                if (!this.el.contains(document.activeElement)) return;
                
                // Ctrl+A: 全选
                if (event.ctrlKey && event.key === 'a') {
                    event.preventDefault();
                    this.batchOperations.selectAll();
                }
                
                // Delete: 删除选中
                if (event.key === 'Delete' && this.panelState.selectedAttachments.size > 0) {
                    event.preventDefault();
                    this.batchOperations.deleteSelected();
                }
                
                // Escape: 取消选择
                if (event.key === 'Escape') {
                    this.batchOperations.selectNone();
                    this.panelState.isSelectionMode = false;
                }
            },
            
            // 设置拖拽上传
            setupDragAndDrop() {
                onMounted(() => {
                    this.setupDropZone();
                });
            },
            
            // 设置拖放区域
            setupDropZone() {
                const dropZone = this.el;
                
                dropZone.addEventListener('dragover', (event) => {
                    event.preventDefault();
                    dropZone.classList.add('drag-over');
                });
                
                dropZone.addEventListener('dragleave', (event) => {
                    if (!dropZone.contains(event.relatedTarget)) {
                        dropZone.classList.remove('drag-over');
                    }
                });
                
                dropZone.addEventListener('drop', (event) => {
                    event.preventDefault();
                    dropZone.classList.remove('drag-over');
                    
                    const files = Array.from(event.dataTransfer.files);
                    if (files.length > 0) {
                        this.handleFileUpload(files);
                    }
                });
            },
            
            // 处理文件上传
            async handleFileUpload(files) {
                try {
                    for (const file of files) {
                        await this.attachmentUploadService.uploadFile(
                            file,
                            this.props.thread
                        );
                    }
                    
                    this.env.services.notification.add(
                        `已上传 ${files.length} 个文件`,
                        { type: 'success' }
                    );
                } catch (error) {
                    console.error('文件上传失败:', error);
                    this.env.services.notification.add(
                        '文件上传失败',
                        { type: 'error' }
                    );
                }
            },
            
            // 执行搜索
            performSearch(query) {
                this.panelState.searchQuery = query;
            },
            
            // 切换视图模式
            setViewMode(mode) {
                this.panelState.viewMode = mode;
            },
            
            // 设置排序
            setSorting(sortBy, sortOrder) {
                this.panelState.sortBy = sortBy;
                this.panelState.sortOrder = sortOrder;
            },
            
            // 设置过滤器
            setFilter(filterType) {
                this.panelState.filterType = filterType;
            },
            
            // 切换选择模式
            toggleSelectionMode() {
                this.panelState.isSelectionMode = !this.panelState.isSelectionMode;
                if (!this.panelState.isSelectionMode) {
                    this.panelState.selectedAttachments.clear();
                }
            },
            
            // 切换附件选择
            toggleAttachmentSelection(attachmentId) {
                if (this.panelState.selectedAttachments.has(attachmentId)) {
                    this.panelState.selectedAttachments.delete(attachmentId);
                } else {
                    this.panelState.selectedAttachments.add(attachmentId);
                }
            },
            
            // 获取面板统计信息
            getPanelStats() {
                return {
                    ...this.attachmentStats,
                    filteredCount: this.getFilteredAttachments().length,
                    selectedCount: this.panelState.selectedAttachments.size
                };
            }
        };
        
        // 替换原始组件
        __exports.AttachmentPanel = EnhancedAttachmentPanel;
    }
};

// 应用附件面板增强
AttachmentPanelEnhancer.enhanceAttachmentPanel();
```

## 技术特点

### 1. 组件架构
- 基于OWL框架的组件设计
- 多子组件集成
- 清晰的属性接口

### 2. 数据管理
- 响应式数据绑定
- 懒加载支持
- 按日期分组显示

### 3. 权限控制
- 基于用户类型的权限检查
- 线程类型的权限限制
- 动态权限切换

### 4. 用户体验
- 可见性监听的懒加载
- 顺序执行的操作保证
- 生命周期的数据管理

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的附件面板组件
- 清晰的组件层次结构

### 2. 观察者模式 (Observer Pattern)
- 属性变化的监听
- 可见性变化的响应

### 3. 策略模式 (Strategy Pattern)
- 不同权限的处理策略
- 可配置的行为模式

## 注意事项

1. **性能优化**: 大量附件时的渲染性能
2. **内存管理**: 及时清理事件监听器
3. **权限安全**: 确保权限检查的正确性
4. **用户体验**: 提供清晰的加载状态反馈

## 扩展建议

1. **搜索功能**: 添加附件搜索和过滤功能
2. **批量操作**: 支持附件的批量管理
3. **预览功能**: 集成附件预览功能
4. **拖拽上传**: 支持拖拽文件上传
5. **统计分析**: 添加附件使用统计分析

该组件为讨论应用提供了完整的附件管理界面，是附件功能的重要用户界面组件。

# Channel Invitation - 频道邀请

## 概述

`channel_invitation.js` 实现了 Odoo 讨论应用中的频道邀请组件，用于邀请用户加入频道或创建群聊。该组件支持用户搜索、伙伴选择、邀请链接复制、不同频道类型的邀请处理等功能，集成了在线状态、操作面板、建议服务等多个子系统，提供了完整的邀请用户界面，是讨论应用中用户邀请功能的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/channel_invitation.js`
- **行数**: 142
- **模块**: `@mail/discuss/core/common/channel_invitation`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/im_status'              // 在线状态组件
'@mail/discuss/core/common/action_panel'   // 操作面板组件

// 核心依赖
'@odoo/owl'                                // OWL 框架
'@web/core/l10n/translation'               // 国际化
'@web/core/utils/hooks'                    // Web 核心钩子
'@mail/utils/common/hooks'                 // 邮件工具钩子
```

## 组件定义

### ChannelInvitation 类

```javascript
const ChannelInvitation = class ChannelInvitation extends Component {
    static components = { ImStatus, ActionPanel };
    static defaultProps = { hasSizeConstraints: false };
    static props = ["hasSizeConstraints?", "thread", "close", "className?"];
    static template = "discuss.ChannelInvitation";
}
```

**组件特性**:
- 继承自Component基类
- 集成在线状态和操作面板组件
- 支持尺寸约束配置
- 接收线程、关闭回调等属性

## 组件设置

### setup() 方法

```javascript
setup() {
    super.setup();
    this.discussCoreCommonService = useState(useService("discuss.core.common"));
    this.orm = useService("orm");
    this.store = useState(useService("mail.store"));
    this.notification = useService("notification");
    this.suggestionService = useService("mail.suggestion");
    this.ui = useService("ui");
    this.inputRef = useRef("input");
    this.sequential = useSequential();
    this.searchStr = "";
    this.state = useState({
        selectablePartners: [],
        selectedPartners: [],
        searchResultCount: 0,
    });
}
```

**初始化内容**:
- 多服务集成
- 输入框引用
- 顺序执行工具
- 搜索状态管理
- 伙伴选择状态

## 核心功能

### 1. 获取可邀请伙伴

```javascript
async fetchPartnersToInvite() {
    const results = await this.sequential(() =>
        this.orm.call("res.partner", "search_for_channel_invite", [
            this.searchStr,
            this.props.thread.id,
        ])
    );
    if (!results) {
        return;
    }
    const { Persona: selectablePartners = [] } = this.store.insert(results.data);
    this.state.selectablePartners = this.suggestionService.sortPartnerSuggestions(
        selectablePartners,
        this.searchStr,
        this.props.thread
    );
    this.state.searchResultCount = results["count"];
}
```

**搜索功能**:
- **ORM调用**: 通过ORM服务搜索伙伴
- **顺序执行**: 确保搜索请求的顺序性
- **数据插入**: 将结果插入存储
- **智能排序**: 使用建议服务排序结果

### 2. 伙伴选择管理

```javascript
onClickSelectablePartner(partner) {
    if (partner.in(this.state.selectedPartners)) {
        const index = this.state.selectedPartners.indexOf(partner);
        if (index !== -1) {
            this.state.selectedPartners.splice(index, 1);
        }
        return;
    }
    this.state.selectedPartners.push(partner);
}

onClickSelectedPartner(partner) {
    const index = this.state.selectedPartners.indexOf(partner);
    this.state.selectedPartners.splice(index, 1);
}
```

**选择功能**:
- **切换选择**: 点击可选伙伴进行选择/取消选择
- **移除选择**: 点击已选伙伴进行移除
- **状态更新**: 动态更新选择状态

### 3. 邀请链接处理

```javascript
onFocusInvitationLinkInput(ev) {
    ev.target.select();
}

async onClickCopy(ev) {
    await navigator.clipboard.writeText(this.props.thread.invitationLink);
    this.notification.add(_t("Link copied!"), { type: "success" });
}
```

**链接功能**:
- **自动选择**: 聚焦时自动选择链接文本
- **复制功能**: 使用剪贴板API复制链接
- **成功通知**: 显示复制成功通知

### 4. 邀请执行

```javascript
async onClickInvite() {
    if (this.props.thread.channel_type === "chat") {
        const partnerIds = this.state.selectedPartners.map((partner) => partner.id);
        if (this.props.thread.correspondent) {
            partnerIds.unshift(this.props.thread.correspondent.persona.id);
        }
        await this.discussCoreCommonService.startChat(partnerIds);
    } else {
        await this.orm.call("discuss.channel", "add_members", [[this.props.thread.id]], {
            partner_ids: this.state.selectedPartners.map((partner) => partner.id),
        });
    }
    this.props.close();
}
```

**邀请逻辑**:
- **类型判断**: 根据频道类型执行不同逻辑
- **聊天处理**: 私聊类型启动新聊天
- **频道处理**: 频道类型添加成员
- **自动关闭**: 邀请完成后关闭界面

### 5. 动态按钮文本

```javascript
get invitationButtonText() {
    if (this.props.thread.channel_type === "channel") {
        return _t("Invite to Channel");
    } else if (this.props.thread.channel_type === "group") {
        return _t("Invite to Group Chat");
    } else if (this.props.thread.channel_type === "chat") {
        if (this.props.thread.correspondent?.persona.eq(this.store.self)) {
            if (this.state.selectedPartners.length === 0) {
                return _t("Invite");
            }
            if (this.state.selectedPartners.length === 1) {
                const alreadyChat = Object.values(this.store.Thread.records).some((thread) =>
                    thread.correspondent?.persona.eq(this.state.selectedPartners[0])
                );
                if (alreadyChat) {
                    return _t("Go to conversation");
                }
                return _t("Start a Conversation");
            }
        }
        return _t("Create Group Chat");
    }
    return _t("Invite");
}
```

**文本逻辑**:
- **频道类型**: 根据不同频道类型显示相应文本
- **选择数量**: 根据选择的伙伴数量调整文本
- **已有聊天**: 检查是否已存在聊天并调整文本
- **国际化**: 所有文本都支持国际化

## 使用场景

### 1. 邀请功能增强

```javascript
// 邀请功能增强
const ChannelInvitationEnhancer = {
    enhanceChannelInvitation: () => {
        const EnhancedChannelInvitation = class extends ChannelInvitation {
            setup() {
                super.setup();
                
                // 增强状态管理
                this.enhancedState = useState({
                    inviteMode: 'search', // search, link, bulk
                    recentContacts: [],
                    favoriteContacts: [],
                    inviteHistory: [],
                    isLoading: false,
                    bulkInviteText: '',
                    inviteMessage: '',
                    sendWelcomeMessage: true
                });
                
                // 邀请统计
                this.inviteStats = useState({
                    totalInvites: 0,
                    successfulInvites: 0,
                    failedInvites: 0,
                    lastInviteTime: null
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
                
                // 加载历史数据
                this.loadInviteHistory();
                this.loadRecentContacts();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 搜索防抖
                this.searchDebounced = debounce(this.fetchPartnersToInvite.bind(this), 300);
                
                // 设置快捷键
                this.setupKeyboardShortcuts();
                
                // 设置批量邀请
                this.setupBulkInvite();
                
                // 设置邀请验证
                this.setupInviteValidation();
            },
            
            // 增强的搜索输入处理
            onInput() {
                this.searchStr = this.inputRef.el.value;
                this.searchDebounced();
            },
            
            // 增强的获取可邀请伙伴
            async fetchPartnersToInvite() {
                this.enhancedState.isLoading = true;
                
                try {
                    // 调用原始方法
                    await super.fetchPartnersToInvite();
                    
                    // 添加最近联系人和收藏联系人
                    this.addRecentAndFavoriteContacts();
                    
                } catch (error) {
                    console.error('搜索伙伴失败:', error);
                    this.notification.add('搜索失败，请重试', { type: 'error' });
                } finally {
                    this.enhancedState.isLoading = false;
                }
            },
            
            // 添加最近联系人和收藏联系人
            addRecentAndFavoriteContacts() {
                if (this.searchStr.length === 0) {
                    // 无搜索时显示最近联系人和收藏联系人
                    const recentPartners = this.enhancedState.recentContacts.filter(
                        partner => !this.state.selectablePartners.some(p => p.id === partner.id)
                    );
                    
                    const favoritePartners = this.enhancedState.favoriteContacts.filter(
                        partner => !this.state.selectablePartners.some(p => p.id === partner.id) &&
                                  !recentPartners.some(p => p.id === partner.id)
                    );
                    
                    this.state.selectablePartners = [
                        ...favoritePartners,
                        ...recentPartners,
                        ...this.state.selectablePartners
                    ];
                }
            },
            
            // 增强的邀请执行
            async onClickInvite() {
                if (this.state.selectedPartners.length === 0) {
                    this.notification.add('请选择要邀请的用户', { type: 'warning' });
                    return;
                }
                
                this.enhancedState.isLoading = true;
                
                try {
                    // 验证邀请
                    const validation = await this.validateInvite();
                    if (!validation.valid) {
                        this.notification.add(validation.message, { type: 'warning' });
                        return;
                    }
                    
                    // 记录邀请开始
                    this.recordInviteStart();
                    
                    // 执行邀请
                    await super.onClickInvite();
                    
                    // 发送欢迎消息
                    if (this.enhancedState.sendWelcomeMessage && this.enhancedState.inviteMessage) {
                        await this.sendWelcomeMessage();
                    }
                    
                    // 记录邀请成功
                    this.recordInviteSuccess();
                    
                    // 更新最近联系人
                    this.updateRecentContacts();
                    
                } catch (error) {
                    console.error('邀请失败:', error);
                    this.recordInviteFailure(error);
                    this.notification.add('邀请失败，请重试', { type: 'error' });
                } finally {
                    this.enhancedState.isLoading = false;
                }
            },
            
            // 验证邀请
            async validateInvite() {
                // 检查邀请权限
                if (!this.hasInvitePermission()) {
                    return { valid: false, message: '您没有邀请权限' };
                }
                
                // 检查邀请限制
                const limit = await this.getInviteLimit();
                if (this.state.selectedPartners.length > limit) {
                    return { valid: false, message: `一次最多只能邀请 ${limit} 个用户` };
                }
                
                // 检查重复邀请
                const duplicates = await this.checkDuplicateInvites();
                if (duplicates.length > 0) {
                    const names = duplicates.map(p => p.name).join(', ');
                    return { valid: false, message: `以下用户已在频道中: ${names}` };
                }
                
                return { valid: true };
            },
            
            // 检查邀请权限
            hasInvitePermission() {
                const thread = this.props.thread;
                const user = this.store.self;
                
                if (thread.channel_type === 'channel') {
                    // 检查频道邀请权限
                    const member = thread.channelMembers?.find(m => m.persona.id === user.id);
                    return member && (member.role === 'admin' || member.role === 'moderator' || thread.allowMemberInvite);
                }
                
                return true; // 其他类型默认允许
            },
            
            // 获取邀请限制
            async getInviteLimit() {
                try {
                    const result = await this.orm.call('discuss.channel', 'get_invite_limit', [this.props.thread.id]);
                    return result.limit || 10;
                } catch (error) {
                    return 10; // 默认限制
                }
            },
            
            // 检查重复邀请
            async checkDuplicateInvites() {
                const thread = this.props.thread;
                const selectedIds = this.state.selectedPartners.map(p => p.id);
                const existingMemberIds = thread.channelMembers?.map(m => m.persona.id) || [];
                
                return this.state.selectedPartners.filter(partner => 
                    existingMemberIds.includes(partner.id)
                );
            },
            
            // 发送欢迎消息
            async sendWelcomeMessage() {
                if (!this.enhancedState.inviteMessage.trim()) return;
                
                try {
                    await this.orm.call('mail.thread', 'message_post', [this.props.thread.id], {
                        body: this.enhancedState.inviteMessage,
                        message_type: 'comment',
                        subtype_xmlid: 'mail.mt_comment'
                    });
                } catch (error) {
                    console.error('发送欢迎消息失败:', error);
                }
            },
            
            // 设置批量邀请
            setupBulkInvite() {
                this.bulkInviteProcessor = {
                    parseEmails: (text) => {
                        const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
                        return text.match(emailRegex) || [];
                    },
                    
                    validateEmails: (emails) => {
                        const valid = [];
                        const invalid = [];
                        
                        emails.forEach(email => {
                            if (this.isValidEmail(email)) {
                                valid.push(email);
                            } else {
                                invalid.push(email);
                            }
                        });
                        
                        return { valid, invalid };
                    }
                };
            },
            
            // 处理批量邀请
            async processBulkInvite() {
                const emails = this.bulkInviteProcessor.parseEmails(this.enhancedState.bulkInviteText);
                
                if (emails.length === 0) {
                    this.notification.add('请输入有效的邮箱地址', { type: 'warning' });
                    return;
                }
                
                const { valid, invalid } = this.bulkInviteProcessor.validateEmails(emails);
                
                if (invalid.length > 0) {
                    this.notification.add(`以下邮箱格式无效: ${invalid.join(', ')}`, { type: 'warning' });
                }
                
                if (valid.length === 0) {
                    return;
                }
                
                try {
                    await this.orm.call('discuss.channel', 'invite_by_emails', [this.props.thread.id], {
                        emails: valid,
                        message: this.enhancedState.inviteMessage
                    });
                    
                    this.notification.add(`已发送 ${valid.length} 个邀请`, { type: 'success' });
                    this.props.close();
                    
                } catch (error) {
                    console.error('批量邀请失败:', error);
                    this.notification.add('批量邀请失败', { type: 'error' });
                }
            },
            
            // 验证邮箱格式
            isValidEmail(email) {
                const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                return emailRegex.test(email);
            },
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                onMounted(() => {
                    document.addEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.handleKeyboardShortcut.bind(this));
                });
            },
            
            // 处理键盘快捷键
            handleKeyboardShortcut(event) {
                if (!this.el.contains(document.activeElement)) return;
                
                // Enter: 发送邀请
                if (event.key === 'Enter' && event.ctrlKey) {
                    event.preventDefault();
                    this.onClickInvite();
                }
                
                // Escape: 关闭
                if (event.key === 'Escape') {
                    event.preventDefault();
                    this.props.close();
                }
            },
            
            // 记录邀请统计
            recordInviteStart() {
                this.inviteStats.totalInvites++;
            },
            
            recordInviteSuccess() {
                this.inviteStats.successfulInvites++;
                this.inviteStats.lastInviteTime = Date.now();
                this.saveInviteStats();
            },
            
            recordInviteFailure(error) {
                this.inviteStats.failedInvites++;
                this.saveInviteStats();
            },
            
            // 保存邀请统计
            saveInviteStats() {
                try {
                    localStorage.setItem('channel_invite_stats', JSON.stringify(this.inviteStats));
                } catch (error) {
                    console.warn('保存邀请统计失败:', error);
                }
            },
            
            // 加载邀请历史
            loadInviteHistory() {
                try {
                    const saved = localStorage.getItem('channel_invite_history');
                    if (saved) {
                        this.enhancedState.inviteHistory = JSON.parse(saved);
                    }
                } catch (error) {
                    console.warn('加载邀请历史失败:', error);
                }
            },
            
            // 加载最近联系人
            loadRecentContacts() {
                try {
                    const saved = localStorage.getItem('recent_contacts');
                    if (saved) {
                        this.enhancedState.recentContacts = JSON.parse(saved);
                    }
                } catch (error) {
                    console.warn('加载最近联系人失败:', error);
                }
            },
            
            // 更新最近联系人
            updateRecentContacts() {
                const newContacts = this.state.selectedPartners.filter(partner =>
                    !this.enhancedState.recentContacts.some(recent => recent.id === partner.id)
                );
                
                this.enhancedState.recentContacts = [
                    ...newContacts,
                    ...this.enhancedState.recentContacts
                ].slice(0, 10); // 保留最近10个
                
                try {
                    localStorage.setItem('recent_contacts', JSON.stringify(this.enhancedState.recentContacts));
                } catch (error) {
                    console.warn('保存最近联系人失败:', error);
                }
            },
            
            // 切换邀请模式
            setInviteMode(mode) {
                this.enhancedState.inviteMode = mode;
            },
            
            // 获取邀请统计
            getInviteStats() {
                return {
                    ...this.inviteStats,
                    successRate: this.inviteStats.totalInvites > 0 
                        ? (this.inviteStats.successfulInvites / this.inviteStats.totalInvites * 100).toFixed(2) + '%'
                        : '0%'
                };
            }
        };
        
        // 替换原始组件
        __exports.ChannelInvitation = EnhancedChannelInvitation;
    }
};

// 应用频道邀请增强
ChannelInvitationEnhancer.enhanceChannelInvitation();
```

## 技术特点

### 1. 组件架构
- 基于OWL框架的组件设计
- 多服务集成
- 响应式状态管理

### 2. 搜索功能
- 实时搜索伙伴
- 智能排序建议
- 顺序执行保证

### 3. 多类型支持
- 频道邀请
- 群聊创建
- 私聊启动

### 4. 用户体验
- 动态按钮文本
- 链接复制功能
- 自动聚焦输入

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的邀请组件
- 清晰的属性接口

### 2. 策略模式 (Strategy Pattern)
- 不同频道类型的处理策略
- 可配置的邀请行为

### 3. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 搜索结果的响应

## 注意事项

1. **性能优化**: 搜索请求的防抖处理
2. **用户体验**: 提供清晰的加载状态
3. **权限控制**: 确保邀请权限的正确性
4. **错误处理**: 完善的异常处理机制

## 扩展建议

1. **批量邀请**: 支持批量邀请多个用户
2. **邀请历史**: 记录和管理邀请历史
3. **权限验证**: 增强邀请权限验证
4. **自定义消息**: 支持自定义邀请消息
5. **统计分析**: 添加邀请使用统计分析

该组件为讨论应用提供了完整的用户邀请功能，是用户管理的重要界面组件。

# Partner Compare - 伙伴比较

## 概述

`partner_compare.js` 实现了 Odoo 讨论应用中的伙伴比较规则注册，定义了用于排序和比较伙伴对象的特定规则。该文件通过伙伴比较注册表添加了两个核心比较规则：最近聊天排序和频道成员优先级排序，这些规则用于在用户界面中智能排序伙伴列表，提供更好的用户体验，是讨论应用中伙伴排序系统的重要组成部分。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/partner_compare.js`
- **行数**: 52
- **模块**: `@mail/discuss/core/common/partner_compare`

## 依赖关系

```javascript
// 比较注册表
'@mail/core/common/partner_compare'  // 伙伴比较注册表
```

## 比较规则注册

### 1. 最近聊天排序规则

```javascript
partnerCompareRegistry.add(
    "discuss.recent-chats",
    (p1, p2, { env, context }) => {
        const recentChatPartnerIds =
            context.recentChatPartnerIds || env.services["mail.store"].getRecentChatPartnerIds();
        const recentChatIndex_p1 = recentChatPartnerIds.findIndex(
            (partnerId) => partnerId === p1.id
        );
        const recentChatIndex_p2 = recentChatPartnerIds.findIndex(
            (partnerId) => partnerId === p2.id
        );
        if (recentChatIndex_p1 !== -1 && recentChatIndex_p2 === -1) {
            return -1;
        } else if (recentChatIndex_p1 === -1 && recentChatIndex_p2 !== -1) {
            return 1;
        } else if (recentChatIndex_p1 < recentChatIndex_p2) {
            return -1;
        } else if (recentChatIndex_p1 > recentChatIndex_p2) {
            return 1;
        }
    },
    { sequence: 25 }
);
```

**规则特性**:
- **序列号**: 25（中等优先级）
- **数据源**: 最近聊天伙伴ID列表
- **排序逻辑**: 最近聊天的伙伴排在前面
- **索引比较**: 基于在最近聊天列表中的索引位置

**比较逻辑**:
- 如果p1在最近聊天中而p2不在，p1排在前面（返回-1）
- 如果p2在最近聊天中而p1不在，p2排在前面（返回1）
- 如果都在最近聊天中，索引小的排在前面
- 如果都不在最近聊天中，返回undefined（继续下一个比较规则）

### 2. 频道成员优先级规则

```javascript
partnerCompareRegistry.add(
    "discuss.members",
    (p1, p2, { thread, memberPartnerIds }) => {
        if (thread?.model === "discuss.channel") {
            const isMember1 = memberPartnerIds.has(p1.id);
            const isMember2 = memberPartnerIds.has(p2.id);
            if (isMember1 && !isMember2) {
                return -1;
            }
            if (!isMember1 && isMember2) {
                return 1;
            }
        }
    },
    { sequence: 40 }
);
```

**规则特性**:
- **序列号**: 40（较低优先级）
- **适用范围**: 仅讨论频道模型
- **数据源**: 频道成员伙伴ID集合
- **排序逻辑**: 频道成员优先于非成员

**比较逻辑**:
- 仅在讨论频道中生效
- 如果p1是成员而p2不是，p1排在前面（返回-1）
- 如果p2是成员而p1不是，p2排在前面（返回1）
- 如果都是成员或都不是成员，返回undefined（继续下一个比较规则）

## 使用场景

### 1. 伙伴比较增强

```javascript
// 伙伴比较增强功能
const PartnerCompareEnhancer = {
    enhancePartnerCompare: () => {
        // 添加在线状态优先级规则
        partnerCompareRegistry.add(
            "discuss.online-status",
            (p1, p2, { env }) => {
                const store = env.services["mail.store"];
                
                const status1 = store.getPersonaOnlineStatus(p1);
                const status2 = store.getPersonaOnlineStatus(p2);
                
                const statusPriority = {
                    'online': 0,
                    'away': 1,
                    'busy': 2,
                    'offline': 3
                };
                
                const priority1 = statusPriority[status1] ?? 3;
                const priority2 = statusPriority[status2] ?? 3;
                
                if (priority1 < priority2) {
                    return -1;
                } else if (priority1 > priority2) {
                    return 1;
                }
                // 相同状态继续下一个规则
            },
            { sequence: 15 } // 高优先级
        );
        
        // 添加收藏联系人规则
        partnerCompareRegistry.add(
            "discuss.favorite-contacts",
            (p1, p2, { env, context }) => {
                const favoritePartnerIds = context.favoritePartnerIds || 
                    PartnerCompareEnhancer.getFavoritePartnerIds(env);
                
                const isFavorite1 = favoritePartnerIds.includes(p1.id);
                const isFavorite2 = favoritePartnerIds.includes(p2.id);
                
                if (isFavorite1 && !isFavorite2) {
                    return -1;
                } else if (!isFavorite1 && isFavorite2) {
                    return 1;
                }
            },
            { sequence: 20 }
        );
        
        // 添加活跃度排序规则
        partnerCompareRegistry.add(
            "discuss.activity-score",
            (p1, p2, { env, thread }) => {
                const score1 = PartnerCompareEnhancer.calculateActivityScore(p1, thread, env);
                const score2 = PartnerCompareEnhancer.calculateActivityScore(p2, thread, env);
                
                if (score1 > score2) {
                    return -1;
                } else if (score1 < score2) {
                    return 1;
                }
            },
            { sequence: 35 }
        );
        
        // 添加角色优先级规则
        partnerCompareRegistry.add(
            "discuss.role-priority",
            (p1, p2, { thread }) => {
                if (thread?.model === "discuss.channel") {
                    const role1 = PartnerCompareEnhancer.getPartnerRole(p1, thread);
                    const role2 = PartnerCompareEnhancer.getPartnerRole(p2, thread);
                    
                    const rolePriority = {
                        'admin': 0,
                        'moderator': 1,
                        'member': 2,
                        'guest': 3
                    };
                    
                    const priority1 = rolePriority[role1] ?? 3;
                    const priority2 = rolePriority[role2] ?? 3;
                    
                    if (priority1 < priority2) {
                        return -1;
                    } else if (priority1 > priority2) {
                        return 1;
                    }
                }
            },
            { sequence: 30 }
        );
        
        // 添加字母排序规则（最后的回退规则）
        partnerCompareRegistry.add(
            "discuss.alphabetical",
            (p1, p2) => {
                const name1 = (p1.name || '').toLowerCase();
                const name2 = (p2.name || '').toLowerCase();
                
                return name1.localeCompare(name2);
            },
            { sequence: 100 } // 最低优先级，作为最终回退
        );
        
        // 增强现有规则
        PartnerCompareEnhancer.enhanceExistingRules();
    },
    
    // 获取收藏伙伴ID列表
    getFavoritePartnerIds(env) {
        try {
            const favorites = JSON.parse(
                localStorage.getItem('favorite_partner_ids') || '[]'
            );
            return favorites;
        } catch (error) {
            console.warn('获取收藏伙伴列表失败:', error);
            return [];
        }
    },
    
    // 计算活跃度分数
    calculateActivityScore(partner, thread, env) {
        let score = 0;
        
        // 基础分数
        score += 10;
        
        // 最近消息分数
        const recentMessages = PartnerCompareEnhancer.getRecentMessages(partner, thread, env);
        score += recentMessages.length * 5;
        
        // 在线状态分数
        const onlineStatus = env.services["mail.store"].getPersonaOnlineStatus(partner);
        const statusScore = {
            'online': 20,
            'away': 10,
            'busy': 5,
            'offline': 0
        };
        score += statusScore[onlineStatus] || 0;
        
        // 互动频率分数
        const interactionCount = PartnerCompareEnhancer.getInteractionCount(partner, env);
        score += Math.min(interactionCount, 50); // 最多50分
        
        // 共同频道分数
        const commonChannels = PartnerCompareEnhancer.getCommonChannels(partner, env);
        score += commonChannels.length * 2;
        
        return score;
    },
    
    // 获取最近消息
    getRecentMessages(partner, thread, env) {
        const store = env.services["mail.store"];
        const messages = thread?.messages || [];
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        
        return messages.filter(message => 
            message.author?.id === partner.id &&
            new Date(message.datetime).getTime() > oneWeekAgo
        );
    },
    
    // 获取互动次数
    getInteractionCount(partner, env) {
        try {
            const interactions = JSON.parse(
                localStorage.getItem('partner_interactions') || '{}'
            );
            return interactions[partner.id] || 0;
        } catch (error) {
            return 0;
        }
    },
    
    // 获取共同频道
    getCommonChannels(partner, env) {
        const store = env.services["mail.store"];
        const allChannels = Object.values(store.Thread.records).filter(
            thread => thread.model === 'discuss.channel'
        );
        
        return allChannels.filter(channel => {
            const memberIds = channel.channelMembers?.map(member => member.persona.id) || [];
            return memberIds.includes(partner.id) && memberIds.includes(store.self?.id);
        });
    },
    
    // 获取伙伴角色
    getPartnerRole(partner, thread) {
        if (thread?.model !== "discuss.channel") {
            return 'member';
        }
        
        const member = thread.channelMembers?.find(
            member => member.persona.id === partner.id
        );
        
        if (!member) {
            return partner.type === 'guest' ? 'guest' : 'member';
        }
        
        return member.role || 'member';
    },
    
    // 增强现有规则
    enhanceExistingRules() {
        // 增强最近聊天规则
        const recentChatsRule = partnerCompareRegistry.get("discuss.recent-chats");
        const originalRecentChats = recentChatsRule.fn;
        
        recentChatsRule.fn = function(p1, p2, context) {
            // 添加时间权重
            const result = originalRecentChats.call(this, p1, p2, context);
            
            if (result !== undefined) {
                // 如果有明确结果，检查时间权重
                const recentChatTimes = PartnerCompareEnhancer.getRecentChatTimes(context.env);
                const time1 = recentChatTimes[p1.id];
                const time2 = recentChatTimes[p2.id];
                
                if (time1 && time2) {
                    // 如果都有聊天时间，更近的排在前面
                    return time1 > time2 ? -1 : (time1 < time2 ? 1 : result);
                }
            }
            
            return result;
        };
        
        // 增强成员规则
        const membersRule = partnerCompareRegistry.get("discuss.members");
        const originalMembers = membersRule.fn;
        
        membersRule.fn = function(p1, p2, context) {
            const result = originalMembers.call(this, p1, p2, context);
            
            if (result !== undefined) {
                return result;
            }
            
            // 如果都是成员或都不是成员，按加入时间排序
            if (context.thread?.model === "discuss.channel") {
                const joinTime1 = PartnerCompareEnhancer.getMemberJoinTime(p1, context.thread);
                const joinTime2 = PartnerCompareEnhancer.getMemberJoinTime(p2, context.thread);
                
                if (joinTime1 && joinTime2) {
                    return joinTime1 < joinTime2 ? -1 : (joinTime1 > joinTime2 ? 1 : 0);
                }
            }
            
            return result;
        };
    },
    
    // 获取最近聊天时间
    getRecentChatTimes(env) {
        try {
            const chatTimes = JSON.parse(
                localStorage.getItem('recent_chat_times') || '{}'
            );
            return chatTimes;
        } catch (error) {
            return {};
        }
    },
    
    // 获取成员加入时间
    getMemberJoinTime(partner, thread) {
        const member = thread.channelMembers?.find(
            member => member.persona.id === partner.id
        );
        
        return member?.create_date ? new Date(member.create_date).getTime() : null;
    },
    
    // 记录伙伴互动
    recordPartnerInteraction(partnerId) {
        try {
            const interactions = JSON.parse(
                localStorage.getItem('partner_interactions') || '{}'
            );
            
            interactions[partnerId] = (interactions[partnerId] || 0) + 1;
            
            localStorage.setItem('partner_interactions', JSON.stringify(interactions));
        } catch (error) {
            console.warn('记录伙伴互动失败:', error);
        }
    },
    
    // 记录聊天时间
    recordChatTime(partnerId) {
        try {
            const chatTimes = JSON.parse(
                localStorage.getItem('recent_chat_times') || '{}'
            );
            
            chatTimes[partnerId] = Date.now();
            
            localStorage.setItem('recent_chat_times', JSON.stringify(chatTimes));
        } catch (error) {
            console.warn('记录聊天时间失败:', error);
        }
    },
    
    // 添加收藏伙伴
    addFavoritePartner(partnerId) {
        try {
            const favorites = JSON.parse(
                localStorage.getItem('favorite_partner_ids') || '[]'
            );
            
            if (!favorites.includes(partnerId)) {
                favorites.push(partnerId);
                localStorage.setItem('favorite_partner_ids', JSON.stringify(favorites));
            }
        } catch (error) {
            console.warn('添加收藏伙伴失败:', error);
        }
    },
    
    // 移除收藏伙伴
    removeFavoritePartner(partnerId) {
        try {
            const favorites = JSON.parse(
                localStorage.getItem('favorite_partner_ids') || '[]'
            );
            
            const index = favorites.indexOf(partnerId);
            if (index !== -1) {
                favorites.splice(index, 1);
                localStorage.setItem('favorite_partner_ids', JSON.stringify(favorites));
            }
        } catch (error) {
            console.warn('移除收藏伙伴失败:', error);
        }
    },
    
    // 获取排序统计
    getSortingStats() {
        const rules = partnerCompareRegistry.getAll();
        
        return {
            totalRules: rules.length,
            rulesBySequence: rules.sort((a, b) => a.sequence - b.sequence),
            customRules: rules.filter(rule => rule.id.startsWith('discuss.')),
            enhancedRules: ['discuss.recent-chats', 'discuss.members']
        };
    },
    
    // 测试排序规则
    testSortingRules(partners, context) {
        const rules = partnerCompareRegistry.getAll().sort((a, b) => a.sequence - b.sequence);
        const results = [];
        
        for (let i = 0; i < partners.length - 1; i++) {
            for (let j = i + 1; j < partners.length; j++) {
                const p1 = partners[i];
                const p2 = partners[j];
                
                for (const rule of rules) {
                    const result = rule.fn(p1, p2, context);
                    
                    if (result !== undefined) {
                        results.push({
                            rule: rule.id,
                            partners: [p1.name, p2.name],
                            result: result,
                            sequence: rule.sequence
                        });
                        break;
                    }
                }
            }
        }
        
        return results;
    }
};

// 应用伙伴比较增强
PartnerCompareEnhancer.enhancePartnerCompare();
```

## 技术特点

### 1. 注册表机制
- 统一的比较规则注册管理
- 序列化优先级控制
- 动态规则添加支持

### 2. 比较逻辑
- 标准化的比较函数接口
- 多级比较规则链
- 上下文感知的比较

### 3. 数据源集成
- 最近聊天数据集成
- 频道成员数据集成
- 存储服务数据访问

### 4. 排序优化
- 高效的索引查找
- 条件化的比较执行
- 回退机制支持

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 统一的规则注册和管理
- 动态规则配置

### 2. 策略模式 (Strategy Pattern)
- 不同比较策略的实现
- 可插拔的比较规则

### 3. 责任链模式 (Chain of Responsibility Pattern)
- 多级比较规则链
- 按序列执行比较

## 注意事项

1. **性能优化**: 避免频繁的数据查找和计算
2. **数据一致性**: 确保比较数据的实时性
3. **规则冲突**: 避免不同规则间的逻辑冲突
4. **扩展性**: 保持规则的可扩展性和可维护性

## 扩展建议

1. **更多规则**: 添加更多智能排序规则
2. **权重配置**: 支持规则权重的动态配置
3. **用户定制**: 允许用户自定义排序偏好
4. **性能监控**: 添加排序性能监控和优化
5. **A/B测试**: 支持不同排序策略的A/B测试

该文件为讨论应用提供了智能的伙伴排序功能，提升了用户界面的易用性和用户体验。

# Message Model Patch - 消息模型补丁

## 概述

`message_model_patch.js` 实现了对 Odoo 邮件系统中消息模型的补丁扩展，专门为讨论应用添加了频道提及处理功能。该补丁通过Odoo的补丁机制扩展了Message模型，重写了edit方法，添加了mentionedChannelPromises属性，用于处理消息编辑时的频道提及验证和合并，确保编辑后的消息包含所有有效的频道提及，是讨论应用中消息编辑功能的重要增强组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/message_model_patch.js`
- **行数**: 30
- **模块**: `@mail/discuss/core/common/message_model_patch`

## 依赖关系

```javascript
// 模型依赖
'@mail/core/common/message_model'  // 消息模型
'@web/core/utils/patch'            // 补丁工具
```

## 补丁定义

### Message 补丁

```javascript
patch(Message.prototype, {
    /** @type {Promise[]} */
    mentionedChannelPromises: [],
    
    async edit(body, attachments = [], { mentionedChannels = [], mentionedPartners = [] } = {}) {
        const validChannels = (await Promise.all(this.mentionedChannelPromises)).filter(
            (channel) => channel !== undefined
        );
        const allChannels = this.store.Thread.insert([...validChannels, ...mentionedChannels]);
        super.edit(body, attachments, {
            mentionedChannels: allChannels,
            mentionedPartners,
        });
    },
});
```

**补丁特性**:
- 添加频道提及Promise数组属性
- 重写消息编辑方法
- 异步处理频道验证
- 合并有效频道提及

## 核心功能

### 1. 频道提及Promise管理

```javascript
/** @type {Promise[]} */
mentionedChannelPromises: [],
```

**属性功能**:
- **类型定义**: Promise数组类型
- **用途**: 存储异步获取的频道对象Promise
- **生命周期**: 在消息编辑过程中使用

### 2. 增强的编辑方法

```javascript
async edit(body, attachments = [], { mentionedChannels = [], mentionedPartners = [] } = {}) {
    const validChannels = (await Promise.all(this.mentionedChannelPromises)).filter(
        (channel) => channel !== undefined
    );
    const allChannels = this.store.Thread.insert([...validChannels, ...mentionedChannels]);
    super.edit(body, attachments, {
        mentionedChannels: allChannels,
        mentionedPartners,
    });
}
```

**编辑逻辑**:
- **异步等待**: 等待所有频道Promise完成
- **有效性过滤**: 过滤掉undefined的频道
- **数据插入**: 将频道插入存储
- **合并提及**: 合并预处理和新的频道提及
- **父类调用**: 调用原有编辑方法

## 使用场景

### 1. 消息编辑增强

```javascript
// 消息编辑增强功能
const MessageModelEnhancer = {
    enhanceMessageModel: () => {
        const EnhancedMessagePatch = {
            // 增强的频道提及Promise管理
            mentionedChannelPromises: [],
            mentionedUserPromises: [],
            mentionedAttachmentPromises: [],
            
            // 编辑状态管理
            editState: {
                isEditing: false,
                originalBody: null,
                originalAttachments: [],
                editStartTime: null,
                editHistory: []
            },
            
            // 增强的编辑方法
            async edit(body, attachments = [], options = {}) {
                const {
                    mentionedChannels = [],
                    mentionedPartners = [],
                    mentionedUsers = [],
                    preserveFormatting = true,
                    validateMentions = true,
                    trackChanges = true
                } = options;
                
                try {
                    // 开始编辑状态
                    this.startEdit(body, attachments);
                    
                    // 验证编辑权限
                    if (!this.canEdit()) {
                        throw new Error('没有编辑权限');
                    }
                    
                    // 处理频道提及
                    const validChannels = await this.processChannelMentions(mentionedChannels, validateMentions);
                    
                    // 处理用户提及
                    const validUsers = await this.processUserMentions(mentionedUsers, validateMentions);
                    
                    // 处理附件引用
                    const validAttachments = await this.processAttachmentReferences(attachments);
                    
                    // 验证内容
                    const validatedBody = await this.validateMessageContent(body, preserveFormatting);
                    
                    // 记录编辑历史
                    if (trackChanges) {
                        this.recordEditHistory(validatedBody, validAttachments, validChannels, validUsers);
                    }
                    
                    // 执行编辑
                    await super.edit(validatedBody, validAttachments, {
                        mentionedChannels: validChannels,
                        mentionedPartners: [...mentionedPartners, ...validUsers]
                    });
                    
                    // 完成编辑状态
                    this.completeEdit();
                    
                } catch (error) {
                    console.error('消息编辑失败:', error);
                    this.cancelEdit();
                    throw error;
                }
            },
            
            // 开始编辑
            startEdit(body, attachments) {
                this.editState.isEditing = true;
                this.editState.originalBody = this.body;
                this.editState.originalAttachments = [...(this.attachments || [])];
                this.editState.editStartTime = Date.now();
            },
            
            // 完成编辑
            completeEdit() {
                this.editState.isEditing = false;
                this.editState.originalBody = null;
                this.editState.originalAttachments = [];
                this.editState.editStartTime = null;
                
                // 清理Promise数组
                this.mentionedChannelPromises = [];
                this.mentionedUserPromises = [];
                this.mentionedAttachmentPromises = [];
            },
            
            // 取消编辑
            cancelEdit() {
                this.editState.isEditing = false;
                this.editState.originalBody = null;
                this.editState.originalAttachments = [];
                this.editState.editStartTime = null;
                
                // 清理Promise数组
                this.mentionedChannelPromises = [];
                this.mentionedUserPromises = [];
                this.mentionedAttachmentPromises = [];
            },
            
            // 处理频道提及
            async processChannelMentions(newChannels, validate = true) {
                // 等待预处理的频道Promise
                const preProcessedChannels = (await Promise.all(this.mentionedChannelPromises)).filter(
                    (channel) => channel !== undefined
                );
                
                // 合并所有频道
                let allChannels = [...preProcessedChannels, ...newChannels];
                
                if (validate) {
                    // 验证频道访问权限
                    allChannels = await this.validateChannelAccess(allChannels);
                }
                
                // 去重
                allChannels = this.deduplicateChannels(allChannels);
                
                // 插入存储
                return this.store.Thread.insert(allChannels);
            },
            
            // 处理用户提及
            async processUserMentions(newUsers, validate = true) {
                // 等待预处理的用户Promise
                const preProcessedUsers = (await Promise.all(this.mentionedUserPromises)).filter(
                    (user) => user !== undefined
                );
                
                // 合并所有用户
                let allUsers = [...preProcessedUsers, ...newUsers];
                
                if (validate) {
                    // 验证用户状态
                    allUsers = await this.validateUserStatus(allUsers);
                }
                
                // 去重
                allUsers = this.deduplicateUsers(allUsers);
                
                return allUsers;
            },
            
            // 处理附件引用
            async processAttachmentReferences(newAttachments) {
                // 等待预处理的附件Promise
                const preProcessedAttachments = (await Promise.all(this.mentionedAttachmentPromises)).filter(
                    (attachment) => attachment !== undefined
                );
                
                // 合并所有附件
                let allAttachments = [...preProcessedAttachments, ...newAttachments];
                
                // 验证附件访问权限
                allAttachments = await this.validateAttachmentAccess(allAttachments);
                
                // 去重
                allAttachments = this.deduplicateAttachments(allAttachments);
                
                return allAttachments;
            },
            
            // 验证频道访问权限
            async validateChannelAccess(channels) {
                const validChannels = [];
                
                for (const channel of channels) {
                    try {
                        // 检查频道是否存在
                        if (!channel || !channel.id) {
                            continue;
                        }
                        
                        // 检查访问权限
                        const hasAccess = await this.checkChannelAccess(channel);
                        
                        if (hasAccess) {
                            validChannels.push(channel);
                        } else {
                            console.warn('无权访问频道:', channel.displayName);
                        }
                    } catch (error) {
                        console.error('验证频道访问权限失败:', channel, error);
                    }
                }
                
                return validChannels;
            },
            
            // 检查频道访问权限
            async checkChannelAccess(channel) {
                try {
                    // 检查频道是否公开
                    if (channel.channel_type === 'channel' && channel.public === 'public') {
                        return true;
                    }
                    
                    // 检查是否为频道成员
                    const user = this.store.self;
                    const isMember = channel.channelMembers?.some(
                        member => member.persona.id === user.id
                    );
                    
                    return isMember;
                } catch (error) {
                    console.error('检查频道访问权限失败:', error);
                    return false;
                }
            },
            
            // 验证用户状态
            async validateUserStatus(users) {
                const validUsers = [];
                
                for (const user of users) {
                    try {
                        if (!user || !user.id) {
                            continue;
                        }
                        
                        // 检查用户是否活跃
                        if (user.active !== false) {
                            validUsers.push(user);
                        } else {
                            console.warn('用户已停用:', user.name);
                        }
                    } catch (error) {
                        console.error('验证用户状态失败:', user, error);
                    }
                }
                
                return validUsers;
            },
            
            // 验证附件访问权限
            async validateAttachmentAccess(attachments) {
                const validAttachments = [];
                
                for (const attachment of attachments) {
                    try {
                        if (!attachment || !attachment.id) {
                            continue;
                        }
                        
                        // 检查附件是否可访问
                        if (attachment.isAccessible !== false) {
                            validAttachments.push(attachment);
                        } else {
                            console.warn('无权访问附件:', attachment.name);
                        }
                    } catch (error) {
                        console.error('验证附件访问权限失败:', attachment, error);
                    }
                }
                
                return validAttachments;
            },
            
            // 去重频道
            deduplicateChannels(channels) {
                const seen = new Set();
                return channels.filter(channel => {
                    if (seen.has(channel.id)) {
                        return false;
                    }
                    seen.add(channel.id);
                    return true;
                });
            },
            
            // 去重用户
            deduplicateUsers(users) {
                const seen = new Set();
                return users.filter(user => {
                    if (seen.has(user.id)) {
                        return false;
                    }
                    seen.add(user.id);
                    return true;
                });
            },
            
            // 去重附件
            deduplicateAttachments(attachments) {
                const seen = new Set();
                return attachments.filter(attachment => {
                    if (seen.has(attachment.id)) {
                        return false;
                    }
                    seen.add(attachment.id);
                    return true;
                });
            },
            
            // 验证消息内容
            async validateMessageContent(body, preserveFormatting) {
                let validatedBody = body;
                
                // 内容长度检查
                if (validatedBody.length > 10000) {
                    throw new Error('消息内容过长');
                }
                
                // HTML安全检查
                validatedBody = this.sanitizeHtml(validatedBody);
                
                // 格式化处理
                if (preserveFormatting) {
                    validatedBody = this.preserveMessageFormatting(validatedBody);
                }
                
                return validatedBody;
            },
            
            // HTML安全处理
            sanitizeHtml(html) {
                // 创建临时DOM元素
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // 移除危险的脚本标签
                const scripts = tempDiv.querySelectorAll('script');
                scripts.forEach(script => script.remove());
                
                // 移除危险的事件属性
                const allElements = tempDiv.querySelectorAll('*');
                allElements.forEach(element => {
                    Array.from(element.attributes).forEach(attr => {
                        if (attr.name.startsWith('on')) {
                            element.removeAttribute(attr.name);
                        }
                    });
                });
                
                return tempDiv.innerHTML;
            },
            
            // 保持消息格式
            preserveMessageFormatting(body) {
                // 保持换行符
                body = body.replace(/\n/g, '<br>');
                
                // 保持空格
                body = body.replace(/  /g, '&nbsp;&nbsp;');
                
                return body;
            },
            
            // 记录编辑历史
            recordEditHistory(body, attachments, channels, users) {
                const historyEntry = {
                    timestamp: Date.now(),
                    originalBody: this.editState.originalBody,
                    newBody: body,
                    originalAttachments: this.editState.originalAttachments.map(a => a.id),
                    newAttachments: attachments.map(a => a.id),
                    mentionedChannels: channels.map(c => c.id),
                    mentionedUsers: users.map(u => u.id),
                    editDuration: Date.now() - this.editState.editStartTime
                };
                
                this.editState.editHistory.push(historyEntry);
                
                // 保留最近10次编辑历史
                if (this.editState.editHistory.length > 10) {
                    this.editState.editHistory.shift();
                }
            },
            
            // 检查编辑权限
            canEdit() {
                // 检查基本编辑权限
                if (!this.editable) {
                    return false;
                }
                
                // 检查编辑时间限制
                const editTimeLimit = 24 * 60 * 60 * 1000; // 24小时
                const messageAge = Date.now() - new Date(this.datetime).getTime();
                
                if (messageAge > editTimeLimit) {
                    return false;
                }
                
                // 检查用户权限
                const user = this.store.self;
                if (!user || (this.author?.id !== user.id && !user.isInternalUser)) {
                    return false;
                }
                
                return true;
            },
            
            // 获取编辑统计
            getEditStatistics() {
                return {
                    totalEdits: this.editState.editHistory.length,
                    lastEditTime: this.editState.editHistory.length > 0 
                        ? this.editState.editHistory[this.editState.editHistory.length - 1].timestamp
                        : null,
                    averageEditDuration: this.editState.editHistory.length > 0
                        ? this.editState.editHistory.reduce((sum, entry) => sum + entry.editDuration, 0) / this.editState.editHistory.length
                        : 0,
                    isCurrentlyEditing: this.editState.isEditing
                };
            },
            
            // 恢复到历史版本
            async revertToHistory(historyIndex) {
                if (historyIndex < 0 || historyIndex >= this.editState.editHistory.length) {
                    throw new Error('无效的历史索引');
                }
                
                const historyEntry = this.editState.editHistory[historyIndex];
                
                // 恢复到历史版本
                await this.edit(historyEntry.originalBody, historyEntry.originalAttachments, {
                    trackChanges: false // 不记录恢复操作
                });
            }
        };
        
        patch(Message.prototype, EnhancedMessagePatch);
    }
};

// 应用消息模型增强
MessageModelEnhancer.enhanceMessageModel();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 异步处理
- Promise数组管理
- 异步频道验证
- 并发处理优化

### 3. 数据验证
- 频道有效性检查
- 数据去重处理
- 存储集成管理

### 4. 编辑增强
- 提及预处理支持
- 数据合并逻辑
- 父类方法调用

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 异步模式 (Async Pattern)
- Promise并发处理
- 异步数据验证

### 3. 装饰器模式 (Decorator Pattern)
- 功能的装饰和增强
- 透明的功能扩展

## 注意事项

1. **异步处理**: 确保Promise的正确处理和错误捕获
2. **数据一致性**: 保证频道数据的一致性和有效性
3. **性能优化**: 避免频繁的异步操作影响性能
4. **内存管理**: 及时清理Promise数组避免内存泄漏

## 扩展建议

1. **更多验证**: 添加更多的数据验证规则
2. **缓存优化**: 实现频道数据的缓存机制
3. **错误恢复**: 增强错误处理和恢复机制
4. **批量处理**: 支持批量频道验证和处理
5. **权限细化**: 实现更细粒度的权限控制

该补丁为讨论应用的消息编辑功能提供了重要的频道提及处理能力，确保编辑后的消息包含正确的频道引用。

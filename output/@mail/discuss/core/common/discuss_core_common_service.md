# Discuss Core Common Service - 讨论核心通用服务

## 概述

`discuss_core_common_service.js` 实现了 Odoo 讨论应用的核心通用服务，负责管理讨论应用的基础功能和事件处理。该服务集成了总线服务、通知服务、ORM服务、在线状态服务等多个核心服务，处理频道离开、删除、新消息、成员更新等各种讨论相关事件，提供了讨论应用的核心业务逻辑和事件协调，是讨论应用架构中的重要基础服务。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/discuss_core_common_service.js`
- **行数**: 220
- **模块**: `@mail/discuss/core/common/discuss_core_common_service`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/l10n/translation'   // 国际化
'@web/core/registry'           // 注册表系统
```

## 服务定义

### DiscussCoreCommon 类

```javascript
const DiscussCoreCommon = class DiscussCoreCommon {
    constructor(env, services) {
        this.busService = services.bus_service;
        this.env = env;
        this.notificationService = services.notification;
        this.orm = services.orm;
        this.presence = services.presence;
        this.store = services["mail.store"];
    }
}
```

**服务特性**:
- 多服务集成
- 事件驱动架构
- 响应式状态管理
- 国际化支持

## 核心功能

### 1. 服务初始化

```javascript
setup() {
    // 总线连接事件监听
    this.busService.addEventListener("connect", () => {
        this.store.imStatusTrackedPersonas.forEach((p) => {
            const model = p.type === "partner" ? "res.partner" : "mail.guest";
            this.busService.addChannel(`odoo-presence-${model}_${p.id}`);
        });
    }, { once: true });
    
    // 订阅各种讨论事件
    this.setupEventSubscriptions();
}
```

**初始化功能**:
- **在线状态跟踪**: 为跟踪的用户添加在线状态频道
- **事件订阅**: 订阅各种讨论相关事件
- **一次性监听**: 连接事件只监听一次

### 2. 频道离开处理

```javascript
this.busService.subscribe("discuss.channel/leave", (payload) => {
    const { Thread } = this.store.insert(payload);
    const [thread] = Thread;
    if (thread.notifyOnLeave) {
        this.notificationService.add(_t("You unsubscribed from %s.", thread.displayName), {
            type: "info",
        });
    }
});
```

**离开处理功能**:
- **数据更新**: 更新存储中的线程数据
- **通知显示**: 根据设置显示离开通知
- **国际化**: 使用国际化的通知消息

### 3. 频道删除处理

```javascript
this.busService.subscribe("discuss.channel/delete", (payload, metadata) => {
    const thread = this.store.Thread.insert({
        id: payload.id,
        model: "discuss.channel",
    });
    thread.delete();
    this.notificationService.add(_t("The channel %s has been deleted.", thread.displayName), {
        type: "info",
    });
});
```

**删除处理功能**:
- **线程删除**: 从存储中删除线程
- **删除通知**: 显示频道删除通知
- **数据清理**: 清理相关数据

### 4. 新消息处理

```javascript
this.busService.subscribe("discuss.channel/new_message", (payload, metadata) => {
    const { Message, Thread } = this.store.insert(payload);
    const [message] = Message;
    const [thread] = Thread;
    
    if (message.isSelfAuthored) {
        return;
    }
    
    // 处理新消息逻辑
    this.handleNewMessage(message, thread, metadata);
});
```

**新消息功能**:
- **消息插入**: 将新消息插入存储
- **自发消息过滤**: 过滤自己发送的消息
- **消息处理**: 执行新消息的处理逻辑

## 使用场景

### 1. 服务功能增强

```javascript
// 服务功能增强
const DiscussServiceEnhancer = {
    enhanceDiscussService: () => {
        const EnhancedDiscussCoreCommon = class extends DiscussCoreCommon {
            constructor(env, services) {
                super(env, services);
                
                // 增强状态管理
                this.serviceState = reactive({
                    isInitialized: false,
                    connectionStatus: 'disconnected',
                    lastActivity: null,
                    eventQueue: [],
                    processingEvents: false
                });
                
                // 性能监控
                this.performanceMetrics = {
                    eventProcessingTime: [],
                    messageHandlingTime: [],
                    notificationCount: 0,
                    errorCount: 0
                };
                
                // 错误处理
                this.errorHandler = this.createErrorHandler();
                
                // 事件缓存
                this.eventCache = new Map();
            },
            
            setup() {
                super.setup();
                
                // 增强的初始化
                this.enhancedSetup();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置错误处理
                this.setupErrorHandling();
                
                // 设置事件队列处理
                this.setupEventQueueProcessing();
            },
            
            // 增强的初始化
            enhancedSetup() {
                // 连接状态监控
                this.busService.addEventListener("connect", () => {
                    this.serviceState.connectionStatus = 'connected';
                    this.serviceState.lastActivity = Date.now();
                    this.processQueuedEvents();
                });
                
                this.busService.addEventListener("disconnect", () => {
                    this.serviceState.connectionStatus = 'disconnected';
                });
                
                // 增强的在线状态跟踪
                this.setupEnhancedPresenceTracking();
                
                // 批量事件处理
                this.setupBatchEventProcessing();
                
                this.serviceState.isInitialized = true;
            },
            
            // 设置增强的在线状态跟踪
            setupEnhancedPresenceTracking() {
                this.busService.addEventListener("connect", () => {
                    const personas = this.store.imStatusTrackedPersonas;
                    
                    // 批量添加在线状态频道
                    const channels = personas.map(p => {
                        const model = p.type === "partner" ? "res.partner" : "mail.guest";
                        return `odoo-presence-${model}_${p.id}`;
                    });
                    
                    // 批量添加频道以提高性能
                    this.busService.addChannels(channels);
                    
                    // 记录跟踪的用户数量
                    console.log(`开始跟踪 ${personas.length} 个用户的在线状态`);
                }, { once: true });
            },
            
            // 设置批量事件处理
            setupBatchEventProcessing() {
                // 频道离开批量处理
                this.busService.subscribeBatch("discuss.channel/leave", (payloads) => {
                    this.handleBatchChannelLeave(payloads);
                });
                
                // 新消息批量处理
                this.busService.subscribeBatch("discuss.channel/new_message", (payloads) => {
                    this.handleBatchNewMessages(payloads);
                });
            },
            
            // 处理批量频道离开
            handleBatchChannelLeave(payloads) {
                const startTime = performance.now();
                
                try {
                    const notifications = [];
                    
                    payloads.forEach(payload => {
                        const { Thread } = this.store.insert(payload);
                        const [thread] = Thread;
                        
                        if (thread.notifyOnLeave) {
                            notifications.push({
                                message: _t("You unsubscribed from %s.", thread.displayName),
                                type: "info"
                            });
                        }
                    });
                    
                    // 批量显示通知
                    if (notifications.length > 0) {
                        this.showBatchNotifications(notifications);
                    }
                    
                } catch (error) {
                    this.errorHandler.handle('batch_channel_leave', error);
                } finally {
                    this.recordPerformanceMetric('eventProcessingTime', performance.now() - startTime);
                }
            },
            
            // 处理批量新消息
            handleBatchNewMessages(payloads) {
                const startTime = performance.now();
                
                try {
                    const processedMessages = [];
                    
                    payloads.forEach(payload => {
                        const { Message, Thread } = this.store.insert(payload);
                        const [message] = Message;
                        const [thread] = Thread;
                        
                        if (!message.isSelfAuthored) {
                            processedMessages.push({ message, thread });
                        }
                    });
                    
                    // 批量处理消息
                    this.processBatchMessages(processedMessages);
                    
                } catch (error) {
                    this.errorHandler.handle('batch_new_messages', error);
                } finally {
                    this.recordPerformanceMetric('messageHandlingTime', performance.now() - startTime);
                }
            },
            
            // 处理批量消息
            processBatchMessages(messages) {
                // 按线程分组消息
                const messagesByThread = new Map();
                
                messages.forEach(({ message, thread }) => {
                    if (!messagesByThread.has(thread.id)) {
                        messagesByThread.set(thread.id, []);
                    }
                    messagesByThread.get(thread.id).push(message);
                });
                
                // 为每个线程处理消息
                messagesByThread.forEach((threadMessages, threadId) => {
                    this.processThreadMessages(threadId, threadMessages);
                });
            },
            
            // 处理线程消息
            processThreadMessages(threadId, messages) {
                const thread = this.store.Thread.get(threadId);
                if (!thread) return;
                
                // 更新未读计数
                thread.message_unread_counter += messages.length;
                
                // 触发通知
                if (thread.shouldNotify) {
                    this.triggerMessageNotifications(thread, messages);
                }
                
                // 更新最后消息时间
                const latestMessage = messages[messages.length - 1];
                thread.lastMessageTime = latestMessage.datetime;
            },
            
            // 触发消息通知
            triggerMessageNotifications(thread, messages) {
                if (messages.length === 1) {
                    const message = messages[0];
                    this.notificationService.add(
                        _t("New message in %s from %s", thread.displayName, message.author.name),
                        { type: "info" }
                    );
                } else {
                    this.notificationService.add(
                        _t("%s new messages in %s", messages.length, thread.displayName),
                        { type: "info" }
                    );
                }
                
                this.performanceMetrics.notificationCount++;
            },
            
            // 显示批量通知
            showBatchNotifications(notifications) {
                if (notifications.length === 1) {
                    this.notificationService.add(notifications[0].message, {
                        type: notifications[0].type
                    });
                } else {
                    // 合并相似通知
                    const groupedNotifications = this.groupSimilarNotifications(notifications);
                    
                    groupedNotifications.forEach(group => {
                        if (group.count === 1) {
                            this.notificationService.add(group.message, { type: group.type });
                        } else {
                            this.notificationService.add(
                                _t("%s similar notifications", group.count),
                                { type: group.type }
                            );
                        }
                    });
                }
            },
            
            // 分组相似通知
            groupSimilarNotifications(notifications) {
                const groups = new Map();
                
                notifications.forEach(notification => {
                    const key = `${notification.type}_${notification.message.split(' ')[0]}`;
                    
                    if (!groups.has(key)) {
                        groups.set(key, {
                            type: notification.type,
                            message: notification.message,
                            count: 0
                        });
                    }
                    
                    groups.get(key).count++;
                });
                
                return Array.from(groups.values());
            },
            
            // 设置性能监控
            setupPerformanceMonitoring() {
                // 定期报告性能指标
                setInterval(() => {
                    this.reportPerformanceMetrics();
                }, 60000); // 每分钟报告一次
            },
            
            // 记录性能指标
            recordPerformanceMetric(metric, value) {
                if (!this.performanceMetrics[metric]) {
                    this.performanceMetrics[metric] = [];
                }
                
                this.performanceMetrics[metric].push(value);
                
                // 限制数组大小
                if (this.performanceMetrics[metric].length > 100) {
                    this.performanceMetrics[metric].shift();
                }
            },
            
            // 报告性能指标
            reportPerformanceMetrics() {
                const metrics = this.calculatePerformanceStats();
                
                console.log('讨论服务性能指标:', metrics);
                
                // 发送到分析服务
                if (this.env.services.analytics) {
                    this.env.services.analytics.track('discuss_service_performance', metrics);
                }
            },
            
            // 计算性能统计
            calculatePerformanceStats() {
                const stats = {};
                
                Object.entries(this.performanceMetrics).forEach(([key, values]) => {
                    if (Array.isArray(values) && values.length > 0) {
                        const sum = values.reduce((a, b) => a + b, 0);
                        stats[key] = {
                            average: sum / values.length,
                            min: Math.min(...values),
                            max: Math.max(...values),
                            count: values.length
                        };
                    } else {
                        stats[key] = values;
                    }
                });
                
                return stats;
            },
            
            // 创建错误处理器
            createErrorHandler() {
                return {
                    handle: (context, error) => {
                        console.error(`讨论服务错误 [${context}]:`, error);
                        
                        this.performanceMetrics.errorCount++;
                        
                        // 发送错误报告
                        if (this.env.services.error_service) {
                            this.env.services.error_service.report(error, {
                                context: `discuss_service_${context}`,
                                service: 'DiscussCoreCommon'
                            });
                        }
                    }
                };
            },
            
            // 设置错误处理
            setupErrorHandling() {
                // 全局错误捕获
                window.addEventListener('unhandledrejection', (event) => {
                    if (event.reason && event.reason.stack && 
                        event.reason.stack.includes('DiscussCoreCommon')) {
                        this.errorHandler.handle('unhandled_promise_rejection', event.reason);
                    }
                });
            },
            
            // 设置事件队列处理
            setupEventQueueProcessing() {
                // 当连接断开时，将事件加入队列
                this.busService.addEventListener("disconnect", () => {
                    this.serviceState.processingEvents = false;
                });
                
                // 当连接恢复时，处理队列中的事件
                this.busService.addEventListener("connect", () => {
                    this.processQueuedEvents();
                });
            },
            
            // 处理队列中的事件
            processQueuedEvents() {
                if (this.serviceState.processingEvents || this.serviceState.eventQueue.length === 0) {
                    return;
                }
                
                this.serviceState.processingEvents = true;
                
                const events = [...this.serviceState.eventQueue];
                this.serviceState.eventQueue = [];
                
                events.forEach(event => {
                    try {
                        this.processQueuedEvent(event);
                    } catch (error) {
                        this.errorHandler.handle('queued_event_processing', error);
                    }
                });
                
                this.serviceState.processingEvents = false;
            },
            
            // 处理队列中的单个事件
            processQueuedEvent(event) {
                switch (event.type) {
                    case 'channel_leave':
                        this.handleBatchChannelLeave([event.payload]);
                        break;
                    case 'new_message':
                        this.handleBatchNewMessages([event.payload]);
                        break;
                    default:
                        console.warn('未知的队列事件类型:', event.type);
                }
            },
            
            // 获取服务状态
            getServiceState() {
                return {
                    ...this.serviceState,
                    performanceMetrics: this.calculatePerformanceStats()
                };
            }
        };
        
        // 替换原始服务
        __exports.DiscussCoreCommon = EnhancedDiscussCoreCommon;
    }
};

// 应用服务功能增强
DiscussServiceEnhancer.enhanceDiscussService();
```

## 技术特点

### 1. 服务架构
- 多服务集成设计
- 事件驱动架构
- 响应式状态管理

### 2. 事件处理
- 总线事件订阅
- 批量事件处理
- 错误处理机制

### 3. 通知系统
- 国际化通知消息
- 条件通知显示
- 用户体验优化

### 4. 数据管理
- 存储服务集成
- 数据插入和更新
- 状态同步机制

## 设计模式

### 1. 服务模式 (Service Pattern)
- 标准的服务定义和注册
- 依赖注入机制

### 2. 观察者模式 (Observer Pattern)
- 事件监听和处理
- 状态变化响应

### 3. 策略模式 (Strategy Pattern)
- 不同事件的处理策略
- 可配置的行为模式

## 注意事项

1. **性能优化**: 避免频繁的事件处理影响性能
2. **错误处理**: 完善的异常处理和错误恢复
3. **内存管理**: 及时清理事件监听器和缓存
4. **数据一致性**: 确保数据更新的一致性

## 扩展建议

1. **批量处理**: 优化事件的批量处理机制
2. **缓存策略**: 实现智能的数据缓存策略
3. **性能监控**: 增强服务性能监控功能
4. **错误恢复**: 实现自动错误恢复机制
5. **插件系统**: 支持功能插件的动态加载

该服务是讨论应用的核心基础服务，提供了完整的事件处理和业务逻辑协调功能。

# Channel Commands - 频道命令

## 概述

`channel_commands.js` 实现了 Odoo 讨论应用中的频道命令注册，定义了用户可以在频道中使用的各种命令。该文件通过频道命令注册表添加了三个核心命令：帮助命令、离开命令和成员列表命令，每个命令都包含帮助文本、执行方法名、适用频道类型等配置，提供了讨论应用中命令行式交互的基础功能，是用户快速操作和获取信息的重要工具。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/channel_commands.js`
- **行数**: 29
- **模块**: `@mail/discuss/core/common/channel_commands`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'  // 国际化
'@web/core/registry'          // 注册表系统
```

## 命令注册

### 注册表获取

```javascript
const commandRegistry = registry.category("discuss.channel_commands");
```

**注册表特性**:
- 使用Web核心注册表系统
- 专门的频道命令分类
- 支持动态命令添加

### 1. 帮助命令 (help)

```javascript
commandRegistry.add("help", {
    help: _t("Show a helper message"),
    methodName: "execute_command_help",
})
```

**命令特性**:
- **命令名**: help
- **帮助文本**: 显示帮助消息
- **执行方法**: execute_command_help
- **适用范围**: 所有频道类型（默认）

### 2. 离开命令 (leave)

```javascript
commandRegistry.add("leave", {
    help: _t("Leave this channel"),
    methodName: "execute_command_leave",
})
```

**命令特性**:
- **命令名**: leave
- **帮助文本**: 离开当前频道
- **执行方法**: execute_command_leave
- **适用范围**: 所有频道类型（默认）

### 3. 成员列表命令 (who)

```javascript
commandRegistry.add("who", {
    channel_types: ["channel", "chat", "group"],
    help: _t("List users in the current channel"),
    methodName: "execute_command_who",
})
```

**命令特性**:
- **命令名**: who
- **帮助文本**: 列出当前频道的用户
- **执行方法**: execute_command_who
- **适用范围**: 频道、聊天、群组

## 使用场景

### 1. 频道命令增强

```javascript
// 频道命令增强功能
const ChannelCommandsEnhancer = {
    enhanceChannelCommands: () => {
        // 添加邀请命令
        commandRegistry.add("invite", {
            help: _t("Invite users to this channel"),
            methodName: "execute_command_invite",
            channel_types: ["channel", "group"],
            parameters: [
                {
                    name: "users",
                    type: "string",
                    required: true,
                    description: _t("User names or emails to invite")
                }
            ]
        });
        
        // 添加静音命令
        commandRegistry.add("mute", {
            help: _t("Mute notifications for this channel"),
            methodName: "execute_command_mute",
            parameters: [
                {
                    name: "duration",
                    type: "string",
                    required: false,
                    description: _t("Mute duration (e.g., 1h, 1d, 1w)")
                }
            ]
        });
        
        // 添加取消静音命令
        commandRegistry.add("unmute", {
            help: _t("Unmute notifications for this channel"),
            methodName: "execute_command_unmute"
        });
        
        // 添加主题设置命令
        commandRegistry.add("topic", {
            help: _t("Set or view channel topic"),
            methodName: "execute_command_topic",
            channel_types: ["channel", "group"],
            parameters: [
                {
                    name: "topic",
                    type: "string",
                    required: false,
                    description: _t("New topic for the channel")
                }
            ]
        });
        
        // 添加踢出用户命令
        commandRegistry.add("kick", {
            help: _t("Remove a user from the channel"),
            methodName: "execute_command_kick",
            channel_types: ["channel", "group"],
            permissions: ["admin", "moderator"],
            parameters: [
                {
                    name: "user",
                    type: "string",
                    required: true,
                    description: _t("User name to remove")
                }
            ]
        });
        
        // 添加禁言命令
        commandRegistry.add("ban", {
            help: _t("Ban a user from the channel"),
            methodName: "execute_command_ban",
            channel_types: ["channel"],
            permissions: ["admin", "moderator"],
            parameters: [
                {
                    name: "user",
                    type: "string",
                    required: true,
                    description: _t("User name to ban")
                },
                {
                    name: "duration",
                    type: "string",
                    required: false,
                    description: _t("Ban duration (e.g., 1h, 1d, permanent)")
                }
            ]
        });
        
        // 添加解禁命令
        commandRegistry.add("unban", {
            help: _t("Unban a user from the channel"),
            methodName: "execute_command_unban",
            channel_types: ["channel"],
            permissions: ["admin", "moderator"],
            parameters: [
                {
                    name: "user",
                    type: "string",
                    required: true,
                    description: _t("User name to unban")
                }
            ]
        });
        
        // 添加角色设置命令
        commandRegistry.add("role", {
            help: _t("Set user role in the channel"),
            methodName: "execute_command_role",
            channel_types: ["channel"],
            permissions: ["admin"],
            parameters: [
                {
                    name: "user",
                    type: "string",
                    required: true,
                    description: _t("User name")
                },
                {
                    name: "role",
                    type: "string",
                    required: true,
                    description: _t("Role: admin, moderator, member")
                }
            ]
        });
        
        // 添加频道信息命令
        commandRegistry.add("info", {
            help: _t("Show channel information"),
            methodName: "execute_command_info"
        });
        
        // 添加搜索命令
        commandRegistry.add("search", {
            help: _t("Search messages in the channel"),
            methodName: "execute_command_search",
            parameters: [
                {
                    name: "query",
                    type: "string",
                    required: true,
                    description: _t("Search query")
                },
                {
                    name: "limit",
                    type: "number",
                    required: false,
                    description: _t("Maximum number of results")
                }
            ]
        });
        
        // 添加清除命令
        commandRegistry.add("clear", {
            help: _t("Clear channel messages"),
            methodName: "execute_command_clear",
            permissions: ["admin"],
            parameters: [
                {
                    name: "count",
                    type: "number",
                    required: false,
                    description: _t("Number of messages to clear")
                }
            ]
        });
        
        // 添加公告命令
        commandRegistry.add("announce", {
            help: _t("Send an announcement to the channel"),
            methodName: "execute_command_announce",
            channel_types: ["channel"],
            permissions: ["admin", "moderator"],
            parameters: [
                {
                    name: "message",
                    type: "string",
                    required: true,
                    description: _t("Announcement message")
                }
            ]
        });
        
        // 添加统计命令
        commandRegistry.add("stats", {
            help: _t("Show channel statistics"),
            methodName: "execute_command_stats",
            permissions: ["admin", "moderator"]
        });
        
        // 添加导出命令
        commandRegistry.add("export", {
            help: _t("Export channel messages"),
            methodName: "execute_command_export",
            permissions: ["admin"],
            parameters: [
                {
                    name: "format",
                    type: "string",
                    required: false,
                    description: _t("Export format: json, csv, txt")
                },
                {
                    name: "days",
                    type: "number",
                    required: false,
                    description: _t("Number of days to export")
                }
            ]
        });
        
        // 添加备份命令
        commandRegistry.add("backup", {
            help: _t("Create channel backup"),
            methodName: "execute_command_backup",
            permissions: ["admin"]
        });
        
        // 添加恢复命令
        commandRegistry.add("restore", {
            help: _t("Restore channel from backup"),
            methodName: "execute_command_restore",
            permissions: ["admin"],
            parameters: [
                {
                    name: "backup_id",
                    type: "string",
                    required: true,
                    description: _t("Backup ID to restore")
                }
            ]
        });
        
        // 增强现有命令
        ChannelCommandsEnhancer.enhanceExistingCommands();
    },
    
    // 增强现有命令
    enhanceExistingCommands() {
        // 增强帮助命令
        const helpCommand = commandRegistry.get("help");
        const originalHelp = helpCommand.methodName;
        
        helpCommand.methodName = "execute_command_help_enhanced";
        helpCommand.parameters = [
            {
                name: "command",
                type: "string",
                required: false,
                description: _t("Specific command to get help for")
            }
        ];
        
        // 增强who命令
        const whoCommand = commandRegistry.get("who");
        whoCommand.parameters = [
            {
                name: "filter",
                type: "string",
                required: false,
                description: _t("Filter users: online, offline, admin, moderator")
            }
        ];
        
        // 增强leave命令
        const leaveCommand = commandRegistry.get("leave");
        leaveCommand.parameters = [
            {
                name: "reason",
                type: "string",
                required: false,
                description: _t("Reason for leaving")
            }
        ];
    },
    
    // 获取命令统计
    getCommandStatistics() {
        const commands = commandRegistry.getAll();
        
        const stats = {
            totalCommands: commands.length,
            byChannelType: {},
            byPermission: {},
            withParameters: 0,
            withoutParameters: 0
        };
        
        commands.forEach(command => {
            // 按频道类型统计
            const channelTypes = command.channel_types || ['all'];
            channelTypes.forEach(type => {
                stats.byChannelType[type] = (stats.byChannelType[type] || 0) + 1;
            });
            
            // 按权限统计
            const permissions = command.permissions || ['all'];
            permissions.forEach(permission => {
                stats.byPermission[permission] = (stats.byPermission[permission] || 0) + 1;
            });
            
            // 参数统计
            if (command.parameters && command.parameters.length > 0) {
                stats.withParameters++;
            } else {
                stats.withoutParameters++;
            }
        });
        
        return stats;
    },
    
    // 验证命令权限
    validateCommandPermission(commandId, user, channel) {
        const command = commandRegistry.get(commandId);
        
        if (!command) {
            return { valid: false, reason: '命令不存在' };
        }
        
        // 检查频道类型
        if (command.channel_types && !command.channel_types.includes(channel.channel_type)) {
            return { valid: false, reason: '此命令不适用于当前频道类型' };
        }
        
        // 检查权限
        if (command.permissions) {
            const userRole = ChannelCommandsEnhancer.getUserRole(user, channel);
            
            if (!command.permissions.includes(userRole)) {
                return { valid: false, reason: '权限不足' };
            }
        }
        
        return { valid: true };
    },
    
    // 获取用户角色
    getUserRole(user, channel) {
        if (!user || !channel) {
            return 'guest';
        }
        
        if (channel.model !== 'discuss.channel') {
            return 'member';
        }
        
        const member = channel.channelMembers?.find(
            member => member.persona.id === user.id
        );
        
        return member?.role || 'member';
    },
    
    // 解析命令参数
    parseCommandParameters(commandText, command) {
        const parts = commandText.trim().split(/\s+/);
        const commandName = parts[0].substring(1); // 移除 '/' 前缀
        const args = parts.slice(1);
        
        const parsedParams = {};
        const errors = [];
        
        if (command.parameters) {
            command.parameters.forEach((param, index) => {
                const value = args[index];
                
                if (param.required && !value) {
                    errors.push(`参数 '${param.name}' 是必需的`);
                    return;
                }
                
                if (value) {
                    // 类型转换
                    switch (param.type) {
                        case 'number':
                            const numValue = parseInt(value);
                            if (isNaN(numValue)) {
                                errors.push(`参数 '${param.name}' 必须是数字`);
                            } else {
                                parsedParams[param.name] = numValue;
                            }
                            break;
                        case 'string':
                        default:
                            parsedParams[param.name] = value;
                            break;
                    }
                }
            });
        }
        
        return { params: parsedParams, errors };
    },
    
    // 获取命令帮助
    getCommandHelp(commandId) {
        const command = commandRegistry.get(commandId);
        
        if (!command) {
            return null;
        }
        
        let help = `/${commandId} - ${command.help}`;
        
        if (command.parameters && command.parameters.length > 0) {
            help += '\n参数:';
            command.parameters.forEach(param => {
                const required = param.required ? '(必需)' : '(可选)';
                help += `\n  ${param.name} ${required}: ${param.description}`;
            });
        }
        
        if (command.channel_types) {
            help += `\n适用频道: ${command.channel_types.join(', ')}`;
        }
        
        if (command.permissions) {
            help += `\n需要权限: ${command.permissions.join(', ')}`;
        }
        
        return help;
    },
    
    // 获取所有命令帮助
    getAllCommandsHelp() {
        const commands = commandRegistry.getAll();
        
        let help = '可用命令:\n';
        
        commands.forEach(command => {
            help += `\n/${command.id} - ${command.help}`;
        });
        
        help += '\n\n使用 /help <命令名> 获取特定命令的详细帮助';
        
        return help;
    },
    
    // 搜索命令
    searchCommands(query) {
        const commands = commandRegistry.getAll();
        const normalizedQuery = query.toLowerCase();
        
        return commands.filter(command => {
            return command.id.toLowerCase().includes(normalizedQuery) ||
                   command.help.toLowerCase().includes(normalizedQuery);
        });
    },
    
    // 获取命令建议
    getCommandSuggestions(partialCommand) {
        const commands = commandRegistry.getAll();
        const normalized = partialCommand.toLowerCase();
        
        return commands
            .filter(command => command.id.toLowerCase().startsWith(normalized))
            .map(command => ({
                id: command.id,
                help: command.help,
                completion: `/${command.id}`
            }));
    }
};

// 应用频道命令增强
ChannelCommandsEnhancer.enhanceChannelCommands();
```

## 技术特点

### 1. 注册表机制
- 统一的命令注册管理
- 动态命令添加支持
- 分类化的命令组织

### 2. 国际化支持
- 完整的多语言支持
- 动态文本翻译
- 用户友好的帮助信息

### 3. 命令配置
- 灵活的命令属性配置
- 频道类型限制
- 执行方法映射

### 4. 扩展性设计
- 易于添加新命令
- 可配置的命令行为
- 模块化的命令结构

## 设计模式

### 1. 注册表模式 (Registry Pattern)
- 统一的命令注册和管理
- 动态命令配置

### 2. 命令模式 (Command Pattern)
- 命令的封装和执行
- 统一的命令接口

### 3. 策略模式 (Strategy Pattern)
- 不同命令的执行策略
- 可配置的命令行为

## 注意事项

1. **权限控制**: 确保命令权限的正确验证
2. **参数验证**: 完善的命令参数验证机制
3. **错误处理**: 提供清晰的错误信息和帮助
4. **性能优化**: 避免频繁的命令查找和验证

## 扩展建议

1. **更多命令**: 添加更多实用的频道命令
2. **权限系统**: 实现更细粒度的权限控制
3. **参数验证**: 增强命令参数的验证和转换
4. **命令历史**: 记录和管理命令使用历史
5. **自动补全**: 实现命令的智能自动补全

该文件为讨论应用提供了基础的命令行式交互功能，是用户快速操作和获取信息的重要工具。

# Suggestion Service Patch - 建议服务补丁

## 概述

`suggestion_service_patch.js` 实现了对 Odoo 邮件系统中建议服务的补丁扩展，专门为讨论频道添加了命令建议功能。该补丁通过Odoo的补丁机制扩展了SuggestionService，添加了对"/"分隔符的支持、频道命令搜索、智能排序等功能，为用户在输入频道命令时提供智能的自动补全和建议，提升了用户体验和操作效率，是讨论应用中命令行交互的重要辅助功能。

## 文件信息
- **路径**: `/mail/static/src/discuss/core/common/suggestion_service_patch.js`
- **行数**: 91
- **模块**: `@mail/discuss/core/common/suggestion_service_patch`

## 依赖关系

```javascript
// 服务依赖
'@mail/core/common/suggestion_service'  // 建议服务
'@mail/utils/common/format'             // 格式化工具
'@web/core/registry'                    // 注册表系统
'@web/core/utils/patch'                 // 补丁工具
```

## 补丁定义

### SuggestionService 补丁

```javascript
patch(SuggestionService.prototype, {
    getSupportedDelimiters(thread) {
        const res = super.getSupportedDelimiters(thread);
        return thread?.model === "discuss.channel" ? [...res, ["/", 0]] : res;
    },
    
    searchSuggestions({ delimiter, term }, { thread, sort = false } = {}) {
        if (delimiter === "/") {
            return this.searchChannelCommand(cleanTerm(term), thread, sort);
        }
        return super.searchSuggestions(...arguments);
    },
    
    searchChannelCommand(cleanedSearchTerm, thread, sort) {
        // 命令搜索实现
    }
});
```

**补丁特性**:
- 扩展支持的分隔符
- 添加命令搜索功能
- 智能排序算法
- 频道类型过滤

## 核心功能

### 1. 分隔符支持扩展

```javascript
getSupportedDelimiters(thread) {
    const res = super.getSupportedDelimiters(thread);
    return thread?.model === "discuss.channel" ? [...res, ["/", 0]] : res;
}
```

**分隔符功能**:
- **继承原有**: 保留原有的分隔符支持
- **条件添加**: 仅在讨论频道中添加"/"分隔符
- **优先级设置**: "/"分隔符优先级为0

### 2. 建议搜索路由

```javascript
searchSuggestions({ delimiter, term }, { thread, sort = false } = {}) {
    if (delimiter === "/") {
        return this.searchChannelCommand(cleanTerm(term), thread, sort);
    }
    return super.searchSuggestions(...arguments);
}
```

**路由功能**:
- **分隔符检查**: 检查是否为"/"分隔符
- **命令搜索**: 调用专门的命令搜索方法
- **术语清理**: 使用cleanTerm清理搜索词
- **回退机制**: 其他分隔符使用原有逻辑

### 3. 频道命令搜索

```javascript
searchChannelCommand(cleanedSearchTerm, thread, sort) {
    if (!thread.model === "discuss.channel") {
        return;
    }
    
    const commands = commandRegistry
        .getEntries()
        .filter(([name, command]) => {
            if (!cleanTerm(name).includes(cleanedSearchTerm)) {
                return false;
            }
            if (command.channel_types) {
                return command.channel_types.includes(thread.channel_type);
            }
            return true;
        })
        .map(([name, command]) => {
            return {
                channel_types: command.channel_types,
                help: command.help,
                id: command.id,
                name,
            };
        });
    
    return {
        type: "ChannelCommand",
        suggestions: sort ? commands.sort(sortFunc) : commands,
    };
}
```

**搜索功能**:
- **模型验证**: 仅在讨论频道中生效
- **命令过滤**: 按名称和频道类型过滤命令
- **数据映射**: 提取命令的关键信息
- **结果封装**: 返回标准的建议结果格式

### 4. 智能排序算法

```javascript
const sortFunc = (c1, c2) => {
    if (c1.channel_types && !c2.channel_types) {
        return -1;
    }
    if (!c1.channel_types && c2.channel_types) {
        return 1;
    }
    const cleanedName1 = cleanTerm(c1.name);
    const cleanedName2 = cleanTerm(c2.name);
    if (
        cleanedName1.startsWith(cleanedSearchTerm) &&
        !cleanedName2.startsWith(cleanedSearchTerm)
    ) {
        return -1;
    }
    if (
        !cleanedName1.startsWith(cleanedSearchTerm) &&
        cleanedName2.startsWith(cleanedSearchTerm)
    ) {
        return 1;
    }
    if (cleanedName1 < cleanedName2) {
        return -1;
    }
    if (cleanedName1 > cleanedName2) {
        return 1;
    }
    return c1.id - c2.id;
};
```

**排序逻辑**:
- **频道类型优先**: 有频道类型限制的命令优先
- **前缀匹配优先**: 以搜索词开头的命令优先
- **字母排序**: 按命令名称字母顺序排序
- **ID回退**: 名称相同时按ID排序

## 使用场景

### 1. 建议服务增强

```javascript
// 建议服务增强功能
const SuggestionServiceEnhancer = {
    enhanceSuggestionService: () => {
        const EnhancedSuggestionPatch = {
            // 增强的分隔符支持
            getSupportedDelimiters(thread) {
                const res = super.getSupportedDelimiters(thread);
                
                if (thread?.model === "discuss.channel") {
                    // 添加更多分隔符支持
                    return [
                        ...res,
                        ["/", 0],      // 命令分隔符
                        ["!", 1],      // 快捷操作分隔符
                        ["?", 2],      // 帮助查询分隔符
                        ["#", 3]       // 标签分隔符
                    ];
                }
                
                return res;
            },
            
            // 增强的建议搜索
            searchSuggestions({ delimiter, term }, options = {}) {
                const { thread, sort = false, limit = 10, context = {} } = options;
                
                // 记录搜索统计
                this.recordSearchStatistics(delimiter, term, thread);
                
                switch (delimiter) {
                    case "/":
                        return this.searchChannelCommand(cleanTerm(term), thread, sort, limit);
                    case "!":
                        return this.searchQuickActions(cleanTerm(term), thread, sort, limit);
                    case "?":
                        return this.searchHelpTopics(cleanTerm(term), thread, sort, limit);
                    case "#":
                        return this.searchTags(cleanTerm(term), thread, sort, limit);
                    default:
                        return super.searchSuggestions({ delimiter, term }, options);
                }
            },
            
            // 增强的频道命令搜索
            searchChannelCommand(cleanedSearchTerm, thread, sort, limit = 10) {
                if (thread?.model !== "discuss.channel") {
                    return { type: "ChannelCommand", suggestions: [] };
                }
                
                // 获取用户权限
                const userPermissions = this.getUserPermissions(thread);
                
                // 获取命令历史
                const commandHistory = this.getCommandHistory(thread);
                
                const commands = commandRegistry
                    .getEntries()
                    .filter(([name, command]) => {
                        // 名称匹配检查
                        if (!cleanTerm(name).includes(cleanedSearchTerm)) {
                            return false;
                        }
                        
                        // 频道类型检查
                        if (command.channel_types && !command.channel_types.includes(thread.channel_type)) {
                            return false;
                        }
                        
                        // 权限检查
                        if (command.permissions && !this.hasRequiredPermissions(command.permissions, userPermissions)) {
                            return false;
                        }
                        
                        return true;
                    })
                    .map(([name, command]) => {
                        const usage = commandHistory[name] || 0;
                        
                        return {
                            channel_types: command.channel_types,
                            help: command.help,
                            id: command.id,
                            name,
                            usage,
                            permissions: command.permissions,
                            parameters: command.parameters || [],
                            lastUsed: commandHistory[`${name}_last_used`] || 0
                        };
                    });
                
                // 增强排序
                const sortedCommands = sort ? this.sortCommandsEnhanced(commands, cleanedSearchTerm) : commands;
                
                return {
                    type: "ChannelCommand",
                    suggestions: sortedCommands.slice(0, limit),
                    total: commands.length
                };
            },
            
            // 搜索快捷操作
            searchQuickActions(cleanedSearchTerm, thread, sort, limit = 10) {
                const quickActions = [
                    {
                        id: "mute",
                        name: "mute",
                        help: _t("Quick mute notifications"),
                        icon: "fa-bell-slash",
                        action: "mute_thread"
                    },
                    {
                        id: "pin",
                        name: "pin",
                        help: _t("Pin this thread"),
                        icon: "fa-thumbtack",
                        action: "pin_thread"
                    },
                    {
                        id: "star",
                        name: "star",
                        help: _t("Star this thread"),
                        icon: "fa-star",
                        action: "star_thread"
                    },
                    {
                        id: "archive",
                        name: "archive",
                        help: _t("Archive this thread"),
                        icon: "fa-archive",
                        action: "archive_thread"
                    }
                ];
                
                const filtered = quickActions.filter(action =>
                    cleanTerm(action.name).includes(cleanedSearchTerm)
                );
                
                return {
                    type: "QuickAction",
                    suggestions: filtered.slice(0, limit)
                };
            },
            
            // 搜索帮助主题
            searchHelpTopics(cleanedSearchTerm, thread, sort, limit = 10) {
                const helpTopics = [
                    {
                        id: "commands",
                        name: "commands",
                        help: _t("Available commands"),
                        category: "basic"
                    },
                    {
                        id: "formatting",
                        name: "formatting",
                        help: _t("Message formatting"),
                        category: "basic"
                    },
                    {
                        id: "mentions",
                        name: "mentions",
                        help: _t("User and channel mentions"),
                        category: "basic"
                    },
                    {
                        id: "shortcuts",
                        name: "shortcuts",
                        help: _t("Keyboard shortcuts"),
                        category: "advanced"
                    }
                ];
                
                const filtered = helpTopics.filter(topic =>
                    cleanTerm(topic.name).includes(cleanedSearchTerm) ||
                    cleanTerm(topic.help).includes(cleanedSearchTerm)
                );
                
                return {
                    type: "HelpTopic",
                    suggestions: filtered.slice(0, limit)
                };
            },
            
            // 搜索标签
            searchTags(cleanedSearchTerm, thread, sort, limit = 10) {
                // 获取频道中使用的标签
                const channelTags = this.getChannelTags(thread);
                
                const filtered = channelTags.filter(tag =>
                    cleanTerm(tag.name).includes(cleanedSearchTerm)
                );
                
                return {
                    type: "Tag",
                    suggestions: filtered.slice(0, limit)
                };
            },
            
            // 增强的命令排序
            sortCommandsEnhanced(commands, searchTerm) {
                return commands.sort((c1, c2) => {
                    // 使用频率权重
                    const usageWeight = (c2.usage || 0) - (c1.usage || 0);
                    if (Math.abs(usageWeight) > 5) {
                        return usageWeight > 0 ? 1 : -1;
                    }
                    
                    // 最近使用权重
                    const recentWeight = (c2.lastUsed || 0) - (c1.lastUsed || 0);
                    if (Math.abs(recentWeight) > 86400000) { // 1天
                        return recentWeight > 0 ? 1 : -1;
                    }
                    
                    // 频道类型特定性
                    if (c1.channel_types && !c2.channel_types) {
                        return -1;
                    }
                    if (!c1.channel_types && c2.channel_types) {
                        return 1;
                    }
                    
                    // 前缀匹配优先
                    const cleanedName1 = cleanTerm(c1.name);
                    const cleanedName2 = cleanTerm(c2.name);
                    
                    const starts1 = cleanedName1.startsWith(searchTerm);
                    const starts2 = cleanedName2.startsWith(searchTerm);
                    
                    if (starts1 && !starts2) return -1;
                    if (!starts1 && starts2) return 1;
                    
                    // 字母排序
                    return cleanedName1.localeCompare(cleanedName2);
                });
            },
            
            // 获取用户权限
            getUserPermissions(thread) {
                const user = this.env.services['mail.store'].self;
                
                if (!user || !thread) {
                    return [];
                }
                
                if (thread.model !== 'discuss.channel') {
                    return ['member'];
                }
                
                const member = thread.channelMembers?.find(
                    member => member.persona.id === user.id
                );
                
                const role = member?.role || 'member';
                const permissions = [role];
                
                if (role === 'admin') {
                    permissions.push('moderator', 'member');
                } else if (role === 'moderator') {
                    permissions.push('member');
                }
                
                return permissions;
            },
            
            // 检查所需权限
            hasRequiredPermissions(requiredPermissions, userPermissions) {
                return requiredPermissions.some(permission =>
                    userPermissions.includes(permission)
                );
            },
            
            // 获取命令历史
            getCommandHistory(thread) {
                try {
                    const historyKey = `command_history_${thread.id}`;
                    const history = JSON.parse(
                        localStorage.getItem(historyKey) || '{}'
                    );
                    return history;
                } catch (error) {
                    console.warn('获取命令历史失败:', error);
                    return {};
                }
            },
            
            // 获取频道标签
            getChannelTags(thread) {
                // 这里可以从消息中提取标签
                const tags = [
                    { id: 1, name: "urgent", usage: 10 },
                    { id: 2, name: "question", usage: 5 },
                    { id: 3, name: "announcement", usage: 3 }
                ];
                
                return tags;
            },
            
            // 记录搜索统计
            recordSearchStatistics(delimiter, term, thread) {
                try {
                    const stats = JSON.parse(
                        localStorage.getItem('suggestion_search_stats') || '{}'
                    );
                    
                    const key = `${delimiter}_${thread?.id || 'global'}`;
                    
                    if (!stats[key]) {
                        stats[key] = { count: 0, terms: {} };
                    }
                    
                    stats[key].count++;
                    stats[key].terms[term] = (stats[key].terms[term] || 0) + 1;
                    
                    localStorage.setItem('suggestion_search_stats', JSON.stringify(stats));
                } catch (error) {
                    console.warn('记录搜索统计失败:', error);
                }
            },
            
            // 获取搜索统计
            getSearchStatistics() {
                try {
                    return JSON.parse(
                        localStorage.getItem('suggestion_search_stats') || '{}'
                    );
                } catch (error) {
                    return {};
                }
            },
            
            // 获取热门建议
            getPopularSuggestions(delimiter, thread, limit = 5) {
                const stats = this.getSearchStatistics();
                const key = `${delimiter}_${thread?.id || 'global'}`;
                
                if (!stats[key] || !stats[key].terms) {
                    return [];
                }
                
                return Object.entries(stats[key].terms)
                    .sort(([, a], [, b]) => b - a)
                    .slice(0, limit)
                    .map(([term, count]) => ({ term, count }));
            }
        };
        
        patch(SuggestionService.prototype, EnhancedSuggestionPatch);
    }
};

// 应用建议服务增强
SuggestionServiceEnhancer.enhanceSuggestionService();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 智能搜索
- 多条件过滤机制
- 智能排序算法
- 上下文感知搜索

### 3. 用户体验
- 实时建议提供
- 相关性排序
- 类型化建议结果

### 4. 扩展性设计
- 可配置的分隔符支持
- 模块化的搜索逻辑
- 灵活的排序策略

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同分隔符的处理策略
- 可配置的排序策略

### 3. 工厂模式 (Factory Pattern)
- 建议对象的创建
- 类型化的建议生成

## 注意事项

1. **性能优化**: 避免频繁的搜索和排序操作
2. **数据一致性**: 确保命令数据的实时性
3. **用户体验**: 提供快速响应的建议
4. **内存管理**: 及时清理搜索缓存

## 扩展建议

1. **更多分隔符**: 支持更多类型的建议分隔符
2. **学习算法**: 实现基于用户行为的学习算法
3. **缓存优化**: 优化搜索结果的缓存机制
4. **个性化**: 提供个性化的建议排序
5. **分析功能**: 添加建议使用的分析功能

该补丁为讨论应用提供了智能的命令建议功能，大大提升了用户的操作效率和体验。

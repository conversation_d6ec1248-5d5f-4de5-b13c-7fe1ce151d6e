# GIF Picker - GIF选择器

## 概述

`gif_picker.js` 实现了 Odoo 讨论应用中的GIF选择器组件，提供了完整的GIF搜索、浏览、收藏和选择功能。该组件集成了Tenor API，支持分类浏览、关键词搜索、收藏管理、瀑布流布局等特性，为用户提供了丰富的GIF表情包选择体验，是讨论应用中GIF功能的核心界面组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/gif_picker/common/gif_picker.js`
- **行数**: 307
- **模块**: `@mail/discuss/gif_picker/common/gif_picker`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                      // OWL 框架
'@mail/utils/common/hooks'       // 邮件钩子
'@web/core/user'                 // 用户服务
'@web/core/utils/hooks'          // Web核心钩子
'@web/core/utils/timing'         // 时间工具
'@web/core/network/rpc'          // RPC网络服务
```

## 类型定义

### TenorCategory
```javascript
/**
 * @typedef {Object} TenorCategory
 * @property {string} searchterm - 搜索词
 * @property {string} path - 路径
 * @property {string} image - 图片URL
 * @property {string} name - 分类名称
 */
```

### TenorGif
```javascript
/**
 * @typedef {Object} TenorGif
 * @property {string} id - GIF ID
 * @property {string} title - 标题
 * @property {number} created - 创建时间
 * @property {string} content_description - 内容描述
 * @property {string} itemurl - 项目URL
 * @property {string} url - GIF URL
 * @property {string[]} tags - 标签
 * @property {string[]} flags - 标记
 * @property {boolean} hasaudio - 是否有音频
 * @property {{ tinygif: TenorMediaFormat }} media_formats - 媒体格式
 */
```

## 组件定义

### GifPicker 类

```javascript
const GifPicker = class GifPicker extends Component {
    static template = "discuss.GifPicker";
    static props = ["PICKERS?", "className?", "close?", "onSelect", "state?"];
}
```

**组件特性**:
- 使用专用模板
- 支持选择回调
- 可选的关闭功能
- 状态管理支持

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.orm = useService("orm");
    this.store = useState(useService("mail.store"));
    this.sequential = useSequential();
    useAutofocus();
    useOnBottomScrolled("scroller", () => {
        if (!this.state.showCategories) {
            this.state.loadingGif = true;
            if (!this.showFavorite) {
                this.search();
            } else {
                this.loadFavoritesDebounced(this.offset);
            }
        }
    }, 300);
    // 状态初始化...
}
```

**初始化功能**:
- **服务注入**: 注入ORM、存储等服务
- **自动聚焦**: 设置自动聚焦
- **滚动监听**: 监听底部滚动实现无限加载
- **状态管理**: 初始化组件状态
- **防抖处理**: 设置防抖加载

### 2. 分类加载

```javascript
async loadCategories() {
    try {
        let { language, region } = new Intl.Locale(user.lang);
        if (!region && language === "sr") {
            region = "RS";
        }
        const { tags } = await rpc("/discuss/gif/categories", {
            country: region,
            locale: `${language}_${region}`,
        }, { silent: true });
        if (tags) {
            this.state.categories = tags;
        }
    } catch {
        this.state.loadingError = true;
    }
}
```

**分类加载功能**:
- **国际化支持**: 根据用户语言和地区加载分类
- **RPC调用**: 调用后端获取分类数据
- **错误处理**: 处理加载失败情况
- **静默请求**: 使用静默模式避免错误提示

### 3. GIF搜索

```javascript
async search() {
    if (!this.searchTerm) {
        return;
    }
    try {
        let { language, region } = new Intl.Locale(user.lang);
        const params = {
            country: region,
            locale: `${language}_${region}`,
            search_term: this.searchTerm,
        };
        if (this.next) {
            params.position = this.next;
        }
        const res = await this.sequential(() => {
            this.state.loadingGif = true;
            const res = rpc("/discuss/gif/search", params, { silent: true });
            this.state.loadingGif = false;
            return res;
        });
        // 处理搜索结果...
    } catch {
        this.state.loadingError = true;
    }
}
```

**搜索功能**:
- **关键词搜索**: 根据搜索词查找GIF
- **分页支持**: 支持分页加载更多结果
- **顺序执行**: 使用sequential确保请求顺序
- **加载状态**: 管理加载状态显示

### 4. 瀑布流布局

```javascript
pushGif(gif) {
    if (this.state.evenGif.columnSize <= this.state.oddGif.columnSize) {
        this.state.evenGif.gifs.set(gif.id, gif);
        this.state.evenGif.columnSize += gif.media_formats.tinygif.dims[1];
    } else {
        this.state.oddGif.gifs.set(gif.id, gif);
        this.state.oddGif.columnSize += gif.media_formats.tinygif.dims[1];
    }
}
```

**布局功能**:
- **双列布局**: 使用奇偶列实现瀑布流
- **高度平衡**: 根据列高度平衡分配GIF
- **动态添加**: 动态添加GIF到合适的列
- **尺寸计算**: 根据GIF尺寸计算列高度

### 5. 收藏管理

```javascript
async onClickFavorite(gif) {
    if (!this.isFavorite(gif)) {
        this.state.favorites.gifs.push(gif);
        await this.orm.silent.create("discuss.gif.favorite", [{ tenor_gif_id: gif.id }]);
    } else {
        const index = this.state.favorites.gifs.findIndex(({ id }) => id === gif.id);
        if (index >= 0) {
            this.state.favorites.gifs.splice(index, 1);
        }
        await rpc("/discuss/gif/remove_favorite", { tenor_gif_id: gif.id }, { silent: true });
    }
}
```

**收藏功能**:
- **收藏切换**: 添加或移除GIF收藏
- **本地更新**: 立即更新本地收藏状态
- **后端同步**: 同步收藏状态到后端
- **状态检查**: 检查GIF是否已收藏

## 使用场景

### 1. GIF选择器增强

```javascript
// GIF选择器增强功能
const GifPickerEnhancer = {
    enhanceGifPicker: () => {
        const EnhancedGifPicker = class extends GifPicker {
            setup() {
                super.setup();
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.dialogService = useService("dialog");
                this.httpService = useService("http");
                
                // 增强的状态
                this.enhancedState = useState({
                    searchHistory: [],
                    recentSearches: [],
                    popularGifs: [],
                    customCategories: [],
                    viewMode: 'grid', // grid, list, masonry
                    sortMode: 'relevance', // relevance, date, popularity
                    filterMode: 'all', // all, animated, static
                    previewMode: false,
                    fullscreenMode: false
                });
                
                // 设置增强功能
                this.setupEnhancedFeatures();
            },
            
            // 设置增强功能
            setupEnhancedFeatures() {
                // 设置搜索历史
                this.setupSearchHistory();
                
                // 设置热门GIF
                this.setupPopularGifs();
                
                // 设置自定义分类
                this.setupCustomCategories();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置拖拽功能
                this.setupDragAndDrop();
            },
            
            // 增强的搜索功能
            async search() {
                if (!this.searchTerm) {
                    return;
                }
                
                try {
                    // 记录搜索历史
                    this.recordSearch(this.searchTerm);
                    
                    // 显示搜索状态
                    this.showSearchStatus();
                    
                    // 执行原有搜索
                    await super.search();
                    
                    // 应用过滤和排序
                    this.applyFiltersAndSorting();
                    
                    // 预加载相关GIF
                    this.preloadRelatedGifs();
                    
                } catch (error) {
                    console.error('搜索GIF失败:', error);
                    this.handleSearchError(error);
                }
            },
            
            // 增强的分类加载
            async loadCategories() {
                try {
                    // 加载默认分类
                    await super.loadCategories();
                    
                    // 加载自定义分类
                    await this.loadCustomCategories();
                    
                    // 加载热门分类
                    await this.loadPopularCategories();
                    
                    // 排序分类
                    this.sortCategories();
                    
                } catch (error) {
                    console.error('加载分类失败:', error);
                    this.handleCategoryLoadError(error);
                }
            },
            
            // 增强的GIF点击处理
            onClickGif(gif) {
                try {
                    // 记录点击
                    this.recordGifClick(gif);
                    
                    // 更新最近使用
                    this.updateRecentGifs(gif);
                    
                    // 预览模式处理
                    if (this.enhancedState.previewMode) {
                        this.showGifPreview(gif);
                        return;
                    }
                    
                    // 执行原有逻辑
                    super.onClickGif(gif);
                    
                } catch (error) {
                    console.error('处理GIF点击失败:', error);
                    this.handleGifClickError(error);
                }
            },
            
            // 增强的收藏处理
            async onClickFavorite(gif) {
                try {
                    // 显示加载状态
                    this.showFavoriteLoading(gif);
                    
                    // 执行原有逻辑
                    await super.onClickFavorite(gif);
                    
                    // 显示成功提示
                    this.showFavoriteSuccess(gif);
                    
                    // 更新收藏统计
                    this.updateFavoriteStats();
                    
                } catch (error) {
                    console.error('处理收藏失败:', error);
                    this.handleFavoriteError(error, gif);
                } finally {
                    this.hideFavoriteLoading(gif);
                }
            },
            
            // 切换视图模式
            switchViewMode(mode) {
                this.enhancedState.viewMode = mode;
                this.reorganizeGifs();
                this.saveUserPreferences();
            },
            
            // 切换排序模式
            switchSortMode(mode) {
                this.enhancedState.sortMode = mode;
                this.sortCurrentGifs();
                this.saveUserPreferences();
            },
            
            // 切换过滤模式
            switchFilterMode(mode) {
                this.enhancedState.filterMode = mode;
                this.filterCurrentGifs();
                this.saveUserPreferences();
            },
            
            // 显示GIF预览
            showGifPreview(gif) {
                this.dialogService.add(GifPreviewDialog, {
                    gif: gif,
                    onSelect: () => this.onClickGif(gif),
                    onFavorite: () => this.onClickFavorite(gif),
                    onShare: () => this.shareGif(gif),
                    onDownload: () => this.downloadGif(gif)
                });
            },
            
            // 分享GIF
            async shareGif(gif) {
                try {
                    if (navigator.share) {
                        await navigator.share({
                            title: gif.title,
                            text: gif.content_description,
                            url: gif.url
                        });
                    } else {
                        await navigator.clipboard.writeText(gif.url);
                        this.notificationService.add('GIF链接已复制到剪贴板', { type: 'success' });
                    }
                } catch (error) {
                    console.error('分享GIF失败:', error);
                    this.notificationService.add('分享失败', { type: 'error' });
                }
            },
            
            // 下载GIF
            async downloadGif(gif) {
                try {
                    const response = await fetch(gif.url);
                    const blob = await response.blob();
                    const url = URL.createObjectURL(blob);
                    
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${gif.title || 'gif'}.gif`;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                    
                    this.notificationService.add('GIF下载成功', { type: 'success' });
                } catch (error) {
                    console.error('下载GIF失败:', error);
                    this.notificationService.add('下载失败', { type: 'error' });
                }
            },
            
            // 记录搜索
            recordSearch(searchTerm) {
                try {
                    const history = this.enhancedState.searchHistory;
                    
                    // 移除重复项
                    const existingIndex = history.indexOf(searchTerm);
                    if (existingIndex !== -1) {
                        history.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    history.unshift(searchTerm);
                    
                    // 限制历史记录数量
                    if (history.length > 20) {
                        history.splice(20);
                    }
                    
                    // 保存到本地存储
                    this.saveSearchHistory();
                    
                } catch (error) {
                    console.warn('记录搜索失败:', error);
                }
            },
            
            // 记录GIF点击
            recordGifClick(gif) {
                try {
                    const clicks = JSON.parse(
                        localStorage.getItem('gif_click_history') || '[]'
                    );
                    
                    clicks.push({
                        gifId: gif.id,
                        gifTitle: gif.title,
                        searchTerm: this.searchTerm,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个点击
                    if (clicks.length > 100) {
                        clicks.splice(0, clicks.length - 100);
                    }
                    
                    localStorage.setItem('gif_click_history', JSON.stringify(clicks));
                } catch (error) {
                    console.warn('记录GIF点击失败:', error);
                }
            },
            
            // 更新最近使用的GIF
            updateRecentGifs(gif) {
                try {
                    const recent = JSON.parse(
                        localStorage.getItem('recent_gifs') || '[]'
                    );
                    
                    // 移除已存在的
                    const existingIndex = recent.findIndex(g => g.id === gif.id);
                    if (existingIndex !== -1) {
                        recent.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    recent.unshift({
                        id: gif.id,
                        title: gif.title,
                        url: gif.url,
                        timestamp: Date.now()
                    });
                    
                    // 限制数量
                    if (recent.length > 50) {
                        recent.splice(50);
                    }
                    
                    localStorage.setItem('recent_gifs', JSON.stringify(recent));
                } catch (error) {
                    console.warn('更新最近GIF失败:', error);
                }
            },
            
            // 应用过滤和排序
            applyFiltersAndSorting() {
                // 过滤GIF
                this.filterCurrentGifs();
                
                // 排序GIF
                this.sortCurrentGifs();
                
                // 重新布局
                this.reorganizeGifs();
            },
            
            // 过滤当前GIF
            filterCurrentGifs() {
                const filter = this.enhancedState.filterMode;
                
                if (filter === 'all') {
                    return;
                }
                
                // 实现过滤逻辑
                const allGifs = [
                    ...this.state.evenGif.gifs.values(),
                    ...this.state.oddGif.gifs.values()
                ];
                
                const filteredGifs = allGifs.filter(gif => {
                    switch (filter) {
                        case 'animated':
                            return gif.media_formats.tinygif.duration > 0;
                        case 'static':
                            return gif.media_formats.tinygif.duration === 0;
                        default:
                            return true;
                    }
                });
                
                // 重新分配到列
                this.clear();
                filteredGifs.forEach(gif => this.pushGif(gif));
            },
            
            // 排序当前GIF
            sortCurrentGifs() {
                const sort = this.enhancedState.sortMode;
                
                if (sort === 'relevance') {
                    return; // 默认已按相关性排序
                }
                
                const allGifs = [
                    ...this.state.evenGif.gifs.values(),
                    ...this.state.oddGif.gifs.values()
                ];
                
                allGifs.sort((a, b) => {
                    switch (sort) {
                        case 'date':
                            return b.created - a.created;
                        case 'popularity':
                            return (b.views || 0) - (a.views || 0);
                        default:
                            return 0;
                    }
                });
                
                // 重新分配到列
                this.clear();
                allGifs.forEach(gif => this.pushGif(gif));
            },
            
            // 重新组织GIF
            reorganizeGifs() {
                const viewMode = this.enhancedState.viewMode;
                
                switch (viewMode) {
                    case 'list':
                        this.organizeAsList();
                        break;
                    case 'masonry':
                        this.organizeAsMasonry();
                        break;
                    default:
                        // 保持网格布局
                        break;
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const prefs = {
                        viewMode: this.enhancedState.viewMode,
                        sortMode: this.enhancedState.sortMode,
                        filterMode: this.enhancedState.filterMode,
                        previewMode: this.enhancedState.previewMode
                    };
                    
                    localStorage.setItem('gif_picker_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('gif_picker_preferences') || '{}'
                    );
                    
                    this.enhancedState.viewMode = prefs.viewMode || 'grid';
                    this.enhancedState.sortMode = prefs.sortMode || 'relevance';
                    this.enhancedState.filterMode = prefs.filterMode || 'all';
                    this.enhancedState.previewMode = prefs.previewMode || false;
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 获取GIF选择器统计
            getGifPickerStatistics() {
                try {
                    return {
                        searchHistory: this.enhancedState.searchHistory.length,
                        clickHistory: JSON.parse(localStorage.getItem('gif_click_history') || '[]').length,
                        recentGifs: JSON.parse(localStorage.getItem('recent_gifs') || '[]').length,
                        favoriteGifs: this.state.favorites.gifs.length,
                        currentViewMode: this.enhancedState.viewMode,
                        currentSortMode: this.enhancedState.sortMode,
                        currentFilterMode: this.enhancedState.filterMode
                    };
                } catch (error) {
                    return {
                        searchHistory: 0,
                        clickHistory: 0,
                        recentGifs: 0,
                        favoriteGifs: 0,
                        currentViewMode: 'grid',
                        currentSortMode: 'relevance',
                        currentFilterMode: 'all'
                    };
                }
            }
        };
        
        // 替换原始组件
        __exports.GifPicker = EnhancedGifPicker;
    }
};

// 应用GIF选择器增强
GifPickerEnhancer.enhanceGifPicker();
```

## 技术特点

### 1. 组件化设计
- 清晰的组件结构
- 类型定义完善
- 模板驱动渲染

### 2. 国际化支持
- 多语言分类加载
- 地区化内容支持
- 本地化用户体验

### 3. 性能优化
- 瀑布流布局
- 无限滚动加载
- 防抖处理机制

### 4. 用户体验
- 收藏功能
- 搜索历史
- 加载状态管理

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的UI组件
- 清晰的组件接口

### 2. 状态模式 (State Pattern)
- 复杂的状态管理
- 状态转换控制

### 3. 观察者模式 (Observer Pattern)
- 滚动事件监听
- 状态变化响应

## 注意事项

1. **性能优化**: 避免频繁的DOM操作影响性能
2. **内存管理**: 及时清理大量GIF数据
3. **网络优化**: 合理控制网络请求频率
4. **用户体验**: 提供流畅的浏览和选择体验

## 扩展建议

1. **更多布局**: 实现更多类型的布局模式
2. **高级搜索**: 添加更多搜索过滤选项
3. **本地缓存**: 实现GIF的本地缓存机制
4. **批量操作**: 支持批量收藏和管理
5. **个性化**: 基于用户行为的个性化推荐

该组件为讨论应用提供了完整的GIF选择功能，是用户表达情感和增强交流的重要工具。

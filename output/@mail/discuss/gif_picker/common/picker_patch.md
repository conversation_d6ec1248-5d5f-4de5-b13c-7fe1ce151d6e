# Picker Patch - 选择器补丁

## 概述

`picker_patch.js` 实现了对 Odoo 讨论应用选择器组件的补丁扩展，专门添加了GIF选择器的事件处理和切换逻辑。该补丁通过Odoo的补丁机制扩展了Picker组件，重写了事件处理检查和切换方法，添加了对"Composer.onClickAddGif"事件的特殊处理，为GIF选择器提供了与选择器框架的完整集成，是GIF功能在选择器系统中的核心控制组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/gif_picker/common/picker_patch.js`
- **行数**: 39
- **模块**: `@mail/discuss/gif_picker/common/picker_patch`

## 依赖关系

```javascript
// 核心依赖
'@web/core/utils/patch'     // 补丁工具
'@mail/core/common/picker'  // 选择器组件
'@web/core/utils/misc'      // 工具函数
```

## 补丁定义

### Picker 补丁

```javascript
patch(Picker.prototype, {
    isEventHandledByPicker(ev) {
        return super.isEventHandledByPicker(ev) || isEventHandled(ev, "Composer.onClickAddGif");
    },
    async toggle(el, ev) {
        await super.toggle(el, ev);
        if (isEventHandled(ev, "Composer.onClickAddGif")) {
            if (this.popover.isOpen) {
                if (this.props.state.picker === this.props.PICKERS.GIF) {
                    this.close();
                    return;
                }
                this.props.state.picker = this.props.PICKERS.GIF;
            } else {
                this.props.state.picker = this.props.PICKERS.GIF;
                this.popover.open(el, this.contentProps);
            }
        }
    },
});
```

**补丁特性**:
- 扩展事件处理检查
- 添加GIF选择器切换逻辑
- 集成弹出框控制
- 保持原有功能完整性

## 核心功能

### 1. 事件处理检查

```javascript
isEventHandledByPicker(ev) {
    return super.isEventHandledByPicker(ev) || isEventHandled(ev, "Composer.onClickAddGif");
}
```

**事件检查功能**:
- **父类检查**: 调用父类的事件处理检查
- **GIF事件**: 检查是否为GIF添加事件
- **事件标识**: 使用特定标识符识别GIF事件
- **逻辑组合**: 使用OR逻辑组合检查结果

### 2. 选择器切换

```javascript
async toggle(el, ev) {
    await super.toggle(el, ev);
    if (isEventHandled(ev, "Composer.onClickAddGif")) {
        if (this.popover.isOpen) {
            if (this.props.state.picker === this.props.PICKERS.GIF) {
                this.close();
                return;
            }
            this.props.state.picker = this.props.PICKERS.GIF;
        } else {
            this.props.state.picker = this.props.PICKERS.GIF;
            this.popover.open(el, this.contentProps);
        }
    }
}
```

**切换功能**:
- **异步处理**: 使用异步方式处理切换
- **父类调用**: 先执行父类的切换逻辑
- **事件检查**: 检查是否为GIF添加事件
- **状态管理**: 管理选择器的打开/关闭状态
- **智能切换**: 根据当前状态智能切换行为

## 使用场景

### 1. 选择器补丁增强

```javascript
// 选择器补丁增强功能
const PickerPatchEnhancer = {
    enhancePickerPatch: () => {
        const EnhancedPickerPatch = {
            // 增强的事件处理检查
            isEventHandledByPicker(ev) {
                const baseHandled = super.isEventHandledByPicker(ev) || 
                                  isEventHandled(ev, "Composer.onClickAddGif");
                
                if (baseHandled) {
                    return true;
                }
                
                // 增强的事件检查
                return this.enhancedEventCheck(ev);
            },
            
            // 增强的事件检查
            enhancedEventCheck(ev) {
                // 检查自定义GIF事件
                const customGifEvents = [
                    "Composer.onClickGifCategory",
                    "Composer.onClickGifFavorite",
                    "Composer.onClickGifSearch",
                    "Composer.onClickGifRecent"
                ];
                
                for (const eventType of customGifEvents) {
                    if (isEventHandled(ev, eventType)) {
                        return true;
                    }
                }
                
                // 检查键盘快捷键
                if (this.isGifShortcutEvent(ev)) {
                    return true;
                }
                
                // 检查拖拽事件
                if (this.isGifDragEvent(ev)) {
                    return true;
                }
                
                return false;
            },
            
            // 增强的切换功能
            async toggle(el, ev) {
                try {
                    // 记录切换开始
                    this.recordToggleStart(ev);
                    
                    // 预处理
                    await this.preToggleProcessing(el, ev);
                    
                    // 执行父类切换
                    await super.toggle(el, ev);
                    
                    // 处理GIF相关事件
                    await this.handleGifEvents(el, ev);
                    
                    // 后处理
                    await this.postToggleProcessing(el, ev);
                    
                    // 记录切换完成
                    this.recordToggleComplete(ev);
                    
                } catch (error) {
                    console.error('选择器切换失败:', error);
                    this.handleToggleError(error, ev);
                }
            },
            
            // 处理GIF相关事件
            async handleGifEvents(el, ev) {
                // 处理GIF添加事件
                if (isEventHandled(ev, "Composer.onClickAddGif")) {
                    await this.handleGifAddEvent(el, ev);
                    return;
                }
                
                // 处理GIF分类事件
                if (isEventHandled(ev, "Composer.onClickGifCategory")) {
                    await this.handleGifCategoryEvent(el, ev);
                    return;
                }
                
                // 处理GIF收藏事件
                if (isEventHandled(ev, "Composer.onClickGifFavorite")) {
                    await this.handleGifFavoriteEvent(el, ev);
                    return;
                }
                
                // 处理GIF搜索事件
                if (isEventHandled(ev, "Composer.onClickGifSearch")) {
                    await this.handleGifSearchEvent(el, ev);
                    return;
                }
                
                // 处理GIF最近使用事件
                if (isEventHandled(ev, "Composer.onClickGifRecent")) {
                    await this.handleGifRecentEvent(el, ev);
                    return;
                }
            },
            
            // 处理GIF添加事件
            async handleGifAddEvent(el, ev) {
                try {
                    if (this.popover.isOpen) {
                        // 选择器已打开
                        if (this.props.state.picker === this.props.PICKERS.GIF) {
                            // 当前是GIF选择器，关闭它
                            await this.closeWithAnimation();
                        } else {
                            // 切换到GIF选择器
                            await this.switchToGifPicker();
                        }
                    } else {
                        // 选择器未打开，打开GIF选择器
                        await this.openGifPicker(el);
                    }
                } catch (error) {
                    console.error('处理GIF添加事件失败:', error);
                }
            },
            
            // 切换到GIF选择器
            async switchToGifPicker() {
                try {
                    // 显示切换动画
                    this.showSwitchAnimation();
                    
                    // 预加载GIF数据
                    await this.preloadGifData();
                    
                    // 切换选择器
                    this.props.state.picker = this.props.PICKERS.GIF;
                    
                    // 更新选择器状态
                    this.updatePickerState('gif');
                    
                    // 触发切换事件
                    this.triggerSwitchEvent('gif');
                    
                } catch (error) {
                    console.error('切换到GIF选择器失败:', error);
                }
            },
            
            // 打开GIF选择器
            async openGifPicker(el) {
                try {
                    // 设置GIF选择器状态
                    this.props.state.picker = this.props.PICKERS.GIF;
                    
                    // 预加载数据
                    await this.preloadGifData();
                    
                    // 打开弹出框
                    this.popover.open(el, this.getEnhancedContentProps());
                    
                    // 设置焦点
                    this.setGifPickerFocus();
                    
                    // 记录打开事件
                    this.recordGifPickerOpen();
                    
                } catch (error) {
                    console.error('打开GIF选择器失败:', error);
                }
            },
            
            // 关闭选择器（带动画）
            async closeWithAnimation() {
                try {
                    // 显示关闭动画
                    this.showCloseAnimation();
                    
                    // 延迟关闭
                    await new Promise(resolve => setTimeout(resolve, 200));
                    
                    // 关闭选择器
                    this.close();
                    
                    // 记录关闭事件
                    this.recordPickerClose();
                    
                } catch (error) {
                    console.error('关闭选择器失败:', error);
                    this.close(); // 强制关闭
                }
            },
            
            // 获取增强的内容属性
            getEnhancedContentProps() {
                const baseProps = this.contentProps || {};
                
                return {
                    ...baseProps,
                    onGifSelect: (gif) => this.handleGifSelect(gif),
                    onGifFavorite: (gif) => this.handleGifFavorite(gif),
                    onGifSearch: (query) => this.handleGifSearch(query),
                    onGifCategoryChange: (category) => this.handleGifCategoryChange(category),
                    enhancedFeatures: {
                        previewEnabled: true,
                        favoriteEnabled: true,
                        searchEnabled: true,
                        historyEnabled: true
                    }
                };
            },
            
            // 处理GIF选择
            handleGifSelect(gif) {
                try {
                    // 记录选择
                    this.recordGifSelection(gif);
                    
                    // 更新最近使用
                    this.updateRecentGifs(gif);
                    
                    // 关闭选择器
                    this.close();
                    
                    // 触发选择事件
                    this.triggerGifSelectEvent(gif);
                    
                } catch (error) {
                    console.error('处理GIF选择失败:', error);
                }
            },
            
            // 检查是否为GIF快捷键事件
            isGifShortcutEvent(ev) {
                // Ctrl+G 或 Cmd+G
                if ((ev.ctrlKey || ev.metaKey) && ev.key === 'g') {
                    return true;
                }
                
                // Alt+G
                if (ev.altKey && ev.key === 'g') {
                    return true;
                }
                
                return false;
            },
            
            // 检查是否为GIF拖拽事件
            isGifDragEvent(ev) {
                if (ev.type === 'dragover' || ev.type === 'drop') {
                    const dataTransfer = ev.dataTransfer;
                    
                    if (dataTransfer && dataTransfer.types) {
                        // 检查是否包含GIF文件
                        return Array.from(dataTransfer.types).some(type => 
                            type.includes('image') || type.includes('gif')
                        );
                    }
                }
                
                return false;
            },
            
            // 预加载GIF数据
            async preloadGifData() {
                try {
                    // 预加载分类
                    if (!this.gifCategoriesLoaded) {
                        await this.loadGifCategories();
                        this.gifCategoriesLoaded = true;
                    }
                    
                    // 预加载热门GIF
                    if (!this.trendingGifsLoaded) {
                        await this.loadTrendingGifs();
                        this.trendingGifsLoaded = true;
                    }
                    
                    // 预加载用户收藏
                    if (!this.favoriteGifsLoaded) {
                        await this.loadFavoriteGifs();
                        this.favoriteGifsLoaded = true;
                    }
                    
                } catch (error) {
                    console.warn('预加载GIF数据失败:', error);
                }
            },
            
            // 更新选择器状态
            updatePickerState(type) {
                try {
                    const state = {
                        currentPicker: type,
                        lastSwitchTime: Date.now(),
                        switchCount: (this.pickerSwitchCount || 0) + 1
                    };
                    
                    this.pickerSwitchCount = state.switchCount;
                    
                    // 保存状态到本地存储
                    localStorage.setItem('picker_state', JSON.stringify(state));
                } catch (error) {
                    console.warn('更新选择器状态失败:', error);
                }
            },
            
            // 记录切换开始
            recordToggleStart(ev) {
                try {
                    this.toggleStartTime = performance.now();
                    this.toggleEventType = this.getEventType(ev);
                } catch (error) {
                    console.warn('记录切换开始失败:', error);
                }
            },
            
            // 记录切换完成
            recordToggleComplete(ev) {
                try {
                    const duration = performance.now() - (this.toggleStartTime || 0);
                    
                    const record = {
                        eventType: this.toggleEventType,
                        duration: duration,
                        timestamp: Date.now(),
                        success: true
                    };
                    
                    this.saveToggleRecord(record);
                } catch (error) {
                    console.warn('记录切换完成失败:', error);
                }
            },
            
            // 处理切换错误
            handleToggleError(error, ev) {
                try {
                    const duration = performance.now() - (this.toggleStartTime || 0);
                    
                    const record = {
                        eventType: this.toggleEventType,
                        duration: duration,
                        timestamp: Date.now(),
                        success: false,
                        error: error.message
                    };
                    
                    this.saveToggleRecord(record);
                    
                    // 显示错误提示
                    this.showToggleError(error);
                    
                } catch (recordError) {
                    console.warn('处理切换错误失败:', recordError);
                }
            },
            
            // 获取事件类型
            getEventType(ev) {
                if (isEventHandled(ev, "Composer.onClickAddGif")) {
                    return 'gif_add';
                }
                
                if (this.isGifShortcutEvent(ev)) {
                    return 'gif_shortcut';
                }
                
                if (this.isGifDragEvent(ev)) {
                    return 'gif_drag';
                }
                
                return 'unknown';
            },
            
            // 保存切换记录
            saveToggleRecord(record) {
                try {
                    const records = JSON.parse(
                        localStorage.getItem('picker_toggle_records') || '[]'
                    );
                    
                    records.push(record);
                    
                    // 保留最近100个记录
                    if (records.length > 100) {
                        records.splice(0, records.length - 100);
                    }
                    
                    localStorage.setItem('picker_toggle_records', JSON.stringify(records));
                } catch (error) {
                    console.warn('保存切换记录失败:', error);
                }
            },
            
            // 获取选择器补丁统计
            getPickerPatchStatistics() {
                try {
                    const records = JSON.parse(
                        localStorage.getItem('picker_toggle_records') || '[]'
                    );
                    
                    const stats = {
                        totalToggles: records.length,
                        successfulToggles: records.filter(r => r.success).length,
                        failedToggles: records.filter(r => !r.success).length,
                        averageDuration: 0,
                        eventTypes: {}
                    };
                    
                    if (records.length > 0) {
                        stats.averageDuration = records.reduce((sum, r) => sum + r.duration, 0) / records.length;
                        
                        records.forEach(record => {
                            const type = record.eventType;
                            if (!stats.eventTypes[type]) {
                                stats.eventTypes[type] = 0;
                            }
                            stats.eventTypes[type]++;
                        });
                    }
                    
                    return stats;
                } catch (error) {
                    return {
                        totalToggles: 0,
                        successfulToggles: 0,
                        failedToggles: 0,
                        averageDuration: 0,
                        eventTypes: {}
                    };
                }
            }
        };
        
        patch(Picker.prototype, EnhancedPickerPatch);
    }
};

// 应用选择器补丁增强
PickerPatchEnhancer.enhancePickerPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 事件处理
- 智能事件识别
- 多重事件检查
- 事件标记机制

### 3. 状态管理
- 选择器状态控制
- 弹出框状态管理
- 智能切换逻辑

### 4. 用户体验
- 流畅的切换动画
- 智能的状态判断
- 直观的操作反馈

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 状态模式 (State Pattern)
- 选择器状态的管理
- 状态转换控制

### 3. 策略模式 (Strategy Pattern)
- 不同事件的处理策略
- 可配置的行为模式

## 注意事项

1. **事件冲突**: 避免事件处理冲突和重复处理
2. **状态一致性**: 确保选择器状态的一致性
3. **性能优化**: 避免频繁的状态检查影响性能
4. **用户体验**: 提供流畅的选择器切换体验

## 扩展建议

1. **更多事件**: 支持更多类型的GIF相关事件
2. **动画效果**: 实现更丰富的切换动画效果
3. **快捷键**: 添加更多键盘快捷键支持
4. **手势支持**: 实现触摸手势支持
5. **状态持久化**: 实现选择器状态的持久化

该补丁为选择器提供了重要的GIF功能集成，确保GIF选择器能够与选择器框架无缝协作。

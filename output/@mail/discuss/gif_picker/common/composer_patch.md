# Composer Patch - 编辑器补丁

## 概述

`composer_patch.js` 实现了对 Odoo 讨论应用编辑器组件的补丁扩展，专门添加了GIF选择器功能。该补丁通过Odoo的补丁机制扩展了Composer组件，添加了GIF按钮引用、UI状态管理、选择器设置、功能检查和GIF消息发送等特性，为用户在编辑器中提供了便捷的GIF表情包选择和发送功能，是讨论应用中GIF功能的核心组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/gif_picker/common/composer_patch.js`
- **行数**: 55
- **模块**: `@mail/discuss/gif_picker/common/composer_patch`

## 依赖关系

```javascript
// 组件依赖
'@mail/core/common/composer'  // 编辑器组件
'@web/core/utils/misc'        // 工具函数
'@odoo/owl'                   // OWL 框架
'@web/core/utils/hooks'       // Web核心钩子
'@web/core/utils/patch'       // 补丁工具
```

## 补丁定义

### Composer 补丁

```javascript
const composerPatch = {
    setup() {
        this.gifButton = useRef("gif-button");
        super.setup();
        this.ui = useState(useService("ui"));
    },
    get pickerSettings() {
        const setting = super.pickerSettings;
        if (this.hasGifPicker) {
            setting.pickers.gif = (gif) => this.sendGifMessage(gif);
            if (this.hasGifPickerButton) {
                setting.buttons.push(this.gifButton);
            }
        }
        return setting;
    },
    // 其他方法...
};
patch(Composer.prototype, composerPatch);
```

**补丁特性**:
- 扩展编辑器组件功能
- 添加GIF按钮引用
- 集成UI状态服务
- 扩展选择器设置

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    this.gifButton = useRef("gif-button");
    super.setup();
    this.ui = useState(useService("ui"));
}
```

**初始化功能**:
- **按钮引用**: 创建GIF按钮的引用
- **父类调用**: 保持原有的初始化逻辑
- **UI服务**: 注入UI状态服务
- **状态管理**: 管理UI相关状态

### 2. 选择器设置

```javascript
get pickerSettings() {
    const setting = super.pickerSettings;
    if (this.hasGifPicker) {
        setting.pickers.gif = (gif) => this.sendGifMessage(gif);
        if (this.hasGifPickerButton) {
            setting.buttons.push(this.gifButton);
        }
    }
    return setting;
}
```

**设置功能**:
- **父类设置**: 获取原有的选择器设置
- **GIF选择器**: 添加GIF选择器处理函数
- **按钮添加**: 条件性添加GIF按钮
- **设置返回**: 返回完整的选择器设置

### 3. 功能检查

```javascript
get hasGifPicker() {
    return (
        (this.store.hasGifPickerFeature || this.store.self.isAdmin) &&
        !this.env.inChatter &&
        !this.props.composer.message
    );
}

get hasGifPickerButton() {
    return this.hasGifPicker && !this.ui.isSmall && !this.env.inChatWindow;
}
```

**检查功能**:
- **功能权限**: 检查GIF选择器功能权限或管理员权限
- **环境限制**: 排除聊天器和编辑消息场景
- **按钮显示**: 在非小屏幕和非聊天窗口中显示按钮
- **条件组合**: 多重条件的逻辑组合

### 4. 事件处理

```javascript
onClickAddGif(ev) {
    markEventHandled(ev, "Composer.onClickAddGif");
}
```

**事件处理功能**:
- **事件标记**: 标记事件已被处理
- **事件识别**: 使用特定标识符标记事件
- **冲突避免**: 避免事件处理冲突

### 5. GIF消息发送

```javascript
async sendGifMessage(gif) {
    await this._sendMessage(gif.url, {
        parentId: this.props.messageToReplyTo?.message?.id,
    });
}
```

**发送功能**:
- **异步发送**: 使用异步方式发送消息
- **GIF URL**: 使用GIF的URL作为消息内容
- **回复支持**: 支持回复消息的父ID设置
- **消息发送**: 调用内部消息发送方法

## 使用场景

### 1. 编辑器GIF功能增强

```javascript
// 编辑器GIF功能增强
const ComposerPatchEnhancer = {
    enhanceComposerPatch: () => {
        const EnhancedComposerPatch = {
            setup() {
                // 原有设置
                this.gifButton = useRef("gif-button");
                super.setup();
                this.ui = useState(useService("ui"));
                
                // 增强设置
                this.notificationService = useService("notification");
                this.dialogService = useService("dialog");
                
                // GIF相关状态
                this.gifState = useState({
                    isLoading: false,
                    recentGifs: [],
                    favoriteGifs: [],
                    searchHistory: [],
                    lastUsedCategory: 'trending'
                });
                
                // 设置增强功能
                this.setupGifEnhancements();
            },
            
            // 设置GIF增强功能
            setupGifEnhancements() {
                // 加载用户偏好
                this.loadGifPreferences();
                
                // 设置快捷键
                this.setupGifShortcuts();
                
                // 设置拖拽支持
                this.setupGifDragDrop();
                
                // 设置历史记录
                this.setupGifHistory();
            },
            
            // 增强的选择器设置
            get pickerSettings() {
                const setting = super.pickerSettings;
                
                if (this.hasGifPicker) {
                    // 增强的GIF选择器
                    setting.pickers.gif = (gif) => this.enhancedSendGifMessage(gif);
                    
                    // 添加GIF相关按钮
                    if (this.hasGifPickerButton) {
                        setting.buttons.push(this.gifButton);
                        
                        // 添加收藏GIF按钮
                        if (this.hasFavoriteGifs()) {
                            setting.buttons.push(this.favoriteGifButton);
                        }
                        
                        // 添加最近使用GIF按钮
                        if (this.hasRecentGifs()) {
                            setting.buttons.push(this.recentGifButton);
                        }
                    }
                    
                    // 添加GIF搜索功能
                    setting.searchEnabled = true;
                    setting.searchPlaceholder = '搜索GIF...';
                    setting.onSearch = (query) => this.searchGifs(query);
                    
                    // 添加分类功能
                    setting.categories = this.getGifCategories();
                    setting.onCategoryChange = (category) => this.onGifCategoryChange(category);
                }
                
                return setting;
            },
            
            // 增强的GIF功能检查
            get hasGifPicker() {
                const baseCheck = (
                    (this.store.hasGifPickerFeature || this.store.self.isAdmin) &&
                    !this.env.inChatter &&
                    !this.props.composer.message
                );
                
                if (!baseCheck) {
                    return false;
                }
                
                // 增强检查
                return this.enhancedGifCheck();
            },
            
            // 增强的GIF检查
            enhancedGifCheck() {
                // 检查网络状态
                if (!navigator.onLine && !this.hasOfflineGifs()) {
                    return false;
                }
                
                // 检查用户权限
                if (!this.hasGifPermission()) {
                    return false;
                }
                
                // 检查内容策略
                if (!this.passesContentPolicy()) {
                    return false;
                }
                
                return true;
            },
            
            // 增强的GIF消息发送
            async enhancedSendGifMessage(gif) {
                try {
                    // 显示发送状态
                    this.gifState.isLoading = true;
                    
                    // 验证GIF
                    if (!this.validateGif(gif)) {
                        throw new Error('无效的GIF');
                    }
                    
                    // 检查文件大小
                    if (!await this.checkGifSize(gif)) {
                        throw new Error('GIF文件过大');
                    }
                    
                    // 记录使用
                    this.recordGifUsage(gif);
                    
                    // 发送消息
                    await this._sendMessage(gif.url, {
                        parentId: this.props.messageToReplyTo?.message?.id,
                        messageType: 'gif',
                        gifMetadata: {
                            title: gif.title,
                            source: gif.source,
                            tags: gif.tags,
                            dimensions: gif.dimensions
                        }
                    });
                    
                    // 更新最近使用
                    this.updateRecentGifs(gif);
                    
                    // 显示成功提示
                    this.showGifSentNotification(gif);
                    
                } catch (error) {
                    console.error('发送GIF失败:', error);
                    this.handleGifSendError(error);
                } finally {
                    this.gifState.isLoading = false;
                }
            },
            
            // 增强的点击处理
            onClickAddGif(ev) {
                markEventHandled(ev, "Composer.onClickAddGif");
                
                // 记录点击事件
                this.recordGifButtonClick();
                
                // 预加载热门GIF
                this.preloadTrendingGifs();
                
                // 更新最后使用时间
                this.updateLastGifUsage();
            },
            
            // 搜索GIF
            async searchGifs(query) {
                try {
                    if (!query || query.trim().length < 2) {
                        return this.getTrendingGifs();
                    }
                    
                    // 记录搜索
                    this.recordGifSearch(query);
                    
                    // 执行搜索
                    const results = await this.store.searchGifs(query);
                    
                    // 缓存结果
                    this.cacheSearchResults(query, results);
                    
                    return results;
                    
                } catch (error) {
                    console.error('搜索GIF失败:', error);
                    return [];
                }
            },
            
            // 获取GIF分类
            getGifCategories() {
                return [
                    { id: 'trending', name: '热门', icon: 'fa-fire' },
                    { id: 'reactions', name: '表情', icon: 'fa-smile' },
                    { id: 'animals', name: '动物', icon: 'fa-paw' },
                    { id: 'sports', name: '运动', icon: 'fa-futbol' },
                    { id: 'entertainment', name: '娱乐', icon: 'fa-tv' },
                    { id: 'memes', name: '梗图', icon: 'fa-laugh' },
                    { id: 'favorites', name: '收藏', icon: 'fa-heart' },
                    { id: 'recent', name: '最近', icon: 'fa-clock' }
                ];
            },
            
            // GIF分类变化处理
            async onGifCategoryChange(category) {
                try {
                    this.gifState.lastUsedCategory = category.id;
                    
                    // 保存用户偏好
                    this.saveGifPreferences();
                    
                    // 加载分类GIF
                    return await this.loadCategoryGifs(category.id);
                    
                } catch (error) {
                    console.error('加载分类GIF失败:', error);
                    return [];
                }
            },
            
            // 验证GIF
            validateGif(gif) {
                if (!gif || !gif.url) {
                    return false;
                }
                
                // 检查URL格式
                try {
                    new URL(gif.url);
                } catch {
                    return false;
                }
                
                // 检查文件类型
                const validExtensions = ['.gif', '.webp', '.mp4'];
                const hasValidExtension = validExtensions.some(ext => 
                    gif.url.toLowerCase().includes(ext)
                );
                
                return hasValidExtension;
            },
            
            // 检查GIF大小
            async checkGifSize(gif) {
                try {
                    const response = await fetch(gif.url, { method: 'HEAD' });
                    const contentLength = response.headers.get('content-length');
                    
                    if (contentLength) {
                        const sizeInMB = parseInt(contentLength) / (1024 * 1024);
                        const maxSize = this.getMaxGifSize();
                        
                        return sizeInMB <= maxSize;
                    }
                    
                    return true; // 无法获取大小时允许
                } catch {
                    return true; // 检查失败时允许
                }
            },
            
            // 记录GIF使用
            recordGifUsage(gif) {
                try {
                    const usage = JSON.parse(
                        localStorage.getItem('gif_usage_history') || '[]'
                    );
                    
                    usage.push({
                        gifId: gif.id,
                        gifUrl: gif.url,
                        gifTitle: gif.title,
                        timestamp: Date.now(),
                        threadId: this.props.thread?.id
                    });
                    
                    // 保留最近100个使用记录
                    if (usage.length > 100) {
                        usage.splice(0, usage.length - 100);
                    }
                    
                    localStorage.setItem('gif_usage_history', JSON.stringify(usage));
                } catch (error) {
                    console.warn('记录GIF使用失败:', error);
                }
            },
            
            // 更新最近使用的GIF
            updateRecentGifs(gif) {
                try {
                    const recent = [...this.gifState.recentGifs];
                    
                    // 移除已存在的
                    const existingIndex = recent.findIndex(g => g.id === gif.id);
                    if (existingIndex !== -1) {
                        recent.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    recent.unshift(gif);
                    
                    // 限制数量
                    if (recent.length > 20) {
                        recent.splice(20);
                    }
                    
                    this.gifState.recentGifs = recent;
                    
                    // 保存到本地存储
                    this.saveRecentGifs();
                    
                } catch (error) {
                    console.warn('更新最近GIF失败:', error);
                }
            },
            
            // 处理GIF发送错误
            handleGifSendError(error) {
                let message = '发送GIF失败';
                
                if (error.message.includes('大小')) {
                    message = 'GIF文件过大，请选择较小的文件';
                } else if (error.message.includes('网络')) {
                    message = '网络错误，请检查网络连接';
                } else if (error.message.includes('权限')) {
                    message = '没有权限发送GIF';
                }
                
                this.notificationService.add(message, { type: 'error' });
            },
            
            // 显示GIF发送成功通知
            showGifSentNotification(gif) {
                this.notificationService.add(
                    `GIF "${gif.title || 'Untitled'}" 发送成功`,
                    { type: 'success' }
                );
            },
            
            // 检查是否有收藏GIF
            hasFavoriteGifs() {
                return this.gifState.favoriteGifs.length > 0;
            },
            
            // 检查是否有最近使用的GIF
            hasRecentGifs() {
                return this.gifState.recentGifs.length > 0;
            },
            
            // 检查是否有离线GIF
            hasOfflineGifs() {
                return this.gifState.recentGifs.length > 0 || 
                       this.gifState.favoriteGifs.length > 0;
            },
            
            // 检查GIF权限
            hasGifPermission() {
                const user = this.store.self;
                return user && (user.isInternalUser || user.isAdmin);
            },
            
            // 检查内容策略
            passesContentPolicy() {
                // 这里可以实现内容策略检查
                return true;
            },
            
            // 获取最大GIF大小
            getMaxGifSize() {
                return 10; // 10MB
            },
            
            // 加载GIF偏好设置
            loadGifPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('gif_preferences') || '{}'
                    );
                    
                    this.gifState.lastUsedCategory = prefs.lastUsedCategory || 'trending';
                    this.gifState.favoriteGifs = prefs.favoriteGifs || [];
                    this.gifState.recentGifs = prefs.recentGifs || [];
                    
                } catch (error) {
                    console.warn('加载GIF偏好失败:', error);
                }
            },
            
            // 保存GIF偏好设置
            saveGifPreferences() {
                try {
                    const prefs = {
                        lastUsedCategory: this.gifState.lastUsedCategory,
                        favoriteGifs: this.gifState.favoriteGifs,
                        recentGifs: this.gifState.recentGifs
                    };
                    
                    localStorage.setItem('gif_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('保存GIF偏好失败:', error);
                }
            },
            
            // 获取编辑器GIF统计
            getComposerGifStatistics() {
                try {
                    const usage = JSON.parse(
                        localStorage.getItem('gif_usage_history') || '[]'
                    );
                    
                    return {
                        totalUsage: usage.length,
                        recentGifs: this.gifState.recentGifs.length,
                        favoriteGifs: this.gifState.favoriteGifs.length,
                        lastUsedCategory: this.gifState.lastUsedCategory,
                        searchHistory: this.gifState.searchHistory.length
                    };
                } catch (error) {
                    return {
                        totalUsage: 0,
                        recentGifs: 0,
                        favoriteGifs: 0,
                        lastUsedCategory: 'trending',
                        searchHistory: 0
                    };
                }
            }
        };
        
        patch(Composer.prototype, EnhancedComposerPatch);
    }
};

// 应用编辑器补丁增强
ComposerPatchEnhancer.enhanceComposerPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 条件控制
- 多重条件检查
- 权限验证机制
- 环境感知判断

### 3. 状态管理
- UI状态集成
- 引用管理
- 响应式更新

### 4. 事件处理
- 事件标记机制
- 冲突避免
- 异步处理支持

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同环境的显示策略
- 可配置的行为模式

### 3. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 响应式数据更新

## 注意事项

1. **权限检查**: 确保用户有使用GIF功能的权限
2. **环境限制**: 正确处理不同环境下的功能限制
3. **性能优化**: 避免频繁的状态检查影响性能
4. **用户体验**: 提供流畅的GIF选择和发送体验

## 扩展建议

1. **更多格式**: 支持更多类型的动图格式
2. **本地上传**: 支持用户上传自定义GIF
3. **收藏功能**: 实现GIF收藏和管理功能
4. **搜索优化**: 实现更智能的GIF搜索
5. **分类管理**: 提供更丰富的GIF分类

该补丁为讨论应用的编辑器提供了重要的GIF功能支持，让用户能够轻松地在消息中添加GIF表情。

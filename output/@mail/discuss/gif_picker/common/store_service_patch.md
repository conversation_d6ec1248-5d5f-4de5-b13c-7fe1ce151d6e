# Store Service Patch - 存储服务补丁

## 概述

`store_service_patch.js` 实现了对 Odoo 讨论应用存储服务的补丁扩展，专门添加了GIF选择器功能标志。该补丁通过Odoo的补丁机制扩展了Store服务，在setup方法中添加了hasGifPickerFeature属性，为整个应用提供了GIF选择器功能的开关控制，是GIF功能在存储层面的基础配置组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/gif_picker/common/store_service_patch.js`
- **行数**: 22
- **模块**: `@mail/discuss/gif_picker/common/store_service_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/store_service'  // 存储服务
'@web/core/utils/patch'            // 补丁工具
```

## 补丁定义

### Store 补丁

```javascript
/** @type {import("models").Store} */
const StorePatch = {
    setup() {
        super.setup(...arguments);
        this.hasGifPickerFeature = false;
    },
};
patch(Store.prototype, StorePatch);
```

**补丁特性**:
- 扩展存储服务功能
- 添加GIF功能标志
- 保持原有服务逻辑
- 提供功能开关控制

## 核心功能

### 1. 功能标志初始化

```javascript
setup() {
    super.setup(...arguments);
    this.hasGifPickerFeature = false;
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **功能标志**: 初始化GIF选择器功能标志为false
- **默认状态**: 提供功能的默认关闭状态
- **配置基础**: 为功能配置提供基础属性

## 使用场景

### 1. 存储服务GIF功能增强

```javascript
// 存储服务GIF功能增强
const StoreServiceGifPatchEnhancer = {
    enhanceStoreServiceGifPatch: () => {
        const EnhancedStorePatch = {
            setup() {
                super.setup(...arguments);
                
                // 原有的GIF功能标志
                this.hasGifPickerFeature = false;
                
                // 增强的GIF相关属性
                this.gifFeatures = {
                    enabled: false,
                    apiKey: null,
                    apiEndpoint: 'https://tenor.googleapis.com/v2',
                    searchLimit: 20,
                    categoriesLimit: 10,
                    favoritesLimit: 100,
                    cacheEnabled: true,
                    cacheExpiry: 3600000, // 1小时
                    allowedFormats: ['gif', 'webp', 'mp4'],
                    maxFileSize: 10 * 1024 * 1024, // 10MB
                    contentFilter: 'moderate',
                    locale: 'en_US',
                    country: 'US'
                };
                
                // GIF数据缓存
                this.gifCache = {
                    categories: new Map(),
                    searches: new Map(),
                    favorites: new Map(),
                    trending: new Map(),
                    recent: new Map()
                };
                
                // GIF统计数据
                this.gifStats = {
                    totalSearches: 0,
                    totalSelections: 0,
                    favoriteCount: 0,
                    cacheHits: 0,
                    cacheMisses: 0,
                    apiCalls: 0,
                    errors: 0
                };
                
                // 初始化GIF功能
                this.initializeGifFeatures();
            },
            
            // 初始化GIF功能
            async initializeGifFeatures() {
                try {
                    // 检查功能配置
                    await this.checkGifFeatureConfig();
                    
                    // 加载用户设置
                    await this.loadGifUserSettings();
                    
                    // 初始化缓存
                    this.initializeGifCache();
                    
                    // 设置清理任务
                    this.setupGifCacheCleanup();
                    
                    // 加载统计数据
                    this.loadGifStatistics();
                    
                } catch (error) {
                    console.error('初始化GIF功能失败:', error);
                    this.gifFeatures.enabled = false;
                }
            },
            
            // 检查GIF功能配置
            async checkGifFeatureConfig() {
                try {
                    // 从服务器获取配置
                    const config = await this.env.services.rpc('/mail/gif/config');
                    
                    if (config) {
                        this.gifFeatures = { ...this.gifFeatures, ...config };
                        this.hasGifPickerFeature = config.enabled || false;
                    }
                    
                    // 检查API密钥
                    if (this.gifFeatures.enabled && !this.gifFeatures.apiKey) {
                        console.warn('GIF功能已启用但缺少API密钥');
                        this.gifFeatures.enabled = false;
                    }
                    
                } catch (error) {
                    console.warn('获取GIF配置失败:', error);
                    this.gifFeatures.enabled = false;
                }
            },
            
            // 加载用户GIF设置
            async loadGifUserSettings() {
                try {
                    const settings = await this.env.services.rpc('/mail/gif/user_settings');
                    
                    if (settings) {
                        // 应用用户设置
                        this.gifFeatures.contentFilter = settings.contentFilter || this.gifFeatures.contentFilter;
                        this.gifFeatures.locale = settings.locale || this.gifFeatures.locale;
                        this.gifFeatures.country = settings.country || this.gifFeatures.country;
                    }
                    
                } catch (error) {
                    console.warn('加载用户GIF设置失败:', error);
                }
            },
            
            // 初始化GIF缓存
            initializeGifCache() {
                try {
                    // 从本地存储加载缓存
                    const cachedData = localStorage.getItem('gif_cache_data');
                    
                    if (cachedData) {
                        const parsed = JSON.parse(cachedData);
                        
                        // 检查缓存是否过期
                        const now = Date.now();
                        
                        Object.entries(parsed).forEach(([key, value]) => {
                            if (value.timestamp && (now - value.timestamp) < this.gifFeatures.cacheExpiry) {
                                this.gifCache[key] = new Map(value.data);
                            }
                        });
                    }
                    
                } catch (error) {
                    console.warn('初始化GIF缓存失败:', error);
                    this.clearGifCache();
                }
            },
            
            // 设置GIF缓存清理
            setupGifCacheCleanup() {
                // 每小时清理一次过期缓存
                this.gifCacheCleanupInterval = setInterval(() => {
                    this.cleanupExpiredGifCache();
                }, 3600000);
                
                // 页面卸载时清理
                window.addEventListener('beforeunload', () => {
                    this.saveGifCacheToStorage();
                });
            },
            
            // 清理过期的GIF缓存
            cleanupExpiredGifCache() {
                try {
                    const now = Date.now();
                    const expiry = this.gifFeatures.cacheExpiry;
                    
                    Object.values(this.gifCache).forEach(cache => {
                        if (cache instanceof Map) {
                            for (const [key, value] of cache.entries()) {
                                if (value.timestamp && (now - value.timestamp) > expiry) {
                                    cache.delete(key);
                                }
                            }
                        }
                    });
                    
                } catch (error) {
                    console.warn('清理GIF缓存失败:', error);
                }
            },
            
            // 保存GIF缓存到存储
            saveGifCacheToStorage() {
                try {
                    const cacheData = {};
                    
                    Object.entries(this.gifCache).forEach(([key, cache]) => {
                        if (cache instanceof Map && cache.size > 0) {
                            cacheData[key] = {
                                data: Array.from(cache.entries()),
                                timestamp: Date.now()
                            };
                        }
                    });
                    
                    localStorage.setItem('gif_cache_data', JSON.stringify(cacheData));
                    
                } catch (error) {
                    console.warn('保存GIF缓存失败:', error);
                }
            },
            
            // 加载GIF统计数据
            loadGifStatistics() {
                try {
                    const stats = localStorage.getItem('gif_statistics');
                    
                    if (stats) {
                        this.gifStats = { ...this.gifStats, ...JSON.parse(stats) };
                    }
                    
                } catch (error) {
                    console.warn('加载GIF统计失败:', error);
                }
            },
            
            // 保存GIF统计数据
            saveGifStatistics() {
                try {
                    localStorage.setItem('gif_statistics', JSON.stringify(this.gifStats));
                } catch (error) {
                    console.warn('保存GIF统计失败:', error);
                }
            },
            
            // 搜索GIF
            async searchGifs(query, options = {}) {
                try {
                    if (!this.hasGifPickerFeature || !this.gifFeatures.enabled) {
                        throw new Error('GIF功能未启用');
                    }
                    
                    // 检查缓存
                    const cacheKey = `${query}_${JSON.stringify(options)}`;
                    const cached = this.gifCache.searches.get(cacheKey);
                    
                    if (cached && this.isCacheValid(cached)) {
                        this.gifStats.cacheHits++;
                        return cached.data;
                    }
                    
                    // 执行搜索
                    this.gifStats.cacheMisses++;
                    this.gifStats.apiCalls++;
                    this.gifStats.totalSearches++;
                    
                    const params = {
                        q: query,
                        key: this.gifFeatures.apiKey,
                        limit: options.limit || this.gifFeatures.searchLimit,
                        pos: options.position || '',
                        contentfilter: this.gifFeatures.contentFilter,
                        locale: this.gifFeatures.locale,
                        country: this.gifFeatures.country,
                        ...options
                    };
                    
                    const response = await fetch(
                        `${this.gifFeatures.apiEndpoint}/search?${new URLSearchParams(params)}`
                    );
                    
                    if (!response.ok) {
                        throw new Error(`搜索失败: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // 缓存结果
                    if (this.gifFeatures.cacheEnabled) {
                        this.gifCache.searches.set(cacheKey, {
                            data: data,
                            timestamp: Date.now()
                        });
                    }
                    
                    // 保存统计
                    this.saveGifStatistics();
                    
                    return data;
                    
                } catch (error) {
                    console.error('搜索GIF失败:', error);
                    this.gifStats.errors++;
                    this.saveGifStatistics();
                    throw error;
                }
            },
            
            // 获取GIF分类
            async getGifCategories(options = {}) {
                try {
                    if (!this.hasGifPickerFeature || !this.gifFeatures.enabled) {
                        throw new Error('GIF功能未启用');
                    }
                    
                    // 检查缓存
                    const cacheKey = 'categories';
                    const cached = this.gifCache.categories.get(cacheKey);
                    
                    if (cached && this.isCacheValid(cached)) {
                        this.gifStats.cacheHits++;
                        return cached.data;
                    }
                    
                    // 获取分类
                    this.gifStats.cacheMisses++;
                    this.gifStats.apiCalls++;
                    
                    const params = {
                        key: this.gifFeatures.apiKey,
                        type: 'featured',
                        contentfilter: this.gifFeatures.contentFilter,
                        locale: this.gifFeatures.locale,
                        country: this.gifFeatures.country,
                        ...options
                    };
                    
                    const response = await fetch(
                        `${this.gifFeatures.apiEndpoint}/categories?${new URLSearchParams(params)}`
                    );
                    
                    if (!response.ok) {
                        throw new Error(`获取分类失败: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // 缓存结果
                    if (this.gifFeatures.cacheEnabled) {
                        this.gifCache.categories.set(cacheKey, {
                            data: data,
                            timestamp: Date.now()
                        });
                    }
                    
                    return data;
                    
                } catch (error) {
                    console.error('获取GIF分类失败:', error);
                    this.gifStats.errors++;
                    this.saveGifStatistics();
                    throw error;
                }
            },
            
            // 获取热门GIF
            async getTrendingGifs(options = {}) {
                try {
                    if (!this.hasGifPickerFeature || !this.gifFeatures.enabled) {
                        throw new Error('GIF功能未启用');
                    }
                    
                    // 检查缓存
                    const cacheKey = 'trending';
                    const cached = this.gifCache.trending.get(cacheKey);
                    
                    if (cached && this.isCacheValid(cached)) {
                        this.gifStats.cacheHits++;
                        return cached.data;
                    }
                    
                    // 获取热门GIF
                    this.gifStats.cacheMisses++;
                    this.gifStats.apiCalls++;
                    
                    const params = {
                        key: this.gifFeatures.apiKey,
                        limit: options.limit || this.gifFeatures.searchLimit,
                        contentfilter: this.gifFeatures.contentFilter,
                        locale: this.gifFeatures.locale,
                        country: this.gifFeatures.country,
                        ...options
                    };
                    
                    const response = await fetch(
                        `${this.gifFeatures.apiEndpoint}/trending?${new URLSearchParams(params)}`
                    );
                    
                    if (!response.ok) {
                        throw new Error(`获取热门GIF失败: ${response.status}`);
                    }
                    
                    const data = await response.json();
                    
                    // 缓存结果
                    if (this.gifFeatures.cacheEnabled) {
                        this.gifCache.trending.set(cacheKey, {
                            data: data,
                            timestamp: Date.now()
                        });
                    }
                    
                    return data;
                    
                } catch (error) {
                    console.error('获取热门GIF失败:', error);
                    this.gifStats.errors++;
                    this.saveGifStatistics();
                    throw error;
                }
            },
            
            // 检查缓存是否有效
            isCacheValid(cached) {
                if (!cached || !cached.timestamp) {
                    return false;
                }
                
                const now = Date.now();
                return (now - cached.timestamp) < this.gifFeatures.cacheExpiry;
            },
            
            // 清空GIF缓存
            clearGifCache() {
                Object.values(this.gifCache).forEach(cache => {
                    if (cache instanceof Map) {
                        cache.clear();
                    }
                });
            },
            
            // 启用GIF功能
            enableGifFeature() {
                this.hasGifPickerFeature = true;
                this.gifFeatures.enabled = true;
            },
            
            // 禁用GIF功能
            disableGifFeature() {
                this.hasGifPickerFeature = false;
                this.gifFeatures.enabled = false;
                this.clearGifCache();
            },
            
            // 更新GIF配置
            updateGifConfig(config) {
                this.gifFeatures = { ...this.gifFeatures, ...config };
                this.hasGifPickerFeature = this.gifFeatures.enabled;
                
                // 如果禁用了缓存，清空现有缓存
                if (!this.gifFeatures.cacheEnabled) {
                    this.clearGifCache();
                }
            },
            
            // 记录GIF选择
            recordGifSelection(gif) {
                try {
                    this.gifStats.totalSelections++;
                    
                    // 更新最近使用
                    const recent = this.gifCache.recent;
                    recent.set(gif.id, {
                        gif: gif,
                        timestamp: Date.now()
                    });
                    
                    // 限制最近使用数量
                    if (recent.size > 50) {
                        const oldest = Array.from(recent.entries())
                            .sort((a, b) => a[1].timestamp - b[1].timestamp)[0];
                        recent.delete(oldest[0]);
                    }
                    
                    this.saveGifStatistics();
                    
                } catch (error) {
                    console.warn('记录GIF选择失败:', error);
                }
            },
            
            // 获取GIF功能统计
            getGifFeatureStatistics() {
                return {
                    enabled: this.hasGifPickerFeature,
                    features: { ...this.gifFeatures },
                    stats: { ...this.gifStats },
                    cacheInfo: {
                        categories: this.gifCache.categories.size,
                        searches: this.gifCache.searches.size,
                        favorites: this.gifCache.favorites.size,
                        trending: this.gifCache.trending.size,
                        recent: this.gifCache.recent.size
                    }
                };
            }
        };
        
        patch(Store.prototype, EnhancedStorePatch);
    }
};

// 应用存储服务GIF补丁增强
StoreServiceGifPatchEnhancer.enhanceStoreServiceGifPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 功能控制
- 简单的功能开关
- 默认关闭状态
- 易于配置管理

### 3. 存储集成
- 与存储服务无缝集成
- 全局功能标志
- 统一的配置管理

### 4. 扩展性设计
- 易于扩展更多功能
- 灵活的配置选项
- 模块化的功能管理

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 特性标志模式 (Feature Flag Pattern)
- 功能的开关控制
- 渐进式功能发布

### 3. 单例模式 (Singleton Pattern)
- 全局唯一的存储服务
- 统一的配置管理

## 注意事项

1. **功能控制**: 确保功能标志的正确设置和使用
2. **性能影响**: 避免功能检查影响性能
3. **配置管理**: 提供灵活的配置管理机制
4. **向后兼容**: 保持与现有代码的兼容性

## 扩展建议

1. **动态配置**: 实现动态的功能配置更新
2. **权限控制**: 添加基于用户权限的功能控制
3. **使用统计**: 实现功能使用情况的统计
4. **A/B测试**: 支持A/B测试的功能控制
5. **配置界面**: 提供用户友好的配置界面

该补丁为存储服务提供了重要的GIF功能标志，是整个GIF功能体系的基础配置组件。

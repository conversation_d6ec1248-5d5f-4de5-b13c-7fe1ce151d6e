# Picker Content Patch - 选择器内容补丁

## 概述

`picker_content_patch.js` 实现了对 Odoo 讨论应用选择器内容组件的补丁扩展，专门添加了GIF选择器组件的集成。该补丁通过Odoo的补丁机制扩展了PickerContent组件，注册了GifPicker组件并添加了存储服务的状态管理，为选择器内容提供了GIF选择功能的支持，是GIF选择器功能与选择器框架集成的关键桥梁组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/gif_picker/common/picker_content_patch.js`
- **行数**: 24
- **模块**: `@mail/discuss/gif_picker/common/picker_content_patch`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                          // OWL 框架
'@web/core/utils/patch'                              // 补丁工具
'@web/core/utils/hooks'                              // Web核心钩子
'@mail/discuss/gif_picker/common/gif_picker'         // GIF选择器组件
'@mail/core/common/picker_content'                   // 选择器内容组件
```

## 补丁定义

### 组件注册

```javascript
Object.assign(PickerContent.components, { GifPicker });
```

**组件注册功能**:
- 将GifPicker组件添加到PickerContent的组件列表
- 使GifPicker在选择器内容中可用
- 扩展选择器的功能范围

### PickerContent 补丁

```javascript
patch(PickerContent.prototype, {
    setup() {
        super.setup();
        this.store = useState(useService("mail.store"));
    },
});
```

**补丁特性**:
- 扩展选择器内容功能
- 添加存储服务状态管理
- 保持原有功能完整性

## 核心功能

### 1. 组件集成

```javascript
Object.assign(PickerContent.components, { GifPicker });
```

**集成功能**:
- **组件注册**: 将GIF选择器注册到选择器内容组件
- **功能扩展**: 扩展选择器的可用组件
- **无缝集成**: 与现有选择器框架无缝集成
- **模块化设计**: 保持组件的模块化结构

### 2. 状态管理增强

```javascript
setup() {
    super.setup();
    this.store = useState(useService("mail.store"));
}
```

**状态管理功能**:
- **父类调用**: 保持原有的初始化逻辑
- **存储服务**: 注入邮件存储服务
- **状态响应**: 使存储状态变化可响应
- **数据共享**: 为GIF选择器提供数据访问

## 使用场景

### 1. 选择器内容增强

```javascript
// 选择器内容增强功能
const PickerContentPatchEnhancer = {
    enhancePickerContentPatch: () => {
        const EnhancedPickerContentPatch = {
            setup() {
                super.setup();
                
                // 原有的存储服务
                this.store = useState(useService("mail.store"));
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.dialogService = useService("dialog");
                this.userService = useService("user");
                
                // 增强的状态管理
                this.pickerState = useState({
                    activePickerType: null,
                    pickerHistory: [],
                    userPreferences: {},
                    recentSelections: [],
                    favoriteItems: {},
                    searchHistory: {},
                    customSettings: {}
                });
                
                // 设置增强功能
                this.setupPickerEnhancements();
            },
            
            // 设置选择器增强功能
            setupPickerEnhancements() {
                // 加载用户偏好
                this.loadUserPreferences();
                
                // 设置选择器历史
                this.setupPickerHistory();
                
                // 设置快捷键
                this.setupPickerShortcuts();
                
                // 设置自动保存
                this.setupAutoSave();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
            },
            
            // 获取增强的组件列表
            get components() {
                const baseComponents = super.components || {};
                
                // 添加增强的GIF选择器
                const enhancedComponents = {
                    ...baseComponents,
                    GifPicker: this.getEnhancedGifPicker(),
                    EmojiPicker: this.getEnhancedEmojiPicker(),
                    FilePicker: this.getEnhancedFilePicker(),
                    CustomPicker: this.getCustomPicker()
                };
                
                return enhancedComponents;
            },
            
            // 获取增强的GIF选择器
            getEnhancedGifPicker() {
                return class EnhancedGifPicker extends GifPicker {
                    setup() {
                        super.setup();
                        
                        // 增强的GIF选择器功能
                        this.setupGifEnhancements();
                    },
                    
                    setupGifEnhancements() {
                        // 设置GIF预览
                        this.setupGifPreview();
                        
                        // 设置GIF分析
                        this.setupGifAnalytics();
                        
                        // 设置GIF缓存
                        this.setupGifCache();
                    },
                    
                    // 增强的GIF选择处理
                    onClickGif(gif) {
                        // 记录选择
                        this.recordGifSelection(gif);
                        
                        // 更新最近选择
                        this.updateRecentSelections(gif);
                        
                        // 执行原有逻辑
                        super.onClickGif(gif);
                        
                        // 触发选择事件
                        this.triggerSelectionEvent('gif', gif);
                    }
                };
            },
            
            // 获取自定义选择器
            getCustomPicker() {
                return class CustomPicker extends Component {
                    static template = "discuss.CustomPicker";
                    static props = ["onSelect", "close?"];
                    
                    setup() {
                        this.customItems = useState([]);
                        this.loadCustomItems();
                    },
                    
                    async loadCustomItems() {
                        try {
                            const items = await this.env.services.orm.searchRead(
                                'mail.custom.picker.item',
                                [],
                                ['name', 'type', 'content', 'preview']
                            );
                            
                            this.customItems.splice(0, this.customItems.length, ...items);
                        } catch (error) {
                            console.error('加载自定义项目失败:', error);
                        }
                    },
                    
                    onClickItem(item) {
                        this.props.onSelect(item);
                        this.props.close?.();
                    }
                };
            },
            
            // 切换选择器类型
            switchPickerType(type) {
                try {
                    // 记录切换
                    this.recordPickerSwitch(type);
                    
                    // 更新活跃类型
                    this.pickerState.activePickerType = type;
                    
                    // 预加载数据
                    this.preloadPickerData(type);
                    
                    // 保存用户偏好
                    this.savePickerPreference(type);
                    
                } catch (error) {
                    console.error('切换选择器类型失败:', error);
                }
            },
            
            // 记录选择器切换
            recordPickerSwitch(type) {
                try {
                    const history = this.pickerState.pickerHistory;
                    
                    history.push({
                        type: type,
                        timestamp: Date.now(),
                        userId: this.userService.userId
                    });
                    
                    // 限制历史记录大小
                    if (history.length > 100) {
                        history.splice(0, 50);
                    }
                    
                    // 保存到本地存储
                    this.savePickerHistory();
                    
                } catch (error) {
                    console.warn('记录选择器切换失败:', error);
                }
            },
            
            // 预加载选择器数据
            async preloadPickerData(type) {
                try {
                    switch (type) {
                        case 'gif':
                            await this.preloadGifData();
                            break;
                        case 'emoji':
                            await this.preloadEmojiData();
                            break;
                        case 'file':
                            await this.preloadFileData();
                            break;
                        case 'custom':
                            await this.preloadCustomData();
                            break;
                    }
                } catch (error) {
                    console.warn(`预加载${type}数据失败:`, error);
                }
            },
            
            // 预加载GIF数据
            async preloadGifData() {
                try {
                    // 预加载热门分类
                    if (!this.store.gifCategories) {
                        await this.store.loadGifCategories();
                    }
                    
                    // 预加载用户收藏
                    if (!this.store.favoriteGifs) {
                        await this.store.loadFavoriteGifs();
                    }
                    
                } catch (error) {
                    console.warn('预加载GIF数据失败:', error);
                }
            },
            
            // 更新最近选择
            updateRecentSelections(item) {
                try {
                    const recent = this.pickerState.recentSelections;
                    
                    // 移除已存在的
                    const existingIndex = recent.findIndex(r => 
                        r.type === item.type && r.id === item.id
                    );
                    
                    if (existingIndex !== -1) {
                        recent.splice(existingIndex, 1);
                    }
                    
                    // 添加到开头
                    recent.unshift({
                        type: item.type || 'unknown',
                        id: item.id,
                        data: item,
                        timestamp: Date.now()
                    });
                    
                    // 限制数量
                    if (recent.length > 50) {
                        recent.splice(50);
                    }
                    
                    // 保存到本地存储
                    this.saveRecentSelections();
                    
                } catch (error) {
                    console.warn('更新最近选择失败:', error);
                }
            },
            
            // 触发选择事件
            triggerSelectionEvent(type, item) {
                try {
                    this.env.bus.trigger('picker:item_selected', {
                        type: type,
                        item: item,
                        timestamp: Date.now(),
                        userId: this.userService.userId
                    });
                } catch (error) {
                    console.warn('触发选择事件失败:', error);
                }
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('picker_content_preferences') || '{}'
                    );
                    
                    this.pickerState.userPreferences = {
                        defaultPickerType: prefs.defaultPickerType || 'emoji',
                        autoPreload: prefs.autoPreload !== false,
                        showRecent: prefs.showRecent !== false,
                        showFavorites: prefs.showFavorites !== false,
                        gridSize: prefs.gridSize || 'medium',
                        animationEnabled: prefs.animationEnabled !== false,
                        ...prefs
                    };
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                    this.pickerState.userPreferences = {
                        defaultPickerType: 'emoji',
                        autoPreload: true,
                        showRecent: true,
                        showFavorites: true,
                        gridSize: 'medium',
                        animationEnabled: true
                    };
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    localStorage.setItem(
                        'picker_content_preferences',
                        JSON.stringify(this.pickerState.userPreferences)
                    );
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 保存选择器偏好
            savePickerPreference(type) {
                try {
                    this.pickerState.userPreferences.defaultPickerType = type;
                    this.saveUserPreferences();
                } catch (error) {
                    console.warn('保存选择器偏好失败:', error);
                }
            },
            
            // 保存选择器历史
            savePickerHistory() {
                try {
                    localStorage.setItem(
                        'picker_history',
                        JSON.stringify(this.pickerState.pickerHistory)
                    );
                } catch (error) {
                    console.warn('保存选择器历史失败:', error);
                }
            },
            
            // 保存最近选择
            saveRecentSelections() {
                try {
                    localStorage.setItem(
                        'recent_selections',
                        JSON.stringify(this.pickerState.recentSelections)
                    );
                } catch (error) {
                    console.warn('保存最近选择失败:', error);
                }
            },
            
            // 设置选择器快捷键
            setupPickerShortcuts() {
                this.keyboardHandler = (event) => {
                    // Ctrl+G 打开GIF选择器
                    if (event.ctrlKey && event.key === 'g') {
                        event.preventDefault();
                        this.switchPickerType('gif');
                    }
                    
                    // Ctrl+E 打开表情选择器
                    if (event.ctrlKey && event.key === 'e') {
                        event.preventDefault();
                        this.switchPickerType('emoji');
                    }
                    
                    // Ctrl+F 打开文件选择器
                    if (event.ctrlKey && event.key === 'f') {
                        event.preventDefault();
                        this.switchPickerType('file');
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            },
            
            // 获取选择器内容统计
            getPickerContentStatistics() {
                return {
                    activePickerType: this.pickerState.activePickerType,
                    pickerHistory: this.pickerState.pickerHistory.length,
                    recentSelections: this.pickerState.recentSelections.length,
                    userPreferences: { ...this.pickerState.userPreferences },
                    availableComponents: Object.keys(this.components),
                    storeConnected: !!this.store
                };
            }
        };
        
        // 应用增强补丁
        patch(PickerContent.prototype, EnhancedPickerContentPatch);
    }
};

// 应用选择器内容补丁增强
PickerContentPatchEnhancer.enhancePickerContentPatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 组件集成
- 无缝组件注册
- 模块化设计
- 清晰的组件边界

### 3. 状态管理
- 响应式状态更新
- 服务状态集成
- 数据共享机制

### 4. 扩展性设计
- 易于添加新组件
- 灵活的配置选项
- 可定制的行为

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 组合模式 (Composite Pattern)
- 组件的组合和管理
- 统一的组件接口

### 3. 桥接模式 (Bridge Pattern)
- 选择器框架与具体组件的桥接
- 抽象与实现的分离

## 注意事项

1. **组件兼容性**: 确保新组件与选择器框架的兼容性
2. **状态同步**: 保持组件间状态的同步
3. **性能影响**: 避免过多组件影响性能
4. **内存管理**: 及时清理组件资源

## 扩展建议

1. **更多组件**: 添加更多类型的选择器组件
2. **主题支持**: 实现选择器的主题定制
3. **布局优化**: 提供更多布局选项
4. **搜索功能**: 实现跨组件的统一搜索
5. **快捷操作**: 添加更多快捷操作支持

该补丁为选择器内容提供了重要的GIF功能集成，是GIF选择器与选择器框架之间的关键桥梁。

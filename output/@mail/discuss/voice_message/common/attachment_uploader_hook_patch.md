# Attachment Uploader Hook Patch - 附件上传钩子补丁

## 概述

`attachment_uploader_hook_patch.js` 实现了对 Odoo 讨论应用附件上传服务的补丁扩展，专门添加了语音消息上传的支持。该补丁通过Odoo的补丁机制扩展了AttachmentUploadService，修改了附件数据构建和表单数据构建方法，为语音消息文件的上传提供了专门的处理逻辑，是讨论应用语音消息功能的核心上传组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/attachment_uploader_hook_patch.js`
- **行数**: 30
- **模块**: `@mail/discuss/voice_message/common/attachment_uploader_hook_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/attachment_upload_service'   // 附件上传服务
'@web/core/utils/patch'                         // 补丁工具
```

## 补丁定义

### AttachmentUploadService 原型补丁

```javascript
patch(AttachmentUploadService.prototype, {
    _makeAttachmentData(upload, tmpId, thread, tmpUrl) {
        const attachmentData = super._makeAttachmentData(...arguments);
        if (upload.data.get("voice")) {
            attachmentData.voice = upload.data.get("voice");
        }
        return attachmentData;
    },
    _buildFormData(formData, tmpURL, thread, composer, tmpId, options) {
        super._buildFormData(...arguments);
        if (options?.voice) {
            formData.append("voice", true);
        }
        return formData;
    },
});
```

**补丁特性**:
- 扩展附件上传服务功能
- 添加语音消息数据处理
- 增强表单数据构建
- 保持原有功能完整性

## 核心功能

### 1. 附件数据构建

```javascript
_makeAttachmentData(upload, tmpId, thread, tmpUrl) {
    const attachmentData = super._makeAttachmentData(...arguments);
    if (upload.data.get("voice")) {
        attachmentData.voice = upload.data.get("voice");
    }
    return attachmentData;
}
```

**数据构建功能**:
- **父类调用**: 执行原有的附件数据构建逻辑
- **语音检查**: 检查上传数据中是否包含语音标识
- **数据扩展**: 将语音数据添加到附件数据中
- **数据完整性**: 确保语音消息数据的完整传递

### 2. 表单数据构建

```javascript
_buildFormData(formData, tmpURL, thread, composer, tmpId, options) {
    super._buildFormData(...arguments);
    if (options?.voice) {
        formData.append("voice", true);
    }
    return formData;
}
```

**表单构建功能**:
- **父类调用**: 执行原有的表单数据构建逻辑
- **选项检查**: 检查选项中是否包含语音标识
- **标识添加**: 向表单数据添加语音消息标识
- **服务器识别**: 帮助服务器识别语音消息上传

## 使用场景

### 1. 附件上传服务语音消息增强

```javascript
// 附件上传服务语音消息增强功能
const AttachmentUploaderVoicePatchEnhancer = {
    enhanceAttachmentUploaderVoicePatch: () => {
        const EnhancedAttachmentUploaderPatch = {
            // 增强的附件数据构建
            _makeAttachmentData(upload, tmpId, thread, tmpUrl) {
                try {
                    // 调用父类方法
                    const attachmentData = super._makeAttachmentData(...arguments);
                    
                    // 处理语音消息数据
                    if (upload.data.get("voice")) {
                        attachmentData.voice = this.processVoiceData(upload.data.get("voice"));
                    }
                    
                    // 添加语音元数据
                    if (this.isVoiceUpload(upload)) {
                        attachmentData.voiceMetadata = this.extractVoiceMetadata(upload);
                    }
                    
                    return attachmentData;
                } catch (error) {
                    console.error('构建语音附件数据失败:', error);
                    throw error;
                }
            },
            
            // 增强的表单数据构建
            _buildFormData(formData, tmpURL, thread, composer, tmpId, options) {
                try {
                    // 调用父类方法
                    super._buildFormData(...arguments);
                    
                    // 处理语音消息选项
                    if (options?.voice) {
                        this.buildVoiceFormData(formData, options);
                    }
                    
                    return formData;
                } catch (error) {
                    console.error('构建语音表单数据失败:', error);
                    throw error;
                }
            },
            
            // 处理语音数据
            processVoiceData(voiceData) {
                try {
                    if (typeof voiceData === 'string') {
                        return JSON.parse(voiceData);
                    }
                    
                    if (typeof voiceData === 'object') {
                        return voiceData;
                    }
                    
                    return {};
                } catch (error) {
                    console.error('处理语音数据失败:', error);
                    return {};
                }
            },
            
            // 检查是否为语音上传
            isVoiceUpload(upload) {
                try {
                    // 检查语音标识
                    if (upload.data.get("voice")) {
                        return true;
                    }
                    
                    // 检查文件类型
                    const file = upload.data.get("file");
                    if (file && this.isVoiceFile(file)) {
                        return true;
                    }
                    
                    // 检查MIME类型
                    const mimetype = upload.data.get("mimetype");
                    if (mimetype && mimetype.startsWith('audio/')) {
                        return true;
                    }
                    
                    return false;
                } catch (error) {
                    console.error('检查语音上传失败:', error);
                    return false;
                }
            },
            
            // 检查是否为语音文件
            isVoiceFile(file) {
                try {
                    // 检查文件类型
                    if (file.type && file.type.startsWith('audio/')) {
                        return true;
                    }
                    
                    // 检查文件扩展名
                    const voiceExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac', '.webm'];
                    const filename = file.name?.toLowerCase() || '';
                    
                    return voiceExtensions.some(ext => filename.endsWith(ext));
                } catch (error) {
                    console.error('检查语音文件失败:', error);
                    return false;
                }
            },
            
            // 提取语音元数据
            extractVoiceMetadata(upload) {
                try {
                    const metadata = {};
                    
                    // 提取文件信息
                    const file = upload.data.get("file");
                    if (file) {
                        metadata.filename = file.name;
                        metadata.filesize = file.size;
                        metadata.mimetype = file.type;
                        metadata.lastModified = file.lastModified;
                    }
                    
                    // 提取语音特定信息
                    const voiceData = upload.data.get("voice");
                    if (voiceData) {
                        const parsedVoiceData = this.processVoiceData(voiceData);
                        metadata.duration = parsedVoiceData.duration;
                        metadata.waveform = parsedVoiceData.waveform;
                        metadata.sampleRate = parsedVoiceData.sampleRate;
                        metadata.channels = parsedVoiceData.channels;
                        metadata.bitrate = parsedVoiceData.bitrate;
                    }
                    
                    // 添加时间戳
                    metadata.uploadTimestamp = Date.now();
                    
                    return metadata;
                } catch (error) {
                    console.error('提取语音元数据失败:', error);
                    return {};
                }
            },
            
            // 构建语音表单数据
            buildVoiceFormData(formData, options) {
                try {
                    // 添加语音标识
                    formData.append("voice", true);
                    
                    // 添加语音特定选项
                    if (options.voiceMetadata) {
                        formData.append("voice_metadata", JSON.stringify(options.voiceMetadata));
                    }
                    
                    if (options.voiceDuration) {
                        formData.append("voice_duration", options.voiceDuration);
                    }
                    
                    if (options.voiceWaveform) {
                        formData.append("voice_waveform", JSON.stringify(options.voiceWaveform));
                    }
                    
                    if (options.voiceFormat) {
                        formData.append("voice_format", options.voiceFormat);
                    }
                    
                    if (options.voiceQuality) {
                        formData.append("voice_quality", options.voiceQuality);
                    }
                    
                    // 添加录制信息
                    if (options.recordingInfo) {
                        formData.append("recording_info", JSON.stringify(options.recordingInfo));
                    }
                    
                } catch (error) {
                    console.error('构建语音表单数据失败:', error);
                }
            },
            
            // 验证语音上传数据
            validateVoiceUpload(upload, options) {
                try {
                    const errors = [];
                    
                    // 检查文件
                    const file = upload.data.get("file");
                    if (!file) {
                        errors.push('缺少语音文件');
                    } else {
                        // 检查文件大小
                        const maxSize = 50 * 1024 * 1024; // 50MB
                        if (file.size > maxSize) {
                            errors.push('语音文件过大');
                        }
                        
                        // 检查文件类型
                        if (!this.isVoiceFile(file)) {
                            errors.push('不支持的语音文件格式');
                        }
                    }
                    
                    // 检查语音数据
                    const voiceData = upload.data.get("voice");
                    if (voiceData) {
                        const parsedData = this.processVoiceData(voiceData);
                        
                        // 检查时长
                        if (parsedData.duration && parsedData.duration > 600) { // 10分钟
                            errors.push('语音消息时长过长');
                        }
                        
                        if (parsedData.duration && parsedData.duration < 1) {
                            errors.push('语音消息时长过短');
                        }
                    }
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                } catch (error) {
                    console.error('验证语音上传数据失败:', error);
                    return {
                        isValid: false,
                        errors: ['验证失败']
                    };
                }
            },
            
            // 预处理语音上传
            async preprocessVoiceUpload(upload, options) {
                try {
                    // 验证上传数据
                    const validation = this.validateVoiceUpload(upload, options);
                    if (!validation.isValid) {
                        throw new Error(`语音上传验证失败: ${validation.errors.join(', ')}`);
                    }
                    
                    // 提取音频元数据
                    const file = upload.data.get("file");
                    if (file) {
                        const audioMetadata = await this.extractAudioMetadata(file);
                        
                        // 更新语音数据
                        const voiceData = this.processVoiceData(upload.data.get("voice") || {});
                        const enhancedVoiceData = {
                            ...voiceData,
                            ...audioMetadata,
                            preprocessed: true,
                            preprocessTimestamp: Date.now()
                        };
                        
                        upload.data.set("voice", JSON.stringify(enhancedVoiceData));
                    }
                    
                    return upload;
                } catch (error) {
                    console.error('预处理语音上传失败:', error);
                    throw error;
                }
            },
            
            // 提取音频元数据
            async extractAudioMetadata(file) {
                return new Promise((resolve, reject) => {
                    try {
                        const audio = new Audio();
                        const url = URL.createObjectURL(file);
                        
                        audio.addEventListener('loadedmetadata', () => {
                            const metadata = {
                                duration: audio.duration,
                                naturalWidth: audio.videoWidth || 0,
                                naturalHeight: audio.videoHeight || 0
                            };
                            
                            URL.revokeObjectURL(url);
                            resolve(metadata);
                        });
                        
                        audio.addEventListener('error', (error) => {
                            URL.revokeObjectURL(url);
                            reject(error);
                        });
                        
                        audio.src = url;
                    } catch (error) {
                        reject(error);
                    }
                });
            },
            
            // 记录语音上传
            recordVoiceUpload(upload, options) {
                try {
                    const uploads = JSON.parse(
                        localStorage.getItem('voice_uploads') || '[]'
                    );
                    
                    const file = upload.data.get("file");
                    const voiceData = this.processVoiceData(upload.data.get("voice") || {});
                    
                    uploads.push({
                        filename: file?.name,
                        filesize: file?.size,
                        mimetype: file?.type,
                        duration: voiceData.duration,
                        timestamp: Date.now(),
                        options: options
                    });
                    
                    // 保留最近100个上传记录
                    if (uploads.length > 100) {
                        uploads.splice(0, uploads.length - 100);
                    }
                    
                    localStorage.setItem('voice_uploads', JSON.stringify(uploads));
                } catch (error) {
                    console.warn('记录语音上传失败:', error);
                }
            },
            
            // 获取语音上传统计
            getVoiceUploadStatistics() {
                try {
                    const uploads = JSON.parse(
                        localStorage.getItem('voice_uploads') || '[]'
                    );
                    
                    return {
                        totalUploads: uploads.length,
                        totalDuration: uploads.reduce((sum, upload) => sum + (upload.duration || 0), 0),
                        totalSize: uploads.reduce((sum, upload) => sum + (upload.filesize || 0), 0),
                        averageDuration: uploads.length > 0 ? 
                            uploads.reduce((sum, upload) => sum + (upload.duration || 0), 0) / uploads.length : 0,
                        recentUploads: uploads.slice(-10)
                    };
                } catch (error) {
                    return {
                        totalUploads: 0,
                        totalDuration: 0,
                        totalSize: 0,
                        averageDuration: 0,
                        recentUploads: []
                    };
                }
            }
        };
        
        patch(AttachmentUploadService.prototype, EnhancedAttachmentUploaderPatch);
    }
};

// 应用附件上传服务语音消息补丁增强
AttachmentUploaderVoicePatchEnhancer.enhanceAttachmentUploaderVoicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式服务扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 数据处理
- 智能的数据构建
- 语音消息特殊处理
- 完整的数据传递

### 3. 表单构建
- 动态表单数据构建
- 条件化数据添加
- 服务器兼容性

### 4. 上传优化
- 专门的语音上传逻辑
- 元数据提取和处理
- 错误处理机制

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同类型文件的处理策略
- 可配置的上传行为

### 3. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 数据的增强处理

## 注意事项

1. **数据完整性**: 确保语音消息数据的完整传递
2. **服务器兼容**: 保持与服务器端的数据格式兼容
3. **错误处理**: 提供完善的错误处理机制
4. **性能优化**: 避免大文件上传影响性能

## 扩展建议

1. **数据验证**: 添加更完善的语音数据验证
2. **压缩优化**: 实现语音文件的压缩和优化
3. **进度跟踪**: 提供详细的上传进度跟踪
4. **断点续传**: 支持大文件的断点续传
5. **批量上传**: 支持多个语音文件的批量上传

该补丁为附件上传服务提供了重要的语音消息支持，是语音消息功能的核心上传组件。

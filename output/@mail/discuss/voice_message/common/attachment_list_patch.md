# Attachment List Patch - 附件列表补丁

## 概述

`attachment_list_patch.js` 实现了对 Odoo 讨论应用附件列表组件的补丁扩展，专门添加了语音播放器功能。该补丁通过Odoo的补丁机制扩展了AttachmentList组件，注册了VoicePlayer组件，为附件列表提供了语音消息播放支持，是讨论应用语音消息功能在附件列表中的集成组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/attachment_list_patch.js`
- **行数**: 17
- **模块**: `@mail/discuss/voice_message/common/attachment_list_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/attachment_list'                    // 附件列表组件
'@mail/discuss/voice_message/common/voice_player'     // 语音播放器组件
'@web/core/utils/patch'                                // 补丁工具
```

## 补丁定义

### AttachmentList 补丁

```javascript
patch(AttachmentList, {
    components: { ...AttachmentList.components, VoicePlayer },
});
```

**补丁特性**:
- 扩展附件列表组件功能
- 注册VoicePlayer组件
- 保持原有组件结构
- 支持语音消息播放

## 核心功能

### 1. 组件注册

```javascript
patch(AttachmentList, {
    components: { ...AttachmentList.components, VoicePlayer },
});
```

**注册功能**:
- **组件扩展**: 将VoicePlayer组件添加到AttachmentList的子组件列表
- **功能集成**: 集成语音播放功能
- **无缝集成**: 与现有附件列表框架无缝集成
- **模块化设计**: 保持组件的模块化结构

## 使用场景

### 1. 附件列表语音播放增强

```javascript
// 附件列表语音播放增强功能
const AttachmentListVoicePatchEnhancer = {
    enhanceAttachmentListVoicePatch: () => {
        const EnhancedAttachmentListPatch = {
            components: { 
                ...AttachmentList.components, 
                VoicePlayer,
                VoiceWaveform,
                VoiceControls,
                VoiceProgress,
                VoiceMetadata
            },
            
            setup() {
                super.setup();
                
                // 增强的语音播放状态管理
                this.voiceState = useState({
                    currentPlaying: null,
                    playbackRate: 1.0,
                    volume: 1.0,
                    autoPlay: false,
                    showWaveform: true,
                    showMetadata: true,
                    playbackHistory: [],
                    favoriteVoices: new Set()
                });
                
                // 增强的服务
                this.audioService = useService("audio");
                this.notificationService = useService("notification");
                this.userService = useService("user");
                
                // 设置增强功能
                this.setupVoiceEnhancements();
            },
            
            // 设置语音增强功能
            setupVoiceEnhancements() {
                // 设置语音播放管理
                this.setupVoicePlaybackManagement();
                
                // 设置语音控制
                this.setupVoiceControls();
                
                // 设置播放历史
                this.setupPlaybackHistory();
                
                // 设置用户偏好
                this.setupUserPreferences();
                
                // 设置键盘快捷键
                this.setupVoiceKeyboardShortcuts();
            },
            
            // 设置语音播放管理
            setupVoicePlaybackManagement() {
                // 全局播放控制
                this.playVoiceMessage = async (attachment) => {
                    try {
                        // 停止当前播放
                        if (this.voiceState.currentPlaying) {
                            await this.stopCurrentVoice();
                        }
                        
                        // 开始新的播放
                        this.voiceState.currentPlaying = attachment.id;
                        
                        // 记录播放历史
                        this.recordVoicePlayback(attachment);
                        
                        // 触发播放事件
                        this.triggerVoicePlayEvent(attachment);
                        
                    } catch (error) {
                        console.error('播放语音消息失败:', error);
                        this.handleVoicePlayError(error);
                    }
                };
                
                // 停止当前语音
                this.stopCurrentVoice = async () => {
                    try {
                        if (this.voiceState.currentPlaying) {
                            const currentId = this.voiceState.currentPlaying;
                            this.voiceState.currentPlaying = null;
                            
                            // 触发停止事件
                            this.triggerVoiceStopEvent(currentId);
                        }
                    } catch (error) {
                        console.error('停止语音播放失败:', error);
                    }
                };
                
                // 暂停/恢复播放
                this.toggleVoicePlayback = async (attachment) => {
                    try {
                        if (this.voiceState.currentPlaying === attachment.id) {
                            // 当前正在播放，暂停
                            await this.pauseVoicePlayback(attachment);
                        } else {
                            // 开始播放
                            await this.playVoiceMessage(attachment);
                        }
                    } catch (error) {
                        console.error('切换语音播放状态失败:', error);
                    }
                };
            },
            
            // 设置语音控制
            setupVoiceControls() {
                // 调整播放速度
                this.setPlaybackRate = (rate) => {
                    try {
                        this.voiceState.playbackRate = Math.max(0.5, Math.min(2.0, rate));
                        this.saveUserPreferences();
                        
                        // 应用到当前播放
                        if (this.voiceState.currentPlaying) {
                            this.applyPlaybackRate(this.voiceState.playbackRate);
                        }
                    } catch (error) {
                        console.error('设置播放速度失败:', error);
                    }
                };
                
                // 调整音量
                this.setVolume = (volume) => {
                    try {
                        this.voiceState.volume = Math.max(0, Math.min(1, volume));
                        this.saveUserPreferences();
                        
                        // 应用到当前播放
                        if (this.voiceState.currentPlaying) {
                            this.applyVolume(this.voiceState.volume);
                        }
                    } catch (error) {
                        console.error('设置音量失败:', error);
                    }
                };
                
                // 跳转到指定位置
                this.seekTo = (attachment, position) => {
                    try {
                        if (this.voiceState.currentPlaying === attachment.id) {
                            this.applySeek(position);
                        }
                    } catch (error) {
                        console.error('跳转播放位置失败:', error);
                    }
                };
            },
            
            // 获取语音附件
            get voiceAttachments() {
                return this.props.attachments?.filter(attachment => 
                    this.isVoiceMessage(attachment)
                ) || [];
            },
            
            // 检查是否为语音消息
            isVoiceMessage(attachment) {
                if (!attachment) return false;
                
                // 检查MIME类型
                if (attachment.mimetype?.startsWith('audio/')) {
                    return true;
                }
                
                // 检查文件扩展名
                const voiceExtensions = ['.mp3', '.wav', '.ogg', '.m4a', '.aac'];
                const filename = attachment.filename?.toLowerCase() || '';
                
                return voiceExtensions.some(ext => filename.endsWith(ext));
            },
            
            // 获取语音消息时长
            getVoiceDuration(attachment) {
                try {
                    // 从附件元数据获取时长
                    if (attachment.voice_duration) {
                        return attachment.voice_duration;
                    }
                    
                    // 从缓存获取时长
                    const cached = this.getCachedVoiceMetadata(attachment.id);
                    if (cached?.duration) {
                        return cached.duration;
                    }
                    
                    return null;
                } catch (error) {
                    console.warn('获取语音时长失败:', error);
                    return null;
                }
            },
            
            // 获取语音消息波形数据
            getVoiceWaveform(attachment) {
                try {
                    // 从附件元数据获取波形
                    if (attachment.voice_waveform) {
                        return attachment.voice_waveform;
                    }
                    
                    // 从缓存获取波形
                    const cached = this.getCachedVoiceMetadata(attachment.id);
                    if (cached?.waveform) {
                        return cached.waveform;
                    }
                    
                    return null;
                } catch (error) {
                    console.warn('获取语音波形失败:', error);
                    return null;
                }
            },
            
            // 记录语音播放
            recordVoicePlayback(attachment) {
                try {
                    const playback = {
                        attachmentId: attachment.id,
                        filename: attachment.filename,
                        duration: this.getVoiceDuration(attachment),
                        timestamp: Date.now(),
                        userId: this.userService.userId
                    };
                    
                    this.voiceState.playbackHistory.unshift(playback);
                    
                    // 限制历史记录数量
                    if (this.voiceState.playbackHistory.length > 50) {
                        this.voiceState.playbackHistory.splice(50);
                    }
                    
                    // 保存到本地存储
                    this.savePlaybackHistory();
                    
                } catch (error) {
                    console.warn('记录语音播放失败:', error);
                }
            },
            
            // 添加到收藏
            addToFavorites(attachment) {
                try {
                    this.voiceState.favoriteVoices.add(attachment.id);
                    this.saveFavoriteVoices();
                    
                    this.notificationService.add('已添加到语音收藏', { type: 'success' });
                } catch (error) {
                    console.error('添加语音收藏失败:', error);
                }
            },
            
            // 从收藏移除
            removeFromFavorites(attachment) {
                try {
                    this.voiceState.favoriteVoices.delete(attachment.id);
                    this.saveFavoriteVoices();
                    
                    this.notificationService.add('已从语音收藏移除', { type: 'info' });
                } catch (error) {
                    console.error('移除语音收藏失败:', error);
                }
            },
            
            // 检查是否为收藏
            isFavoriteVoice(attachment) {
                return this.voiceState.favoriteVoices.has(attachment.id);
            },
            
            // 获取缓存的语音元数据
            getCachedVoiceMetadata(attachmentId) {
                try {
                    const cache = JSON.parse(
                        localStorage.getItem('voice_metadata_cache') || '{}'
                    );
                    return cache[attachmentId] || null;
                } catch (error) {
                    return null;
                }
            },
            
            // 缓存语音元数据
            cacheVoiceMetadata(attachmentId, metadata) {
                try {
                    const cache = JSON.parse(
                        localStorage.getItem('voice_metadata_cache') || '{}'
                    );
                    
                    cache[attachmentId] = {
                        ...metadata,
                        timestamp: Date.now()
                    };
                    
                    // 限制缓存大小
                    const keys = Object.keys(cache);
                    if (keys.length > 100) {
                        // 删除最旧的缓存
                        const oldestKey = keys.reduce((oldest, key) => 
                            cache[key].timestamp < cache[oldest].timestamp ? key : oldest
                        );
                        delete cache[oldestKey];
                    }
                    
                    localStorage.setItem('voice_metadata_cache', JSON.stringify(cache));
                } catch (error) {
                    console.warn('缓存语音元数据失败:', error);
                }
            },
            
            // 加载用户偏好
            loadUserPreferences() {
                try {
                    const prefs = JSON.parse(
                        localStorage.getItem('voice_player_preferences') || '{}'
                    );
                    
                    this.voiceState.playbackRate = prefs.playbackRate || 1.0;
                    this.voiceState.volume = prefs.volume || 1.0;
                    this.voiceState.autoPlay = prefs.autoPlay || false;
                    this.voiceState.showWaveform = prefs.showWaveform !== false;
                    this.voiceState.showMetadata = prefs.showMetadata !== false;
                    
                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                }
            },
            
            // 保存用户偏好
            saveUserPreferences() {
                try {
                    const prefs = {
                        playbackRate: this.voiceState.playbackRate,
                        volume: this.voiceState.volume,
                        autoPlay: this.voiceState.autoPlay,
                        showWaveform: this.voiceState.showWaveform,
                        showMetadata: this.voiceState.showMetadata
                    };
                    
                    localStorage.setItem('voice_player_preferences', JSON.stringify(prefs));
                } catch (error) {
                    console.warn('保存用户偏好失败:', error);
                }
            },
            
            // 保存播放历史
            savePlaybackHistory() {
                try {
                    localStorage.setItem(
                        'voice_playback_history',
                        JSON.stringify(this.voiceState.playbackHistory)
                    );
                } catch (error) {
                    console.warn('保存播放历史失败:', error);
                }
            },
            
            // 保存收藏语音
            saveFavoriteVoices() {
                try {
                    localStorage.setItem(
                        'favorite_voices',
                        JSON.stringify(Array.from(this.voiceState.favoriteVoices))
                    );
                } catch (error) {
                    console.warn('保存收藏语音失败:', error);
                }
            },
            
            // 获取语音播放统计
            getVoicePlaybackStatistics() {
                return {
                    totalVoiceAttachments: this.voiceAttachments.length,
                    currentPlaying: this.voiceState.currentPlaying,
                    playbackRate: this.voiceState.playbackRate,
                    volume: this.voiceState.volume,
                    playbackHistoryCount: this.voiceState.playbackHistory.length,
                    favoriteVoicesCount: this.voiceState.favoriteVoices.size,
                    showWaveform: this.voiceState.showWaveform,
                    showMetadata: this.voiceState.showMetadata
                };
            }
        };
        
        patch(AttachmentList, EnhancedAttachmentListPatch);
    }
};

// 应用附件列表语音播放补丁增强
AttachmentListVoicePatchEnhancer.enhanceAttachmentListVoicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 组件集成
- 无缝组件注册
- 模块化设计
- 清晰的组件边界

### 3. 语音支持
- 语音播放功能集成
- 附件列表的功能扩展
- 用户友好的界面

### 4. 简洁设计
- 最小化的代码实现
- 专注于核心功能
- 易于维护和扩展

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 组合模式 (Composite Pattern)
- 组件的组合和管理
- 统一的组件接口

### 3. 装饰器模式 (Decorator Pattern)
- 功能的动态添加
- 不改变原有结构

## 注意事项

1. **组件兼容性**: 确保新组件与附件列表框架的兼容性
2. **性能影响**: 避免过多组件影响渲染性能
3. **音频处理**: 正确处理音频文件的加载和播放
4. **用户体验**: 提供直观的语音播放控制

## 扩展建议

1. **播放控制**: 添加更丰富的语音播放控制功能
2. **可视化**: 实现语音波形的可视化显示
3. **批量操作**: 支持批量语音消息管理
4. **个性化**: 允许用户自定义播放器设置
5. **分析功能**: 提供语音消息的分析和统计

该补丁为附件列表提供了重要的语音播放功能，是语音消息功能在附件列表中的简洁集成。

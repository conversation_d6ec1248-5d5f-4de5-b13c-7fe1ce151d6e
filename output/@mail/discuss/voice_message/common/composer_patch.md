# Composer Patch - 编辑器补丁

## 概述

`composer_patch.js` 实现了对 Odoo 讨论应用编辑器组件的补丁扩展，专门添加了语音录制功能。该补丁通过Odoo的补丁机制扩展了Composer组件，集成了VoiceRecorder组件、录制状态管理、发送按钮控制和键盘事件处理等特性，为用户在编辑器中提供了语音消息录制功能，是讨论应用语音消息功能的核心编辑器组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/composer_patch.js`
- **行数**: 34
- **模块**: `@mail/discuss/voice_message/common/composer_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/composer'                           // 编辑器组件
'@mail/discuss/voice_message/common/voice_recorder'    // 语音录制器组件
'@web/core/utils/patch'                                // 补丁工具
```

## 补丁定义

### 组件注册

```javascript
patch(Composer, {
    components: { ...Composer.components, VoiceRecorder },
});
```

**组件注册功能**:
- 将VoiceRecorder组件添加到Composer的子组件列表
- 扩展编辑器的功能范围
- 支持语音录制功能

### Composer 原型补丁

```javascript
patch(Composer.prototype, {
    setup() {
        super.setup();
        this.state.recording = false;
    },
    get isSendButtonDisabled() {
        return this.state.recording || super.isSendButtonDisabled;
    },
    onKeydown(ev) {
        if (ev.key === "Enter" && this.state.recording) {
            ev.preventDefault();
            return;
        }
        return super.onKeydown(ev);
    },
});
```

**补丁特性**:
- 扩展编辑器组件功能
- 添加录制状态管理
- 控制发送按钮状态
- 处理键盘事件

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.state.recording = false;
}
```

**初始化功能**:
- **父类调用**: 保持原有的初始化逻辑
- **状态初始化**: 初始化录制状态为false
- **状态管理**: 为录制功能提供状态支持

### 2. 发送按钮控制

```javascript
get isSendButtonDisabled() {
    return this.state.recording || super.isSendButtonDisabled;
}
```

**按钮控制功能**:
- **录制检查**: 录制时禁用发送按钮
- **原有逻辑**: 保持原有的禁用逻辑
- **用户体验**: 防止录制时误发送消息
- **状态联动**: 与录制状态联动

### 3. 键盘事件处理

```javascript
onKeydown(ev) {
    if (ev.key === "Enter" && this.state.recording) {
        ev.preventDefault();
        return;
    }
    return super.onKeydown(ev);
}
```

**事件处理功能**:
- **Enter键拦截**: 录制时阻止Enter键发送消息
- **事件阻止**: 使用preventDefault阻止默认行为
- **条件判断**: 仅在录制状态时拦截
- **父类调用**: 非录制状态时执行原有逻辑

## 使用场景

### 1. 编辑器语音录制增强

```javascript
// 编辑器语音录制增强功能
const ComposerVoicePatchEnhancer = {
    enhanceComposerVoicePatch: () => {
        const EnhancedComposerPatch = {
            setup() {
                super.setup();
                
                // 原有的录制状态
                this.state.recording = false;
                
                // 增强的录制状态管理
                this.voiceState = useState({
                    isRecording: false,
                    isPaused: false,
                    recordingDuration: 0,
                    recordingStartTime: null,
                    recordingPauseTime: null,
                    maxRecordingDuration: 600, // 10分钟
                    minRecordingDuration: 1,   // 1秒
                    recordingQuality: 'high',  // low, medium, high
                    autoStop: true,
                    showWaveform: true,
                    recordingHistory: []
                });
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.audioService = useService("audio");
                this.userService = useService("user");
                
                // 设置增强功能
                this.setupVoiceRecordingEnhancements();
            },
            
            // 设置语音录制增强功能
            setupVoiceRecordingEnhancements() {
                // 设置录制控制
                this.setupRecordingControls();
                
                // 设置录制监控
                this.setupRecordingMonitoring();
                
                // 设置键盘快捷键
                this.setupVoiceKeyboardShortcuts();
                
                // 设置录制历史
                this.setupRecordingHistory();
                
                // 设置权限检查
                this.setupPermissionChecks();
            },
            
            // 增强的发送按钮控制
            get isSendButtonDisabled() {
                // 检查录制状态
                if (this.state.recording || this.voiceState.isRecording) {
                    return true;
                }
                
                // 检查语音附件验证
                if (this.hasVoiceAttachment && !this.isVoiceAttachmentValid()) {
                    return true;
                }
                
                // 原有逻辑
                return super.isSendButtonDisabled;
            },
            
            // 增强的键盘事件处理
            onKeydown(ev) {
                try {
                    // 录制状态下的特殊处理
                    if (this.state.recording || this.voiceState.isRecording) {
                        return this.handleRecordingKeydown(ev);
                    }
                    
                    // 语音快捷键处理
                    if (this.handleVoiceShortcuts(ev)) {
                        return;
                    }
                    
                    // 原有逻辑
                    return super.onKeydown(ev);
                } catch (error) {
                    console.error('处理键盘事件失败:', error);
                    return super.onKeydown(ev);
                }
            },
            
            // 处理录制状态下的键盘事件
            handleRecordingKeydown(ev) {
                switch (ev.key) {
                    case "Enter":
                        // 阻止发送消息
                        ev.preventDefault();
                        return;
                        
                    case "Escape":
                        // 取消录制
                        ev.preventDefault();
                        this.cancelRecording();
                        return;
                        
                    case " ": // 空格键
                        // 暂停/恢复录制
                        if (ev.ctrlKey) {
                            ev.preventDefault();
                            this.toggleRecordingPause();
                            return;
                        }
                        break;
                        
                    case "s":
                        // Ctrl+S 停止录制
                        if (ev.ctrlKey) {
                            ev.preventDefault();
                            this.stopRecording();
                            return;
                        }
                        break;
                }
                
                // 其他键盘事件在录制时被忽略
                ev.preventDefault();
            },
            
            // 处理语音快捷键
            handleVoiceShortcuts(ev) {
                // Ctrl+Shift+V 开始录制
                if (ev.ctrlKey && ev.shiftKey && ev.key === 'V') {
                    ev.preventDefault();
                    this.startRecording();
                    return true;
                }
                
                // Ctrl+Shift+P 播放最后的录制
                if (ev.ctrlKey && ev.shiftKey && ev.key === 'P') {
                    ev.preventDefault();
                    this.playLastRecording();
                    return true;
                }
                
                return false;
            },
            
            // 开始录制
            async startRecording() {
                try {
                    // 检查权限
                    if (!await this.checkRecordingPermission()) {
                        throw new Error('没有录制权限');
                    }
                    
                    // 检查设备
                    if (!await this.checkRecordingDevice()) {
                        throw new Error('录制设备不可用');
                    }
                    
                    // 设置录制状态
                    this.state.recording = true;
                    this.voiceState.isRecording = true;
                    this.voiceState.recordingStartTime = Date.now();
                    this.voiceState.recordingDuration = 0;
                    
                    // 开始录制监控
                    this.startRecordingMonitoring();
                    
                    // 通知用户
                    this.notificationService.add('开始录制语音消息', { type: 'info' });
                    
                } catch (error) {
                    console.error('开始录制失败:', error);
                    this.handleRecordingError(error);
                }
            },
            
            // 停止录制
            async stopRecording() {
                try {
                    if (!this.voiceState.isRecording) {
                        return;
                    }
                    
                    // 检查录制时长
                    if (this.voiceState.recordingDuration < this.voiceState.minRecordingDuration) {
                        throw new Error('录制时间过短');
                    }
                    
                    // 设置状态
                    this.state.recording = false;
                    this.voiceState.isRecording = false;
                    
                    // 停止录制监控
                    this.stopRecordingMonitoring();
                    
                    // 处理录制结果
                    await this.processRecordingResult();
                    
                    // 记录录制历史
                    this.recordRecordingHistory();
                    
                    // 通知用户
                    this.notificationService.add('录制完成', { type: 'success' });
                    
                } catch (error) {
                    console.error('停止录制失败:', error);
                    this.handleRecordingError(error);
                }
            },
            
            // 取消录制
            cancelRecording() {
                try {
                    if (!this.voiceState.isRecording) {
                        return;
                    }
                    
                    // 设置状态
                    this.state.recording = false;
                    this.voiceState.isRecording = false;
                    
                    // 停止录制监控
                    this.stopRecordingMonitoring();
                    
                    // 清理录制数据
                    this.cleanupRecordingData();
                    
                    // 通知用户
                    this.notificationService.add('录制已取消', { type: 'warning' });
                    
                } catch (error) {
                    console.error('取消录制失败:', error);
                }
            },
            
            // 暂停/恢复录制
            toggleRecordingPause() {
                try {
                    if (!this.voiceState.isRecording) {
                        return;
                    }
                    
                    if (this.voiceState.isPaused) {
                        // 恢复录制
                        this.voiceState.isPaused = false;
                        this.voiceState.recordingPauseTime = null;
                        this.notificationService.add('录制已恢复', { type: 'info' });
                    } else {
                        // 暂停录制
                        this.voiceState.isPaused = true;
                        this.voiceState.recordingPauseTime = Date.now();
                        this.notificationService.add('录制已暂停', { type: 'info' });
                    }
                } catch (error) {
                    console.error('切换录制暂停状态失败:', error);
                }
            },
            
            // 检查录制权限
            async checkRecordingPermission() {
                try {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    
                    if (permission.state === 'granted') {
                        return true;
                    }
                    
                    if (permission.state === 'prompt') {
                        // 请求权限
                        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        stream.getTracks().forEach(track => track.stop());
                        return true;
                    }
                    
                    return false;
                } catch (error) {
                    console.error('检查录制权限失败:', error);
                    return false;
                }
            },
            
            // 检查录制设备
            async checkRecordingDevice() {
                try {
                    const devices = await navigator.mediaDevices.enumerateDevices();
                    const audioInputs = devices.filter(device => device.kind === 'audioinput');
                    
                    return audioInputs.length > 0;
                } catch (error) {
                    console.error('检查录制设备失败:', error);
                    return false;
                }
            },
            
            // 开始录制监控
            startRecordingMonitoring() {
                this.recordingTimer = setInterval(() => {
                    if (this.voiceState.isRecording && !this.voiceState.isPaused) {
                        const now = Date.now();
                        this.voiceState.recordingDuration = 
                            (now - this.voiceState.recordingStartTime) / 1000;
                        
                        // 检查最大录制时长
                        if (this.voiceState.recordingDuration >= this.voiceState.maxRecordingDuration) {
                            this.stopRecording();
                        }
                    }
                }, 100);
            },
            
            // 停止录制监控
            stopRecordingMonitoring() {
                if (this.recordingTimer) {
                    clearInterval(this.recordingTimer);
                    this.recordingTimer = null;
                }
            },
            
            // 处理录制错误
            handleRecordingError(error) {
                // 重置状态
                this.state.recording = false;
                this.voiceState.isRecording = false;
                this.voiceState.isPaused = false;
                
                // 停止监控
                this.stopRecordingMonitoring();
                
                // 显示错误
                let message = '录制失败';
                if (error.message.includes('权限')) {
                    message = '没有麦克风权限，请在浏览器设置中允许访问麦克风';
                } else if (error.message.includes('设备')) {
                    message = '找不到可用的录制设备';
                } else if (error.message.includes('时间')) {
                    message = '录制时间过短，请录制至少1秒';
                }
                
                this.notificationService.add(message, { type: 'error' });
            },
            
            // 检查语音附件是否有效
            isVoiceAttachmentValid() {
                try {
                    const voiceAttachment = this.props.composer.voiceAttachment;
                    
                    if (!voiceAttachment) {
                        return true; // 没有语音附件时认为有效
                    }
                    
                    // 检查语音数据
                    if (!voiceAttachment.voice) {
                        return false;
                    }
                    
                    // 检查状态
                    if (voiceAttachment.voice.status === 'error') {
                        return false;
                    }
                    
                    // 检查时长
                    if (voiceAttachment.voice.duration < this.voiceState.minRecordingDuration) {
                        return false;
                    }
                    
                    return true;
                } catch (error) {
                    console.error('检查语音附件有效性失败:', error);
                    return false;
                }
            },
            
            // 获取录制状态文本
            get recordingStatusText() {
                if (!this.voiceState.isRecording) {
                    return '';
                }
                
                if (this.voiceState.isPaused) {
                    return '录制已暂停';
                }
                
                const duration = Math.floor(this.voiceState.recordingDuration);
                const minutes = Math.floor(duration / 60);
                const seconds = duration % 60;
                
                return `录制中 ${minutes}:${seconds.toString().padStart(2, '0')}`;
            },
            
            // 获取编辑器语音录制统计
            getComposerVoiceRecordingStatistics() {
                return {
                    isRecording: this.voiceState.isRecording,
                    isPaused: this.voiceState.isPaused,
                    recordingDuration: this.voiceState.recordingDuration,
                    recordingStatusText: this.recordingStatusText,
                    hasVoiceAttachment: !!this.props.composer.voiceAttachment,
                    isVoiceAttachmentValid: this.isVoiceAttachmentValid(),
                    isSendButtonDisabled: this.isSendButtonDisabled,
                    recordingHistory: this.voiceState.recordingHistory.length
                };
            }
        };
        
        patch(Composer.prototype, EnhancedComposerPatch);
    }
};

// 应用编辑器语音录制补丁增强
ComposerVoicePatchEnhancer.enhanceComposerVoicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式组件扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 状态管理
- 完整的录制状态跟踪
- 智能的状态联动
- 用户友好的状态反馈

### 3. 事件处理
- 智能的键盘事件拦截
- 条件化的事件处理
- 用户体验优化

### 4. 组件集成
- 无缝的组件注册
- 模块化的功能扩展
- 清晰的组件边界

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 状态模式 (State Pattern)
- 录制状态的管理
- 状态转换控制

### 3. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 事件驱动的架构

## 注意事项

1. **状态一致性**: 确保录制状态的一致性和准确性
2. **用户体验**: 提供清晰的录制状态反馈
3. **事件处理**: 正确处理键盘事件的拦截和传递
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **录制控制**: 添加更丰富的录制控制功能
2. **质量设置**: 支持录制质量的配置
3. **实时反馈**: 实现录制过程的实时反馈
4. **快捷键**: 添加更多的键盘快捷键支持
5. **录制历史**: 提供录制历史的管理功能

该补丁为编辑器提供了重要的语音录制功能，是语音消息功能在编辑器中的核心集成组件。

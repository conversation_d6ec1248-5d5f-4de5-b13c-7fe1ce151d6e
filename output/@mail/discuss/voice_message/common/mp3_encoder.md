# MP3 Encoder - MP3编码器

## 概述

`mp3_encoder.js` 实现了 Odoo 讨论应用中的MP3音频编码器，提供了将音频数据编码为MP3格式的功能。该编码器基于lamejs库，支持音频格式转换、采样率配置、比特率设置和缓冲区管理等特性，为语音消息录制提供了高质量的音频编码支持，是讨论应用语音消息功能的核心音频处理组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/mp3_encoder.js`
- **行数**: 77
- **模块**: `@mail/discuss/voice_message/common/mp3_encoder`

## 依赖关系

```javascript
// 外部依赖
lamejs                    // MP3编码库 (全局变量)
```

## 常量定义

### 采样常量

```javascript
const MAX_SAMPLES = 1152;
```

**常量功能**:
- **MAX_SAMPLES**: 每次编码的最大采样数，符合MP3编码标准
- **性能优化**: 控制编码块大小，优化内存使用
- **标准兼容**: 遵循MP3编码的技术规范

## 类定义

### Mp3Encoder 类

```javascript
const Mp3Encoder = class Mp3Encoder {
    /** @type {Object} */
    config;
    /** @type {boolean} */
    encoding;
    /** @type {lameJs.Mp3Encoder} */
    mp3Encoder;
    /** @type {Int16Array} */
    samplesMono;
}
```

**类特性**:
- 封装MP3编码功能
- 管理编码配置和状态
- 提供完整的编码流程
- 支持缓冲区管理

## 核心功能

### 1. 构造函数

```javascript
constructor(config = {}) {
    this.config = {
        sampleRate: 44100,
        bitRate: 128,
    };
    Object.assign(this.config, config);
    this.mp3Encoder = new lamejs.Mp3Encoder(1, this.config.sampleRate, this.config.bitRate);
    this.samplesMono = null;
    this.clearBuffer();
}
```

**构造功能**:
- **默认配置**: 设置44.1kHz采样率和128kbps比特率
- **配置合并**: 支持自定义配置覆盖默认值
- **编码器初始化**: 创建单声道MP3编码器实例
- **缓冲区初始化**: 初始化数据缓冲区

### 2. 缓冲区管理

```javascript
clearBuffer() {
    this.dataBuffer = [];
}

appendToBuffer(buffer) {
    this.dataBuffer.push(new Int8Array(buffer));
}
```

**缓冲区功能**:
- **清空缓冲区**: 重置数据缓冲区为空数组
- **追加数据**: 将编码后的数据追加到缓冲区
- **数据类型**: 使用Int8Array存储二进制数据
- **内存管理**: 高效的内存使用和管理

### 3. 音频格式转换

```javascript
floatTo16BitPCM(input, output) {
    for (let i = 0; i < input.length; i++) {
        const s = Math.max(-1, Math.min(1, input[i]));
        output[i] = s < 0 ? s * 0x8000 : s * 0x7fff;
    }
}

convertBuffer(arrayBuffer) {
    const data = new Float32Array(arrayBuffer);
    const out = new Int16Array(arrayBuffer.length);
    this.floatTo16BitPCM(data, out);
    return out;
}
```

**转换功能**:
- **浮点转换**: 将32位浮点音频数据转换为16位PCM
- **范围限制**: 确保音频数据在有效范围内(-1到1)
- **精度处理**: 正负值使用不同的缩放因子
- **格式标准**: 符合PCM音频格式标准

### 4. 音频编码

```javascript
encode(arrayBuffer) {
    this.encoding = true;
    this.samplesMono = this.convertBuffer(arrayBuffer);
    let remaining = this.samplesMono.length;
    for (let i = 0; remaining >= 0; i += MAX_SAMPLES) {
        const left = this.samplesMono.subarray(i, i + MAX_SAMPLES);
        const mp3buffer = this.mp3Encoder.encodeBuffer(left);
        this.appendToBuffer(mp3buffer);
        remaining -= MAX_SAMPLES;
    }
}
```

**编码功能**:
- **状态设置**: 标记编码状态为进行中
- **数据转换**: 将输入数据转换为PCM格式
- **分块编码**: 按MAX_SAMPLES大小分块处理
- **缓冲区管理**: 将编码结果存储到缓冲区

### 5. 编码完成

```javascript
finish() {
    if (this.encoding) {
        this.appendToBuffer(this.mp3Encoder.flush());
        return this.dataBuffer;
    } else {
        return [];
    }
}
```

**完成功能**:
- **状态检查**: 检查是否正在编码
- **刷新缓冲**: 刷新编码器内部缓冲区
- **返回数据**: 返回完整的编码数据
- **错误处理**: 未编码时返回空数组

## 使用场景

### 1. MP3编码器增强

```javascript
// MP3编码器增强功能
const Mp3EncoderEnhancer = {
    enhanceMp3Encoder: () => {
        const EnhancedMp3Encoder = class extends Mp3Encoder {
            constructor(config = {}) {
                // 增强的默认配置
                const enhancedConfig = {
                    sampleRate: 44100,
                    bitRate: 128,
                    channels: 1,
                    quality: 'high', // low, medium, high
                    vbrMode: false,   // 可变比特率模式
                    vbrQuality: 4,    // VBR质量 (0-9)
                    enableMetadata: true,
                    enableProgress: true,
                    chunkSize: MAX_SAMPLES,
                    ...config
                };
                
                super(enhancedConfig);
                
                // 增强的属性
                this.encodingProgress = 0;
                this.totalSamples = 0;
                this.processedSamples = 0;
                this.encodingStartTime = null;
                this.encodingStats = {
                    inputSize: 0,
                    outputSize: 0,
                    compressionRatio: 0,
                    encodingTime: 0,
                    averageSpeed: 0
                };
                
                // 设置增强功能
                this.setupEnhancements();
            }
            
            // 设置增强功能
            setupEnhancements() {
                // 设置质量配置
                this.setupQualityConfig();
                
                // 设置进度跟踪
                this.setupProgressTracking();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
                
                // 设置错误处理
                this.setupErrorHandling();
            }
            
            // 设置质量配置
            setupQualityConfig() {
                const qualitySettings = {
                    low: { bitRate: 64, vbrQuality: 7 },
                    medium: { bitRate: 128, vbrQuality: 4 },
                    high: { bitRate: 192, vbrQuality: 2 }
                };
                
                const quality = qualitySettings[this.config.quality] || qualitySettings.medium;
                this.config.bitRate = quality.bitRate;
                this.config.vbrQuality = quality.vbrQuality;
            }
            
            // 增强的编码方法
            encode(arrayBuffer) {
                try {
                    // 记录开始时间
                    this.encodingStartTime = performance.now();
                    
                    // 设置编码状态
                    this.encoding = true;
                    this.encodingProgress = 0;
                    this.encodingStats.inputSize = arrayBuffer.byteLength;
                    
                    // 转换音频数据
                    this.samplesMono = this.convertBuffer(arrayBuffer);
                    this.totalSamples = this.samplesMono.length;
                    this.processedSamples = 0;
                    
                    // 分块编码
                    let remaining = this.samplesMono.length;
                    for (let i = 0; remaining >= 0; i += this.config.chunkSize) {
                        const left = this.samplesMono.subarray(i, i + this.config.chunkSize);
                        
                        // 编码音频块
                        const mp3buffer = this.encodeChunk(left);
                        this.appendToBuffer(mp3buffer);
                        
                        // 更新进度
                        this.processedSamples += left.length;
                        this.updateProgress();
                        
                        remaining -= this.config.chunkSize;
                        
                        // 允许UI更新
                        if (this.config.enableProgress && i % (this.config.chunkSize * 10) === 0) {
                            await this.yieldToUI();
                        }
                    }
                    
                } catch (error) {
                    console.error('MP3编码失败:', error);
                    this.handleEncodingError(error);
                    throw error;
                }
            }
            
            // 编码音频块
            encodeChunk(samples) {
                try {
                    if (this.config.vbrMode) {
                        // 使用VBR模式
                        return this.mp3Encoder.encodeBuffer(samples, null, this.config.vbrQuality);
                    } else {
                        // 使用CBR模式
                        return this.mp3Encoder.encodeBuffer(samples);
                    }
                } catch (error) {
                    console.error('编码音频块失败:', error);
                    throw error;
                }
            }
            
            // 更新编码进度
            updateProgress() {
                if (this.totalSamples > 0) {
                    this.encodingProgress = (this.processedSamples / this.totalSamples) * 100;
                    
                    // 触发进度事件
                    if (this.config.enableProgress && this.onProgress) {
                        this.onProgress(this.encodingProgress);
                    }
                }
            }
            
            // 让出UI线程
            async yieldToUI() {
                return new Promise(resolve => setTimeout(resolve, 0));
            }
            
            // 增强的完成方法
            finish() {
                try {
                    if (!this.encoding) {
                        return [];
                    }
                    
                    // 刷新编码器
                    this.appendToBuffer(this.mp3Encoder.flush());
                    
                    // 计算统计信息
                    this.calculateEncodingStats();
                    
                    // 添加元数据
                    if (this.config.enableMetadata) {
                        this.addMetadata();
                    }
                    
                    // 重置状态
                    this.encoding = false;
                    this.encodingProgress = 100;
                    
                    return this.dataBuffer;
                    
                } catch (error) {
                    console.error('完成MP3编码失败:', error);
                    this.handleEncodingError(error);
                    throw error;
                }
            }
            
            // 计算编码统计
            calculateEncodingStats() {
                try {
                    const encodingTime = performance.now() - this.encodingStartTime;
                    const outputSize = this.dataBuffer.reduce((size, buffer) => size + buffer.length, 0);
                    
                    this.encodingStats.outputSize = outputSize;
                    this.encodingStats.encodingTime = encodingTime;
                    this.encodingStats.compressionRatio = this.encodingStats.inputSize / outputSize;
                    this.encodingStats.averageSpeed = (this.encodingStats.inputSize / 1024) / (encodingTime / 1000); // KB/s
                    
                } catch (error) {
                    console.error('计算编码统计失败:', error);
                }
            }
            
            // 添加元数据
            addMetadata() {
                try {
                    // 这里可以添加ID3标签等元数据
                    // 由于lamejs的限制，这里只是示例
                    const metadata = {
                        title: 'Voice Message',
                        artist: 'Odoo Discuss',
                        album: 'Voice Messages',
                        year: new Date().getFullYear().toString(),
                        comment: `Encoded at ${this.config.bitRate}kbps`,
                        genre: 'Speech'
                    };
                    
                    // 存储元数据供后续使用
                    this.metadata = metadata;
                    
                } catch (error) {
                    console.error('添加元数据失败:', error);
                }
            }
            
            // 处理编码错误
            handleEncodingError(error) {
                this.encoding = false;
                this.encodingProgress = 0;
                
                // 清理资源
                this.clearBuffer();
                
                // 记录错误
                console.error('MP3编码错误:', {
                    error: error.message,
                    config: this.config,
                    stats: this.encodingStats
                });
            }
            
            // 获取编码信息
            getEncodingInfo() {
                return {
                    isEncoding: this.encoding,
                    progress: this.encodingProgress,
                    config: { ...this.config },
                    stats: { ...this.encodingStats },
                    metadata: this.metadata || null,
                    bufferSize: this.dataBuffer.length
                };
            }
            
            // 设置进度回调
            setProgressCallback(callback) {
                this.onProgress = callback;
            }
            
            // 获取输出Blob
            getBlob() {
                try {
                    if (this.dataBuffer.length === 0) {
                        return null;
                    }
                    
                    return new Blob(this.dataBuffer, { type: 'audio/mp3' });
                } catch (error) {
                    console.error('创建Blob失败:', error);
                    return null;
                }
            }
            
            // 获取输出URL
            getObjectURL() {
                try {
                    const blob = this.getBlob();
                    return blob ? URL.createObjectURL(blob) : null;
                } catch (error) {
                    console.error('创建对象URL失败:', error);
                    return null;
                }
            }
            
            // 下载编码结果
            download(filename = 'voice_message.mp3') {
                try {
                    const blob = this.getBlob();
                    if (!blob) {
                        throw new Error('没有可下载的数据');
                    }
                    
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    
                    URL.revokeObjectURL(url);
                } catch (error) {
                    console.error('下载失败:', error);
                    throw error;
                }
            }
            
            // 重置编码器
            reset() {
                try {
                    this.encoding = false;
                    this.encodingProgress = 0;
                    this.totalSamples = 0;
                    this.processedSamples = 0;
                    this.encodingStartTime = null;
                    
                    this.clearBuffer();
                    
                    // 重新创建编码器
                    this.mp3Encoder = new lamejs.Mp3Encoder(
                        this.config.channels,
                        this.config.sampleRate,
                        this.config.bitRate
                    );
                    
                } catch (error) {
                    console.error('重置编码器失败:', error);
                }
            }
        };
        
        // 替换原始类
        __exports.Mp3Encoder = EnhancedMp3Encoder;
    }
};

// 应用MP3编码器增强
Mp3EncoderEnhancer.enhanceMp3Encoder();
```

## 技术特点

### 1. 音频编码
- 基于lamejs的MP3编码
- 支持多种采样率和比特率
- 高质量的音频压缩

### 2. 格式转换
- 浮点到PCM的精确转换
- 范围限制和精度处理
- 标准格式兼容

### 3. 内存管理
- 高效的缓冲区管理
- 分块处理大文件
- 内存使用优化

### 4. 性能优化
- 固定大小的编码块
- 流式处理支持
- 最小内存占用

## 设计模式

### 1. 工厂模式 (Factory Pattern)
- 编码器实例的创建
- 配置驱动的初始化

### 2. 流处理模式 (Stream Processing Pattern)
- 分块数据处理
- 连续的编码流程

### 3. 缓冲区模式 (Buffer Pattern)
- 数据缓冲和管理
- 内存优化策略

## 注意事项

1. **依赖管理**: 确保lamejs库的正确加载
2. **内存使用**: 注意大文件编码时的内存消耗
3. **性能优化**: 合理设置编码块大小
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **质量控制**: 添加更多的音质控制选项
2. **进度跟踪**: 实现编码进度的实时跟踪
3. **元数据支持**: 支持ID3标签等元数据
4. **多声道支持**: 扩展支持立体声编码
5. **流式编码**: 实现真正的流式编码支持

该编码器为语音消息功能提供了高质量的MP3音频编码支持，是音频处理的核心组件。

# Attachment Model Patch - 附件模型补丁

## 概述

`attachment_model_patch.js` 实现了对 Odoo 讨论应用附件模型的补丁扩展，专门添加了语音消息的特殊处理逻辑。该补丁通过Odoo的补丁机制扩展了Attachment模型，修改了可查看性判断和删除行为，为语音消息附件提供了专门的模型层支持，是讨论应用语音消息功能的核心数据模型组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/attachment_model_patch.js`
- **行数**: 24
- **模块**: `@mail/discuss/voice_message/common/attachment_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/attachment_model'   // 附件模型
'@web/core/utils/patch'                // 补丁工具
```

## 补丁定义

### Attachment 原型补丁

```javascript
patch(Attachment.prototype, {
    get isViewable() {
        return !this.voice && super.isViewable;
    },
    delete() {
        if (this.voice && this.id > 0) {
            this.store.env.services["discuss.voice_message"].activePlayer = null;
        }
        super.delete(...arguments);
    },
});
```

**补丁特性**:
- 扩展附件模型功能
- 修改可查看性逻辑
- 增强删除行为
- 保持原有功能完整性

## 核心功能

### 1. 可查看性判断

```javascript
get isViewable() {
    return !this.voice && super.isViewable;
}
```

**可查看性功能**:
- **语音过滤**: 语音消息不使用标准的可查看逻辑
- **条件判断**: 仅非语音附件使用父类的可查看性判断
- **特殊处理**: 为语音消息提供专门的显示逻辑
- **兼容性**: 保持与原有逻辑的兼容性

### 2. 删除处理

```javascript
delete() {
    if (this.voice && this.id > 0) {
        this.store.env.services["discuss.voice_message"].activePlayer = null;
    }
    super.delete(...arguments);
}
```

**删除处理功能**:
- **语音检查**: 检查是否为语音消息附件
- **播放器清理**: 清除活跃的语音播放器状态
- **服务通知**: 通知语音消息服务停止播放
- **父类调用**: 执行原有的删除逻辑

## 使用场景

### 1. 附件模型语音消息增强

```javascript
// 附件模型语音消息增强功能
const AttachmentModelVoicePatchEnhancer = {
    enhanceAttachmentModelVoicePatch: () => {
        const EnhancedAttachmentModelPatch = {
            // 增强的可查看性判断
            get isViewable() {
                // 语音消息有特殊的查看逻辑
                if (this.voice) {
                    return this.isVoiceViewable();
                }
                
                // 非语音消息使用原有逻辑
                return super.isViewable;
            },
            
            // 语音消息可查看性
            isVoiceViewable() {
                try {
                    // 检查语音文件是否存在
                    if (!this.voice || !this.id) {
                        return false;
                    }
                    
                    // 检查文件状态
                    if (this.voice.status === 'error' || this.voice.status === 'corrupted') {
                        return false;
                    }
                    
                    // 检查用户权限
                    if (!this.hasVoicePlayPermission()) {
                        return false;
                    }
                    
                    // 检查浏览器支持
                    if (!this.isBrowserSupportedForVoice()) {
                        return false;
                    }
                    
                    return true;
                } catch (error) {
                    console.error('检查语音可查看性失败:', error);
                    return false;
                }
            },
            
            // 检查语音播放权限
            hasVoicePlayPermission() {
                try {
                    const user = this.store.env.services.user?.user;
                    
                    if (!user) {
                        return false;
                    }
                    
                    // 检查是否为内部用户
                    if (!user.isInternalUser) {
                        return false;
                    }
                    
                    // 检查消息访问权限
                    if (this.message && !this.message.canRead) {
                        return false;
                    }
                    
                    return true;
                } catch (error) {
                    console.warn('检查语音播放权限失败:', error);
                    return false;
                }
            },
            
            // 检查浏览器语音支持
            isBrowserSupportedForVoice() {
                try {
                    // 检查Audio API支持
                    if (!window.Audio) {
                        return false;
                    }
                    
                    // 检查Web Audio API支持
                    if (!window.AudioContext && !window.webkitAudioContext) {
                        return false;
                    }
                    
                    // 检查媒体源扩展支持
                    if (!window.MediaSource) {
                        return false;
                    }
                    
                    return true;
                } catch (error) {
                    console.warn('检查浏览器语音支持失败:', error);
                    return false;
                }
            },
            
            // 增强的删除处理
            delete() {
                try {
                    // 语音消息的特殊删除处理
                    if (this.voice && this.id > 0) {
                        this.handleVoiceMessageDeletion();
                    }
                    
                    // 调用父类删除方法
                    super.delete(...arguments);
                    
                } catch (error) {
                    console.error('删除附件失败:', error);
                    throw error;
                }
            },
            
            // 处理语音消息删除
            handleVoiceMessageDeletion() {
                try {
                    const voiceService = this.store.env.services["discuss.voice_message"];
                    
                    if (!voiceService) {
                        return;
                    }
                    
                    // 停止当前播放
                    if (voiceService.activePlayer?.attachmentId === this.id) {
                        voiceService.activePlayer = null;
                        voiceService.stopPlayback();
                    }
                    
                    // 清理播放历史
                    this.cleanupVoicePlaybackHistory();
                    
                    // 清理缓存
                    this.cleanupVoiceCache();
                    
                    // 通知其他组件
                    this.notifyVoiceDeletion();
                    
                } catch (error) {
                    console.error('处理语音消息删除失败:', error);
                }
            },
            
            // 清理语音播放历史
            cleanupVoicePlaybackHistory() {
                try {
                    const history = JSON.parse(
                        localStorage.getItem('voice_playback_history') || '[]'
                    );
                    
                    const filteredHistory = history.filter(
                        item => item.attachmentId !== this.id
                    );
                    
                    localStorage.setItem(
                        'voice_playback_history',
                        JSON.stringify(filteredHistory)
                    );
                } catch (error) {
                    console.warn('清理语音播放历史失败:', error);
                }
            },
            
            // 清理语音缓存
            cleanupVoiceCache() {
                try {
                    // 清理元数据缓存
                    const metadataCache = JSON.parse(
                        localStorage.getItem('voice_metadata_cache') || '{}'
                    );
                    
                    delete metadataCache[this.id];
                    
                    localStorage.setItem(
                        'voice_metadata_cache',
                        JSON.stringify(metadataCache)
                    );
                    
                    // 清理音频缓存
                    const audioCache = JSON.parse(
                        localStorage.getItem('voice_audio_cache') || '{}'
                    );
                    
                    delete audioCache[this.id];
                    
                    localStorage.setItem(
                        'voice_audio_cache',
                        JSON.stringify(audioCache)
                    );
                    
                } catch (error) {
                    console.warn('清理语音缓存失败:', error);
                }
            },
            
            // 通知语音删除
            notifyVoiceDeletion() {
                try {
                    this.store.env.bus.trigger('voice_message:deleted', {
                        attachmentId: this.id,
                        voiceData: this.voice,
                        timestamp: Date.now()
                    });
                } catch (error) {
                    console.warn('通知语音删除失败:', error);
                }
            },
            
            // 获取语音消息信息
            getVoiceInfo() {
                if (!this.voice) {
                    return null;
                }
                
                return {
                    id: this.id,
                    duration: this.voice.duration || 0,
                    waveform: this.voice.waveform || null,
                    status: this.voice.status || 'unknown',
                    size: this.voice.size || this.filesize,
                    format: this.voice.format || this.mimetype,
                    isViewable: this.isVoiceViewable(),
                    hasPlayPermission: this.hasVoicePlayPermission(),
                    browserSupported: this.isBrowserSupportedForVoice()
                };
            },
            
            // 检查是否为语音消息
            get isVoiceMessage() {
                return !!this.voice;
            },
            
            // 获取语音时长
            get voiceDuration() {
                return this.voice?.duration || 0;
            },
            
            // 获取语音波形
            get voiceWaveform() {
                return this.voice?.waveform || null;
            },
            
            // 获取语音状态
            get voiceStatus() {
                return this.voice?.status || 'unknown';
            },
            
            // 检查语音是否可播放
            get isVoicePlayable() {
                return this.isVoiceMessage && 
                       this.isVoiceViewable() && 
                       this.voiceStatus === 'ready';
            },
            
            // 获取语音文件大小（格式化）
            get voiceFileSizeFormatted() {
                if (!this.voice) {
                    return '';
                }
                
                const size = this.voice.size || this.filesize || 0;
                
                if (size < 1024) {
                    return `${size} B`;
                }
                
                if (size < 1024 * 1024) {
                    return `${(size / 1024).toFixed(1)} KB`;
                }
                
                return `${(size / (1024 * 1024)).toFixed(1)} MB`;
            },
            
            // 获取语音时长（格式化）
            get voiceDurationFormatted() {
                const duration = this.voiceDuration;
                
                if (duration < 60) {
                    return `${Math.round(duration)}s`;
                }
                
                const minutes = Math.floor(duration / 60);
                const seconds = Math.round(duration % 60);
                
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            },
            
            // 获取附件统计信息
            getAttachmentStatistics() {
                const baseStats = {
                    id: this.id,
                    filename: this.filename,
                    mimetype: this.mimetype,
                    filesize: this.filesize,
                    isViewable: this.isViewable
                };
                
                if (this.isVoiceMessage) {
                    return {
                        ...baseStats,
                        isVoiceMessage: true,
                        voiceInfo: this.getVoiceInfo(),
                        voiceDuration: this.voiceDuration,
                        voiceDurationFormatted: this.voiceDurationFormatted,
                        voiceFileSizeFormatted: this.voiceFileSizeFormatted,
                        isVoicePlayable: this.isVoicePlayable
                    };
                }
                
                return {
                    ...baseStats,
                    isVoiceMessage: false
                };
            }
        };
        
        patch(Attachment.prototype, EnhancedAttachmentModelPatch);
    }
};

// 应用附件模型语音消息补丁增强
AttachmentModelVoicePatchEnhancer.enhanceAttachmentModelVoicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 条件逻辑
- 智能的条件判断
- 语音消息特殊处理
- 兼容性保证

### 3. 服务集成
- 与语音消息服务的集成
- 状态管理和清理
- 事件通知机制

### 4. 数据模型
- 完整的数据模型扩展
- 属性和方法的增强
- 类型安全的实现

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 策略模式 (Strategy Pattern)
- 不同类型附件的处理策略
- 可配置的行为模式

### 3. 观察者模式 (Observer Pattern)
- 状态变化的通知
- 事件驱动的架构

## 注意事项

1. **数据一致性**: 确保语音消息数据的一致性
2. **状态管理**: 正确管理语音播放器状态
3. **内存管理**: 及时清理相关资源和缓存
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **元数据管理**: 增强语音消息元数据的管理
2. **缓存策略**: 实现更智能的缓存策略
3. **权限控制**: 实现更细粒度的权限控制
4. **性能优化**: 优化大量语音消息的处理性能
5. **分析功能**: 提供语音消息的分析和统计

该补丁为附件模型提供了重要的语音消息支持，是语音消息功能的核心数据模型组件。

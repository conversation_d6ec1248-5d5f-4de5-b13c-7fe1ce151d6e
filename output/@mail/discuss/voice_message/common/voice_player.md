# Voice Player - 语音播放器

## 概述

`voice_player.js` 实现了 Odoo 讨论应用中的语音播放器组件，提供了完整的语音消息播放功能。该组件基于Web Audio API，支持音频波形可视化、播放控制、进度跳转、音量调节等特性，集成了音频上下文管理、缓冲区处理、Canvas绘制和用户交互等功能，为用户提供了专业级的语音消息播放体验，是讨论应用语音消息功能的核心播放组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/voice_player.js`
- **行数**: 403
- **模块**: `@mail/discuss/voice_message/common/voice_player`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                    // OWL 框架
'@web/core/browser/browser'    // 浏览器服务
'@web/core/utils/hooks'        // 钩子工具
'@web/core/utils/urls'         // URL工具
```

## 常量定义

### 波形颜色

```javascript
const WAVE_COLOR = "#7775";
```

**常量功能**:
- **WAVE_COLOR**: 音频波形的默认颜色
- **视觉一致性**: 保持界面的视觉一致性
- **可配置性**: 便于后续的主题定制

## 类型定义

### Props 类型

```javascript
/**
 * @typedef {Object} Props
 * @property {import("models").Attachment} attachment
 */
```

**属性说明**:
- **attachment**: 语音消息附件对象，包含音频文件信息

## 组件定义

### VoicePlayer 类

```javascript
const VoicePlayer = class VoicePlayer extends Component {
    static props = ["attachment"];
    static template = "mail.VoicePlayer";
    
    // 播放状态属性
    lastPlaytime = 0;
    lastPos = 0;
    startPosition = 0;
    progressColor;
    
    // Web Audio API 属性
    gainNode;
    audioCtx;
    scheduledPause;
    buffer;
    analyser;
    source;
    
    // Canvas 相关属性
    width;
    height;
    wrapper;
    progressWave;
    waveCtx;
    progressCtx;
}
```

**组件特性**:
- 继承自OWL Component
- 使用专用模板
- 集成Web Audio API
- 支持Canvas绘制

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.wrapperRef = useRef("wrapper");
    this.drawerRef = useRef("drawer");
    this.waveRef = useRef("wave");
    this.progressRef = useRef("progress");
    this.voiceMessageService = useService("discuss.voice_message");
    this.state = useState({
        paused: true,
        playing: false,
        repeat: false,
        visualTime: "-- : --",
    });
}
```

**初始化功能**:
- **引用管理**: 创建DOM元素引用
- **服务注入**: 注入语音消息服务
- **状态管理**: 初始化播放状态
- **时间显示**: 初始化时间显示格式

### 2. 音频上下文创建

```javascript
makeAudio() {
    this.audioCtx = new browser.AudioContext();
    this.gainNode = this.audioCtx.createGain();
    this.gainNode.connect(this.audioCtx.destination);
    this.analyser = this.audioCtx.createAnalyser();
    this.analyser.connect(this.gainNode);
    this.fetchFile(
        url(this.props.attachment.urlRoute, {
            ...this.props.attachment.urlQueryParams,
        })
    ).then((arrayBuffer) => this.drawBuffer(arrayBuffer));
}
```

**音频创建功能**:
- **音频上下文**: 创建Web Audio API上下文
- **增益节点**: 创建音量控制节点
- **分析器**: 创建音频分析器用于波形显示
- **文件获取**: 异步获取音频文件数据

### 3. 文件获取

```javascript
async fetchFile(url) {
    const response = await this._fetch(url);
    if (!response.ok) {
        throw new Error("HTTP error status: " + response.status);
    }
    const arrayBuffer = await response.arrayBuffer();
    return arrayBuffer;
}
```

**文件获取功能**:
- **HTTP请求**: 发送HTTP请求获取音频文件
- **错误处理**: 检查响应状态并处理错误
- **数据转换**: 将响应转换为ArrayBuffer格式
- **异步处理**: 支持异步文件加载

### 4. 用户交互

```javascript
this.wrapper.addEventListener("click", (e) => {
    if (this.props.attachment.uploading) {
        return;
    }
    const clientX = (e.targetTouches ? e.targetTouches[0] : e).clientX;
    const bcr = this.wrapper.getBoundingClientRect();
    const progressPixels = clientX - bcr.left;
    const progress = Math.min(
        Math.max(0, progressPixels / this.wrapper.scrollWidth),
        1
    );
    this.seekTo(progress);
});
```

**交互功能**:
- **点击跳转**: 支持点击波形跳转到指定位置
- **触摸支持**: 支持移动设备的触摸操作
- **进度计算**: 根据点击位置计算播放进度
- **边界检查**: 确保进度值在有效范围内

## 使用场景

### 1. 语音播放器增强

```javascript
// 语音播放器增强功能
const VoicePlayerEnhancer = {
    enhanceVoicePlayer: () => {
        const EnhancedVoicePlayer = class extends VoicePlayer {
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    volume: 1.0,
                    playbackRate: 1.0,
                    isLoading: false,
                    error: null,
                    duration: 0,
                    currentTime: 0,
                    buffered: 0,
                    isBuffering: false,
                    visualizerType: 'waveform', // waveform, spectrum, bars
                    showControls: true,
                    showTime: true,
                    showWaveform: true,
                    autoPlay: false,
                    loop: false,
                    muted: false
                });
                
                // 增强的服务
                this.notificationService = useService("notification");
                this.userService = useService("user");
                
                // 播放历史
                this.playbackHistory = [];
                this.bookmarks = [];
                
                // 设置增强功能
                this.setupEnhancements();
            }
            
            // 设置增强功能
            setupEnhancements() {
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
                
                // 设置可视化增强
                this.setupVisualizationEnhancements();
                
                // 设置播放控制增强
                this.setupPlaybackControlEnhancements();
                
                // 设置用户偏好
                this.setupUserPreferences();
                
                // 设置性能监控
                this.setupPerformanceMonitoring();
            }
            
            // 增强的音频创建
            makeAudio() {
                try {
                    this.enhancedState.isLoading = true;
                    this.enhancedState.error = null;
                    
                    // 调用父类方法
                    super.makeAudio();
                    
                    // 设置增强的音频处理
                    this.setupEnhancedAudioProcessing();
                    
                } catch (error) {
                    console.error('创建音频失败:', error);
                    this.enhancedState.error = error.message;
                    this.enhancedState.isLoading = false;
                }
            }
            
            // 设置增强的音频处理
            setupEnhancedAudioProcessing() {
                if (!this.audioCtx) return;
                
                // 创建压缩器
                this.compressor = this.audioCtx.createDynamicsCompressor();
                this.compressor.threshold.setValueAtTime(-24, this.audioCtx.currentTime);
                this.compressor.knee.setValueAtTime(30, this.audioCtx.currentTime);
                this.compressor.ratio.setValueAtTime(12, this.audioCtx.currentTime);
                this.compressor.attack.setValueAtTime(0.003, this.audioCtx.currentTime);
                this.compressor.release.setValueAtTime(0.25, this.audioCtx.currentTime);
                
                // 创建均衡器
                this.setupEqualizer();
                
                // 重新连接音频节点
                this.reconnectAudioNodes();
            }
            
            // 设置均衡器
            setupEqualizer() {
                const frequencies = [60, 170, 350, 1000, 3500, 10000];
                this.eqFilters = frequencies.map(freq => {
                    const filter = this.audioCtx.createBiquadFilter();
                    filter.type = 'peaking';
                    filter.frequency.value = freq;
                    filter.Q.value = 1;
                    filter.gain.value = 0;
                    return filter;
                });
            }
            
            // 重新连接音频节点
            reconnectAudioNodes() {
                // 断开原有连接
                this.analyser.disconnect();
                this.gainNode.disconnect();
                
                // 建立新的连接链
                let currentNode = this.analyser;
                
                // 连接均衡器
                this.eqFilters.forEach(filter => {
                    currentNode.connect(filter);
                    currentNode = filter;
                });
                
                // 连接压缩器
                currentNode.connect(this.compressor);
                
                // 连接增益节点
                this.compressor.connect(this.gainNode);
                
                // 连接到输出
                this.gainNode.connect(this.audioCtx.destination);
            }
            
            // 增强的播放控制
            async play() {
                try {
                    // 检查音频上下文状态
                    if (this.audioCtx.state === 'suspended') {
                        await this.audioCtx.resume();
                    }
                    
                    // 调用父类播放方法
                    await super.play();
                    
                    // 记录播放事件
                    this.recordPlaybackEvent('play');
                    
                    // 更新用户活动
                    this.updateUserActivity();
                    
                } catch (error) {
                    console.error('播放失败:', error);
                    this.handlePlaybackError(error);
                }
            }
            
            // 增强的暂停控制
            pause() {
                try {
                    // 调用父类暂停方法
                    super.pause();
                    
                    // 记录暂停事件
                    this.recordPlaybackEvent('pause');
                    
                } catch (error) {
                    console.error('暂停失败:', error);
                }
            }
            
            // 增强的停止控制
            stop() {
                try {
                    // 调用父类停止方法
                    if (super.stop) {
                        super.stop();
                    } else {
                        this.pause();
                        this.seekTo(0);
                    }
                    
                    // 记录停止事件
                    this.recordPlaybackEvent('stop');
                    
                } catch (error) {
                    console.error('停止失败:', error);
                }
            }
            
            // 设置音量
            setVolume(volume) {
                try {
                    const clampedVolume = Math.max(0, Math.min(1, volume));
                    this.enhancedState.volume = clampedVolume;
                    
                    if (this.gainNode) {
                        this.gainNode.gain.setValueAtTime(
                            clampedVolume,
                            this.audioCtx.currentTime
                        );
                    }
                    
                    // 保存用户偏好
                    this.saveUserPreferences();
                    
                } catch (error) {
                    console.error('设置音量失败:', error);
                }
            }
            
            // 设置播放速度
            setPlaybackRate(rate) {
                try {
                    const clampedRate = Math.max(0.25, Math.min(4, rate));
                    this.enhancedState.playbackRate = clampedRate;
                    
                    if (this.source) {
                        this.source.playbackRate.setValueAtTime(
                            clampedRate,
                            this.audioCtx.currentTime
                        );
                    }
                    
                    // 保存用户偏好
                    this.saveUserPreferences();
                    
                } catch (error) {
                    console.error('设置播放速度失败:', error);
                }
            }
            
            // 设置均衡器
            setEqualizer(band, gain) {
                try {
                    if (this.eqFilters && this.eqFilters[band]) {
                        const clampedGain = Math.max(-12, Math.min(12, gain));
                        this.eqFilters[band].gain.setValueAtTime(
                            clampedGain,
                            this.audioCtx.currentTime
                        );
                    }
                } catch (error) {
                    console.error('设置均衡器失败:', error);
                }
            }
            
            // 添加书签
            addBookmark(time, label = '') {
                try {
                    const bookmark = {
                        id: Date.now(),
                        time: time,
                        label: label || `书签 ${this.bookmarks.length + 1}`,
                        createdAt: Date.now()
                    };
                    
                    this.bookmarks.push(bookmark);
                    this.saveBookmarks();
                    
                    return bookmark;
                } catch (error) {
                    console.error('添加书签失败:', error);
                    return null;
                }
            }
            
            // 跳转到书签
            jumpToBookmark(bookmarkId) {
                try {
                    const bookmark = this.bookmarks.find(b => b.id === bookmarkId);
                    if (bookmark) {
                        this.seekTo(bookmark.time / this.enhancedState.duration);
                    }
                } catch (error) {
                    console.error('跳转到书签失败:', error);
                }
            }
            
            // 设置键盘快捷键
            setupKeyboardShortcuts() {
                this.keyboardHandler = (event) => {
                    if (!this.el?.contains(document.activeElement)) {
                        return;
                    }
                    
                    switch (event.key) {
                        case ' ': // 空格键播放/暂停
                            event.preventDefault();
                            this.togglePlayPause();
                            break;
                            
                        case 'ArrowLeft': // 左箭头后退
                            event.preventDefault();
                            this.skipBackward(event.shiftKey ? 10 : 5);
                            break;
                            
                        case 'ArrowRight': // 右箭头前进
                            event.preventDefault();
                            this.skipForward(event.shiftKey ? 10 : 5);
                            break;
                            
                        case 'ArrowUp': // 上箭头增加音量
                            event.preventDefault();
                            this.setVolume(this.enhancedState.volume + 0.1);
                            break;
                            
                        case 'ArrowDown': // 下箭头减少音量
                            event.preventDefault();
                            this.setVolume(this.enhancedState.volume - 0.1);
                            break;
                            
                        case 'm': // M键静音
                            event.preventDefault();
                            this.toggleMute();
                            break;
                            
                        case 'r': // R键重复
                            event.preventDefault();
                            this.toggleRepeat();
                            break;
                    }
                };
                
                document.addEventListener('keydown', this.keyboardHandler);
                
                onWillUnmount(() => {
                    document.removeEventListener('keydown', this.keyboardHandler);
                });
            }
            
            // 切换播放/暂停
            togglePlayPause() {
                if (this.state.playing) {
                    this.pause();
                } else {
                    this.play();
                }
            }
            
            // 向后跳跃
            skipBackward(seconds) {
                const currentTime = this.enhancedState.currentTime;
                const newTime = Math.max(0, currentTime - seconds);
                this.seekTo(newTime / this.enhancedState.duration);
            }
            
            // 向前跳跃
            skipForward(seconds) {
                const currentTime = this.enhancedState.currentTime;
                const newTime = Math.min(this.enhancedState.duration, currentTime + seconds);
                this.seekTo(newTime / this.enhancedState.duration);
            }
            
            // 切换静音
            toggleMute() {
                this.enhancedState.muted = !this.enhancedState.muted;
                
                if (this.enhancedState.muted) {
                    this.setVolume(0);
                } else {
                    this.setVolume(this.enhancedState.volume);
                }
            }
            
            // 切换重复
            toggleRepeat() {
                this.enhancedState.loop = !this.enhancedState.loop;
                this.saveUserPreferences();
            }
            
            // 记录播放事件
            recordPlaybackEvent(eventType) {
                try {
                    const event = {
                        type: eventType,
                        timestamp: Date.now(),
                        currentTime: this.enhancedState.currentTime,
                        duration: this.enhancedState.duration,
                        volume: this.enhancedState.volume,
                        playbackRate: this.enhancedState.playbackRate
                    };
                    
                    this.playbackHistory.push(event);
                    
                    // 限制历史记录数量
                    if (this.playbackHistory.length > 100) {
                        this.playbackHistory.splice(0, this.playbackHistory.length - 100);
                    }
                    
                } catch (error) {
                    console.warn('记录播放事件失败:', error);
                }
            }
            
            // 处理播放错误
            handlePlaybackError(error) {
                this.enhancedState.error = error.message;
                this.enhancedState.isLoading = false;
                
                let message = '播放失败';
                if (error.message.includes('network')) {
                    message = '网络错误，请检查网络连接';
                } else if (error.message.includes('decode')) {
                    message = '音频文件格式不支持';
                } else if (error.message.includes('permission')) {
                    message = '没有音频播放权限';
                }
                
                this.notificationService.add(message, { type: 'error' });
            }
            
            // 获取播放器统计
            getPlayerStatistics() {
                return {
                    state: { ...this.state },
                    enhancedState: { ...this.enhancedState },
                    playbackHistoryCount: this.playbackHistory.length,
                    bookmarksCount: this.bookmarks.length,
                    audioContextState: this.audioCtx?.state || 'unknown',
                    hasBuffer: !!this.buffer,
                    attachmentId: this.props.attachment.id
                };
            }
        };
        
        // 替换原始组件
        __exports.VoicePlayer = EnhancedVoicePlayer;
    }
};

// 应用语音播放器增强
VoicePlayerEnhancer.enhanceVoicePlayer();
```

## 技术特点

### 1. Web Audio API
- 专业级音频处理
- 实时音频分析
- 高质量音频输出

### 2. Canvas 可视化
- 音频波形绘制
- 实时进度显示
- 交互式界面

### 3. 用户交互
- 点击跳转支持
- 触摸设备兼容
- 直观的控制界面

### 4. 状态管理
- 完整的播放状态
- 响应式状态更新
- 生命周期管理

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的播放器组件
- 清晰的组件接口

### 2. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 事件驱动的更新

### 3. 策略模式 (Strategy Pattern)
- 不同的可视化策略
- 可配置的播放行为

## 注意事项

1. **浏览器兼容性**: 确保Web Audio API的浏览器支持
2. **性能优化**: 避免频繁的Canvas重绘影响性能
3. **内存管理**: 及时清理音频资源和事件监听器
4. **用户体验**: 提供清晰的加载和错误状态反馈

## 扩展建议

1. **可视化增强**: 添加更多类型的音频可视化
2. **播放控制**: 实现更丰富的播放控制功能
3. **音效处理**: 添加音频效果和滤镜
4. **键盘快捷键**: 支持更多的键盘快捷键
5. **播放列表**: 支持多个音频文件的播放列表

该播放器为语音消息功能提供了专业级的音频播放体验，是语音消息功能的核心用户界面组件。

# Voice Message Service - 语音消息服务

## 概述

`voice_message_service.js` 实现了 Odoo 讨论应用中的语音消息服务，提供了语音消息功能的核心服务支持。该服务负责管理语音播放器状态、加载必要的音频库和提供语音消息相关的全局功能，集成了资源加载、服务注册、状态管理等特性，为整个语音消息功能提供了统一的服务层支持，是讨论应用语音消息功能的核心服务组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/voice_message_service.js`
- **行数**: 40
- **模块**: `@mail/discuss/voice_message/common/voice_message_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'           // 资源加载
'@web/core/registry'         // 服务注册表
'@web/core/utils/functions'  // 工具函数
```

## 资源加载器

### Lamejs 加载器

```javascript
const loader = {
    loadLamejs: memoize(() => loadBundle("mail.assets_lamejs")),
};
```

**加载器功能**:
- **记忆化加载**: 使用memoize确保lamejs只加载一次
- **资源包加载**: 加载mail.assets_lamejs资源包
- **性能优化**: 避免重复加载相同资源

### 加载函数

```javascript
async function loadLamejs() {
    try {
        await loader.loadLamejs();
    } catch {
        // Could be intentional (tour ended successfully while lamejs still loading)
    }
}
```

**加载功能**:
- **异步加载**: 异步加载lamejs库
- **错误处理**: 静默处理加载错误
- **容错机制**: 允许加载失败而不影响其他功能

## 服务定义

### VoiceMessageService 类

```javascript
const VoiceMessageService = class VoiceMessageService {
    constructor(env) {
        /** @type {import("@mail/discuss/voice_message/common/voice_player").VoicePlayer} */
        this.activePlayer = null;
    }
}
```

**服务特性**:
- 管理活跃的语音播放器
- 提供环境上下文支持
- 简洁的服务接口

### 服务工厂

```javascript
const voiceMessageService = {
    start(env) {
        return new VoiceMessageService(env);
    },
};
```

**工厂功能**:
- **服务创建**: 创建VoiceMessageService实例
- **环境注入**: 注入环境上下文
- **标准接口**: 符合Odoo服务标准

### 服务注册

```javascript
registry.category("services").add("discuss.voice_message", voiceMessageService);
```

**注册功能**:
- **服务注册**: 将服务注册到服务注册表
- **命名空间**: 使用"discuss.voice_message"命名空间
- **全局访问**: 使服务在整个应用中可访问

## 使用场景

### 1. 语音消息服务增强

```javascript
// 语音消息服务增强功能
const VoiceMessageServiceEnhancer = {
    enhanceVoiceMessageService: () => {
        const EnhancedVoiceMessageService = class extends VoiceMessageService {
            constructor(env) {
                super(env);
                
                // 增强的属性
                this.env = env;
                this.activePlayers = new Map(); // 支持多个播放器
                this.recordingSession = null;
                this.audioContext = null;
                this.isInitialized = false;
                
                // 语音消息配置
                this.config = {
                    maxRecordingDuration: 600, // 10分钟
                    minRecordingDuration: 1,   // 1秒
                    defaultBitRate: 128,       // 128kbps
                    defaultSampleRate: 44100,  // 44.1kHz
                    enableWaveform: true,
                    enableAutoPlay: false,
                    enableNotifications: true,
                    maxConcurrentPlayers: 3
                };
                
                // 统计信息
                this.stats = {
                    totalRecordings: 0,
                    totalPlaybacks: 0,
                    totalDuration: 0,
                    averageDuration: 0,
                    lastActivity: null
                };
                
                // 设置增强功能
                this.setupEnhancements();
            }
            
            // 设置增强功能
            setupEnhancements() {
                // 初始化音频上下文
                this.initializeAudioContext();
                
                // 设置事件监听
                this.setupEventListeners();
                
                // 加载配置
                this.loadConfiguration();
                
                // 加载统计
                this.loadStatistics();
                
                // 设置清理
                this.setupCleanup();
            }
            
            // 初始化音频上下文
            async initializeAudioContext() {
                try {
                    if (!this.audioContext) {
                        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    }
                    
                    // 确保音频上下文处于运行状态
                    if (this.audioContext.state === 'suspended') {
                        await this.audioContext.resume();
                    }
                    
                    this.isInitialized = true;
                    console.log('语音消息服务初始化成功');
                    
                } catch (error) {
                    console.error('初始化音频上下文失败:', error);
                }
            }
            
            // 设置事件监听
            setupEventListeners() {
                // 监听页面可见性变化
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.pauseAllPlayers();
                    }
                });
                
                // 监听页面卸载
                window.addEventListener('beforeunload', () => {
                    this.cleanup();
                });
                
                // 监听音频上下文状态变化
                if (this.audioContext) {
                    this.audioContext.addEventListener('statechange', () => {
                        console.log('音频上下文状态:', this.audioContext.state);
                    });
                }
            }
            
            // 注册播放器
            registerPlayer(playerId, player) {
                try {
                    // 检查并发播放器数量
                    if (this.activePlayers.size >= this.config.maxConcurrentPlayers) {
                        // 停止最旧的播放器
                        const oldestPlayer = this.getOldestPlayer();
                        if (oldestPlayer) {
                            this.unregisterPlayer(oldestPlayer.id);
                        }
                    }
                    
                    this.activePlayers.set(playerId, {
                        id: playerId,
                        player: player,
                        registeredAt: Date.now(),
                        lastActivity: Date.now()
                    });
                    
                    // 设置为活跃播放器（向后兼容）
                    this.activePlayer = player;
                    
                    console.log(`播放器 ${playerId} 已注册`);
                    
                } catch (error) {
                    console.error('注册播放器失败:', error);
                }
            }
            
            // 注销播放器
            unregisterPlayer(playerId) {
                try {
                    const playerInfo = this.activePlayers.get(playerId);
                    if (playerInfo) {
                        // 停止播放器
                        if (playerInfo.player && typeof playerInfo.player.stop === 'function') {
                            playerInfo.player.stop();
                        }
                        
                        // 从映射中移除
                        this.activePlayers.delete(playerId);
                        
                        // 更新活跃播放器
                        if (this.activePlayer === playerInfo.player) {
                            this.activePlayer = this.getLatestPlayer()?.player || null;
                        }
                        
                        console.log(`播放器 ${playerId} 已注销`);
                    }
                } catch (error) {
                    console.error('注销播放器失败:', error);
                }
            }
            
            // 获取最旧的播放器
            getOldestPlayer() {
                let oldest = null;
                let oldestTime = Infinity;
                
                for (const playerInfo of this.activePlayers.values()) {
                    if (playerInfo.registeredAt < oldestTime) {
                        oldestTime = playerInfo.registeredAt;
                        oldest = playerInfo;
                    }
                }
                
                return oldest;
            }
            
            // 获取最新的播放器
            getLatestPlayer() {
                let latest = null;
                let latestTime = 0;
                
                for (const playerInfo of this.activePlayers.values()) {
                    if (playerInfo.lastActivity > latestTime) {
                        latestTime = playerInfo.lastActivity;
                        latest = playerInfo;
                    }
                }
                
                return latest;
            }
            
            // 暂停所有播放器
            pauseAllPlayers() {
                try {
                    for (const playerInfo of this.activePlayers.values()) {
                        if (playerInfo.player && typeof playerInfo.player.pause === 'function') {
                            playerInfo.player.pause();
                        }
                    }
                } catch (error) {
                    console.error('暂停所有播放器失败:', error);
                }
            }
            
            // 停止所有播放器
            stopAllPlayers() {
                try {
                    for (const playerInfo of this.activePlayers.values()) {
                        if (playerInfo.player && typeof playerInfo.player.stop === 'function') {
                            playerInfo.player.stop();
                        }
                    }
                    
                    this.activePlayers.clear();
                    this.activePlayer = null;
                } catch (error) {
                    console.error('停止所有播放器失败:', error);
                }
            }
            
            // 开始录制会话
            startRecordingSession(options = {}) {
                try {
                    if (this.recordingSession) {
                        throw new Error('已有录制会话正在进行');
                    }
                    
                    this.recordingSession = {
                        id: `recording_${Date.now()}`,
                        startTime: Date.now(),
                        options: options,
                        status: 'recording'
                    };
                    
                    this.stats.totalRecordings++;
                    this.stats.lastActivity = Date.now();
                    
                    console.log('录制会话已开始:', this.recordingSession.id);
                    return this.recordingSession.id;
                    
                } catch (error) {
                    console.error('开始录制会话失败:', error);
                    throw error;
                }
            }
            
            // 结束录制会话
            endRecordingSession(sessionId, duration = 0) {
                try {
                    if (!this.recordingSession || this.recordingSession.id !== sessionId) {
                        throw new Error('无效的录制会话');
                    }
                    
                    const session = this.recordingSession;
                    session.endTime = Date.now();
                    session.duration = duration;
                    session.status = 'completed';
                    
                    // 更新统计
                    this.stats.totalDuration += duration;
                    this.stats.averageDuration = this.stats.totalDuration / this.stats.totalRecordings;
                    this.stats.lastActivity = Date.now();
                    
                    // 保存统计
                    this.saveStatistics();
                    
                    // 清除会话
                    this.recordingSession = null;
                    
                    console.log('录制会话已结束:', sessionId);
                    
                } catch (error) {
                    console.error('结束录制会话失败:', error);
                }
            }
            
            // 记录播放事件
            recordPlaybackEvent(attachmentId, duration = 0) {
                try {
                    this.stats.totalPlaybacks++;
                    this.stats.lastActivity = Date.now();
                    
                    // 保存播放历史
                    const playbacks = JSON.parse(
                        localStorage.getItem('voice_playback_history') || '[]'
                    );
                    
                    playbacks.push({
                        attachmentId: attachmentId,
                        duration: duration,
                        timestamp: Date.now()
                    });
                    
                    // 保留最近100个播放记录
                    if (playbacks.length > 100) {
                        playbacks.splice(0, playbacks.length - 100);
                    }
                    
                    localStorage.setItem('voice_playback_history', JSON.stringify(playbacks));
                    
                    // 保存统计
                    this.saveStatistics();
                    
                } catch (error) {
                    console.error('记录播放事件失败:', error);
                }
            }
            
            // 加载配置
            loadConfiguration() {
                try {
                    const savedConfig = JSON.parse(
                        localStorage.getItem('voice_message_config') || '{}'
                    );
                    
                    this.config = { ...this.config, ...savedConfig };
                } catch (error) {
                    console.warn('加载语音消息配置失败:', error);
                }
            }
            
            // 保存配置
            saveConfiguration() {
                try {
                    localStorage.setItem('voice_message_config', JSON.stringify(this.config));
                } catch (error) {
                    console.warn('保存语音消息配置失败:', error);
                }
            }
            
            // 加载统计
            loadStatistics() {
                try {
                    const savedStats = JSON.parse(
                        localStorage.getItem('voice_message_stats') || '{}'
                    );
                    
                    this.stats = { ...this.stats, ...savedStats };
                } catch (error) {
                    console.warn('加载语音消息统计失败:', error);
                }
            }
            
            // 保存统计
            saveStatistics() {
                try {
                    localStorage.setItem('voice_message_stats', JSON.stringify(this.stats));
                } catch (error) {
                    console.warn('保存语音消息统计失败:', error);
                }
            }
            
            // 获取服务状态
            getServiceStatus() {
                return {
                    isInitialized: this.isInitialized,
                    audioContextState: this.audioContext?.state || 'unknown',
                    activePlayersCount: this.activePlayers.size,
                    hasRecordingSession: !!this.recordingSession,
                    config: { ...this.config },
                    stats: { ...this.stats }
                };
            }
            
            // 清理资源
            cleanup() {
                try {
                    // 停止所有播放器
                    this.stopAllPlayers();
                    
                    // 结束录制会话
                    if (this.recordingSession) {
                        this.endRecordingSession(this.recordingSession.id);
                    }
                    
                    // 关闭音频上下文
                    if (this.audioContext && this.audioContext.state !== 'closed') {
                        this.audioContext.close();
                    }
                    
                    // 保存配置和统计
                    this.saveConfiguration();
                    this.saveStatistics();
                    
                    console.log('语音消息服务已清理');
                    
                } catch (error) {
                    console.error('清理语音消息服务失败:', error);
                }
            }
        };
        
        // 增强的服务工厂
        const enhancedVoiceMessageService = {
            start(env) {
                return new EnhancedVoiceMessageService(env);
            },
        };
        
        // 重新注册增强的服务
        registry.category("services").add("discuss.voice_message", enhancedVoiceMessageService, { force: true });
    }
};

// 应用语音消息服务增强
VoiceMessageServiceEnhancer.enhanceVoiceMessageService();
```

## 技术特点

### 1. 服务架构
- 标准的Odoo服务模式
- 环境上下文支持
- 全局状态管理

### 2. 资源管理
- 记忆化的资源加载
- 异步加载支持
- 错误容错机制

### 3. 状态管理
- 活跃播放器管理
- 简洁的状态接口
- 扩展性设计

### 4. 服务注册
- 标准的服务注册
- 命名空间管理
- 全局访问支持

## 设计模式

### 1. 服务定位器模式 (Service Locator Pattern)
- 服务的注册和定位
- 全局服务访问

### 2. 单例模式 (Singleton Pattern)
- 服务实例的唯一性
- 全局状态管理

### 3. 工厂模式 (Factory Pattern)
- 服务实例的创建
- 环境注入支持

## 注意事项

1. **资源加载**: 确保lamejs库的正确加载
2. **状态管理**: 正确管理播放器状态
3. **内存管理**: 及时清理不需要的资源
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **多播放器支持**: 支持多个并发播放器
2. **配置管理**: 添加更丰富的配置选项
3. **事件系统**: 实现完整的事件系统
4. **统计功能**: 提供详细的使用统计
5. **缓存机制**: 实现智能的缓存策略

该服务为语音消息功能提供了核心的服务层支持，是整个语音消息系统的基础组件。

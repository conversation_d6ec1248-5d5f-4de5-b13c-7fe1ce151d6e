# Composer Model Patch - 编辑器模型补丁

## 概述

`composer_model_patch.js` 实现了对 Odoo 讨论应用编辑器模型的补丁扩展，专门添加了语音附件的访问功能。该补丁通过Odoo的补丁机制扩展了Composer模型，添加了voiceAttachment属性，为编辑器提供了快速访问语音消息附件的能力，是讨论应用语音消息功能在编辑器模型中的集成组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/composer_model_patch.js`
- **行数**: 19
- **模块**: `@mail/discuss/voice_message/common/composer_model_patch`

## 依赖关系

```javascript
// 核心依赖
'@mail/core/common/composer_model'   // 编辑器模型
'@web/core/utils/patch'              // 补丁工具
```

## 补丁定义

### Composer 原型补丁

```javascript
patch(Composer.prototype, {
    get voiceAttachment() {
        return this.attachments.find((attachment) => attachment.voice);
    },
});
```

**补丁特性**:
- 扩展编辑器模型功能
- 添加语音附件访问器
- 保持原有功能完整性
- 简洁的实现方式

## 核心功能

### 1. 语音附件访问

```javascript
get voiceAttachment() {
    return this.attachments.find((attachment) => attachment.voice);
}
```

**访问功能**:
- **附件查找**: 在所有附件中查找语音消息附件
- **条件过滤**: 基于voice属性进行过滤
- **单一返回**: 返回第一个找到的语音附件
- **快速访问**: 提供便捷的语音附件访问方式

## 使用场景

### 1. 编辑器模型语音消息增强

```javascript
// 编辑器模型语音消息增强功能
const ComposerModelVoicePatchEnhancer = {
    enhanceComposerModelVoicePatch: () => {
        const EnhancedComposerModelPatch = {
            // 增强的语音附件访问
            get voiceAttachment() {
                try {
                    return this.attachments.find((attachment) => 
                        attachment.voice && attachment.voice.status !== 'error'
                    );
                } catch (error) {
                    console.error('获取语音附件失败:', error);
                    return null;
                }
            },
            
            // 获取所有语音附件
            get voiceAttachments() {
                try {
                    return this.attachments.filter((attachment) => 
                        attachment.voice
                    );
                } catch (error) {
                    console.error('获取语音附件列表失败:', error);
                    return [];
                }
            },
            
            // 获取有效的语音附件
            get validVoiceAttachments() {
                try {
                    return this.attachments.filter((attachment) => 
                        attachment.voice && 
                        attachment.voice.status === 'ready' &&
                        attachment.voice.duration > 0
                    );
                } catch (error) {
                    console.error('获取有效语音附件失败:', error);
                    return [];
                }
            },
            
            // 检查是否有语音附件
            get hasVoiceAttachment() {
                return this.voiceAttachments.length > 0;
            },
            
            // 检查是否有有效语音附件
            get hasValidVoiceAttachment() {
                return this.validVoiceAttachments.length > 0;
            },
            
            // 获取语音附件总数
            get voiceAttachmentCount() {
                return this.voiceAttachments.length;
            },
            
            // 获取语音消息总时长
            get totalVoiceDuration() {
                try {
                    return this.voiceAttachments.reduce((total, attachment) => {
                        return total + (attachment.voice?.duration || 0);
                    }, 0);
                } catch (error) {
                    console.error('计算语音总时长失败:', error);
                    return 0;
                }
            },
            
            // 获取格式化的语音总时长
            get totalVoiceDurationFormatted() {
                const duration = this.totalVoiceDuration;
                
                if (duration < 60) {
                    return `${Math.round(duration)}s`;
                }
                
                const minutes = Math.floor(duration / 60);
                const seconds = Math.round(duration % 60);
                
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            },
            
            // 获取语音附件总大小
            get totalVoiceSize() {
                try {
                    return this.voiceAttachments.reduce((total, attachment) => {
                        return total + (attachment.filesize || 0);
                    }, 0);
                } catch (error) {
                    console.error('计算语音总大小失败:', error);
                    return 0;
                }
            },
            
            // 获取格式化的语音总大小
            get totalVoiceSizeFormatted() {
                const size = this.totalVoiceSize;
                
                if (size < 1024) {
                    return `${size} B`;
                }
                
                if (size < 1024 * 1024) {
                    return `${(size / 1024).toFixed(1)} KB`;
                }
                
                return `${(size / (1024 * 1024)).toFixed(1)} MB`;
            },
            
            // 添加语音附件
            addVoiceAttachment(voiceData, file) {
                try {
                    // 创建附件数据
                    const attachmentData = {
                        filename: file.name,
                        mimetype: file.type,
                        filesize: file.size,
                        voice: voiceData,
                        tmpUrl: URL.createObjectURL(file)
                    };
                    
                    // 添加到附件列表
                    const attachment = this.store.Attachment.insert(attachmentData);
                    this.attachments.push(attachment);
                    
                    return attachment;
                } catch (error) {
                    console.error('添加语音附件失败:', error);
                    throw error;
                }
            },
            
            // 移除语音附件
            removeVoiceAttachment(attachmentId) {
                try {
                    const index = this.attachments.findIndex(
                        attachment => attachment.id === attachmentId && attachment.voice
                    );
                    
                    if (index !== -1) {
                        const attachment = this.attachments[index];
                        
                        // 清理临时URL
                        if (attachment.tmpUrl) {
                            URL.revokeObjectURL(attachment.tmpUrl);
                        }
                        
                        // 从列表中移除
                        this.attachments.splice(index, 1);
                        
                        return true;
                    }
                    
                    return false;
                } catch (error) {
                    console.error('移除语音附件失败:', error);
                    return false;
                }
            },
            
            // 清除所有语音附件
            clearVoiceAttachments() {
                try {
                    const voiceAttachments = [...this.voiceAttachments];
                    
                    voiceAttachments.forEach(attachment => {
                        this.removeVoiceAttachment(attachment.id);
                    });
                    
                    return voiceAttachments.length;
                } catch (error) {
                    console.error('清除语音附件失败:', error);
                    return 0;
                }
            },
            
            // 替换语音附件
            replaceVoiceAttachment(oldAttachmentId, voiceData, file) {
                try {
                    // 移除旧附件
                    this.removeVoiceAttachment(oldAttachmentId);
                    
                    // 添加新附件
                    return this.addVoiceAttachment(voiceData, file);
                } catch (error) {
                    console.error('替换语音附件失败:', error);
                    throw error;
                }
            },
            
            // 获取语音附件信息
            getVoiceAttachmentInfo(attachmentId) {
                try {
                    const attachment = this.voiceAttachments.find(
                        att => att.id === attachmentId
                    );
                    
                    if (!attachment) {
                        return null;
                    }
                    
                    return {
                        id: attachment.id,
                        filename: attachment.filename,
                        mimetype: attachment.mimetype,
                        filesize: attachment.filesize,
                        voice: attachment.voice,
                        duration: attachment.voice?.duration || 0,
                        durationFormatted: this.formatDuration(attachment.voice?.duration || 0),
                        sizeFormatted: this.formatFileSize(attachment.filesize || 0),
                        status: attachment.voice?.status || 'unknown',
                        waveform: attachment.voice?.waveform || null
                    };
                } catch (error) {
                    console.error('获取语音附件信息失败:', error);
                    return null;
                }
            },
            
            // 格式化时长
            formatDuration(duration) {
                if (duration < 60) {
                    return `${Math.round(duration)}s`;
                }
                
                const minutes = Math.floor(duration / 60);
                const seconds = Math.round(duration % 60);
                
                return `${minutes}:${seconds.toString().padStart(2, '0')}`;
            },
            
            // 格式化文件大小
            formatFileSize(size) {
                if (size < 1024) {
                    return `${size} B`;
                }
                
                if (size < 1024 * 1024) {
                    return `${(size / 1024).toFixed(1)} KB`;
                }
                
                return `${(size / (1024 * 1024)).toFixed(1)} MB`;
            },
            
            // 验证语音附件
            validateVoiceAttachments() {
                try {
                    const errors = [];
                    
                    this.voiceAttachments.forEach(attachment => {
                        // 检查文件大小
                        const maxSize = 50 * 1024 * 1024; // 50MB
                        if (attachment.filesize > maxSize) {
                            errors.push(`${attachment.filename}: 文件过大`);
                        }
                        
                        // 检查时长
                        const maxDuration = 600; // 10分钟
                        if (attachment.voice?.duration > maxDuration) {
                            errors.push(`${attachment.filename}: 时长过长`);
                        }
                        
                        // 检查状态
                        if (attachment.voice?.status === 'error') {
                            errors.push(`${attachment.filename}: 文件损坏`);
                        }
                    });
                    
                    return {
                        isValid: errors.length === 0,
                        errors: errors
                    };
                } catch (error) {
                    console.error('验证语音附件失败:', error);
                    return {
                        isValid: false,
                        errors: ['验证失败']
                    };
                }
            },
            
            // 获取编辑器语音统计
            getComposerVoiceStatistics() {
                return {
                    hasVoiceAttachment: this.hasVoiceAttachment,
                    hasValidVoiceAttachment: this.hasValidVoiceAttachment,
                    voiceAttachmentCount: this.voiceAttachmentCount,
                    totalVoiceDuration: this.totalVoiceDuration,
                    totalVoiceDurationFormatted: this.totalVoiceDurationFormatted,
                    totalVoiceSize: this.totalVoiceSize,
                    totalVoiceSizeFormatted: this.totalVoiceSizeFormatted,
                    validationResult: this.validateVoiceAttachments()
                };
            }
        };
        
        patch(Composer.prototype, EnhancedComposerModelPatch);
    }
};

// 应用编辑器模型语音消息补丁增强
ComposerModelVoicePatchEnhancer.enhanceComposerModelVoicePatch();
```

## 技术特点

### 1. 补丁机制
- 非侵入式模型扩展
- 保持原有功能完整性
- 运行时功能增强

### 2. 数据访问
- 便捷的数据访问接口
- 智能的数据过滤
- 高效的查找算法

### 3. 简洁设计
- 最小化的代码实现
- 专注于核心功能
- 易于理解和维护

### 4. 扩展性
- 易于扩展的架构
- 灵活的数据处理
- 可配置的行为

## 设计模式

### 1. 补丁模式 (Patch Pattern)
- 运行时功能扩展
- 非破坏性修改

### 2. 访问器模式 (Accessor Pattern)
- 统一的数据访问接口
- 封装的数据操作

### 3. 过滤器模式 (Filter Pattern)
- 条件化的数据过滤
- 可组合的过滤逻辑

## 注意事项

1. **数据一致性**: 确保语音附件数据的一致性
2. **性能优化**: 避免频繁的数组查找影响性能
3. **错误处理**: 提供完善的错误处理机制
4. **内存管理**: 及时清理不需要的资源

## 扩展建议

1. **多语音支持**: 支持多个语音附件的管理
2. **状态管理**: 增强语音附件的状态管理
3. **验证功能**: 添加更完善的数据验证
4. **缓存机制**: 实现智能的数据缓存
5. **事件通知**: 添加语音附件变化的事件通知

该补丁为编辑器模型提供了重要的语音附件访问功能，是语音消息功能在编辑器模型中的简洁集成。

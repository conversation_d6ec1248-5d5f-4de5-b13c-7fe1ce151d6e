# Voice Recorder - 语音录制器

## 概述

`voice_recorder.js` 实现了 Odoo 讨论应用中的语音录制器组件，提供了完整的语音消息录制功能。该组件基于Web Audio API和MediaStream API，支持实时音频录制、MP3编码、录制时长限制、权限管理等特性，集成了音频上下文管理、流处理、编码器集成和用户界面控制等功能，为用户提供了专业级的语音消息录制体验，是讨论应用语音消息功能的核心录制组件。

## 文件信息
- **路径**: `/mail/static/src/discuss/voice_message/common/voice_recorder.js`
- **行数**: 217
- **模块**: `@mail/discuss/voice_message/common/voice_recorder`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                                    // OWL 框架
'@web/core/utils/hooks'                                        // 钩子工具
'@web/core/l10n/translation'                                   // 国际化
'@web/core/browser/browser'                                    // 浏览器服务
'@mail/discuss/voice_message/common/mp3_encoder'               // MP3编码器
'@mail/discuss/voice_message/common/voice_message_service'     // 语音消息服务
```

## 类型定义

### Props 类型

```javascript
/**
 * @typedef {Object} Props
 * @property {import("models").Composer} composer
 * @property {function} [attachmentUploader]
 * @property {function} [onchangeRecording]
 */
```

**属性说明**:
- **composer**: 编辑器模型对象
- **attachmentUploader**: 附件上传函数（可选）
- **onchangeRecording**: 录制状态变化回调（可选）

## 组件定义

### VoiceRecorder 类

```javascript
const VoiceRecorder = class VoiceRecorder extends Component {
    static props = ["composer", "attachmentUploader", "onchangeRecording?"];
    static template = "mail.VoiceRecorder";
    
    // 媒体流属性
    microphone;
    startTimeStamp;
    
    // Web Audio API 属性
    audioContext;
    streamSource;
    processor;
    
    // 编码器属性
    encoder;
    
    // 服务属性
    store;
    notification;
    config;
    voiceMessageService;
}
```

**组件特性**:
- 继承自OWL Component
- 使用专用模板
- 集成Web Audio API
- 支持MP3编码

## 核心功能

### 1. 组件初始化

```javascript
setup() {
    super.setup();
    this.state = useState({
        limitWarning: false,
    });
    this.store = useService("mail.store");
    this.notification = useService("notification");
    this.voiceMessageService = useService("discuss.voice_message");
    this.config = {
        sampleRate: 44100,
        bitRate: 128,
        maxDuration: 600, // 10 minutes
    };
}
```

**初始化功能**:
- **状态管理**: 初始化录制状态
- **服务注入**: 注入必要的服务
- **配置设置**: 设置录制参数
- **时长限制**: 设置最大录制时长

### 2. 录制开始

```javascript
async startRecording() {
    try {
        await loadLamejs();
        this.microphone = await navigator.mediaDevices.getUserMedia({ audio: true });
        this.audioContext = new browser.AudioContext();
        this.streamSource = this.audioContext.createMediaStreamSource(this.microphone);
        this.encoder = new Mp3Encoder(this.config);
        this.startTimeStamp = Date.now();
        
        // 设置音频处理
        await this.setupAudioProcessing();
        
        // 更新状态
        this.props.onchangeRecording?.(true);
        
    } catch (error) {
        this.handleRecordingError(error);
    }
}
```

**录制开始功能**:
- **库加载**: 异步加载lamejs编码库
- **权限获取**: 请求麦克风访问权限
- **音频上下文**: 创建Web Audio API上下文
- **流处理**: 创建媒体流音频源
- **编码器初始化**: 创建MP3编码器实例

### 3. 音频处理设置

```javascript
async setupAudioProcessing() {
    // 创建音频工作节点
    await this.audioContext.audioWorklet.addModule('/path/to/audio-processor.js');
    this.processor = new AudioWorkletNode(this.audioContext, 'audio-processor');
    
    // 连接音频节点
    this.streamSource.connect(this.processor);
    
    // 监听音频数据
    this.processor.port.onmessage = (event) => {
        this.processAudioData(event.data);
    };
}
```

**音频处理功能**:
- **工作节点**: 创建音频处理工作节点
- **节点连接**: 连接音频流和处理器
- **数据监听**: 监听实时音频数据
- **数据处理**: 处理音频数据用于编码

### 4. 录制停止

```javascript
async stopRecording() {
    try {
        // 停止媒体流
        if (this.microphone) {
            this.microphone.getTracks().forEach(track => track.stop());
        }
        
        // 完成编码
        const mp3Data = this.encoder.finish();
        
        // 创建音频文件
        const audioBlob = new Blob(mp3Data, { type: 'audio/mp3' });
        
        // 上传附件
        await this.uploadVoiceAttachment(audioBlob);
        
        // 清理资源
        this.cleanup();
        
        // 更新状态
        this.props.onchangeRecording?.(false);
        
    } catch (error) {
        this.handleRecordingError(error);
    }
}
```

**录制停止功能**:
- **流停止**: 停止所有媒体轨道
- **编码完成**: 完成MP3编码并获取数据
- **文件创建**: 创建音频Blob对象
- **附件上传**: 上传语音附件
- **资源清理**: 清理所有相关资源

## 使用场景

### 1. 语音录制器增强

```javascript
// 语音录制器增强功能
const VoiceRecorderEnhancer = {
    enhanceVoiceRecorder: () => {
        const EnhancedVoiceRecorder = class extends VoiceRecorder {
            setup() {
                super.setup();
                
                // 增强的状态管理
                this.enhancedState = useState({
                    isRecording: false,
                    isPaused: false,
                    recordingDuration: 0,
                    recordingLevel: 0,
                    recordingQuality: 'high', // low, medium, high
                    noiseReduction: true,
                    echoCancellation: true,
                    autoGainControl: true,
                    recordingFormat: 'mp3', // mp3, wav, ogg
                    visualizerEnabled: true,
                    recordingHistory: [],
                    deviceInfo: null,
                    permissionStatus: 'unknown' // unknown, granted, denied, prompt
                });
                
                // 增强的配置
                this.enhancedConfig = {
                    ...this.config,
                    qualitySettings: {
                        low: { sampleRate: 22050, bitRate: 64 },
                        medium: { sampleRate: 44100, bitRate: 128 },
                        high: { sampleRate: 48000, bitRate: 192 }
                    },
                    visualizer: {
                        enabled: true,
                        type: 'waveform', // waveform, spectrum, level
                        updateInterval: 50,
                        smoothing: 0.8
                    },
                    recording: {
                        maxDuration: 600,
                        minDuration: 1,
                        warningDuration: 540, // 9分钟警告
                        autoStop: true,
                        pauseSupported: true
                    }
                };
                
                // 音频分析器
                this.analyser = null;
                this.dataArray = null;
                this.animationFrame = null;
                
                // 设置增强功能
                this.setupEnhancements();
            }
            
            // 设置增强功能
            setupEnhancements() {
                // 设置设备检测
                this.setupDeviceDetection();
                
                // 设置权限管理
                this.setupPermissionManagement();
                
                // 设置可视化
                this.setupVisualization();
                
                // 设置录制监控
                this.setupRecordingMonitoring();
                
                // 设置键盘快捷键
                this.setupKeyboardShortcuts();
            }
            
            // 增强的录制开始
            async startRecording() {
                try {
                    // 检查权限
                    await this.checkPermissions();
                    
                    // 检查设备
                    await this.checkDevices();
                    
                    // 加载编码库
                    await loadLamejs();
                    
                    // 获取媒体流
                    const constraints = this.buildMediaConstraints();
                    this.microphone = await navigator.mediaDevices.getUserMedia(constraints);
                    
                    // 创建音频上下文
                    this.audioContext = new browser.AudioContext({
                        sampleRate: this.getCurrentQualitySettings().sampleRate
                    });
                    
                    // 设置音频处理链
                    await this.setupEnhancedAudioProcessing();
                    
                    // 初始化编码器
                    this.encoder = new Mp3Encoder(this.getCurrentQualitySettings());
                    
                    // 开始录制
                    this.startTimeStamp = Date.now();
                    this.enhancedState.isRecording = true;
                    this.enhancedState.recordingDuration = 0;
                    
                    // 开始监控
                    this.startRecordingMonitoring();
                    
                    // 开始可视化
                    if (this.enhancedState.visualizerEnabled) {
                        this.startVisualization();
                    }
                    
                    // 通知状态变化
                    this.props.onchangeRecording?.(true);
                    
                    // 记录录制开始
                    this.recordRecordingEvent('start');
                    
                } catch (error) {
                    console.error('开始录制失败:', error);
                    this.handleRecordingError(error);
                }
            }
            
            // 构建媒体约束
            buildMediaConstraints() {
                const quality = this.enhancedState.recordingQuality;
                const settings = this.enhancedConfig.qualitySettings[quality];
                
                return {
                    audio: {
                        sampleRate: settings.sampleRate,
                        channelCount: 1,
                        echoCancellation: this.enhancedState.echoCancellation,
                        noiseSuppression: this.enhancedState.noiseReduction,
                        autoGainControl: this.enhancedState.autoGainControl
                    }
                };
            }
            
            // 设置增强的音频处理
            async setupEnhancedAudioProcessing() {
                // 创建媒体流源
                this.streamSource = this.audioContext.createMediaStreamSource(this.microphone);
                
                // 创建分析器
                this.analyser = this.audioContext.createAnalyser();
                this.analyser.fftSize = 2048;
                this.analyser.smoothingTimeConstant = this.enhancedConfig.visualizer.smoothing;
                
                // 创建增益节点
                this.gainNode = this.audioContext.createGain();
                
                // 创建压缩器
                this.compressor = this.audioContext.createDynamicsCompressor();
                this.compressor.threshold.setValueAtTime(-24, this.audioContext.currentTime);
                this.compressor.knee.setValueAtTime(30, this.audioContext.currentTime);
                this.compressor.ratio.setValueAtTime(12, this.audioContext.currentTime);
                
                // 创建滤波器
                this.highPassFilter = this.audioContext.createBiquadFilter();
                this.highPassFilter.type = 'highpass';
                this.highPassFilter.frequency.setValueAtTime(80, this.audioContext.currentTime);
                
                // 连接音频节点
                this.streamSource
                    .connect(this.highPassFilter)
                    .connect(this.compressor)
                    .connect(this.gainNode)
                    .connect(this.analyser);
                
                // 设置音频处理器
                await this.setupAudioProcessor();
            }
            
            // 设置音频处理器
            async setupAudioProcessor() {
                try {
                    // 创建脚本处理器
                    this.scriptProcessor = this.audioContext.createScriptProcessor(4096, 1, 1);
                    
                    this.scriptProcessor.onaudioprocess = (event) => {
                        if (this.enhancedState.isRecording && !this.enhancedState.isPaused) {
                            const inputBuffer = event.inputBuffer;
                            const inputData = inputBuffer.getChannelData(0);
                            
                            // 编码音频数据
                            this.encoder.encode(inputData.buffer);
                            
                            // 更新录制电平
                            this.updateRecordingLevel(inputData);
                        }
                    };
                    
                    // 连接处理器
                    this.analyser.connect(this.scriptProcessor);
                    this.scriptProcessor.connect(this.audioContext.destination);
                    
                } catch (error) {
                    console.error('设置音频处理器失败:', error);
                    throw error;
                }
            }
            
            // 更新录制电平
            updateRecordingLevel(inputData) {
                try {
                    let sum = 0;
                    for (let i = 0; i < inputData.length; i++) {
                        sum += inputData[i] * inputData[i];
                    }
                    
                    const rms = Math.sqrt(sum / inputData.length);
                    const level = Math.min(1, rms * 10); // 放大并限制到0-1
                    
                    this.enhancedState.recordingLevel = level;
                } catch (error) {
                    console.warn('更新录制电平失败:', error);
                }
            }
            
            // 暂停录制
            pauseRecording() {
                try {
                    if (!this.enhancedState.isRecording || this.enhancedState.isPaused) {
                        return;
                    }
                    
                    this.enhancedState.isPaused = true;
                    this.pauseTimeStamp = Date.now();
                    
                    // 记录暂停事件
                    this.recordRecordingEvent('pause');
                    
                } catch (error) {
                    console.error('暂停录制失败:', error);
                }
            }
            
            // 恢复录制
            resumeRecording() {
                try {
                    if (!this.enhancedState.isRecording || !this.enhancedState.isPaused) {
                        return;
                    }
                    
                    this.enhancedState.isPaused = false;
                    
                    // 调整开始时间戳
                    const pauseDuration = Date.now() - this.pauseTimeStamp;
                    this.startTimeStamp += pauseDuration;
                    
                    // 记录恢复事件
                    this.recordRecordingEvent('resume');
                    
                } catch (error) {
                    console.error('恢复录制失败:', error);
                }
            }
            
            // 取消录制
            cancelRecording() {
                try {
                    if (!this.enhancedState.isRecording) {
                        return;
                    }
                    
                    // 停止录制但不保存
                    this.cleanup();
                    
                    // 重置状态
                    this.enhancedState.isRecording = false;
                    this.enhancedState.isPaused = false;
                    this.enhancedState.recordingDuration = 0;
                    
                    // 通知状态变化
                    this.props.onchangeRecording?.(false);
                    
                    // 记录取消事件
                    this.recordRecordingEvent('cancel');
                    
                } catch (error) {
                    console.error('取消录制失败:', error);
                }
            }
            
            // 开始录制监控
            startRecordingMonitoring() {
                this.recordingTimer = setInterval(() => {
                    if (this.enhancedState.isRecording && !this.enhancedState.isPaused) {
                        const duration = (Date.now() - this.startTimeStamp) / 1000;
                        this.enhancedState.recordingDuration = duration;
                        
                        // 检查时长警告
                        if (duration >= this.enhancedConfig.recording.warningDuration) {
                            this.state.limitWarning = true;
                        }
                        
                        // 检查自动停止
                        if (duration >= this.enhancedConfig.recording.maxDuration) {
                            if (this.enhancedConfig.recording.autoStop) {
                                this.stopRecording();
                            }
                        }
                    }
                }, 100);
            }
            
            // 停止录制监控
            stopRecordingMonitoring() {
                if (this.recordingTimer) {
                    clearInterval(this.recordingTimer);
                    this.recordingTimer = null;
                }
            }
            
            // 开始可视化
            startVisualization() {
                if (!this.analyser) return;
                
                this.dataArray = new Uint8Array(this.analyser.frequencyBinCount);
                
                const updateVisualization = () => {
                    if (this.enhancedState.isRecording) {
                        this.analyser.getByteFrequencyData(this.dataArray);
                        
                        // 触发可视化更新事件
                        this.triggerVisualizationUpdate(this.dataArray);
                        
                        this.animationFrame = requestAnimationFrame(updateVisualization);
                    }
                };
                
                updateVisualization();
            }
            
            // 停止可视化
            stopVisualization() {
                if (this.animationFrame) {
                    cancelAnimationFrame(this.animationFrame);
                    this.animationFrame = null;
                }
            }
            
            // 获取当前质量设置
            getCurrentQualitySettings() {
                return this.enhancedConfig.qualitySettings[this.enhancedState.recordingQuality];
            }
            
            // 设置录制质量
            setRecordingQuality(quality) {
                if (this.enhancedState.isRecording) {
                    throw new Error('无法在录制过程中更改质量设置');
                }
                
                this.enhancedState.recordingQuality = quality;
                this.saveUserPreferences();
            }
            
            // 记录录制事件
            recordRecordingEvent(eventType) {
                try {
                    const event = {
                        type: eventType,
                        timestamp: Date.now(),
                        duration: this.enhancedState.recordingDuration,
                        quality: this.enhancedState.recordingQuality,
                        format: this.enhancedState.recordingFormat
                    };
                    
                    this.enhancedState.recordingHistory.push(event);
                    
                    // 限制历史记录数量
                    if (this.enhancedState.recordingHistory.length > 50) {
                        this.enhancedState.recordingHistory.splice(0, 1);
                    }
                    
                } catch (error) {
                    console.warn('记录录制事件失败:', error);
                }
            }
            
            // 获取录制器统计
            getRecorderStatistics() {
                return {
                    state: { ...this.state },
                    enhancedState: { ...this.enhancedState },
                    config: { ...this.enhancedConfig },
                    isRecording: this.enhancedState.isRecording,
                    recordingDuration: this.enhancedState.recordingDuration,
                    recordingLevel: this.enhancedState.recordingLevel,
                    audioContextState: this.audioContext?.state || 'unknown',
                    hasPermission: this.enhancedState.permissionStatus === 'granted'
                };
            }
        };
        
        // 替换原始组件
        __exports.VoiceRecorder = EnhancedVoiceRecorder;
    }
};

// 应用语音录制器增强
VoiceRecorderEnhancer.enhanceVoiceRecorder();
```

## 技术特点

### 1. Web Audio API
- 专业级音频录制
- 实时音频处理
- 高质量音频捕获

### 2. MediaStream API
- 麦克风访问管理
- 媒体流控制
- 权限处理

### 3. MP3编码
- 实时音频编码
- 高效压缩算法
- 标准格式输出

### 4. 用户体验
- 直观的录制界面
- 实时状态反馈
- 错误处理机制

## 设计模式

### 1. 组件模式 (Component Pattern)
- 可复用的录制器组件
- 清晰的组件接口

### 2. 观察者模式 (Observer Pattern)
- 状态变化的监听
- 事件驱动的更新

### 3. 策略模式 (Strategy Pattern)
- 不同的编码策略
- 可配置的录制参数

## 注意事项

1. **权限管理**: 正确处理麦克风访问权限
2. **浏览器兼容性**: 确保Web Audio API的浏览器支持
3. **内存管理**: 及时清理音频资源和流
4. **错误处理**: 提供完善的错误处理和用户反馈

## 扩展建议

1. **音频效果**: 添加实时音频效果处理
2. **多格式支持**: 支持更多音频格式的录制
3. **云端处理**: 实现云端音频处理和存储
4. **AI增强**: 集成AI音频增强功能
5. **协作录制**: 支持多人协作录制功能

该录制器为语音消息功能提供了专业级的音频录制体验，是语音消息功能的核心录制组件。

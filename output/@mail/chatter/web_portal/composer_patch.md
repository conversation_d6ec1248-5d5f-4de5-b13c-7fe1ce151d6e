# Odoo Web Portal 撰写器补丁 (Composer Patch) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web_portal/composer_patch.js`
**原始路径**: `/mail/static/src/chatter/web_portal/composer_patch.js`
**模块类型**: Web Portal 补丁 - 撰写器占位符优化
**代码行数**: 22 行
**依赖关系**:
- `@mail/core/common/composer` - 撰写器组件
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/utils/patch` - 补丁工具

## 模块功能

Web Portal 撰写器补丁模块是对核心撰写器组件的 Portal 环境优化。该模块提供了：
- Portal 环境下的占位符文本优化
- 基于线程类型的智能占位符
- 消息类型感知的提示文本
- 用户体验的本地化改进
- 非讨论频道的特殊处理

这个补丁专门为 Web Portal 环境提供了更友好和准确的用户提示，改善了撰写器的用户体验。

## Web Portal 撰写器补丁架构

### 核心补丁结构
```
Web Portal Composer Patch
├── 占位符优化
│   ├── 线程类型检测
│   ├── 消息类型判断
│   ├── 智能文本选择
│   └── 本地化支持
├── 条件逻辑
│   ├── 讨论频道排除
│   ├── 自定义占位符检查
│   ├── 消息类型分支
│   └── 回退机制
├── 文本策略
│   ├── 关注者消息提示
│   ├── 内部笔记提示
│   ├── 默认占位符
│   └── 多语言支持
└── Portal 特性
    ├── 简化界面
    ├── 用户引导
    ├── 上下文感知
    └── 体验优化
```

### 补丁实现
```javascript
// Web Portal 撰写器补丁
patch(Composer.prototype, {
    get placeholder() {
        // 检查是否为非讨论频道且没有自定义占位符
        if (this.thread &&
            this.thread.model !== "discuss.channel" &&
            !this.props.placeholder) {

            // 根据消息类型返回不同占位符
            if (this.props.type === "message") {
                return _t("Send a message to followers…");
            } else {
                return _t("Log an internal note…");
            }
        }

        // 回退到父类占位符
        return super.placeholder;
    }
});
```

## 核心功能详解

### 1. 智能占位符管理器
```javascript
// 智能占位符管理器
class SmartPlaceholderManager {
    constructor() {
        this.placeholderStrategies = new Map();
        this.contextAnalyzers = new Map();
        this.localizationCache = new Map();
        this.setupPlaceholderStrategies();
    }

    setupPlaceholderStrategies() {
        // 设置占位符策略

        // 基于线程模型的策略
        this.placeholderStrategies.set('thread_model', {
            'helpdesk.ticket': {
                message: '向工单关注者发送消息…',
                note: '记录内部工单笔记…'
            },
            'project.task': {
                message: '向任务关注者发送消息…',
                note: '记录任务进展笔记…'
            },
            'sale.order': {
                message: '向订单关注者发送消息…',
                note: '记录订单处理笔记…'
            },
            'purchase.order': {
                message: '向采购关注者发送消息…',
                note: '记录采购进展笔记…'
            },
            'account.move': {
                message: '向会计凭证关注者发送消息…',
                note: '记录会计处理笔记…'
            },
            'hr.employee': {
                message: '向员工档案关注者发送消息…',
                note: '记录员工信息笔记…'
            },
            'res.partner': {
                message: '向联系人关注者发送消息…',
                note: '记录联系人互动笔记…'
            },
            'crm.lead': {
                message: '向商机关注者发送消息…',
                note: '记录商机跟进笔记…'
            }
        });

        // 基于用户角色的策略
        this.placeholderStrategies.set('user_role', {
            'portal': {
                message: '发送消息…',
                note: '添加备注…'
            },
            'internal': {
                message: '向关注者发送消息…',
                note: '记录内部笔记…'
            },
            'admin': {
                message: '向关注者发送消息…',
                note: '记录管理笔记…'
            }
        });

        // 基于业务上下文的策略
        this.placeholderStrategies.set('business_context', {
            'customer_service': {
                message: '回复客户…',
                note: '记录服务笔记…'
            },
            'sales': {
                message: '联系客户…',
                note: '记录销售笔记…'
            },
            'support': {
                message: '提供支持…',
                note: '记录支持笔记…'
            },
            'project': {
                message: '项目沟通…',
                note: '记录项目笔记…'
            }
        });

        // 基于设备类型的策略
        this.placeholderStrategies.set('device_type', {
            'mobile': {
                message: '发送消息…',
                note: '添加笔记…'
            },
            'tablet': {
                message: '向关注者发送消息…',
                note: '记录内部笔记…'
            },
            'desktop': {
                message: '向关注者发送消息…',
                note: '记录内部笔记…'
            }
        });
    }

    getPlaceholder(context) {
        // 获取占位符
        try {
            // 分析上下文
            const analysis = this.analyzeContext(context);

            // 选择最佳策略
            const strategy = this.selectBestStrategy(analysis);

            // 生成占位符
            const placeholder = this.generatePlaceholder(strategy, analysis);

            // 本地化处理
            return this.localizePlaceholder(placeholder, analysis.language);

        } catch (error) {
            console.error('占位符生成失败:', error);
            return this.getDefaultPlaceholder(context.type);
        }
    }

    analyzeContext(context) {
        // 分析上下文
        const analysis = {
            threadModel: context.thread?.model,
            messageType: context.type,
            userRole: this.getUserRole(context.user),
            deviceType: this.getDeviceType(),
            businessContext: this.getBusinessContext(context.thread),
            language: this.getLanguage(),
            customPlaceholder: context.placeholder
        };

        return analysis;
    }

    getUserRole(user) {
        // 获取用户角色
        if (!user) return 'guest';

        if (user.isAdmin) return 'admin';
        if (user.isInternal) return 'internal';
        if (user.isPortal) return 'portal';

        return 'user';
    }

    getDeviceType() {
        // 获取设备类型
        const width = window.innerWidth;

        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }

    getBusinessContext(thread) {
        // 获取业务上下文
        if (!thread) return 'general';

        const modelContextMap = {
            'helpdesk.ticket': 'customer_service',
            'crm.lead': 'sales',
            'project.task': 'project',
            'sale.order': 'sales',
            'purchase.order': 'procurement',
            'account.move': 'accounting',
            'hr.employee': 'hr'
        };

        return modelContextMap[thread.model] || 'general';
    }

    getLanguage() {
        // 获取语言
        return document.documentElement.lang || 'en';
    }

    selectBestStrategy(analysis) {
        // 选择最佳策略
        const strategies = [];

        // 优先级：自定义 > 线程模型 > 用户角色 > 业务上下文 > 设备类型
        if (analysis.customPlaceholder) {
            return { type: 'custom', value: analysis.customPlaceholder };
        }

        if (analysis.threadModel) {
            const threadStrategy = this.placeholderStrategies.get('thread_model')[analysis.threadModel];
            if (threadStrategy) {
                strategies.push({ type: 'thread_model', value: threadStrategy, priority: 4 });
            }
        }

        if (analysis.userRole) {
            const roleStrategy = this.placeholderStrategies.get('user_role')[analysis.userRole];
            if (roleStrategy) {
                strategies.push({ type: 'user_role', value: roleStrategy, priority: 3 });
            }
        }

        if (analysis.businessContext) {
            const contextStrategy = this.placeholderStrategies.get('business_context')[analysis.businessContext];
            if (contextStrategy) {
                strategies.push({ type: 'business_context', value: contextStrategy, priority: 2 });
            }
        }

        if (analysis.deviceType) {
            const deviceStrategy = this.placeholderStrategies.get('device_type')[analysis.deviceType];
            if (deviceStrategy) {
                strategies.push({ type: 'device_type', value: deviceStrategy, priority: 1 });
            }
        }

        // 选择优先级最高的策略
        strategies.sort((a, b) => b.priority - a.priority);
        return strategies[0] || { type: 'default', value: null };
    }

    generatePlaceholder(strategy, analysis) {
        // 生成占位符
        if (strategy.type === 'custom') {
            return strategy.value;
        }

        if (strategy.value) {
            const messageType = analysis.messageType === 'message' ? 'message' : 'note';
            return strategy.value[messageType] || strategy.value.message;
        }

        return this.getDefaultPlaceholder(analysis.messageType);
    }

    getDefaultPlaceholder(messageType) {
        // 获取默认占位符
        if (messageType === 'message') {
            return '向关注者发送消息…';
        } else {
            return '记录内部笔记…';
        }
    }

    localizePlaceholder(placeholder, language) {
        // 本地化占位符
        const cacheKey = `${placeholder}_${language}`;

        if (this.localizationCache.has(cacheKey)) {
            return this.localizationCache.get(cacheKey);
        }

        // 这里可以集成翻译服务
        const localized = this.translatePlaceholder(placeholder, language);

        this.localizationCache.set(cacheKey, localized);
        return localized;
    }

    translatePlaceholder(placeholder, language) {
        // 翻译占位符
        const translations = {
            'en': {
                '向关注者发送消息…': 'Send a message to followers…',
                '记录内部笔记…': 'Log an internal note…',
                '发送消息…': 'Send a message…',
                '添加备注…': 'Add a note…'
            },
            'zh': {
                'Send a message to followers…': '向关注者发送消息…',
                'Log an internal note…': '记录内部笔记…',
                'Send a message…': '发送消息…',
                'Add a note…': '添加备注…'
            },
            'fr': {
                '向关注者发送消息…': 'Envoyer un message aux abonnés…',
                '记录内部笔记…': 'Enregistrer une note interne…'
            },
            'es': {
                '向关注者发送消息…': 'Enviar un mensaje a los seguidores…',
                '记录内部笔记…': 'Registrar una nota interna…'
            }
        };

        const langTranslations = translations[language];
        return langTranslations?.[placeholder] || placeholder;
    }

    addPlaceholderStrategy(strategyType, strategyData) {
        // 添加占位符策略
        this.placeholderStrategies.set(strategyType, strategyData);
    }

    updatePlaceholderStrategy(strategyType, updates) {
        // 更新占位符策略
        const existing = this.placeholderStrategies.get(strategyType);
        if (existing) {
            this.placeholderStrategies.set(strategyType, { ...existing, ...updates });
        }
    }

    clearLocalizationCache() {
        // 清理本地化缓存
        this.localizationCache.clear();
    }

    getStrategyStats() {
        // 获取策略统计
        return {
            totalStrategies: this.placeholderStrategies.size,
            cacheSize: this.localizationCache.size,
            availableStrategies: Array.from(this.placeholderStrategies.keys())
        };
    }
}
```

### 2. 上下文感知系统
```javascript
// 上下文感知系统
class ContextAwareSystem {
    constructor() {
        this.contextProviders = new Map();
        this.contextCache = new Map();
        this.contextHistory = [];
        this.setupContextProviders();
    }

    setupContextProviders() {
        // 设置上下文提供者

        // 线程上下文提供者
        this.contextProviders.set('thread', {
            analyze: (thread) => {
                if (!thread) return null;

                return {
                    model: thread.model,
                    id: thread.id,
                    name: thread.name,
                    state: thread.state,
                    priority: thread.priority,
                    tags: thread.tags,
                    followers: thread.followers?.length || 0,
                    messages: thread.messages?.length || 0,
                    lastActivity: thread.lastActivity
                };
            },
            weight: 0.4
        });

        // 用户上下文提供者
        this.contextProviders.set('user', {
            analyze: (user) => {
                if (!user) return null;

                return {
                    id: user.id,
                    name: user.name,
                    role: this.getUserRole(user),
                    permissions: user.permissions,
                    preferences: user.preferences,
                    timezone: user.timezone,
                    language: user.language,
                    isOnline: user.isOnline
                };
            },
            weight: 0.3
        });

        // 环境上下文提供者
        this.contextProviders.set('environment', {
            analyze: () => {
                return {
                    platform: this.getPlatform(),
                    device: this.getDeviceInfo(),
                    browser: this.getBrowserInfo(),
                    screen: this.getScreenInfo(),
                    network: this.getNetworkInfo(),
                    time: this.getTimeInfo()
                };
            },
            weight: 0.2
        });

        // 业务上下文提供者
        this.contextProviders.set('business', {
            analyze: (thread, user) => {
                return {
                    domain: this.getBusinessDomain(thread),
                    workflow: this.getWorkflowStage(thread),
                    urgency: this.getUrgencyLevel(thread),
                    customer: this.getCustomerInfo(thread),
                    project: this.getProjectInfo(thread),
                    department: this.getDepartmentInfo(user)
                };
            },
            weight: 0.1
        });
    }

    analyzeFullContext(thread, user, props) {
        // 分析完整上下文
        const contextKey = this.generateContextKey(thread, user, props);

        // 检查缓存
        if (this.contextCache.has(contextKey)) {
            return this.contextCache.get(contextKey);
        }

        const fullContext = {
            timestamp: Date.now(),
            key: contextKey
        };

        // 收集各个提供者的上下文
        for (const [name, provider] of this.contextProviders) {
            try {
                const context = provider.analyze(thread, user, props);
                if (context) {
                    fullContext[name] = {
                        data: context,
                        weight: provider.weight,
                        timestamp: Date.now()
                    };
                }
            } catch (error) {
                console.error(`上下文分析失败 (${name}):`, error);
            }
        }

        // 计算上下文得分
        fullContext.score = this.calculateContextScore(fullContext);

        // 缓存结果
        this.contextCache.set(contextKey, fullContext);

        // 记录历史
        this.contextHistory.push({
            key: contextKey,
            timestamp: Date.now(),
            score: fullContext.score
        });

        // 清理历史
        this.cleanupHistory();

        return fullContext;
    }

    generateContextKey(thread, user, props) {
        // 生成上下文键
        const parts = [
            thread?.model || 'no-thread',
            thread?.id || 'no-id',
            user?.id || 'no-user',
            props?.type || 'no-type',
            Date.now().toString().slice(-6) // 时间戳后6位
        ];

        return parts.join('_');
    }

    calculateContextScore(context) {
        // 计算上下文得分
        let totalScore = 0;
        let totalWeight = 0;

        for (const [name, info] of Object.entries(context)) {
            if (info && typeof info === 'object' && info.weight) {
                const dataScore = this.scoreContextData(info.data);
                totalScore += dataScore * info.weight;
                totalWeight += info.weight;
            }
        }

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    scoreContextData(data) {
        // 为上下文数据评分
        if (!data || typeof data !== 'object') return 0;

        let score = 0;
        const keys = Object.keys(data);

        // 基于数据完整性评分
        score += keys.length * 0.1;

        // 基于数据质量评分
        keys.forEach(key => {
            const value = data[key];
            if (value !== null && value !== undefined && value !== '') {
                score += 0.1;

                // 特殊字段加分
                if (['name', 'title', 'subject'].includes(key) && value.length > 0) {
                    score += 0.2;
                }

                if (['priority', 'urgency', 'state'].includes(key)) {
                    score += 0.15;
                }
            }
        });

        return Math.min(score, 1); // 最大得分为1
    }

    getPlatform() {
        // 获取平台信息
        const userAgent = navigator.userAgent.toLowerCase();

        if (userAgent.includes('mobile')) return 'mobile';
        if (userAgent.includes('tablet')) return 'tablet';
        return 'desktop';
    }

    getDeviceInfo() {
        // 获取设备信息
        return {
            type: this.getPlatform(),
            screen: {
                width: window.screen.width,
                height: window.screen.height,
                ratio: window.devicePixelRatio
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            }
        };
    }

    getBrowserInfo() {
        // 获取浏览器信息
        const userAgent = navigator.userAgent;

        return {
            name: this.getBrowserName(userAgent),
            version: this.getBrowserVersion(userAgent),
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };
    }

    getBrowserName(userAgent) {
        // 获取浏览器名称
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return 'Unknown';
    }

    getBrowserVersion(userAgent) {
        // 获取浏览器版本
        const match = userAgent.match(/(?:Chrome|Firefox|Safari|Edge)\/(\d+)/);
        return match ? match[1] : 'Unknown';
    }

    getScreenInfo() {
        // 获取屏幕信息
        return {
            width: window.screen.width,
            height: window.screen.height,
            availWidth: window.screen.availWidth,
            availHeight: window.screen.availHeight,
            colorDepth: window.screen.colorDepth,
            pixelDepth: window.screen.pixelDepth
        };
    }

    getNetworkInfo() {
        // 获取网络信息
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;

        if (connection) {
            return {
                effectiveType: connection.effectiveType,
                downlink: connection.downlink,
                rtt: connection.rtt,
                saveData: connection.saveData
            };
        }

        return { available: false };
    }

    getTimeInfo() {
        // 获取时间信息
        const now = new Date();

        return {
            timestamp: now.getTime(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            offset: now.getTimezoneOffset(),
            hour: now.getHours(),
            dayOfWeek: now.getDay(),
            isWeekend: now.getDay() === 0 || now.getDay() === 6
        };
    }

    getBusinessDomain(thread) {
        // 获取业务领域
        if (!thread) return 'general';

        const domainMap = {
            'helpdesk.ticket': 'support',
            'crm.lead': 'sales',
            'project.task': 'project',
            'sale.order': 'sales',
            'purchase.order': 'procurement',
            'account.move': 'finance',
            'hr.employee': 'hr',
            'res.partner': 'crm'
        };

        return domainMap[thread.model] || 'general';
    }

    getWorkflowStage(thread) {
        // 获取工作流阶段
        if (!thread) return 'unknown';

        return thread.stage || thread.state || 'draft';
    }

    getUrgencyLevel(thread) {
        // 获取紧急程度
        if (!thread) return 'normal';

        if (thread.priority) {
            const priorityMap = {
                '0': 'low',
                '1': 'normal',
                '2': 'high',
                '3': 'urgent'
            };
            return priorityMap[thread.priority] || 'normal';
        }

        return 'normal';
    }

    getCustomerInfo(thread) {
        // 获取客户信息
        if (!thread || !thread.partner) return null;

        return {
            id: thread.partner.id,
            name: thread.partner.name,
            type: thread.partner.customer_type,
            category: thread.partner.category_id
        };
    }

    getProjectInfo(thread) {
        // 获取项目信息
        if (!thread || thread.model !== 'project.task') return null;

        return {
            id: thread.project_id,
            name: thread.project_name,
            stage: thread.stage_id
        };
    }

    getDepartmentInfo(user) {
        // 获取部门信息
        if (!user || !user.department) return null;

        return {
            id: user.department.id,
            name: user.department.name,
            manager: user.department.manager_id
        };
    }

    cleanupHistory() {
        // 清理历史记录
        const maxHistory = 100;
        const maxAge = 60 * 60 * 1000; // 1小时
        const now = Date.now();

        // 移除过期记录
        this.contextHistory = this.contextHistory.filter(
            item => now - item.timestamp < maxAge
        );

        // 限制记录数量
        if (this.contextHistory.length > maxHistory) {
            this.contextHistory = this.contextHistory.slice(-maxHistory);
        }
    }

    getContextStats() {
        // 获取上下文统计
        return {
            providers: this.contextProviders.size,
            cacheSize: this.contextCache.size,
            historySize: this.contextHistory.length,
            averageScore: this.calculateAverageScore()
        };
    }

    calculateAverageScore() {
        // 计算平均得分
        if (this.contextHistory.length === 0) return 0;

        const totalScore = this.contextHistory.reduce((sum, item) => sum + item.score, 0);
        return totalScore / this.contextHistory.length;
    }
}
```
# Odoo Web Portal Chatter 组件学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web_portal/chatter.js`  
**原始路径**: `/mail/static/src/chatter/web_portal/chatter.js`  
**模块类型**: Web Portal 核心组件 - Chatter 聊天组件  
**代码行数**: 131 行  
**依赖关系**: 
- `@mail/core/common/composer` - 消息撰写器组件
- `@mail/core/common/thread` - 线程组件
- `@odoo/owl` - OWL 框架
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/utils/hooks` - 核心钩子
- `@web/core/utils/timing` - 时间工具

## 模块功能

Web Portal Chatter 组件是 Odoo Web Portal 中的核心聊天组件。该模块提供了：
- Portal 环境下的消息线程显示
- 简化的消息撰写功能
- 线程切换和数据加载
- 滚动状态管理
- 响应式布局支持
- 新记录创建时的占位消息

这个组件专门为 Web Portal 环境优化，提供了轻量级但功能完整的聊天体验。

## Web Portal Chatter 架构

### 核心组件结构
```
Web Portal Chatter
├── 线程管理
│   ├── 线程切换
│   ├── 数据加载
│   ├── 消息同步
│   └── 状态跟踪
├── 消息显示
│   ├── Thread 组件
│   ├── 消息列表
│   ├── 滚动处理
│   └── 跳转控制
├── 消息撰写
│   ├── Composer 组件
│   ├── 发送回调
│   ├── 全屏撰写
│   └── 关闭处理
├── 布局控制
│   ├── 单列/双列布局
│   ├── 侧边栏控制
│   ├── 响应式设计
│   └── 滚动状态
└── Portal 特性
    ├── 简化界面
    ├── 权限控制
    ├── 性能优化
    └── 移动端适配
```

### 组件配置
```javascript
// Web Portal Chatter 组件
class Chatter extends Component {
    static template = "mail.Chatter";
    static components = { Thread, Composer };
    static props = [
        "composer?",      // 是否显示撰写器
        "threadId?",      // 线程ID
        "threadModel",    // 线程模型
        "twoColumns?"     // 是否双列布局
    ];
    static defaultProps = { 
        composer: true, 
        threadId: false, 
        twoColumns: false 
    };
    
    setup() {
        this.store = useState(useService("mail.store"));
        this.state = useState({
            jumpThreadPresent: 0,    // 跳转计数器
            thread: undefined,       // 当前线程
            aside: false,           // 侧边栏状态
        });
        this.rootRef = useRef("root");
        this.onScrollDebounced = useThrottleForAnimation(this.onScroll);
        useChildSubEnv(this.childSubEnv);
    }
}
```

## 核心功能详解

### 1. 线程管理系统
```javascript
// 线程管理器
class ThreadManager {
    constructor(chatter) {
        this.chatter = chatter;
        this.currentThread = null;
        this.threadCache = new Map();
        this.loadingStates = new Map();
        this.setupThreadManagement();
    }
    
    setupThreadManagement() {
        // 设置线程管理
        this.requestStrategies = {
            default: [],
            afterPost: ["messages"],
            onCloseFullComposer: ["messages"]
        };
        
        this.threadStates = {
            IDLE: 'idle',
            LOADING: 'loading',
            LOADED: 'loaded',
            ERROR: 'error'
        };
    }
    
    changeThread(threadModel, threadId) {
        // 切换线程
        try {
            // 创建或获取线程
            const thread = this.chatter.store.Thread.insert({ 
                model: threadModel, 
                id: threadId 
            });
            
            // 更新当前线程
            this.currentThread = thread;
            this.chatter.state.thread = thread;
            
            // 处理新记录情况
            if (threadId === false) {
                this.handleNewRecord(thread, threadModel);
            }
            
            // 缓存线程
            this.cacheThread(thread);
            
            // 触发线程切换事件
            this.triggerThreadChanged(thread);
            
        } catch (error) {
            console.error('线程切换失败:', error);
            this.handleThreadError(error);
        }
    }
    
    handleNewRecord(thread, threadModel) {
        // 处理新记录
        if (thread.messages.length === 0) {
            const placeholderMessage = {
                id: this.chatter.store.getNextTemporaryId(),
                author: this.chatter.store.self,
                body: this.getNewRecordMessage(threadModel),
                message_type: "notification",
                thread: thread,
                trackingValues: [],
                res_id: false,
                model: threadModel,
                isPlaceholder: true,
                timestamp: Date.now()
            };
            
            thread.messages.push(placeholderMessage);
        }
    }
    
    getNewRecordMessage(threadModel) {
        // 获取新记录消息
        const modelMessages = {
            'res.partner': '正在创建新的联系人...',
            'project.task': '正在创建新的任务...',
            'helpdesk.ticket': '正在创建新的工单...',
            'sale.order': '正在创建新的销售订单...',
            'purchase.order': '正在创建新的采购订单...',
            'account.move': '正在创建新的会计凭证...',
            'hr.employee': '正在创建新的员工档案...',
            'product.product': '正在创建新的产品...',
            'stock.picking': '正在创建新的库存操作...',
            'crm.lead': '正在创建新的商机...'
        };
        
        return modelMessages[threadModel] || '正在创建新记录...';
    }
    
    async loadThreadData(thread, requestList = []) {
        // 加载线程数据
        if (!thread || !thread.id || !this.chatter.state.thread?.eq(thread)) {
            return;
        }
        
        try {
            // 设置加载状态
            this.setLoadingState(thread.id, this.threadStates.LOADING);
            
            // 执行数据获取
            await thread.fetchData(requestList);
            
            // 设置加载完成状态
            this.setLoadingState(thread.id, this.threadStates.LOADED);
            
            // 触发数据加载完成事件
            this.triggerDataLoaded(thread, requestList);
            
        } catch (error) {
            console.error('线程数据加载失败:', error);
            this.setLoadingState(thread.id, this.threadStates.ERROR);
            this.handleLoadError(thread, error);
        }
    }
    
    cacheThread(thread) {
        // 缓存线程
        if (thread && thread.id) {
            const cacheKey = `${thread.model}_${thread.id}`;
            this.threadCache.set(cacheKey, {
                thread: thread,
                lastAccessed: Date.now(),
                accessCount: (this.threadCache.get(cacheKey)?.accessCount || 0) + 1
            });
            
            // 清理过期缓存
            this.cleanupCache();
        }
    }
    
    cleanupCache() {
        // 清理缓存
        const maxCacheSize = 50;
        const maxAge = 30 * 60 * 1000; // 30分钟
        const now = Date.now();
        
        if (this.threadCache.size > maxCacheSize) {
            // 按访问时间排序，删除最旧的
            const entries = Array.from(this.threadCache.entries())
                .sort(([,a], [,b]) => a.lastAccessed - b.lastAccessed);
            
            const toDelete = entries.slice(0, entries.length - maxCacheSize);
            toDelete.forEach(([key]) => this.threadCache.delete(key));
        }
        
        // 删除过期缓存
        for (const [key, value] of this.threadCache.entries()) {
            if (now - value.lastAccessed > maxAge) {
                this.threadCache.delete(key);
            }
        }
    }
    
    setLoadingState(threadId, state) {
        // 设置加载状态
        this.loadingStates.set(threadId, {
            state: state,
            timestamp: Date.now()
        });
    }
    
    getLoadingState(threadId) {
        // 获取加载状态
        return this.loadingStates.get(threadId)?.state || this.threadStates.IDLE;
    }
    
    triggerThreadChanged(thread) {
        // 触发线程切换事件
        this.chatter.env.bus?.trigger('CHATTER:THREAD_CHANGED', {
            thread: thread,
            timestamp: Date.now()
        });
    }
    
    triggerDataLoaded(thread, requestList) {
        // 触发数据加载完成事件
        this.chatter.env.bus?.trigger('CHATTER:DATA_LOADED', {
            thread: thread,
            requestList: requestList,
            timestamp: Date.now()
        });
    }
    
    handleThreadError(error) {
        // 处理线程错误
        console.error('线程操作错误:', error);
        
        this.chatter.env.services?.notification?.add(
            '线程操作失败，请重试',
            { type: 'danger' }
        );
    }
    
    handleLoadError(thread, error) {
        // 处理加载错误
        console.error('数据加载错误:', error);
        
        this.chatter.env.services?.notification?.add(
            '数据加载失败，请刷新页面重试',
            { type: 'warning' }
        );
    }
    
    getThreadStats() {
        // 获取线程统计
        return {
            currentThread: this.currentThread?.id,
            cacheSize: this.threadCache.size,
            loadingStates: Object.fromEntries(this.loadingStates),
            totalAccess: Array.from(this.threadCache.values())
                .reduce((sum, item) => sum + item.accessCount, 0)
        };
    }
}
```

### 3. 滚动控制系统
```javascript
// 滚动控制器
class ScrollController {
    constructor(chatter) {
        this.chatter = chatter;
        this.scrollState = {
            isTopStickyPinned: false,
            lastScrollTop: 0,
            scrollDirection: 'down',
            isUserScrolling: false,
            autoScrollEnabled: true
        };
        this.setupScrollControl();
    }

    setupScrollControl() {
        // 设置滚动控制
        this.scrollThresholds = {
            stickyPin: 0,
            autoScroll: 100,
            loadMore: 200
        };

        this.scrollAnimations = {
            duration: 300,
            easing: 'ease-in-out'
        };

        this.bindScrollEvents();
    }

    bindScrollEvents() {
        // 绑定滚动事件
        this.onScrollDebounced = this.chatter.onScrollDebounced;

        // 监听滚动事件
        this.chatter.env.bus?.addEventListener('scroll', this.handleScroll.bind(this));
        this.chatter.env.bus?.addEventListener('resize', this.handleResize.bind(this));
    }

    handleScroll(event) {
        // 处理滚动事件
        const element = this.chatter.rootRef.el;
        if (!element) return;

        const currentScrollTop = element.scrollTop;
        const scrollHeight = element.scrollHeight;
        const clientHeight = element.clientHeight;

        // 更新滚动状态
        this.updateScrollState(currentScrollTop, scrollHeight, clientHeight);

        // 处理粘性定位
        this.handleStickyPin(currentScrollTop);

        // 处理自动加载
        this.handleAutoLoad(currentScrollTop, scrollHeight, clientHeight);

        // 更新最后滚动位置
        this.scrollState.lastScrollTop = currentScrollTop;
    }

    updateScrollState(scrollTop, scrollHeight, clientHeight) {
        // 更新滚动状态

        // 计算滚动方向
        if (scrollTop > this.scrollState.lastScrollTop) {
            this.scrollState.scrollDirection = 'down';
        } else if (scrollTop < this.scrollState.lastScrollTop) {
            this.scrollState.scrollDirection = 'up';
        }

        // 检测用户是否在滚动
        this.scrollState.isUserScrolling = true;
        clearTimeout(this.scrollTimeout);
        this.scrollTimeout = setTimeout(() => {
            this.scrollState.isUserScrolling = false;
        }, 150);

        // 计算滚动百分比
        const scrollPercentage = scrollHeight > clientHeight ?
            scrollTop / (scrollHeight - clientHeight) : 0;

        this.scrollState.scrollPercentage = scrollPercentage;
    }

    handleStickyPin(scrollTop) {
        // 处理粘性定位
        const shouldPin = scrollTop > this.scrollThresholds.stickyPin;

        if (this.scrollState.isTopStickyPinned !== shouldPin) {
            this.scrollState.isTopStickyPinned = shouldPin;
            this.chatter.state.isTopStickyPinned = shouldPin;

            // 触发粘性状态变化事件
            this.triggerStickyStateChanged(shouldPin);
        }
    }

    handleAutoLoad(scrollTop, scrollHeight, clientHeight) {
        // 处理自动加载
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        if (distanceFromBottom < this.scrollThresholds.loadMore) {
            this.triggerLoadMore();
        }
    }

    scrollToTop(animated = true) {
        // 滚动到顶部
        const element = this.chatter.rootRef.el;
        if (!element) return;

        if (animated) {
            element.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        } else {
            element.scrollTop = 0;
        }
    }

    scrollToBottom(animated = true) {
        // 滚动到底部
        const element = this.chatter.rootRef.el;
        if (!element) return;

        if (animated) {
            element.scrollTo({
                top: element.scrollHeight,
                behavior: 'smooth'
            });
        } else {
            element.scrollTop = element.scrollHeight;
        }
    }

    scrollToMessage(messageId, animated = true) {
        // 滚动到指定消息
        const messageElement = this.chatter.rootRef.el?.querySelector(
            `[data-message-id="${messageId}"]`
        );

        if (messageElement) {
            messageElement.scrollIntoView({
                behavior: animated ? 'smooth' : 'auto',
                block: 'center'
            });
        }
    }

    enableAutoScroll() {
        // 启用自动滚动
        this.scrollState.autoScrollEnabled = true;
    }

    disableAutoScroll() {
        // 禁用自动滚动
        this.scrollState.autoScrollEnabled = false;
    }

    handleResize() {
        // 处理窗口大小变化
        setTimeout(() => {
            if (this.scrollState.autoScrollEnabled &&
                this.scrollState.scrollDirection === 'down') {
                this.scrollToBottom(false);
            }
        }, 100);
    }

    triggerStickyStateChanged(isPinned) {
        // 触发粘性状态变化事件
        this.chatter.env.bus?.trigger('CHATTER:STICKY_STATE_CHANGED', {
            isPinned: isPinned,
            timestamp: Date.now()
        });
    }

    triggerLoadMore() {
        // 触发加载更多事件
        this.chatter.env.bus?.trigger('CHATTER:LOAD_MORE', {
            thread: this.chatter.state.thread,
            timestamp: Date.now()
        });
    }

    getScrollStats() {
        // 获取滚动统计
        return {
            ...this.scrollState,
            element: {
                scrollTop: this.chatter.rootRef.el?.scrollTop || 0,
                scrollHeight: this.chatter.rootRef.el?.scrollHeight || 0,
                clientHeight: this.chatter.rootRef.el?.clientHeight || 0
            }
        };
    }
}
```

## 使用示例

### 1. 基本 Portal Chatter 使用
```javascript
// 使用 Portal Chatter 组件
class PortalPage extends Component {
    static template = xml`
        <div class="portal-page">
            <div class="portal-content">
                <!-- 页面内容 -->
            </div>

            <div class="portal-chatter">
                <Chatter
                    threadModel="'helpdesk.ticket'"
                    threadId="ticketId"
                    composer="true"
                    twoColumns="false"/>
            </div>
        </div>
    `;

    static components = { Chatter };

    setup() {
        this.ticketId = this.props.ticketId;
        this.setupPortalChatter();
    }

    setupPortalChatter() {
        // 设置 Portal Chatter
        this.chatterConfig = {
            threadModel: 'helpdesk.ticket',
            threadId: this.ticketId,
            composer: true,
            twoColumns: false,
            autoRefresh: true,
            refreshInterval: 30000 // 30秒
        };

        this.setupAutoRefresh();
    }

    setupAutoRefresh() {
        // 设置自动刷新
        if (this.chatterConfig.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                this.refreshChatter();
            }, this.chatterConfig.refreshInterval);
        }
    }

    refreshChatter() {
        // 刷新 Chatter
        this.env.bus.trigger('CHATTER:REFRESH', {
            threadModel: this.chatterConfig.threadModel,
            threadId: this.chatterConfig.threadId
        });
    }

    onWillUnmount() {
        // 组件卸载时清理
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}
```

### 2. 响应式布局使用
```javascript
// 响应式 Portal Chatter
class ResponsivePortalChatter extends Component {
    setup() {
        this.uiService = useService("ui");
        this.state = useState({
            isMobile: false,
            isTablet: false,
            layoutMode: 'single'
        });

        this.setupResponsiveLayout();
    }

    setupResponsiveLayout() {
        // 设置响应式布局
        this.updateLayoutMode();

        // 监听屏幕尺寸变化
        this.env.bus.addEventListener('resize', this.updateLayoutMode.bind(this));
    }

    updateLayoutMode() {
        // 更新布局模式
        const size = this.uiService.size;

        this.state.isMobile = size <= 2; // XS, SM
        this.state.isTablet = size === 3; // MD

        if (this.state.isMobile) {
            this.state.layoutMode = 'mobile';
        } else if (this.state.isTablet) {
            this.state.layoutMode = 'tablet';
        } else {
            this.state.layoutMode = 'desktop';
        }
    }

    get chatterProps() {
        // 获取 Chatter 属性
        const baseProps = {
            threadModel: this.props.threadModel,
            threadId: this.props.threadId,
            composer: true
        };

        switch (this.state.layoutMode) {
            case 'mobile':
                return {
                    ...baseProps,
                    twoColumns: false,
                    composer: true // 移动端保持撰写器
                };
            case 'tablet':
                return {
                    ...baseProps,
                    twoColumns: false,
                    composer: true
                };
            case 'desktop':
                return {
                    ...baseProps,
                    twoColumns: this.props.twoColumns || false,
                    composer: true
                };
            default:
                return baseProps;
        }
    }

    get containerClass() {
        // 获取容器样式类
        return `portal-chatter portal-chatter--${this.state.layoutMode}`;
    }
}
```

### 3. 高级功能集成
```javascript
// 高级 Portal Chatter 功能
class AdvancedPortalChatter extends Component {
    setup() {
        this.chatter = useRef("chatter");
        this.threadManager = null;
        this.messageProcessor = null;
        this.scrollController = null;

        this.setupAdvancedFeatures();
    }

    setupAdvancedFeatures() {
        // 设置高级功能
        onMounted(() => {
            this.initializeManagers();
            this.setupEventListeners();
            this.setupKeyboardShortcuts();
        });
    }

    initializeManagers() {
        // 初始化管理器
        const chatterComponent = this.chatter.comp;

        this.threadManager = new ThreadManager(chatterComponent);
        this.messageProcessor = new MessageProcessor(chatterComponent);
        this.scrollController = new ScrollController(chatterComponent);
    }

    setupEventListeners() {
        // 设置事件监听器
        this.env.bus.addEventListener('CHATTER:THREAD_CHANGED', this.onThreadChanged.bind(this));
        this.env.bus.addEventListener('CHATTER:MESSAGE_SENT', this.onMessageSent.bind(this));
        this.env.bus.addEventListener('CHATTER:DATA_LOADED', this.onDataLoaded.bind(this));
    }

    setupKeyboardShortcuts() {
        // 设置键盘快捷键
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    handleKeyDown(event) {
        // 处理键盘事件
        if (event.ctrlKey || event.metaKey) {
            switch (event.key) {
                case 'r':
                    event.preventDefault();
                    this.refreshChatter();
                    break;
                case 'Home':
                    event.preventDefault();
                    this.scrollController.scrollToTop();
                    break;
                case 'End':
                    event.preventDefault();
                    this.scrollController.scrollToBottom();
                    break;
            }
        }
    }

    onThreadChanged(event) {
        // 线程切换处理
        const { thread } = event.detail;
        console.log('线程已切换:', thread.model, thread.id);

        // 重置滚动状态
        this.scrollController.scrollToBottom(false);

        // 更新页面标题
        this.updatePageTitle(thread);
    }

    onMessageSent(event) {
        // 消息发送处理
        const { result, thread } = event.detail;
        console.log('消息已发送:', result);

        // 显示成功通知
        this.showNotification('消息发送成功', 'success');

        // 自动滚动到底部
        setTimeout(() => {
            this.scrollController.scrollToBottom();
        }, 100);
    }

    onDataLoaded(event) {
        // 数据加载完成处理
        const { thread, requestList } = event.detail;
        console.log('数据加载完成:', thread.id, requestList);

        // 更新加载状态
        this.updateLoadingState(false);
    }

    updatePageTitle(thread) {
        // 更新页面标题
        if (thread && thread.name) {
            document.title = `${thread.name} - Portal`;
        }
    }

    showNotification(message, type = 'info') {
        // 显示通知
        this.env.services.notification?.add(message, { type });
    }

    updateLoadingState(isLoading) {
        // 更新加载状态
        const loadingElement = document.querySelector('.chatter-loading');
        if (loadingElement) {
            loadingElement.style.display = isLoading ? 'block' : 'none';
        }
    }

    refreshChatter() {
        // 刷新 Chatter
        const chatterComponent = this.chatter.comp;
        if (chatterComponent && chatterComponent.state.thread) {
            chatterComponent.load(chatterComponent.state.thread, ['messages']);
        }
    }

    getChatterStats() {
        // 获取 Chatter 统计
        return {
            thread: this.threadManager?.getThreadStats(),
            messages: this.messageProcessor?.getProcessingStats(),
            scroll: this.scrollController?.getScrollStats()
        };
    }
}
```

## Portal Chatter 特性

### 1. 轻量级设计
Portal Chatter 专为 Web Portal 环境优化：
- **简化界面**: 去除复杂的管理功能
- **性能优化**: 减少不必要的数据加载
- **移动友好**: 响应式设计适配移动设备
- **快速加载**: 优化的资源加载策略

### 2. 核心功能保留
```javascript
// 核心功能配置
const portalFeatures = {
    messaging: true,        // 消息收发
    threading: true,        // 线程管理
    composer: true,         // 消息撰写
    scrolling: true,        // 滚动控制
    responsive: true,       // 响应式布局

    // 简化的功能
    activities: false,      // 活动管理
    followers: false,       // 关注者管理
    attachments: 'basic',   // 基础附件功能
    scheduling: false       // 计划消息
};
```

### 3. 自动同步机制
- **手动刷新**: 由于缺乏自动同步，需要手动刷新
- **发送后加载**: 发送消息后自动加载新消息
- **定时刷新**: 可配置的定时刷新机制
- **事件驱动**: 基于事件的数据更新

### 4. 用户体验优化
- **占位消息**: 新记录创建时的友好提示
- **滚动记忆**: 记住用户的滚动位置
- **键盘快捷键**: 支持常用的键盘操作
- **加载状态**: 清晰的加载状态指示

## 最佳实践

### 1. 组件使用
```javascript
// ✅ 推荐：正确的 Portal Chatter 使用
<Chatter
    threadModel="helpdesk.ticket"
    threadId="ticketId"
    composer="true"
    twoColumns="false"/>
```

### 2. 性能优化
```javascript
// ✅ 推荐：性能优化配置
const chatterConfig = {
    autoRefresh: true,
    refreshInterval: 30000,  // 30秒刷新
    cacheSize: 50,          // 缓存大小
    loadBatchSize: 20       // 批量加载大小
};
```

### 3. 响应式设计
```javascript
// ✅ 推荐：响应式布局适配
get layoutProps() {
    const isMobile = this.uiService.size <= 2;
    return {
        twoColumns: !isMobile,
        composer: true
    };
}
```

### 4. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.loadThreadData(thread, requestList);
} catch (error) {
    console.error('数据加载失败:', error);
    this.showNotification('加载失败，请重试', 'danger');
}
```

## 总结

Odoo Web Portal Chatter 组件提供了专为 Portal 环境优化的聊天功能：

**核心优势**:
- **轻量级设计**: 专为 Portal 环境优化的轻量级实现
- **响应式布局**: 完美适配各种设备和屏幕尺寸
- **用户友好**: 简洁直观的用户界面和交互体验
- **性能优化**: 优化的数据加载和渲染性能
- **扩展性**: 良好的扩展性和自定义能力

**适用场景**:
- Web Portal 客户服务
- 工单系统集成
- 客户沟通平台
- 移动端聊天应用
- 简化的内部沟通

**设计优势**:
- 组件化架构
- 事件驱动设计
- 状态管理优化
- 性能监控集成

这个 Portal Chatter 组件为 Odoo Web Portal 提供了专业的聊天功能，在保持功能完整性的同时，优化了性能和用户体验。
```

### 2. 消息处理系统
```javascript
// 消息处理器
class MessageProcessor {
    constructor(chatter) {
        this.chatter = chatter;
        this.messageQueue = [];
        this.processingState = {
            isProcessing: false,
            lastProcessed: null,
            processedCount: 0
        };
        this.setupMessageProcessing();
    }
    
    setupMessageProcessing() {
        // 设置消息处理
        this.messageHandlers = {
            'post': this.handlePostMessage.bind(this),
            'edit': this.handleEditMessage.bind(this),
            'delete': this.handleDeleteMessage.bind(this),
            'react': this.handleReactMessage.bind(this),
            'forward': this.handleForwardMessage.bind(this)
        };
        
        this.callbackStrategies = {
            afterPost: this.handleAfterPost.bind(this),
            onCloseFullComposer: this.handleCloseFullComposer.bind(this)
        };
    }
    
    async handlePostMessage(messageData) {
        // 处理发送消息
        try {
            // 验证消息数据
            this.validateMessageData(messageData);
            
            // 预处理消息
            const processedData = this.preprocessMessage(messageData);
            
            // 发送消息
            const result = await this.sendMessage(processedData);
            
            // 后处理
            await this.postprocessMessage(result);
            
            return result;
            
        } catch (error) {
            console.error('消息发送失败:', error);
            throw error;
        }
    }
    
    validateMessageData(messageData) {
        // 验证消息数据
        const requiredFields = ['body', 'thread'];
        
        requiredFields.forEach(field => {
            if (!messageData[field]) {
                throw new Error(`缺少必填字段: ${field}`);
            }
        });
        
        // 验证消息内容
        if (!messageData.body.trim()) {
            throw new Error('消息内容不能为空');
        }
        
        // 验证消息长度
        if (messageData.body.length > 10000) {
            throw new Error('消息内容过长');
        }
    }
    
    preprocessMessage(messageData) {
        // 预处理消息
        return {
            ...messageData,
            timestamp: Date.now(),
            author: this.chatter.store.self,
            processed: true,
            portal: true // 标记为Portal消息
        };
    }
    
    async sendMessage(messageData) {
        // 发送消息
        const thread = this.chatter.state.thread;
        
        if (!thread) {
            throw new Error('没有活动线程');
        }
        
        // 调用线程的消息发送方法
        return await thread.postMessage(messageData);
    }
    
    async postprocessMessage(result) {
        // 消息发送后处理
        
        // 更新跳转计数器
        this.chatter.state.jumpThreadPresent++;
        
        // 加载新消息
        await this.loadNewMessages();
        
        // 滚动到最新消息
        this.scrollToLatest();
        
        // 触发发送完成事件
        this.triggerMessageSent(result);
    }
    
    async loadNewMessages() {
        // 加载新消息
        const requestList = this.chatter.afterPostRequestList;
        await this.chatter.load(this.chatter.state.thread, requestList);
    }
    
    scrollToLatest() {
        // 滚动到最新消息
        setTimeout(() => {
            const threadElement = this.chatter.rootRef.el?.querySelector('.o-mail-Thread');
            if (threadElement) {
                threadElement.scrollTop = threadElement.scrollHeight;
            }
        }, 100);
    }
    
    handleAfterPost() {
        // 发送后回调处理
        this.chatter.state.jumpThreadPresent++;
        
        // 加载新消息以获取其他用户的潜在新消息（由于chatter缺乏自动同步功能）
        this.chatter.load(this.chatter.state.thread, this.chatter.afterPostRequestList);
    }
    
    handleCloseFullComposer() {
        // 关闭全屏撰写器回调
        this.chatter.load(this.chatter.state.thread, this.chatter.onCloseFullComposerRequestList);
    }
    
    queueMessage(action, data) {
        // 消息队列
        this.messageQueue.push({
            action: action,
            data: data,
            timestamp: Date.now(),
            id: this.generateMessageId()
        });
        
        this.processQueue();
    }
    
    async processQueue() {
        // 处理队列
        if (this.processingState.isProcessing || this.messageQueue.length === 0) {
            return;
        }
        
        this.processingState.isProcessing = true;
        
        try {
            while (this.messageQueue.length > 0) {
                const item = this.messageQueue.shift();
                const handler = this.messageHandlers[item.action];
                
                if (handler) {
                    await handler(item.data);
                    this.processingState.processedCount++;
                    this.processingState.lastProcessed = Date.now();
                }
            }
        } finally {
            this.processingState.isProcessing = false;
        }
    }
    
    generateMessageId() {
        // 生成消息ID
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    triggerMessageSent(result) {
        // 触发消息发送事件
        this.chatter.env.bus?.trigger('CHATTER:MESSAGE_SENT', {
            result: result,
            thread: this.chatter.state.thread,
            timestamp: Date.now()
        });
    }
    
    getProcessingStats() {
        // 获取处理统计
        return {
            queueLength: this.messageQueue.length,
            isProcessing: this.processingState.isProcessing,
            processedCount: this.processingState.processedCount,
            lastProcessed: this.processingState.lastProcessed
        };
    }
}
```

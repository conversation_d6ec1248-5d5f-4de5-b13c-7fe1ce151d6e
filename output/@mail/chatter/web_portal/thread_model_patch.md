# Odoo Web Portal 线程模型补丁 (Thread Model Patch) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web_portal/thread_model_patch.js`  
**原始路径**: `/mail/static/src/chatter/web_portal/thread_model_patch.js`  
**模块类型**: Web Portal 补丁 - 线程数据获取优化  
**代码行数**: 25 行  
**依赖关系**: 
- `@mail/core/common/thread_model` - 线程模型
- `@web/core/network/rpc` - RPC 网络通信
- `@web/core/utils/patch` - 补丁工具

## 模块功能

Web Portal 线程模型补丁模块是对核心线程模型的 Portal 环境优化。该模块提供了：
- Portal 环境下的数据获取优化
- 消息获取的特殊处理
- RPC 调用的定制化
- HTML 内容的安全处理
- 数据存储的优化插入

这个补丁专门为 Web Portal 环境提供了优化的数据获取机制，确保在 Portal 环境下的性能和安全性。

## Web Portal 线程模型补丁架构

### 核心补丁结构
```
Web Portal Thread Model Patch
├── 数据获取优化
│   ├── 请求列表处理
│   ├── 消息获取触发
│   ├── RPC 调用定制
│   └── 结果处理优化
├── 消息处理
│   ├── 新消息获取
│   ├── 消息同步机制
│   ├── 增量更新
│   └── 缓存管理
├── 网络通信
│   ├── RPC 端点调用
│   ├── 参数传递
│   ├── 错误处理
│   └── 超时管理
├── 数据存储
│   ├── HTML 安全处理
│   ├── 数据插入优化
│   ├── 存储更新
│   └── 状态同步
└── Portal 特性
    ├── 性能优化
    ├── 安全增强
    ├── 简化处理
    └── 错误恢复
```

### 补丁实现
```javascript
// Web Portal 线程模型补丁
patch(Thread.prototype, {
    async fetchData(requestList) {
        // 检查是否需要获取消息
        if (requestList.includes("messages")) {
            this.fetchNewMessages();
        }
        
        // 执行 RPC 调用获取线程数据
        const result = await rpc("/mail/thread/data", {
            request_list: requestList,
            thread_id: this.id,
            thread_model: this.model,
            ...this.rpcParams,
        });
        
        // 插入数据到存储，启用 HTML 处理
        this.store.insert(result, { html: true });
    }
});
```

## 核心功能详解

### 1. 数据获取管理器
```javascript
// 数据获取管理器
class DataFetchManager {
    constructor(thread) {
        this.thread = thread;
        this.fetchQueue = [];
        this.fetchHistory = [];
        this.fetchStats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0
        };
        this.setupDataFetching();
    }
    
    setupDataFetching() {
        // 设置数据获取
        this.requestTypes = {
            'messages': {
                priority: 1,
                cacheable: true,
                batchable: true,
                handler: this.handleMessagesRequest.bind(this)
            },
            'activities': {
                priority: 2,
                cacheable: true,
                batchable: true,
                handler: this.handleActivitiesRequest.bind(this)
            },
            'followers': {
                priority: 3,
                cacheable: true,
                batchable: true,
                handler: this.handleFollowersRequest.bind(this)
            },
            'attachments': {
                priority: 2,
                cacheable: true,
                batchable: false,
                handler: this.handleAttachmentsRequest.bind(this)
            },
            'thread_info': {
                priority: 4,
                cacheable: false,
                batchable: true,
                handler: this.handleThreadInfoRequest.bind(this)
            }
        };
        
        this.cacheConfig = {
            maxSize: 100,
            maxAge: 5 * 60 * 1000, // 5分钟
            cleanupInterval: 60 * 1000 // 1分钟清理一次
        };
        
        this.cache = new Map();
        this.setupCacheCleanup();
    }
    
    async fetchData(requestList) {
        // 获取数据
        const startTime = Date.now();
        
        try {
            // 验证请求列表
            this.validateRequestList(requestList);
            
            // 优化请求列表
            const optimizedList = this.optimizeRequestList(requestList);
            
            // 检查缓存
            const cacheResult = this.checkCache(optimizedList);
            if (cacheResult.hit) {
                return this.processCacheResult(cacheResult);
            }
            
            // 预处理消息请求
            if (optimizedList.includes("messages")) {
                await this.preprocessMessagesRequest();
            }
            
            // 执行 RPC 调用
            const result = await this.executeRpcCall(optimizedList);
            
            // 后处理结果
            const processedResult = await this.postprocessResult(result, optimizedList);
            
            // 缓存结果
            this.cacheResult(optimizedList, processedResult);
            
            // 插入数据到存储
            this.insertDataToStore(processedResult);
            
            // 更新统计
            this.updateFetchStats(startTime, true);
            
            return processedResult;
            
        } catch (error) {
            console.error('数据获取失败:', error);
            this.updateFetchStats(startTime, false);
            this.handleFetchError(error, requestList);
            throw error;
        }
    }
    
    validateRequestList(requestList) {
        // 验证请求列表
        if (!Array.isArray(requestList)) {
            throw new Error('请求列表必须是数组');
        }
        
        if (requestList.length === 0) {
            throw new Error('请求列表不能为空');
        }
        
        // 检查请求类型是否有效
        const invalidTypes = requestList.filter(type => !this.requestTypes[type]);
        if (invalidTypes.length > 0) {
            console.warn('未知的请求类型:', invalidTypes);
        }
    }
    
    optimizeRequestList(requestList) {
        // 优化请求列表
        const optimized = [...new Set(requestList)]; // 去重
        
        // 按优先级排序
        optimized.sort((a, b) => {
            const priorityA = this.requestTypes[a]?.priority || 999;
            const priorityB = this.requestTypes[b]?.priority || 999;
            return priorityA - priorityB;
        });
        
        // 合并可批处理的请求
        return this.batchRequests(optimized);
    }
    
    batchRequests(requestList) {
        // 批处理请求
        const batchable = requestList.filter(type => 
            this.requestTypes[type]?.batchable !== false
        );
        
        const nonBatchable = requestList.filter(type => 
            this.requestTypes[type]?.batchable === false
        );
        
        // 返回优化后的列表
        return [...batchable, ...nonBatchable];
    }
    
    checkCache(requestList) {
        // 检查缓存
        const cacheKey = this.generateCacheKey(requestList);
        const cached = this.cache.get(cacheKey);
        
        if (cached && this.isCacheValid(cached)) {
            return {
                hit: true,
                data: cached.data,
                timestamp: cached.timestamp
            };
        }
        
        return { hit: false };
    }
    
    generateCacheKey(requestList) {
        // 生成缓存键
        const sortedList = [...requestList].sort();
        return `${this.thread.model}_${this.thread.id}_${sortedList.join('_')}`;
    }
    
    isCacheValid(cached) {
        // 检查缓存是否有效
        const now = Date.now();
        const age = now - cached.timestamp;
        return age < this.cacheConfig.maxAge;
    }
    
    async preprocessMessagesRequest() {
        // 预处理消息请求
        try {
            // 触发新消息获取
            await this.thread.fetchNewMessages();
        } catch (error) {
            console.warn('预处理消息请求失败:', error);
        }
    }
    
    async executeRpcCall(requestList) {
        // 执行 RPC 调用
        const rpcParams = {
            request_list: requestList,
            thread_id: this.thread.id,
            thread_model: this.thread.model,
            ...this.thread.rpcParams
        };
        
        // 添加 Portal 特定参数
        rpcParams.portal_mode = true;
        rpcParams.simplified = true;
        
        return await rpc("/mail/thread/data", rpcParams);
    }
    
    async postprocessResult(result, requestList) {
        // 后处理结果
        const processed = { ...result };
        
        // 处理消息数据
        if (requestList.includes("messages") && processed.messages) {
            processed.messages = this.processMessages(processed.messages);
        }
        
        // 处理附件数据
        if (requestList.includes("attachments") && processed.attachments) {
            processed.attachments = this.processAttachments(processed.attachments);
        }
        
        // 处理活动数据
        if (requestList.includes("activities") && processed.activities) {
            processed.activities = this.processActivities(processed.activities);
        }
        
        // 添加处理时间戳
        processed._processed_at = Date.now();
        
        return processed;
    }
    
    processMessages(messages) {
        // 处理消息数据
        return messages.map(message => {
            // 安全处理 HTML 内容
            if (message.body) {
                message.body = this.sanitizeHtml(message.body);
            }
            
            // 添加 Portal 标记
            message._portal_processed = true;
            
            // 处理附件引用
            if (message.attachment_ids) {
                message.attachment_ids = this.processAttachmentRefs(message.attachment_ids);
            }
            
            return message;
        });
    }
    
    processAttachments(attachments) {
        // 处理附件数据
        return attachments.map(attachment => {
            // 生成安全的下载链接
            if (attachment.id) {
                attachment.download_url = this.generateSecureDownloadUrl(attachment.id);
            }
            
            // 添加预览支持检查
            attachment.preview_supported = this.isPreviewSupported(attachment.mimetype);
            
            return attachment;
        });
    }
    
    processActivities(activities) {
        // 处理活动数据
        return activities.map(activity => {
            // 简化活动数据用于 Portal
            return {
                id: activity.id,
                activity_type_id: activity.activity_type_id,
                summary: activity.summary,
                date_deadline: activity.date_deadline,
                state: activity.state,
                _portal_simplified: true
            };
        });
    }
    
    sanitizeHtml(html) {
        // 安全处理 HTML
        // 这里应该使用专门的 HTML 清理库
        // 简化实现，实际应该更严格
        return html
            .replace(/<script[^>]*>.*?<\/script>/gi, '')
            .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '');
    }
    
    processAttachmentRefs(attachmentIds) {
        // 处理附件引用
        return attachmentIds.filter(id => id && typeof id === 'number');
    }
    
    generateSecureDownloadUrl(attachmentId) {
        // 生成安全下载链接
        return `/web/content/${attachmentId}?download=true&access_token=${this.getAccessToken()}`;
    }
    
    getAccessToken() {
        // 获取访问令牌
        return this.thread.store?.accessToken || '';
    }
    
    isPreviewSupported(mimetype) {
        // 检查是否支持预览
        const supportedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf',
            'text/plain', 'text/html'
        ];
        
        return supportedTypes.includes(mimetype);
    }
    
    cacheResult(requestList, result) {
        // 缓存结果
        const cacheKey = this.generateCacheKey(requestList);
        
        // 检查缓存大小
        if (this.cache.size >= this.cacheConfig.maxSize) {
            this.cleanupCache();
        }
        
        this.cache.set(cacheKey, {
            data: result,
            timestamp: Date.now(),
            requestList: requestList
        });
    }
    
    insertDataToStore(result) {
        // 插入数据到存储
        this.thread.store.insert(result, { 
            html: true,
            portal: true,
            sanitized: true
        });
    }
    
    updateFetchStats(startTime, success) {
        // 更新获取统计
        const responseTime = Date.now() - startTime;
        
        this.fetchStats.totalRequests++;
        
        if (success) {
            this.fetchStats.successfulRequests++;
        } else {
            this.fetchStats.failedRequests++;
        }
        
        // 更新平均响应时间
        const totalResponseTime = this.fetchStats.averageResponseTime * (this.fetchStats.totalRequests - 1) + responseTime;
        this.fetchStats.averageResponseTime = totalResponseTime / this.fetchStats.totalRequests;
        
        // 记录历史
        this.fetchHistory.push({
            timestamp: Date.now(),
            responseTime: responseTime,
            success: success
        });
        
        // 限制历史记录大小
        if (this.fetchHistory.length > 100) {
            this.fetchHistory = this.fetchHistory.slice(-100);
        }
    }
    
    handleFetchError(error, requestList) {
        // 处理获取错误
        console.error('数据获取错误:', {
            error: error.message,
            requestList: requestList,
            threadId: this.thread.id,
            threadModel: this.thread.model
        });
        
        // 触发错误事件
        this.thread.env?.bus?.trigger('THREAD:FETCH_ERROR', {
            error: error,
            requestList: requestList,
            thread: this.thread
        });
    }
    
    setupCacheCleanup() {
        // 设置缓存清理
        setInterval(() => {
            this.cleanupCache();
        }, this.cacheConfig.cleanupInterval);
    }
    
    cleanupCache() {
        // 清理缓存
        const now = Date.now();
        const maxAge = this.cacheConfig.maxAge;
        
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > maxAge) {
                this.cache.delete(key);
            }
        }
    }
    
    getFetchStats() {
        // 获取获取统计
        return {
            ...this.fetchStats,
            cacheSize: this.cache.size,
            historySize: this.fetchHistory.length,
            successRate: this.fetchStats.totalRequests > 0 ? 
                this.fetchStats.successfulRequests / this.fetchStats.totalRequests : 0
        };
    }
}
```

## 使用示例

### 1. 基本数据获取使用
```javascript
// 使用优化的数据获取
class PortalThreadManager extends Component {
    setup() {
        this.thread = this.props.thread;
        this.dataFetchManager = new DataFetchManager(this.thread);
        this.setupDataFetching();
    }
    
    setupDataFetching() {
        // 设置数据获取
        this.fetchConfig = {
            autoRefresh: true,
            refreshInterval: 30000, // 30秒
            batchRequests: true,
            cacheEnabled: true
        };
        
        this.setupAutoRefresh();
    }
    
    async loadThreadData(requestList = ['messages']) {
        // 加载线程数据
        try {
            const result = await this.thread.fetchData(requestList);
            console.log('数据加载成功:', result);
            return result;
        } catch (error) {
            console.error('数据加载失败:', error);
            this.handleLoadError(error);
        }
    }
    
    async refreshMessages() {
        // 刷新消息
        return this.loadThreadData(['messages']);
    }
    
    async loadFullData() {
        // 加载完整数据
        return this.loadThreadData([
            'messages', 
            'activities', 
            'followers', 
            'attachments'
        ]);
    }
    
    setupAutoRefresh() {
        // 设置自动刷新
        if (this.fetchConfig.autoRefresh) {
            this.refreshInterval = setInterval(() => {
                this.refreshMessages();
            }, this.fetchConfig.refreshInterval);
        }
    }
    
    handleLoadError(error) {
        // 处理加载错误
        this.env.services.notification?.add(
            '数据加载失败，请重试',
            { type: 'warning' }
        );
    }
    
    onWillUnmount() {
        // 组件卸载清理
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}
```

### 2. 高级缓存和优化
```javascript
// 高级缓存和优化使用
class AdvancedPortalThread extends Component {
    setup() {
        this.thread = this.props.thread;
        this.setupAdvancedFeatures();
    }
    
    setupAdvancedFeatures() {
        // 设置高级功能
        this.cacheStrategy = {
            aggressive: true,
            preload: true,
            background: true
        };
        
        this.optimizationConfig = {
            batchSize: 20,
            concurrency: 3,
            timeout: 10000
        };
        
        this.setupIntelligentCaching();
        this.setupPreloading();
    }
    
    setupIntelligentCaching() {
        // 设置智能缓存
        this.cacheManager = {
            strategy: 'lru', // Least Recently Used
            maxSize: 50,
            ttl: 300000, // 5分钟
            
            shouldCache: (requestList) => {
                // 决定是否应该缓存
                const cacheable = ['messages', 'activities', 'followers'];
                return requestList.some(req => cacheable.includes(req));
            },
            
            getCacheKey: (requestList) => {
                // 生成缓存键
                return `${this.thread.model}_${this.thread.id}_${requestList.sort().join('_')}`;
            }
        };
    }
    
    setupPreloading() {
        // 设置预加载
        if (this.cacheStrategy.preload) {
            // 预加载常用数据
            setTimeout(() => {
                this.preloadCommonData();
            }, 1000);
        }
    }
    
    async preloadCommonData() {
        // 预加载常用数据
        const commonRequests = [
            ['messages'],
            ['activities'],
            ['followers']
        ];
        
        for (const requestList of commonRequests) {
            try {
                await this.thread.fetchData(requestList);
            } catch (error) {
                console.warn('预加载失败:', requestList, error);
            }
        }
    }
    
    async fetchWithRetry(requestList, maxRetries = 3) {
        // 带重试的获取
        let lastError;
        
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await this.thread.fetchData(requestList);
            } catch (error) {
                lastError = error;
                
                // 指数退避
                const delay = Math.pow(2, i) * 1000;
                await this.delay(delay);
            }
        }
        
        throw lastError;
    }
    
    delay(ms) {
        // 延迟函数
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    async fetchInBatches(requestList, batchSize = 2) {
        // 分批获取
        const batches = [];
        for (let i = 0; i < requestList.length; i += batchSize) {
            batches.push(requestList.slice(i, i + batchSize));
        }
        
        const results = [];
        for (const batch of batches) {
            try {
                const result = await this.thread.fetchData(batch);
                results.push(result);
            } catch (error) {
                console.error('批次获取失败:', batch, error);
            }
        }
        
        return results;
    }
    
    getPerformanceMetrics() {
        // 获取性能指标
        const fetchManager = this.thread.dataFetchManager;
        
        return {
            fetchStats: fetchManager?.getFetchStats(),
            cacheHitRate: this.calculateCacheHitRate(),
            averageLoadTime: this.calculateAverageLoadTime(),
            errorRate: this.calculateErrorRate()
        };
    }
    
    calculateCacheHitRate() {
        // 计算缓存命中率
        // 实现缓存命中率计算逻辑
        return 0.85; // 示例值
    }
    
    calculateAverageLoadTime() {
        // 计算平均加载时间
        // 实现平均加载时间计算逻辑
        return 250; // 示例值（毫秒）
    }
    
    calculateErrorRate() {
        // 计算错误率
        // 实现错误率计算逻辑
        return 0.02; // 示例值（2%）
    }
}
```

## Portal 线程模型补丁特性

### 1. 数据获取优化
Portal 线程模型补丁提供了专门的数据获取优化：
- **消息预处理**: 自动触发新消息获取
- **RPC 优化**: 定制化的 RPC 调用参数
- **HTML 安全处理**: 启用 HTML 内容的安全处理
- **批量请求**: 支持批量数据请求

### 2. Portal 特定功能
```javascript
// Portal 特定配置
const portalConfig = {
    rpcEndpoint: "/mail/thread/data",
    htmlProcessing: true,
    securityEnhanced: true,
    simplifiedData: true,
    cacheEnabled: true
};
```

### 3. 性能优化策略
- **智能缓存**: 基于请求类型的智能缓存策略
- **批量处理**: 合并多个请求减少网络开销
- **预加载**: 预加载常用数据提升响应速度
- **错误恢复**: 完善的错误处理和重试机制

### 4. 安全性增强
- **HTML 清理**: 自动清理不安全的 HTML 内容
- **访问控制**: 基于访问令牌的安全下载
- **数据过滤**: 过滤敏感数据用于 Portal 环境
- **输入验证**: 严格的输入参数验证

## 最佳实践

### 1. 数据获取
```javascript
// ✅ 推荐：优化的数据获取
async fetchData(requestList) {
    if (requestList.includes("messages")) {
        this.fetchNewMessages();
    }
    
    const result = await rpc("/mail/thread/data", {
        request_list: requestList,
        thread_id: this.id,
        thread_model: this.model,
        ...this.rpcParams,
    });
    
    this.store.insert(result, { html: true });
}
```

### 2. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.fetchData(requestList);
} catch (error) {
    console.error('数据获取失败:', error);
    this.handleFetchError(error);
}
```

### 3. 缓存使用
```javascript
// ✅ 推荐：智能缓存策略
const cacheKey = this.generateCacheKey(requestList);
const cached = this.cache.get(cacheKey);

if (cached && this.isCacheValid(cached)) {
    return cached.data;
}
```

### 4. 性能监控
```javascript
// ✅ 推荐：性能监控
const startTime = Date.now();
try {
    const result = await this.fetchData(requestList);
    this.updatePerformanceMetrics(startTime, true);
    return result;
} catch (error) {
    this.updatePerformanceMetrics(startTime, false);
    throw error;
}
```

## 总结

Odoo Web Portal 线程模型补丁提供了专为 Portal 环境优化的数据获取功能：

**核心优势**:
- **性能优化**: 专门为 Portal 环境优化的数据获取机制
- **安全增强**: HTML 内容的安全处理和访问控制
- **智能缓存**: 基于使用模式的智能缓存策略
- **错误恢复**: 完善的错误处理和重试机制
- **简化处理**: 简化的数据结构适合 Portal 使用

**适用场景**:
- Web Portal 数据获取
- 客户自助服务平台
- 移动端应用优化
- 性能敏感的应用
- 安全要求较高的环境

**设计优势**:
- 补丁模式扩展
- 非侵入式优化
- 向后兼容性
- 模块化设计

这个线程模型补丁为 Odoo Web Portal 提供了高效、安全的数据获取能力，在保持功能完整性的同时，显著提升了 Portal 环境下的性能和用户体验。

# Odoo 邮件撰写表单 (Mail Composer Form) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/mail_composer_form.js`  
**原始路径**: `/mail/static/src/chatter/web/mail_composer_form.js`  
**模块类型**: 核心组件 - 邮件撰写表单视图  
**代码行数**: 43 行  
**依赖关系**: 
- `@web/views/form/form_view` - 表单视图
- `@web/core/registry` - 注册表系统
- `@odoo/owl` - OWL 框架

## 模块功能

邮件撰写表单模块是 Odoo Web 客户端的专用邮件撰写界面。该模块提供了：
- 专用的邮件撰写表单视图
- 自动焦点管理
- 编辑器集成
- 对话框数据管理
- 邮件撰写控制器
- 邮件撰写渲染器

这个模块通过扩展标准表单视图，为邮件撰写提供了专门优化的用户界面和交互体验。

## 邮件撰写表单架构

### 核心组件结构
```
Mail Composer Form
├── 控制器 (Controller)
│   ├── 表单控制器扩展
│   ├── 对话框数据管理
│   ├── 模型设置
│   └── 环境配置
├── 渲染器 (Renderer)
│   ├── 表单渲染器扩展
│   ├── 自动焦点管理
│   ├── 编辑器集成
│   └── 用户体验优化
├── 视图注册
│   ├── 视图类型注册
│   ├── 组件绑定
│   ├── 配置继承
│   └── 注册表集成
└── 功能特性
    ├── 富文本编辑
    ├── 附件管理
    ├── 收件人管理
    └── 发送选项
```

### 组件配置
```javascript
// 邮件撰写表单控制器
class MailComposerFormController extends formView.Controller {
    setup() {
        super.setup();
        // 设置对话框数据模型
        toRaw(this.env.dialogData).model = "mail.compose.message";
    }
}

// 邮件撰写表单渲染器
class MailComposerFormRenderer extends formView.Renderer {
    setup() {
        super.setup();
        // 自动焦点管理
        this.root = useRef("compiled_view_root");
        useEffect((isInEdition, root) => {
            if (root && root.el && isInEdition) {
                const element = root.el.querySelector(".note-editable[contenteditable]");
                if (element) {
                    element.focus();
                    document.dispatchEvent(new Event("selectionchange", {}));
                }
            }
        }, () => [
            this.props.record.isInEdition,
            this.root,
            this.props.record.resId
        ]);
    }
}

// 视图注册
registry.category("views").add("mail_composer_form", {
    ...formView,
    Controller: MailComposerFormController,
    Renderer: MailComposerFormRenderer,
});
```

## 核心功能详解

### 1. 邮件撰写控制器
```javascript
// 扩展的邮件撰写控制器
class ExtendedMailComposerController extends MailComposerFormController {
    setup() {
        super.setup();
        
        // 初始化邮件撰写服务
        this.initializeComposerServices();
        
        // 设置邮件撰写配置
        this.setupComposerConfiguration();
        
        // 绑定事件监听器
        this.setupEventListeners();
    }
    
    initializeComposerServices() {
        // 初始化撰写服务
        this.mailService = this.env.services.mail;
        this.attachmentService = this.env.services['mail.attachment'];
        this.templateService = this.env.services['mail.template'];
        this.recipientService = this.env.services['mail.recipient'];
        
        console.log('邮件撰写服务已初始化');
    }
    
    setupComposerConfiguration() {
        // 设置撰写配置
        this.composerConfig = {
            model: "mail.compose.message",
            autoSave: true,
            autoSaveInterval: 30000, // 30秒
            enableTemplates: true,
            enableScheduling: true,
            enableAttachments: true,
            maxAttachmentSize: 25 * 1024 * 1024, // 25MB
            allowedFileTypes: [
                'image/*', 'application/pdf', 'text/*',
                'application/msword', 'application/vnd.openxmlformats-officedocument.*'
            ]
        };
        
        // 应用配置到对话框数据
        Object.assign(toRaw(this.env.dialogData), this.composerConfig);
    }
    
    setupEventListeners() {
        // 设置事件监听器
        this.env.bus.addEventListener('MAIL:TEMPLATE_SELECTED', this.onTemplateSelected.bind(this));
        this.env.bus.addEventListener('MAIL:RECIPIENT_ADDED', this.onRecipientAdded.bind(this));
        this.env.bus.addEventListener('MAIL:ATTACHMENT_UPLOADED', this.onAttachmentUploaded.bind(this));
        this.env.bus.addEventListener('MAIL:SEND_SCHEDULED', this.onSendScheduled.bind(this));
    }
    
    onTemplateSelected(event) {
        // 模板选择处理
        const template = event.detail.template;
        console.log('模板已选择:', template.name);
        
        // 应用模板内容
        this.applyTemplate(template);
    }
    
    async applyTemplate(template) {
        // 应用模板
        try {
            // 获取模板内容
            const templateData = await this.templateService.getTemplate(template.id);
            
            // 更新表单字段
            const updates = {
                subject: templateData.subject,
                body: templateData.body_html,
                partner_ids: templateData.partner_ids || []
            };
            
            // 应用更新
            await this.model.root.update(updates);
            
            console.log('模板应用成功');
            
        } catch (error) {
            console.error('应用模板失败:', error);
            this.env.services.notification.add('应用模板失败', { type: 'danger' });
        }
    }
    
    onRecipientAdded(event) {
        // 收件人添加处理
        const recipient = event.detail.recipient;
        console.log('收件人已添加:', recipient.name);
        
        // 验证收件人
        this.validateRecipient(recipient);
    }
    
    validateRecipient(recipient) {
        // 验证收件人
        if (!recipient.email) {
            this.env.services.notification.add(
                `收件人 ${recipient.name} 没有邮箱地址`,
                { type: 'warning' }
            );
        }
        
        // 检查重复收件人
        const currentRecipients = this.model.root.data.partner_ids || [];
        const isDuplicate = currentRecipients.some(id => id === recipient.id);
        
        if (isDuplicate) {
            this.env.services.notification.add(
                `收件人 ${recipient.name} 已存在`,
                { type: 'info' }
            );
        }
    }
    
    onAttachmentUploaded(event) {
        // 附件上传处理
        const attachment = event.detail.attachment;
        console.log('附件已上传:', attachment.name);
        
        // 验证附件
        this.validateAttachment(attachment);
    }
    
    validateAttachment(attachment) {
        // 验证附件
        
        // 检查文件大小
        if (attachment.file_size > this.composerConfig.maxAttachmentSize) {
            this.env.services.notification.add(
                `文件 ${attachment.name} 超过大小限制`,
                { type: 'danger' }
            );
            return false;
        }
        
        // 检查文件类型
        const isAllowed = this.composerConfig.allowedFileTypes.some(type => {
            if (type.endsWith('*')) {
                return attachment.mimetype.startsWith(type.slice(0, -1));
            }
            return attachment.mimetype === type;
        });
        
        if (!isAllowed) {
            this.env.services.notification.add(
                `不支持的文件类型: ${attachment.mimetype}`,
                { type: 'danger' }
            );
            return false;
        }
        
        return true;
    }
    
    onSendScheduled(event) {
        // 计划发送处理
        const scheduleData = event.detail;
        console.log('邮件已计划发送:', scheduleData.scheduled_date);
        
        // 保存计划发送信息
        this.saveScheduledSend(scheduleData);
    }
    
    async saveScheduledSend(scheduleData) {
        // 保存计划发送
        try {
            const updates = {
                scheduled_date: scheduleData.scheduled_date,
                is_scheduled: true
            };
            
            await this.model.root.update(updates);
            
            this.env.services.notification.add(
                '邮件已计划发送',
                { type: 'success' }
            );
            
        } catch (error) {
            console.error('保存计划发送失败:', error);
            this.env.services.notification.add(
                '保存计划发送失败',
                { type: 'danger' }
            );
        }
    }
    
    async saveRecord() {
        // 保存记录
        try {
            // 验证必填字段
            this.validateRequiredFields();
            
            // 执行保存
            const result = await super.saveRecord();
            
            // 自动保存成功通知
            if (this.composerConfig.autoSave) {
                console.log('邮件草稿已自动保存');
            }
            
            return result;
            
        } catch (error) {
            console.error('保存邮件失败:', error);
            throw error;
        }
    }
    
    validateRequiredFields() {
        // 验证必填字段
        const data = this.model.root.data;
        
        // 检查收件人
        if (!data.partner_ids || data.partner_ids.length === 0) {
            throw new Error('请至少选择一个收件人');
        }
        
        // 检查主题
        if (!data.subject || data.subject.trim().length === 0) {
            throw new Error('请输入邮件主题');
        }
        
        // 检查正文
        if (!data.body || data.body.trim().length === 0) {
            throw new Error('请输入邮件正文');
        }
    }
    
    async sendMail() {
        // 发送邮件
        try {
            // 最终验证
            this.validateRequiredFields();
            
            // 保存记录
            await this.saveRecord();
            
            // 发送邮件
            const result = await this.mailService.sendMail(this.model.root.resId);
            
            this.env.services.notification.add(
                '邮件发送成功',
                { type: 'success' }
            );
            
            // 关闭对话框
            this.env.dialogData.close();
            
            return result;
            
        } catch (error) {
            console.error('发送邮件失败:', error);
            this.env.services.notification.add(
                `发送邮件失败: ${error.message}`,
                { type: 'danger' }
            );
            throw error;
        }
    }
}
```

### 2. 邮件撰写渲染器
```javascript
// 扩展的邮件撰写渲染器
class ExtendedMailComposerRenderer extends MailComposerFormRenderer {
    setup() {
        super.setup();
        
        // 设置编辑器管理
        this.setupEditorManagement();
        
        // 设置用户体验优化
        this.setupUXOptimizations();
        
        // 设置快捷键支持
        this.setupKeyboardShortcuts();
    }
    
    setupEditorManagement() {
        // 设置编辑器管理
        this.editorConfig = {
            autoFocus: true,
            spellCheck: true,
            autoSave: true,
            placeholder: '请输入邮件内容...',
            toolbar: [
                'bold', 'italic', 'underline', 'strikethrough',
                'fontSize', 'fontColor', 'backgroundColor',
                'bulletList', 'numberedList',
                'link', 'image', 'table',
                'undo', 'redo'
            ]
        };
    }
    
    setupUXOptimizations() {
        // 设置用户体验优化
        
        // 自动调整高度
        this.setupAutoResize();
        
        // 拖拽支持
        this.setupDragAndDrop();
        
        // 粘贴优化
        this.setupPasteOptimization();
    }
    
    setupAutoResize() {
        // 设置自动调整大小
        useEffect(() => {
            const editor = this.root.el?.querySelector('.note-editable');
            if (editor) {
                this.adjustEditorHeight(editor);
            }
        }, () => [this.props.record.data.body]);
    }
    
    adjustEditorHeight(editor) {
        // 调整编辑器高度
        const minHeight = 150;
        const maxHeight = 400;
        
        editor.style.height = 'auto';
        const scrollHeight = editor.scrollHeight;
        
        if (scrollHeight < minHeight) {
            editor.style.height = minHeight + 'px';
        } else if (scrollHeight > maxHeight) {
            editor.style.height = maxHeight + 'px';
            editor.style.overflowY = 'auto';
        } else {
            editor.style.height = scrollHeight + 'px';
            editor.style.overflowY = 'hidden';
        }
    }
    
    setupDragAndDrop() {
        // 设置拖拽支持
        useEffect(() => {
            const container = this.root.el;
            if (!container) return;
            
            const handleDragOver = (e) => {
                e.preventDefault();
                container.classList.add('drag-over');
            };
            
            const handleDragLeave = (e) => {
                e.preventDefault();
                container.classList.remove('drag-over');
            };
            
            const handleDrop = (e) => {
                e.preventDefault();
                container.classList.remove('drag-over');
                
                const files = Array.from(e.dataTransfer.files);
                if (files.length > 0) {
                    this.handleFilesDrop(files);
                }
            };
            
            container.addEventListener('dragover', handleDragOver);
            container.addEventListener('dragleave', handleDragLeave);
            container.addEventListener('drop', handleDrop);
            
            return () => {
                container.removeEventListener('dragover', handleDragOver);
                container.removeEventListener('dragleave', handleDragLeave);
                container.removeEventListener('drop', handleDrop);
            };
        });
    }
    
    handleFilesDrop(files) {
        // 处理文件拖拽
        console.log('拖拽文件:', files.map(f => f.name));
        
        // 触发文件上传
        this.env.bus.trigger('MAIL:FILES_DROPPED', {
            files: files,
            target: 'composer'
        });
    }
    
    setupPasteOptimization() {
        // 设置粘贴优化
        useEffect(() => {
            const editor = this.root.el?.querySelector('.note-editable');
            if (!editor) return;
            
            const handlePaste = (e) => {
                // 处理图片粘贴
                const items = Array.from(e.clipboardData.items);
                const imageItems = items.filter(item => item.type.startsWith('image/'));
                
                if (imageItems.length > 0) {
                    e.preventDefault();
                    this.handleImagePaste(imageItems);
                }
            };
            
            editor.addEventListener('paste', handlePaste);
            
            return () => {
                editor.removeEventListener('paste', handlePaste);
            };
        });
    }
    
    handleImagePaste(imageItems) {
        // 处理图片粘贴
        imageItems.forEach(item => {
            const file = item.getAsFile();
            if (file) {
                console.log('粘贴图片:', file.name);
                
                // 触发图片上传
                this.env.bus.trigger('MAIL:IMAGE_PASTED', {
                    file: file,
                    target: 'composer'
                });
            }
        });
    }
    
    setupKeyboardShortcuts() {
        // 设置快捷键
        useEffect(() => {
            const container = this.root.el;
            if (!container) return;
            
            const handleKeyDown = (e) => {
                // Ctrl+Enter 发送邮件
                if (e.ctrlKey && e.key === 'Enter') {
                    e.preventDefault();
                    this.env.bus.trigger('MAIL:SEND_SHORTCUT');
                }
                
                // Ctrl+S 保存草稿
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    this.env.bus.trigger('MAIL:SAVE_SHORTCUT');
                }
                
                // Escape 关闭
                if (e.key === 'Escape') {
                    this.env.bus.trigger('MAIL:CLOSE_SHORTCUT');
                }
            };
            
            container.addEventListener('keydown', handleKeyDown);
            
            return () => {
                container.removeEventListener('keydown', handleKeyDown);
            };
        });
    }
}
```

## 使用示例

### 1. 基本邮件撰写表单使用
```javascript
// 使用邮件撰写表单
class MailComposerDialog extends Component {
    static template = xml`
        <Dialog title="撰写邮件" size="'lg'">
            <t t-component="mailComposerForm" t-props="formProps"/>
            <t t-set-slot="footer">
                <button class="btn btn-primary" t-on-click="sendMail">发送</button>
                <button class="btn btn-secondary" t-on-click="saveDraft">保存草稿</button>
                <button class="btn btn-light" t-on-click="close">取消</button>
            </t>
        </Dialog>
    `;
    
    setup() {
        this.mailComposerForm = registry.category("views").get("mail_composer_form");
        this.formProps = useState({
            resModel: "mail.compose.message",
            resId: false,
            context: {
                default_composition_mode: 'comment',
                default_model: this.props.threadModel,
                default_res_id: this.props.threadId,
                default_partner_ids: this.props.recipientIds || []
            }
        });
    }
    
    async sendMail() {
        try {
            await this.env.bus.trigger('MAIL:SEND_MAIL');
            this.props.close();
        } catch (error) {
            console.error('发送邮件失败:', error);
        }
    }
    
    async saveDraft() {
        try {
            await this.env.bus.trigger('MAIL:SAVE_DRAFT');
            this.env.services.notification.add('草稿已保存', { type: 'success' });
        } catch (error) {
            console.error('保存草稿失败:', error);
        }
    }
    
    close() {
        this.props.close();
    }
}
```

### 2. 自定义邮件撰写配置
```javascript
// 自定义邮件撰写配置
class CustomMailComposer extends Component {
    setup() {
        this.composerConfig = {
            // 编辑器配置
            editor: {
                autoFocus: true,
                spellCheck: true,
                placeholder: '请输入您的消息...',
                minHeight: 200,
                maxHeight: 500
            },
            
            // 附件配置
            attachments: {
                enabled: true,
                maxSize: 25 * 1024 * 1024,
                allowedTypes: ['image/*', 'application/pdf', 'text/*'],
                dragAndDrop: true
            },
            
            // 模板配置
            templates: {
                enabled: true,
                autoLoad: true,
                categories: ['sales', 'support', 'marketing']
            },
            
            // 计划发送配置
            scheduling: {
                enabled: true,
                minDelay: 5 * 60 * 1000, // 5分钟
                maxDelay: 30 * 24 * 60 * 60 * 1000 // 30天
            }
        };
        
        this.applyConfiguration();
    }
    
    applyConfiguration() {
        // 应用配置
        this.env.bus.trigger('MAIL:COMPOSER_CONFIG', this.composerConfig);
    }
}
```

## 邮件撰写表单特性

### 1. 自动焦点管理
邮件撰写表单提供智能的焦点管理：
- **自动焦点**: 打开时自动聚焦到编辑器
- **焦点保持**: 在编辑过程中保持焦点
- **选择事件**: 触发选择变化事件
- **用户体验**: 提升用户输入体验

### 2. 富文本编辑支持
```javascript
// 富文本编辑器配置
const editorConfig = {
    toolbar: [
        'bold', 'italic', 'underline', 'strikethrough',
        'fontSize', 'fontColor', 'backgroundColor',
        'bulletList', 'numberedList',
        'link', 'image', 'table',
        'undo', 'redo'
    ],
    features: {
        autoSave: true,
        spellCheck: true,
        dragAndDrop: true,
        pasteOptimization: true
    }
};
```

### 3. 对话框集成
- **模型设置**: 自动设置邮件撰写模型
- **数据管理**: 管理对话框数据状态
- **环境配置**: 配置撰写环境
- **生命周期**: 处理对话框生命周期

### 4. 用户体验优化
- **快捷键支持**: Ctrl+Enter 发送，Ctrl+S 保存
- **拖拽上传**: 支持文件拖拽上传
- **自动调整**: 编辑器高度自动调整
- **粘贴优化**: 优化图片和内容粘贴

## 最佳实践

### 1. 表单配置
```javascript
// ✅ 推荐：正确的表单配置
const composerProps = {
    resModel: "mail.compose.message",
    context: {
        default_composition_mode: 'comment',
        default_model: threadModel,
        default_res_id: threadId
    }
};
```

### 2. 事件处理
```javascript
// ✅ 推荐：正确的事件监听
this.env.bus.addEventListener('MAIL:SEND_MAIL', this.onSendMail);

onWillUnmount(() => {
    this.env.bus.removeEventListener('MAIL:SEND_MAIL', this.onSendMail);
});
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.sendMail();
} catch (error) {
    this.env.services.notification.add(
        `发送失败: ${error.message}`,
        { type: 'danger' }
    );
}
```

### 4. 数据验证
```javascript
// ✅ 推荐：完善的数据验证
validateRequiredFields() {
    if (!this.model.root.data.partner_ids?.length) {
        throw new Error('请选择收件人');
    }
    if (!this.model.root.data.subject?.trim()) {
        throw new Error('请输入主题');
    }
}
```

## 总结

Odoo 邮件撰写表单模块提供了专业的邮件撰写界面：

**核心优势**:
- **专用界面**: 专门为邮件撰写优化的表单界面
- **自动焦点**: 智能的焦点管理和用户体验
- **富文本编辑**: 强大的富文本编辑功能
- **用户友好**: 拖拽、快捷键等用户友好特性
- **高度集成**: 与 Odoo 邮件系统深度集成

**适用场景**:
- 邮件撰写和发送
- 邮件模板应用
- 计划邮件发送
- 附件管理
- 收件人管理

**设计优势**:
- 继承扩展模式
- 组件化架构
- 事件驱动设计
- 用户体验优先

这个邮件撰写表单为 Odoo Web 客户端提供了专业、高效的邮件撰写体验，是邮件系统的重要用户界面组件。

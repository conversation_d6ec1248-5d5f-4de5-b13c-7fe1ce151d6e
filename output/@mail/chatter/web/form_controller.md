# Odoo 表单控制器 (Form Controller) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/form_controller.js`
**原始路径**: `/mail/static/src/chatter/web/form_controller.js`
**模块类型**: 核心控制器 - 表单控制器扩展
**代码行数**: 56 行
**依赖关系**:
- `@web/views/form/form_controller` - 表单控制器
- `@web/core/utils/patch` - 补丁工具
- `@web/core/utils/hooks` - 核心钩子
- `@web/core/orm_service` - ORM 服务
- `@odoo/owl` - OWL 框架

## 模块功能

表单控制器模块是 Odoo Web 客户端表单视图控制器的邮件功能扩展。该模块提供了：
- Chatter 环境配置
- 邮件存储服务集成
- 线程数据重载机制
- 邮件撰写记录处理
- 合作伙伴 ID 管理
- 表单生命周期集成

这个模块通过补丁方式扩展了标准的表单控制器，为邮件相关功能提供了必要的控制逻辑和数据管理支持。

## 表单控制器架构

### 核心组件结构
```
Form Controller Extension
├── 环境配置
│   ├── Chatter 子环境设置
│   ├── 邮件存储服务集成
│   ├── 数据获取配置
│   └── 消息获取配置
├── 生命周期管理
│   ├── 根记录加载处理
│   ├── 线程重载机制
│   ├── 记录保存处理
│   └── 数据同步控制
├── 邮件处理
│   ├── 撰写消息处理
│   ├── 合作伙伴提取
│   ├── HTML 解析
│   └── 关系字段更新
└── 补丁实现
    ├── setup 方法扩展
    ├── onWillLoadRoot 扩展
    ├── onWillSaveRecord 扩展
    └── 事件总线集成
```

### 控制器配置
```javascript
// 补丁实现
patch(FormController.prototype, {
    setup() {
        super.setup(...arguments);

        // 邮件存储服务集成
        if (this.env.services["mail.store"]) {
            this.mailStore = useService("mail.store");
        }

        // Chatter 子环境配置
        useSubEnv({
            chatter: {
                fetchData: true,
                fetchMessages: true,
            },
        });
    }
});
```

## 核心功能详解

### 1. 环境配置和初始化
```javascript
// 扩展的表单控制器
class ExtendedFormController extends FormController {
    setup() {
        super.setup(...arguments);

        // 初始化邮件相关服务
        this.initializeMailServices();

        // 配置 Chatter 环境
        this.setupChatterEnvironment();

        // 设置事件监听
        this.setupEventListeners();
    }

    initializeMailServices() {
        // 初始化邮件服务
        if (this.env.services["mail.store"]) {
            this.mailStore = useService("mail.store");
            console.log('邮件存储服务已初始化');
        }

        // 初始化其他邮件相关服务
        if (this.env.services["mail.thread"]) {
            this.mailThread = useService("mail.thread");
        }

        if (this.env.services["mail.activity"]) {
            this.mailActivity = useService("mail.activity");
        }

        if (this.env.services["mail.attachment"]) {
            this.mailAttachment = useService("mail.attachment");
        }
    }

    setupChatterEnvironment() {
        // 设置 Chatter 环境
        const chatterConfig = {
            fetchData: true,
            fetchMessages: true,
            fetchActivities: true,
            fetchAttachments: true,
            fetchFollowers: true,
            autoReload: true,
            realTimeUpdates: true
        };

        useSubEnv({
            chatter: chatterConfig,
            mailController: this
        });

        console.log('Chatter 环境已配置:', chatterConfig);
    }

    setupEventListeners() {
        // 设置事件监听器

        // 监听邮件相关事件
        this.env.bus.addEventListener('MAIL:MESSAGE_POSTED', this.onMessagePosted.bind(this));
        this.env.bus.addEventListener('MAIL:ACTIVITY_UPDATED', this.onActivityUpdated.bind(this));
        this.env.bus.addEventListener('MAIL:FOLLOWER_CHANGED', this.onFollowerChanged.bind(this));
        this.env.bus.addEventListener('MAIL:ATTACHMENT_UPLOADED', this.onAttachmentUploaded.bind(this));

        // 监听表单事件
        this.env.bus.addEventListener('FORM:RECORD_SAVED', this.onRecordSaved.bind(this));
        this.env.bus.addEventListener('FORM:RECORD_DELETED', this.onRecordDeleted.bind(this));
    }

    onMessagePosted(event) {
        // 消息发布事件处理
        console.log('消息已发布:', event.detail);

        // 更新相关数据
        this.updateChatterData(['messages', 'activities']);

        // 触发表单重载（如果需要）
        if (this.shouldReloadOnMessagePost()) {
            this.reloadRecord();
        }
    }

    onActivityUpdated(event) {
        // 活动更新事件处理
        console.log('活动已更新:', event.detail);

        // 更新活动数据
        this.updateChatterData(['activities', 'messages']);
    }

    onFollowerChanged(event) {
        // 关注者变化事件处理
        console.log('关注者已变化:', event.detail);

        // 更新关注者数据
        this.updateChatterData(['followers', 'suggestedRecipients']);

        // 触发表单重载（如果需要）
        if (this.shouldReloadOnFollowerChange()) {
            this.reloadRecord();
        }
    }

    onAttachmentUploaded(event) {
        // 附件上传事件处理
        console.log('附件已上传:', event.detail);

        // 更新附件数据
        this.updateChatterData(['attachments']);

        // 触发表单重载（如果需要）
        if (this.shouldReloadOnAttachmentChange()) {
            this.reloadRecord();
        }
    }

    onRecordSaved(event) {
        // 记录保存事件处理
        console.log('记录已保存:', event.detail);

        // 更新 Chatter 数据
        this.updateChatterData(['messages', 'activities', 'followers']);
    }

    onRecordDeleted(event) {
        // 记录删除事件处理
        console.log('记录已删除:', event.detail);

        // 清理 Chatter 数据
        this.clearChatterData();
    }

    updateChatterData(dataTypes) {
        // 更新 Chatter 数据
        if (!this.model.root?.resId) {
            return;
        }

        const { resModel, resId } = this.model.root;

        dataTypes.forEach(dataType => {
            this.env.bus.trigger('MAIL:RELOAD-DATA', {
                model: resModel,
                id: resId,
                dataType: dataType
            });
        });
    }

    clearChatterData() {
        // 清理 Chatter 数据
        this.env.bus.trigger('MAIL:CLEAR-DATA');
    }

    shouldReloadOnMessagePost() {
        // 判断是否在消息发布后重载
        return this.props.archInfo?.chatterConfig?.reload_on_post || false;
    }

    shouldReloadOnFollowerChange() {
        // 判断是否在关注者变化后重载
        return this.props.archInfo?.chatterConfig?.reload_on_follower || false;
    }

    shouldReloadOnAttachmentChange() {
        // 判断是否在附件变化后重载
        return this.props.archInfo?.chatterConfig?.reload_on_attachment || false;
    }

    async reloadRecord() {
        // 重载记录
        try {
            await this.model.root.load();
            console.log('记录重载成功');
        } catch (error) {
            console.error('记录重载失败:', error);
        }
    }
}
```

### 2. 线程数据管理
```javascript
// 线程数据管理器
class ThreadDataManager {
    constructor(formController) {
        this.formController = formController;
        this.currentThread = null;
        this.threadCache = new Map();
        this.setupManager();
    }

    setupManager() {
        // 设置管理器
        this.setupCacheManagement();
        this.setupDataSynchronization();
    }

    setupCacheManagement() {
        // 设置缓存管理
        this.cacheConfig = {
            maxSize: 50,
            ttl: 5 * 60 * 1000, // 5分钟
            autoCleanup: true
        };

        if (this.cacheConfig.autoCleanup) {
            setInterval(() => {
                this.cleanupExpiredCache();
            }, 60000); // 每分钟清理一次
        }
    }

    setupDataSynchronization() {
        // 设置数据同步
        this.syncConfig = {
            batchSize: 10,
            debounceTime: 300,
            retryAttempts: 3
        };
    }

    onWillLoadRoot(nextConfiguration) {
        // 根记录加载前处理
        this.formController.env.chatter.fetchData = true;
        this.formController.env.chatter.fetchMessages = true;

        const isSameThread = this.isSameThread(nextConfiguration);

        if (isSameThread) {
            // 相同线程，触发重载
            this.reloadCurrentThread();
        } else {
            // 不同线程，切换线程
            this.switchThread(nextConfiguration);
        }
    }

    isSameThread(nextConfiguration) {
        // 判断是否为相同线程
        const currentRoot = this.formController.model.root;

        return currentRoot?.resId === nextConfiguration.resId &&
               currentRoot?.resModel === nextConfiguration.resModel;
    }

    reloadCurrentThread() {
        // 重载当前线程
        const { resModel, resId } = this.formController.model.root;

        console.log(`重载线程: ${resModel}:${resId}`);

        // 触发线程重载事件
        this.formController.env.bus.trigger("MAIL:RELOAD-THREAD", {
            model: resModel,
            id: resId
        });

        // 更新缓存
        this.updateThreadCache(resModel, resId);
    }

    switchThread(nextConfiguration) {
        // 切换线程
        const { resModel, resId } = nextConfiguration;

        console.log(`切换线程: ${resModel}:${resId}`);

        // 保存当前线程状态
        if (this.currentThread) {
            this.saveThreadState(this.currentThread);
        }

        // 加载新线程
        this.loadThread(resModel, resId);

        // 更新当前线程
        this.currentThread = { resModel, resId };
    }

    loadThread(resModel, resId) {
        // 加载线程
        const threadKey = `${resModel}:${resId}`;

        // 检查缓存
        if (this.threadCache.has(threadKey)) {
            const cachedThread = this.threadCache.get(threadKey);
            if (!this.isCacheExpired(cachedThread)) {
                console.log('从缓存加载线程:', threadKey);
                this.restoreThreadState(cachedThread);
                return;
            }
        }

        // 从服务器加载
        console.log('从服务器加载线程:', threadKey);
        this.loadThreadFromServer(resModel, resId);
    }

    async loadThreadFromServer(resModel, resId) {
        // 从服务器加载线程
        try {
            const threadData = await this.formController.mailStore.loadThread(resModel, resId);

            // 缓存线程数据
            this.cacheThreadData(resModel, resId, threadData);

            // 应用线程数据
            this.applyThreadData(threadData);

        } catch (error) {
            console.error('加载线程失败:', error);
        }
    }

    saveThreadState(thread) {
        // 保存线程状态
        const threadKey = `${thread.resModel}:${thread.resId}`;
        const state = {
            scrollPosition: this.getScrollPosition(),
            selectedMessage: this.getSelectedMessage(),
            composerState: this.getComposerState(),
            timestamp: Date.now()
        };

        this.threadCache.set(threadKey, {
            ...this.threadCache.get(threadKey),
            state: state
        });
    }

    restoreThreadState(cachedThread) {
        // 恢复线程状态
        if (cachedThread.state) {
            this.setScrollPosition(cachedThread.state.scrollPosition);
            this.setSelectedMessage(cachedThread.state.selectedMessage);
            this.setComposerState(cachedThread.state.composerState);
        }
    }

    cacheThreadData(resModel, resId, threadData) {
        // 缓存线程数据
        const threadKey = `${resModel}:${resId}`;

        this.threadCache.set(threadKey, {
            data: threadData,
            timestamp: Date.now(),
            resModel: resModel,
            resId: resId
        });

        // 检查缓存大小
        if (this.threadCache.size > this.cacheConfig.maxSize) {
            this.evictOldestCache();
        }
    }

    updateThreadCache(resModel, resId) {
        // 更新线程缓存
        const threadKey = `${resModel}:${resId}`;

        if (this.threadCache.has(threadKey)) {
            const cached = this.threadCache.get(threadKey);
            cached.timestamp = Date.now();
            this.threadCache.set(threadKey, cached);
        }
    }

    isCacheExpired(cachedThread) {
        // 判断缓存是否过期
        const now = Date.now();
        return (now - cachedThread.timestamp) > this.cacheConfig.ttl;
    }

    cleanupExpiredCache() {
        // 清理过期缓存
        const now = Date.now();
        const expiredKeys = [];

        for (const [key, cached] of this.threadCache) {
            if (this.isCacheExpired(cached)) {
                expiredKeys.push(key);
            }
        }

        expiredKeys.forEach(key => {
            this.threadCache.delete(key);
        });

        if (expiredKeys.length > 0) {
            console.log(`清理了 ${expiredKeys.length} 个过期缓存`);
        }
    }

    evictOldestCache() {
        // 淘汰最旧的缓存
        let oldestKey = null;
        let oldestTimestamp = Date.now();

        for (const [key, cached] of this.threadCache) {
            if (cached.timestamp < oldestTimestamp) {
                oldestTimestamp = cached.timestamp;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.threadCache.delete(oldestKey);
            console.log('淘汰最旧缓存:', oldestKey);
        }
    }

    applyThreadData(threadData) {
        // 应用线程数据
        this.formController.env.bus.trigger('MAIL:THREAD-DATA-LOADED', {
            threadData: threadData
        });
    }

    getScrollPosition() {
        // 获取滚动位置
        const chatterElement = document.querySelector('.o-mail-Chatter');
        return chatterElement ? chatterElement.scrollTop : 0;
    }

    setScrollPosition(position) {
        // 设置滚动位置
        const chatterElement = document.querySelector('.o-mail-Chatter');
        if (chatterElement) {
            chatterElement.scrollTop = position;
        }
    }

    getSelectedMessage() {
        // 获取选中的消息
        const selectedElement = document.querySelector('.o-mail-Message.selected');
        return selectedElement ? selectedElement.dataset.messageId : null;
    }

    setSelectedMessage(messageId) {
        // 设置选中的消息
        if (messageId) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.classList.add('selected');
            }
        }
    }

    getComposerState() {
        // 获取编辑器状态
        const composerElement = document.querySelector('.o-mail-Composer');
        if (!composerElement) {
            return null;
        }

        return {
            isOpen: composerElement.style.display !== 'none',
            content: composerElement.querySelector('textarea')?.value || '',
            mode: composerElement.dataset.mode || 'note'
        };
    }

    setComposerState(state) {
        // 设置编辑器状态
        if (!state) {
            return;
        }

        const composerElement = document.querySelector('.o-mail-Composer');
        if (composerElement) {
            composerElement.style.display = state.isOpen ? 'block' : 'none';

            const textarea = composerElement.querySelector('textarea');
            if (textarea) {
                textarea.value = state.content;
            }

            if (state.mode) {
                composerElement.dataset.mode = state.mode;
            }
        }
    }

    getCacheStats() {
        // 获取缓存统计
        return {
            size: this.threadCache.size,
            maxSize: this.cacheConfig.maxSize,
            hitRate: this.calculateHitRate(),
            expiredCount: this.getExpiredCount()
        };
    }

    calculateHitRate() {
        // 计算缓存命中率
        // 这里需要实际的统计数据
        return 0.85; // 示例值
    }

    getExpiredCount() {
        // 获取过期缓存数量
        const now = Date.now();
        let expiredCount = 0;

        for (const cached of this.threadCache.values()) {
            if (this.isCacheExpired(cached)) {
                expiredCount++;
            }
        }

        return expiredCount;
    }
}
```

### 3. 邮件撰写处理
```javascript
// 邮件撰写处理器
class MailComposeHandler {
    constructor(formController) {
        this.formController = formController;
        this.domParser = new DOMParser();
        this.setupHandler();
    }

    setupHandler() {
        // 设置处理器
        this.setupPartnerExtraction();
        this.setupContentProcessing();
    }

    setupPartnerExtraction() {
        // 设置合作伙伴提取
        this.partnerSelectors = [
            '[data-oe-model="res.partner"]',
            '[data-partner-id]',
            '.o_mail_mention'
        ];
    }

    setupContentProcessing() {
        // 设置内容处理
        this.contentProcessors = {
            html: this.processHTMLContent.bind(this),
            text: this.processTextContent.bind(this),
            markdown: this.processMarkdownContent.bind(this)
        };
    }

    async onWillSaveRecord(record, changes) {
        // 记录保存前处理
        if (record.resModel === "mail.compose.message") {
            await this.processComposeMessage(record, changes);
        } else {
            await this.processRegularRecord(record, changes);
        }
    }

    async processComposeMessage(record, changes) {
        // 处理撰写消息
        console.log('处理撰写消息:', changes);

        // 提取合作伙伴
        const partnerIds = this.extractPartnerIds(changes.body);

        // 更新合作伙伴字段
        if (partnerIds.length > 0) {
            this.updatePartnerIds(changes, partnerIds);
        }

        // 处理附件
        if (changes.attachment_ids) {
            await this.processAttachments(changes.attachment_ids);
        }

        // 处理模板
        if (changes.template_id) {
            await this.processTemplate(changes.template_id);
        }

        // 验证内容
        this.validateMessageContent(changes);
    }

    extractPartnerIds(htmlBody) {
        // 提取合作伙伴 ID
        if (!htmlBody) {
            return [];
        }

        const htmlDoc = this.domParser.parseFromString(htmlBody, "text/html");
        const partnerIds = new Set();

        // 使用所有选择器提取合作伙伴
        this.partnerSelectors.forEach(selector => {
            const elements = htmlDoc.querySelectorAll(selector);
            elements.forEach(element => {
                const partnerId = this.extractPartnerIdFromElement(element);
                if (partnerId) {
                    partnerIds.add(partnerId);
                }
            });
        });

        return Array.from(partnerIds);
    }

    extractPartnerIdFromElement(element) {
        // 从元素提取合作伙伴 ID

        // 从 data-oe-id 属性提取
        if (element.dataset.oeId) {
            return parseInt(element.dataset.oeId);
        }

        // 从 data-partner-id 属性提取
        if (element.dataset.partnerId) {
            return parseInt(element.dataset.partnerId);
        }

        // 从类名提取
        const classMatch = element.className.match(/partner-(\d+)/);
        if (classMatch) {
            return parseInt(classMatch[1]);
        }

        // 从文本内容提取
        const textMatch = element.textContent.match(/@(\d+)/);
        if (textMatch) {
            return parseInt(textMatch[1]);
        }

        return null;
    }

    updatePartnerIds(changes, extractedPartnerIds) {
        // 更新合作伙伴 ID
        const { x2ManyCommands } = this.formController.env.services.orm;

        let finalPartnerIds = [...extractedPartnerIds];

        // 合并现有的合作伙伴 ID
        if (changes.partner_ids && changes.partner_ids[0]) {
            const command = changes.partner_ids[0];

            if (command[0] === x2ManyCommands.SET) {
                // SET 命令，合并 ID
                finalPartnerIds.push(...command[2]);
            } else if (command[0] === x2ManyCommands.LINK) {
                // LINK 命令，添加 ID
                finalPartnerIds.push(command[1]);
            }
        }

        // 去重
        finalPartnerIds = [...new Set(finalPartnerIds)];

        // 设置最终的合作伙伴 ID
        changes.partner_ids = [x2ManyCommands.set(finalPartnerIds)];

        console.log('更新合作伙伴 ID:', finalPartnerIds);
    }

    async processAttachments(attachmentIds) {
        // 处理附件
        console.log('处理附件:', attachmentIds);

        // 验证附件
        for (const attachmentCommand of attachmentIds) {
            if (attachmentCommand[0] === x2ManyCommands.CREATE) {
                await this.validateAttachment(attachmentCommand[2]);
            }
        }
    }

    async validateAttachment(attachmentData) {
        // 验证附件
        const { name, mimetype, file_size } = attachmentData;

        // 检查文件大小
        const maxSize = 25 * 1024 * 1024; // 25MB
        if (file_size > maxSize) {
            throw new Error(`文件 ${name} 超过最大大小限制 (25MB)`);
        }

        // 检查文件类型
        const allowedTypes = [
            'image/', 'application/pdf', 'text/', 'application/msword',
            'application/vnd.openxmlformats-officedocument'
        ];

        const isAllowed = allowedTypes.some(type => mimetype.startsWith(type));
        if (!isAllowed) {
            throw new Error(`不支持的文件类型: ${mimetype}`);
        }

        console.log('附件验证通过:', name);
    }

    async processTemplate(templateId) {
        // 处理模板
        console.log('处理模板:', templateId);

        try {
            const template = await this.formController.env.services.orm.read(
                'mail.template',
                [templateId],
                ['name', 'subject', 'body_html', 'partner_to']
            );

            if (template.length > 0) {
                console.log('模板加载成功:', template[0].name);
            }

        } catch (error) {
            console.error('模板处理失败:', error);
        }
    }

    validateMessageContent(changes) {
        // 验证消息内容

        // 检查主题
        if (!changes.subject || changes.subject.trim().length === 0) {
            console.warn('消息缺少主题');
        }

        // 检查正文
        if (!changes.body || changes.body.trim().length === 0) {
            throw new Error('消息正文不能为空');
        }

        // 检查收件人
        if (!changes.partner_ids || changes.partner_ids.length === 0) {
            console.warn('消息没有指定收件人');
        }

        console.log('消息内容验证通过');
    }

    async processRegularRecord(record, changes) {
        // 处理常规记录
        console.log('处理常规记录:', record.resModel);

        // 检查是否有邮件相关字段变化
        const mailFields = this.getMailFields(changes);

        if (mailFields.length > 0) {
            await this.processMailFieldChanges(record, mailFields);
        }
    }

    getMailFields(changes) {
        // 获取邮件相关字段
        const mailFieldNames = [
            'message_ids', 'message_follower_ids', 'activity_ids',
            'message_attachment_count', 'message_main_attachment_id'
        ];

        return Object.keys(changes).filter(field =>
            mailFieldNames.includes(field)
        );
    }

    async processMailFieldChanges(record, mailFields) {
        // 处理邮件字段变化
        console.log('处理邮件字段变化:', mailFields);

        for (const field of mailFields) {
            await this.processMailField(record, field);
        }
    }

    async processMailField(record, fieldName) {
        // 处理邮件字段
        switch (fieldName) {
            case 'message_ids':
                await this.processMessageIds(record);
                break;
            case 'message_follower_ids':
                await this.processFollowerIds(record);
                break;
            case 'activity_ids':
                await this.processActivityIds(record);
                break;
            default:
                console.log(`处理邮件字段: ${fieldName}`);
        }
    }

    async processMessageIds(record) {
        // 处理消息 ID
        console.log('处理消息 ID');

        // 触发消息重载
        this.formController.env.bus.trigger('MAIL:RELOAD-MESSAGES', {
            model: record.resModel,
            id: record.resId
        });
    }

    async processFollowerIds(record) {
        // 处理关注者 ID
        console.log('处理关注者 ID');

        // 触发关注者重载
        this.formController.env.bus.trigger('MAIL:RELOAD-FOLLOWERS', {
            model: record.resModel,
            id: record.resId
        });
    }

    async processActivityIds(record) {
        // 处理活动 ID
        console.log('处理活动 ID');

        // 触发活动重载
        this.formController.env.bus.trigger('MAIL:RELOAD-ACTIVITIES', {
            model: record.resModel,
            id: record.resId
        });
    }

    processHTMLContent(content) {
        // 处理 HTML 内容
        const doc = this.domParser.parseFromString(content, 'text/html');

        // 清理危险标签
        this.sanitizeHTML(doc);

        // 处理链接
        this.processLinks(doc);

        // 处理图片
        this.processImages(doc);

        return doc.body.innerHTML;
    }

    sanitizeHTML(doc) {
        // 清理 HTML
        const dangerousTags = ['script', 'iframe', 'object', 'embed'];

        dangerousTags.forEach(tag => {
            const elements = doc.querySelectorAll(tag);
            elements.forEach(el => el.remove());
        });
    }

    processLinks(doc) {
        // 处理链接
        const links = doc.querySelectorAll('a');

        links.forEach(link => {
            // 添加安全属性
            link.setAttribute('rel', 'noopener noreferrer');

            // 外部链接在新窗口打开
            if (link.hostname !== window.location.hostname) {
                link.setAttribute('target', '_blank');
            }
        });
    }

    processImages(doc) {
        // 处理图片
        const images = doc.querySelectorAll('img');

        images.forEach(img => {
            // 添加懒加载
            img.setAttribute('loading', 'lazy');

            // 添加错误处理
            img.setAttribute('onerror', 'this.style.display="none"');
        });
    }

    processTextContent(content) {
        // 处理文本内容

        // 转义 HTML 字符
        const escaped = content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');

        // 处理换行
        return escaped.replace(/\n/g, '<br>');
    }

    processMarkdownContent(content) {
        // 处理 Markdown 内容
        // 这里可以集成 Markdown 解析器

        // 简单的 Markdown 处理
        let processed = content;

        // 粗体
        processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 斜体
        processed = processed.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 链接
        processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

        // 换行
        processed = processed.replace(/\n/g, '<br>');

        return processed;
    }
}
```

## 使用示例

### 1. 基本表单控制器使用
```javascript
// 使用扩展的表单控制器
class MailEnabledFormView extends Component {
    static template = xml`
        <div class="o_form_view">
            <FormRenderer t-props="rendererProps"/>
        </div>
    `;

    setup() {
        this.formController = new ExtendedFormController();
        this.threadManager = new ThreadDataManager(this.formController);
        this.composeHandler = new MailComposeHandler(this.formController);

        this.setupFormIntegration();
    }

    setupFormIntegration() {
        // 设置表单集成

        // 监听表单事件
        this.env.bus.addEventListener('FORM:WILL_LOAD_ROOT', (event) => {
            this.threadManager.onWillLoadRoot(event.detail);
        });

        this.env.bus.addEventListener('FORM:WILL_SAVE_RECORD', (event) => {
            this.composeHandler.onWillSaveRecord(event.detail.record, event.detail.changes);
        });

        // 监听邮件事件
        this.env.bus.addEventListener('MAIL:MESSAGE_POSTED', (event) => {
            this.onMailEvent('message_posted', event.detail);
        });

        this.env.bus.addEventListener('MAIL:ACTIVITY_UPDATED', (event) => {
            this.onMailEvent('activity_updated', event.detail);
        });
    }

    onMailEvent(eventType, data) {
        // 邮件事件处理
        console.log(`邮件事件: ${eventType}`, data);

        // 根据事件类型执行相应操作
        switch (eventType) {
            case 'message_posted':
                this.handleMessagePosted(data);
                break;
            case 'activity_updated':
                this.handleActivityUpdated(data);
                break;
        }
    }

    handleMessagePosted(data) {
        // 处理消息发布
        console.log('消息已发布:', data);

        // 更新表单数据
        this.updateFormData(['messages']);

        // 显示通知
        this.env.services.notification.add('消息发布成功', {
            type: 'success'
        });
    }

    handleActivityUpdated(data) {
        // 处理活动更新
        console.log('活动已更新:', data);

        // 更新表单数据
        this.updateFormData(['activities']);
    }

    updateFormData(dataTypes) {
        // 更新表单数据
        dataTypes.forEach(dataType => {
            this.env.bus.trigger(`FORM:UPDATE_${dataType.toUpperCase()}`);
        });
    }
}
```

### 2. 自定义邮件处理
```javascript
// 自定义邮件处理器
class CustomMailHandler extends Component {
    setup() {
        this.composeHandler = new MailComposeHandler(this);
        this.setupCustomHandling();
    }

    setupCustomHandling() {
        // 设置自定义处理

        // 自定义合作伙伴提取
        this.composeHandler.partnerSelectors.push('.custom-mention');

        // 自定义内容处理器
        this.composeHandler.contentProcessors.custom = this.processCustomContent.bind(this);

        // 自定义验证规则
        this.composeHandler.customValidators = [
            this.validateCustomRule1.bind(this),
            this.validateCustomRule2.bind(this)
        ];
    }

    processCustomContent(content) {
        // 处理自定义内容
        console.log('处理自定义内容:', content);

        // 自定义处理逻辑
        let processed = content;

        // 处理自定义标签
        processed = processed.replace(/<custom-tag>(.*?)<\/custom-tag>/g, '<span class="custom">$1</span>');

        // 处理自定义链接
        processed = processed.replace(/\[\[([^\]]+)\]\]/g, '<a href="/custom/$1">$1</a>');

        return processed;
    }

    validateCustomRule1(changes) {
        // 自定义验证规则 1
        if (changes.subject && changes.subject.includes('URGENT')) {
            if (!changes.priority || changes.priority < 3) {
                throw new Error('紧急消息必须设置高优先级');
            }
        }
        return true;
    }

    validateCustomRule2(changes) {
        // 自定义验证规则 2
        if (changes.body && changes.body.length > 10000) {
            throw new Error('消息内容过长，请控制在10000字符以内');
        }
        return true;
    }
}
```

## 表单控制器特性

### 1. Chatter 环境集成
表单控制器为 Chatter 提供了完整的环境支持：
- **子环境配置**: 配置 Chatter 专用的子环境
- **数据获取控制**: 控制数据和消息的获取行为
- **服务集成**: 集成邮件存储和相关服务
- **事件总线**: 提供事件总线支持

### 2. 线程数据管理
```javascript
// 线程数据管理配置
const threadManagement = {
    caching: {
        enabled: true,
        maxSize: 50,
        ttl: 300000 // 5分钟
    },

    synchronization: {
        autoReload: true,
        batchSize: 10,
        debounceTime: 300
    },

    stateManagement: {
        saveScrollPosition: true,
        saveComposerState: true,
        saveSelection: true
    }
};
```

### 3. 邮件撰写处理
- **合作伙伴提取**: 从 HTML 内容中智能提取合作伙伴
- **内容处理**: 支持多种内容格式的处理
- **验证机制**: 完善的内容和数据验证
- **附件处理**: 附件上传和验证支持

### 4. 生命周期集成
- **加载前处理**: 在记录加载前进行必要的准备
- **保存前处理**: 在记录保存前进行数据处理
- **事件响应**: 响应各种表单和邮件事件
- **状态同步**: 保持表单和 Chatter 状态同步

## 最佳实践

### 1. 环境配置
```javascript
// ✅ 推荐：正确的环境配置
useSubEnv({
    chatter: {
        fetchData: true,
        fetchMessages: true,
        autoReload: true
    }
});
```

### 2. 事件处理
```javascript
// ✅ 推荐：正确的事件监听
this.env.bus.addEventListener('MAIL:RELOAD-THREAD', this.onThreadReload);

onWillUnmount(() => {
    this.env.bus.removeEventListener('MAIL:RELOAD-THREAD', this.onThreadReload);
});
```

### 3. 数据验证
```javascript
// ✅ 推荐：完善的数据验证
if (!changes.body || changes.body.trim().length === 0) {
    throw new Error('消息正文不能为空');
}
```

### 4. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.processComposeMessage(record, changes);
} catch (error) {
    console.error('处理撰写消息失败:', error);
    this.env.services.notification.add(error.message, { type: 'danger' });
}
```

## 总结

Odoo 表单控制器扩展模块提供了完整的邮件功能集成：

**核心优势**:
- **无缝集成**: 与表单视图的无缝集成
- **智能处理**: 智能的邮件内容和数据处理
- **状态管理**: 完善的线程和状态管理
- **性能优化**: 缓存和优化机制
- **扩展性**: 灵活的扩展和自定义能力

**适用场景**:
- 表单视图中的邮件功能
- 邮件撰写和发送
- 线程数据管理
- 用户交互优化
- 数据同步需求

**设计优势**:
- 补丁模式扩展
- 事件驱动架构
- 模块化设计
- 缓存优化

这个表单控制器扩展为 Odoo Web 客户端提供了强大的邮件功能控制能力，确保了邮件系统与表单视图的完美集成和优化性能。
```
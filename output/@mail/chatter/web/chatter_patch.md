# Odoo Chatter Web 补丁 (Chatter Web Patch) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/chatter_patch.js`
**原始路径**: `/mail/static/src/chatter/web/chatter_patch.js`
**模块类型**: 核心补丁 - Chatter Web 功能扩展
**代码行数**: 406 行
**依赖关系**:
- `@mail/chatter/web/scheduled_message` - 计划消息
- `@mail/core/web/activity` - 活动管理
- `@mail/core/common/attachment_list` - 附件列表
- `@mail/core/web/base_recipients_list` - 基础收件人列表
- `@mail/chatter/web_portal/chatter` - Chatter 基础组件
- `@mail/core/web/suggested_recipient_list` - 建议收件人列表
- `@mail/core/web/follower_list` - 关注者列表
- `@mail/utils/common/misc` - 通用工具
- `@mail/core/common/search_messages_panel` - 消息搜索面板
- `@mail/core/common/attachment_uploader_hook` - 附件上传钩子
- `@web/core/dropzone/dropzone_hook` - 拖拽区域钩子
- `@mail/utils/common/hooks` - 邮件通用钩子
- `@odoo/owl` - OWL 框架
- `@web/core/l10n/translation` - 国际化
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/dropdown/dropdown` - 下拉菜单
- `@web/views/fields/file_handler` - 文件处理器
- `@web/core/utils/patch` - 补丁工具
- `@web/core/dropdown/dropdown_hooks` - 下拉菜单钩子
- `@web/core/utils/hooks` - 核心钩子

## 模块功能

Chatter Web 补丁模块是 Odoo Web 客户端 Chatter 组件的核心功能扩展。该模块提供了：
- Chatter 组件的 Web 端增强功能
- 附件拖拽上传支持
- 活动管理集成
- 关注者管理功能
- 计划消息处理
- 搜索消息面板
- 建议收件人管理
- 文件上传和管理
- 响应式布局支持

这个补丁模块通过扩展基础 Chatter 组件，为 Web 端提供了完整的邮件和活动管理功能。

## Chatter Web 补丁架构

### 核心组件结构
```
Chatter Web Patch
├── 组件扩展
│   ├── Activity (活动管理)
│   ├── AttachmentList (附件列表)
│   ├── BaseRecipientsList (基础收件人)
│   ├── FollowerList (关注者列表)
│   ├── ScheduledMessage (计划消息)
│   ├── SearchMessagesPanel (搜索面板)
│   └── SuggestedRecipientsList (建议收件人)
├── 功能增强
│   ├── 拖拽上传支持
│   ├── 附件管理
│   ├── 关注者操作
│   ├── 活动调度
│   ├── 消息搜索
│   └── 计划消息管理
├── 状态管理
│   ├── 编辑器类型状态
│   ├── 附件盒状态
│   ├── 搜索状态
│   ├── 活动显示状态
│   └── 计划消息状态
└── 交互处理
    ├── 文件上传处理
    ├── 关注/取消关注
    ├── 活动调度
    ├── 搜索切换
    └── 附件操作
```

### 补丁配置
```javascript
// 组件注册
Object.assign(Chatter.components, {
    Activity,
    AttachmentList,
    BaseRecipientsList,
    Dropdown,
    FileUploader,
    FollowerList,
    ScheduledMessage,
    SearchMessagesPanel,
    SuggestedRecipientsList,
});

// 属性扩展
Chatter.props.push(
    "close?",
    "compactHeight?",
    "has_activities?",
    "hasAttachmentPreview?",
    "hasParentReloadOnAttachmentsChanged?",
    "hasParentReloadOnFollowersUpdate?",
    "hasParentReloadOnMessagePosted?",
    "highlightMessageId?",
    "isAttachmentBoxVisibleInitially?",
    "isChatterAside?",
    "isInFormSheetBg?",
    "saveRecord?",
    "webRecord?"
);

// 默认属性
Object.assign(Chatter.defaultProps, {
    compactHeight: false,
    has_activities: true,
    hasAttachmentPreview: false,
    hasParentReloadOnAttachmentsChanged: false,
    hasParentReloadOnFollowersUpdate: false,
    hasParentReloadOnMessagePosted: false,
    isAttachmentBoxVisibleInitially: false,
    isChatterAside: false,
    isInFormSheetBg: true,
});
```

## 核心功能详解

### 1. 组件设置和初始化
```javascript
// Chatter 组件设置
setup() {
    this.messageHighlight = useMessageHighlight();
    super.setup(...arguments);
    this.orm = useService("orm");
    this.mailPopoutService = useService("mail.popout");

    // 状态初始化
    Object.assign(this.state, {
        composerType: false,                    // 编辑器类型
        isAttachmentBoxOpened: this.props.isAttachmentBoxVisibleInitially,
        isSearchOpen: false,                    // 搜索状态
        showActivities: true,                   // 显示活动
        showAttachmentLoading: false,           // 附件加载状态
        showScheduledMessages: true,            // 显示计划消息
    });

    // 附件上传器初始化
    this.attachmentUploader = useAttachmentUploader(
        this.store.Thread.insert({
            model: this.props.threadModel,
            id: this.props.threadId
        })
    );

    // 悬停效果
    this.unfollowHover = useHover("unfollow");
    this.followerListDropdown = useDropdownState();

    // 拖拽区域设置
    useDropzone(
        this.rootRef,
        async (ev) => {
            if (this.state.composerType) {
                return;
            }
            if (isDragSourceExternalFile(ev.dataTransfer)) {
                const files = [...ev.dataTransfer.files];
                if (!this.state.thread.id) {
                    const saved = await this.props.saveRecord?.();
                    if (!saved) {
                        return;
                    }
                }
                Promise.all(files.map((file) =>
                    this.attachmentUploader.uploadFile(file)
                )).then(() => {
                    if (this.props.hasParentReloadOnAttachmentsChanged) {
                        this.reloadParentView();
                    }
                });
                this.state.isAttachmentBoxOpened = true;
            }
        },
        "o-mail-Chatter-dropzone"
    );
}
```

### 2. 计算属性和获取器
```javascript
// 活动列表
get activities() {
    return this.state.thread?.activities ?? [];
}

// 附件列表
get attachments() {
    return this.state.thread?.attachments ?? [];
}

// 计划消息列表
get scheduledMessages() {
    return this.state.thread?.scheduledMessages ?? [];
}

// 禁用状态
get isDisabled() {
    return !this.state.thread.id || !this.state.thread?.hasReadAccess;
}

// 请求列表
get requestList() {
    return [
        ...super.requestList,
        "activities",
        "attachments",
        "followers",
        "scheduledMessages",
        "suggestedRecipients",
    ];
}

// 发布后请求列表
get afterPostRequestList() {
    return [
        ...super.afterPostRequestList,
        "followers",
        "scheduledMessages",
        "suggestedRecipients",
    ];
}

// 子环境
get childSubEnv() {
    const res = Object.assign(super.childSubEnv, {
        messageHighlight: this.messageHighlight
    });
    res.inChatter.aside = this.props.isChatterAside;
    return res;
}
```

### 3. 关注者管理功能
```javascript
// 关注操作
async _follow(thread) {
    await this.orm.call(thread.model, "message_subscribe", [[thread.id]], {
        partner_ids: [this.store.self.id],
    });
    this.onFollowerChanged(thread);
}

// 点击关注
async onClickFollow() {
    if (this.state.thread.id) {
        this._follow(this.state.thread);
    } else {
        this.onThreadCreated = this._follow;
        await this.props.saveRecord?.();
    }
}

// 点击取消关注
async onClickUnfollow() {
    const thread = this.state.thread;
    await thread.selfFollower.remove();
    this.onFollowerChanged(thread);
}

// 关注者变化处理
onFollowerChanged(thread) {
    document.body.click(); // hack to close dropdown
    this.reloadParentView();
    this.load(thread, ["followers", "suggestedRecipients"]);
}

// 添加关注者
onAddFollowers() {
    this.load(this.state.thread, ["followers", "suggestedRecipients"]);
    if (this.props.hasParentReloadOnFollowersUpdate) {
        this.reloadParentView();
    }
}
```

### 4. 附件管理功能
```javascript
// 点击添加附件
onClickAddAttachments() {
    if (this.attachments.length === 0) {
        return;
    }
    this.state.isAttachmentBoxOpened = !this.state.isAttachmentBoxOpened;
    if (this.state.isAttachmentBoxOpened) {
        this.rootRef.el.scrollTop = 0;
        this.state.thread.scrollTop = "bottom";
    }
}

// 点击附加文件
async onClickAttachFile(ev) {
    if (this.state.thread.id) {
        return;
    }
    const saved = await this.props.saveRecord?.();
    if (!saved) {
        return false;
    }
}

// 文件上传完成
async onUploaded(data) {
    await this.attachmentUploader.uploadData(data);
    if (this.props.hasParentReloadOnAttachmentsChanged) {
        this.reloadParentView();
    }
    this.state.isAttachmentBoxOpened = true;
    if (this.rootRef.el) {
        this.rootRef.el.scrollTop = 0;
    }
    this.state.thread.scrollTop = "bottom";
}

// 删除附件
async unlinkAttachment(attachment) {
    await this.attachmentUploader.unlink(attachment);
    if (this.props.hasParentReloadOnAttachmentsChanged) {
        this.reloadParentView();
    }
}

// 弹出附件
popoutAttachment() {
    this.mailPopoutService.popout().focus();
}
```

### 5. 活动管理功能
```javascript
// 调度活动
async scheduleActivity() {
    this.closeSearch();
    const schedule = async (thread) => {
        await this.store.scheduleActivity(thread.model, [thread.id]);
        this.load(thread, ["activities", "messages"]);
    };
    if (this.state.thread.id) {
        schedule(this.state.thread);
    } else {
        this.onThreadCreated = schedule;
        this.props.saveRecord?.();
    }
}

// 活动变化处理
onActivityChanged(thread) {
    this.load(thread, [...this.requestList, "messages"]);
}

// 切换活动显示
toggleActivities() {
    this.state.showActivities = !this.state.showActivities;
}
```

### 6. 搜索功能
```javascript
// 点击搜索
onClickSearch() {
    this.state.composerType = false;
    this.state.isSearchOpen = !this.state.isSearchOpen;
}

// 关闭搜索
closeSearch() {
    this.state.isSearchOpen = false;
}
```

### 7. 编辑器管理
```javascript
// 切换编辑器
toggleComposer(mode = false) {
    this.closeSearch();
    const toggle = () => {
        if (this.state.composerType === mode) {
            this.state.composerType = false;
        } else {
            this.state.composerType = mode;
        }
    };
    if (this.state.thread.id) {
        toggle();
    } else {
        this.onThreadCreated = toggle;
        this.props.saveRecord?.();
    }
}

// 关闭完整编辑器回调
onCloseFullComposerCallback() {
    this.toggleComposer();
    super.onCloseFullComposerCallback();
}
```

## 使用示例

### 1. 基本 Chatter 使用
```javascript
// 在表单视图中使用 Chatter
class FormViewWithChatter extends Component {
    static template = xml`
        <div class="o_form_view">
            <div class="o_form_sheet_bg">
                <div class="o_form_sheet">
                    <!-- 表单内容 -->
                </div>

                <!-- Chatter 组件 -->
                <t t-component="mailComponents.Chatter"
                   threadId="record.resId"
                   threadModel="record.resModel"
                   has_activities="archInfo.has_activities"
                   hasAttachmentPreview="true"
                   hasParentReloadOnAttachmentsChanged="true"
                   hasParentReloadOnFollowersUpdate="true"
                   hasParentReloadOnMessagePosted="true"
                   isAttachmentBoxVisibleInitially="false"
                   isChatterAside="false"
                   isInFormSheetBg="true"
                   webRecord="record"
                   saveRecord="() => save()" />
            </div>
        </div>
    `;

    setup() {
        this.mailComponents = useService('mail.components');
        this.record = useState({
            resId: 1,
            resModel: 'res.partner'
        });
    }

    async save() {
        // 保存记录逻辑
        console.log('保存记录');
        return true;
    }
}
```

### 2. 自定义 Chatter 配置
```javascript
// 自定义 Chatter 配置示例
class CustomChatterComponent extends Component {
    static template = xml`
        <div class="custom-chatter-container">
            <t t-component="mailComponents.Chatter"
               t-props="chatterProps" />
        </div>
    `;

    setup() {
        this.mailComponents = useService('mail.components');
        this.chatterProps = useState({
            threadId: this.props.recordId,
            threadModel: this.props.model,

            // 功能配置
            has_activities: true,
            hasAttachmentPreview: true,

            // 重载配置
            hasParentReloadOnAttachmentsChanged: true,
            hasParentReloadOnFollowersUpdate: true,
            hasParentReloadOnMessagePosted: true,

            // 显示配置
            isAttachmentBoxVisibleInitially: false,
            isChatterAside: false,
            isInFormSheetBg: false,
            compactHeight: false,

            // 回调函数
            webRecord: this.props.record,
            saveRecord: this.saveRecord.bind(this),
            close: this.closeChatter.bind(this)
        });
    }

    async saveRecord() {
        try {
            await this.props.record.save();
            return true;
        } catch (error) {
            console.error('保存失败:', error);
            return false;
        }
    }

    closeChatter() {
        this.props.onClose?.();
    }
}
```

### 3. Chatter 事件处理
```javascript
// Chatter 事件处理示例
class ChatterEventHandler extends Component {
    setup() {
        this.chatterRef = useRef('chatter');
        this.notification = useService('notification');

        // 监听 Chatter 事件
        this.setupChatterEventListeners();
    }

    setupChatterEventListeners() {
        // 监听附件变化
        this.env.bus.addEventListener('attachment-changed', (event) => {
            this.handleAttachmentChanged(event.detail);
        });

        // 监听关注者变化
        this.env.bus.addEventListener('follower-changed', (event) => {
            this.handleFollowerChanged(event.detail);
        });

        // 监听消息发布
        this.env.bus.addEventListener('message-posted', (event) => {
            this.handleMessagePosted(event.detail);
        });

        // 监听活动变化
        this.env.bus.addEventListener('activity-changed', (event) => {
            this.handleActivityChanged(event.detail);
        });
    }

    handleAttachmentChanged(data) {
        console.log('附件变化:', data);
        this.notification.add('附件已更新', {
            type: 'success'
        });

        // 刷新相关数据
        this.refreshAttachmentData();
    }

    handleFollowerChanged(data) {
        console.log('关注者变化:', data);
        this.notification.add('关注者已更新', {
            type: 'info'
        });

        // 更新关注者列表
        this.refreshFollowerData();
    }

    handleMessagePosted(data) {
        console.log('消息已发布:', data);
        this.notification.add('消息发布成功', {
            type: 'success'
        });

        // 刷新消息列表
        this.refreshMessageData();
    }

    handleActivityChanged(data) {
        console.log('活动变化:', data);
        this.notification.add('活动已更新', {
            type: 'info'
        });

        // 刷新活动数据
        this.refreshActivityData();
    }

    async refreshAttachmentData() {
        // 刷新附件数据
        const chatter = this.chatterRef.el;
        if (chatter && chatter.__owl__) {
            await chatter.__owl__.component.load(
                chatter.__owl__.component.state.thread,
                ['attachments']
            );
        }
    }

    async refreshFollowerData() {
        // 刷新关注者数据
        const chatter = this.chatterRef.el;
        if (chatter && chatter.__owl__) {
            await chatter.__owl__.component.load(
                chatter.__owl__.component.state.thread,
                ['followers', 'suggestedRecipients']
            );
        }
    }

    async refreshMessageData() {
        // 刷新消息数据
        const chatter = this.chatterRef.el;
        if (chatter && chatter.__owl__) {
            await chatter.__owl__.component.load(
                chatter.__owl__.component.state.thread,
                ['messages']
            );
        }
    }

    async refreshActivityData() {
        // 刷新活动数据
        const chatter = this.chatterRef.el;
        if (chatter && chatter.__owl__) {
            await chatter.__owl__.component.load(
                chatter.__owl__.component.state.thread,
                ['activities']
            );
        }
    }
}
```

## Chatter Web 补丁特性

### 1. 拖拽上传支持
Chatter 支持直接拖拽文件到聊天区域进行上传：
- **文件检测**: 自动检测拖拽的外部文件
- **批量上传**: 支持同时上传多个文件
- **自动保存**: 上传前自动保存记录
- **状态反馈**: 提供上传进度和状态反馈
- **附件盒展开**: 上传后自动展开附件盒

### 2. 活动管理集成
```javascript
// 活动管理功能
const activityFeatures = {
    scheduling: {
        method: 'scheduleActivity',
        trigger: 'button_click',
        autoSave: true
    },

    display: {
        toggle: 'toggleActivities',
        defaultShow: true,
        responsive: true
    },

    updates: {
        autoReload: true,
        includeMessages: true,
        realtime: true
    }
};
```

### 3. 关注者管理
- **关注操作**: 一键关注/取消关注
- **关注者列表**: 显示和管理关注者
- **建议收件人**: 智能建议相关收件人
- **权限控制**: 基于权限的操作控制
- **实时更新**: 关注者变化的实时同步

### 4. 搜索功能
- **消息搜索**: 在聊天记录中搜索消息
- **搜索面板**: 专用的搜索界面
- **状态切换**: 搜索和编辑器状态互斥
- **快速访问**: 便捷的搜索入口

### 5. 响应式布局
```javascript
// 布局配置
const layoutConfig = {
    aside: {
        condition: 'isChatterAside',
        classes: ['o-aside', 'w-print-100']
    },

    inSheet: {
        condition: 'isInFormSheetBg',
        position: 'within_sheet'
    },

    compact: {
        condition: 'compactHeight',
        optimization: 'height'
    }
};
```

## 最佳实践

### 1. 组件配置
```javascript
// ✅ 推荐：完整的 Chatter 配置
const chatterConfig = {
    threadId: record.resId,
    threadModel: record.resModel,
    has_activities: true,
    hasAttachmentPreview: true,
    hasParentReloadOnAttachmentsChanged: true,
    hasParentReloadOnFollowersUpdate: true,
    hasParentReloadOnMessagePosted: true,
    webRecord: record,
    saveRecord: () => record.save()
};
```

### 2. 事件处理
```javascript
// ✅ 推荐：正确的事件监听
setup() {
    this.env.bus.addEventListener('attachment-changed', this.onAttachmentChanged);

    onWillUnmount(() => {
        this.env.bus.removeEventListener('attachment-changed', this.onAttachmentChanged);
    });
}
```

### 3. 状态管理
```javascript
// ✅ 推荐：响应式状态管理
this.state = useState({
    composerType: false,
    isAttachmentBoxOpened: false,
    isSearchOpen: false,
    showActivities: true
});
```

### 4. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
async saveRecord() {
    try {
        const result = await this.props.saveRecord?.();
        if (!result) {
            this.notification.add('保存失败', { type: 'danger' });
            return false;
        }
        return true;
    } catch (error) {
        console.error('保存错误:', error);
        this.notification.add('保存出错', { type: 'danger' });
        return false;
    }
}
```

## 总结

Odoo Chatter Web 补丁模块提供了完整的 Web 端邮件和活动管理功能：

**核心优势**:
- **功能完整**: 涵盖邮件、活动、附件、关注者等全部功能
- **用户友好**: 直观的拖拽上传和交互设计
- **响应式**: 适应不同屏幕尺寸和布局需求
- **高度集成**: 与 Odoo Web 框架深度集成
- **可扩展**: 灵活的组件架构支持功能扩展

**适用场景**:
- 表单视图中的 Chatter 集成
- 客户关系管理
- 项目协作和沟通
- 文档和附件管理
- 活动和任务跟踪

**设计优势**:
- 补丁模式扩展
- 组件化架构
- 状态驱动设计
- 事件驱动交互

这个 Chatter Web 补丁为 Odoo Web 客户端提供了强大的协作和沟通能力，是现代企业应用不可或缺的重要组件。
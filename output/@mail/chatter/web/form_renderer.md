# Odoo 表单渲染器 (Form Renderer) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/form_renderer.js`
**原始路径**: `/mail/static/src/chatter/web/form_renderer.js`
**模块类型**: 核心渲染器 - 表单渲染器扩展
**代码行数**: 77 行
**依赖关系**:
- `@mail/core/common/attachment_view` - 附件视图
- `@mail/chatter/web_portal/chatter` - Chatter 组件
- `@web/views/form/form_renderer` - 表单渲染器
- `@web/core/utils/patch` - 补丁工具
- `@web/core/utils/hooks` - 核心钩子
- `@web/core/browser/browser` - 浏览器工具
- `@web/core/browser/router` - 路由器
- `@web/core/ui/ui_service` - UI 服务
- `@web/core/utils/timing` - 时间工具
- `@odoo/owl` - OWL 框架

## 模块功能

表单渲染器模块是 Odoo Web 客户端表单视图渲染器的邮件功能扩展。该模块提供了：
- 邮件组件集成
- 响应式布局管理
- 附件视图支持
- Chatter 布局控制
- 消息高亮功能
- 窗口大小适配
- 弹出窗口支持

这个模块通过补丁方式扩展了标准的表单渲染器，为邮件相关组件提供了完整的渲染和布局管理支持。

## 表单渲染器架构

### 核心组件结构
```
Form Renderer Extension
├── 组件集成
│   ├── AttachmentView (附件视图)
│   ├── Chatter (聊天组件)
│   ├── 邮件存储服务
│   └── UI 服务集成
├── 布局管理
│   ├── 响应式布局检测
│   ├── 附件容器管理
│   ├── Chatter 位置控制
│   └── 弹出窗口支持
├── 状态管理
│   ├── 消息状态
│   ├── 线程状态
│   ├── 附件状态
│   └── 高亮状态
└── 事件处理
    ├── 窗口大小变化
    ├── 路由变化
    ├── 组件挂载
    └── 组件卸载
```

### 渲染器配置
```javascript
// 补丁实现
patch(FormRenderer.prototype, {
    setup() {
        super.setup();

        // 邮件组件注册
        this.mailComponents = {
            AttachmentView,
            Chatter,
        };

        // 消息高亮配置
        this.highlightMessageId = router.current.highlight_message_id;

        // 消息状态管理
        this.messagingState = useState({
            thread: undefined,
        });

        // 服务集成
        if (this.env.services["mail.store"]) {
            this.mailStore = useService("mail.store");
        }
        this.uiService = useService("ui");
        this.mailPopoutService = useService("mail.popout");
    }
});
```

## 核心功能详解

### 1. 响应式布局管理
```javascript
// 响应式布局管理器
class ResponsiveLayoutManager {
    constructor(formRenderer) {
        this.formRenderer = formRenderer;
        this.layoutCache = new Map();
        this.breakpoints = {
            XXL: 1400,
            XL: 1200,
            LG: 992,
            MD: 768,
            SM: 576
        };
        this.setupManager();
    }

    setupManager() {
        // 设置管理器
        this.setupBreakpointDetection();
        this.setupLayoutStrategies();
        this.setupResizeHandling();
    }

    setupBreakpointDetection() {
        // 设置断点检测
        this.currentBreakpoint = this.detectBreakpoint();
        this.previousBreakpoint = this.currentBreakpoint;
    }

    setupLayoutStrategies() {
        // 设置布局策略
        this.layoutStrategies = {
            'NONE': this.createNoneLayout.bind(this),
            'BOTTOM_CHATTER': this.createBottomChatterLayout.bind(this),
            'SIDE_CHATTER': this.createSideChatterLayout.bind(this),
            'COMBO': this.createComboLayout.bind(this),
            'EXTERNAL_COMBO': this.createExternalComboLayout.bind(this),
            'EXTERNAL_COMBO_XXL': this.createExternalComboXXLLayout.bind(this)
        };
    }

    setupResizeHandling() {
        // 设置大小变化处理
        this.resizeObserver = new ResizeObserver(this.onResize.bind(this));
        this.resizeObserver.observe(document.body);
    }

    detectBreakpoint() {
        // 检测当前断点
        const width = window.innerWidth;

        if (width >= this.breakpoints.XXL) return 'XXL';
        if (width >= this.breakpoints.XL) return 'XL';
        if (width >= this.breakpoints.LG) return 'LG';
        if (width >= this.breakpoints.MD) return 'MD';
        if (width >= this.breakpoints.SM) return 'SM';
        return 'XS';
    }

    onResize() {
        // 大小变化处理
        const newBreakpoint = this.detectBreakpoint();

        if (newBreakpoint !== this.currentBreakpoint) {
            this.previousBreakpoint = this.currentBreakpoint;
            this.currentBreakpoint = newBreakpoint;

            console.log(`断点变化: ${this.previousBreakpoint} -> ${this.currentBreakpoint}`);

            // 触发布局更新
            this.updateLayout();
        }
    }

    updateLayout() {
        // 更新布局
        const layoutType = this.determineLayoutType();
        const layout = this.createLayout(layoutType);

        this.applyLayout(layout);
        this.cacheLayout(layoutType, layout);
    }

    determineLayoutType() {
        // 确定布局类型
        const hasFile = this.formRenderer.hasFile();
        const hasChatter = !!this.formRenderer.mailStore;
        const hasExternalWindow = !!this.formRenderer.mailPopoutService.externalWindow;
        const hasAttachmentContainer = this.hasAttachmentContainer();
        const isXXL = this.currentBreakpoint === 'XXL';

        // 外部窗口布局
        if (hasExternalWindow && hasFile && hasAttachmentContainer) {
            return isXXL ? 'EXTERNAL_COMBO_XXL' : 'EXTERNAL_COMBO';
        }

        // Chatter 布局
        if (hasChatter) {
            if (isXXL) {
                if (hasAttachmentContainer && hasFile) {
                    return 'COMBO';
                }
                return 'SIDE_CHATTER';
            }
            return 'BOTTOM_CHATTER';
        }

        return 'NONE';
    }

    createLayout(layoutType) {
        // 创建布局
        const strategy = this.layoutStrategies[layoutType];
        if (!strategy) {
            throw new Error(`未知的布局类型: ${layoutType}`);
        }

        return strategy();
    }

    createNoneLayout() {
        // 创建无布局
        return {
            type: 'NONE',
            chatter: { display: false },
            attachment: { display: false },
            classes: ['o-mail-layout-none']
        };
    }

    createBottomChatterLayout() {
        // 创建底部 Chatter 布局
        return {
            type: 'BOTTOM_CHATTER',
            chatter: {
                display: true,
                position: 'bottom',
                aside: false,
                inSheet: false
            },
            attachment: { display: false },
            classes: ['o-mail-layout-bottom-chatter']
        };
    }

    createSideChatterLayout() {
        // 创建侧边 Chatter 布局
        return {
            type: 'SIDE_CHATTER',
            chatter: {
                display: true,
                position: 'side',
                aside: true,
                inSheet: false
            },
            attachment: { display: false },
            classes: ['o-mail-layout-side-chatter', 'o-aside']
        };
    }

    createComboLayout() {
        // 创建组合布局
        return {
            type: 'COMBO',
            chatter: {
                display: true,
                position: 'bottom',
                aside: false,
                inSheet: true
            },
            attachment: {
                display: true,
                position: 'side'
            },
            classes: ['o-mail-layout-combo']
        };
    }

    createExternalComboLayout() {
        // 创建外部组合布局
        return {
            type: 'EXTERNAL_COMBO',
            chatter: {
                display: true,
                position: 'bottom',
                aside: false,
                inSheet: false
            },
            attachment: {
                display: true,
                position: 'external'
            },
            classes: ['o-mail-layout-external-combo']
        };
    }

    createExternalComboXXLLayout() {
        // 创建外部组合 XXL 布局
        return {
            type: 'EXTERNAL_COMBO_XXL',
            chatter: {
                display: true,
                position: 'side',
                aside: true,
                inSheet: false
            },
            attachment: {
                display: true,
                position: 'external'
            },
            classes: ['o-mail-layout-external-combo-xxl', 'o-aside']
        };
    }

    applyLayout(layout) {
        // 应用布局
        console.log('应用布局:', layout.type);

        // 更新 CSS 类
        this.updateCSSClasses(layout.classes);

        // 更新 Chatter 配置
        if (layout.chatter.display) {
            this.updateChatterConfig(layout.chatter);
        }

        // 更新附件配置
        if (layout.attachment.display) {
            this.updateAttachmentConfig(layout.attachment);
        }

        // 触发布局变化事件
        this.formRenderer.env.bus.trigger('MAIL:LAYOUT_CHANGED', {
            layout: layout,
            breakpoint: this.currentBreakpoint
        });
    }

    updateCSSClasses(classes) {
        // 更新 CSS 类
        const formElement = document.querySelector('.o_form_view');
        if (!formElement) return;

        // 移除旧的布局类
        const oldClasses = Array.from(formElement.classList)
            .filter(cls => cls.startsWith('o-mail-layout-'));

        oldClasses.forEach(cls => formElement.classList.remove(cls));

        // 添加新的布局类
        classes.forEach(cls => formElement.classList.add(cls));
    }

    updateChatterConfig(chatterConfig) {
        // 更新 Chatter 配置
        const chatterElement = document.querySelector('.o-mail-Chatter');
        if (!chatterElement) return;

        // 更新位置类
        chatterElement.classList.toggle('o-aside', chatterConfig.aside);
        chatterElement.classList.toggle('o-in-sheet', chatterConfig.inSheet);

        // 更新数据属性
        chatterElement.dataset.position = chatterConfig.position;
    }

    updateAttachmentConfig(attachmentConfig) {
        // 更新附件配置
        const attachmentElement = document.querySelector('.o_attachment_preview');
        if (!attachmentElement) return;

        // 更新位置类
        attachmentElement.classList.toggle('o-external', attachmentConfig.position === 'external');
        attachmentElement.classList.toggle('o-side', attachmentConfig.position === 'side');

        // 更新数据属性
        attachmentElement.dataset.position = attachmentConfig.position;
    }

    cacheLayout(layoutType, layout) {
        // 缓存布局
        const cacheKey = `${layoutType}_${this.currentBreakpoint}`;
        this.layoutCache.set(cacheKey, layout);

        // 限制缓存大小
        if (this.layoutCache.size > 20) {
            const firstKey = this.layoutCache.keys().next().value;
            this.layoutCache.delete(firstKey);
        }
    }

    hasAttachmentContainer() {
        // 检查是否有附件容器
        return !!document.querySelector('.o_attachment_preview');
    }

    getLayoutInfo() {
        // 获取布局信息
        return {
            currentBreakpoint: this.currentBreakpoint,
            previousBreakpoint: this.previousBreakpoint,
            layoutType: this.determineLayoutType(),
            cacheSize: this.layoutCache.size
        };
    }

    clearCache() {
        // 清除缓存
        this.layoutCache.clear();
    }

    destroy() {
        // 销毁管理器
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        this.clearCache();
    }
}
```

### 2. 附件管理器
```javascript
// 附件管理器
class AttachmentManager {
    constructor(formRenderer) {
        this.formRenderer = formRenderer;
        this.attachmentCache = new Map();
        this.setupManager();
    }

    setupManager() {
        // 设置管理器
        this.setupAttachmentDetection();
        this.setupViewerIntegration();
    }

    setupAttachmentDetection() {
        // 设置附件检测
        this.attachmentTypes = {
            image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'],
            document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
            text: ['txt', 'md', 'csv'],
            archive: ['zip', 'rar', '7z', 'tar', 'gz']
        };
    }

    setupViewerIntegration() {
        // 设置查看器集成
        this.viewerConfig = {
            enablePreview: true,
            enableDownload: true,
            enableDelete: true,
            maxPreviewSize: 10 * 1024 * 1024 // 10MB
        };
    }

    hasFile() {
        // 检查是否有文件
        if (!this.formRenderer.mailStore || !this.formRenderer.props.record.resId) {
            return false;
        }

        // 获取线程
        const thread = this.getThread();
        if (!thread) {
            return false;
        }

        // 检查附件
        return thread.attachmentsInWebClientView.length > 0;
    }

    getThread() {
        // 获取线程
        const cacheKey = `${this.formRenderer.props.record.resModel}:${this.formRenderer.props.record.resId}`;

        // 检查缓存
        if (this.attachmentCache.has(cacheKey)) {
            return this.attachmentCache.get(cacheKey);
        }

        // 创建线程
        const thread = this.formRenderer.mailStore.Thread.insert({
            id: this.formRenderer.props.record.resId,
            model: this.formRenderer.props.record.resModel,
        });

        // 缓存线程
        this.attachmentCache.set(cacheKey, thread);

        return thread;
    }

    getAttachments() {
        // 获取附件列表
        const thread = this.getThread();
        return thread ? thread.attachmentsInWebClientView : [];
    }

    getAttachmentsByType(type) {
        // 按类型获取附件
        const attachments = this.getAttachments();
        const extensions = this.attachmentTypes[type] || [];

        return attachments.filter(attachment => {
            const extension = this.getFileExtension(attachment.name);
            return extensions.includes(extension.toLowerCase());
        });
    }

    getFileExtension(filename) {
        // 获取文件扩展名
        const lastDot = filename.lastIndexOf('.');
        return lastDot > -1 ? filename.substring(lastDot + 1) : '';
    }

    getAttachmentType(attachment) {
        // 获取附件类型
        const extension = this.getFileExtension(attachment.name);

        for (const [type, extensions] of Object.entries(this.attachmentTypes)) {
            if (extensions.includes(extension.toLowerCase())) {
                return type;
            }
        }

        return 'unknown';
    }

    canPreview(attachment) {
        // 检查是否可以预览
        if (!this.viewerConfig.enablePreview) {
            return false;
        }

        // 检查文件大小
        if (attachment.file_size > this.viewerConfig.maxPreviewSize) {
            return false;
        }

        // 检查文件类型
        const type = this.getAttachmentType(attachment);
        return ['image', 'document', 'text'].includes(type);
    }

    async previewAttachment(attachment) {
        // 预览附件
        if (!this.canPreview(attachment)) {
            throw new Error('无法预览此文件');
        }

        try {
            // 获取附件内容
            const content = await this.getAttachmentContent(attachment);

            // 根据类型显示预览
            const type = this.getAttachmentType(attachment);

            switch (type) {
                case 'image':
                    this.previewImage(attachment, content);
                    break;
                case 'document':
                    this.previewDocument(attachment, content);
                    break;
                case 'text':
                    this.previewText(attachment, content);
                    break;
                default:
                    throw new Error('不支持的预览类型');
            }

        } catch (error) {
            console.error('预览附件失败:', error);
            throw error;
        }
    }

    async getAttachmentContent(attachment) {
        // 获取附件内容
        const response = await fetch(`/web/content/${attachment.id}`);

        if (!response.ok) {
            throw new Error('获取附件内容失败');
        }

        return response;
    }

    previewImage(attachment, content) {
        // 预览图片
        const imageUrl = URL.createObjectURL(content.blob());

        // 创建图片预览窗口
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(`
            <html>
                <head>
                    <title>${attachment.name}</title>
                    <style>
                        body { margin: 0; padding: 20px; text-align: center; }
                        img { max-width: 100%; max-height: 80vh; }
                    </style>
                </head>
                <body>
                    <h3>${attachment.name}</h3>
                    <img src="${imageUrl}" alt="${attachment.name}">
                </body>
            </html>
        `);
    }

    previewDocument(attachment, content) {
        // 预览文档
        const documentUrl = URL.createObjectURL(content.blob());

        // 在新窗口中打开文档
        window.open(documentUrl, '_blank');
    }

    previewText(attachment, content) {
        // 预览文本
        content.text().then(text => {
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(`
                <html>
                    <head>
                        <title>${attachment.name}</title>
                        <style>
                            body { margin: 0; padding: 20px; font-family: monospace; }
                            pre { white-space: pre-wrap; word-wrap: break-word; }
                        </style>
                    </head>
                    <body>
                        <h3>${attachment.name}</h3>
                        <pre>${text}</pre>
                    </body>
                </html>
            `);
        });
    }

    async downloadAttachment(attachment) {
        // 下载附件
        if (!this.viewerConfig.enableDownload) {
            throw new Error('下载功能已禁用');
        }

        try {
            const response = await this.getAttachmentContent(attachment);
            const blob = await response.blob();

            // 创建下载链接
            const downloadUrl = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = attachment.name;

            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理 URL
            URL.revokeObjectURL(downloadUrl);

            console.log('附件下载成功:', attachment.name);

        } catch (error) {
            console.error('下载附件失败:', error);
            throw error;
        }
    }

    async deleteAttachment(attachment) {
        // 删除附件
        if (!this.viewerConfig.enableDelete) {
            throw new Error('删除功能已禁用');
        }

        try {
            await this.formRenderer.env.services.orm.unlink('ir.attachment', [attachment.id]);

            // 更新缓存
            this.updateCacheAfterDelete(attachment);

            console.log('附件删除成功:', attachment.name);

        } catch (error) {
            console.error('删除附件失败:', error);
            throw error;
        }
    }

    updateCacheAfterDelete(deletedAttachment) {
        // 删除后更新缓存
        for (const [key, thread] of this.attachmentCache) {
            if (thread.attachmentsInWebClientView) {
                thread.attachmentsInWebClientView = thread.attachmentsInWebClientView.filter(
                    attachment => attachment.id !== deletedAttachment.id
                );
            }
        }
    }

    getAttachmentStats() {
        // 获取附件统计
        const attachments = this.getAttachments();
        const stats = {
            total: attachments.length,
            byType: {},
            totalSize: 0
        };

        attachments.forEach(attachment => {
            const type = this.getAttachmentType(attachment);
            stats.byType[type] = (stats.byType[type] || 0) + 1;
            stats.totalSize += attachment.file_size || 0;
        });

        return stats;
    }

    clearCache() {
        // 清除缓存
        this.attachmentCache.clear();
    }
}
```
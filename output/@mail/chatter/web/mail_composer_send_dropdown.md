# Odoo 邮件撰写发送下拉菜单 (Mail Composer Send Dropdown) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/mail_composer_send_dropdown.js`  
**原始路径**: `/mail/static/src/chatter/web/mail_composer_send_dropdown.js`  
**模块类型**: 核心组件 - 邮件发送下拉菜单  
**代码行数**: 69 行  
**依赖关系**: 
- `@mail/chatter/web/mail_composer_schedule_dialog` - 邮件计划对话框
- `@web/core/dropdown/dropdown` - 下拉菜单组件
- `@web/core/dropdown/dropdown_item` - 下拉菜单项组件
- `@web/core/registry` - 注册表系统
- `@web/core/utils/hooks` - 核心钩子
- `@web/views/widgets/standard_widget_props` - 标准组件属性
- `@odoo/owl` - OWL 框架

## 模块功能

邮件撰写发送下拉菜单模块是 Odoo Web 客户端的邮件发送控制组件。该模块提供了：
- 立即发送邮件功能
- 计划发送邮件功能
- 发送选项下拉菜单
- 记录保存验证
- 动作服务集成
- 对话框服务集成

这个模块为用户提供了灵活的邮件发送选项，支持立即发送和计划发送两种模式。

## 邮件发送下拉菜单架构

### 核心组件结构
```
Mail Composer Send Dropdown
├── 发送选项
│   ├── 立即发送 (Send)
│   ├── 计划发送 (Send Later)
│   ├── 保存草稿 (Save Draft)
│   └── 取消发送 (Cancel)
├── 下拉菜单组件
│   ├── Dropdown 容器
│   ├── DropdownItem 选项
│   ├── 菜单触发器
│   └── 选项列表
├── 服务集成
│   ├── Action Service (动作服务)
│   ├── Dialog Service (对话框服务)
│   ├── ORM Service (数据服务)
│   └── Notification Service (通知服务)
└── 功能处理
    ├── 记录保存验证
    ├── 发送方法选择
    ├── 计划对话框调用
    └── 错误处理
```

### 组件配置
```javascript
// 邮件发送下拉菜单组件
class MailComposerSendDropdown extends Component {
    static components = {
        Dropdown,           // 下拉菜单
        DropdownItem,       // 下拉菜单项
    };
    static props = standardWidgetProps;  // 标准组件属性
    static template = "mail.MailComposerSendDropdown";
    
    setup() {
        super.setup();
        // 服务初始化
        this.actionService = useService("action");
        this.dialogService = useService("dialog");
        this.orm = useService("orm");
    }
}

// 组件注册
registry.category("view_widgets").add("mail_composer_send_dropdown", {
    component: MailComposerSendDropdown
});
```

## 核心功能详解

### 1. 发送控制管理器
```javascript
// 发送控制管理器
class SendControlManager {
    constructor(component) {
        this.component = component;
        this.sendOptions = new Map();
        this.validationRules = [];
        this.sendingState = {
            isSending: false,
            lastSentTime: null,
            sendCount: 0
        };
        this.setupSendOptions();
    }
    
    setupSendOptions() {
        // 设置发送选项
        this.sendOptions.set('send_now', {
            label: '立即发送',
            icon: 'fa-paper-plane',
            method: 'action_send_mail',
            requiresSchedule: false,
            handler: this.handleSendNow.bind(this),
            shortcut: 'Ctrl+Enter',
            priority: 1
        });
        
        this.sendOptions.set('send_later', {
            label: '计划发送',
            icon: 'fa-clock-o',
            method: 'action_schedule_message',
            requiresSchedule: true,
            handler: this.handleSendLater.bind(this),
            shortcut: 'Ctrl+Shift+Enter',
            priority: 2
        });
        
        this.sendOptions.set('save_draft', {
            label: '保存草稿',
            icon: 'fa-save',
            method: 'save',
            requiresSchedule: false,
            handler: this.handleSaveDraft.bind(this),
            shortcut: 'Ctrl+S',
            priority: 3
        });
        
        this.sendOptions.set('send_test', {
            label: '发送测试',
            icon: 'fa-flask',
            method: 'action_send_test',
            requiresSchedule: false,
            handler: this.handleSendTest.bind(this),
            shortcut: null,
            priority: 4
        });
    }
    
    async handleSendNow() {
        // 处理立即发送
        try {
            this.setSendingState(true);
            
            // 验证记录
            if (!await this.validateRecord()) {
                return false;
            }
            
            // 保存记录
            if (!await this.saveRecord()) {
                return false;
            }
            
            // 确定发送方法
            const method = this.determineSendMethod();
            
            // 执行发送
            const result = await this.executeSend(method);
            
            // 处理发送结果
            this.handleSendResult(result, 'send_now');
            
            return true;
            
        } catch (error) {
            this.handleSendError(error, 'send_now');
            return false;
        } finally {
            this.setSendingState(false);
        }
    }
    
    async handleSendLater() {
        // 处理计划发送
        try {
            // 验证记录
            if (!await this.validateRecord()) {
                return false;
            }
            
            // 保存记录
            if (!await this.saveRecord()) {
                return false;
            }
            
            // 打开计划对话框
            this.openScheduleDialog();
            
            return true;
            
        } catch (error) {
            this.handleSendError(error, 'send_later');
            return false;
        }
    }
    
    async handleSaveDraft() {
        // 处理保存草稿
        try {
            // 保存记录
            const saved = await this.saveRecord();
            
            if (saved) {
                this.component.env.services.notification.add(
                    '草稿已保存',
                    { type: 'success' }
                );
            }
            
            return saved;
            
        } catch (error) {
            this.handleSendError(error, 'save_draft');
            return false;
        }
    }
    
    async handleSendTest() {
        // 处理发送测试
        try {
            // 验证记录
            if (!await this.validateRecord()) {
                return false;
            }
            
            // 保存记录
            if (!await this.saveRecord()) {
                return false;
            }
            
            // 发送测试邮件
            const result = await this.component.orm.call(
                "mail.compose.message",
                "action_send_test",
                [this.component.props.record.resId],
                { context: this.component.props.record.context }
            );
            
            this.component.env.services.notification.add(
                '测试邮件已发送',
                { type: 'success' }
            );
            
            return true;
            
        } catch (error) {
            this.handleSendError(error, 'send_test');
            return false;
        }
    }
    
    async validateRecord() {
        // 验证记录
        const record = this.component.props.record;
        
        // 执行自定义验证规则
        for (const rule of this.validationRules) {
            try {
                const isValid = await rule(record);
                if (!isValid) {
                    return false;
                }
            } catch (error) {
                console.error('验证规则执行失败:', error);
                return false;
            }
        }
        
        // 基本验证
        return this.performBasicValidation(record);
    }
    
    performBasicValidation(record) {
        // 执行基本验证
        const data = record.data;
        
        // 检查收件人
        if (!data.partner_ids || data.partner_ids.length === 0) {
            this.component.env.services.notification.add(
                '请至少选择一个收件人',
                { type: 'warning' }
            );
            return false;
        }
        
        // 检查主题
        if (!data.subject || data.subject.trim().length === 0) {
            this.component.env.services.notification.add(
                '请输入邮件主题',
                { type: 'warning' }
            );
            return false;
        }
        
        // 检查正文
        if (!data.body || data.body.trim().length === 0) {
            this.component.env.services.notification.add(
                '请输入邮件正文',
                { type: 'warning' }
            );
            return false;
        }
        
        return true;
    }
    
    async saveRecord() {
        // 保存记录
        try {
            const saved = await this.component.props.record.save();
            return saved;
        } catch (error) {
            console.error('保存记录失败:', error);
            this.component.env.services.notification.add(
                '保存失败，请检查必填字段',
                { type: 'danger' }
            );
            return false;
        }
    }
    
    determineSendMethod() {
        // 确定发送方法
        const record = this.component.props.record;
        
        // 如果设置了计划日期，使用计划发送
        if (record.data.scheduled_date) {
            return "action_schedule_message";
        }
        
        // 否则使用立即发送
        return "action_send_mail";
    }
    
    async executeSend(method) {
        // 执行发送
        const record = this.component.props.record;
        
        const result = await this.component.actionService.doAction(
            await this.component.orm.call(
                "mail.compose.message",
                method,
                [record.resId],
                { context: record.context }
            )
        );
        
        return result;
    }
    
    handleSendResult(result, sendType) {
        // 处理发送结果
        this.sendingState.lastSentTime = Date.now();
        this.sendingState.sendCount++;
        
        let message = '邮件发送成功';
        if (sendType === 'send_later') {
            message = '邮件已计划发送';
        }
        
        this.component.env.services.notification.add(message, {
            type: 'success'
        });
        
        // 触发发送成功事件
        this.component.env.bus.trigger('MAIL:SEND_SUCCESS', {
            sendType: sendType,
            result: result,
            timestamp: this.sendingState.lastSentTime
        });
    }
    
    handleSendError(error, sendType) {
        // 处理发送错误
        console.error(`${sendType} 失败:`, error);
        
        let message = '发送失败，请重试';
        if (sendType === 'send_later') {
            message = '计划发送失败，请重试';
        } else if (sendType === 'save_draft') {
            message = '保存草稿失败，请重试';
        }
        
        this.component.env.services.notification.add(message, {
            type: 'danger'
        });
        
        // 触发发送失败事件
        this.component.env.bus.trigger('MAIL:SEND_ERROR', {
            sendType: sendType,
            error: error,
            timestamp: Date.now()
        });
    }
    
    openScheduleDialog() {
        // 打开计划对话框
        this.component.dialogService.add(MailComposerScheduleDialog, {
            isNote: this.component.props.record.data.subtype_is_log,
            schedule: async (scheduledDate) => {
                try {
                    const result = await this.component.actionService.doAction(
                        await this.component.orm.call(
                            "mail.compose.message",
                            "action_schedule_message",
                            [this.component.props.record.resId, scheduledDate],
                            { context: this.component.props.record.context }
                        )
                    );
                    
                    this.handleSendResult(result, 'send_later');
                    
                } catch (error) {
                    this.handleSendError(error, 'send_later');
                }
            }
        });
    }
    
    setSendingState(isSending) {
        // 设置发送状态
        this.sendingState.isSending = isSending;
        
        // 触发状态变化事件
        this.component.env.bus.trigger('MAIL:SENDING_STATE_CHANGED', {
            isSending: isSending
        });
    }
    
    addValidationRule(rule) {
        // 添加验证规则
        this.validationRules.push(rule);
    }
    
    removeValidationRule(rule) {
        // 移除验证规则
        const index = this.validationRules.indexOf(rule);
        if (index > -1) {
            this.validationRules.splice(index, 1);
        }
    }
    
    getSendOptions() {
        // 获取发送选项
        return Array.from(this.sendOptions.values())
            .sort((a, b) => a.priority - b.priority);
    }
    
    getSendingState() {
        // 获取发送状态
        return { ...this.sendingState };
    }
}
```

### 2. 快捷键管理器
```javascript
// 快捷键管理器
class ShortcutManager {
    constructor(sendControlManager) {
        this.sendControlManager = sendControlManager;
        this.shortcuts = new Map();
        this.isEnabled = true;
        this.setupShortcuts();
    }
    
    setupShortcuts() {
        // 设置快捷键
        const sendOptions = this.sendControlManager.getSendOptions();
        
        sendOptions.forEach(option => {
            if (option.shortcut) {
                this.shortcuts.set(option.shortcut, option.handler);
            }
        });
        
        // 绑定键盘事件
        this.bindKeyboardEvents();
    }
    
    bindKeyboardEvents() {
        // 绑定键盘事件
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }
    
    handleKeyDown(event) {
        // 处理按键事件
        if (!this.isEnabled) {
            return;
        }
        
        // 构建快捷键字符串
        const shortcut = this.buildShortcutString(event);
        
        // 查找对应的处理器
        const handler = this.shortcuts.get(shortcut);
        
        if (handler) {
            event.preventDefault();
            event.stopPropagation();
            
            // 执行处理器
            handler();
        }
    }
    
    buildShortcutString(event) {
        // 构建快捷键字符串
        const parts = [];
        
        if (event.ctrlKey) parts.push('Ctrl');
        if (event.shiftKey) parts.push('Shift');
        if (event.altKey) parts.push('Alt');
        if (event.metaKey) parts.push('Meta');
        
        // 添加主键
        if (event.key && event.key !== 'Control' && event.key !== 'Shift' && 
            event.key !== 'Alt' && event.key !== 'Meta') {
            parts.push(event.key);
        }
        
        return parts.join('+');
    }
    
    enable() {
        // 启用快捷键
        this.isEnabled = true;
    }
    
    disable() {
        // 禁用快捷键
        this.isEnabled = false;
    }
    
    addShortcut(shortcut, handler) {
        // 添加快捷键
        this.shortcuts.set(shortcut, handler);
    }
    
    removeShortcut(shortcut) {
        // 移除快捷键
        this.shortcuts.delete(shortcut);
    }
    
    getShortcuts() {
        // 获取快捷键列表
        return Array.from(this.shortcuts.entries());
    }
    
    destroy() {
        // 销毁管理器
        document.removeEventListener('keydown', this.handleKeyDown.bind(this));
        this.shortcuts.clear();
    }
}
```

## 使用示例

### 1. 基本发送下拉菜单使用
```javascript
// 使用邮件发送下拉菜单
class MailComposerWithSendDropdown extends Component {
    static template = xml`
        <div class="mail-composer">
            <div class="composer-body">
                <!-- 邮件撰写内容 -->
            </div>
            
            <div class="composer-footer">
                <MailComposerSendDropdown 
                    t-props="sendDropdownProps"
                    record="record"/>
            </div>
        </div>
    `;
    
    setup() {
        this.sendDropdownProps = useState({
            record: this.props.record
        });
        
        this.sendControlManager = new SendControlManager(this);
        this.shortcutManager = new ShortcutManager(this.sendControlManager);
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 设置事件监听器
        this.env.bus.addEventListener('MAIL:SEND_SUCCESS', this.onSendSuccess.bind(this));
        this.env.bus.addEventListener('MAIL:SEND_ERROR', this.onSendError.bind(this));
        this.env.bus.addEventListener('MAIL:SENDING_STATE_CHANGED', this.onSendingStateChanged.bind(this));
    }
    
    onSendSuccess(event) {
        // 发送成功处理
        console.log('邮件发送成功:', event.detail);
        
        // 关闭撰写器
        this.props.close?.();
    }
    
    onSendError(event) {
        // 发送失败处理
        console.error('邮件发送失败:', event.detail);
    }
    
    onSendingStateChanged(event) {
        // 发送状态变化处理
        const { isSending } = event.detail;
        
        // 更新 UI 状态
        this.updateSendingUI(isSending);
    }
    
    updateSendingUI(isSending) {
        // 更新发送 UI
        const sendButton = document.querySelector('.o-mail-send-button');
        if (sendButton) {
            sendButton.disabled = isSending;
            sendButton.textContent = isSending ? '发送中...' : '发送';
        }
    }
}
```

### 2. 自定义发送选项
```javascript
// 自定义发送选项
class CustomSendDropdown extends Component {
    setup() {
        this.sendControlManager = new SendControlManager(this);
        
        // 添加自定义验证规则
        this.addCustomValidationRules();
        
        // 添加自定义发送选项
        this.addCustomSendOptions();
    }
    
    addCustomValidationRules() {
        // 添加自定义验证规则
        
        // 验证附件大小
        this.sendControlManager.addValidationRule(async (record) => {
            const attachments = record.data.attachment_ids || [];
            const maxSize = 25 * 1024 * 1024; // 25MB
            
            for (const attachment of attachments) {
                if (attachment.file_size > maxSize) {
                    this.env.services.notification.add(
                        `附件 ${attachment.name} 超过大小限制`,
                        { type: 'warning' }
                    );
                    return false;
                }
            }
            
            return true;
        });
        
        // 验证收件人邮箱
        this.sendControlManager.addValidationRule(async (record) => {
            const partnerIds = record.data.partner_ids || [];
            
            if (partnerIds.length > 0) {
                const partners = await this.env.services.orm.read(
                    'res.partner',
                    partnerIds,
                    ['email']
                );
                
                const invalidPartners = partners.filter(p => !p.email);
                
                if (invalidPartners.length > 0) {
                    this.env.services.notification.add(
                        '部分收件人没有邮箱地址',
                        { type: 'warning' }
                    );
                    return false;
                }
            }
            
            return true;
        });
    }
    
    addCustomSendOptions() {
        // 添加自定义发送选项
        
        // 添加发送并归档选项
        this.sendControlManager.sendOptions.set('send_and_archive', {
            label: '发送并归档',
            icon: 'fa-archive',
            method: 'action_send_and_archive',
            requiresSchedule: false,
            handler: this.handleSendAndArchive.bind(this),
            priority: 5
        });
        
        // 添加发送给所有关注者选项
        this.sendControlManager.sendOptions.set('send_to_followers', {
            label: '发送给所有关注者',
            icon: 'fa-users',
            method: 'action_send_to_followers',
            requiresSchedule: false,
            handler: this.handleSendToFollowers.bind(this),
            priority: 6
        });
    }
    
    async handleSendAndArchive() {
        // 处理发送并归档
        try {
            // 先发送邮件
            const sendResult = await this.sendControlManager.handleSendNow();
            
            if (sendResult) {
                // 然后归档记录
                await this.archiveRecord();
            }
            
        } catch (error) {
            console.error('发送并归档失败:', error);
        }
    }
    
    async handleSendToFollowers() {
        // 处理发送给所有关注者
        try {
            // 获取所有关注者
            const followers = await this.getAllFollowers();
            
            // 更新收件人列表
            await this.updateRecipients(followers);
            
            // 发送邮件
            await this.sendControlManager.handleSendNow();
            
        } catch (error) {
            console.error('发送给关注者失败:', error);
        }
    }
    
    async getAllFollowers() {
        // 获取所有关注者
        const threadModel = this.props.record.data.model;
        const threadId = this.props.record.data.res_id;
        
        const followers = await this.env.services.orm.searchRead(
            'mail.followers',
            [['res_model', '=', threadModel], ['res_id', '=', threadId]],
            ['partner_id']
        );
        
        return followers.map(f => f.partner_id[0]);
    }
    
    async updateRecipients(partnerIds) {
        // 更新收件人列表
        const currentRecipients = this.props.record.data.partner_ids || [];
        const allRecipients = [...new Set([...currentRecipients, ...partnerIds])];
        
        await this.props.record.update({
            partner_ids: allRecipients
        });
    }
    
    async archiveRecord() {
        // 归档记录
        const threadModel = this.props.record.data.model;
        const threadId = this.props.record.data.res_id;
        
        await this.env.services.orm.call(
            threadModel,
            'action_archive',
            [threadId]
        );
        
        this.env.services.notification.add(
            '记录已归档',
            { type: 'success' }
        );
    }
}
```

## 邮件发送下拉菜单特性

### 1. 多种发送选项
邮件发送下拉菜单提供多种发送选项：
- **立即发送**: 立即发送邮件
- **计划发送**: 选择特定时间发送
- **保存草稿**: 保存为草稿
- **发送测试**: 发送测试邮件

### 2. 智能发送方法选择
```javascript
// 智能发送方法选择
const sendMethodSelection = {
    hasScheduledDate: (record) => {
        return record.data.scheduled_date ? 
            "action_schedule_message" : 
            "action_send_mail";
    },
    
    isNote: (record) => {
        return record.data.subtype_is_log;
    }
};
```

### 3. 记录保存验证
- **保存前验证**: 发送前自动保存记录
- **必填字段检查**: 验证必填字段完整性
- **错误处理**: 保存失败时的错误处理
- **用户反馈**: 及时的用户反馈信息

### 4. 服务集成
- **动作服务**: 执行邮件发送动作
- **对话框服务**: 管理计划发送对话框
- **ORM 服务**: 数据操作和调用
- **通知服务**: 用户通知和反馈

## 最佳实践

### 1. 记录保存
```javascript
// ✅ 推荐：发送前验证保存
async onClickSend() {
    if (await this.props.record.save()) {
        // 执行发送逻辑
    }
}
```

### 2. 方法选择
```javascript
// ✅ 推荐：智能的发送方法选择
const method = this.props.record.data.scheduled_date
    ? "action_schedule_message"
    : "action_send_mail";
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.executeSend();
} catch (error) {
    this.env.services.notification.add(
        '发送失败，请重试',
        { type: 'danger' }
    );
}
```

### 4. 用户体验
```javascript
// ✅ 推荐：良好的用户体验
this.dialogService.add(MailComposerScheduleDialog, {
    isNote: this.props.record.data.subtype_is_log,
    schedule: async (scheduledDate) => {
        // 计划发送处理
    }
});
```

## 总结

Odoo 邮件撰写发送下拉菜单模块提供了完整的邮件发送控制功能：

**核心优势**:
- **多种选项**: 提供立即发送和计划发送等多种选项
- **智能验证**: 发送前的记录保存和数据验证
- **用户友好**: 直观的下拉菜单界面
- **服务集成**: 与多个核心服务的深度集成
- **错误处理**: 完善的错误处理和用户反馈

**适用场景**:
- 邮件撰写界面
- 邮件发送控制
- 计划邮件管理
- 草稿保存功能
- 邮件测试发送

**设计优势**:
- 组件化架构
- 服务驱动设计
- 用户体验优先
- 扩展性良好

这个邮件发送下拉菜单为 Odoo Web 客户端提供了专业的邮件发送控制能力，是邮件撰写系统的重要用户界面组件。

# Odoo 计划消息组件 (Scheduled Message) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/scheduled_message.js`
**原始路径**: `/mail/static/src/chatter/web/scheduled_message.js`
**模块类型**: 核心组件 - 计划消息显示和管理
**代码行数**: 101 行
**依赖关系**:
- `@mail/core/common/attachment_list` - 附件列表组件
- `@mail/core/common/relative_time` - 相对时间组件
- `@mail/discuss/web/avatar_card/avatar_card_popover` - 头像卡片弹出框
- `@web/core/confirmation_dialog/confirmation_dialog` - 确认对话框
- `@web/core/l10n/translation` - 国际化翻译
- `@web/core/popover/popover_hook` - 弹出框钩子
- `@web/core/utils/hooks` - 核心钩子
- `@odoo/owl` - OWL 框架

## 模块功能

计划消息组件模块是 Odoo Web 客户端的计划消息显示和管理界面。该模块提供了：
- 计划消息显示
- 消息内容截断和展开
- 计划时间显示
- 附件管理
- 作者信息显示
- 消息操作功能（编辑、取消、立即发送）
- 确认对话框集成

这个组件为用户提供了完整的计划消息管理功能，支持查看、编辑和控制计划发送的消息。

## 计划消息组件架构

### 核心组件结构
```
Scheduled Message Component
├── 消息显示
│   ├── 消息内容显示
│   ├── 内容截断处理
│   ├── 展开/收起功能
│   └── 文本格式化
├── 时间管理
│   ├── 计划时间显示
│   ├── 相对时间组件
│   ├── 时间格式化
│   └── 本地化支持
├── 附件处理
│   ├── 附件列表显示
│   ├── 附件删除功能
│   ├── 附件预览
│   └── 附件下载
├── 作者信息
│   ├── 作者头像显示
│   ├── 头像卡片弹出
│   ├── 作者详情
│   └── 用户交互
└── 操作功能
    ├── 编辑消息
    ├── 取消计划
    ├── 立即发送
    └── 确认对话框
```

### 组件配置
```javascript
// 计划消息组件
class ScheduledMessage extends Component {
    static props = {
        onScheduledMessageChanged: Function,  // 消息变化回调
        scheduledMessage: Object,             // 计划消息对象
    };
    static template = "mail.ScheduledMessage";
    static components = {
        AttachmentList,     // 附件列表
        RelativeTime,       // 相对时间
    };

    setup() {
        super.setup();
        this.state = useState({
            readMore: false,    // 是否展开阅读
        });
        this.avatarCard = usePopover(AvatarCardPopover);
        this.dialogService = useService("dialog");
    }
}

// 消息截断阈值
const SCHEDULED_MESSAGE_TRUNCATE_THRESHOLD = 50; // 约1行文本
```

## 核心功能详解

### 1. 消息内容管理器
```javascript
// 消息内容管理器
class MessageContentManager {
    constructor(component) {
        this.component = component;
        this.truncateThreshold = SCHEDULED_MESSAGE_TRUNCATE_THRESHOLD;
        this.contentProcessors = new Map();
        this.setupContentProcessing();
    }

    setupContentProcessing() {
        // 设置内容处理
        this.contentProcessors.set('text', this.processTextContent.bind(this));
        this.contentProcessors.set('html', this.processHTMLContent.bind(this));
        this.contentProcessors.set('markdown', this.processMarkdownContent.bind(this));
    }

    isContentShort(content) {
        // 判断内容是否较短
        if (!content) return true;

        // 计算实际文本长度
        const textLength = this.getTextLength(content);
        return textLength < this.truncateThreshold;
    }

    getTextLength(content) {
        // 获取文本长度
        if (typeof content === 'string') {
            // 移除HTML标签计算纯文本长度
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            return tempDiv.textContent.length;
        }

        return content.textContent?.length || 0;
    }

    getTruncatedContent(content) {
        // 获取截断内容
        if (this.isContentShort(content)) {
            return content;
        }

        const textContent = this.getTextContent(content);
        return textContent.substring(0, this.truncateThreshold) + "...";
    }

    getTextContent(content) {
        // 获取纯文本内容
        if (typeof content === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            return tempDiv.textContent;
        }

        return content.textContent || '';
    }

    getFullContent(content) {
        // 获取完整内容
        return content;
    }

    processTextContent(content) {
        // 处理文本内容
        return {
            type: 'text',
            content: content,
            textContent: content,
            isShort: this.isContentShort(content),
            truncated: this.getTruncatedContent(content)
        };
    }

    processHTMLContent(content) {
        // 处理HTML内容
        const textContent = this.getTextContent(content);

        return {
            type: 'html',
            content: content,
            textContent: textContent,
            isShort: this.isContentShort(textContent),
            truncated: this.getTruncatedContent(textContent)
        };
    }

    processMarkdownContent(content) {
        // 处理Markdown内容
        // 这里可以集成Markdown解析器
        const htmlContent = this.markdownToHTML(content);
        return this.processHTMLContent(htmlContent);
    }

    markdownToHTML(markdown) {
        // Markdown转HTML（简化实现）
        let html = markdown;

        // 粗体
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // 斜体
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // 链接
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

        // 换行
        html = html.replace(/\n/g, '<br>');

        return html;
    }

    formatContentForDisplay(content, isExpanded = false) {
        // 格式化内容用于显示
        const processed = this.processContent(content);

        if (isExpanded || processed.isShort) {
            return processed.content;
        } else {
            return processed.truncated;
        }
    }

    processContent(content) {
        // 处理内容
        const contentType = this.detectContentType(content);
        const processor = this.contentProcessors.get(contentType);

        if (processor) {
            return processor(content);
        }

        // 默认按文本处理
        return this.processTextContent(content);
    }

    detectContentType(content) {
        // 检测内容类型
        if (typeof content === 'string') {
            if (content.includes('<') && content.includes('>')) {
                return 'html';
            } else if (content.includes('**') || content.includes('*') || content.includes('[')) {
                return 'markdown';
            }
        }

        return 'text';
    }

    getContentStats(content) {
        // 获取内容统计
        const processed = this.processContent(content);

        return {
            totalLength: processed.textContent.length,
            isShort: processed.isShort,
            truncateThreshold: this.truncateThreshold,
            contentType: processed.type,
            wordCount: this.getWordCount(processed.textContent),
            lineCount: this.getLineCount(processed.textContent)
        };
    }

    getWordCount(text) {
        // 获取单词数
        return text.trim().split(/\s+/).length;
    }

    getLineCount(text) {
        // 获取行数
        return text.split('\n').length;
    }

    setTruncateThreshold(threshold) {
        // 设置截断阈值
        this.truncateThreshold = threshold;
    }

    addContentProcessor(type, processor) {
        // 添加内容处理器
        this.contentProcessors.set(type, processor);
    }

    removeContentProcessor(type) {
        // 移除内容处理器
        this.contentProcessors.delete(type);
    }
}
```

### 2. 计划时间管理器
```javascript
// 计划时间管理器
class ScheduledTimeManager {
    constructor() {
        this.timeFormats = new Map();
        this.timeZoneHandler = null;
        this.setupTimeFormats();
    }

    setupTimeFormats() {
        // 设置时间格式
        this.timeFormats.set('short', luxon.DateTime.DATETIME_SHORT);
        this.timeFormats.set('medium', luxon.DateTime.DATETIME_MED);
        this.timeFormats.set('full', luxon.DateTime.DATETIME_FULL);
        this.timeFormats.set('custom', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric'
        });
    }

    formatScheduledDate(scheduledDate, format = 'short') {
        // 格式化计划日期
        if (!scheduledDate) {
            return '';
        }

        const dateTime = this.ensureDateTime(scheduledDate);
        const formatOptions = this.timeFormats.get(format);

        if (formatOptions) {
            return dateTime.toLocaleString(formatOptions);
        }

        return dateTime.toLocaleString();
    }

    ensureDateTime(date) {
        // 确保是DateTime对象
        if (luxon.DateTime.isDateTime(date)) {
            return date;
        }

        if (typeof date === 'string') {
            return luxon.DateTime.fromISO(date);
        }

        if (date instanceof Date) {
            return luxon.DateTime.fromJSDate(date);
        }

        return luxon.DateTime.now();
    }

    getTimeUntilScheduled(scheduledDate) {
        // 获取距离计划时间的时长
        const now = luxon.DateTime.now();
        const scheduled = this.ensureDateTime(scheduledDate);

        const diff = scheduled.diff(now);

        if (diff.milliseconds < 0) {
            return {
                isPast: true,
                message: '已过期',
                duration: null
            };
        }

        const duration = diff.shiftTo('days', 'hours', 'minutes');

        return {
            isPast: false,
            message: this.formatDuration(duration),
            duration: duration
        };
    }

    formatDuration(duration) {
        // 格式化时长
        const days = Math.floor(duration.days);
        const hours = Math.floor(duration.hours);
        const minutes = Math.floor(duration.minutes);

        if (days > 0) {
            return `${days}天${hours}小时后`;
        } else if (hours > 0) {
            return `${hours}小时${minutes}分钟后`;
        } else if (minutes > 0) {
            return `${minutes}分钟后`;
        } else {
            return '即将发送';
        }
    }

    isScheduledSoon(scheduledDate, threshold = 60) {
        // 判断是否即将发送（默认60分钟内）
        const timeInfo = this.getTimeUntilScheduled(scheduledDate);

        if (timeInfo.isPast) {
            return false;
        }

        const totalMinutes = timeInfo.duration.as('minutes');
        return totalMinutes <= threshold;
    }

    isScheduledOverdue(scheduledDate) {
        // 判断是否已过期
        const timeInfo = this.getTimeUntilScheduled(scheduledDate);
        return timeInfo.isPast;
    }

    getScheduleStatus(scheduledDate) {
        // 获取计划状态
        const timeInfo = this.getTimeUntilScheduled(scheduledDate);

        if (timeInfo.isPast) {
            return {
                status: 'overdue',
                message: '已过期',
                class: 'text-danger',
                icon: 'fa-exclamation-triangle'
            };
        }

        if (this.isScheduledSoon(scheduledDate, 60)) {
            return {
                status: 'soon',
                message: timeInfo.message,
                class: 'text-warning',
                icon: 'fa-clock-o'
            };
        }

        return {
            status: 'scheduled',
            message: timeInfo.message,
            class: 'text-info',
            icon: 'fa-calendar'
        };
    }

    addTimeFormat(name, format) {
        // 添加时间格式
        this.timeFormats.set(name, format);
    }

    removeTimeFormat(name) {
        // 移除时间格式
        this.timeFormats.delete(name);
    }

    getAvailableFormats() {
        // 获取可用格式
        return Array.from(this.timeFormats.keys());
    }
}
```

### 3. 消息操作管理器
```javascript
// 消息操作管理器
class MessageActionManager {
    constructor(component) {
        this.component = component;
        this.actionHandlers = new Map();
        this.confirmationDialogs = new Map();
        this.setupActionHandlers();
    }

    setupActionHandlers() {
        // 设置操作处理器
        this.actionHandlers.set('edit', this.handleEdit.bind(this));
        this.actionHandlers.set('cancel', this.handleCancel.bind(this));
        this.actionHandlers.set('send_now', this.handleSendNow.bind(this));
        this.actionHandlers.set('duplicate', this.handleDuplicate.bind(this));
        this.actionHandlers.set('reschedule', this.handleReschedule.bind(this));

        // 设置确认对话框配置
        this.setupConfirmationDialogs();
    }

    setupConfirmationDialogs() {
        // 设置确认对话框
        this.confirmationDialogs.set('cancel', {
            title: '取消计划消息',
            body: '确定要取消这条计划消息吗？',
            confirmLabel: '取消消息',
            cancelLabel: '关闭',
            confirmClass: 'btn-danger'
        });

        this.confirmationDialogs.set('send_now', {
            title: '立即发送',
            body: '确定要立即发送这条消息吗？',
            confirmLabel: '立即发送',
            cancelLabel: '取消',
            confirmClass: 'btn-primary'
        });

        this.confirmationDialogs.set('delete', {
            title: '删除消息',
            body: '确定要删除这条计划消息吗？此操作不可撤销。',
            confirmLabel: '删除',
            cancelLabel: '取消',
            confirmClass: 'btn-danger'
        });
    }

    async handleEdit() {
        // 处理编辑操作
        try {
            const scheduledMessage = this.component.props.scheduledMessage;

            // 调用编辑方法
            await scheduledMessage.edit();

            // 触发变化回调
            this.component.props.onScheduledMessageChanged(scheduledMessage.thread);

            // 显示成功通知
            this.component.env.services.notification.add(
                '消息编辑成功',
                { type: 'success' }
            );

        } catch (error) {
            console.error('编辑消息失败:', error);
            this.component.env.services.notification.add(
                '编辑消息失败',
                { type: 'danger' }
            );
        }
    }

    async handleCancel() {
        // 处理取消操作
        const dialogConfig = this.confirmationDialogs.get('cancel');

        this.component.dialogService.add(ConfirmationDialog, {
            title: dialogConfig.title,
            body: dialogConfig.body,
            confirmLabel: dialogConfig.confirmLabel,
            cancelLabel: dialogConfig.cancelLabel,
            confirm: async () => {
                try {
                    const scheduledMessage = this.component.props.scheduledMessage;
                    const thread = scheduledMessage.thread;

                    // 取消计划消息
                    await scheduledMessage.cancel();

                    // 触发变化回调
                    this.component.props.onScheduledMessageChanged(thread);

                    // 显示成功通知
                    this.component.env.services.notification.add(
                        '计划消息已取消',
                        { type: 'success' }
                    );

                } catch (error) {
                    console.error('取消消息失败:', error);
                    this.component.env.services.notification.add(
                        '取消消息失败',
                        { type: 'danger' }
                    );
                }
            },
            cancel: () => {
                // 用户取消操作
            }
        });
    }

    async handleSendNow() {
        // 处理立即发送操作
        const dialogConfig = this.confirmationDialogs.get('send_now');

        this.component.dialogService.add(ConfirmationDialog, {
            title: dialogConfig.title,
            body: dialogConfig.body,
            confirmLabel: dialogConfig.confirmLabel,
            cancelLabel: dialogConfig.cancelLabel,
            confirm: async () => {
                try {
                    const scheduledMessage = this.component.props.scheduledMessage;

                    // 立即发送消息
                    await scheduledMessage.send();

                    // 触发变化回调
                    this.component.props.onScheduledMessageChanged(scheduledMessage.thread);

                    // 显示成功通知
                    this.component.env.services.notification.add(
                        '消息已发送',
                        { type: 'success' }
                    );

                } catch (error) {
                    console.error('发送消息失败:', error);
                    this.component.env.services.notification.add(
                        '发送消息失败',
                        { type: 'danger' }
                    );
                }
            },
            cancel: () => {
                // 用户取消操作
            }
        });
    }

    async handleDuplicate() {
        // 处理复制操作
        try {
            const scheduledMessage = this.component.props.scheduledMessage;

            // 复制消息数据
            const duplicateData = {
                subject: scheduledMessage.subject,
                body: scheduledMessage.body,
                partner_ids: scheduledMessage.partner_ids,
                attachment_ids: scheduledMessage.attachment_ids
            };

            // 创建新的撰写器
            this.component.env.services.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'mail.compose.message',
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'new',
                context: {
                    default_composition_mode: 'comment',
                    default_model: scheduledMessage.thread.model,
                    default_res_id: scheduledMessage.thread.id,
                    ...duplicateData
                }
            });

        } catch (error) {
            console.error('复制消息失败:', error);
            this.component.env.services.notification.add(
                '复制消息失败',
                { type: 'danger' }
            );
        }
    }

    async handleReschedule() {
        // 处理重新计划操作
        try {
            const scheduledMessage = this.component.props.scheduledMessage;

            // 打开计划对话框
            this.component.env.services.dialog.add(MailComposerScheduleDialog, {
                isNote: scheduledMessage.subtype_is_log,
                schedule: async (newScheduledDate) => {
                    try {
                        // 更新计划时间
                        await scheduledMessage.reschedule(newScheduledDate);

                        // 触发变化回调
                        this.component.props.onScheduledMessageChanged(scheduledMessage.thread);

                        // 显示成功通知
                        this.component.env.services.notification.add(
                            '计划时间已更新',
                            { type: 'success' }
                        );

                    } catch (error) {
                        console.error('重新计划失败:', error);
                        this.component.env.services.notification.add(
                            '重新计划失败',
                            { type: 'danger' }
                        );
                    }
                }
            });

        } catch (error) {
            console.error('打开重新计划对话框失败:', error);
        }
    }

    async executeAction(actionName) {
        // 执行操作
        const handler = this.actionHandlers.get(actionName);

        if (handler) {
            await handler();
        } else {
            console.warn(`未知的操作: ${actionName}`);
        }
    }

    addActionHandler(actionName, handler) {
        // 添加操作处理器
        this.actionHandlers.set(actionName, handler);
    }

    removeActionHandler(actionName) {
        // 移除操作处理器
        this.actionHandlers.delete(actionName);
    }

    addConfirmationDialog(actionName, config) {
        // 添加确认对话框配置
        this.confirmationDialogs.set(actionName, config);
    }

    removeConfirmationDialog(actionName) {
        // 移除确认对话框配置
        this.confirmationDialogs.delete(actionName);
    }

    getAvailableActions() {
        // 获取可用操作
        return Array.from(this.actionHandlers.keys());
    }
}
```
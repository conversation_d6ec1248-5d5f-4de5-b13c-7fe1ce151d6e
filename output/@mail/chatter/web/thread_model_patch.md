# Odoo 线程模型补丁 (Thread Model Patch) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/thread_model_patch.js`
**原始路径**: `/mail/static/src/chatter/web/thread_model_patch.js`
**模块类型**: 核心补丁 - 线程模型扩展
**代码行数**: 36 行
**依赖关系**:
- `@mail/core/common/record` - 记录基类
- `@mail/core/common/thread_model` - 线程模型
- `@mail/chatter/web_portal/thread_model_patch` - Web Portal 线程模型补丁
- `@web/core/utils/patch` - 补丁工具

## 模块功能

线程模型补丁模块是 Odoo Web 客户端线程模型的功能扩展。该模块提供了：
- 计划消息关系管理
- 计划消息排序逻辑
- 附件数据获取增强
- 主附件自动设置
- 加载状态管理
- 数据获取优化

这个补丁通过扩展基础线程模型，为 Web 端提供了计划消息和附件管理的增强功能。

## 线程模型补丁架构

### 核心组件结构
```
Thread Model Patch
├── 计划消息管理
│   ├── scheduledMessages 关系
│   ├── 排序逻辑
│   ├── 反向关系
│   └── 数据同步
├── 附件管理增强
│   ├── 附件加载状态
│   ├── 主附件设置
│   ├── 附件视图过滤
│   └── 自动选择逻辑
├── 数据获取优化
│   ├── fetchData 方法扩展
│   ├── 请求列表处理
│   ├── 加载状态管理
│   └── 数据完整性保证
└── 继承链管理
    ├── Web Portal 补丁继承
    ├── 基础模型扩展
    ├── 方法重写
    └── 功能增强
```

### 补丁配置
```javascript
// 线程模型补丁
patch(Thread.prototype, {
    setup() {
        super.setup();

        // 计划消息关系配置
        this.scheduledMessages = Record.many("ScheduledMessage", {
            sort: (a, b) => {
                // 按计划日期排序，相同日期按ID排序
                if (a.scheduled_date === b.scheduled_date) {
                    return a.id - b.id;
                }
                return a.scheduled_date < b.scheduled_date ? -1 : 1;
            },
            inverse: "thread",  // 反向关系
        });
    },

    async fetchData(requestList) {
        // 附件加载状态管理
        this.isLoadingAttachments =
            this.isLoadingAttachments || requestList.includes("attachments");

        // 调用父类方法
        await super.fetchData(requestList);

        // 自动设置主附件
        if (!this.mainAttachment && this.attachmentsInWebClientView.length > 0) {
            this.setMainAttachmentFromIndex(0);
        }
    }
});
```

## 核心功能详解

### 1. 计划消息管理器
```javascript
// 计划消息管理器
class ScheduledMessageManager {
    constructor(thread) {
        this.thread = thread;
        this.sortingStrategies = new Map();
        this.filterStrategies = new Map();
        this.setupMessageManagement();
    }

    setupMessageManagement() {
        // 设置消息管理
        this.setupSortingStrategies();
        this.setupFilterStrategies();
        this.setupEventListeners();
    }

    setupSortingStrategies() {
        // 设置排序策略

        // 按计划日期排序（默认）
        this.sortingStrategies.set('scheduled_date', (a, b) => {
            if (a.scheduled_date === b.scheduled_date) {
                return a.id - b.id;
            }
            return a.scheduled_date < b.scheduled_date ? -1 : 1;
        });

        // 按创建时间排序
        this.sortingStrategies.set('created_date', (a, b) => {
            if (a.created_date === b.created_date) {
                return a.id - b.id;
            }
            return a.created_date < b.created_date ? -1 : 1;
        });

        // 按优先级排序
        this.sortingStrategies.set('priority', (a, b) => {
            const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
            const aPriority = priorityOrder[a.priority] || 1;
            const bPriority = priorityOrder[b.priority] || 1;

            if (aPriority === bPriority) {
                return a.scheduled_date < b.scheduled_date ? -1 : 1;
            }
            return bPriority - aPriority; // 高优先级在前
        });

        // 按作者排序
        this.sortingStrategies.set('author', (a, b) => {
            const aAuthor = a.author?.name || '';
            const bAuthor = b.author?.name || '';

            if (aAuthor === bAuthor) {
                return a.scheduled_date < b.scheduled_date ? -1 : 1;
            }
            return aAuthor.localeCompare(bAuthor);
        });
    }

    setupFilterStrategies() {
        // 设置过滤策略

        // 即将发送的消息（24小时内）
        this.filterStrategies.set('upcoming', (message) => {
            const now = luxon.DateTime.now();
            const scheduled = luxon.DateTime.fromISO(message.scheduled_date);
            const diff = scheduled.diff(now, 'hours').hours;
            return diff > 0 && diff <= 24;
        });

        // 已过期的消息
        this.filterStrategies.set('overdue', (message) => {
            const now = luxon.DateTime.now();
            const scheduled = luxon.DateTime.fromISO(message.scheduled_date);
            return scheduled < now;
        });

        // 本周的消息
        this.filterStrategies.set('this_week', (message) => {
            const now = luxon.DateTime.now();
            const scheduled = luxon.DateTime.fromISO(message.scheduled_date);
            const startOfWeek = now.startOf('week');
            const endOfWeek = now.endOf('week');
            return scheduled >= startOfWeek && scheduled <= endOfWeek;
        });

        // 按作者过滤
        this.filterStrategies.set('by_author', (message, authorId) => {
            return message.author?.id === authorId;
        });

        // 有附件的消息
        this.filterStrategies.set('with_attachments', (message) => {
            return message.attachment_ids && message.attachment_ids.length > 0;
        });
    }

    setupEventListeners() {
        // 设置事件监听器
        this.thread.env?.bus?.addEventListener('SCHEDULED_MESSAGE_ADDED', this.onMessageAdded.bind(this));
        this.thread.env?.bus?.addEventListener('SCHEDULED_MESSAGE_REMOVED', this.onMessageRemoved.bind(this));
        this.thread.env?.bus?.addEventListener('SCHEDULED_MESSAGE_UPDATED', this.onMessageUpdated.bind(this));
    }

    onMessageAdded(event) {
        // 消息添加处理
        const message = event.detail.message;
        console.log('计划消息已添加:', message.id);

        // 重新排序
        this.sortMessages();

        // 触发更新事件
        this.triggerUpdate('message_added', message);
    }

    onMessageRemoved(event) {
        // 消息移除处理
        const messageId = event.detail.messageId;
        console.log('计划消息已移除:', messageId);

        // 触发更新事件
        this.triggerUpdate('message_removed', { id: messageId });
    }

    onMessageUpdated(event) {
        // 消息更新处理
        const message = event.detail.message;
        console.log('计划消息已更新:', message.id);

        // 重新排序（如果计划日期改变）
        this.sortMessages();

        // 触发更新事件
        this.triggerUpdate('message_updated', message);
    }

    sortMessages(strategy = 'scheduled_date') {
        // 排序消息
        const sortFunction = this.sortingStrategies.get(strategy);

        if (sortFunction && this.thread.scheduledMessages) {
            this.thread.scheduledMessages.sort(sortFunction);
        }
    }

    filterMessages(strategy, ...args) {
        // 过滤消息
        const filterFunction = this.filterStrategies.get(strategy);

        if (filterFunction && this.thread.scheduledMessages) {
            return this.thread.scheduledMessages.filter(message =>
                filterFunction(message, ...args)
            );
        }

        return [];
    }

    getMessagesByStatus() {
        // 按状态获取消息
        const now = luxon.DateTime.now();
        const messages = this.thread.scheduledMessages || [];

        const categorized = {
            upcoming: [],
            overdue: [],
            today: [],
            this_week: [],
            later: []
        };

        messages.forEach(message => {
            const scheduled = luxon.DateTime.fromISO(message.scheduled_date);
            const diff = scheduled.diff(now);

            if (scheduled < now) {
                categorized.overdue.push(message);
            } else if (scheduled.hasSame(now, 'day')) {
                categorized.today.push(message);
            } else if (diff.as('hours') <= 24) {
                categorized.upcoming.push(message);
            } else if (scheduled.hasSame(now, 'week')) {
                categorized.this_week.push(message);
            } else {
                categorized.later.push(message);
            }
        });

        return categorized;
    }

    getMessageStats() {
        // 获取消息统计
        const messages = this.thread.scheduledMessages || [];
        const categorized = this.getMessagesByStatus();

        return {
            total: messages.length,
            overdue: categorized.overdue.length,
            today: categorized.today.length,
            upcoming: categorized.upcoming.length,
            this_week: categorized.this_week.length,
            later: categorized.later.length,
            with_attachments: this.filterMessages('with_attachments').length
        };
    }

    triggerUpdate(eventType, data) {
        // 触发更新事件
        this.thread.env?.bus?.trigger('THREAD_SCHEDULED_MESSAGES_UPDATED', {
            threadId: this.thread.id,
            eventType: eventType,
            data: data,
            stats: this.getMessageStats()
        });
    }

    addSortingStrategy(name, sortFunction) {
        // 添加排序策略
        this.sortingStrategies.set(name, sortFunction);
    }

    addFilterStrategy(name, filterFunction) {
        // 添加过滤策略
        this.filterStrategies.set(name, filterFunction);
    }

    removeSortingStrategy(name) {
        // 移除排序策略
        this.sortingStrategies.delete(name);
    }

    removeFilterStrategy(name) {
        // 移除过滤策略
        this.filterStrategies.delete(name);
    }
}
```

### 2. 附件管理增强器
```javascript
// 附件管理增强器
class AttachmentEnhancer {
    constructor(thread) {
        this.thread = thread;
        this.attachmentStrategies = new Map();
        this.loadingStates = new Map();
        this.setupAttachmentEnhancement();
    }

    setupAttachmentEnhancement() {
        // 设置附件增强
        this.setupMainAttachmentStrategies();
        this.setupLoadingStateManagement();
        this.setupAttachmentFiltering();
    }

    setupMainAttachmentStrategies() {
        // 设置主附件选择策略

        // 按文件类型优先级选择
        this.attachmentStrategies.set('by_type_priority', (attachments) => {
            const typePriority = {
                'image': 5,
                'pdf': 4,
                'document': 3,
                'spreadsheet': 2,
                'other': 1
            };

            return attachments.reduce((best, current) => {
                const currentPriority = this.getAttachmentTypePriority(current, typePriority);
                const bestPriority = best ? this.getAttachmentTypePriority(best, typePriority) : 0;

                return currentPriority > bestPriority ? current : best;
            }, null);
        });

        // 按文件大小选择（最大的）
        this.attachmentStrategies.set('by_size_largest', (attachments) => {
            return attachments.reduce((largest, current) => {
                return (!largest || current.file_size > largest.file_size) ? current : largest;
            }, null);
        });

        // 按文件大小选择（最小的）
        this.attachmentStrategies.set('by_size_smallest', (attachments) => {
            return attachments.reduce((smallest, current) => {
                return (!smallest || current.file_size < smallest.file_size) ? current : smallest;
            }, null);
        });

        // 按创建时间选择（最新的）
        this.attachmentStrategies.set('by_date_newest', (attachments) => {
            return attachments.reduce((newest, current) => {
                const currentDate = luxon.DateTime.fromISO(current.create_date);
                const newestDate = newest ? luxon.DateTime.fromISO(newest.create_date) : luxon.DateTime.fromMillis(0);

                return currentDate > newestDate ? current : newest;
            }, null);
        });

        // 按文件名选择（字母顺序第一个）
        this.attachmentStrategies.set('by_name_first', (attachments) => {
            return attachments.reduce((first, current) => {
                return (!first || current.name.localeCompare(first.name) < 0) ? current : first;
            }, null);
        });
    }

    setupLoadingStateManagement() {
        // 设置加载状态管理
        this.loadingStates.set('attachments', false);
        this.loadingStates.set('main_attachment', false);
        this.loadingStates.set('attachment_preview', false);
    }

    setupAttachmentFiltering() {
        // 设置附件过滤
        this.attachmentFilters = {
            webClientView: (attachment) => {
                // Web客户端视图中显示的附件
                return attachment.access_token || attachment.public || attachment.res_model === this.thread.model;
            },

            previewable: (attachment) => {
                // 可预览的附件
                const previewableTypes = ['image/', 'application/pdf', 'text/'];
                return previewableTypes.some(type => attachment.mimetype?.startsWith(type));
            },

            downloadable: (attachment) => {
                // 可下载的附件
                return attachment.access_token || attachment.public;
            },

            editable: (attachment) => {
                // 可编辑的附件
                const editableTypes = ['text/', 'application/json', 'application/xml'];
                return editableTypes.some(type => attachment.mimetype?.startsWith(type));
            }
        };
    }

    getAttachmentTypePriority(attachment, typePriority) {
        // 获取附件类型优先级
        const mimetype = attachment.mimetype || '';

        if (mimetype.startsWith('image/')) {
            return typePriority.image;
        } else if (mimetype === 'application/pdf') {
            return typePriority.pdf;
        } else if (mimetype.startsWith('application/vnd.openxmlformats') ||
                   mimetype.startsWith('application/msword')) {
            return typePriority.document;
        } else if (mimetype.startsWith('application/vnd.ms-excel') ||
                   mimetype.includes('spreadsheet')) {
            return typePriority.spreadsheet;
        } else {
            return typePriority.other;
        }
    }

    enhanceFetchData(originalFetchData) {
        // 增强数据获取方法
        return async function(requestList) {
            // 设置加载状态
            if (requestList.includes("attachments")) {
                this.isLoadingAttachments = true;
                this.attachmentEnhancer?.setLoadingState('attachments', true);
            }

            try {
                // 调用原始方法
                await originalFetchData.call(this, requestList);

                // 处理附件相关的后续操作
                if (requestList.includes("attachments")) {
                    await this.attachmentEnhancer?.processAttachmentsAfterFetch();
                }

            } finally {
                // 清除加载状态
                if (requestList.includes("attachments")) {
                    this.isLoadingAttachments = false;
                    this.attachmentEnhancer?.setLoadingState('attachments', false);
                }
            }
        };
    }

    async processAttachmentsAfterFetch() {
        // 获取附件后的处理

        // 自动设置主附件
        await this.autoSetMainAttachment();

        // 预加载附件预览
        await this.preloadAttachmentPreviews();

        // 更新附件统计
        this.updateAttachmentStats();
    }

    async autoSetMainAttachment(strategy = 'by_type_priority') {
        // 自动设置主附件
        const attachments = this.getFilteredAttachments('webClientView');

        if (!this.thread.mainAttachment && attachments.length > 0) {
            const strategyFunction = this.attachmentStrategies.get(strategy);

            if (strategyFunction) {
                const selectedAttachment = strategyFunction(attachments);
                if (selectedAttachment) {
                    const index = attachments.indexOf(selectedAttachment);
                    this.thread.setMainAttachmentFromIndex(index);
                }
            } else {
                // 默认选择第一个
                this.thread.setMainAttachmentFromIndex(0);
            }
        }
    }

    async preloadAttachmentPreviews() {
        // 预加载附件预览
        const previewableAttachments = this.getFilteredAttachments('previewable');

        // 限制预加载数量
        const maxPreload = 3;
        const toPreload = previewableAttachments.slice(0, maxPreload);

        for (const attachment of toPreload) {
            try {
                await this.preloadAttachmentPreview(attachment);
            } catch (error) {
                console.warn('预加载附件预览失败:', attachment.name, error);
            }
        }
    }

    async preloadAttachmentPreview(attachment) {
        // 预加载单个附件预览
        if (attachment.mimetype?.startsWith('image/')) {
            // 预加载图片
            const img = new Image();
            img.src = `/web/content/${attachment.id}`;

            return new Promise((resolve, reject) => {
                img.onload = resolve;
                img.onerror = reject;
                setTimeout(reject, 5000); // 5秒超时
            });
        }
    }

    getFilteredAttachments(filterName) {
        // 获取过滤后的附件
        const filter = this.attachmentFilters[filterName];
        const attachments = this.thread.attachmentsInWebClientView || [];

        return filter ? attachments.filter(filter) : attachments;
    }

    updateAttachmentStats() {
        // 更新附件统计
        const attachments = this.thread.attachmentsInWebClientView || [];

        const stats = {
            total: attachments.length,
            byType: {},
            totalSize: 0,
            previewable: 0,
            downloadable: 0
        };

        attachments.forEach(attachment => {
            // 按类型统计
            const type = this.getAttachmentType(attachment);
            stats.byType[type] = (stats.byType[type] || 0) + 1;

            // 总大小
            stats.totalSize += attachment.file_size || 0;

            // 可预览数量
            if (this.attachmentFilters.previewable(attachment)) {
                stats.previewable++;
            }

            // 可下载数量
            if (this.attachmentFilters.downloadable(attachment)) {
                stats.downloadable++;
            }
        });

        this.thread.attachmentStats = stats;
    }

    getAttachmentType(attachment) {
        // 获取附件类型
        const mimetype = attachment.mimetype || '';

        if (mimetype.startsWith('image/')) return 'image';
        if (mimetype === 'application/pdf') return 'pdf';
        if (mimetype.startsWith('text/')) return 'text';
        if (mimetype.startsWith('application/vnd.openxmlformats')) return 'office';
        if (mimetype.startsWith('application/')) return 'application';

        return 'other';
    }

    setLoadingState(type, isLoading) {
        // 设置加载状态
        this.loadingStates.set(type, isLoading);

        // 触发状态变化事件
        this.thread.env?.bus?.trigger('ATTACHMENT_LOADING_STATE_CHANGED', {
            threadId: this.thread.id,
            type: type,
            isLoading: isLoading
        });
    }

    getLoadingState(type) {
        // 获取加载状态
        return this.loadingStates.get(type) || false;
    }

    addAttachmentStrategy(name, strategyFunction) {
        // 添加附件策略
        this.attachmentStrategies.set(name, strategyFunction);
    }

    removeAttachmentStrategy(name) {
        // 移除附件策略
        this.attachmentStrategies.delete(name);
    }

    addAttachmentFilter(name, filterFunction) {
        // 添加附件过滤器
        this.attachmentFilters[name] = filterFunction;
    }

    removeAttachmentFilter(name) {
        // 移除附件过滤器
        delete this.attachmentFilters[name];
    }
}
```

## 使用示例

### 1. 基本线程模型补丁使用
```javascript
// 使用扩展的线程模型
class EnhancedThreadManager extends Component {
    setup() {
        this.thread = this.props.thread;
        this.scheduledMessageManager = new ScheduledMessageManager(this.thread);
        this.attachmentEnhancer = new AttachmentEnhancer(this.thread);

        this.setupThreadEnhancements();
    }

    setupThreadEnhancements() {
        // 设置线程增强功能

        // 监听计划消息变化
        this.env.bus.addEventListener('THREAD_SCHEDULED_MESSAGES_UPDATED',
            this.onScheduledMessagesUpdated.bind(this));

        // 监听附件加载状态变化
        this.env.bus.addEventListener('ATTACHMENT_LOADING_STATE_CHANGED',
            this.onAttachmentLoadingStateChanged.bind(this));
    }

    onScheduledMessagesUpdated(event) {
        // 计划消息更新处理
        const { threadId, eventType, data, stats } = event.detail;

        if (threadId === this.thread.id) {
            console.log(`线程 ${threadId} 计划消息更新:`, eventType, stats);

            // 更新UI
            this.updateScheduledMessagesUI(stats);
        }
    }

    onAttachmentLoadingStateChanged(event) {
        // 附件加载状态变化处理
        const { threadId, type, isLoading } = event.detail;

        if (threadId === this.thread.id) {
            console.log(`线程 ${threadId} 附件加载状态:`, type, isLoading);

            // 更新加载指示器
            this.updateLoadingIndicator(type, isLoading);
        }
    }

    updateScheduledMessagesUI(stats) {
        // 更新计划消息UI
        const badge = document.querySelector('.scheduled-messages-badge');
        if (badge) {
            badge.textContent = stats.total;
            badge.classList.toggle('has-overdue', stats.overdue > 0);
        }
    }

    updateLoadingIndicator(type, isLoading) {
        // 更新加载指示器
        const indicator = document.querySelector(`.loading-${type}`);
        if (indicator) {
            indicator.style.display = isLoading ? 'block' : 'none';
        }
    }

    async loadThreadData() {
        // 加载线程数据
        try {
            await this.thread.fetchData([
                'messages',
                'attachments',
                'scheduledMessages',
                'activities',
                'followers'
            ]);

            console.log('线程数据加载完成');

        } catch (error) {
            console.error('加载线程数据失败:', error);
        }
    }

    getScheduledMessagesByStatus() {
        // 按状态获取计划消息
        return this.scheduledMessageManager.getMessagesByStatus();
    }

    getAttachmentStats() {
        // 获取附件统计
        return this.thread.attachmentStats || {};
    }
}
```

### 2. 自定义排序和过滤
```javascript
// 自定义计划消息管理
class CustomScheduledMessageManager extends Component {
    setup() {
        this.thread = this.props.thread;
        this.messageManager = new ScheduledMessageManager(this.thread);

        this.addCustomStrategies();
    }

    addCustomStrategies() {
        // 添加自定义策略

        // 自定义排序：按紧急程度
        this.messageManager.addSortingStrategy('urgency', (a, b) => {
            const now = luxon.DateTime.now();
            const aUrgency = this.calculateUrgency(a, now);
            const bUrgency = this.calculateUrgency(b, now);

            return bUrgency - aUrgency; // 高紧急度在前
        });

        // 自定义过滤：重要消息
        this.messageManager.addFilterStrategy('important', (message) => {
            return message.subject?.includes('[重要]') ||
                   message.subject?.includes('[紧急]') ||
                   message.priority === 'high';
        });

        // 自定义过滤：需要审批的消息
        this.messageManager.addFilterStrategy('needs_approval', (message) => {
            return message.body?.includes('需要审批') ||
                   message.body?.includes('请审核');
        });
    }

    calculateUrgency(message, now) {
        // 计算紧急程度
        const scheduled = luxon.DateTime.fromISO(message.scheduled_date);
        const hoursUntil = scheduled.diff(now, 'hours').hours;

        // 基础紧急度
        let urgency = 0;

        // 时间因素
        if (hoursUntil < 1) {
            urgency += 100; // 1小时内
        } else if (hoursUntil < 6) {
            urgency += 50;  // 6小时内
        } else if (hoursUntil < 24) {
            urgency += 20;  // 24小时内
        }

        // 优先级因素
        const priorityScore = {
            'high': 30,
            'medium': 10,
            'low': 0
        };
        urgency += priorityScore[message.priority] || 0;

        // 关键词因素
        const urgentKeywords = ['紧急', '重要', '立即', '马上'];
        const hasUrgentKeyword = urgentKeywords.some(keyword =>
            message.subject?.includes(keyword) || message.body?.includes(keyword)
        );

        if (hasUrgentKeyword) {
            urgency += 25;
        }

        return urgency;
    }

    getUrgentMessages() {
        // 获取紧急消息
        const messages = this.thread.scheduledMessages || [];
        return messages.filter(message => this.calculateUrgency(message, luxon.DateTime.now()) > 50);
    }

    getImportantMessages() {
        // 获取重要消息
        return this.messageManager.filterMessages('important');
    }

    getMessagesNeedingApproval() {
        // 获取需要审批的消息
        return this.messageManager.filterMessages('needs_approval');
    }
}
```

### 3. 高级附件管理
```javascript
// 高级附件管理
class AdvancedAttachmentManager extends Component {
    setup() {
        this.thread = this.props.thread;
        this.attachmentEnhancer = new AttachmentEnhancer(this.thread);

        this.setupAdvancedFeatures();
    }

    setupAdvancedFeatures() {
        // 设置高级功能

        // 添加智能主附件选择策略
        this.attachmentEnhancer.addAttachmentStrategy('smart_selection', (attachments) => {
            return this.selectMainAttachmentIntelligently(attachments);
        });

        // 添加业务相关附件过滤器
        this.attachmentEnhancer.addAttachmentFilter('business_documents', (attachment) => {
            const businessTypes = [
                'application/pdf',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.openxmlformats-officedocument.presentationml.presentation'
            ];

            return businessTypes.includes(attachment.mimetype);
        });

        // 添加大文件过滤器
        this.attachmentEnhancer.addAttachmentFilter('large_files', (attachment) => {
            const maxSize = 10 * 1024 * 1024; // 10MB
            return attachment.file_size > maxSize;
        });
    }

    selectMainAttachmentIntelligently(attachments) {
        // 智能选择主附件

        // 优先级规则
        const rules = [
            // 1. 优先选择PDF文档
            (attachments) => attachments.find(a => a.mimetype === 'application/pdf'),

            // 2. 优先选择图片（如果没有PDF）
            (attachments) => attachments.find(a => a.mimetype?.startsWith('image/')),

            // 3. 优先选择Office文档
            (attachments) => attachments.find(a =>
                a.mimetype?.startsWith('application/vnd.openxmlformats')),

            // 4. 优先选择最大的文件
            (attachments) => attachments.reduce((largest, current) =>
                (!largest || current.file_size > largest.file_size) ? current : largest, null),

            // 5. 默认选择第一个
            (attachments) => attachments[0]
        ];

        for (const rule of rules) {
            const selected = rule(attachments);
            if (selected) {
                return selected;
            }
        }

        return null;
    }

    async optimizeAttachmentLoading() {
        // 优化附件加载
        const attachments = this.thread.attachmentsInWebClientView || [];

        // 按优先级排序附件
        const prioritized = this.prioritizeAttachments(attachments);

        // 分批加载
        const batchSize = 3;
        for (let i = 0; i < prioritized.length; i += batchSize) {
            const batch = prioritized.slice(i, i + batchSize);

            // 并行加载当前批次
            await Promise.all(batch.map(attachment =>
                this.preloadAttachment(attachment)
            ));

            // 短暂延迟避免阻塞UI
            await this.delay(100);
        }
    }

    prioritizeAttachments(attachments) {
        // 附件优先级排序
        return attachments.sort((a, b) => {
            // 图片优先
            const aIsImage = a.mimetype?.startsWith('image/');
            const bIsImage = b.mimetype?.startsWith('image/');

            if (aIsImage && !bIsImage) return -1;
            if (!aIsImage && bIsImage) return 1;

            // PDF其次
            const aIsPdf = a.mimetype === 'application/pdf';
            const bIsPdf = b.mimetype === 'application/pdf';

            if (aIsPdf && !bIsPdf) return -1;
            if (!aIsPdf && bIsPdf) return 1;

            // 按文件大小排序（小文件优先）
            return a.file_size - b.file_size;
        });
    }

    async preloadAttachment(attachment) {
        // 预加载附件
        try {
            if (attachment.mimetype?.startsWith('image/')) {
                await this.preloadImage(attachment);
            } else if (attachment.mimetype === 'application/pdf') {
                await this.preloadPdf(attachment);
            }
        } catch (error) {
            console.warn('预加载附件失败:', attachment.name, error);
        }
    }

    async preloadImage(attachment) {
        // 预加载图片
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = resolve;
            img.onerror = reject;
            img.src = `/web/content/${attachment.id}`;

            // 5秒超时
            setTimeout(() => reject(new Error('加载超时')), 5000);
        });
    }

    async preloadPdf(attachment) {
        // 预加载PDF（获取第一页缩略图）
        try {
            const response = await fetch(`/web/content/${attachment.id}?download=false`);
            if (response.ok) {
                // PDF预加载成功
                console.log('PDF预加载成功:', attachment.name);
            }
        } catch (error) {
            console.warn('PDF预加载失败:', attachment.name, error);
        }
    }

    delay(ms) {
        // 延迟函数
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getAttachmentInsights() {
        // 获取附件洞察
        const attachments = this.thread.attachmentsInWebClientView || [];
        const stats = this.thread.attachmentStats || {};

        return {
            ...stats,
            recommendations: this.generateAttachmentRecommendations(attachments),
            performance: this.analyzeAttachmentPerformance(attachments)
        };
    }

    generateAttachmentRecommendations(attachments) {
        // 生成附件建议
        const recommendations = [];

        // 大文件建议
        const largeFiles = attachments.filter(a => a.file_size > 5 * 1024 * 1024);
        if (largeFiles.length > 0) {
            recommendations.push({
                type: 'large_files',
                message: `发现 ${largeFiles.length} 个大文件，建议压缩或使用外部链接`,
                files: largeFiles.map(f => f.name)
            });
        }

        // 重复文件建议
        const duplicates = this.findDuplicateAttachments(attachments);
        if (duplicates.length > 0) {
            recommendations.push({
                type: 'duplicates',
                message: `发现 ${duplicates.length} 个可能重复的文件`,
                files: duplicates
            });
        }

        // 格式建议
        const oldFormats = attachments.filter(a =>
            a.mimetype?.includes('application/msword') ||
            a.mimetype?.includes('application/vnd.ms-excel')
        );

        if (oldFormats.length > 0) {
            recommendations.push({
                type: 'format_upgrade',
                message: `建议将 ${oldFormats.length} 个旧格式文件升级为新格式`,
                files: oldFormats.map(f => f.name)
            });
        }

        return recommendations;
    }

    findDuplicateAttachments(attachments) {
        // 查找重复附件
        const duplicates = [];
        const seen = new Map();

        attachments.forEach(attachment => {
            const key = `${attachment.name}_${attachment.file_size}`;

            if (seen.has(key)) {
                duplicates.push({
                    original: seen.get(key),
                    duplicate: attachment
                });
            } else {
                seen.set(key, attachment);
            }
        });

        return duplicates;
    }

    analyzeAttachmentPerformance(attachments) {
        // 分析附件性能
        const totalSize = attachments.reduce((sum, a) => sum + (a.file_size || 0), 0);
        const avgSize = attachments.length > 0 ? totalSize / attachments.length : 0;

        return {
            totalSize: totalSize,
            averageSize: avgSize,
            loadingEstimate: this.estimateLoadingTime(totalSize),
            optimizationPotential: this.calculateOptimizationPotential(attachments)
        };
    }

    estimateLoadingTime(totalSize) {
        // 估算加载时间（基于平均网速）
        const avgSpeedMbps = 10; // 假设10Mbps
        const avgSpeedBps = avgSpeedMbps * 1024 * 1024 / 8;

        return Math.ceil(totalSize / avgSpeedBps);
    }

    calculateOptimizationPotential(attachments) {
        // 计算优化潜力
        const compressibleTypes = ['image/', 'application/pdf'];
        const compressibleSize = attachments
            .filter(a => compressibleTypes.some(type => a.mimetype?.startsWith(type)))
            .reduce((sum, a) => sum + (a.file_size || 0), 0);

        // 假设可以压缩30%
        const potentialSavings = compressibleSize * 0.3;

        return {
            compressibleSize: compressibleSize,
            potentialSavings: potentialSavings,
            savingsPercentage: compressibleSize > 0 ? (potentialSavings / compressibleSize) * 100 : 0
        };
    }
}
```

## 线程模型补丁特性

### 1. 计划消息关系管理
线程模型补丁提供了完整的计划消息关系管理：
- **多对一关系**: scheduledMessages 与 thread 的关系
- **自动排序**: 按计划日期和ID排序
- **反向关系**: 支持双向导航
- **数据同步**: 自动同步相关数据

### 2. 智能附件处理
```javascript
// 智能附件处理配置
const attachmentProcessing = {
    autoMainSelection: true,        // 自动选择主附件
    loadingStateTracking: true,     // 跟踪加载状态
    previewPreloading: true,        // 预加载预览
    performanceOptimization: true   // 性能优化
};
```

### 3. 数据获取增强
- **加载状态管理**: 跟踪附件加载状态
- **自动主附件设置**: 智能选择主附件
- **数据完整性**: 确保数据获取完整性
- **性能优化**: 优化数据获取性能

### 4. 继承链管理
- **Web Portal 继承**: 继承 Web Portal 的补丁
- **方法扩展**: 扩展而非替换原有方法
- **向后兼容**: 保持向后兼容性
- **功能增强**: 在原有基础上增强功能

## 最佳实践

### 1. 补丁使用
```javascript
// ✅ 推荐：正确的补丁使用
patch(Thread.prototype, {
    setup() {
        super.setup(); // 始终调用父类方法
        // 添加新功能
    }
});
```

### 2. 关系定义
```javascript
// ✅ 推荐：完整的关系定义
this.scheduledMessages = Record.many("ScheduledMessage", {
    sort: (a, b) => a.scheduled_date < b.scheduled_date ? -1 : 1,
    inverse: "thread"
});
```

### 3. 数据获取
```javascript
// ✅ 推荐：增强而非替换
async fetchData(requestList) {
    // 设置状态
    this.isLoadingAttachments = requestList.includes("attachments");

    // 调用父类方法
    await super.fetchData(requestList);

    // 后续处理
    if (!this.mainAttachment && this.attachmentsInWebClientView.length > 0) {
        this.setMainAttachmentFromIndex(0);
    }
}
```

### 4. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await this.fetchData(requestList);
} catch (error) {
    console.error('数据获取失败:', error);
    this.isLoadingAttachments = false;
}
```

## 总结

Odoo 线程模型补丁模块提供了重要的线程功能增强：

**核心优势**:
- **计划消息支持**: 完整的计划消息关系和管理
- **智能附件处理**: 自动主附件选择和加载优化
- **数据获取增强**: 增强的数据获取和状态管理
- **继承链管理**: 正确的补丁继承和扩展
- **性能优化**: 附件加载和处理的性能优化

**适用场景**:
- 线程数据模型扩展
- 计划消息功能集成
- 附件管理优化
- 数据获取增强
- 性能优化需求

**设计优势**:
- 补丁模式扩展
- 关系映射支持
- 智能数据处理
- 性能优化集成

这个线程模型补丁为 Odoo Web 客户端提供了强大的线程功能增强，是邮件系统中线程管理的重要组件。
```
# Odoo 计划消息模型 (Scheduled Message Model) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/scheduled_message_model.js`
**原始路径**: `/mail/static/src/chatter/web/scheduled_message_model.js`
**模块类型**: 核心数据模型 - 计划消息数据模型
**代码行数**: 104 行
**依赖关系**:
- `@mail/core/common/record` - 记录基类
- `@mail/utils/common/format` - 格式化工具
- `@web/core/l10n/translation` - 国际化翻译

## 模块功能

计划消息模型模块是 Odoo Web 客户端的计划消息数据模型。该模块提供了：
- 计划消息数据结构定义
- 消息属性和关系管理
- 消息操作方法（编辑、取消、发送）
- 权限控制和验证
- 文本内容处理
- 数据模型注册

这个模型为计划消息功能提供了完整的数据层支持，定义了计划消息的结构、行为和业务逻辑。

## 计划消息模型架构

### 核心数据结构
```
Scheduled Message Model
├── 基础属性
│   ├── id (消息ID)
│   ├── body (消息正文HTML)
│   ├── scheduled_date (计划日期时间)
│   ├── is_note (是否为内部笔记)
│   └── composition_batch (批量撰写标志)
├── 关系属性
│   ├── attachment_ids (附件列表)
│   ├── author (作者信息)
│   ├── thread (所属线程)
│   └── store (存储引用)
├── 计算属性
│   ├── textContent (纯文本内容)
│   ├── deletable (是否可删除)
│   ├── editable (是否可编辑)
│   ├── isSelfAuthored (是否自己创建)
│   └── isSubjectThreadName (主题是否为线程名)
└── 操作方法
    ├── cancel() (取消计划)
    ├── edit() (编辑消息)
    ├── send() (立即发送)
    └── notifyAlreadySent() (已发送通知)
```

### 模型定义
```javascript
// 计划消息模型类
class ScheduledMessage extends Record {
    static id = "id";
    static records = {};

    // 基础属性
    id;                                              // 消息ID
    body = Record.attr("", { html: true });         // 消息正文(HTML)
    scheduled_date = Record.attr(undefined, { type: "datetime" }); // 计划日期
    is_note;                                         // 是否为笔记
    composition_batch;                               // 批量撰写标志

    // 关系属性
    attachment_ids = Record.many("Attachment");      // 附件列表
    author = Record.one("Persona");                  // 作者
    thread = Record.one("Thread");                   // 所属线程

    // 计算属性
    textContent = Record.attr(false, {
        compute() {
            return this.body ? htmlToTextContentInline(this.body) : "";
        }
    });

    // 权限属性
    get deletable() {
        return this.store.self.isAdmin || this.thread.hasWriteAccess;
    }

    get editable() {
        return this.store.self.isAdmin || this.isSelfAuthored;
    }
}
```

## 核心功能详解

### 1. 数据属性管理
```javascript
// 数据属性详细说明
class ScheduledMessageAttributes {
    constructor() {
        this.attributeDefinitions = {
            // 基础标识属性
            id: {
                type: 'number',
                required: true,
                description: '计划消息的唯一标识符'
            },

            // 内容属性
            body: {
                type: 'html',
                required: true,
                description: '消息正文内容，支持HTML格式',
                validation: (value) => value && value.trim().length > 0
            },

            // 时间属性
            scheduled_date: {
                type: 'datetime',
                required: true,
                description: '计划发送的日期和时间',
                validation: (value) => {
                    const scheduledDate = luxon.DateTime.fromISO(value);
                    const now = luxon.DateTime.now();
                    return scheduledDate > now;
                }
            },

            // 标志属性
            is_note: {
                type: 'boolean',
                default: false,
                description: '是否为内部笔记（不发送给外部用户）'
            },

            composition_batch: {
                type: 'boolean',
                default: false,
                description: '是否为批量撰写的消息'
            }
        };

        this.relationshipDefinitions = {
            // 多对一关系
            author: {
                model: 'Persona',
                type: 'many2one',
                description: '消息作者',
                required: true
            },

            thread: {
                model: 'Thread',
                type: 'many2one',
                description: '所属的消息线程',
                required: true
            },

            // 一对多关系
            attachment_ids: {
                model: 'Attachment',
                type: 'one2many',
                description: '消息附件列表'
            }
        };
    }

    validateAttribute(name, value) {
        // 验证属性值
        const definition = this.attributeDefinitions[name];
        if (!definition) {
            throw new Error(`未知的属性: ${name}`);
        }

        // 必填验证
        if (definition.required && (value === null || value === undefined)) {
            throw new Error(`属性 ${name} 是必填的`);
        }

        // 类型验证
        if (value !== null && value !== undefined) {
            if (!this.validateType(value, definition.type)) {
                throw new Error(`属性 ${name} 类型不匹配，期望 ${definition.type}`);
            }
        }

        // 自定义验证
        if (definition.validation && !definition.validation(value)) {
            throw new Error(`属性 ${name} 验证失败`);
        }

        return true;
    }

    validateType(value, expectedType) {
        // 类型验证
        switch (expectedType) {
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'string':
                return typeof value === 'string';
            case 'boolean':
                return typeof value === 'boolean';
            case 'html':
                return typeof value === 'string';
            case 'datetime':
                return typeof value === 'string' || luxon.DateTime.isDateTime(value);
            default:
                return true;
        }
    }

    getDefaultValue(attributeName) {
        // 获取默认值
        const definition = this.attributeDefinitions[attributeName];
        return definition?.default;
    }

    getAttributeDescription(attributeName) {
        // 获取属性描述
        const definition = this.attributeDefinitions[attributeName];
        return definition?.description || '';
    }
}
```

### 2. 权限控制系统
```javascript
// 权限控制管理器
class PermissionManager {
    constructor(scheduledMessage) {
        this.message = scheduledMessage;
        this.permissionRules = new Map();
        this.setupPermissionRules();
    }

    setupPermissionRules() {
        // 设置权限规则

        // 删除权限规则
        this.permissionRules.set('delete', {
            check: () => {
                // 管理员或线程有写权限
                return this.message.store.self.isAdmin ||
                       this.message.thread.hasWriteAccess;
            },
            description: '删除计划消息需要管理员权限或线程写权限'
        });

        // 编辑权限规则
        this.permissionRules.set('edit', {
            check: () => {
                // 管理员或消息作者
                return this.message.store.self.isAdmin ||
                       this.message.isSelfAuthored;
            },
            description: '编辑计划消息需要管理员权限或为消息作者'
        });

        // 发送权限规则
        this.permissionRules.set('send', {
            check: () => {
                // 管理员或消息作者
                return this.message.store.self.isAdmin ||
                       this.message.isSelfAuthored;
            },
            description: '发送计划消息需要管理员权限或为消息作者'
        });

        // 查看权限规则
        this.permissionRules.set('view', {
            check: () => {
                // 线程有读权限
                return this.message.thread.hasReadAccess;
            },
            description: '查看计划消息需要线程读权限'
        });

        // 取消权限规则
        this.permissionRules.set('cancel', {
            check: () => {
                // 管理员或消息作者
                return this.message.store.self.isAdmin ||
                       this.message.isSelfAuthored;
            },
            description: '取消计划消息需要管理员权限或为消息作者'
        });
    }

    hasPermission(action) {
        // 检查权限
        const rule = this.permissionRules.get(action);
        if (!rule) {
            console.warn(`未知的权限动作: ${action}`);
            return false;
        }

        try {
            return rule.check();
        } catch (error) {
            console.error(`权限检查失败 (${action}):`, error);
            return false;
        }
    }

    getPermissionDescription(action) {
        // 获取权限描述
        const rule = this.permissionRules.get(action);
        return rule?.description || '';
    }

    getAllPermissions() {
        // 获取所有权限状态
        const permissions = {};

        for (const [action, rule] of this.permissionRules) {
            permissions[action] = {
                allowed: this.hasPermission(action),
                description: rule.description
            };
        }

        return permissions;
    }

    checkMultiplePermissions(actions) {
        // 检查多个权限
        const results = {};

        actions.forEach(action => {
            results[action] = this.hasPermission(action);
        });

        return results;
    }

    requirePermission(action) {
        // 要求权限（抛出异常如果没有权限）
        if (!this.hasPermission(action)) {
            const description = this.getPermissionDescription(action);
            throw new Error(`权限不足: ${description}`);
        }

        return true;
    }

    addPermissionRule(action, checkFunction, description) {
        // 添加权限规则
        this.permissionRules.set(action, {
            check: checkFunction,
            description: description
        });
    }

    removePermissionRule(action) {
        // 移除权限规则
        this.permissionRules.delete(action);
    }

    updatePermissionRule(action, checkFunction, description) {
        // 更新权限规则
        if (this.permissionRules.has(action)) {
            this.permissionRules.set(action, {
                check: checkFunction,
                description: description
            });
        }
    }
}
```

### 3. 消息操作处理器
```javascript
// 消息操作处理器
class MessageOperationHandler {
    constructor(scheduledMessage) {
        this.message = scheduledMessage;
        this.permissionManager = new PermissionManager(scheduledMessage);
        this.operationQueue = [];
        this.isProcessing = false;
        this.setupOperations();
    }

    setupOperations() {
        // 设置操作处理器
        this.operations = {
            cancel: this.handleCancel.bind(this),
            edit: this.handleEdit.bind(this),
            send: this.handleSend.bind(this),
            duplicate: this.handleDuplicate.bind(this),
            reschedule: this.handleReschedule.bind(this)
        };
    }

    async handleCancel() {
        // 处理取消操作
        try {
            // 检查权限
            this.permissionManager.requirePermission('cancel');

            // 调用服务器API
            await this.message.store.env.services.orm.unlink(
                "mail.scheduled.message",
                [this.message.id]
            );

            // 删除本地记录
            this.message.delete();

            // 显示成功通知
            this.showNotification('计划消息已取消', 'success');

            return { success: true, message: '计划消息已取消' };

        } catch (error) {
            console.error('取消计划消息失败:', error);
            this.showNotification('取消失败: ' + error.message, 'danger');
            throw error;
        }
    }

    async handleEdit() {
        // 处理编辑操作
        try {
            // 检查权限
            this.permissionManager.requirePermission('edit');

            // 调用服务器API获取编辑表单
            let action;
            try {
                action = await this.message.store.env.services.orm.call(
                    "mail.scheduled.message",
                    "open_edit_form",
                    [this.message.id]
                );
            } catch (error) {
                // 消息可能已经发送
                this.message.notifyAlreadySent();
                return { success: false, reason: 'already_sent' };
            }

            // 打开编辑表单
            return new Promise((resolve) => {
                this.message.store.env.services.action.doAction(action, {
                    onClose: (result) => {
                        resolve({
                            success: true,
                            message: '编辑完成',
                            result: result
                        });
                    }
                });
            });

        } catch (error) {
            console.error('编辑计划消息失败:', error);
            this.showNotification('编辑失败: ' + error.message, 'danger');
            throw error;
        }
    }

    async handleSend() {
        // 处理立即发送操作
        try {
            // 检查权限
            this.permissionManager.requirePermission('send');

            // 调用服务器API发送消息
            try {
                await this.message.store.env.services.orm.call(
                    "mail.scheduled.message",
                    "post_message",
                    [this.message.id]
                );

                // 显示成功通知
                this.showNotification('消息已发送', 'success');

                // 删除本地记录（已发送）
                this.message.delete();

                return { success: true, message: '消息已发送' };

            } catch (error) {
                // 消息可能已经被发送（由其他人或定时任务）
                console.warn('消息可能已经发送:', error);
                this.message.notifyAlreadySent();
                return { success: false, reason: 'already_sent' };
            }

        } catch (error) {
            console.error('发送计划消息失败:', error);
            this.showNotification('发送失败: ' + error.message, 'danger');
            throw error;
        }
    }

    async handleDuplicate() {
        // 处理复制操作
        try {
            // 检查权限
            this.permissionManager.requirePermission('view');

            // 创建复制数据
            const duplicateData = {
                body: this.message.body,
                subject: this.message.subject ? `副本: ${this.message.subject}` : undefined,
                thread_id: this.message.thread.id,
                thread_model: this.message.thread.model,
                attachment_ids: this.message.attachment_ids.map(att => att.id),
                is_note: this.message.is_note
            };

            // 打开新的撰写器
            const action = {
                type: 'ir.actions.act_window',
                res_model: 'mail.compose.message',
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'new',
                context: {
                    default_composition_mode: 'comment',
                    default_model: this.message.thread.model,
                    default_res_id: this.message.thread.id,
                    ...duplicateData
                }
            };

            this.message.store.env.services.action.doAction(action);

            return { success: true, message: '已创建消息副本' };

        } catch (error) {
            console.error('复制计划消息失败:', error);
            this.showNotification('复制失败: ' + error.message, 'danger');
            throw error;
        }
    }

    async handleReschedule(newScheduledDate) {
        // 处理重新计划操作
        try {
            // 检查权限
            this.permissionManager.requirePermission('edit');

            // 验证新的计划时间
            const scheduledDate = luxon.DateTime.fromISO(newScheduledDate);
            const now = luxon.DateTime.now();

            if (scheduledDate <= now) {
                throw new Error('计划时间必须在未来');
            }

            // 调用服务器API更新计划时间
            await this.message.store.env.services.orm.write(
                "mail.scheduled.message",
                [this.message.id],
                { scheduled_date: newScheduledDate }
            );

            // 更新本地数据
            this.message.scheduled_date = scheduledDate;

            // 显示成功通知
            this.showNotification('计划时间已更新', 'success');

            return { success: true, message: '计划时间已更新' };

        } catch (error) {
            console.error('重新计划消息失败:', error);
            this.showNotification('重新计划失败: ' + error.message, 'danger');
            throw error;
        }
    }

    async executeOperation(operationName, ...args) {
        // 执行操作
        const operation = this.operations[operationName];

        if (!operation) {
            throw new Error(`未知的操作: ${operationName}`);
        }

        // 添加到队列
        return this.queueOperation(operation, ...args);
    }

    async queueOperation(operation, ...args) {
        // 队列操作
        return new Promise((resolve, reject) => {
            this.operationQueue.push({
                operation: operation,
                args: args,
                resolve: resolve,
                reject: reject,
                timestamp: Date.now()
            });

            this.processQueue();
        });
    }

    async processQueue() {
        // 处理队列
        if (this.isProcessing || this.operationQueue.length === 0) {
            return;
        }

        this.isProcessing = true;

        try {
            while (this.operationQueue.length > 0) {
                const item = this.operationQueue.shift();

                try {
                    const result = await item.operation(...item.args);
                    item.resolve(result);
                } catch (error) {
                    item.reject(error);
                }
            }
        } finally {
            this.isProcessing = false;
        }
    }

    showNotification(message, type = 'info') {
        // 显示通知
        this.message.store.env.services.notification.add(message, {
            type: type
        });
    }

    getOperationStatus() {
        // 获取操作状态
        return {
            queueLength: this.operationQueue.length,
            isProcessing: this.isProcessing,
            availableOperations: Object.keys(this.operations)
        };
    }

    clearQueue() {
        // 清空队列
        this.operationQueue.forEach(item => {
            item.reject(new Error('操作队列已清空'));
        });

        this.operationQueue = [];
        this.isProcessing = false;
    }
}
```

### 4. 文本内容处理器
```javascript
// 文本内容处理器
class TextContentProcessor {
    constructor() {
        this.processors = new Map();
        this.setupProcessors();
    }

    setupProcessors() {
        // 设置处理器

        // HTML到纯文本转换
        this.processors.set('htmlToText', (htmlContent) => {
            return htmlToTextContentInline(htmlContent);
        });

        // 文本截断
        this.processors.set('truncate', (text, maxLength = 100) => {
            if (!text || text.length <= maxLength) {
                return text;
            }
            return text.substring(0, maxLength) + '...';
        });

        // 文本清理
        this.processors.set('clean', (text) => {
            return text
                .replace(/\s+/g, ' ')  // 合并空白字符
                .replace(/^\s+|\s+$/g, '')  // 去除首尾空白
                .replace(/\n\s*\n/g, '\n');  // 合并空行
        });

        // 关键词提取
        this.processors.set('extractKeywords', (text) => {
            const words = text.toLowerCase()
                .replace(/[^\w\s]/g, '')
                .split(/\s+/)
                .filter(word => word.length > 3);

            const frequency = {};
            words.forEach(word => {
                frequency[word] = (frequency[word] || 0) + 1;
            });

            return Object.entries(frequency)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([word]) => word);
        });

        // 摘要生成
        this.processors.set('generateSummary', (text, maxSentences = 2) => {
            const sentences = text.split(/[.!?]+/)
                .map(s => s.trim())
                .filter(s => s.length > 10);

            if (sentences.length <= maxSentences) {
                return text;
            }

            // 简单的摘要：取前几句
            return sentences.slice(0, maxSentences).join('. ') + '.';
        });

        // 语言检测
        this.processors.set('detectLanguage', (text) => {
            // 简化的语言检测
            const chinesePattern = /[\u4e00-\u9fff]/;
            const englishPattern = /[a-zA-Z]/;

            const chineseCount = (text.match(chinesePattern) || []).length;
            const englishCount = (text.match(englishPattern) || []).length;

            if (chineseCount > englishCount) {
                return 'zh';
            } else if (englishCount > 0) {
                return 'en';
            } else {
                return 'unknown';
            }
        });
    }

    process(processorName, content, ...args) {
        // 处理内容
        const processor = this.processors.get(processorName);

        if (!processor) {
            throw new Error(`未知的处理器: ${processorName}`);
        }

        try {
            return processor(content, ...args);
        } catch (error) {
            console.error(`内容处理失败 (${processorName}):`, error);
            return content; // 返回原内容
        }
    }

    processMultiple(content, processorChain) {
        // 链式处理
        let result = content;

        for (const { processor, args = [] } of processorChain) {
            result = this.process(processor, result, ...args);
        }

        return result;
    }

    addProcessor(name, processorFunction) {
        // 添加处理器
        this.processors.set(name, processorFunction);
    }

    removeProcessor(name) {
        // 移除处理器
        this.processors.delete(name);
    }

    getAvailableProcessors() {
        // 获取可用处理器
        return Array.from(this.processors.keys());
    }
}
```

## 使用示例

### 1. 基本模型使用
```javascript
// 使用计划消息模型
class ScheduledMessageManager extends Component {
    setup() {
        this.textProcessor = new TextContentProcessor();
        this.setupMessageHandling();
    }

    setupMessageHandling() {
        // 设置消息处理
        this.loadScheduledMessages();
        this.setupEventListeners();
    }

    async loadScheduledMessages() {
        // 加载计划消息
        try {
            const messages = await this.env.services.orm.searchRead(
                'mail.scheduled.message',
                [['thread_id', '=', this.props.threadId]],
                ['id', 'body', 'scheduled_date', 'author_id', 'is_note']
            );

            // 创建模型实例
            this.scheduledMessages = messages.map(data =>
                ScheduledMessage.get(data)
            );

        } catch (error) {
            console.error('加载计划消息失败:', error);
        }
    }

    async createScheduledMessage(data) {
        // 创建计划消息
        try {
            // 验证数据
            this.validateMessageData(data);

            // 创建模型实例
            const message = ScheduledMessage.get(data);

            // 处理文本内容
            message.textContent = this.textProcessor.process('htmlToText', message.body);

            return message;

        } catch (error) {
            console.error('创建计划消息失败:', error);
            throw error;
        }
    }

    validateMessageData(data) {
        // 验证消息数据
        const attributeManager = new ScheduledMessageAttributes();

        // 验证必填字段
        const requiredFields = ['body', 'scheduled_date', 'author', 'thread'];

        requiredFields.forEach(field => {
            if (!data[field]) {
                throw new Error(`缺少必填字段: ${field}`);
            }
        });

        // 验证计划时间
        const scheduledDate = luxon.DateTime.fromISO(data.scheduled_date);
        const now = luxon.DateTime.now();

        if (scheduledDate <= now) {
            throw new Error('计划时间必须在未来');
        }

        // 验证内容长度
        const textContent = this.textProcessor.process('htmlToText', data.body);
        if (textContent.length < 1) {
            throw new Error('消息内容不能为空');
        }

        if (textContent.length > 10000) {
            throw new Error('消息内容过长');
        }
    }

    getMessageSummary(message) {
        // 获取消息摘要
        const textContent = message.textContent ||
            this.textProcessor.process('htmlToText', message.body);

        return {
            id: message.id,
            summary: this.textProcessor.process('generateSummary', textContent),
            keywords: this.textProcessor.process('extractKeywords', textContent),
            language: this.textProcessor.process('detectLanguage', textContent),
            length: textContent.length,
            scheduledDate: message.scheduled_date,
            author: message.author?.name,
            isNote: message.is_note
        };
    }

    async performBulkOperation(messageIds, operation, ...args) {
        // 批量操作
        const results = [];

        for (const messageId of messageIds) {
            try {
                const message = ScheduledMessage.records[messageId];
                if (!message) {
                    results.push({ id: messageId, success: false, error: '消息不存在' });
                    continue;
                }

                const operationHandler = new MessageOperationHandler(message);
                const result = await operationHandler.executeOperation(operation, ...args);

                results.push({ id: messageId, success: true, result: result });

            } catch (error) {
                results.push({ id: messageId, success: false, error: error.message });
            }
        }

        return results;
    }
}
```

### 2. 权限控制使用
```javascript
// 权限控制使用示例
class MessagePermissionController extends Component {
    setup() {
        this.message = this.props.scheduledMessage;
        this.permissionManager = new PermissionManager(this.message);
        this.setupPermissionUI();
    }

    setupPermissionUI() {
        // 设置权限UI
        this.permissions = this.permissionManager.getAllPermissions();
        this.updateUIBasedOnPermissions();
    }

    updateUIBasedOnPermissions() {
        // 根据权限更新UI
        const actions = ['edit', 'delete', 'send', 'cancel'];

        actions.forEach(action => {
            const button = document.querySelector(`[data-action="${action}"]`);
            if (button) {
                button.disabled = !this.permissions[action].allowed;
                button.title = this.permissions[action].description;
            }
        });
    }

    async onActionClick(action) {
        // 操作点击处理
        try {
            // 检查权限
            if (!this.permissionManager.hasPermission(action)) {
                this.showPermissionError(action);
                return;
            }

            // 执行操作
            const operationHandler = new MessageOperationHandler(this.message);
            await operationHandler.executeOperation(action);

        } catch (error) {
            console.error(`操作失败 (${action}):`, error);
            this.showError(error.message);
        }
    }

    showPermissionError(action) {
        // 显示权限错误
        const description = this.permissionManager.getPermissionDescription(action);
        this.env.services.notification.add(
            `权限不足: ${description}`,
            { type: 'warning' }
        );
    }

    showError(message) {
        // 显示错误
        this.env.services.notification.add(
            `操作失败: ${message}`,
            { type: 'danger' }
        );
    }

    getPermissionSummary() {
        // 获取权限摘要
        const permissions = this.permissionManager.getAllPermissions();
        const allowedActions = Object.keys(permissions).filter(
            action => permissions[action].allowed
        );

        return {
            total: Object.keys(permissions).length,
            allowed: allowedActions.length,
            actions: allowedActions,
            isAdmin: this.message.store.self.isAdmin,
            isSelfAuthored: this.message.isSelfAuthored
        };
    }
}
```

### 3. 高级文本处理
```javascript
// 高级文本处理示例
class AdvancedTextProcessor extends Component {
    setup() {
        this.textProcessor = new TextContentProcessor();
        this.addCustomProcessors();
    }

    addCustomProcessors() {
        // 添加自定义处理器

        // 敏感词过滤
        this.textProcessor.addProcessor('filterSensitive', (text) => {
            const sensitiveWords = ['密码', '账号', '机密'];
            let filtered = text;

            sensitiveWords.forEach(word => {
                const regex = new RegExp(word, 'gi');
                filtered = filtered.replace(regex, '*'.repeat(word.length));
            });

            return filtered;
        });

        // 链接提取
        this.textProcessor.addProcessor('extractLinks', (text) => {
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            return text.match(urlRegex) || [];
        });

        // 邮箱提取
        this.textProcessor.addProcessor('extractEmails', (text) => {
            const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
            return text.match(emailRegex) || [];
        });

        // 提及用户提取
        this.textProcessor.addProcessor('extractMentions', (text) => {
            const mentionRegex = /@(\w+)/g;
            const mentions = [];
            let match;

            while ((match = mentionRegex.exec(text)) !== null) {
                mentions.push(match[1]);
            }

            return mentions;
        });

        // 标签提取
        this.textProcessor.addProcessor('extractTags', (text) => {
            const tagRegex = /#(\w+)/g;
            const tags = [];
            let match;

            while ((match = tagRegex.exec(text)) !== null) {
                tags.push(match[1]);
            }

            return tags;
        });
    }

    analyzeMessageContent(message) {
        // 分析消息内容
        const textContent = message.textContent ||
            this.textProcessor.process('htmlToText', message.body);

        const analysis = {
            // 基础信息
            length: textContent.length,
            wordCount: textContent.split(/\s+/).length,
            language: this.textProcessor.process('detectLanguage', textContent),

            // 内容提取
            summary: this.textProcessor.process('generateSummary', textContent),
            keywords: this.textProcessor.process('extractKeywords', textContent),
            links: this.textProcessor.process('extractLinks', textContent),
            emails: this.textProcessor.process('extractEmails', textContent),
            mentions: this.textProcessor.process('extractMentions', textContent),
            tags: this.textProcessor.process('extractTags', textContent),

            // 内容特征
            hasLinks: false,
            hasEmails: false,
            hasMentions: false,
            hasTags: false,
            isLongMessage: textContent.length > 500,

            // 安全检查
            filteredContent: this.textProcessor.process('filterSensitive', textContent)
        };

        // 更新特征标志
        analysis.hasLinks = analysis.links.length > 0;
        analysis.hasEmails = analysis.emails.length > 0;
        analysis.hasMentions = analysis.mentions.length > 0;
        analysis.hasTags = analysis.tags.length > 0;

        return analysis;
    }

    generateContentInsights(messages) {
        // 生成内容洞察
        const insights = {
            totalMessages: messages.length,
            totalWords: 0,
            languages: {},
            commonKeywords: {},
            linkDomains: {},
            mentionedUsers: {},
            usedTags: {},
            averageLength: 0
        };

        messages.forEach(message => {
            const analysis = this.analyzeMessageContent(message);

            // 统计总词数
            insights.totalWords += analysis.wordCount;

            // 统计语言
            insights.languages[analysis.language] =
                (insights.languages[analysis.language] || 0) + 1;

            // 统计关键词
            analysis.keywords.forEach(keyword => {
                insights.commonKeywords[keyword] =
                    (insights.commonKeywords[keyword] || 0) + 1;
            });

            // 统计链接域名
            analysis.links.forEach(link => {
                try {
                    const domain = new URL(link).hostname;
                    insights.linkDomains[domain] =
                        (insights.linkDomains[domain] || 0) + 1;
                } catch (error) {
                    // 忽略无效链接
                }
            });

            // 统计提及用户
            analysis.mentions.forEach(mention => {
                insights.mentionedUsers[mention] =
                    (insights.mentionedUsers[mention] || 0) + 1;
            });

            // 统计标签
            analysis.tags.forEach(tag => {
                insights.usedTags[tag] =
                    (insights.usedTags[tag] || 0) + 1;
            });
        });

        // 计算平均长度
        insights.averageLength = messages.length > 0 ?
            insights.totalWords / messages.length : 0;

        // 排序统计结果
        insights.topKeywords = this.getTopItems(insights.commonKeywords, 10);
        insights.topDomains = this.getTopItems(insights.linkDomains, 5);
        insights.topMentions = this.getTopItems(insights.mentionedUsers, 10);
        insights.topTags = this.getTopItems(insights.usedTags, 10);

        return insights;
    }

    getTopItems(itemCounts, limit) {
        // 获取排名前N的项目
        return Object.entries(itemCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, limit)
            .map(([item, count]) => ({ item, count }));
    }
}
```

## 计划消息模型特性

### 1. 完整的数据模型
计划消息模型提供了完整的数据结构：
- **基础属性**: ID、正文、计划日期、类型标志
- **关系属性**: 作者、线程、附件关系
- **计算属性**: 文本内容、权限状态
- **操作方法**: 取消、编辑、发送等

### 2. 权限控制系统
```javascript
// 权限控制示例
const permissions = {
    deletable: message.store.self.isAdmin || message.thread.hasWriteAccess,
    editable: message.store.self.isAdmin || message.isSelfAuthored,
    sendable: message.store.self.isAdmin || message.isSelfAuthored
};
```

### 3. 智能文本处理
- **HTML转换**: 自动转换HTML为纯文本
- **内容分析**: 关键词提取、摘要生成
- **多语言支持**: 语言检测和处理
- **安全过滤**: 敏感信息过滤

### 4. 操作队列管理
- **异步操作**: 支持异步操作队列
- **错误处理**: 完善的错误处理机制
- **状态跟踪**: 操作状态的实时跟踪
- **批量处理**: 支持批量操作

## 最佳实践

### 1. 模型使用
```javascript
// ✅ 推荐：正确的模型使用
const scheduledMessage = ScheduledMessage.get({
    id: messageId,
    body: messageBody,
    scheduled_date: scheduledDate,
    author: authorData,
    thread: threadData
});
```

### 2. 权限检查
```javascript
// ✅ 推荐：操作前检查权限
if (scheduledMessage.editable) {
    await scheduledMessage.edit();
} else {
    console.warn('没有编辑权限');
}
```

### 3. 错误处理
```javascript
// ✅ 推荐：完善的错误处理
try {
    await scheduledMessage.send();
} catch (error) {
    if (error.message.includes('already sent')) {
        scheduledMessage.notifyAlreadySent();
    } else {
        console.error('发送失败:', error);
    }
}
```

### 4. 文本处理
```javascript
// ✅ 推荐：使用计算属性获取文本内容
const textContent = scheduledMessage.textContent; // 自动HTML转换
const summary = textProcessor.process('generateSummary', textContent);
```

## 总结

Odoo 计划消息模型模块提供了完整的计划消息数据管理功能：

**核心优势**:
- **完整数据模型**: 定义了完整的计划消息数据结构和关系
- **权限控制**: 基于角色和所有权的细粒度权限控制
- **智能操作**: 支持取消、编辑、发送等智能操作
- **文本处理**: 强大的文本内容处理和分析能力
- **错误处理**: 完善的错误处理和用户反馈机制

**适用场景**:
- 计划消息数据管理
- 消息权限控制
- 文本内容处理
- 批量消息操作
- 消息状态跟踪

**设计优势**:
- 继承Record基类
- 声明式属性定义
- 计算属性支持
- 操作方法集成
- 权限系统内置

这个计划消息模型为 Odoo Web 客户端提供了强大的计划消息数据管理能力，是计划消息功能的核心数据层组件。
```
# Odoo 邮件撰写计划对话框 (Mail Composer Schedule Dialog) 学习资料

## 文件概述

**文件路径**: `output/@mail/chatter/web/mail_composer_schedule_dialog.js`  
**原始路径**: `/mail/static/src/chatter/web/mail_composer_schedule_dialog.js`  
**模块类型**: 核心组件 - 邮件计划发送对话框  
**代码行数**: 80 行  
**依赖关系**: 
- `@web/core/datetime/datetime_input` - 日期时间输入组件
- `@web/core/dialog/dialog` - 对话框组件
- `@web/core/l10n/dates` - 日期本地化工具
- `@odoo/owl` - OWL 框架

## 模块功能

邮件撰写计划对话框模块是 Odoo Web 客户端的邮件计划发送功能界面。该模块提供了：
- 邮件计划发送时间选择
- 预设时间选项
- 自定义时间选择
- 日期时间格式化
- 时间验证和处理
- 用户友好的时间选择界面

这个模块为用户提供了灵活的邮件计划发送功能，支持多种时间选择方式和智能的时间建议。

## 邮件计划对话框架构

### 核心组件结构
```
Mail Composer Schedule Dialog
├── 时间选择选项
│   ├── 明天上午 (8:00)
│   ├── 明天下午 (13:00)
│   ├── 下周一上午 (8:00)
│   └── 自定义时间
├── 日期时间组件
│   ├── DateTimeInput 集成
│   ├── 最小日期限制
│   ├── 时间格式化
│   └── 用户交互
├── 状态管理
│   ├── 选中选项状态
│   ├── 自定义时间状态
│   ├── 计划日期计算
│   └── 表单验证
└── 操作功能
    ├── 计划确认
    ├── 对话框关闭
    ├── 时间序列化
    └── 回调执行
```

### 组件配置
```javascript
// 邮件计划对话框组件
class MailComposerScheduleDialog extends Component {
    static template = "mail.MailComposerScheduleDialog";
    static props = {
        close: Function,        // 关闭回调
        isNote: Boolean,        // 是否为笔记
        schedule: Function,     // 计划回调
    };
    static components = {
        DateTimeInput,          // 日期时间输入
        Dialog,                 // 对话框
    };
    
    setup() {
        const now = luxon.DateTime.now();
        this.state = useState({
            // 自定义时间：当前时间+1小时，分钟数向上取整到5的倍数
            customDateTime: now
                .plus({ hours: 1 })
                .set({ 
                    minutes: Math.ceil(now.minute / 5) * 5, 
                    seconds: 0, 
                    milliseconds: 0 
                }),
            selectedOption: "morning",  // 默认选择明天上午
        });
        
        // 日期时间格式配置
        this.dateTimeFormat = {
            day: "numeric",
            hour: "numeric", 
            minute: "numeric",
            month: "short",
        };
    }
}
```

## 核心功能详解

### 1. 时间选择管理器
```javascript
// 时间选择管理器
class ScheduleTimeManager {
    constructor() {
        this.timeOptions = new Map();
        this.customTimeValidators = [];
        this.timeZoneHandler = null;
        this.setupTimeOptions();
    }
    
    setupTimeOptions() {
        // 设置时间选项
        this.timeOptions.set('morning', {
            label: '明天上午',
            description: '明天 8:00',
            calculator: () => this.getTomorrowMorning(),
            icon: 'fa-sun-o',
            priority: 1
        });
        
        this.timeOptions.set('afternoon', {
            label: '明天下午',
            description: '明天 13:00',
            calculator: () => this.getTomorrowAfternoon(),
            icon: 'fa-sun-o',
            priority: 2
        });
        
        this.timeOptions.set('monday', {
            label: '下周一上午',
            description: '下周一 8:00',
            calculator: () => this.getMondayMorning(),
            icon: 'fa-calendar',
            priority: 3
        });
        
        this.timeOptions.set('custom', {
            label: '自定义时间',
            description: '选择具体时间',
            calculator: (customTime) => customTime,
            icon: 'fa-clock-o',
            priority: 4
        });
    }
    
    getTomorrowMorning() {
        // 获取明天上午时间
        return today().plus({ days: 1 }).set({ hour: 8, minute: 0, second: 0 });
    }
    
    getTomorrowAfternoon() {
        // 获取明天下午时间
        return today().plus({ days: 1 }).set({ hour: 13, minute: 0, second: 0 });
    }
    
    getMondayMorning() {
        // 获取下周一上午时间
        const today_date = today();
        const daysUntilMonday = (1 - today_date.weekday + 7) % 7 || 7;
        return today_date
            .plus({ days: daysUntilMonday })
            .set({ hour: 8, minute: 0, second: 0 });
    }
    
    getScheduledDate(selectedOption, customDateTime) {
        // 获取计划日期
        const option = this.timeOptions.get(selectedOption);
        if (!option) {
            throw new Error(`未知的时间选项: ${selectedOption}`);
        }
        
        if (selectedOption === 'custom') {
            return option.calculator(customDateTime);
        } else {
            return option.calculator();
        }
    }
    
    validateScheduleTime(dateTime) {
        // 验证计划时间
        const now = luxon.DateTime.now();
        
        // 检查时间是否在未来
        if (dateTime <= now) {
            throw new Error('计划时间必须在未来');
        }
        
        // 检查时间是否过远
        const maxFuture = now.plus({ months: 6 });
        if (dateTime > maxFuture) {
            throw new Error('计划时间不能超过6个月');
        }
        
        // 检查工作时间（可选）
        if (this.shouldValidateBusinessHours()) {
            this.validateBusinessHours(dateTime);
        }
        
        // 执行自定义验证器
        for (const validator of this.customTimeValidators) {
            validator(dateTime);
        }
        
        return true;
    }
    
    shouldValidateBusinessHours() {
        // 是否应该验证工作时间
        return false; // 默认不验证，可根据需要配置
    }
    
    validateBusinessHours(dateTime) {
        // 验证工作时间
        const hour = dateTime.hour;
        const weekday = dateTime.weekday;
        
        // 检查是否为工作日
        if (weekday > 5) { // 周六、周日
            console.warn('计划时间在周末，可能影响邮件送达效果');
        }
        
        // 检查是否为工作时间
        if (hour < 8 || hour > 18) {
            console.warn('计划时间在非工作时间，可能影响邮件送达效果');
        }
    }
    
    addCustomValidator(validator) {
        // 添加自定义验证器
        this.customTimeValidators.push(validator);
    }
    
    removeCustomValidator(validator) {
        // 移除自定义验证器
        const index = this.customTimeValidators.indexOf(validator);
        if (index > -1) {
            this.customTimeValidators.splice(index, 1);
        }
    }
    
    formatScheduleTime(dateTime, format = 'full') {
        // 格式化计划时间
        const formats = {
            full: {
                day: "numeric",
                month: "short", 
                year: "numeric",
                hour: "numeric",
                minute: "numeric"
            },
            short: {
                day: "numeric",
                hour: "numeric",
                minute: "numeric",
                month: "short"
            },
            time: {
                hour: "numeric",
                minute: "numeric"
            }
        };
        
        const formatOptions = formats[format] || formats.full;
        return dateTime.toLocaleString(formatOptions);
    }
    
    getTimeUntilScheduled(scheduledTime) {
        // 获取距离计划时间的时长
        const now = luxon.DateTime.now();
        const diff = scheduledTime.diff(now);
        
        const duration = diff.shiftTo('days', 'hours', 'minutes');
        
        if (duration.days > 0) {
            return `${Math.floor(duration.days)}天${Math.floor(duration.hours)}小时`;
        } else if (duration.hours > 0) {
            return `${Math.floor(duration.hours)}小时${Math.floor(duration.minutes)}分钟`;
        } else {
            return `${Math.floor(duration.minutes)}分钟`;
        }
    }
    
    suggestOptimalTime(messageType = 'email') {
        // 建议最佳发送时间
        const now = luxon.DateTime.now();
        const hour = now.hour;
        const weekday = now.weekday;
        
        // 根据当前时间建议最佳发送时间
        if (messageType === 'email') {
            // 邮件最佳发送时间建议
            if (hour < 8) {
                return 'morning'; // 建议明天上午
            } else if (hour < 13) {
                return 'afternoon'; // 建议明天下午
            } else if (weekday < 5) {
                return 'morning'; // 工作日建议明天上午
            } else {
                return 'monday'; // 周末建议下周一
            }
        } else {
            // 其他类型消息的建议
            return 'morning';
        }
    }
    
    getQuickOptions() {
        // 获取快速选项
        const options = [];
        
        for (const [key, option] of this.timeOptions) {
            if (key !== 'custom') {
                const scheduledTime = option.calculator();
                options.push({
                    key: key,
                    label: option.label,
                    description: option.description,
                    time: scheduledTime,
                    formatted: this.formatScheduleTime(scheduledTime, 'short'),
                    timeUntil: this.getTimeUntilScheduled(scheduledTime),
                    icon: option.icon,
                    priority: option.priority
                });
            }
        }
        
        // 按优先级排序
        return options.sort((a, b) => a.priority - b.priority);
    }
}
```

### 2. 智能时间建议器
```javascript
// 智能时间建议器
class SmartTimeScheduler {
    constructor() {
        this.userPreferences = new Map();
        this.sendingPatterns = new Map();
        this.timeZoneDetector = null;
        this.setupScheduler();
    }
    
    setupScheduler() {
        // 设置调度器
        this.loadUserPreferences();
        this.analyzeSendingPatterns();
        this.setupTimeZoneDetection();
    }
    
    loadUserPreferences() {
        // 加载用户偏好
        const preferences = localStorage.getItem('mail_schedule_preferences');
        if (preferences) {
            try {
                const parsed = JSON.parse(preferences);
                Object.entries(parsed).forEach(([key, value]) => {
                    this.userPreferences.set(key, value);
                });
            } catch (error) {
                console.error('加载用户偏好失败:', error);
            }
        }
        
        // 设置默认偏好
        this.setDefaultPreferences();
    }
    
    setDefaultPreferences() {
        // 设置默认偏好
        if (!this.userPreferences.has('preferred_morning_hour')) {
            this.userPreferences.set('preferred_morning_hour', 8);
        }
        
        if (!this.userPreferences.has('preferred_afternoon_hour')) {
            this.userPreferences.set('preferred_afternoon_hour', 13);
        }
        
        if (!this.userPreferences.has('avoid_weekends')) {
            this.userPreferences.set('avoid_weekends', false);
        }
        
        if (!this.userPreferences.has('business_hours_only')) {
            this.userPreferences.set('business_hours_only', false);
        }
    }
    
    analyzeSendingPatterns() {
        // 分析发送模式
        // 这里可以分析用户的历史发送时间，找出模式
        const patterns = localStorage.getItem('mail_sending_patterns');
        if (patterns) {
            try {
                const parsed = JSON.parse(patterns);
                Object.entries(parsed).forEach(([key, value]) => {
                    this.sendingPatterns.set(key, value);
                });
            } catch (error) {
                console.error('加载发送模式失败:', error);
            }
        }
    }
    
    setupTimeZoneDetection() {
        // 设置时区检测
        this.timeZoneDetector = {
            userTimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            offset: new Date().getTimezoneOffset()
        };
    }
    
    getSmartSuggestions(recipientTimeZones = []) {
        // 获取智能建议
        const suggestions = [];
        const now = luxon.DateTime.now();
        
        // 基于用户偏好的建议
        suggestions.push(...this.getUserPreferenceSuggestions());
        
        // 基于收件人时区的建议
        if (recipientTimeZones.length > 0) {
            suggestions.push(...this.getRecipientTimeZoneSuggestions(recipientTimeZones));
        }
        
        // 基于发送模式的建议
        suggestions.push(...this.getSendingPatternSuggestions());
        
        // 基于最佳实践的建议
        suggestions.push(...this.getBestPracticeSuggestions());
        
        // 去重和排序
        return this.deduplicateAndSort(suggestions);
    }
    
    getUserPreferenceSuggestions() {
        // 基于用户偏好的建议
        const suggestions = [];
        const morningHour = this.userPreferences.get('preferred_morning_hour');
        const afternoonHour = this.userPreferences.get('preferred_afternoon_hour');
        
        // 明天上午建议
        suggestions.push({
            type: 'user_preference',
            time: today().plus({ days: 1 }).set({ hour: morningHour }),
            label: `明天上午 ${morningHour}:00`,
            reason: '基于您的偏好时间',
            score: 90
        });
        
        // 明天下午建议
        suggestions.push({
            type: 'user_preference',
            time: today().plus({ days: 1 }).set({ hour: afternoonHour }),
            label: `明天下午 ${afternoonHour}:00`,
            reason: '基于您的偏好时间',
            score: 85
        });
        
        return suggestions;
    }
    
    getRecipientTimeZoneSuggestions(recipientTimeZones) {
        // 基于收件人时区的建议
        const suggestions = [];
        
        // 计算收件人的最佳接收时间
        recipientTimeZones.forEach(timeZone => {
            const recipientTime = luxon.DateTime.now().setZone(timeZone);
            const optimalHours = [9, 14]; // 上午9点和下午2点
            
            optimalHours.forEach(hour => {
                const optimalTime = recipientTime.plus({ days: 1 }).set({ hour: hour });
                const localTime = optimalTime.setZone(this.timeZoneDetector.userTimeZone);
                
                suggestions.push({
                    type: 'recipient_timezone',
                    time: localTime,
                    label: `收件人最佳时间 (${timeZone})`,
                    reason: `在收件人时区的最佳接收时间`,
                    score: 80
                });
            });
        });
        
        return suggestions;
    }
    
    getSendingPatternSuggestions() {
        // 基于发送模式的建议
        const suggestions = [];
        
        // 分析用户最常发送的时间
        const commonHours = this.sendingPatterns.get('common_hours') || [9, 14];
        const commonDays = this.sendingPatterns.get('common_days') || [1, 2, 3, 4, 5]; // 工作日
        
        commonHours.forEach(hour => {
            const nextWorkday = this.getNextWorkday();
            const suggestedTime = nextWorkday.set({ hour: hour });
            
            suggestions.push({
                type: 'sending_pattern',
                time: suggestedTime,
                label: `常用发送时间 ${hour}:00`,
                reason: '基于您的发送习惯',
                score: 75
            });
        });
        
        return suggestions;
    }
    
    getBestPracticeSuggestions() {
        // 基于最佳实践的建议
        const suggestions = [];
        
        // 邮件营销最佳时间
        const bestPracticeTimes = [
            { hour: 10, label: '上午10点', reason: '邮件打开率最高的时间' },
            { hour: 14, label: '下午2点', reason: '工作日下午活跃时间' },
            { hour: 19, label: '晚上7点', reason: '个人邮件查看时间' }
        ];
        
        bestPracticeTimes.forEach(({ hour, label, reason }) => {
            const nextWorkday = this.getNextWorkday();
            const suggestedTime = nextWorkday.set({ hour: hour });
            
            suggestions.push({
                type: 'best_practice',
                time: suggestedTime,
                label: label,
                reason: reason,
                score: 70
            });
        });
        
        return suggestions;
    }
    
    getNextWorkday() {
        // 获取下一个工作日
        let nextDay = today().plus({ days: 1 });
        
        // 如果是周末，跳到下周一
        while (nextDay.weekday > 5) {
            nextDay = nextDay.plus({ days: 1 });
        }
        
        return nextDay;
    }
    
    deduplicateAndSort(suggestions) {
        // 去重和排序
        const uniqueSuggestions = new Map();
        
        suggestions.forEach(suggestion => {
            const key = suggestion.time.toISO();
            if (!uniqueSuggestions.has(key) || uniqueSuggestions.get(key).score < suggestion.score) {
                uniqueSuggestions.set(key, suggestion);
            }
        });
        
        return Array.from(uniqueSuggestions.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, 5); // 只返回前5个建议
    }
    
    recordScheduleChoice(scheduledTime, option) {
        // 记录计划选择
        const choice = {
            time: scheduledTime.toISO(),
            option: option,
            timestamp: Date.now(),
            hour: scheduledTime.hour,
            weekday: scheduledTime.weekday
        };
        
        // 更新发送模式
        this.updateSendingPatterns(choice);
        
        // 保存到本地存储
        this.saveSendingPatterns();
    }
    
    updateSendingPatterns(choice) {
        // 更新发送模式
        
        // 更新常用小时
        const commonHours = this.sendingPatterns.get('common_hours') || [];
        if (!commonHours.includes(choice.hour)) {
            commonHours.push(choice.hour);
            this.sendingPatterns.set('common_hours', commonHours);
        }
        
        // 更新常用星期
        const commonDays = this.sendingPatterns.get('common_days') || [];
        if (!commonDays.includes(choice.weekday)) {
            commonDays.push(choice.weekday);
            this.sendingPatterns.set('common_days', commonDays);
        }
        
        // 更新选择历史
        const history = this.sendingPatterns.get('choice_history') || [];
        history.push(choice);
        
        // 只保留最近50次选择
        if (history.length > 50) {
            history.splice(0, history.length - 50);
        }
        
        this.sendingPatterns.set('choice_history', history);
    }
    
    saveSendingPatterns() {
        // 保存发送模式
        const patterns = Object.fromEntries(this.sendingPatterns);
        localStorage.setItem('mail_sending_patterns', JSON.stringify(patterns));
    }
    
    saveUserPreferences() {
        // 保存用户偏好
        const preferences = Object.fromEntries(this.userPreferences);
        localStorage.setItem('mail_schedule_preferences', JSON.stringify(preferences));
    }
}
```

## 使用示例

### 1. 基本计划对话框使用
```javascript
// 使用邮件计划对话框
class ScheduleMailDialog extends Component {
    static template = xml`
        <MailComposerScheduleDialog 
            t-props="scheduleProps"
            close="close"
            schedule="scheduleEmail"
            isNote="false"/>
    `;
    
    setup() {
        this.scheduleProps = useState({
            isNote: false
        });
        
        this.timeManager = new ScheduleTimeManager();
        this.smartScheduler = new SmartTimeScheduler();
    }
    
    async scheduleEmail(scheduledDateTime) {
        try {
            // 验证计划时间
            this.timeManager.validateScheduleTime(luxon.DateTime.fromISO(scheduledDateTime));
            
            // 记录用户选择
            this.smartScheduler.recordScheduleChoice(
                luxon.DateTime.fromISO(scheduledDateTime),
                'custom'
            );
            
            // 执行计划发送
            await this.env.services.mail.scheduleEmail({
                messageId: this.props.messageId,
                scheduledDate: scheduledDateTime
            });
            
            this.env.services.notification.add(
                '邮件已计划发送',
                { type: 'success' }
            );
            
        } catch (error) {
            console.error('计划发送失败:', error);
            this.env.services.notification.add(
                `计划发送失败: ${error.message}`,
                { type: 'danger' }
            );
        }
    }
    
    close() {
        this.props.close();
    }
}
```

### 2. 智能时间建议集成
```javascript
// 集成智能时间建议
class SmartScheduleDialog extends Component {
    setup() {
        this.smartScheduler = new SmartTimeScheduler();
        this.suggestions = useState([]);
        this.loadSmartSuggestions();
    }
    
    async loadSmartSuggestions() {
        // 加载智能建议
        const recipientTimeZones = await this.getRecipientTimeZones();
        const suggestions = this.smartScheduler.getSmartSuggestions(recipientTimeZones);
        
        this.suggestions.splice(0, this.suggestions.length, ...suggestions);
    }
    
    async getRecipientTimeZones() {
        // 获取收件人时区
        const recipientIds = this.props.recipientIds || [];
        if (recipientIds.length === 0) return [];
        
        try {
            const recipients = await this.env.services.orm.read(
                'res.partner',
                recipientIds,
                ['tz']
            );
            
            return recipients
                .map(r => r.tz)
                .filter(tz => tz);
                
        } catch (error) {
            console.error('获取收件人时区失败:', error);
            return [];
        }
    }
    
    selectSuggestion(suggestion) {
        // 选择建议时间
        this.scheduleEmail(suggestion.time.toISO());
        
        // 记录选择
        this.smartScheduler.recordScheduleChoice(suggestion.time, suggestion.type);
    }
}
```

## 邮件计划对话框特性

### 1. 预设时间选项
邮件计划对话框提供便捷的预设时间选项：
- **明天上午**: 明天 8:00
- **明天下午**: 明天 13:00  
- **下周一上午**: 下周一 8:00
- **自定义时间**: 用户自定义选择

### 2. 智能时间计算
```javascript
// 智能时间计算
const timeCalculations = {
    tomorrowMorning: () => today().plus({ days: 1 }).set({ hour: 8 }),
    tomorrowAfternoon: () => today().plus({ days: 1 }).set({ hour: 13 }),
    mondayMorning: () => {
        const daysUntilMonday = (1 - today().weekday + 7) % 7 || 7;
        return today().plus({ days: daysUntilMonday }).set({ hour: 8 });
    }
};
```

### 3. 时间验证和格式化
- **最小时间限制**: 不能选择过去的时间
- **时间格式化**: 用户友好的时间显示格式
- **时间序列化**: 标准化的时间数据传输
- **本地化支持**: 支持多语言时间显示

### 4. 用户体验优化
- **智能默认值**: 当前时间+1小时，分钟取整到5的倍数
- **直观界面**: 清晰的时间选择界面
- **即时反馈**: 实时显示选择的时间
- **快捷操作**: 一键选择常用时间

## 最佳实践

### 1. 时间选择
```javascript
// ✅ 推荐：智能的默认时间设置
const defaultTime = luxon.DateTime.now()
    .plus({ hours: 1 })
    .set({ 
        minutes: Math.ceil(now.minute / 5) * 5,
        seconds: 0,
        milliseconds: 0 
    });
```

### 2. 时间验证
```javascript
// ✅ 推荐：完善的时间验证
validateScheduleTime(dateTime) {
    if (dateTime <= luxon.DateTime.now()) {
        throw new Error('计划时间必须在未来');
    }
    return true;
}
```

### 3. 用户偏好
```javascript
// ✅ 推荐：记录和应用用户偏好
recordUserPreference(timeOption) {
    localStorage.setItem('preferred_schedule_option', timeOption);
}
```

### 4. 错误处理
```javascript
// ✅ 推荐：友好的错误处理
try {
    await this.schedule(scheduledDateTime);
} catch (error) {
    this.env.services.notification.add(
        '计划发送失败，请重试',
        { type: 'danger' }
    );
}
```

## 总结

Odoo 邮件撰写计划对话框模块提供了专业的邮件计划发送功能：

**核心优势**:
- **便捷选择**: 提供常用的预设时间选项
- **灵活自定义**: 支持精确的自定义时间选择
- **智能计算**: 自动计算合适的发送时间
- **用户友好**: 直观的界面和交互设计
- **时间验证**: 完善的时间验证和错误处理

**适用场景**:
- 邮件计划发送
- 营销邮件定时
- 重要通知安排
- 跨时区沟通
- 工作流程自动化

**设计优势**:
- 组件化架构
- 状态驱动设计
- 时间处理专业化
- 用户体验优先

这个邮件计划对话框为 Odoo Web 客户端提供了强大的邮件计划发送能力，是现代邮件系统不可或缺的重要功能。

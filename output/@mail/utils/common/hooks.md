# Hooks - 钩子工具模块

## 概述

`hooks.js` 是 Odoo 邮件系统的钩子工具模块，专门提供各种自定义React风格的钩子函数。该模块基于OWL框架，集成了外部事件监听、拖拽功能、状态管理、生命周期处理等核心功能，为邮件系统提供了完整的组件行为增强和交互处理支持，是邮件系统用户界面和交互体验的重要工具组件。

## 文件信息
- **路径**: `/mail/static/src/utils/common/hooks.js`
- **行数**: 502
- **模块**: `@mail/utils/common/hooks`

## 依赖关系

```javascript
// 核心依赖
'@odoo/owl'                                    // OWL 框架
'@web/core/browser/browser'                    // 浏览器工具
'@web/core/utils/concurrency'                 // 并发工具
'@web/core/utils/draggable_hook_builder_owl'  // 拖拽钩子构建器
'@web/core/utils/hooks'                       // 核心钩子
```

## 核心功能

### 1. 延迟外部监听器钩子

```javascript
function useLazyExternalListener(target, eventName, handler, eventParams) {
    const boundHandler = handler.bind(useComponent());
    let t;
    
    onMounted(() => {
        t = target();
        if (!t) {
            return;
        }
        t.addEventListener(eventName, boundHandler, eventParams);
    });
    
    onPatched(() => {
        const t2 = target();
        if (t !== t2) {
            if (t) {
                t.removeEventListener(eventName, boundHandler, eventParams);
            }
            if (t2) {
                t2.addEventListener(eventName, boundHandler, eventParams);
            }
            t = t2;
        }
    });
    
    onWillUnmount(() => {
        if (!t) {
            return;
        }
        t.removeEventListener(eventName, boundHandler, eventParams);
    });
}
```

**延迟监听器功能**:
- **动态目标**: 支持动态变化的事件目标
- **生命周期管理**: 自动管理事件监听器的添加和移除
- **内存安全**: 防止内存泄漏和重复监听
- **上下文绑定**: 正确绑定事件处理器的上下文

### 2. 拖拽钩子

```javascript
const useDraggable = makeDraggableHook({
    name: "useDraggable",
    onDragStart: ({ element, ctx }) => {
        // 拖拽开始处理
    },
    onDrag: ({ element, ctx }) => {
        // 拖拽过程处理
    },
    onDragEnd: ({ element, ctx }) => {
        // 拖拽结束处理
    }
});
```

**拖拽功能**:
- **拖拽生命周期**: 完整的拖拽事件生命周期管理
- **上下文传递**: 在拖拽过程中传递上下文信息
- **元素操作**: 直接操作DOM元素
- **事件处理**: 标准化的拖拽事件处理

### 3. 状态管理钩子

```javascript
function useStateWithCallback(initialState, callback) {
    const [state, setState] = useState(initialState);
    
    const setStateWithCallback = (newState) => {
        setState(newState);
        if (callback) {
            callback(newState);
        }
    };
    
    return [state, setStateWithCallback];
}
```

**状态管理功能**:
- **回调支持**: 状态变更时执行回调函数
- **类型安全**: 保持状态类型的一致性
- **副作用处理**: 处理状态变更的副作用
- **组合使用**: 可与其他钩子组合使用

### 4. 异步操作钩子

```javascript
function useAsyncEffect(asyncFn, deps) {
    useEffect(() => {
        let cancelled = false;
        
        const runAsync = async () => {
            try {
                const result = await asyncFn();
                if (!cancelled) {
                    // 处理结果
                }
            } catch (error) {
                if (!cancelled) {
                    console.error('异步操作失败:', error);
                }
            }
        };
        
        runAsync();
        
        return () => {
            cancelled = true;
        };
    }, deps);
}
```

**异步操作功能**:
- **取消机制**: 支持异步操作的取消
- **错误处理**: 完善的异步错误处理
- **依赖管理**: 基于依赖的重新执行
- **内存安全**: 防止组件卸载后的状态更新

## 使用场景

### 1. 钩子工具增强

```javascript
// 钩子工具增强功能
const HooksEnhancer = {
    enhanceHooks: () => {
        // 增强的钩子工具
        const enhancedHooks = {
            // 增强的延迟外部监听器
            useLazyExternalListener: (target, eventName, handler, options = {}) => {
                const component = useComponent();
                const boundHandler = handler.bind(component);
                let currentTarget = null;
                let isActive = false;
                
                const config = {
                    passive: options.passive || false,
                    capture: options.capture || false,
                    once: options.once || false,
                    debounce: options.debounce || 0,
                    throttle: options.throttle || 0,
                    ...options
                };
                
                // 防抖处理
                let debouncedHandler = boundHandler;
                if (config.debounce > 0) {
                    let debounceTimer;
                    debouncedHandler = (...args) => {
                        clearTimeout(debounceTimer);
                        debounceTimer = setTimeout(() => boundHandler(...args), config.debounce);
                    };
                }
                
                // 节流处理
                if (config.throttle > 0) {
                    let lastCall = 0;
                    const originalHandler = debouncedHandler;
                    debouncedHandler = (...args) => {
                        const now = Date.now();
                        if (now - lastCall >= config.throttle) {
                            lastCall = now;
                            originalHandler(...args);
                        }
                    };
                }
                
                const addListener = (element) => {
                    if (element && !isActive) {
                        element.addEventListener(eventName, debouncedHandler, {
                            passive: config.passive,
                            capture: config.capture,
                            once: config.once
                        });
                        isActive = true;
                    }
                };
                
                const removeListener = (element) => {
                    if (element && isActive) {
                        element.removeEventListener(eventName, debouncedHandler, {
                            capture: config.capture
                        });
                        isActive = false;
                    }
                };
                
                onMounted(() => {
                    currentTarget = typeof target === 'function' ? target() : target;
                    addListener(currentTarget);
                });
                
                onPatched(() => {
                    const newTarget = typeof target === 'function' ? target() : target;
                    if (currentTarget !== newTarget) {
                        removeListener(currentTarget);
                        currentTarget = newTarget;
                        addListener(currentTarget);
                    }
                });
                
                onWillUnmount(() => {
                    removeListener(currentTarget);
                });
                
                return {
                    isActive: () => isActive,
                    getCurrentTarget: () => currentTarget
                };
            },
            
            // 新增：智能状态钩子
            useSmartState: (initialState, options = {}) => {
                const [state, setState] = useState(initialState);
                const [history, setHistory] = useState([initialState]);
                const [currentIndex, setCurrentIndex] = useState(0);
                
                const config = {
                    maxHistory: options.maxHistory || 10,
                    enableUndo: options.enableUndo !== false,
                    enableRedo: options.enableRedo !== false,
                    validator: options.validator || null,
                    onChange: options.onChange || null,
                    ...options
                };
                
                const setSmartState = (newState, options = {}) => {
                    try {
                        // 验证新状态
                        if (config.validator && !config.validator(newState)) {
                            console.warn('状态验证失败:', newState);
                            return false;
                        }
                        
                        // 更新状态
                        setState(newState);
                        
                        // 更新历史记录
                        if (config.enableUndo && !options.skipHistory) {
                            const newHistory = history.slice(0, currentIndex + 1);
                            newHistory.push(newState);
                            
                            // 限制历史记录长度
                            if (newHistory.length > config.maxHistory) {
                                newHistory.shift();
                            } else {
                                setCurrentIndex(currentIndex + 1);
                            }
                            
                            setHistory(newHistory);
                        }
                        
                        // 触发变更回调
                        if (config.onChange) {
                            config.onChange(newState, state);
                        }
                        
                        return true;
                    } catch (error) {
                        console.error('设置智能状态失败:', error);
                        return false;
                    }
                };
                
                const undo = () => {
                    if (config.enableUndo && currentIndex > 0) {
                        const newIndex = currentIndex - 1;
                        const previousState = history[newIndex];
                        setState(previousState);
                        setCurrentIndex(newIndex);
                        
                        if (config.onChange) {
                            config.onChange(previousState, state);
                        }
                        
                        return true;
                    }
                    return false;
                };
                
                const redo = () => {
                    if (config.enableRedo && currentIndex < history.length - 1) {
                        const newIndex = currentIndex + 1;
                        const nextState = history[newIndex];
                        setState(nextState);
                        setCurrentIndex(newIndex);
                        
                        if (config.onChange) {
                            config.onChange(nextState, state);
                        }
                        
                        return true;
                    }
                    return false;
                };
                
                const reset = () => {
                    setState(initialState);
                    setHistory([initialState]);
                    setCurrentIndex(0);
                    
                    if (config.onChange) {
                        config.onChange(initialState, state);
                    }
                };
                
                return {
                    state,
                    setState: setSmartState,
                    undo,
                    redo,
                    reset,
                    canUndo: currentIndex > 0,
                    canRedo: currentIndex < history.length - 1,
                    history: history.slice(),
                    currentIndex
                };
            },
            
            // 新增：异步状态钩子
            useAsyncState: (asyncFn, initialState = null, deps = []) => {
                const [state, setState] = useState({
                    data: initialState,
                    loading: false,
                    error: null,
                    lastUpdated: null
                });
                
                const [abortController, setAbortController] = useState(null);
                
                const execute = async (options = {}) => {
                    try {
                        // 取消之前的请求
                        if (abortController) {
                            abortController.abort();
                        }
                        
                        const newController = new AbortController();
                        setAbortController(newController);
                        
                        setState(prev => ({
                            ...prev,
                            loading: true,
                            error: null
                        }));
                        
                        const result = await asyncFn({
                            signal: newController.signal,
                            ...options
                        });
                        
                        if (!newController.signal.aborted) {
                            setState({
                                data: result,
                                loading: false,
                                error: null,
                                lastUpdated: Date.now()
                            });
                        }
                        
                        return result;
                    } catch (error) {
                        if (!abortController?.signal.aborted) {
                            setState(prev => ({
                                ...prev,
                                loading: false,
                                error: error
                            }));
                        }
                        throw error;
                    }
                };
                
                const reset = () => {
                    if (abortController) {
                        abortController.abort();
                    }
                    setState({
                        data: initialState,
                        loading: false,
                        error: null,
                        lastUpdated: null
                    });
                };
                
                useEffect(() => {
                    if (deps.length > 0) {
                        execute();
                    }
                }, deps);
                
                onWillUnmount(() => {
                    if (abortController) {
                        abortController.abort();
                    }
                });
                
                return {
                    ...state,
                    execute,
                    reset,
                    isLoading: state.loading,
                    hasError: !!state.error,
                    hasData: state.data !== null
                };
            },
            
            // 新增：本地存储钩子
            useLocalStorage: (key, initialValue, options = {}) => {
                const config = {
                    serializer: options.serializer || JSON,
                    syncAcrossTabs: options.syncAcrossTabs !== false,
                    ...options
                };
                
                const [storedValue, setStoredValue] = useState(() => {
                    try {
                        const item = browser.localStorage.getItem(key);
                        return item ? config.serializer.parse(item) : initialValue;
                    } catch (error) {
                        console.error('读取本地存储失败:', error);
                        return initialValue;
                    }
                });
                
                const setValue = (value) => {
                    try {
                        const valueToStore = typeof value === 'function' ? value(storedValue) : value;
                        setStoredValue(valueToStore);
                        browser.localStorage.setItem(key, config.serializer.stringify(valueToStore));
                    } catch (error) {
                        console.error('设置本地存储失败:', error);
                    }
                };
                
                const removeValue = () => {
                    try {
                        setStoredValue(initialValue);
                        browser.localStorage.removeItem(key);
                    } catch (error) {
                        console.error('移除本地存储失败:', error);
                    }
                };
                
                // 跨标签页同步
                if (config.syncAcrossTabs) {
                    enhancedHooks.useLazyExternalListener(
                        () => window,
                        'storage',
                        (e) => {
                            if (e.key === key && e.newValue !== null) {
                                try {
                                    setStoredValue(config.serializer.parse(e.newValue));
                                } catch (error) {
                                    console.error('同步本地存储失败:', error);
                                }
                            }
                        }
                    );
                }
                
                return [storedValue, setValue, removeValue];
            },
            
            // 新增：防抖钩子
            useDebounce: (value, delay, options = {}) => {
                const [debouncedValue, setDebouncedValue] = useState(value);
                const config = {
                    leading: options.leading || false,
                    trailing: options.trailing !== false,
                    ...options
                };
                
                useEffect(() => {
                    let handler;
                    
                    if (config.leading && debouncedValue !== value) {
                        setDebouncedValue(value);
                    }
                    
                    if (config.trailing) {
                        handler = setTimeout(() => {
                            setDebouncedValue(value);
                        }, delay);
                    }
                    
                    return () => {
                        if (handler) {
                            clearTimeout(handler);
                        }
                    };
                }, [value, delay, config.leading, config.trailing]);
                
                return debouncedValue;
            },
            
            // 新增：节流钩子
            useThrottle: (value, limit) => {
                const [throttledValue, setThrottledValue] = useState(value);
                const lastRan = useRef(Date.now());
                
                useEffect(() => {
                    const handler = setTimeout(() => {
                        if (Date.now() - lastRan.current >= limit) {
                            setThrottledValue(value);
                            lastRan.current = Date.now();
                        }
                    }, limit - (Date.now() - lastRan.current));
                    
                    return () => {
                        clearTimeout(handler);
                    };
                }, [value, limit]);
                
                return throttledValue;
            },
            
            // 新增：媒体查询钩子
            useMediaQuery: (query) => {
                const [matches, setMatches] = useState(() => {
                    if (typeof window !== 'undefined') {
                        return window.matchMedia(query).matches;
                    }
                    return false;
                });
                
                useEffect(() => {
                    if (typeof window === 'undefined') {
                        return;
                    }
                    
                    const mediaQuery = window.matchMedia(query);
                    const handler = (e) => setMatches(e.matches);
                    
                    mediaQuery.addListener(handler);
                    setMatches(mediaQuery.matches);
                    
                    return () => mediaQuery.removeListener(handler);
                }, [query]);
                
                return matches;
            },
            
            // 新增：在线状态钩子
            useOnlineStatus: () => {
                const [isOnline, setIsOnline] = useState(() => {
                    if (typeof navigator !== 'undefined') {
                        return navigator.onLine;
                    }
                    return true;
                });
                
                enhancedHooks.useLazyExternalListener(
                    () => window,
                    'online',
                    () => setIsOnline(true)
                );
                
                enhancedHooks.useLazyExternalListener(
                    () => window,
                    'offline',
                    () => setIsOnline(false)
                );
                
                return isOnline;
            },
            
            // 新增：窗口大小钩子
            useWindowSize: () => {
                const [windowSize, setWindowSize] = useState(() => {
                    if (typeof window !== 'undefined') {
                        return {
                            width: window.innerWidth,
                            height: window.innerHeight
                        };
                    }
                    return { width: 0, height: 0 };
                });
                
                enhancedHooks.useLazyExternalListener(
                    () => window,
                    'resize',
                    () => {
                        setWindowSize({
                            width: window.innerWidth,
                            height: window.innerHeight
                        });
                    },
                    { throttle: 100 }
                );
                
                return windowSize;
            }
        };
        
        // 替换原始钩子
        Object.assign(__exports, enhancedHooks);
    }
};

// 应用钩子工具增强
HooksEnhancer.enhanceHooks();
```

## 技术特点

### 1. 生命周期管理
- 完整的组件生命周期钩子
- 自动的资源清理
- 内存泄漏防护

### 2. 事件处理
- 智能的事件监听器管理
- 动态目标支持
- 性能优化的事件处理

### 3. 状态管理
- 响应式状态更新
- 历史记录和撤销功能
- 异步状态处理

### 4. 实用工具
- 防抖和节流功能
- 本地存储集成
- 媒体查询支持

## 设计模式

### 1. 钩子模式 (Hook Pattern)
- 可复用的逻辑封装
- 组合式的功能增强

### 2. 观察者模式 (Observer Pattern)
- 事件监听和状态变更
- 响应式的数据更新

### 3. 策略模式 (Strategy Pattern)
- 不同的处理策略
- 可配置的行为选项

## 注意事项

1. **内存管理**: 确保事件监听器的正确清理
2. **性能优化**: 避免频繁的重新渲染
3. **依赖管理**: 正确管理钩子的依赖数组
4. **错误处理**: 提供完善的错误处理机制

## 扩展建议

1. **更多钩子**: 添加更多实用的自定义钩子
2. **性能监控**: 实现钩子性能的监控和分析
3. **调试工具**: 提供钩子状态的调试工具
4. **类型安全**: 添加TypeScript类型定义
5. **测试支持**: 提供钩子的测试工具和模拟

该钩子工具模块为邮件系统提供了重要的组件行为增强功能，是用户界面和交互体验的核心工具组件。

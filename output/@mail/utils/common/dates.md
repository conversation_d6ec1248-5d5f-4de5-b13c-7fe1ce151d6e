# Dates - 日期工具模块

## 概述

`dates.js` 是 Odoo 邮件系统的日期工具模块，专门提供日期和时间相关的工具函数。该模块基于Luxon日期库，集成了日期计算、时间差计算、日期判断等核心功能，为邮件系统提供了完整的日期处理和时间计算支持，是邮件系统时间管理和日期显示的重要工具组件。

## 文件信息
- **路径**: `/mail/static/src/utils/common/dates.js`
- **行数**: 45
- **模块**: `@mail/utils/common/dates`

## 依赖关系

```javascript
// 核心依赖
const { DateTime } = luxon;   // Luxon 日期库
```

## 核心功能

### 1. 计算延迟天数

```javascript
function computeDelay(datetime) {
    if (!datetime) {
        return 0;
    }
    const today = DateTime.now().startOf("day");
    return datetime.diff(today, "days").days;
}
```

**延迟计算功能**:
- **参数验证**: 检查datetime参数的有效性
- **基准时间**: 使用当天开始时间作为基准
- **天数差异**: 计算目标日期与今天的天数差异
- **返回值**: 返回正数表示未来，负数表示过去

### 2. 获取到明天的毫秒数

```javascript
function getMsToTomorrow() {
    const now = new Date();
    const night = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1, // the next day
        0,
        0,
        0 // at 00:00:00 hours
    );
    return night.getTime() - now.getTime();
}
```

**明天毫秒数功能**:
- **当前时间**: 获取当前的精确时间
- **明天零点**: 计算明天00:00:00的时间点
- **时间差**: 计算当前时间到明天零点的毫秒数
- **用途**: 常用于定时器和倒计时功能

### 3. 判断是否为今天

```javascript
function isToday(datetime) {
    if (!datetime) {
        return false;
    }
    return (
        datetime.toLocaleString(DateTime.DATE_FULL) ===
        DateTime.now().toLocaleString(DateTime.DATE_FULL)
    );
}
```

**今天判断功能**:
- **参数验证**: 检查datetime参数的有效性
- **日期格式**: 使用完整日期格式进行比较
- **精确比较**: 比较日期部分而忽略时间部分
- **本地化**: 使用本地化的日期格式

## 使用场景

### 1. 日期工具增强

```javascript
// 日期工具增强功能
const DatesEnhancer = {
    enhanceDates: () => {
        // 增强的日期工具函数
        const enhancedDateUtils = {
            // 增强的延迟计算
            computeDelay: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return 0;
                    }
                    
                    // 支持多种输入格式
                    let luxonDateTime;
                    if (typeof datetime === 'string') {
                        luxonDateTime = DateTime.fromISO(datetime);
                    } else if (datetime instanceof Date) {
                        luxonDateTime = DateTime.fromJSDate(datetime);
                    } else if (datetime.isLuxonDateTime) {
                        luxonDateTime = datetime;
                    } else {
                        throw new Error('不支持的日期格式');
                    }
                    
                    // 验证日期有效性
                    if (!luxonDateTime.isValid) {
                        throw new Error('无效的日期');
                    }
                    
                    // 获取基准时间
                    const baseTime = options.baseTime ? 
                        DateTime.fromISO(options.baseTime) : 
                        DateTime.now();
                    
                    const today = baseTime.startOf("day");
                    
                    // 计算差异
                    const diff = luxonDateTime.diff(today, "days").days;
                    
                    // 根据选项处理结果
                    if (options.absolute) {
                        return Math.abs(diff);
                    }
                    
                    if (options.rounded) {
                        return Math.round(diff);
                    }
                    
                    return diff;
                } catch (error) {
                    console.error('计算延迟失败:', error);
                    return 0;
                }
            },
            
            // 增强的明天毫秒数计算
            getMsToTomorrow: (options = {}) => {
                try {
                    const now = options.baseTime ? new Date(options.baseTime) : new Date();
                    
                    // 计算目标时间
                    const targetDate = new Date(now);
                    targetDate.setDate(targetDate.getDate() + (options.daysOffset || 1));
                    targetDate.setHours(options.targetHour || 0);
                    targetDate.setMinutes(options.targetMinute || 0);
                    targetDate.setSeconds(options.targetSecond || 0);
                    targetDate.setMilliseconds(0);
                    
                    const diff = targetDate.getTime() - now.getTime();
                    
                    // 确保返回正数
                    return Math.max(0, diff);
                } catch (error) {
                    console.error('计算到明天毫秒数失败:', error);
                    return 0;
                }
            },
            
            // 增强的今天判断
            isToday: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return false;
                    }
                    
                    // 支持多种输入格式
                    let luxonDateTime;
                    if (typeof datetime === 'string') {
                        luxonDateTime = DateTime.fromISO(datetime);
                    } else if (datetime instanceof Date) {
                        luxonDateTime = DateTime.fromJSDate(datetime);
                    } else if (datetime.isLuxonDateTime) {
                        luxonDateTime = datetime;
                    } else {
                        return false;
                    }
                    
                    if (!luxonDateTime.isValid) {
                        return false;
                    }
                    
                    // 获取比较基准
                    const baseTime = options.baseTime ? 
                        DateTime.fromISO(options.baseTime) : 
                        DateTime.now();
                    
                    // 使用时区
                    if (options.timezone) {
                        luxonDateTime = luxonDateTime.setZone(options.timezone);
                        baseTime = baseTime.setZone(options.timezone);
                    }
                    
                    // 比较日期
                    return luxonDateTime.toISODate() === baseTime.toISODate();
                } catch (error) {
                    console.error('判断是否为今天失败:', error);
                    return false;
                }
            },
            
            // 新增：判断是否为昨天
            isYesterday: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return false;
                    }
                    
                    const luxonDateTime = enhancedDateUtils.parseDatetime(datetime);
                    if (!luxonDateTime || !luxonDateTime.isValid) {
                        return false;
                    }
                    
                    const baseTime = options.baseTime ? 
                        DateTime.fromISO(options.baseTime) : 
                        DateTime.now();
                    
                    const yesterday = baseTime.minus({ days: 1 });
                    
                    return luxonDateTime.toISODate() === yesterday.toISODate();
                } catch (error) {
                    console.error('判断是否为昨天失败:', error);
                    return false;
                }
            },
            
            // 新增：判断是否为明天
            isTomorrow: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return false;
                    }
                    
                    const luxonDateTime = enhancedDateUtils.parseDatetime(datetime);
                    if (!luxonDateTime || !luxonDateTime.isValid) {
                        return false;
                    }
                    
                    const baseTime = options.baseTime ? 
                        DateTime.fromISO(options.baseTime) : 
                        DateTime.now();
                    
                    const tomorrow = baseTime.plus({ days: 1 });
                    
                    return luxonDateTime.toISODate() === tomorrow.toISODate();
                } catch (error) {
                    console.error('判断是否为明天失败:', error);
                    return false;
                }
            },
            
            // 新增：获取相对日期描述
            getRelativeDateDescription: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return '';
                    }
                    
                    const luxonDateTime = enhancedDateUtils.parseDatetime(datetime);
                    if (!luxonDateTime || !luxonDateTime.isValid) {
                        return '';
                    }
                    
                    const delay = enhancedDateUtils.computeDelay(luxonDateTime, options);
                    
                    if (delay === 0) {
                        return options.todayText || '今天';
                    } else if (delay === -1) {
                        return options.yesterdayText || '昨天';
                    } else if (delay === 1) {
                        return options.tomorrowText || '明天';
                    } else if (delay < 0) {
                        return options.pastFormat ? 
                            options.pastFormat.replace('{days}', Math.abs(delay)) :
                            `${Math.abs(delay)}天前`;
                    } else {
                        return options.futureFormat ? 
                            options.futureFormat.replace('{days}', delay) :
                            `${delay}天后`;
                    }
                } catch (error) {
                    console.error('获取相对日期描述失败:', error);
                    return '';
                }
            },
            
            // 新增：格式化日期时间
            formatDateTime: (datetime, options = {}) => {
                try {
                    if (!datetime) {
                        return '';
                    }
                    
                    const luxonDateTime = enhancedDateUtils.parseDatetime(datetime);
                    if (!luxonDateTime || !luxonDateTime.isValid) {
                        return '';
                    }
                    
                    // 应用时区
                    let formattedDateTime = luxonDateTime;
                    if (options.timezone) {
                        formattedDateTime = formattedDateTime.setZone(options.timezone);
                    }
                    
                    // 根据格式选项格式化
                    if (options.format) {
                        return formattedDateTime.toFormat(options.format);
                    }
                    
                    // 默认格式
                    if (options.dateOnly) {
                        return formattedDateTime.toLocaleString(DateTime.DATE_FULL);
                    }
                    
                    if (options.timeOnly) {
                        return formattedDateTime.toLocaleString(DateTime.TIME_SIMPLE);
                    }
                    
                    return formattedDateTime.toLocaleString(DateTime.DATETIME_MED);
                } catch (error) {
                    console.error('格式化日期时间失败:', error);
                    return '';
                }
            },
            
            // 新增：解析日期时间
            parseDatetime: (datetime) => {
                try {
                    if (!datetime) {
                        return null;
                    }
                    
                    if (typeof datetime === 'string') {
                        // 尝试多种格式
                        let parsed = DateTime.fromISO(datetime);
                        if (parsed.isValid) return parsed;
                        
                        parsed = DateTime.fromSQL(datetime);
                        if (parsed.isValid) return parsed;
                        
                        parsed = DateTime.fromRFC2822(datetime);
                        if (parsed.isValid) return parsed;
                        
                        parsed = DateTime.fromHTTP(datetime);
                        if (parsed.isValid) return parsed;
                        
                        return null;
                    } else if (datetime instanceof Date) {
                        return DateTime.fromJSDate(datetime);
                    } else if (datetime.isLuxonDateTime) {
                        return datetime;
                    } else if (typeof datetime === 'number') {
                        return DateTime.fromMillis(datetime);
                    }
                    
                    return null;
                } catch (error) {
                    console.error('解析日期时间失败:', error);
                    return null;
                }
            },
            
            // 新增：获取工作日
            getWorkingDays: (startDate, endDate, options = {}) => {
                try {
                    const start = enhancedDateUtils.parseDatetime(startDate);
                    const end = enhancedDateUtils.parseDatetime(endDate);
                    
                    if (!start || !end || !start.isValid || !end.isValid) {
                        return 0;
                    }
                    
                    const weekends = options.weekends || [6, 7]; // 默认周六周日
                    const holidays = options.holidays || [];
                    
                    let workingDays = 0;
                    let current = start.startOf('day');
                    const endDay = end.startOf('day');
                    
                    while (current <= endDay) {
                        const weekday = current.weekday;
                        const dateStr = current.toISODate();
                        
                        // 检查是否为周末
                        if (!weekends.includes(weekday) && !holidays.includes(dateStr)) {
                            workingDays++;
                        }
                        
                        current = current.plus({ days: 1 });
                    }
                    
                    return workingDays;
                } catch (error) {
                    console.error('计算工作日失败:', error);
                    return 0;
                }
            },
            
            // 新增：时间范围检查
            isInTimeRange: (datetime, startTime, endTime, options = {}) => {
                try {
                    const target = enhancedDateUtils.parseDatetime(datetime);
                    if (!target || !target.isValid) {
                        return false;
                    }
                    
                    const start = enhancedDateUtils.parseDatetime(startTime);
                    const end = enhancedDateUtils.parseDatetime(endTime);
                    
                    if (!start || !end || !start.isValid || !end.isValid) {
                        return false;
                    }
                    
                    if (options.inclusive) {
                        return target >= start && target <= end;
                    } else {
                        return target > start && target < end;
                    }
                } catch (error) {
                    console.error('检查时间范围失败:', error);
                    return false;
                }
            },
            
            // 新增：获取时间差描述
            getTimeDifferenceDescription: (datetime1, datetime2, options = {}) => {
                try {
                    const dt1 = enhancedDateUtils.parseDatetime(datetime1);
                    const dt2 = enhancedDateUtils.parseDatetime(datetime2);
                    
                    if (!dt1 || !dt2 || !dt1.isValid || !dt2.isValid) {
                        return '';
                    }
                    
                    const diff = dt2.diff(dt1);
                    const duration = diff.shiftTo('years', 'months', 'days', 'hours', 'minutes', 'seconds');
                    
                    const parts = [];
                    
                    if (duration.years > 0) {
                        parts.push(`${Math.floor(duration.years)}年`);
                    }
                    if (duration.months > 0) {
                        parts.push(`${Math.floor(duration.months)}月`);
                    }
                    if (duration.days > 0) {
                        parts.push(`${Math.floor(duration.days)}天`);
                    }
                    if (duration.hours > 0 && parts.length < 2) {
                        parts.push(`${Math.floor(duration.hours)}小时`);
                    }
                    if (duration.minutes > 0 && parts.length < 2) {
                        parts.push(`${Math.floor(duration.minutes)}分钟`);
                    }
                    if (parts.length === 0) {
                        parts.push('不到1分钟');
                    }
                    
                    return parts.slice(0, options.maxParts || 2).join('');
                } catch (error) {
                    console.error('获取时间差描述失败:', error);
                    return '';
                }
            }
        };
        
        // 替换原始函数
        Object.assign(__exports, enhancedDateUtils);
    }
};

// 应用日期工具增强
DatesEnhancer.enhanceDates();
```

## 技术特点

### 1. Luxon集成
- 基于现代Luxon日期库
- 支持时区和本地化
- 提供丰富的日期操作API

### 2. 多格式支持
- 支持多种日期输入格式
- 智能的日期解析
- 灵活的格式化选项

### 3. 实用工具
- 常用的日期计算函数
- 相对时间的计算
- 工作日和时间范围处理

### 4. 错误处理
- 完善的参数验证
- 安全的错误处理
- 降级处理机制

## 设计模式

### 1. 工具模式 (Utility Pattern)
- 提供静态的工具函数
- 无状态的功能实现

### 2. 策略模式 (Strategy Pattern)
- 不同的日期处理策略
- 可配置的格式化选项

### 3. 适配器模式 (Adapter Pattern)
- 适配不同的日期格式
- 统一的接口设计

## 注意事项

1. **时区处理**: 注意时区对日期计算的影响
2. **格式兼容**: 确保日期格式的兼容性
3. **性能考虑**: 避免频繁的日期对象创建
4. **边界情况**: 处理无效日期和边界值

## 扩展建议

1. **时区支持**: 添加更完善的时区处理功能
2. **本地化**: 支持更多语言的日期格式
3. **缓存优化**: 实现日期计算结果的缓存
4. **业务日历**: 支持业务日历和假期管理
5. **性能优化**: 优化频繁调用的日期函数

该日期工具模块为邮件系统提供了重要的日期处理功能，是时间管理和显示的核心工具组件。

# Format - 格式化工具模块

## 概述

`format.js` 是 Odoo 邮件系统的格式化工具模块，专门提供文本格式化、HTML处理、URL链接化、表情符号处理等功能。该模块基于Web核心工具，集成了字符串转义、内容美化、链接识别、表情符号加载等核心功能，为邮件系统提供了完整的内容格式化和显示处理支持，是邮件内容渲染和用户体验的重要组件。

## 文件信息
- **路径**: `/mail/static/src/utils/common/format.js`
- **行数**: 293
- **模块**: `@mail/utils/common/format`

## 依赖关系

```javascript
// 核心依赖
'@web/core/browser/router'           // 路由工具
'@web/core/emoji_picker/emoji_picker' // 表情符号选择器
'@web/core/utils/strings'            // 字符串工具
```

## 核心功能

### 1. URL正则表达式

```javascript
const urlRegexp =
    /\b(?:https?:\/\/\d{1,3}(?:\.\d{1,3}){3}|(?:https?:\/\/|(?:www\.))[-a-z0-9@:%._+~#=\u00C0-\u024F\u1E00-\u1EFF]{2,256}\.[a-z]{2,13})\b(?:[-a-z0-9@:%_+~#?&[\]^|{}`\\'$//=\u00C0-\u024F\u1E00-\u1EFF]|[.]*[-a-z0-9@:%_+~#?&[\]^|{}`\\'$//=\u00C0-\u024F\u1E00-\u1EFF]|,(?!$| )|\.(?!$| |\.)|;(?!$| ))*/gi;
```

**URL识别功能**:
- **IP地址**: 支持IPv4地址格式的URL
- **域名**: 支持标准域名格式
- **协议**: 支持http和https协议
- **国际化**: 支持拉丁字符扩展集
- **特殊字符**: 处理URL中的特殊字符和标点

### 2. HTML实体转义

```javascript
const _escapeEntities = (function () {
    const map = { "&": "&amp;", "<": "&lt;", ">": "&gt;" };
    const escaper = function (match) {
        return map[match];
    };
    const testRegexp = RegExp("(?:&|<|>)");
    const replaceRegexp = RegExp("(?:&|<|>)", "g");
    return function (string) {
        string = string == null ? "" : "" + string;
        return testRegexp.test(string) ? string.replace(replaceRegexp, escaper) : string;
    };
})();
```

**实体转义功能**:
- **安全转义**: 转义HTML特殊字符防止XSS攻击
- **性能优化**: 使用闭包和预编译正则表达式
- **字符映射**: 将危险字符转换为安全的HTML实体
- **空值处理**: 安全处理null和undefined值

### 3. 消息内容美化

```javascript
async function prettifyMessageContent(rawBody, validRecords = []) {
    const escapedAndCompactContent = escapeAndCompactTextContent(rawBody);
    let body = escapedAndCompactContent.replace(/&nbsp;/g, " ").trim();
    
    // URL链接化处理
    body = body.replace(urlRegexp, function (url) {
        let href = url;
        if (url.indexOf("http") !== 0) {
            href = "http://" + url;
        }
        return `<a href="${href}" target="_blank" rel="noreferrer">${url}</a>`;
    });
    
    // 表情符号处理
    body = await loadEmoji(body);
    
    return body;
}
```

**内容美化功能**:
- **文本转义**: 安全转义用户输入内容
- **URL链接化**: 自动识别并转换URL为可点击链接
- **表情符号**: 加载和显示表情符号
- **格式清理**: 清理和标准化文本格式

### 4. 文本内容转义和压缩

```javascript
function escapeAndCompactTextContent(content) {
    // 转义HTML实体
    let escaped = _escapeEntities(content);
    
    // 压缩空白字符
    escaped = escaped.replace(/\s+/g, ' ');
    
    // 处理换行
    escaped = escaped.replace(/\n/g, '<br>');
    
    return escaped;
}
```

**转义压缩功能**:
- **实体转义**: 转义HTML特殊字符
- **空白压缩**: 压缩多余的空白字符
- **换行处理**: 将换行符转换为HTML换行标签
- **格式保持**: 保持文本的基本格式结构

## 使用场景

### 1. 格式化工具增强

```javascript
// 格式化工具增强功能
const FormatEnhancer = {
    enhanceFormat: () => {
        // 增强的格式化工具
        const enhancedFormatUtils = {
            // 增强的消息内容美化
            prettifyMessageContent: async (rawBody, validRecords = [], options = {}) => {
                try {
                    if (!rawBody || typeof rawBody !== 'string') {
                        return '';
                    }
                    
                    // 预处理选项
                    const config = {
                        enableUrlLinking: options.enableUrlLinking !== false,
                        enableEmoji: options.enableEmoji !== false,
                        enableMentions: options.enableMentions !== false,
                        enableHashtags: options.enableHashtags !== false,
                        maxLength: options.maxLength || null,
                        allowedTags: options.allowedTags || ['br', 'a', 'strong', 'em'],
                        linkTarget: options.linkTarget || '_blank',
                        linkRel: options.linkRel || 'noreferrer noopener',
                        ...options
                    };
                    
                    // 长度限制
                    let content = rawBody;
                    if (config.maxLength && content.length > config.maxLength) {
                        content = content.substring(0, config.maxLength) + '...';
                    }
                    
                    // 转义和压缩内容
                    let body = enhancedFormatUtils.escapeAndCompactTextContent(content, config);
                    
                    // URL链接化
                    if (config.enableUrlLinking) {
                        body = enhancedFormatUtils.linkifyUrls(body, config);
                    }
                    
                    // 提及处理
                    if (config.enableMentions && validRecords.partners) {
                        body = enhancedFormatUtils.linkifyMentions(body, validRecords.partners, config);
                    }
                    
                    // 话题标签处理
                    if (config.enableHashtags) {
                        body = enhancedFormatUtils.linkifyHashtags(body, config);
                    }
                    
                    // 表情符号处理
                    if (config.enableEmoji) {
                        body = await enhancedFormatUtils.processEmojis(body, config);
                    }
                    
                    // 内容清理
                    body = enhancedFormatUtils.sanitizeContent(body, config);
                    
                    return body;
                } catch (error) {
                    console.error('美化消息内容失败:', error);
                    return _escapeEntities(rawBody || '');
                }
            },
            
            // 增强的转义和压缩
            escapeAndCompactTextContent: (content, options = {}) => {
                try {
                    if (!content) {
                        return '';
                    }
                    
                    // 转义HTML实体
                    let escaped = _escapeEntities(content);
                    
                    // 处理换行
                    if (options.preserveLineBreaks !== false) {
                        escaped = escaped.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
                        escaped = escaped.replace(/\n/g, '<br>');
                    }
                    
                    // 压缩空白字符
                    if (options.compactWhitespace !== false) {
                        escaped = escaped.replace(/[ \t]+/g, ' ');
                    }
                    
                    // 处理特殊空格
                    escaped = escaped.replace(/&nbsp;/g, ' ');
                    
                    // 清理首尾空白
                    escaped = escaped.trim();
                    
                    return escaped;
                } catch (error) {
                    console.error('转义和压缩文本失败:', error);
                    return _escapeEntities(content || '');
                }
            },
            
            // 增强的URL链接化
            linkifyUrls: (text, options = {}) => {
                try {
                    const config = {
                        target: options.linkTarget || '_blank',
                        rel: options.linkRel || 'noreferrer noopener',
                        className: options.linkClassName || '',
                        maxLength: options.urlMaxLength || 50,
                        ...options
                    };
                    
                    return text.replace(urlRegexp, (url) => {
                        let href = url;
                        let displayUrl = url;
                        
                        // 添加协议
                        if (!/^https?:\/\//i.test(url)) {
                            href = 'http://' + url;
                        }
                        
                        // 截断显示URL
                        if (config.maxLength && displayUrl.length > config.maxLength) {
                            displayUrl = displayUrl.substring(0, config.maxLength) + '...';
                        }
                        
                        // 构建链接属性
                        const attributes = [];
                        attributes.push(`href="${_escapeEntities(href)}"`);
                        
                        if (config.target) {
                            attributes.push(`target="${config.target}"`);
                        }
                        
                        if (config.rel) {
                            attributes.push(`rel="${config.rel}"`);
                        }
                        
                        if (config.className) {
                            attributes.push(`class="${config.className}"`);
                        }
                        
                        return `<a ${attributes.join(' ')}>${_escapeEntities(displayUrl)}</a>`;
                    });
                } catch (error) {
                    console.error('URL链接化失败:', error);
                    return text;
                }
            },
            
            // 新增：提及链接化
            linkifyMentions: (text, partners, options = {}) => {
                try {
                    if (!partners || !Array.isArray(partners)) {
                        return text;
                    }
                    
                    const config = {
                        className: options.mentionClassName || 'o_mail_mention',
                        prefix: options.mentionPrefix || '@',
                        ...options
                    };
                    
                    // 按名称长度排序，避免短名称覆盖长名称
                    const sortedPartners = partners.sort((a, b) => b.name.length - a.name.length);
                    
                    let result = text;
                    
                    for (const partner of sortedPartners) {
                        const escapedName = _escapeEntities(partner.name);
                        const mentionRegex = new RegExp(
                            `${config.prefix}(${partner.name.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})\\b`,
                            'gi'
                        );
                        
                        result = result.replace(mentionRegex, (match, name) => {
                            const attributes = [`class="${config.className}"`];
                            
                            if (partner.id) {
                                attributes.push(`data-partner-id="${partner.id}"`);
                            }
                            
                            return `<span ${attributes.join(' ')}>${config.prefix}${escapedName}</span>`;
                        });
                    }
                    
                    return result;
                } catch (error) {
                    console.error('提及链接化失败:', error);
                    return text;
                }
            },
            
            // 新增：话题标签链接化
            linkifyHashtags: (text, options = {}) => {
                try {
                    const config = {
                        className: options.hashtagClassName || 'o_mail_hashtag',
                        prefix: options.hashtagPrefix || '#',
                        ...options
                    };
                    
                    const hashtagRegex = new RegExp(
                        `${config.prefix}([a-zA-Z0-9_\\u00C0-\\u024F\\u1E00-\\u1EFF]+)\\b`,
                        'g'
                    );
                    
                    return text.replace(hashtagRegex, (match, tag) => {
                        const escapedTag = _escapeEntities(tag);
                        const attributes = [`class="${config.className}"`];
                        
                        if (config.onClick) {
                            attributes.push(`data-hashtag="${escapedTag}"`);
                            attributes.push('style="cursor: pointer;"');
                        }
                        
                        return `<span ${attributes.join(' ')}>${config.prefix}${escapedTag}</span>`;
                    });
                } catch (error) {
                    console.error('话题标签链接化失败:', error);
                    return text;
                }
            },
            
            // 增强的表情符号处理
            processEmojis: async (text, options = {}) => {
                try {
                    const config = {
                        enableShortcodes: options.enableShortcodes !== false,
                        enableUnicode: options.enableUnicode !== false,
                        emojiSize: options.emojiSize || 'small',
                        ...options
                    };
                    
                    let result = text;
                    
                    // 处理表情符号短代码
                    if (config.enableShortcodes) {
                        result = await loadEmoji(result);
                    }
                    
                    // 处理Unicode表情符号
                    if (config.enableUnicode) {
                        result = enhancedFormatUtils.enhanceUnicodeEmojis(result, config);
                    }
                    
                    return result;
                } catch (error) {
                    console.error('处理表情符号失败:', error);
                    return text;
                }
            },
            
            // 新增：增强Unicode表情符号
            enhanceUnicodeEmojis: (text, options = {}) => {
                try {
                    const config = {
                        className: options.emojiClassName || 'o_mail_emoji',
                        size: options.emojiSize || 'small',
                        ...options
                    };
                    
                    // Unicode表情符号正则表达式
                    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
                    
                    return text.replace(emojiRegex, (emoji) => {
                        const attributes = [`class="${config.className}"`];
                        
                        if (config.size) {
                            attributes.push(`data-size="${config.size}"`);
                        }
                        
                        return `<span ${attributes.join(' ')}>${emoji}</span>`;
                    });
                } catch (error) {
                    console.error('增强Unicode表情符号失败:', error);
                    return text;
                }
            },
            
            // 新增：内容清理
            sanitizeContent: (content, options = {}) => {
                try {
                    const config = {
                        allowedTags: options.allowedTags || ['br', 'a', 'strong', 'em', 'span'],
                        allowedAttributes: options.allowedAttributes || {
                            'a': ['href', 'target', 'rel', 'class'],
                            'span': ['class', 'data-partner-id', 'data-hashtag', 'data-size']
                        },
                        ...options
                    };
                    
                    // 简单的HTML清理（生产环境应使用专业的HTML清理库）
                    let cleaned = content;
                    
                    // 移除不允许的标签
                    const tagRegex = /<(\/?)([\w]+)([^>]*)>/g;
                    cleaned = cleaned.replace(tagRegex, (match, closing, tagName, attributes) => {
                        const lowerTagName = tagName.toLowerCase();
                        
                        if (!config.allowedTags.includes(lowerTagName)) {
                            return '';
                        }
                        
                        if (closing) {
                            return `</${lowerTagName}>`;
                        }
                        
                        // 清理属性
                        const allowedAttrs = config.allowedAttributes[lowerTagName] || [];
                        const cleanAttributes = enhancedFormatUtils.sanitizeAttributes(attributes, allowedAttrs);
                        
                        return `<${lowerTagName}${cleanAttributes}>`;
                    });
                    
                    return cleaned;
                } catch (error) {
                    console.error('内容清理失败:', error);
                    return content;
                }
            },
            
            // 新增：属性清理
            sanitizeAttributes: (attributeString, allowedAttributes) => {
                try {
                    if (!attributeString || !allowedAttributes.length) {
                        return '';
                    }
                    
                    const attrRegex = /(\w+)=["']([^"']*)["']/g;
                    const cleanAttrs = [];
                    let match;
                    
                    while ((match = attrRegex.exec(attributeString)) !== null) {
                        const [, attrName, attrValue] = match;
                        
                        if (allowedAttributes.includes(attrName.toLowerCase())) {
                            const escapedValue = _escapeEntities(attrValue);
                            cleanAttrs.push(`${attrName}="${escapedValue}"`);
                        }
                    }
                    
                    return cleanAttrs.length ? ' ' + cleanAttrs.join(' ') : '';
                } catch (error) {
                    console.error('属性清理失败:', error);
                    return '';
                }
            },
            
            // 新增：文本截断
            truncateText: (text, maxLength, options = {}) => {
                try {
                    if (!text || typeof text !== 'string') {
                        return '';
                    }
                    
                    if (!maxLength || text.length <= maxLength) {
                        return text;
                    }
                    
                    const config = {
                        suffix: options.suffix || '...',
                        wordBoundary: options.wordBoundary !== false,
                        stripHtml: options.stripHtml || false,
                        ...options
                    };
                    
                    let content = text;
                    
                    // 移除HTML标签
                    if (config.stripHtml) {
                        content = content.replace(/<[^>]*>/g, '');
                    }
                    
                    if (content.length <= maxLength) {
                        return content;
                    }
                    
                    let truncated = content.substring(0, maxLength - config.suffix.length);
                    
                    // 在单词边界截断
                    if (config.wordBoundary) {
                        const lastSpace = truncated.lastIndexOf(' ');
                        if (lastSpace > maxLength * 0.8) { // 至少保留80%的长度
                            truncated = truncated.substring(0, lastSpace);
                        }
                    }
                    
                    return truncated + config.suffix;
                } catch (error) {
                    console.error('文本截断失败:', error);
                    return text;
                }
            },
            
            // 新增：移除HTML标签
            stripHtmlTags: (html, options = {}) => {
                try {
                    if (!html || typeof html !== 'string') {
                        return '';
                    }
                    
                    const config = {
                        preserveLineBreaks: options.preserveLineBreaks !== false,
                        preserveSpaces: options.preserveSpaces !== false,
                        ...options
                    };
                    
                    let text = html;
                    
                    // 将<br>转换为换行符
                    if (config.preserveLineBreaks) {
                        text = text.replace(/<br\s*\/?>/gi, '\n');
                    }
                    
                    // 移除所有HTML标签
                    text = text.replace(/<[^>]*>/g, '');
                    
                    // 解码HTML实体
                    text = text.replace(/&lt;/g, '<')
                              .replace(/&gt;/g, '>')
                              .replace(/&amp;/g, '&')
                              .replace(/&quot;/g, '"')
                              .replace(/&#39;/g, "'");
                    
                    // 压缩空白字符
                    if (!config.preserveSpaces) {
                        text = text.replace(/\s+/g, ' ');
                    }
                    
                    return text.trim();
                } catch (error) {
                    console.error('移除HTML标签失败:', error);
                    return html;
                }
            }
        };
        
        // 替换原始函数
        Object.assign(__exports, enhancedFormatUtils);
    }
};

// 应用格式化工具增强
FormatEnhancer.enhanceFormat();
```

## 技术特点

### 1. 安全处理
- HTML实体转义防止XSS攻击
- 内容清理和验证
- 安全的链接处理

### 2. 智能识别
- 高精度的URL识别正则表达式
- 支持国际化字符集
- 智能的内容解析

### 3. 丰富格式
- 表情符号支持
- 提及和话题标签
- 多样化的文本格式

### 4. 性能优化
- 预编译正则表达式
- 高效的字符串处理
- 缓存和复用机制

## 设计模式

### 1. 策略模式 (Strategy Pattern)
- 不同的格式化策略
- 可配置的处理选项

### 2. 装饰器模式 (Decorator Pattern)
- 逐步增强文本内容
- 可组合的格式化功能

### 3. 工厂模式 (Factory Pattern)
- 统一的格式化接口
- 类型化的处理器创建

## 注意事项

1. **安全性**: 确保所有用户输入都经过适当的转义
2. **性能**: 避免复杂正则表达式影响性能
3. **兼容性**: 确保格式化结果在不同浏览器中一致
4. **国际化**: 支持多语言字符和格式

## 扩展建议

1. **富文本编辑**: 支持更丰富的文本编辑功能
2. **插件系统**: 提供可扩展的格式化插件
3. **缓存优化**: 实现格式化结果的缓存
4. **实时预览**: 提供实时的格式化预览
5. **自定义规则**: 支持用户自定义格式化规则

该格式化工具模块为邮件系统提供了重要的内容处理功能，是用户体验和内容安全的核心组件。

# Partner Autocomplete Char Field - 合作伙伴自动完成字符字段

## 概述

`partner_autocomplete_fieldchar.js` 是 Odoo Partner Autocomplete 模块的字符字段组件，专门用于在字符输入字段中提供合作伙伴信息的自动完成功能。该组件基于Odoo的字符字段和自动完成组件，集成了合作伙伴自动完成核心功能，为用户提供智能的公司信息输入和自动填充体验。

## 文件信息
- **路径**: `/partner_autocomplete/static/src/js/partner_autocomplete_fieldchar.js`
- **行数**: 98
- **模块**: `@partner_autocomplete/js/partner_autocomplete_fieldchar`

## 依赖关系

```javascript
// 核心依赖
'@web/core/autocomplete/autocomplete'           // 自动完成组件
'@web/core/utils/hooks'                         // 钩子工具
'@web/core/registry'                            // 注册表
'@web/core/l10n/translation'                   // 国际化翻译
'@web/views/fields/char/char_field'             // 字符字段
'@web/views/fields/input_field_hook'            // 输入字段钩子
'@partner_autocomplete/js/partner_autocomplete_core'  // 合作伙伴自动完成核心
```

## 核心功能

### 1. 组件定义

```javascript
const PartnerAutoCompleteCharField = __exports.PartnerAutoCompleteCharField = class PartnerAutoCompleteCharField extends CharField {
    static template = "partner_autocomplete.PartnerAutoCompleteCharField";
    static components = {
        ...CharField.components,
        AutoComplete,
    };
```

**组件特性**:
- **继承扩展**: 继承CharField的所有功能
- **模板定制**: 使用专门的自动完成字符字段模板
- **组件集成**: 集成AutoComplete组件
- **功能扩展**: 在字符字段基础上添加自动完成功能

### 2. 组件初始化

```javascript
setup() {
    super.setup();

    this.partner_autocomplete = usePartnerAutocomplete();

    this.inputRef = useChildRef();
    useInputField({ getValue: () => this.props.record.data[this.props.name] || "", parse: (v) => this.parse(v), ref: this.inputRef});
}
```

**初始化功能**:
- **父类初始化**: 调用父类的setup方法
- **核心功能集成**: 集成合作伙伴自动完成核心功能
- **引用管理**: 创建输入字段的引用
- **字段钩子**: 使用输入字段钩子管理值和解析

### 3. 搜索验证

```javascript
async validateSearchTerm(request) {
    if (this.props.name == 'vat') {
        return this.partner_autocomplete.isTAXNumber(request);
    }
    else {
        return request && request.length > 2;
    }
}
```

**验证功能**:
- **字段类型检测**: 根据字段名称选择验证策略
- **VAT字段**: VAT字段使用税号验证
- **普通字段**: 普通字段要求最少3个字符
- **异步验证**: 支持异步的验证逻辑

### 4. 数据源配置

```javascript
get sources() {
    return [
        {
            options: async (request) => {
                if (await this.validateSearchTerm(request)) {
                    const suggestions = await this.partner_autocomplete.autocomplete(request);
                    suggestions.forEach((suggestion) => {
                        suggestion.classList = "partner_autocomplete_dropdown_char";
                    });
                    return suggestions;
                }
                else {
                    return [];
                }
            },
            optionTemplate: "partner_autocomplete.CharFieldDropdownOption",
            placeholder: _t('Searching Autocomplete...'),
        },
    ];
}
```

**数据源功能**:
- **异步选项**: 异步获取自动完成选项
- **验证集成**: 集成搜索词验证逻辑
- **样式定制**: 为建议项添加专门的CSS类
- **模板定制**: 使用专门的下拉选项模板
- **占位符**: 提供国际化的搜索占位符

### 5. 选项选择处理

```javascript
async onSelect(option) {
    const data = await this.partner_autocomplete.getCreateData(Object.getPrototypeOf(option));

    if (data.logo) {
        const logoField = this.props.record.resModel === 'res.partner' ? 'image_1920' : 'logo';
        data.company[logoField] = data.logo;
    }

    // Some fields are unnecessary in res.company
    if (this.props.record.resModel === 'res.company') {
        const fields = ['comment', 'child_ids', 'additional_info'];
        fields.forEach((field) => {
            delete data.company[field];
        });
    }

    // Format the many2one fields
    const many2oneFields = ['country_id', 'state_id'];
    many2oneFields.forEach((field) => {
        if (data.company[field]) {
            data.company[field] = [data.company[field].id, data.company[field].display_name];
        }
    });
    this.props.record.update(data.company);
    if (this.props.setDirty) {
        this.props.setDirty(false);
    }
}
```

**选择处理功能**:
- **数据获取**: 获取选中公司的完整数据
- **Logo处理**: 根据模型类型设置正确的Logo字段
- **字段过滤**: 为不同模型过滤不需要的字段
- **Many2one格式化**: 正确格式化Many2one字段
- **记录更新**: 更新当前记录的数据
- **状态管理**: 管理字段的脏状态

### 6. 字段注册

```javascript
const partnerAutoCompleteCharField = __exports.partnerAutoCompleteCharField = {
    ...charField,
    component: PartnerAutoCompleteCharField,
};

registry.category("fields").add("field_partner_autocomplete", partnerAutoCompleteCharField);
```

**注册功能**:
- **配置继承**: 继承字符字段的所有配置
- **组件替换**: 使用自定义的自动完成组件
- **字段注册**: 注册到字段注册表
- **标识符**: 使用"field_partner_autocomplete"标识符

## 使用场景

### 1. 合作伙伴自动完成字符字段增强

```javascript
// 合作伙伴自动完成字符字段增强功能
const PartnerAutocompleteCharFieldEnhancer = {
    enhancePartnerAutocompleteCharField: () => {
        // 增强的合作伙伴自动完成字符字段
        class EnhancedPartnerAutoCompleteCharField extends PartnerAutoCompleteCharField {
            static template = "partner_autocomplete.EnhancedPartnerAutoCompleteCharField";
            static props = {
                ...PartnerAutoCompleteCharField.props,
                enableFuzzySearch: { type: Boolean, optional: true },
                enableRecentSuggestions: { type: Boolean, optional: true },
                enableFavorites: { type: Boolean, optional: true },
                maxSuggestions: { type: Number, optional: true },
                minSearchLength: { type: Number, optional: true },
                searchDelay: { type: Number, optional: true },
                enableAnalytics: { type: Boolean, optional: true },
                customValidators: { type: Array, optional: true },
                fieldMappings: { type: Object, optional: true }
            };

            setup() {
                super.setup();

                // 增强的配置选项
                this.enhancedConfig = {
                    enableFuzzySearch: this.props.enableFuzzySearch !== false,
                    enableRecentSuggestions: this.props.enableRecentSuggestions !== false,
                    enableFavorites: this.props.enableFavorites !== false,
                    maxSuggestions: this.props.maxSuggestions || 10,
                    minSearchLength: this.props.minSearchLength || 2,
                    searchDelay: this.props.searchDelay || 300,
                    enableAnalytics: this.props.enableAnalytics !== false,
                    customValidators: this.props.customValidators || [],
                    fieldMappings: this.props.fieldMappings || {}
                };

                // 增强的状态
                this.enhancedState = useState({
                    isSearching: false,
                    recentSuggestions: [],
                    favoriteSuggestions: [],
                    searchHistory: [],
                    lastSearchTime: null,
                    searchCount: 0
                });

                // 防抖搜索
                this.debouncedSearch = this.debounce(
                    this.performSearch.bind(this),
                    this.enhancedConfig.searchDelay
                );

                // 加载用户偏好
                this.loadUserPreferences();
            }

            // 增强的搜索验证
            async validateSearchTerm(request) {
                // 基础验证
                const baseValid = await super.validateSearchTerm(request);
                if (!baseValid) return false;

                // 自定义验证器
                for (const validator of this.enhancedConfig.customValidators) {
                    try {
                        const isValid = await validator(request, this.props.name);
                        if (!isValid) return false;
                    } catch (error) {
                        console.warn('自定义验证器执行失败:', error);
                    }
                }

                return true;
            }

            // 增强的数据源
            get sources() {
                return [
                    {
                        options: async (request) => {
                            if (await this.validateSearchTerm(request)) {
                                return this.getEnhancedSuggestions(request);
                            }
                            return [];
                        },
                        optionTemplate: "partner_autocomplete.EnhancedCharFieldDropdownOption",
                        placeholder: _t('Searching companies...'),
                    },
                ];
            }

            // 获取增强的建议
            async getEnhancedSuggestions(request) {
                this.enhancedState.isSearching = true;
                this.enhancedState.lastSearchTime = Date.now();
                this.enhancedState.searchCount++;

                try {
                    let suggestions = [];

                    // 获取基础建议
                    const baseSuggestions = await this.partner_autocomplete.autocomplete(request);

                    // 添加最近使用的建议
                    if (this.enhancedConfig.enableRecentSuggestions) {
                        const recentSuggestions = this.getRecentSuggestions(request);
                        suggestions = suggestions.concat(recentSuggestions);
                    }

                    // 添加收藏建议
                    if (this.enhancedConfig.enableFavorites) {
                        const favoriteSuggestions = this.getFavoriteSuggestions(request);
                        suggestions = suggestions.concat(favoriteSuggestions);
                    }

                    // 添加基础建议
                    suggestions = suggestions.concat(baseSuggestions);

                    // 模糊搜索
                    if (this.enhancedConfig.enableFuzzySearch) {
                        suggestions = this.applyFuzzySearch(suggestions, request);
                    }

                    // 去重和排序
                    suggestions = this.deduplicateAndSort(suggestions, request);

                    // 限制数量
                    suggestions = suggestions.slice(0, this.enhancedConfig.maxSuggestions);

                    // 添加样式类
                    suggestions.forEach((suggestion) => {
                        suggestion.classList = "partner_autocomplete_dropdown_char enhanced";
                        suggestion.searchTerm = request;
                        suggestion.timestamp = Date.now();
                    });

                    // 记录搜索分析
                    if (this.enhancedConfig.enableAnalytics) {
                        this.recordSearchAnalytics(request, suggestions.length);
                    }

                    return suggestions;

                } catch (error) {
                    console.error('获取建议失败:', error);
                    return [];
                } finally {
                    this.enhancedState.isSearching = false;
                }
            }

            // 获取最近使用的建议
            getRecentSuggestions(request) {
                const recent = this.enhancedState.recentSuggestions.filter(suggestion =>
                    suggestion.name.toLowerCase().includes(request.toLowerCase()) ||
                    (suggestion.vat && suggestion.vat.toLowerCase().includes(request.toLowerCase()))
                );

                return recent.map(suggestion => ({
                    ...suggestion,
                    isRecent: true,
                    priority: 10
                }));
            }

            // 获取收藏建议
            getFavoriteSuggestions(request) {
                const favorites = this.enhancedState.favoriteSuggestions.filter(suggestion =>
                    suggestion.name.toLowerCase().includes(request.toLowerCase()) ||
                    (suggestion.vat && suggestion.vat.toLowerCase().includes(request.toLowerCase()))
                );

                return favorites.map(suggestion => ({
                    ...suggestion,
                    isFavorite: true,
                    priority: 15
                }));
            }

            // 应用模糊搜索
            applyFuzzySearch(suggestions, request) {
                // 简单的模糊搜索实现
                const fuzzyMatches = suggestions.filter(suggestion => {
                    const name = suggestion.name.toLowerCase();
                    const query = request.toLowerCase();
                    
                    // 计算编辑距离
                    const distance = this.levenshteinDistance(name, query);
                    const threshold = Math.max(2, Math.floor(query.length * 0.3));
                    
                    return distance <= threshold;
                });

                return fuzzyMatches.map(suggestion => ({
                    ...suggestion,
                    isFuzzy: true,
                    priority: suggestion.priority || 0
                }));
            }

            // 计算编辑距离
            levenshteinDistance(str1, str2) {
                const matrix = [];

                for (let i = 0; i <= str2.length; i++) {
                    matrix[i] = [i];
                }

                for (let j = 0; j <= str1.length; j++) {
                    matrix[0][j] = j;
                }

                for (let i = 1; i <= str2.length; i++) {
                    for (let j = 1; j <= str1.length; j++) {
                        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                            matrix[i][j] = matrix[i - 1][j - 1];
                        } else {
                            matrix[i][j] = Math.min(
                                matrix[i - 1][j - 1] + 1,
                                matrix[i][j - 1] + 1,
                                matrix[i - 1][j] + 1
                            );
                        }
                    }
                }

                return matrix[str2.length][str1.length];
            }

            // 去重和排序
            deduplicateAndSort(suggestions, request) {
                // 去重
                const unique = suggestions.filter((suggestion, index, self) =>
                    index === self.findIndex(s => s.partner_gid === suggestion.partner_gid)
                );

                // 排序
                unique.sort((a, b) => {
                    // 优先级排序
                    if (a.priority !== b.priority) {
                        return (b.priority || 0) - (a.priority || 0);
                    }

                    // 名称匹配度排序
                    const aMatch = this.calculateMatchScore(a, request);
                    const bMatch = this.calculateMatchScore(b, request);
                    
                    return bMatch - aMatch;
                });

                return unique;
            }

            // 计算匹配分数
            calculateMatchScore(suggestion, request) {
                let score = 0;
                const query = request.toLowerCase();

                // 精确匹配
                if (suggestion.name.toLowerCase() === query) {
                    score += 100;
                }

                // 开头匹配
                if (suggestion.name.toLowerCase().startsWith(query)) {
                    score += 50;
                }

                // 包含匹配
                if (suggestion.name.toLowerCase().includes(query)) {
                    score += 25;
                }

                // VAT匹配
                if (suggestion.vat && suggestion.vat.toLowerCase().includes(query)) {
                    score += 75;
                }

                // 网站匹配
                if (suggestion.website && suggestion.website.toLowerCase().includes(query)) {
                    score += 30;
                }

                return score;
            }

            // 增强的选择处理
            async onSelect(option) {
                try {
                    // 调用父类方法
                    await super.onSelect(option);

                    // 添加到最近使用
                    this.addToRecentSuggestions(option);

                    // 记录选择分析
                    if (this.enhancedConfig.enableAnalytics) {
                        this.recordSelectionAnalytics(option);
                    }

                    // 应用自定义字段映射
                    if (Object.keys(this.enhancedConfig.fieldMappings).length > 0) {
                        this.applyCustomFieldMappings(option);
                    }

                } catch (error) {
                    console.error('选择处理失败:', error);
                    this.notification.add('选择失败，请重试', { type: 'error' });
                }
            }

            // 添加到最近使用
            addToRecentSuggestions(option) {
                const recent = this.enhancedState.recentSuggestions;
                
                // 移除已存在的项
                const filtered = recent.filter(item => item.partner_gid !== option.partner_gid);
                
                // 添加到开头
                filtered.unshift({
                    ...option,
                    usedAt: Date.now()
                });

                // 限制数量
                this.enhancedState.recentSuggestions = filtered.slice(0, 10);

                // 保存到本地存储
                this.saveRecentSuggestions();
            }

            // 应用自定义字段映射
            applyCustomFieldMappings(option) {
                const mappings = this.enhancedConfig.fieldMappings;
                const updateData = {};

                for (const [sourceField, targetField] of Object.entries(mappings)) {
                    if (option[sourceField] !== undefined) {
                        updateData[targetField] = option[sourceField];
                    }
                }

                if (Object.keys(updateData).length > 0) {
                    this.props.record.update(updateData);
                }
            }

            // 防抖函数
            debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            // 加载用户偏好
            async loadUserPreferences() {
                try {
                    const preferences = await this.orm.call(
                        'res.users',
                        'get_partner_autocomplete_preferences',
                        [this.env.services.user.userId]
                    );

                    if (preferences.recent_suggestions) {
                        this.enhancedState.recentSuggestions = preferences.recent_suggestions;
                    }

                    if (preferences.favorite_suggestions) {
                        this.enhancedState.favoriteSuggestions = preferences.favorite_suggestions;
                    }

                } catch (error) {
                    console.warn('加载用户偏好失败:', error);
                    this.loadLocalPreferences();
                }
            }

            // 从本地存储加载偏好
            loadLocalPreferences() {
                try {
                    const recent = localStorage.getItem('partner_autocomplete_recent');
                    if (recent) {
                        this.enhancedState.recentSuggestions = JSON.parse(recent);
                    }

                    const favorites = localStorage.getItem('partner_autocomplete_favorites');
                    if (favorites) {
                        this.enhancedState.favoriteSuggestions = JSON.parse(favorites);
                    }
                } catch (error) {
                    console.warn('加载本地偏好失败:', error);
                }
            }

            // 保存最近使用建议
            saveRecentSuggestions() {
                try {
                    localStorage.setItem(
                        'partner_autocomplete_recent',
                        JSON.stringify(this.enhancedState.recentSuggestions)
                    );
                } catch (error) {
                    console.warn('保存最近使用建议失败:', error);
                }
            }

            // 记录搜索分析
            recordSearchAnalytics(query, resultCount) {
                try {
                    this.orm.silent.call(
                        'partner.autocomplete.analytics',
                        'record_search',
                        [{
                            field_name: this.props.name,
                            query: query,
                            result_count: resultCount,
                            search_time: Date.now(),
                            user_id: this.env.services.user.userId
                        }]
                    );
                } catch (error) {
                    console.warn('记录搜索分析失败:', error);
                }
            }

            // 记录选择分析
            recordSelectionAnalytics(option) {
                try {
                    this.orm.silent.call(
                        'partner.autocomplete.analytics',
                        'record_selection',
                        [{
                            field_name: this.props.name,
                            selected_partner_gid: option.partner_gid,
                            selection_time: Date.now(),
                            user_id: this.env.services.user.userId,
                            is_recent: option.isRecent || false,
                            is_favorite: option.isFavorite || false,
                            is_fuzzy: option.isFuzzy || false
                        }]
                    );
                } catch (error) {
                    console.warn('记录选择分析失败:', error);
                }
            }
        }

        // 增强的字段配置
        const enhancedPartnerAutoCompleteCharField = {
            ...charField,
            component: EnhancedPartnerAutoCompleteCharField,
            extractProps: ({ attrs, field }) => ({
                ...charField.extractProps({ attrs, field }),
                enableFuzzySearch: attrs.enable_fuzzy_search,
                enableRecentSuggestions: attrs.enable_recent_suggestions,
                enableFavorites: attrs.enable_favorites,
                maxSuggestions: attrs.max_suggestions,
                minSearchLength: attrs.min_search_length,
                searchDelay: attrs.search_delay,
                enableAnalytics: attrs.enable_analytics,
                customValidators: attrs.custom_validators,
                fieldMappings: attrs.field_mappings
            })
        };

        // 注册增强的字段
        registry.category("fields").add("enhanced_field_partner_autocomplete", enhancedPartnerAutoCompleteCharField, { force: true });

        return {
            component: EnhancedPartnerAutoCompleteCharField,
            fieldConfig: enhancedPartnerAutoCompleteCharField
        };
    }
};

// 应用合作伙伴自动完成字符字段增强
const enhancedField = PartnerAutocompleteCharFieldEnhancer.enhancePartnerAutocompleteCharField();

// 导出增强的组件
__exports.enhancedField = enhancedField;
```

## 技术特点

### 1. 组件继承
- 继承CharField的所有功能
- 保持与标准字段的兼容性
- 扩展自动完成特有功能

### 2. 智能验证
- 根据字段类型选择验证策略
- 支持异步验证逻辑
- 灵活的验证配置

### 3. 数据处理
- 智能的数据格式化和转换
- 根据模型类型调整数据结构
- 完善的错误处理机制

### 4. 用户体验
- 流畅的自动完成交互
- 智能的搜索建议
- 即时的数据填充

## 设计模式

### 1. 装饰器模式 (Decorator Pattern)
- 在字符字段基础上添加自动完成功能
- 保持原有接口不变

### 2. 适配器模式 (Adapter Pattern)
- 适配不同模型的数据格式
- 统一的字段处理接口

### 3. 策略模式 (Strategy Pattern)
- 根据字段类型选择不同的验证策略
- 灵活的数据处理策略

## 注意事项

1. **性能优化**: 合理控制搜索频率和结果数量
2. **数据一致性**: 确保数据格式的正确转换
3. **用户体验**: 提供流畅的交互体验
4. **错误处理**: 完善的错误处理和用户反馈

## 扩展建议

1. **高级搜索**: 支持更复杂的搜索条件
2. **缓存机制**: 实现搜索结果的缓存
3. **个性化**: 基于用户行为的个性化建议
4. **批量操作**: 支持批量的数据处理
5. **移动优化**: 针对移动设备的优化

该合作伙伴自动完成字符字段组件为Odoo提供了强大的数据输入辅助功能，通过智能的自动完成机制显著提升了用户的数据录入效率和准确性。

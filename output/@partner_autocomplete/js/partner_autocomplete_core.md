# Partner Autocomplete Core - 合作伙伴自动完成核心模块

## 概述

`partner_autocomplete_core.js` 是 Odoo Partner Autocomplete 模块的核心实现，专门用于提供合作伙伴信息的自动完成和数据丰富功能。该模块集成了多个数据源（Odoo内部API和Clearbit API），支持VAT和GST税号验证，提供公司信息自动补全、数据丰富和Logo获取等功能，是企业客户关系管理的重要工具。

## 文件信息
- **路径**: `/partner_autocomplete/static/src/js/partner_autocomplete_core.js`
- **行数**: 352
- **模块**: `@partner_autocomplete/js/partner_autocomplete_core`

## 依赖关系

```javascript
// 核心依赖
'@web/core/assets'              // 资源加载
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/utils/concurrency'   // 并发控制
'@web/core/utils/hooks'         // 钩子工具
'@web/core/utils/render'        // 渲染工具
'@web/core/utils/urls'          // URL工具
```

## 核心功能

### 1. 主要Hook函数

```javascript
__exports.usePartnerAutocomplete = usePartnerAutocomplete; function usePartnerAutocomplete() {
    const keepLastOdoo = new KeepLast();
    const keepLastClearbit = new KeepLast();

    const http = useService("http");
    const notification = useService("notification");
    const orm = useService("orm");
```

**Hook功能**:
- **并发控制**: 使用KeepLast确保只处理最新的请求
- **服务集成**: 集成HTTP、通知和ORM服务
- **状态管理**: 管理多个数据源的请求状态
- **返回接口**: 提供autocomplete、getCreateData、isTAXNumber接口

### 2. 税号验证系统

```javascript
function sanitizeVAT(value) {
    return value ? value.replace(/[^A-Za-z0-9]/g, '') : '';
}

async function isVATNumber(value) {
    // Lazyload jsvat only if the component is being used.
    await loadJS("/partner_autocomplete/static/lib/jsvat.js");

    // checkVATNumber is defined in library jsvat.
    // It validates that the input has a valid VAT number format
    return checkVATNumber(sanitizeVAT(value));
}

function isGSTNumber(value) {
    // Check if the input is a valid GST number.
    let isGST = false;
    if (value && value.length === 15) {
        const allGSTinRe = [
            /\d{2}[a-zA-Z]{5}\d{4}[a-zA-Z][1-9A-Za-z][Zz1-9A-Ja-j][0-9a-zA-Z]/, // Normal, Composite, Casual GSTIN
            /\d{4}[A-Z]{3}\d{5}[UO]N[A-Z0-9]/, // UN/ON Body GSTIN
            /\d{4}[a-zA-Z]{3}\d{5}NR[0-9a-zA-Z]/, // NRI GSTIN
            /\d{2}[a-zA-Z]{4}[a-zA-Z0-9]\d{4}[a-zA-Z][1-9A-Za-z][DK][0-9a-zA-Z]/, // TDS GSTIN
            /\d{2}[a-zA-Z]{5}\d{4}[a-zA-Z][1-9A-Za-z]C[0-9a-zA-Z]/ // TCS GSTIN
        ];

        isGST = allGSTinRe.some((re) => re.test(value));
    }

    return isGST;
}

async function isTAXNumber(value) {
    const isVAT = await isVATNumber(value);
    const isGST = isGSTNumber(value);
    return isVAT || isGST;
}
```

**税号验证功能**:
- **VAT验证**: 使用jsvat库进行VAT号码格式验证
- **GST验证**: 支持印度GST号码的多种格式验证
- **懒加载**: 按需加载jsvat验证库
- **数据清理**: 自动清理VAT号码中的非字母数字字符
- **综合验证**: 统一的税号验证接口

### 3. 自动完成系统

```javascript
async function autocomplete(value) {
    value = value.trim();

    const isVAT = await isTAXNumber(value);
    let odooSuggestions = [];
    let clearbitSuggestions = [];
    return new Promise((resolve, reject) => {
        const odooPromise = getOdooSuggestions(value, isVAT).then((suggestions) => {
            odooSuggestions = suggestions;
        });

        // Only get Clearbit suggestions if not a VAT number
        const clearbitPromise = isVAT ? false : getClearbitSuggestions(value).then((suggestions) => {
            suggestions.forEach((suggestion) => {
                suggestion.label = suggestion.name;
                suggestion.website = suggestion.domain;
                suggestion.description = suggestion.website;
            });
            clearbitSuggestions = suggestions;
        });

        const concatResults = () => {
            // Add Clearbit result with Odoo result (with unique domain)
            if (clearbitSuggestions && clearbitSuggestions.length) {
                const websites = odooSuggestions.map((suggestion) => {
                    return suggestion.website;
                });
                clearbitSuggestions.forEach((suggestion) => {
                    if (websites.indexOf(suggestion.domain) < 0) {
                        websites.push(suggestion.domain);
                        odooSuggestions.push(suggestion);
                    }
                });
            }

            odooSuggestions = odooSuggestions.filter((suggestion) => {
                return !suggestion.ignored;
            });
            odooSuggestions.forEach((suggestion) => {
                delete suggestion.ignored;
            });
            return resolve(odooSuggestions);
        };

        whenAll([odooPromise, clearbitPromise]).then(concatResults, concatResults);
    });
}
```

**自动完成功能**:
- **多数据源**: 同时查询Odoo和Clearbit数据源
- **智能路由**: 税号查询只使用Odoo，公司名查询使用双数据源
- **数据合并**: 智能合并多个数据源的结果，避免重复
- **数据清理**: 过滤被忽略的建议并清理临时字段
- **异步处理**: 并行处理多个数据源请求

### 4. 数据丰富系统

```javascript
function enrichCompany(company) {
    return orm.call(
        'res.partner',
        'enrich_company',
        [company.website, company.partner_gid, company.vat]
    );
}

async function getCompanyLogo(url) {
    try {
        const base64Image = await getBase64Image(url)
        // base64Image equals "data:" if image not available on given url
        return base64Image ? base64Image.replace(/^data:image[^;]*;base64,?/, '') : false;
    }
    catch {
        return false;
    }
}

function getCreateData(company) {
    const removeUselessFields = (company) => {
        // Delete attribute to avoid "Field_changed" errors
        const fields = ['label', 'description', 'domain', 'logo', 'legal_name', 'ignored', 'email', 'bank_ids', 'classList', 'skip_enrich'];
        fields.forEach((field) => {
            delete company[field];
        });

        // Remove if empty and format it otherwise
        const many2oneFields = ['country_id', 'state_id'];
        many2oneFields.forEach((field) => {
            if (!company[field]) {
                delete company[field];
            }
        });
    };

    return new Promise((resolve) => {
        // Fetch additional company info via Autocomplete Enrichment API
        const enrichPromise = !company.skip_enrich ? enrichCompany(company) : false;

        // Get logo
        const logoPromise = company.logo ? getCompanyLogo(company.logo) : false;
        whenAll([enrichPromise, logoPromise]).then(([company_data, logo_data]) => {
            // The vat should be returned for free. This is the reason why
            // we add it into the data of 'company' even if an error such as
            // an insufficient credit error is raised.
            if (company_data.error && company_data.vat) {
                company.vat = company_data.vat;
            }

            if (company_data.error) {
                if (company_data.error_message === 'Insufficient Credit') {
                    notifyNoCredits();
                }
                else if (company_data.error_message === 'No Account Token') {
                    notifyAccountToken();
                }
                else {
                    notification.add(company_data.error_message);
                }
                if (company_data.city !== undefined) {
                    company.city = company_data.city;
                }
                if (company_data.street !== undefined) {
                    company.street = company_data.street;
                }
                if (company_data.zip !== undefined) {
                    company.zip = company_data.zip;
                }
                company_data = company;
            }

            if (!Object.keys(company_data).length) {
                company_data = company;
            }

            removeUselessFields(company_data);

            // Assign VAT coming from parent VIES VAT query
            if (company.vat) {
                company_data.vat = company.vat;
            }
            resolve({
                company: company_data,
                logo: logo_data
            });
        });
    });
}
```

**数据丰富功能**:
- **公司信息丰富**: 通过API获取详细的公司信息
- **Logo获取**: 自动获取并转换公司Logo为Base64格式
- **错误处理**: 完善的错误处理和用户通知机制
- **数据清理**: 自动清理不需要的字段和空值
- **信用管理**: 处理API信用不足和账户令牌缺失的情况

### 5. 数据源集成

```javascript
async function getClearbitSuggestions(value) {
    const url = `https://autocomplete.clearbit.com/v1/companies/suggest?query=${value}`;
    const prom = http.get(url);
    return keepLastClearbit.add(prom);
}

async function getOdooSuggestions(value, isVAT) {
    const method = isVAT ? 'read_by_vat' : 'autocomplete';

    const prom = orm.silent.call(
        'res.partner',
        method,
        [value],
    );

    const suggestions = await keepLastOdoo.add(prom);
    suggestions.map((suggestion) => {
        suggestion.logo = suggestion.logo || '';
        suggestion.label = suggestion.legal_name || suggestion.name;
        if (suggestion.vat) suggestion.description = suggestion.vat;
        else if (suggestion.website) suggestion.description = suggestion.website;

        if (suggestion.country_id && suggestion.country_id.display_name) {
            if (suggestion.description) suggestion.description += ` (${suggestion.country_id.display_name})`;
            else suggestion.description += suggestion.country_id.display_name;
        }

        return suggestion;
    });
    return suggestions;
}
```

**数据源功能**:
- **Clearbit集成**: 集成Clearbit公司数据库API
- **Odoo内部**: 使用Odoo内部的合作伙伴数据
- **智能路由**: 根据输入类型选择合适的查询方法
- **数据标准化**: 统一不同数据源的数据格式
- **并发控制**: 确保只处理最新的请求结果

## 使用场景

### 1. 合作伙伴自动完成增强

```javascript
// 合作伙伴自动完成增强功能
const PartnerAutocompleteEnhancer = {
    enhancePartnerAutocomplete: () => {
        // 增强的合作伙伴自动完成功能
        function useEnhancedPartnerAutocomplete() {
            const keepLastOdoo = new KeepLast();
            const keepLastClearbit = new KeepLast();
            const keepLastLinkedIn = new KeepLast();
            const keepLastCrunchbase = new KeepLast();

            const http = useService("http");
            const notification = useService("notification");
            const orm = useService("orm");
            const user = useService("user");

            // 增强的配置选项
            const config = {
                enableClearbit: true,
                enableLinkedIn: false,
                enableCrunchbase: false,
                enableLocalCache: true,
                cacheExpiry: 24 * 60 * 60 * 1000, // 24小时
                maxSuggestions: 10,
                minQueryLength: 2,
                debounceDelay: 300,
                enableAnalytics: true,
                enableGeoLocation: true,
                enableIndustryFilter: false,
                enableSizeFilter: false
            };

            // 增强的税号验证
            async function enhancedIsTAXNumber(value, country) {
                const validators = {
                    'VAT': isVATNumber,
                    'GST': isGSTNumber,
                    'TIN': isTINNumber,
                    'EIN': isEINNumber,
                    'ABN': isABNNumber,
                    'CIF': isCIFNumber
                };

                const results = {};
                for (const [type, validator] of Object.entries(validators)) {
                    try {
                        results[type] = await validator(value, country);
                    } catch (error) {
                        results[type] = false;
                    }
                }

                return {
                    isValid: Object.values(results).some(Boolean),
                    types: Object.keys(results).filter(key => results[key]),
                    details: results
                };
            }

            // TIN号码验证（美国税务识别号）
            function isTINNumber(value) {
                if (!value || value.length !== 9) return false;
                const tinRegex = /^\d{2}-\d{7}$/;
                return tinRegex.test(value);
            }

            // EIN号码验证（雇主识别号）
            function isEINNumber(value) {
                if (!value || value.length !== 10) return false;
                const einRegex = /^\d{2}-\d{7}$/;
                return einRegex.test(value);
            }

            // ABN号码验证（澳大利亚商业号码）
            function isABNNumber(value) {
                if (!value || value.length !== 11) return false;
                const abnRegex = /^\d{11}$/;
                if (!abnRegex.test(value)) return false;

                // ABN校验算法
                const weights = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19];
                let sum = 0;
                for (let i = 0; i < 11; i++) {
                    sum += parseInt(value[i]) * weights[i];
                }
                return sum % 89 === 0;
            }

            // CIF号码验证（西班牙公司识别码）
            function isCIFNumber(value) {
                if (!value || value.length !== 9) return false;
                const cifRegex = /^[ABCDEFGHJNPQRSUVW]\d{7}[0-9A-J]$/;
                return cifRegex.test(value);
            }

            // 增强的自动完成功能
            async function enhancedAutocomplete(value, options = {}) {
                value = value.trim();
                
                if (value.length < config.minQueryLength) {
                    return [];
                }

                // 检查本地缓存
                if (config.enableLocalCache) {
                    const cached = getFromCache(value);
                    if (cached) {
                        return cached;
                    }
                }

                const taxInfo = await enhancedIsTAXNumber(value, options.country);
                let allSuggestions = [];

                const promises = [];

                // Odoo建议
                promises.push(
                    getEnhancedOdooSuggestions(value, taxInfo, options)
                        .then(suggestions => ({ source: 'odoo', suggestions }))
                );

                // Clearbit建议
                if (config.enableClearbit && !taxInfo.isValid) {
                    promises.push(
                        getEnhancedClearbitSuggestions(value, options)
                            .then(suggestions => ({ source: 'clearbit', suggestions }))
                    );
                }

                // LinkedIn建议
                if (config.enableLinkedIn && !taxInfo.isValid) {
                    promises.push(
                        getLinkedInSuggestions(value, options)
                            .then(suggestions => ({ source: 'linkedin', suggestions }))
                    );
                }

                // Crunchbase建议
                if (config.enableCrunchbase && !taxInfo.isValid) {
                    promises.push(
                        getCrunchbaseSuggestions(value, options)
                            .then(suggestions => ({ source: 'crunchbase', suggestions }))
                    );
                }

                try {
                    const results = await Promise.allSettled(promises);
                    
                    results.forEach(result => {
                        if (result.status === 'fulfilled') {
                            const { source, suggestions } = result.value;
                            allSuggestions = mergeSuggestions(allSuggestions, suggestions, source);
                        }
                    });

                    // 应用过滤器
                    allSuggestions = applyFilters(allSuggestions, options);

                    // 排序和限制结果
                    allSuggestions = rankAndLimitSuggestions(allSuggestions, value);

                    // 缓存结果
                    if (config.enableLocalCache) {
                        saveToCache(value, allSuggestions);
                    }

                    // 记录分析数据
                    if (config.enableAnalytics) {
                        recordSearchAnalytics(value, allSuggestions.length, options);
                    }

                    return allSuggestions;

                } catch (error) {
                    console.error('自动完成搜索失败:', error);
                    notification.add('搜索失败，请重试', { type: 'error' });
                    return [];
                }
            }

            // 增强的Odoo建议
            async function getEnhancedOdooSuggestions(value, taxInfo, options) {
                const method = taxInfo.isValid ? 'read_by_vat' : 'autocomplete';
                
                const searchParams = {
                    query: value,
                    limit: config.maxSuggestions,
                    country: options.country,
                    industry: options.industry,
                    size_range: options.sizeRange,
                    include_inactive: options.includeInactive || false
                };

                const prom = orm.silent.call(
                    'res.partner',
                    method,
                    [searchParams]
                );

                const suggestions = await keepLastOdoo.add(prom);
                
                return suggestions.map(suggestion => ({
                    ...suggestion,
                    source: 'odoo',
                    confidence: calculateConfidence(suggestion, value),
                    logo: suggestion.logo || '',
                    label: suggestion.legal_name || suggestion.name,
                    description: buildDescription(suggestion),
                    enriched: true
                }));
            }

            // LinkedIn建议
            async function getLinkedInSuggestions(value, options) {
                // 这里应该集成LinkedIn API
                // 由于API限制，这里返回模拟数据
                return [];
            }

            // Crunchbase建议
            async function getCrunchbaseSuggestions(value, options) {
                // 这里应该集成Crunchbase API
                // 由于API限制，这里返回模拟数据
                return [];
            }

            // 合并建议
            function mergeSuggestions(existing, newSuggestions, source) {
                const existingDomains = existing.map(s => s.website || s.domain).filter(Boolean);
                
                newSuggestions.forEach(suggestion => {
                    const domain = suggestion.website || suggestion.domain;
                    if (!domain || !existingDomains.includes(domain)) {
                        existing.push({
                            ...suggestion,
                            source: source
                        });
                        if (domain) existingDomains.push(domain);
                    }
                });

                return existing;
            }

            // 应用过滤器
            function applyFilters(suggestions, options) {
                let filtered = suggestions;

                // 行业过滤
                if (config.enableIndustryFilter && options.industry) {
                    filtered = filtered.filter(s => 
                        s.industry && s.industry.toLowerCase().includes(options.industry.toLowerCase())
                    );
                }

                // 规模过滤
                if (config.enableSizeFilter && options.sizeRange) {
                    filtered = filtered.filter(s => 
                        s.employee_count && isInSizeRange(s.employee_count, options.sizeRange)
                    );
                }

                // 地理位置过滤
                if (config.enableGeoLocation && options.location) {
                    filtered = filtered.filter(s => 
                        s.country_id && s.country_id.code === options.location
                    );
                }

                return filtered;
            }

            // 排序和限制建议
            function rankAndLimitSuggestions(suggestions, query) {
                // 按相关性排序
                suggestions.sort((a, b) => {
                    const scoreA = calculateRelevanceScore(a, query);
                    const scoreB = calculateRelevanceScore(b, query);
                    return scoreB - scoreA;
                });

                return suggestions.slice(0, config.maxSuggestions);
            }

            // 计算相关性分数
            function calculateRelevanceScore(suggestion, query) {
                let score = 0;
                const queryLower = query.toLowerCase();

                // 名称匹配
                if (suggestion.name && suggestion.name.toLowerCase().includes(queryLower)) {
                    score += 10;
                }

                // 法定名称匹配
                if (suggestion.legal_name && suggestion.legal_name.toLowerCase().includes(queryLower)) {
                    score += 8;
                }

                // 网站匹配
                if (suggestion.website && suggestion.website.toLowerCase().includes(queryLower)) {
                    score += 6;
                }

                // VAT匹配
                if (suggestion.vat && suggestion.vat.toLowerCase().includes(queryLower)) {
                    score += 15;
                }

                // 数据源权重
                switch (suggestion.source) {
                    case 'odoo': score += 5; break;
                    case 'clearbit': score += 3; break;
                    case 'linkedin': score += 2; break;
                    case 'crunchbase': score += 1; break;
                }

                return score;
            }

            // 缓存管理
            function getFromCache(key) {
                try {
                    const cached = localStorage.getItem(`partner_autocomplete_${key}`);
                    if (cached) {
                        const data = JSON.parse(cached);
                        if (Date.now() - data.timestamp < config.cacheExpiry) {
                            return data.suggestions;
                        }
                    }
                } catch (error) {
                    console.warn('缓存读取失败:', error);
                }
                return null;
            }

            function saveToCache(key, suggestions) {
                try {
                    const data = {
                        suggestions: suggestions,
                        timestamp: Date.now()
                    };
                    localStorage.setItem(`partner_autocomplete_${key}`, JSON.stringify(data));
                } catch (error) {
                    console.warn('缓存保存失败:', error);
                }
            }

            // 分析记录
            function recordSearchAnalytics(query, resultCount, options) {
                try {
                    orm.silent.call(
                        'partner.autocomplete.analytics',
                        'record_search',
                        [{
                            query: query,
                            result_count: resultCount,
                            user_id: user.userId,
                            search_time: Date.now(),
                            options: options
                        }]
                    );
                } catch (error) {
                    console.warn('分析记录失败:', error);
                }
            }

            return {
                autocomplete: enhancedAutocomplete,
                getCreateData: getCreateData,
                isTAXNumber: enhancedIsTAXNumber,
                config: config
            };
        }

        return useEnhancedPartnerAutocomplete;
    }
};

// 应用合作伙伴自动完成增强
const enhancedPartnerAutocomplete = PartnerAutocompleteEnhancer.enhancePartnerAutocomplete();

// 导出增强的功能
__exports.enhancedPartnerAutocomplete = enhancedPartnerAutocomplete;
```

## 技术特点

### 1. 多数据源集成
- 集成Odoo内部数据和Clearbit外部API
- 智能的数据源选择和路由
- 统一的数据格式和接口

### 2. 税号验证系统
- 支持VAT和GST多种税号格式
- 懒加载验证库优化性能
- 完整的验证错误处理

### 3. 异步并发处理
- 使用KeepLast避免竞态条件
- 并行处理多个数据源请求
- 优雅的错误处理和降级

### 4. 数据丰富功能
- 自动获取公司详细信息
- Logo图片处理和转换
- 完善的错误处理和通知

## 设计模式

### 1. Hook模式 (Hook Pattern)
- 使用React Hook风格的API设计
- 封装复杂的状态管理逻辑

### 2. 策略模式 (Strategy Pattern)
- 根据输入类型选择不同的查询策略
- 灵活的数据源切换机制

### 3. 适配器模式 (Adapter Pattern)
- 统一不同数据源的接口
- 标准化数据格式

## 注意事项

1. **API限制**: 注意Clearbit API的使用限制和费用
2. **数据隐私**: 确保合规的数据处理和存储
3. **性能优化**: 合理使用缓存和并发控制
4. **错误处理**: 提供完善的错误处理和用户反馈

## 扩展建议

1. **更多数据源**: 集成更多的公司数据API
2. **智能缓存**: 实现更智能的缓存策略
3. **机器学习**: 使用ML提升匹配准确性
4. **实时验证**: 实现实时的数据验证和更新
5. **批量处理**: 支持批量的公司信息处理

该合作伙伴自动完成核心模块为Odoo提供了强大的客户信息管理功能，通过多数据源集成和智能匹配算法，显著提升了用户的数据录入效率和准确性。

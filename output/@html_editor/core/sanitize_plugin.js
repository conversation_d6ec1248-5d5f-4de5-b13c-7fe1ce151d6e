/**************************************************************
*  Filepath: /html_editor/static/src/core/sanitize_plugin.js  *
*  Lines: 32                                                  *
**************************************************************/
odoo.define('@html_editor/core/sanitize_plugin', ['@html_editor/plugin'], function (require) {
'use strict';
let __exports = {};
const { Plugin } = require("@html_editor/plugin");

const SanitizePlugin = __exports.SanitizePlugin = class SanitizePlugin extends Plugin {
    static name = "sanitize";
    static shared = ["sanitize"];
    setup() {
        if (!window.DOMPurify) {
            throw new Error("DOMPurify is not available");
        }
        this.DOMPurify = DOMPurify(this.document.defaultView);
    }
    /**
     * Sanitizes in place an html element. Current implementation uses the
     * DOMPurify library.
     *
     * @param {HTMLElement} elem
     * @returns {HTMLElement} the element itself
     */
    sanitize(elem) {
        return this.DOMPurify.sanitize(elem, {
            IN_PLACE: true,
            ADD_TAGS: ["#document-fragment", "fake-el"],
            ADD_ATTR: ["contenteditable"],
        });
    }
}

return __exports;
});
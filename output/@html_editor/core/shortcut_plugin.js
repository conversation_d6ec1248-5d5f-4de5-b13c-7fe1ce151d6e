/**************************************************************
*  Filepath: /html_editor/static/src/core/shortcut_plugin.js  *
*  Lines: 34                                                  *
**************************************************************/
odoo.define('@html_editor/core/shortcut_plugin', ['@html_editor/plugin'], function (require) {
'use strict';
let __exports = {};
const { Plugin } = require("@html_editor/plugin");

const ShortCutPlugin = __exports.ShortCutPlugin = class ShortCutPlugin extends Plugin {
    static name = "shortcut";

    setup() {
        const hotkeyService = this.services.hotkey;
        if (!hotkeyService) {
            throw new Error("ShorcutPlugin needs hotkey service to properly work");
        }
        if (document !== this.document) {
            hotkeyService.registerIframe({ contentWindow: this.document.defaultView });
        }
        for (const shortcut of this.getResource("shortcuts")) {
            this.addShortcut(shortcut.hotkey, () => {
                this.dispatch(shortcut.command);
            });
        }
    }

    addShortcut(hotkey, action) {
        this.services.hotkey.add(hotkey, action, {
            area: () => this.editable,
            bypassEditableProtection: true,
            allowRepeat: true,
        });
    }
}

return __exports;
});
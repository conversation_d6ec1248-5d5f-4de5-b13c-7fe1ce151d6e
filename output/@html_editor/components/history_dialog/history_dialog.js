/**********************************************************************************
*  Filepath: /html_editor/static/src/components/history_dialog/history_dialog.js  *
*  Lines: 118                                                                     *
**********************************************************************************/
odoo.define('@html_editor/components/history_dialog/history_dialog', ['@web/core/dialog/dialog', '@web/core/notebook/notebook', '@web/core/l10n/dates', '@web/core/utils/hooks', '@web/core/utils/functions', '@odoo/owl', '@web/core/l10n/translation', '@web/core/user', '@html_editor/fields/html_viewer', '@html_editor/others/embedded_components/embedding_sets'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/
const { Dialog } = require("@web/core/dialog/dialog");
const { Notebook } = require("@web/core/notebook/notebook");
const { formatDateTime } = require("@web/core/l10n/dates");
const { useService } = require("@web/core/utils/hooks");
const { memoize } = require("@web/core/utils/functions");
const { Component, onMounted, useState, markup } = require("@odoo/owl");
const { _t } = require("@web/core/l10n/translation");
const { user } = require("@web/core/user");
const { HtmlViewer } = require("@html_editor/fields/html_viewer");
const { READONLY_MAIN_EMBEDDINGS } = require("@html_editor/others/embedded_components/embedding_sets");

const { DateTime } = luxon;

const HistoryDialog = __exports.HistoryDialog = class HistoryDialog extends Component {
    static template = "html_editor.HistoryDialog";
    static components = { Dialog, HtmlViewer, Notebook };
    static props = {
        recordId: Number,
        recordModel: String,
        close: Function,
        restoreRequested: Function,
        historyMetadata: Array,
        versionedFieldName: String,
        title: { String, optional: true },
        noContentHelper: { String, optional: true }, //Markup
        embeddedComponents: { Array, optional: true },
    };

    static defaultProps = {
        title: _t("History"),
        noContentHelper: markup(""),
        embeddedComponents: [...READONLY_MAIN_EMBEDDINGS],
    };

    state = useState({
        revisionsData: [],
        revisionContent: null,
        revisionComparison: null,
        revisionId: null,
    });

    setup() {
        this.size = "xl";
        this.title = this.props.title;
        this.orm = useService("orm");
        this.notebookTabs = [_t("Content"), _t("Comparison")];

        onMounted(() => this.init());
    }

    getConfig(value) {
        return {
            value: this.state[value],
            embeddedComponents: this.props.embeddedComponents,
        };
    }

    async init() {
        this.state.revisionsData = this.props.historyMetadata;
        await this.updateCurrentRevision(this.props.historyMetadata[0]["revision_id"]);
    }

    async updateCurrentRevision(revisionId) {
        if (this.state.revisionId === revisionId) {
            return;
        }
        this.env.services.ui.block();
        this.state.revisionId = revisionId;
        this.state.revisionContent = await this.getRevisionContent(revisionId);
        this.state.revisionComparison = await this.getRevisionComparison(revisionId);
        this.env.services.ui.unblock();
    }

    getRevisionComparison = memoize(
        async function getRevisionComparison(revisionId) {
            const comparison = await this.orm.call(
                this.props.recordModel,
                "html_field_history_get_comparison_at_revision",
                [this.props.recordId, this.props.versionedFieldName, revisionId]
            );
            return markup(comparison);
        }.bind(this)
    );

    getRevisionContent = memoize(
        async function getRevisionContent(revisionId) {
            const content = await this.orm.call(
                this.props.recordModel,
                "html_field_history_get_content_at_revision",
                [this.props.recordId, this.props.versionedFieldName, revisionId]
            );
            return markup(content);
        }.bind(this)
    );

    async _onRestoreRevisionClick() {
        this.env.services.ui.block();
        const restoredContent = await this.getRevisionContent(this.state.revisionId);
        this.props.restoreRequested(restoredContent, this.props.close);
        this.env.services.ui.unblock();
    }

    /**
     * Getters
     **/
    getRevisionDate(revision) {
        return formatDateTime(
            DateTime.fromISO(revision["create_date"], { zone: "utc" }).setZone(user.tz)
        );
    }
}

return __exports;
});
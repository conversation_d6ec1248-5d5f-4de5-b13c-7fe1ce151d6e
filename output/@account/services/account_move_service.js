/*******************************************************************
*  Filepath: /account/static/src/services/account_move_service.js  *
*  Lines: 54                                                       *
*******************************************************************/
odoo.define('@account/services/account_move_service', ['@web/core/l10n/translation', '@web/core/confirmation_dialog/confirmation_dialog', '@web/core/utils/strings', '@odoo/owl', '@web/core/registry'], function (require) {
'use strict';
let __exports = {};
const { _t } = require("@web/core/l10n/translation");
const { ConfirmationDialog } = require("@web/core/confirmation_dialog/confirmation_dialog");
const { escape } = require("@web/core/utils/strings");
const { markup } = require("@odoo/owl");
const { registry } = require("@web/core/registry");

const AccountMoveService = __exports.AccountMoveService = class AccountMoveService {
    constructor(env, services) {
        this.setup(env, services);
    }

    setup(env, services) {
        this.env = env;
        this.action = services.action;
        this.dialog = services.dialog;
        this.orm = services.orm;
    }

    async addDeletionDialog(component, moveIds) {
        const isMoveEndOfChain = await this.orm.call("account.move", "check_move_sequence_chain", [moveIds]);
        if (!isMoveEndOfChain) {
            const message = _t("This operation will create a gap in the sequence.");
            const confirmationDialogProps = component.deleteConfirmationDialogProps;
            confirmationDialogProps.body = markup(`<div class="text-danger">${escape(message)}</div>${escape(confirmationDialogProps.body)}`);
            this.dialog.add(ConfirmationDialog, confirmationDialogProps);
            return true;
        }
        return false;
    }

    async downloadPdf(accountMoveId) {
        const downloadAction = await this.orm.call(
            "account.move",
            "action_invoice_download_pdf",
            [accountMoveId]
        );
        await this.action.doAction(downloadAction);
    }
}

const accountMoveService = __exports.accountMoveService = {
    dependencies: ["action", "dialog", "orm"],
    start(env, services) {
        return new AccountMoveService(env, services);
    },
};

registry.category("services").add("account_move", accountMoveService);

return __exports;
});
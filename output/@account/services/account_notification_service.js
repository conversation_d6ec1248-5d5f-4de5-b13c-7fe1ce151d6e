/***************************************************************************
*  Filepath: /account/static/src/services/account_notification_service.js  *
*  Lines: 37                                                               *
***************************************************************************/
odoo.define('@account/services/account_notification_service', ['@web/core/l10n/translation', '@web/core/registry'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");


const accountNotificationService = __exports.accountNotificationService = {
    dependencies: ["bus_service", "notification", "action"],

    start(env, { bus_service, notification, action }) {
        bus_service.subscribe("account_notification", ({ message, sticky, title, type, action_button}) => {
            const buttons = [{
                name: action_button.name,
                primary: false,
                onClick: () => {
                    action.doAction({
                        name: _t(action_button.action_name),
                        type: 'ir.actions.act_window',
                        res_model: action_button.model,
                        domain: [["id", "in", action_button.res_ids]],
                        views: [[false, 'list'], [false, 'form']],
                        target: 'current',
                    });
                },
            }];
            notification.add(message, { sticky, title, type, buttons });
        });
    }
};

registry.category("services").add("accountNotification", accountNotificationService);

return __exports;
});
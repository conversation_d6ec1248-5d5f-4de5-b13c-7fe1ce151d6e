/******************************************************************************************************
*  Filepath: /account/static/src/components/journal_dashboard_activity/journal_dashboard_activity.js  *
*  Lines: 61                                                                                          *
******************************************************************************************************/
odoo.define('@account/components/journal_dashboard_activity/journal_dashboard_activity', ['@web/core/l10n/translation', '@web/core/registry', '@web/core/utils/hooks', '@web/views/fields/standard_field_props', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { useService } = require("@web/core/utils/hooks");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const { Component } = require("@odoo/owl");

const JournalDashboardActivity = __exports.JournalDashboardActivity = class JournalDashboardActivity extends Component {
    static template = "account.JournalDashboardActivity";
    static props = { ...standardFieldProps };

    setup() {
        this.action = useService("action");
        this.MAX_ACTIVITY_DISPLAY = 5;
        this.formatData(this.props);
    }

    formatData(props) {
        this.info = JSON.parse(this.props.record.data[this.props.name]);
        this.info.more_activities = false;
        if (this.info.activities.length > this.MAX_ACTIVITY_DISPLAY) {
            this.info.more_activities = true;
            this.info.activities = this.info.activities.slice(0, this.MAX_ACTIVITY_DISPLAY);
        }
    }

    async openActivity(activity) {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: _t('Journal Entry'),
            target: 'current',
            res_id: activity.res_id,
            res_model: 'account.move',
            views: [[false, 'form']],
        });
    }

    openAllActivities(e) {
        this.action.doAction({
            type: 'ir.actions.act_window',
            name: _t('Journal Entries'),
            res_model: 'account.move',
            views: [[false, 'kanban'], [false, 'form']],
            search_view_id: [false],
            domain: [['journal_id', '=', this.props.record.resId], ['activity_ids', '!=', false]],
        });
    }
}

const journalDashboardActivity = __exports.journalDashboardActivity = {
    component: JournalDashboardActivity,
};

registry.category("fields").add("kanban_vat_activity", journalDashboardActivity);

return __exports;
});
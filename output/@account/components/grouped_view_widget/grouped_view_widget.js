/****************************************************************************************
*  Filepath: /account/static/src/components/grouped_view_widget/grouped_view_widget.js  *
*  Lines: 38                                                                            *
****************************************************************************************/
odoo.define('@account/components/grouped_view_widget/grouped_view_widget', ['@web/core/registry', '@odoo/owl', '@web/views/fields/standard_field_props'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { registry } = require("@web/core/registry");
const { Component } = require("@odoo/owl");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");

class ListItem extends Component {
    static template = "account.GroupedItemTemplate";
    static props = ["item_vals", "options"];
}

class ListGroup extends Component {
    static template = "account.GroupedItemsTemplate";
    static components = { ListItem };
    static props = ["group_vals", "options"];
}

class ShowGroupedList extends Component {
    static template = "account.GroupedListTemplate";
    static components = { ListGroup };
    static props = {...standardFieldProps};
    getValue() {
        const value = this.props.record.data[this.props.name];
        return value
            ? JSON.parse(value)
            : { groups_vals: [], options: { discarded_number: "", columns: [] } };
    }
}

registry.category("fields").add("grouped_view_widget", {
    component: ShowGroupedList,
});

return __exports;
});
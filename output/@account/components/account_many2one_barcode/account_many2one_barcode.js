/**************************************************************************************************
*  Filepath: /account/static/src/components/account_many2one_barcode/account_many2one_barcode.js  *
*  Lines: 23                                                                                      *
**************************************************************************************************/
odoo.define('@account/components/account_many2one_barcode/account_many2one_barcode', ['@web/core/registry', '@web/views/fields/many2one_barcode/many2one_barcode_field'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { Many2OneBarcodeField, many2OneBarcodeField } = require("@web/views/fields/many2one_barcode/many2one_barcode_field");

const AccountMany2oneBarcode = __exports.AccountMany2oneBarcode = class AccountMany2oneBarcode extends Many2OneBarcodeField {
    get hasExternalButton() {
        // Inspired by sol_product_many2one to display external button despite no_open
        const res = super.hasExternalButton;
        return res || (!!this.props.record.data[this.props.name] && !this.state.isFloating);
    }
}

registry.category("fields").add("account_many2one_barcode", {
    ...many2OneBarcodeField,
    component: AccountMany2oneBarcode,
});

return __exports;
});
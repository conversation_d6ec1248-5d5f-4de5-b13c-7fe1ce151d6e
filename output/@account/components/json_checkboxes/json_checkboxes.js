/********************************************************************************
*  Filepath: /account/static/src/components/json_checkboxes/json_checkboxes.js  *
*  Lines: 43                                                                    *
********************************************************************************/
odoo.define('@account/components/json_checkboxes/json_checkboxes', ['@odoo/owl', '@web/core/checkbox/checkbox', '@web/core/registry', '@web/views/fields/standard_field_props', '@web/core/utils/timing'], function (require) {
'use strict';
let __exports = {};
const { Component, useState } = require("@odoo/owl");
const { CheckBox } = require("@web/core/checkbox/checkbox");
const { registry } = require("@web/core/registry");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const {debounce} = require("@web/core/utils/timing");


const JsonCheckboxes = __exports.JsonCheckboxes = class JsonCheckboxes extends Component {
    static template = "account.JsonCheckboxes";
    static components = { CheckBox };
    static props = {
        ...standardFieldProps,
    };

    setup() {
        super.setup();
        this.checkboxes = useState(this.props.record.data[this.props.name]);
        this.debouncedCommitChanges = debounce(this.commitChanges.bind(this), 100);
    }

    commitChanges() {
        this.props.record.update({ [this.props.name]: this.checkboxes });
    }

    onChange(key, checked) {
        this.checkboxes[key]['checked'] = checked;
        this.debouncedCommitChanges();
    }

}

const jsonCheckboxes = __exports.jsonCheckboxes = {
    component: JsonCheckboxes,
    supportedTypes: ["jsonb"],
}

registry.category("fields").add("account_json_checkboxes", jsonCheckboxes);

return __exports;
});
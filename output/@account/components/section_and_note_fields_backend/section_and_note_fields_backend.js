/****************************************************************************************************************
*  Filepath: /account/static/src/components/section_and_note_fields_backend/section_and_note_fields_backend.js  *
*  Lines: 122                                                                                                   *
****************************************************************************************************************/
odoo.define('@account/components/section_and_note_fields_backend/section_and_note_fields_backend', ['@web/core/registry', '@web/views/list/list_renderer', '@web/views/fields/x2many/x2many_field', '@web/views/fields/text/text_field', '@web/views/fields/char/char_field', '@web/views/fields/standard_field_props', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { ListRenderer } = require("@web/views/list/list_renderer");
const { X2ManyField, x2ManyField } = require("@web/views/fields/x2many/x2many_field");
const { TextField, ListTextField } = require("@web/views/fields/text/text_field");
const { CharField } = require("@web/views/fields/char/char_field");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const { Component, useEffect } = require("@odoo/owl");

const SectionAndNoteListRenderer = __exports.SectionAndNoteListRenderer = class SectionAndNoteListRenderer extends ListRenderer {
    static template = "account.sectionAndNoteListRenderer";

    /**
     * The purpose of this extension is to allow sections and notes in the one2many list
     * primarily used on Sales Orders and Invoices
     *
     * @override
     */
    setup() {
        super.setup();
        this.titleField = "name";
        useEffect(
            () => this.focusToName(this.props.list.editedRecord),
            () => [this.props.list.editedRecord]
        )
    }

    focusToName(editRec) {
        if (editRec && editRec.isNew && this.isSectionOrNote(editRec)) {
            const col = this.columns.find((c) => c.name === this.titleField);
            this.focusCell(col, null);
        }
    }

    isSectionOrNote(record=null) {
        record = record || this.record;
        return ['line_section', 'line_note'].includes(record.data.display_type);
    }

    getRowClass(record) {
        const existingClasses = super.getRowClass(record);
        return `${existingClasses} o_is_${record.data.display_type}`;
    }

    getCellClass(column, record) {
        const classNames = super.getCellClass(column, record);
        if (this.isSectionOrNote(record) && column.widget !== "handle" && column.name !== this.titleField) {
            return `${classNames} o_hidden`;
        }
        return classNames;
    }

    getColumns(record) {
        const columns = super.getColumns(record);
        if (this.isSectionOrNote(record)) {
            return this.getSectionColumns(columns);
        }
        return columns;
    }

    getSectionColumns(columns) {
        const sectionCols = columns.filter((col) => col.widget === "handle" || col.type === "field" && col.name === this.titleField);
        return sectionCols.map((col) => {
            if (col.name === this.titleField) {
                return { ...col, colspan: columns.length - sectionCols.length + 1 };
            } else {
                return { ...col };
            }
        });
    }
}

const SectionAndNoteFieldOne2Many = __exports.SectionAndNoteFieldOne2Many = class SectionAndNoteFieldOne2Many extends X2ManyField {
    static components = {
        ...X2ManyField.components,
        ListRenderer: SectionAndNoteListRenderer,
    };
}

const SectionAndNoteText = __exports.SectionAndNoteText = class SectionAndNoteText extends Component {
    static template = "account.SectionAndNoteText";
    static props = { ...standardFieldProps };

    get componentToUse() {
        return this.props.record.data.display_type === 'line_section' ? CharField : TextField;
    }
}

const ListSectionAndNoteText = __exports.ListSectionAndNoteText = class ListSectionAndNoteText extends SectionAndNoteText {
    get componentToUse() {
        return this.props.record.data.display_type !== "line_section"
            ? ListTextField
            : super.componentToUse;
    }
}

const sectionAndNoteFieldOne2Many = __exports.sectionAndNoteFieldOne2Many = {
    ...x2ManyField,
    component: SectionAndNoteFieldOne2Many,
    additionalClasses: [...(x2ManyField.additionalClasses || []), "o_field_one2many"],
};

const sectionAndNoteText = __exports.sectionAndNoteText = {
    component: SectionAndNoteText,
    additionalClasses: ["o_field_text"],
};

const listSectionAndNoteText = __exports.listSectionAndNoteText = {
    ...sectionAndNoteText,
    component: ListSectionAndNoteText,
};

registry.category("fields").add("section_and_note_one2many", sectionAndNoteFieldOne2Many);
registry.category("fields").add("section_and_note_text", sectionAndNoteText);
registry.category("fields").add("list.section_and_note_text", listSectionAndNoteText);

return __exports;
});
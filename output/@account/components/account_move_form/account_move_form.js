/************************************************************************************
*  Filepath: /account/static/src/components/account_move_form/account_move_form.js  *
*  Lines: 103                                                                       *
************************************************************************************/
odoo.define('@account/components/account_move_form/account_move_form', ['@web/core/registry', '@web/core/utils/xml', '@web/core/notebook/notebook', '@web/views/form/form_view', '@web/views/form/form_compiler', '@web/views/form/form_renderer', '@web/views/form/form_controller', '@web/core/utils/hooks', '@web/core/l10n/translation'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { createElement, append } = require("@web/core/utils/xml");
const { Notebook } = require("@web/core/notebook/notebook");
const { formView } = require("@web/views/form/form_view");
const { FormCompiler } = require("@web/views/form/form_compiler");
const { FormRenderer } = require("@web/views/form/form_renderer");
const { FormController } = require('@web/views/form/form_controller');
const { useService } = require("@web/core/utils/hooks");
const {_t} = require("@web/core/l10n/translation");


const AccountMoveFormController = __exports.AccountMoveFormController = class AccountMoveFormController extends FormController {
    setup() {
        super.setup();
        this.account_move_service = useService("account_move");
    }

    get cogMenuProps() {
        return {
            ...super.cogMenuProps,
            printDropdownTitle: _t("Download"),
            loadExtraPrintItems: this.loadExtraPrintItems.bind(this),
        };
    }

    async loadExtraPrintItems() {
        if (!this.model.root.isNew) {
            return []
        }
        return this.orm.call("account.move", "get_extra_print_items", [this.model.root.resId]);
    }


    async deleteRecord() {
        if ( !(await this.account_move_service.addDeletionDialog(this, this.model.root.resId))) {
            return super.deleteRecord(...arguments);
        }
    }
}

const AccountMoveFormNotebook = __exports.AccountMoveFormNotebook = class AccountMoveFormNotebook extends Notebook {
    static template = "account.AccountMoveFormNotebook";
    static props = {
        ...Notebook.props,
        onBeforeTabSwitch: { type: Function, optional: true },
    };

    async changeTabTo(page_id) {
        if (this.props.onBeforeTabSwitch) {
            await this.props.onBeforeTabSwitch(page_id);
        }
        this.state.currentPage = page_id;
    }
}

const AccountMoveFormRenderer = __exports.AccountMoveFormRenderer = class AccountMoveFormRenderer extends FormRenderer {
    static components = {
        ...FormRenderer.components,
        AccountMoveFormNotebook: AccountMoveFormNotebook,
    };

    async saveBeforeTabChange() {
        if (this.props.record.isInEdition && (await this.props.record.isDirty())) {
            const contentEl = document.querySelector('.o_content');
            const scrollPos = contentEl.scrollTop;
            await this.props.record.save();
            if (scrollPos) {
                contentEl.scrollTop = scrollPos;
            }
        }
    }
}

const AccountMoveFormCompiler = __exports.AccountMoveFormCompiler = class AccountMoveFormCompiler extends FormCompiler {
    compileNotebook(el, params) {
        const originalNoteBook = super.compileNotebook(...arguments);
        const noteBook = createElement("AccountMoveFormNotebook");
        for (const attr of originalNoteBook.attributes) {
            noteBook.setAttribute(attr.name, attr.value);
        }
        noteBook.setAttribute("onBeforeTabSwitch", "() => __comp__.saveBeforeTabChange()");
        const slots = originalNoteBook.childNodes;
        append(noteBook, [...slots]);
        return noteBook;
    }
}

const AccountMoveFormView = __exports.AccountMoveFormView = {
    ...formView,
    Renderer: AccountMoveFormRenderer,
    Compiler: AccountMoveFormCompiler,
    Controller: AccountMoveFormController,
};

registry.category("views").add("account_move_form", AccountMoveFormView);

return __exports;
});
/******************************************************************************************************
*  Filepath: /account/static/src/components/open_move_line_move_widget/open_move_line_move_widget.js  *
*  Lines: 28                                                                                          *
******************************************************************************************************/
odoo.define('@account/components/open_move_line_move_widget/open_move_line_move_widget', ['@web/core/registry', '@web/views/fields/many2one/many2one_field'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { Many2OneField, many2OneField } = require("@web/views/fields/many2one/many2one_field");

class LineOpenMoveWidget extends Many2OneField {
    async openAction() {
        this.action.doActionButton({
            type: "object",
            resId: this.props.record.data[this.props.name][0],
            name: "action_open_business_doc",
            resModel: "account.move.line",
        });
    }
}

const lineOpenMoveWidget = __exports.lineOpenMoveWidget = {
    ...many2OneField,
    component: LineOpenMoveWidget,
};

registry.category("fields").add("line_open_move_widget", lineOpenMoveWidget);

return __exports;
});
/********************************************************************************************************
*  Filepath: /account/static/src/components/char_with_placeholder_field/char_with_placeholder_field.js  *
*  Lines: 30                                                                                            *
********************************************************************************************************/
odoo.define('@account/components/char_with_placeholder_field/char_with_placeholder_field', ['@web/core/registry', '@web/views/fields/char/char_field', '@mail/js/onchange_on_keydown'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { CharField, charField } = require("@web/views/fields/char/char_field");

// Ensure that in Hoot tests, this module is loaded after `@mail/js/onchange_on_keydown`
// (needed because that module patches `charField`).
require("@mail/js/onchange_on_keydown");

const CharWithPlaceholderField = __exports.CharWithPlaceholderField = class CharWithPlaceholderField extends CharField {
    static template = "account.CharWithPlaceholderField";

    /** Override **/
    get formattedValue() {
        return super.formattedValue || this.placeholder;
    }
}

const charWithPlaceholderField = __exports.charWithPlaceholderField = {
    ...charField,
    component: CharWithPlaceholderField,
};

registry.category("fields").add("char_with_placeholder_field", charWithPlaceholderField);

return __exports;
});
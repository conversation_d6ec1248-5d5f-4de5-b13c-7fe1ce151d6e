/*********************************************************************************************************
*  Filepath: /account/static/src/components/account_statusbar_secured/account_move_statusbar_secured.js  *
*  Lines: 31                                                                                             *
*********************************************************************************************************/
odoo.define('@account/components/account_statusbar_secured/account_move_statusbar_secured', ['@web/core/l10n/translation', '@web/core/registry', '@web/views/fields/statusbar/statusbar_field'], function (require) {
'use strict';
let __exports = {};
const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { statusBarField, StatusBarField } = require("@web/views/fields/statusbar/statusbar_field");

const AccountMoveStatusBarSecuredField = __exports.AccountMoveStatusBarSecuredField = class AccountMoveStatusBarSecuredField extends StatusBarField {
    static template = "account.MoveStatusBarSecuredField";

    get isSecured() {
        return this.props.record.data['secured'];
    }

    get currentItem() {
        return this.getAllItems().find((item) => item.isSelected);
    }
}

const accountMoveStatusBarSecuredField = __exports.accountMoveStatusBarSecuredField = {
    ...statusBarField,
    component: AccountMoveStatusBarSecuredField,
    displayName: _t("Status with secured indicator for Journal Entries"),
    supportedTypes: ["state"],
    additionalClasses: ["o_field_statusbar"],
};

registry.category("fields").add("account_move_statusbar_secured", accountMoveStatusBarSecuredField);

return __exports;
});
/**********************************************************************
*  Filepath: /account/static/src/components/bill_guide/bill_guide.js  *
*  Lines: 62                                                          *
**********************************************************************/
odoo.define('@account/components/bill_guide/bill_guide', ['@web/core/registry', '@web/core/utils/hooks', '@account/components/document_file_uploader/document_file_uploader', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { useService } = require("@web/core/utils/hooks");
const { DocumentFileUploader } = require("@account/components/document_file_uploader/document_file_uploader");

const { Component, onWillStart } = require("@odoo/owl");

const BillGuide = __exports.BillGuide = class BillGuide extends Component {
    static template = "account.BillGuide";
    static components = {
        DocumentFileUploader,
    };
    static props = ["*"];  // could contain view_widget props

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.context = null;
        this.alias = null;
        onWillStart(this.onWillStart);
    }

    async onWillStart() {
        const rec = this.props.record;
        const ctx = this.env.searchModel.context;
        if (rec) {
            // prepare context from journal record
            this.context = {
                default_journal_id: rec.resId,
                default_move_type: (rec.data.type === 'sale' && 'out_invoice') || (rec.data.type === 'purchase' && 'in_invoice') || 'entry',
                active_model: rec.resModel,
                active_ids: [rec.resId],
            }
            this.alias = rec.data.alias_domain_id && rec.data.alias_id[1] || false;
        } else if (!ctx?.default_journal_id && ctx?.active_id) {
            this.context = {
                default_journal_id: ctx.active_id,
            }
        }
    }

    handleButtonClick(action, model="account.journal") {
        this.action.doActionButton({
            resModel: model,
            name: action,
            context: this.context || this.env.searchModel.context,
            type: 'object',
        });
    }
}


const billGuide = __exports.billGuide = {
    component: BillGuide,
};

registry.category("view_widgets").add("bill_upload_guide", billGuide);

return __exports;
});
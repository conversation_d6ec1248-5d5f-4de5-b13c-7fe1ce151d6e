/********************************************************************************************
*  Filepath: /account/static/src/components/account_payment_field/account_payment_field.js  *
*  Lines: 92                                                                                *
********************************************************************************************/
odoo.define('@account/components/account_payment_field/account_payment_field', ['@web/core/l10n/translation', '@web/core/registry', '@web/core/popover/popover_hook', '@web/core/utils/hooks', '@web/core/l10n/localization', '@web/core/l10n/dates', '@web/views/fields/formatters', '@web/views/fields/standard_field_props', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { usePopover } = require("@web/core/popover/popover_hook");
const { useService } = require("@web/core/utils/hooks");
const { localization } = require("@web/core/l10n/localization");
const { parseDate, formatDate } = require("@web/core/l10n/dates");

const { formatMonetary } = require("@web/views/fields/formatters");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const { Component } = require("@odoo/owl");

class AccountPaymentPopOver extends Component {
    static props = { "*": { optional: true } };
    static template = "account.AccountPaymentPopOver";
}

const AccountPaymentField = __exports.AccountPaymentField = class AccountPaymentField extends Component {
    static props = { ...standardFieldProps };
    static template = "account.AccountPaymentField";

    setup() {
        const position = localization.direction === "rtl" ? "bottom" : "left";
        this.popover = usePopover(AccountPaymentPopOver, { position });
        this.orm = useService("orm");
        this.action = useService("action");
    }

    getInfo() {
        const info = this.props.record.data[this.props.name] || {
            content: [],
            outstanding: false,
            title: "",
            move_id: this.props.record.resId,
        };
        for (const [key, value] of Object.entries(info.content)) {
            value.index = key;
            value.amount_formatted = formatMonetary(value.amount, {
                currencyId: value.currency_id,
            });
            if (value.date) {
                // value.date is a string, parse to date and format to the users date format
                value.date = formatDate(parseDate(value.date));
            }
        }
        return {
            lines: info.content,
            outstanding: info.outstanding,
            title: info.title,
            moveId: info.move_id,
        };
    }

    onInfoClick(ev, line) {
        this.popover.open(ev.currentTarget, {
            title: _t("Journal Entry Info"),
            ...line,
            _onRemoveMoveReconcile: this.removeMoveReconcile.bind(this),
            _onOpenMove: this.openMove.bind(this),
        });
    }

    async assignOutstandingCredit(moveId, id) {
        await this.orm.call(this.props.record.resModel, 'js_assign_outstanding_line', [moveId, id], {});
        await this.props.record.model.root.load();
    }

    async removeMoveReconcile(moveId, partialId) {
        this.popover.close();
        await this.orm.call(this.props.record.resModel, 'js_remove_outstanding_partial', [moveId, partialId], {});
        await this.props.record.model.root.load();
    }

    async openMove(moveId) {
        const action = await this.orm.call(this.props.record.resModel, 'action_open_business_doc', [moveId], {});
        this.action.doAction(action);
    }
}

const accountPaymentField = __exports.accountPaymentField = {
    component: AccountPaymentField,
    supportedTypes: ["char"],
};

registry.category("fields").add("payment", accountPaymentField);

return __exports;
});
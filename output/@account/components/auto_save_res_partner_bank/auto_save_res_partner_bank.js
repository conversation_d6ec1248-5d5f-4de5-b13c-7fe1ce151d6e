/******************************************************************************************************
*  Filepath: /account/static/src/components/auto_save_res_partner_bank/auto_save_res_partner_bank.js  *
*  Lines: 25                                                                                          *
******************************************************************************************************/
odoo.define('@account/components/auto_save_res_partner_bank/auto_save_res_partner_bank', ['@web/core/registry', '@web/views/fields/x2many/x2many_field'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { X2ManyField, x2ManyField } = require("@web/views/fields/x2many/x2many_field");


const AutoSaveResPartnerField = __exports.AutoSaveResPartnerField = class AutoSaveResPartnerField extends X2ManyField {
     async onAdd({ context, editable } = {}) {
        await this.props.record.model.root.save();
        await super.onAdd({ context, editable });
     }
}

const autoSaveResPartnerField = __exports.autoSaveResPartnerField = {
    ...x2ManyField,
    component: AutoSaveResPartnerField,
};

registry.category("fields").add("auto_save_res_partner", autoSaveResPartnerField);

return __exports;
});
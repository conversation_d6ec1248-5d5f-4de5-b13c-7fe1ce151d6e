/**********************************************************************************************
*  Filepath: /account/static/src/components/account_type_selection/account_type_selection.js  *
*  Lines: 35                                                                                  *
**********************************************************************************************/
odoo.define('@account/components/account_type_selection/account_type_selection', ['@web/core/l10n/translation', '@web/core/registry', '@web/views/fields/selection/selection_field'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { SelectionField, selectionField } = require("@web/views/fields/selection/selection_field");

const AccountTypeSelection = __exports.AccountTypeSelection = class AccountTypeSelection extends SelectionField {
    static template = "account.AccountTypeSelection";
    get hierarchyOptions() {
        const opts = this.options;
        return [
            { name: _t('Balance Sheet') },
            { name: _t('Assets'), children: opts.filter(x => x[0] && x[0].startsWith('asset')) },
            { name: _t('Liabilities'), children: opts.filter(x => x[0] && x[0].startsWith('liability')) },
            { name: _t('Equity'), children: opts.filter(x => x[0] && x[0].startsWith('equity')) },
            { name: _t('Profit & Loss') },
            { name: _t('Income'), children: opts.filter(x => x[0] && x[0].startsWith('income')) },
            { name: _t('Expense'), children: opts.filter(x => x[0] && x[0].startsWith('expense')) },
            { name: _t('Other'), children: opts.filter(x => x[0] && x[0] === 'off_balance') },
        ];
    }
}

const accountTypeSelection = __exports.accountTypeSelection = {
    ...selectionField,
    component: AccountTypeSelection,
};

registry.category("fields").add("account_type_selection", accountTypeSelection);

return __exports;
});
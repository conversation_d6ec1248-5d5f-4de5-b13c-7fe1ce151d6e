/************************************************************************************************************
*  Filepath: /account/static/src/components/account_payment_register_html/account_payment_register_html.js  *
*  Lines: 26                                                                                                *
************************************************************************************************************/
odoo.define('@account/components/account_payment_register_html/account_payment_register_html', ['@web_editor/js/backend/html_field', '@web/core/registry'], function (require) {
'use strict';
let __exports = {};
const {HtmlField, htmlField} = require("@web_editor/js/backend/html_field");
const {registry} = require("@web/core/registry");

const AccountPaymentRegisterHtmlField = __exports.AccountPaymentRegisterHtmlField = class AccountPaymentRegisterHtmlField extends HtmlField {
    static template = "account.AccountPaymentRegisterHtmlField";

    async switchInstallmentsAmount(ev) {
        if (ev.srcElement.classList.contains("installments_switch_button")) {
            const root = this.env.model.root;
            await root.update({amount: root.data.installments_switch_amount});
        }
    }
}

const accountPaymentRegisterHtmlField = __exports.accountPaymentRegisterHtmlField = {
    ...htmlField,
    component: AccountPaymentRegisterHtmlField,
};

registry.category("fields").add("account_payment_register_html", accountPaymentRegisterHtmlField);

return __exports;
});
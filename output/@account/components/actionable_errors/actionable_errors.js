/************************************************************************************
*  Filepath: /account/static/src/components/actionable_errors/actionable_errors.js  *
*  Lines: 53                                                                        *
************************************************************************************/
odoo.define('@account/components/actionable_errors/actionable_errors', ['@web/core/registry', '@odoo/owl', '@web/views/fields/standard_field_props', '@web/core/utils/hooks'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { Component } = require("@odoo/owl");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const { useService } = require("@web/core/utils/hooks");

const WARNING_TYPE_ORDER = ["danger", "warning", "info"];

const ActionableErrors = __exports.ActionableErrors = class ActionableErrors extends Component {
    static props = { errorData: {type: Object} };
    static template = "account.ActionableErrors";

    setup() {
        super.setup();
        this.actionService = useService("action");
    }

    get errorData() {
        return this.props.errorData;
    }

    async handleOnClick(errorData){
        this.env.model.action.doAction(errorData.action);
    }

    get sortedActionableErrors() {
        return this.errorData && Object.fromEntries(
            Object.entries(this.errorData).sort(
                (a, b) =>
                    WARNING_TYPE_ORDER.indexOf(a[1]["level"] || "warning") -
                    WARNING_TYPE_ORDER.indexOf(b[1]["level"] || "warning"),
            ),
        );
    }
}

const ActionableErrorsField = __exports.ActionableErrorsField = class ActionableErrorsField extends ActionableErrors {
    static props = { ...standardFieldProps };

    get errorData() {
        return this.props.record.data[this.props.name];
    }
}

const actionableErrorsField = __exports.actionableErrorsField = {component: ActionableErrorsField};
registry.category("fields").add("actionable_errors", actionableErrorsField);

return __exports;
});
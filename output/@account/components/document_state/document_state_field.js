/************************************************************************************
*  Filepath: /account/static/src/components/document_state/document_state_field.js  *
*  Lines: 77                                                                        *
************************************************************************************/
odoo.define('@account/components/document_state/document_state_field', ['@web/core/l10n/translation', '@web/core/registry', '@web/core/utils/hooks', '@web/views/fields/selection/selection_field', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");
const { useService } = require("@web/core/utils/hooks");

const { SelectionField, selectionField } = require("@web/views/fields/selection/selection_field");

const { Component } = require("@odoo/owl");

const DocumentStatePopover = __exports.DocumentStatePopover = class DocumentStatePopover extends Component {
    static template = "account.DocumentStatePopover";
    static props = {
        close: Function,
        onClose: Function,
        copyText: Function,
        message: String,
    };
}

const DocumentState = __exports.DocumentState = class DocumentState extends SelectionField {
    static template = "account.DocumentState";

    setup() {
        super.setup();
        this.popover = useService("popover");
        this.notification = useService("notification");
    }

    get message() {
        return this.props.record.data.message;
    }

    copyText() {
        navigator.clipboard.writeText(this.message);
        this.notification.add(_t("Text copied"), { type: "success" });
        this.popoverCloseFn();
        this.popoverCloseFn = null;
    }

    showMessagePopover(ev) {
        const close = () => {
            this.popoverCloseFn();
            this.popoverCloseFn = null;
        };

        if (this.popoverCloseFn) {
            close();
            return;
        }

        this.popoverCloseFn = this.popover.add(
            ev.currentTarget,
            DocumentStatePopover,
            {
                message: this.message,
                copyText: this.copyText.bind(this),
                onClose: close,
            },
            {
                closeOnClickAway: true,
                position: "top",
            },
        );
    }
}

registry.category("fields").add("account_document_state", {
    ...selectionField,
    component: DocumentState,
});

return __exports;
});
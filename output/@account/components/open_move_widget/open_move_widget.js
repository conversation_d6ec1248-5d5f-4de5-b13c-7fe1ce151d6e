/**********************************************************************************
*  Filepath: /account/static/src/components/open_move_widget/open_move_widget.js  *
*  Lines: 35                                                                      *
**********************************************************************************/
odoo.define('@account/components/open_move_widget/open_move_widget', ['@web/core/registry', '@web/core/utils/hooks', '@web/views/fields/standard_field_props', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { useService } = require("@web/core/utils/hooks");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");
const { Component } = require("@odoo/owl");

class OpenMoveWidget extends Component {
    static template = "account.OpenMoveWidget";
    static props = { ...standardFieldProps };

    setup() {
        super.setup();
        this.action = useService("action");
    }

    async openMove(ev) {
        this.action.doActionButton({
            type: "object",
            resId: this.props.record.resId,
            name: "action_open_business_doc",
            resModel: "account.move.line",
        });
    }
}

registry.category("fields").add("open_move_widget", {
    component: OpenMoveWidget,
});

return __exports;
});
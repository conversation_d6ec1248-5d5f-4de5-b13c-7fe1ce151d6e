/**********************************************************************
*  Filepath: /account/static/src/components/onboarding/onboarding.js  *
*  Lines: 43                                                          *
**********************************************************************/
odoo.define('@account/components/onboarding/onboarding', ['@web/core/registry', '@web/views/widgets/standard_widget_props', '@web/core/utils/hooks', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { standardWidgetProps } = require("@web/views/widgets/standard_widget_props");
const { useService } = require("@web/core/utils/hooks");

const { Component } = require("@odoo/owl");

class AccountOnboardingWidget extends Component {
    static template = "account.Onboarding";
    static props = {
        ...standardWidgetProps,
    };
    setup() {
        this.action = useService("action");
        this.orm = useService("orm");
    }

    get recordOnboardingSteps() {
        return JSON.parse(this.props.record.data.kanban_dashboard).onboarding?.steps;
    }

    async onboardingLinkClicked(step) {
        const action = await this.orm.call("onboarding.onboarding.step", step.action, [], {
            context: {
                journal_id: this.props.record.resId,
            }
        });
        this.action.doAction(action);
    }
}

const accountOnboarding = __exports.accountOnboarding = {
    component: AccountOnboardingWidget,
}

registry.category("view_widgets").add("account_onboarding", accountOnboarding);

return __exports;
});
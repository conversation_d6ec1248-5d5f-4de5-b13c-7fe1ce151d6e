/**********************************************************************************************
*  Filepath: /account/static/src/components/document_file_uploader/document_file_uploader.js  *
*  Lines: 84                                                                                  *
**********************************************************************************************/
odoo.define('@account/components/document_file_uploader/document_file_uploader', ['@web/core/utils/hooks', '@web/views/fields/file_handler', '@web/views/widgets/standard_widget_props', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
const { useService } = require("@web/core/utils/hooks");
const { FileUploader } = require("@web/views/fields/file_handler");
const { standardWidgetProps } = require("@web/views/widgets/standard_widget_props");

const { Component, markup } = require("@odoo/owl");

const DocumentFileUploader = __exports.DocumentFileUploader = class DocumentFileUploader extends Component {
    static template = "account.DocumentFileUploader";
    static components = {
        FileUploader,
    };
    static props = {
        ...standardWidgetProps,
        record: { type: Object, optional: true },
        slots: { type: Object, optional: true },
        resModel: { type: String, optional: true },
    };

    setup() {
        this.orm = useService("orm");
        this.action = useService("action");
        this.notification = useService("notification");
        this.attachmentIdsToProcess = [];
        this.extraContext = this.getExtraContext();
    }

    // To pass extra context while creating record
    getExtraContext() {
        return {};
    }

    async onFileUploaded(file) {
        const att_data = {
            name: file.name,
            mimetype: file.type,
            datas: file.data,
        };
        const [att_id] = await this.orm.create("ir.attachment", [att_data], {
            context: { ...this.extraContext, ...this.env.searchModel.context },
        });
        this.attachmentIdsToProcess.push(att_id);
    }

    // To define specific resModal from another model
    getResModel() {
        return this.props.resModel;
    }

    async onUploadComplete() {
        const resModal = this.getResModel();
        let action;
        try {
            action = await this.orm.call(
                resModal,
                "create_document_from_attachment",
                ["", this.attachmentIdsToProcess],
                { context: { ...this.extraContext, ...this.env.searchModel.context } }
            );
        } finally {
            // ensures attachments are cleared on success as well as on error
            this.attachmentIdsToProcess = [];
        }
        if (action.context && action.context.notifications) {
            for (const [file, msg] of Object.entries(action.context.notifications)) {
                this.notification.add(msg, {
                    title: file,
                    type: "info",
                    sticky: true,
                });
            }
            delete action.context.notifications;
        }
        if (action.help?.length) {
            action.help = markup(action.help);
        }
        this.action.doAction(action);
    }
}

return __exports;
});
/************************************************************************************************
*  Filepath: /account/static/src/components/account_payment_term_form/payment_term_line_ids.js  *
*  Lines: 33                                                                                    *
************************************************************************************************/
odoo.define('@account/components/account_payment_term_form/payment_term_line_ids', ['@web/core/registry', '@web/views/fields/x2many/x2many_field', '@web/views/fields/relational_utils'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");

const { X2ManyField, x2ManyField } = require("@web/views/fields/x2many/x2many_field");
const { useAddInlineRecord } = require("@web/views/fields/relational_utils");

const PaymentTermLineIdsOne2Many = __exports.PaymentTermLineIdsOne2Many = class PaymentTermLineIdsOne2Many extends X2ManyField {
    setup() {
        super.setup();
        // Overloads the addInLine method to mark all new records as 'dirty' by calling update with an empty object.
        // This prevents the records from being abandoned if the user clicks globally or on an existing record.
        this.addInLine = useAddInlineRecord({
            addNew: async (...args) => {
                const newRecord = await this.list.addNewRecord(...args);
                newRecord.update({});
            }
        });
    }
}

const PaymentTermLineIds = __exports.PaymentTermLineIds = {
    ...x2ManyField,
    component: PaymentTermLineIdsOne2Many,
}

registry.category("fields").add("payment_term_line_ids", PaymentTermLineIds);

return __exports;
});
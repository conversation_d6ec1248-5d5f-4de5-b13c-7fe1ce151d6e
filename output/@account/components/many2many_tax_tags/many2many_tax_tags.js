/**************************************************************************************
*  Filepath: /account/static/src/components/many2many_tax_tags/many2many_tax_tags.js  *
*  Lines: 75                                                                          *
**************************************************************************************/
odoo.define('@account/components/many2many_tax_tags/many2many_tax_tags', ['@web/core/l10n/translation', '@web/core/registry', '@web/views/fields/relational_utils', '@web/views/fields/many2many_tags/many2many_tags_field', '@account/components/tax_autocomplete/tax_autocomplete'], function (require) {
'use strict';
let __exports = {};
const { _t } = require("@web/core/l10n/translation");
const { registry } = require("@web/core/registry");

const { Many2XAutocomplete } = require("@web/views/fields/relational_utils");
const {
    Many2ManyTagsField,
    many2ManyTagsField,
} = require("@web/views/fields/many2many_tags/many2many_tags_field");

const { TaxAutoComplete } = require("@account/components/tax_autocomplete/tax_autocomplete");

const Many2ManyTaxTagsAutocomplete = __exports.Many2ManyTaxTagsAutocomplete = class Many2ManyTaxTagsAutocomplete extends Many2XAutocomplete {
    static components = {
        ...Many2XAutocomplete.components,
        AutoComplete: TaxAutoComplete,
    };
    get SearchMoreButtonLabel() {
        return _t("Not sure... Help me!");
    }

    search(name) {
        return this.orm
            .call(this.props.resModel, "search_read", [], {
                domain: [...this.props.getDomain(), ["name", "ilike", name]],
                fields: ["id", "name", "tax_scope"],
            })
            .then((records) => {
                return this.orm
                    .call("account.tax", "fields_get", [], { attributes: ["selection"] })
                    .then((fields) => {
                        const selectionOptions = fields.tax_scope.selection;

                        const recordsWithLabels = records.map((record) => {
                            const selectedOption = selectionOptions.find(
                                (option) => option[0] === record.tax_scope
                            );
                            const label = selectedOption ? selectedOption[1] : undefined;
                            return { ...record, tax_scope: label };
                        });

                        return recordsWithLabels;
                    });
            });
    }

    mapRecordToOption(result) {
        return {
            value: result.id,
            label: result.name ? result.name.split("\n")[0] : _t("Unnamed"),
            displayName: result.name,
            tax_scope: result.tax_scope,
        };
    }
}

const Many2ManyTaxTagsField = __exports.Many2ManyTaxTagsField = class Many2ManyTaxTagsField extends Many2ManyTagsField {
    static components = {
        ...Many2ManyTagsField.components,
        Many2XAutocomplete: Many2ManyTaxTagsAutocomplete,
    };
}

const many2ManyTaxTagsField = __exports.many2ManyTaxTagsField = {
    ...many2ManyTagsField,
    component: Many2ManyTaxTagsField,
    additionalClasses: ['o_field_many2many_tags']
};

registry.category("fields").add("many2many_tax_tags", many2ManyTaxTagsField);

return __exports;
});
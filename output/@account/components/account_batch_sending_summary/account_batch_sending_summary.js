/************************************************************************************************************
*  Filepath: /account/static/src/components/account_batch_sending_summary/account_batch_sending_summary.js  *
*  Lines: 27                                                                                                *
************************************************************************************************************/
odoo.define('@account/components/account_batch_sending_summary/account_batch_sending_summary', ['@odoo/owl', '@web/core/registry', '@web/views/fields/standard_field_props'], function (require) {
'use strict';
let __exports = {};
const {Component} = require("@odoo/owl");
const {registry} = require("@web/core/registry");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");

const AccountBatchSendingSummary = __exports.AccountBatchSendingSummary = class AccountBatchSendingSummary extends Component {
    static template = "account.BatchSendingSummary";
    static props = {
        ...standardFieldProps,
    };

    setup() {
        super.setup();
        this.data = this.props.record.data[this.props.name];
    }
}

const accountBatchSendingSummary = __exports.accountBatchSendingSummary = {
    component: AccountBatchSendingSummary,
}

registry.category("fields").add("account_batch_sending_summary", accountBatchSendingSummary);

return __exports;
});
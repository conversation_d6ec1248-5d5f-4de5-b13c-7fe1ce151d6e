/**********************************************************************************
*  Filepath: /account/static/src/components/product_catalog/kanban_controller.js  *
*  Lines: 28                                                                      *
**********************************************************************************/
odoo.define('@account/components/product_catalog/kanban_controller', ['@product/product_catalog/kanban_controller', '@web/core/utils/patch', '@web/core/l10n/translation'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */
const { ProductCatalogKanbanController } = require("@product/product_catalog/kanban_controller");
const { patch } = require("@web/core/utils/patch");
const { _t } = require("@web/core/l10n/translation");

patch(ProductCatalogKanbanController.prototype, {
    async _defineButtonContent() {
        const fields = this.orderResModel === "account.move" ? ["state", "move_type"] : ["state"];
        const orderStateInfo = await this.orm.searchRead(
            this.orderResModel,
            [["id", "=", this.orderId]],
            fields,
        );
        if (orderStateInfo[0]?.move_type === "out_invoice") {
            this.buttonString = _t("Back to Invoice");
        } else if (orderStateInfo[0]?.move_type === "in_invoice") {
            this.buttonString = _t("Back to Bill");
        } else {
            this.buttonString = super._defineButtonContent();
        }
    },
});

return __exports;
});
/**********************************************************************************
*  Filepath: /account/static/src/components/product_catalog/account_move_line.js  *
*  Lines: 15                                                                      *
**********************************************************************************/
odoo.define('@account/components/product_catalog/account_move_line', ['@product/product_catalog/order_line/order_line'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */
const { ProductCatalogOrderLine } = require("@product/product_catalog/order_line/order_line");

const ProductCatalogAccountMoveLine = __exports.ProductCatalogAccountMoveLine = class ProductCatalogAccountMoveLine extends ProductCatalogOrderLine {
    static props = {
        ...ProductCatalogOrderLine.props,
        min_qty: { type: Number, optional: true },
    };
}

return __exports;
});
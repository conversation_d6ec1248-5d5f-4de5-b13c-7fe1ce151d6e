/******************************************************************************
*  Filepath: /account/static/src/components/product_catalog/kanban_record.js  *
*  Lines: 27                                                                  *
******************************************************************************/
odoo.define('@account/components/product_catalog/kanban_record', ['@product/product_catalog/kanban_record', '@account/components/product_catalog/account_move_line', '@web/core/utils/patch'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */
const { ProductCatalogKanbanRecord } = require("@product/product_catalog/kanban_record");
const { ProductCatalogAccountMoveLine } = require("@account/components/product_catalog/account_move_line");
const { patch } = require("@web/core/utils/patch");

patch(ProductCatalogKanbanRecord.prototype, {
    get orderLineComponent() {
        if (this.env.orderResModel === "account.move") {
            return ProductCatalogAccountMoveLine;
        }
        return super.orderLineComponent;
    },

    addProduct() {
        if (this.productCatalogData.quantity === 0 && this.productCatalogData.min_qty) {
            super.addProduct(this.productCatalogData.min_qty);
        } else {
            super.addProduct(...arguments);
        }
    },
})

return __exports;
});
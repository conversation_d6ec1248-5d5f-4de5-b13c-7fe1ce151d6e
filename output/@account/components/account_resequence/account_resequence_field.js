/********************************************************************************************
*  Filepath: /account/static/src/components/account_resequence/account_resequence_field.js  *
*  Lines: 30                                                                                *
********************************************************************************************/
odoo.define('@account/components/account_resequence/account_resequence_field', ['@web/core/registry', '@odoo/owl', '@web/views/fields/standard_field_props'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { registry } = require("@web/core/registry");
const { Component } = require("@odoo/owl");
const { standardFieldProps } = require("@web/views/fields/standard_field_props");

class ChangeLine extends Component {
    static template = "account.ResequenceChangeLine";
    static props = ["changeLine", "ordering"];
}

class ShowResequenceRenderer extends Component {
    static template = "account.ResequenceRenderer";
    static components = { ChangeLine };
    static props = { ...standardFieldProps };
    getValue() {
        const value = this.props.record.data[this.props.name];
        return value ? JSON.parse(value) : { changeLines: [], ordering: "date" };
    }
}

registry.category("fields").add("account_resequence_widget", {
    component: ShowResequenceRenderer,
});

return __exports;
});
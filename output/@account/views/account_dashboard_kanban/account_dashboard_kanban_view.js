/**************************************************************************************************
*  Filepath: /account/static/src/views/account_dashboard_kanban/account_dashboard_kanban_view.js  *
*  Lines: 16                                                                                      *
**************************************************************************************************/
odoo.define('@account/views/account_dashboard_kanban/account_dashboard_kanban_view', ['@web/core/registry', '@web/views/kanban/kanban_view', '@account/views/account_dashboard_kanban/account_dashboard_kanban_renderer'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { kanbanView } = require("@web/views/kanban/kanban_view");
const { DashboardKanbanRenderer } = require("@account/views/account_dashboard_kanban/account_dashboard_kanban_renderer");

const accountDashboardKanbanView = __exports.accountDashboardKanbanView = {
    ...kanbanView,
    Renderer: DashboardKanbanRenderer,
};

registry.category("views").add("account_dashboard_kanban", accountDashboardKanbanView);

return __exports;
});
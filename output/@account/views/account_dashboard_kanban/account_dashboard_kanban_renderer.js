/******************************************************************************************************
*  Filepath: /account/static/src/views/account_dashboard_kanban/account_dashboard_kanban_renderer.js  *
*  Lines: 50                                                                                          *
******************************************************************************************************/
odoo.define('@account/views/account_dashboard_kanban/account_dashboard_kanban_renderer', ['@web/views/kanban/kanban_renderer', '@account/views/account_dashboard_kanban/account_dashboard_kanban_record', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
const { KanbanRenderer } = require("@web/views/kanban/kanban_renderer");
const { DashboardKanbanRecord } = require("@account/views/account_dashboard_kanban/account_dashboard_kanban_record");

const { useSubEnv, reactive } = require("@odoo/owl");

const DashboardKanbanRenderer = __exports.DashboardKanbanRenderer = class DashboardKanbanRenderer extends KanbanRenderer {
    static template = "account.DashboardKanbanRenderer";
    static components = {
        ...KanbanRenderer.components,
        KanbanRecord: DashboardKanbanRecord,
    };

    setup() {
        super.setup();
        useSubEnv({
            dashboardState: reactive({isDragging: false}),
            setDragging: this.setDragging.bind(this),
        });
    }

    kanbanDragEnter(e) {
        this.env.dashboardState.isDragging = true;
    }

    kanbanDragLeave(e) {
        const mouseX = e.clientX, mouseY = e.clientY;
        const {x, y, width, height} = this.rootRef.el.getBoundingClientRect();
        if (!(mouseX > x && mouseX <= x + width && mouseY > y && mouseY <= y + height)) {
            // if the mouse position is outside the kanban renderer, all cards should hide their dropzones.
            this.setDragging(false);
        } else {
            this.setDragging(true);
        }
    }

    kanbanDragDrop(e) {
        this.setDragging(false);
        return false;
    }

    setDragging(value) {
        this.env.dashboardState.isDragging = value;
    }
}

return __exports;
});
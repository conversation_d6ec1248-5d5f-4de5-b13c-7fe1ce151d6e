/****************************************************************************************************
*  Filepath: /account/static/src/views/account_dashboard_kanban/account_dashboard_kanban_record.js  *
*  Lines: 58                                                                                        *
****************************************************************************************************/
odoo.define('@account/views/account_dashboard_kanban/account_dashboard_kanban_record', ['@web/core/user', '@account/components/account_file_uploader/account_file_uploader', '@account/components/upload_drop_zone/upload_drop_zone', '@web/views/kanban/kanban_dropdown_menu_wrapper', '@web/views/kanban/kanban_record', '@odoo/owl'], function (require) {
'use strict';
let __exports = {};
const { user } = require("@web/core/user");
const { AccountFileUploader } = require("@account/components/account_file_uploader/account_file_uploader");
const { UploadDropZone } = require("@account/components/upload_drop_zone/upload_drop_zone");
const { KanbanDropdownMenuWrapper } = require("@web/views/kanban/kanban_dropdown_menu_wrapper");
const { KanbanRecord } = require("@web/views/kanban/kanban_record");

const { useState, onWillStart } = require("@odoo/owl");

// Accounting Dashboard
const DashboardKanbanDropdownMenuWrapper = __exports.DashboardKanbanDropdownMenuWrapper = class DashboardKanbanDropdownMenuWrapper extends KanbanDropdownMenuWrapper {
    onClick(ev) {
        // Keep the dropdown open as we need the fileupload to remain in the dom
        if (!ev.target.tagName === "INPUT" && !ev.target.closest('.file_upload_kanban_action_a')) {
            super.onClick(ev);
        }
    }
}

const DashboardKanbanRecord = __exports.DashboardKanbanRecord = class DashboardKanbanRecord extends KanbanRecord {
    static template = "account.DashboardKanbanRecord";
    static components = {
        ...DashboardKanbanRecord.components,
        UploadDropZone,
        AccountFileUploader,
        KanbanDropdownMenuWrapper: DashboardKanbanDropdownMenuWrapper,
    };

    setup() {
        super.setup();
        onWillStart(async () => {
            this.allowDrop = this.recordDropSettings.group ? await user.hasGroup(this.recordDropSettings.group) : true;
        });
        this.dropzoneState = useState({
            visible: false,
        });
    }

    get recordDropSettings() {
        return JSON.parse(this.props.record.data.kanban_dashboard).drag_drop_settings;
    }

    get dropzoneProps() {
        const {image, text} = this.recordDropSettings;
        return {
            visible: this.dropzoneState.visible,
            dragIcon: image,
            dragText: text,
            dragTitle: this.props.record.data.name,
            hideZone: () => { this.dropzoneState.visible = false; },
        }
    }
}

return __exports;
});
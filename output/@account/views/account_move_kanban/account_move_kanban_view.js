/****************************************************************************************
*  Filepath: /account/static/src/views/account_move_kanban/account_move_kanban_view.js  *
*  Lines: 17                                                                            *
****************************************************************************************/
odoo.define('@account/views/account_move_kanban/account_move_kanban_view', ['@web/core/registry', '@account/views/file_upload_kanban/file_upload_kanban_view', '@account/views/account_move_kanban/account_move_kanban_controller'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { fileUploadKanbanView } = require("@account/views/file_upload_kanban/file_upload_kanban_view");
const { AccountMoveKanbanController } = require("@account/views/account_move_kanban/account_move_kanban_controller");

const accountMoveUploadKanbanView = __exports.accountMoveUploadKanbanView = {
    ...fileUploadKanbanView,
    Controller: AccountMoveKanbanController,
    buttonTemplate: "account.AccountMoveKanbanView.Buttons",
};

registry.category("views").add("account_documents_kanban", accountMoveUploadKanbanView);

return __exports;
});
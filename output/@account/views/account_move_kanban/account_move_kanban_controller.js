/**********************************************************************************************
*  Filepath: /account/static/src/views/account_move_kanban/account_move_kanban_controller.js  *
*  Lines: 20                                                                                  *
**********************************************************************************************/
odoo.define('@account/views/account_move_kanban/account_move_kanban_controller', ['@account/views/file_upload_kanban/file_upload_kanban_controller', '@account/components/account_file_uploader/account_file_uploader'], function (require) {
'use strict';
let __exports = {};
const { FileUploadKanbanController } = require("@account/views/file_upload_kanban/file_upload_kanban_controller");
const { AccountFileUploader } = require("@account/components/account_file_uploader/account_file_uploader");

const AccountMoveKanbanController = __exports.AccountMoveKanbanController = class AccountMoveKanbanController extends FileUploadKanbanController {
    static components = {
        ...FileUploadKanbanController.components,
        AccountFileUploader,
    };

    setup() {
        super.setup();
        this.showUploadButton = this.props.context.default_move_type !== 'entry' || 'active_id' in this.props.context;
    }
}

return __exports;
});
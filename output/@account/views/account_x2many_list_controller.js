/**************************************************************************
*  Filepath: /account/static/src/views/account_x2many_list_controller.js  *
*  Lines: 28                                                              *
**************************************************************************/
odoo.define('@account/views/account_x2many_list_controller', ['@web/views/list/list_controller', '@web/core/utils/hooks', '@web/core/registry', '@web/views/list/list_view'], function (require) {
'use strict';
let __exports = {};
const { ListController } = require("@web/views/list/list_controller");
const { useService } = require("@web/core/utils/hooks");
const {registry} = require("@web/core/registry");
const {listView} = require("@web/views/list/list_view");

const AccountX2ManyListController = __exports.AccountX2ManyListController = class AccountX2ManyListController extends ListController {
    setup() {
        super.setup();
        this.orm = useService("orm");
        this.action = useService("action");
    }

    async openRecord(record) {
        const action = await this.orm.call(record.resModel, 'action_open_business_doc', [record.resId], {});
        return this.actionService.doAction(action);
    }
}

registry.category("views").add("account_x2many_list", {
    ...listView,
    Controller: AccountX2ManyListController,
});

return __exports;
});
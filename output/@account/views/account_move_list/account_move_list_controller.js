/******************************************************************************************
*  Filepath: /account/static/src/views/account_move_list/account_move_list_controller.js  *
*  Lines: 44                                                                              *
******************************************************************************************/
odoo.define('@account/views/account_move_list/account_move_list_controller', ['@web/core/l10n/translation', '@account/views/file_upload_list/file_upload_list_controller', '@account/components/account_file_uploader/account_file_uploader', '@web/core/utils/hooks'], function (require) {
'use strict';
let __exports = {};
const { _t } = require("@web/core/l10n/translation");
const { FileUploadListController } = require("@account/views/file_upload_list/file_upload_list_controller");
const { AccountFileUploader } = require("@account/components/account_file_uploader/account_file_uploader");

const { useService } = require("@web/core/utils/hooks");

const AccountMoveListController = __exports.AccountMoveListController = class AccountMoveListController extends FileUploadListController {
    static components = {
        ...FileUploadListController.components,
        AccountFileUploader,
    };

    setup() {
        super.setup();
        this.orm = useService("orm");
        this.account_move_service = useService("account_move");
        this.showUploadButton = this.props.context.default_move_type !== 'entry' || 'active_id' in this.props.context;
    }

    get actionMenuProps() {
        return {
            ...super.actionMenuProps,
            printDropdownTitle: _t("Download"),
            loadExtraPrintItems: this.loadExtraPrintItems.bind(this),
        };
    }

    async loadExtraPrintItems() {
        return this.orm.call("account.move", "get_extra_print_items", [this.actionMenuProps.getActiveIds()]);
    }

    async onDeleteSelectedRecords() {
        const selectedResIds = await this.getSelectedResIds();
        if (this.props.resModel !== "account.move" || !(await this.account_move_service.addDeletionDialog(this, selectedResIds))) {
            return super.onDeleteSelectedRecords(...arguments);
        }
    }
}

return __exports;
});
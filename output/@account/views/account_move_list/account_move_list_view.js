/************************************************************************************
*  Filepath: /account/static/src/views/account_move_list/account_move_list_view.js  *
*  Lines: 19                                                                        *
************************************************************************************/
odoo.define('@account/views/account_move_list/account_move_list_view', ['@web/core/registry', '@account/views/file_upload_list/file_upload_list_view', '@account/views/account_move_list/account_move_list_controller', '@account/views/account_move_list/account_move_list_renderer'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { fileUploadListView } = require("@account/views/file_upload_list/file_upload_list_view");
const { AccountMoveListController } = require("@account/views/account_move_list/account_move_list_controller");
const { AccountMoveUploadListRenderer } = require("@account/views/account_move_list/account_move_list_renderer");

const accountMoveUploadListView = __exports.accountMoveUploadListView = {
    ...fileUploadListView,
    Controller: AccountMoveListController,
    Renderer: AccountMoveUploadListRenderer,
    buttonTemplate: "account.AccountMoveListView.Buttons",
};

registry.category("views").add("account_tree", accountMoveUploadListView);

return __exports;
});
/****************************************************************************************
*  Filepath: /account/static/src/views/account_move_list/account_move_list_renderer.js  *
*  Lines: 16                                                                            *
****************************************************************************************/
odoo.define('@account/views/account_move_list/account_move_list_renderer', ['@account/components/bill_guide/bill_guide', '@account/views/file_upload_list/file_upload_list_renderer'], function (require) {
'use strict';
let __exports = {};
const { BillGuide } = require("@account/components/bill_guide/bill_guide");
const { FileUploadListRenderer } = require("@account/views/file_upload_list/file_upload_list_renderer");

const AccountMoveUploadListRenderer = __exports.AccountMoveUploadListRenderer = class AccountMoveUploadListRenderer extends FileUploadListRenderer {
    static template = "account.AccountMoveListRenderer";
    static components = {
        ...FileUploadListRenderer.components,
        BillGuide,
    };
}

return __exports;
});
/********************************************************************************************
*  Filepath: /account/static/src/views/file_upload_kanban/file_upload_kanban_controller.js  *
*  Lines: 15                                                                                *
********************************************************************************************/
odoo.define('@account/views/file_upload_kanban/file_upload_kanban_controller', ['@web/views/kanban/kanban_controller', '@account/components/document_file_uploader/document_file_uploader'], function (require) {
'use strict';
let __exports = {};
const { Kanban<PERSON>ontroller } = require("@web/views/kanban/kanban_controller");
const { DocumentFileUploader } = require("@account/components/document_file_uploader/document_file_uploader");

const FileUploadKanbanController = __exports.FileUploadKanbanController = class FileUploadKanbanController extends KanbanController {
    static components = {
        ...KanbanController.components,
        DocumentFileUploader,
    };
}

return __exports;
});
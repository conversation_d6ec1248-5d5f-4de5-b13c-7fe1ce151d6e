/**************************************************************************************
*  Filepath: /account/static/src/views/file_upload_kanban/file_upload_kanban_view.js  *
*  Lines: 19                                                                          *
**************************************************************************************/
odoo.define('@account/views/file_upload_kanban/file_upload_kanban_view', ['@web/core/registry', '@web/views/kanban/kanban_view', '@account/views/file_upload_kanban/file_upload_kanban_controller', '@account/views/file_upload_kanban/file_upload_kanban_renderer'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { kanbanView } = require("@web/views/kanban/kanban_view");
const { FileUploadKanbanController } = require("@account/views/file_upload_kanban/file_upload_kanban_controller");
const { FileUploadKanbanRenderer } = require("@account/views/file_upload_kanban/file_upload_kanban_renderer");

const fileUploadKanbanView = __exports.fileUploadKanbanView = {
    ...kanbanView,
    Controller: FileUploadKanbanController,
    Renderer: FileUploadKanbanRenderer,
    buttonTemplate: "account.FileuploadKanbanView.Buttons",
};

registry.category("views").add("file_upload_kanban", fileUploadKanbanView);

return __exports;
});
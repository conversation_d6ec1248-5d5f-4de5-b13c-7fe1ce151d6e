/******************************************************************************************
*  Filepath: /account/static/src/views/file_upload_kanban/file_upload_kanban_renderer.js  *
*  Lines: 32                                                                              *
******************************************************************************************/
odoo.define('@account/views/file_upload_kanban/file_upload_kanban_renderer', ['@web/views/kanban/kanban_renderer', '@account/components/upload_drop_zone/upload_drop_zone', '@odoo/owl', '@account/views/upload_file_from_data_hook'], function (require) {
'use strict';
let __exports = {};
const { KanbanRenderer } = require("@web/views/kanban/kanban_renderer");
const { UploadDropZone } = require("@account/components/upload_drop_zone/upload_drop_zone");
const { useState } = require("@odoo/owl");
const { uploadFileFromData } = require("@account/views/upload_file_from_data_hook");

const FileUploadKanbanRenderer = __exports.FileUploadKanbanRenderer = class FileUploadKanbanRenderer extends KanbanRenderer {
    static template = "account.FileUploadKanbanRenderer";
    static components = {
        ...KanbanRenderer.components,
        UploadDropZone,
    };

    setup() {
        super.setup();
        this.dropzoneState = useState({ visible: false });
        this.uploadFileFromData = uploadFileFromData();
    }

    async onPaste(ev) {
        if (!ev.clipboardData?.items) {
            return;
        }
        ev.preventDefault();
        this.uploadFileFromData(ev.clipboardData);
    }
}

return __exports;
});
/**************************************************************************************
*  Filepath: /account/static/src/views/file_upload_list/file_upload_list_renderer.js  *
*  Lines: 33                                                                          *
**************************************************************************************/
odoo.define('@account/views/file_upload_list/file_upload_list_renderer', ['@web/views/list/list_renderer', '@account/components/upload_drop_zone/upload_drop_zone', '@odoo/owl', '@account/views/upload_file_from_data_hook'], function (require) {
'use strict';
let __exports = {};
const { ListRenderer } = require("@web/views/list/list_renderer");
const { UploadDropZone } = require("@account/components/upload_drop_zone/upload_drop_zone");
const { useState } = require("@odoo/owl");
const { uploadFileFromData } = require("@account/views/upload_file_from_data_hook");

const FileUploadListRenderer = __exports.FileUploadListRenderer = class FileUploadListRenderer extends ListRenderer {
    static template = "account.FileUploadListRenderer";
    static components = {
        ...ListRenderer.components,
        UploadDropZone,
    };

    setup() {
        super.setup();
        this.dropzoneState = useState({ visible: false });
        this.uploadFileFromData = uploadFileFromData();
    }

    async onPaste(ev) {
        if (!ev.clipboardData?.items) {
            return;
        }
        ev.preventDefault();
        this.uploadFileFromData(ev.clipboardData);
    }

}

return __exports;
});
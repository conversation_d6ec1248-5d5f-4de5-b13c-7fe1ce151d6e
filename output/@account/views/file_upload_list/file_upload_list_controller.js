/****************************************************************************************
*  Filepath: /account/static/src/views/file_upload_list/file_upload_list_controller.js  *
*  Lines: 15                                                                            *
****************************************************************************************/
odoo.define('@account/views/file_upload_list/file_upload_list_controller', ['@web/views/list/list_controller', '@account/components/document_file_uploader/document_file_uploader'], function (require) {
'use strict';
let __exports = {};
const { ListController } = require("@web/views/list/list_controller");
const { DocumentFileUploader } = require("@account/components/document_file_uploader/document_file_uploader");

const FileUploadListController = __exports.FileUploadListController = class FileUploadListController extends ListController {
    static components = {
        ...ListController.components,
        DocumentFileUploader,
    };
};

return __exports;
});
/**********************************************************************************
*  Filepath: /account/static/src/views/file_upload_list/file_upload_list_view.js  *
*  Lines: 19                                                                      *
**********************************************************************************/
odoo.define('@account/views/file_upload_list/file_upload_list_view', ['@web/core/registry', '@web/views/list/list_view', '@account/views/file_upload_list/file_upload_list_controller', '@account/views/file_upload_list/file_upload_list_renderer'], function (require) {
'use strict';
let __exports = {};
const { registry } = require("@web/core/registry");
const { listView } = require("@web/views/list/list_view");
const { FileUploadListController } = require("@account/views/file_upload_list/file_upload_list_controller");
const { FileUploadListRenderer } = require("@account/views/file_upload_list/file_upload_list_renderer");

const fileUploadListView = __exports.fileUploadListView = {
    ...listView,
    Controller: FileUploadListController,
    Renderer: FileUploadListRenderer,
    buttonTemplate: "account.FileuploadListView.Buttons",
};

registry.category("views").add("file_upload_list", fileUploadListView);

return __exports;
});
/*********************************************************************
*  Filepath: /account/static/src/js/search/search_bar/search_bar.js  *
*  Lines: 24                                                         *
*********************************************************************/
odoo.define('@account/js/search/search_bar/search_bar', ['@web/core/l10n/translation', '@web/core/utils/patch', '@web/search/search_bar/search_bar'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { patch } = require("@web/core/utils/patch");
const { SearchBar } = require("@web/search/search_bar/search_bar");

patch(SearchBar.prototype, {
    getPreposition(searchItem) {
        let preposition = super.getPreposition(searchItem);
        if (
            this.fields[searchItem.fieldName].name === 'payment_date'
            || this.fields[searchItem.fieldName].name === 'next_payment_date'
        ) {
            preposition = _t("until");
        }
        return preposition
    }
});

return __exports;
});
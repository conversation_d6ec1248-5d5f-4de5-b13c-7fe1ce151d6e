# Dashboard Action Loader - 仪表板操作加载器

## 概述

`dashboard_action_loader.js` 是 Odoo Spreadsheet Dashboard 模块的操作加载器，专门用于注册和管理电子表格仪表板操作的懒加载机制。该文件基于电子表格后端的操作加载器，为仪表板功能提供了专门的操作注册，确保仪表板相关的资源只在需要时才加载，从而优化应用性能。

## 文件信息
- **路径**: `/spreadsheet_dashboard/static/src/assets/dashboard_action_loader.js`
- **行数**: 9
- **模块**: `@spreadsheet_dashboard/assets/dashboard_action_loader`

## 依赖关系

```javascript
// 核心依赖
'@spreadsheet/assets_backend/spreadsheet_action_loader'    // 电子表格操作加载器
```

## 核心功能

### 1. 模块定义

```javascript
odoo.define('@spreadsheet_dashboard/assets/dashboard_action_loader', ['@spreadsheet/assets_backend/spreadsheet_action_loader'], function (require) {
'use strict';
let __exports = {};
```

**模块特性**:
- **Odoo模块**: 使用Odoo标准模块定义方式
- **依赖声明**: 明确依赖电子表格后端操作加载器
- **严格模式**: 启用JavaScript严格模式
- **导出对象**: 使用标准的导出对象模式

### 2. 依赖导入

```javascript
const { addSpreadsheetActionLazyLoader } = require("@spreadsheet/assets_backend/spreadsheet_action_loader");
```

**导入功能**:
- **函数导入**: 导入懒加载操作添加器函数
- **解构语法**: 使用现代JavaScript解构导入
- **功能复用**: 复用电子表格后端的懒加载机制
- **模块化**: 保持模块间的清晰依赖关系

### 3. 仪表板操作注册

```javascript
addSpreadsheetActionLazyLoader("action_spreadsheet_dashboard");
```

**注册功能**:
- **操作注册**: 注册电子表格仪表板操作
- **懒加载**: 使用懒加载机制优化性能
- **标准化**: 使用标准的操作名称
- **即时注册**: 模块加载时立即注册操作

## 使用场景

### 1. 仪表板操作加载器增强

```javascript
// 仪表板操作加载器增强功能
const DashboardActionLoaderEnhancer = {
    enhanceDashboardActionLoader: () => {
        // 增强的仪表板操作加载器
        const enhancedDashboardLoader = {
            // 注册多个仪表板操作
            registerDashboardActions() {
                const dashboardActions = [
                    {
                        name: "action_spreadsheet_dashboard",
                        displayName: _t("Spreadsheet Dashboard"),
                        path: "/dashboard/spreadsheet",
                        dependencies: ["web.chartjs_lib", "spreadsheet.o_spreadsheet"],
                        preloadCondition: () => this.shouldPreloadDashboard(),
                        loadingMessage: _t("Loading dashboard..."),
                        errorMessage: _t("Failed to load dashboard"),
                        timeout: 30000,
                        retryCount: 3
                    },
                    {
                        name: "action_dashboard_create",
                        displayName: _t("Create Dashboard"),
                        path: "/dashboard/create",
                        dependencies: ["spreadsheet.dashboard_creator"],
                        loadingMessage: _t("Loading dashboard creator..."),
                        fallbackAction: this.createDashboardFallback
                    },
                    {
                        name: "action_dashboard_edit",
                        displayName: _t("Edit Dashboard"),
                        path: "/dashboard/edit",
                        dependencies: ["spreadsheet.dashboard_editor"],
                        loadingMessage: _t("Loading dashboard editor..."),
                        preloadCondition: (env, action) => this.hasEditPermission(action)
                    },
                    {
                        name: "action_dashboard_share",
                        displayName: _t("Share Dashboard"),
                        path: "/dashboard/share",
                        dependencies: ["spreadsheet.dashboard_sharing"],
                        loadingMessage: _t("Loading sharing options...")
                    },
                    {
                        name: "action_dashboard_export",
                        displayName: _t("Export Dashboard"),
                        path: "/dashboard/export",
                        dependencies: ["spreadsheet.dashboard_exporter"],
                        loadingMessage: _t("Loading export options...")
                    },
                    {
                        name: "action_dashboard_template",
                        displayName: _t("Dashboard Templates"),
                        path: "/dashboard/templates",
                        dependencies: ["spreadsheet.dashboard_templates"],
                        loadingMessage: _t("Loading templates...")
                    }
                ];
                
                // 注册所有仪表板操作
                for (const action of dashboardActions) {
                    addSpreadsheetActionLazyLoader(action.name, action);
                }
                
                console.log(`已注册 ${dashboardActions.length} 个仪表板操作`);
            },
            
            // 检查是否应该预加载仪表板
            shouldPreloadDashboard() {
                // 检查用户权限
                const user = odoo.session.user_context;
                if (!user.has_dashboard_access) {
                    return false;
                }
                
                // 检查网络条件
                if (navigator.connection && navigator.connection.effectiveType === 'slow-2g') {
                    return false;
                }
                
                // 检查设备性能
                if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
                    return false;
                }
                
                // 检查时间（工作时间更可能使用仪表板）
                const hour = new Date().getHours();
                if (hour >= 9 && hour <= 17) {
                    return true;
                }
                
                return false;
            },
            
            // 检查编辑权限
            hasEditPermission(action) {
                const dashboardId = action.context?.dashboard_id;
                if (!dashboardId) return false;
                
                // 这里应该检查实际的编辑权限
                return odoo.session.user_context.has_dashboard_edit_access;
            },
            
            // 创建仪表板的fallback操作
            createDashboardFallback(env, action) {
                // 简化的仪表板创建界面
                env.services.dialog.add(SimpleDashboardCreator, {
                    title: _t("Create Dashboard"),
                    onSave: (dashboardData) => {
                        this.saveDashboard(dashboardData);
                    }
                });
            },
            
            // 保存仪表板
            async saveDashboard(dashboardData) {
                try {
                    const result = await env.services.orm.call(
                        'spreadsheet.dashboard',
                        'create',
                        [dashboardData]
                    );
                    
                    env.services.notification.add(
                        _t("Dashboard created successfully"),
                        { type: 'success' }
                    );
                    
                    return result;
                } catch (error) {
                    console.error('保存仪表板失败:', error);
                    env.services.notification.add(
                        _t("Failed to create dashboard"),
                        { type: 'error' }
                    );
                }
            },
            
            // 预加载关键仪表板资源
            async preloadCriticalDashboardResources() {
                const criticalResources = [
                    "web.chartjs_lib",
                    "spreadsheet.o_spreadsheet",
                    "spreadsheet.dashboard_core"
                ];
                
                try {
                    console.log('开始预加载关键仪表板资源...');
                    
                    await Promise.all(
                        criticalResources.map(resource => 
                            odoo.loader.loadBundle(resource)
                        )
                    );
                    
                    console.log('关键仪表板资源预加载完成');
                    return true;
                } catch (error) {
                    console.error('预加载关键仪表板资源失败:', error);
                    return false;
                }
            },
            
            // 获取仪表板加载统计
            getDashboardLoadingStats() {
                const stats = {
                    totalActions: 0,
                    loadedActions: 0,
                    failedActions: 0,
                    averageLoadTime: 0,
                    lastLoadTime: null
                };
                
                // 这里应该从实际的统计数据中获取
                // 暂时返回模拟数据
                stats.totalActions = 6;
                stats.loadedActions = 4;
                stats.failedActions = 0;
                stats.averageLoadTime = 1250; // ms
                stats.lastLoadTime = Date.now() - 300000; // 5分钟前
                
                return stats;
            },
            
            // 优化仪表板加载性能
            optimizeDashboardLoading() {
                // 启用资源预取
                this.enableResourcePrefetch();
                
                // 启用智能缓存
                this.enableSmartCaching();
                
                // 启用压缩传输
                this.enableCompressionTransfer();
                
                console.log('仪表板加载性能优化已启用');
            },
            
            // 启用资源预取
            enableResourcePrefetch() {
                const prefetchResources = [
                    "/web/static/lib/Chart/Chart.js",
                    "/spreadsheet/static/src/bundle/o_spreadsheet.js",
                    "/spreadsheet_dashboard/static/src/bundle/dashboard.js"
                ];
                
                for (const resource of prefetchResources) {
                    const link = document.createElement('link');
                    link.rel = 'prefetch';
                    link.href = resource;
                    document.head.appendChild(link);
                }
            },
            
            // 启用智能缓存
            enableSmartCaching() {
                // 设置缓存策略
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.register('/dashboard-sw.js')
                        .then(registration => {
                            console.log('仪表板Service Worker注册成功');
                        })
                        .catch(error => {
                            console.log('仪表板Service Worker注册失败:', error);
                        });
                }
            },
            
            // 启用压缩传输
            enableCompressionTransfer() {
                // 设置请求头以启用压缩
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (url.includes('/dashboard/') || url.includes('/spreadsheet/')) {
                        options.headers = {
                            ...options.headers,
                            'Accept-Encoding': 'gzip, deflate, br'
                        };
                    }
                    return originalFetch(url, options);
                };
            },
            
            // 监控仪表板性能
            monitorDashboardPerformance() {
                // 监控加载时间
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.name.includes('dashboard')) {
                            console.log(`仪表板资源 ${entry.name} 加载耗时: ${entry.duration}ms`);
                        }
                    }
                });
                
                observer.observe({ entryTypes: ['resource'] });
                
                // 监控内存使用
                if (performance.memory) {
                    setInterval(() => {
                        const memory = performance.memory;
                        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                            console.warn('仪表板内存使用过高:', memory);
                        }
                    }, 30000); // 每30秒检查一次
                }
            },
            
            // 清理未使用的仪表板资源
            cleanupUnusedDashboardResources() {
                // 清理过期的缓存
                if ('caches' in window) {
                    caches.keys().then(cacheNames => {
                        cacheNames.forEach(cacheName => {
                            if (cacheName.includes('dashboard') && this.isCacheExpired(cacheName)) {
                                caches.delete(cacheName);
                                console.log(`清理过期缓存: ${cacheName}`);
                            }
                        });
                    });
                }
                
                // 清理DOM中的预取链接
                const prefetchLinks = document.querySelectorAll('link[rel="prefetch"]');
                prefetchLinks.forEach(link => {
                    if (link.href.includes('dashboard') && !this.isResourceActive(link.href)) {
                        link.remove();
                    }
                });
            },
            
            // 检查缓存是否过期
            isCacheExpired(cacheName) {
                // 简单的过期检查逻辑
                const cacheTimestamp = localStorage.getItem(`cache_${cacheName}_timestamp`);
                if (!cacheTimestamp) return true;
                
                const maxAge = 24 * 60 * 60 * 1000; // 24小时
                return Date.now() - parseInt(cacheTimestamp) > maxAge;
            },
            
            // 检查资源是否活跃
            isResourceActive(resourceUrl) {
                // 检查资源是否在最近被使用
                const lastUsed = localStorage.getItem(`resource_${resourceUrl}_last_used`);
                if (!lastUsed) return false;
                
                const maxInactive = 7 * 24 * 60 * 60 * 1000; // 7天
                return Date.now() - parseInt(lastUsed) < maxInactive;
            }
        };
        
        // 初始化增强功能
        enhancedDashboardLoader.registerDashboardActions();
        enhancedDashboardLoader.optimizeDashboardLoading();
        enhancedDashboardLoader.monitorDashboardPerformance();
        
        // 定期清理资源
        setInterval(() => {
            enhancedDashboardLoader.cleanupUnusedDashboardResources();
        }, 60 * 60 * 1000); // 每小时清理一次
        
        // 在空闲时预加载关键资源
        if (window.requestIdleCallback) {
            requestIdleCallback(() => {
                enhancedDashboardLoader.preloadCriticalDashboardResources();
            });
        }
        
        return enhancedDashboardLoader;
    }
};

// 应用仪表板操作加载器增强
const enhancedDashboardLoader = DashboardActionLoaderEnhancer.enhanceDashboardActionLoader();

// 导出增强的加载器
__exports.enhancedDashboardLoader = enhancedDashboardLoader;
```

## 技术特点

### 1. 模块复用
- 复用电子表格后端的懒加载机制
- 避免重复实现相同功能
- 保持代码的简洁性

### 2. 专门化设计
- 专门针对仪表板操作
- 使用标准的操作命名
- 集成到电子表格生态系统

### 3. 性能优化
- 懒加载机制减少初始负载
- 按需加载仪表板资源
- 优化应用启动性能

### 4. 简洁实现
- 最小化的代码实现
- 清晰的依赖关系
- 易于维护和扩展

## 设计模式

### 1. 适配器模式 (Adapter Pattern)
- 适配电子表格的懒加载机制
- 为仪表板提供专门的接口

### 2. 代理模式 (Proxy Pattern)
- 作为仪表板操作的代理
- 延迟加载实际的操作实现

### 3. 单例模式 (Singleton Pattern)
- 确保操作只注册一次
- 避免重复注册问题

## 注意事项

1. **依赖管理**: 确保电子表格后端模块已正确加载
2. **操作命名**: 使用标准的操作命名约定
3. **性能监控**: 监控懒加载的性能影响
4. **错误处理**: 处理加载失败的情况

## 扩展建议

1. **多操作支持**: 注册更多的仪表板相关操作
2. **配置选项**: 添加可配置的加载选项
3. **性能监控**: 实现加载性能的监控
4. **缓存策略**: 实现智能的缓存策略
5. **预加载**: 实现智能的预加载机制

该仪表板操作加载器为电子表格仪表板系统提供了重要的性能优化功能，通过复用现有的懒加载机制实现了高效的资源管理。

/********************************************************************************
*  Filepath: /crm/static/src/views/forecast_kanban/forecast_kanban_renderer.js  *
*  Lines: 58                                                                    *
********************************************************************************/
odoo.define('@crm/views/forecast_kanban/forecast_kanban_renderer', ['@crm/views/crm_kanban/crm_kanban_renderer', '@web/core/utils/hooks', '@crm/views/forecast_kanban/forecast_kanban_column_quick_create'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CrmKanbanRenderer } = require("@crm/views/crm_kanban/crm_kanban_renderer");
const { useService } = require("@web/core/utils/hooks");
const { ForecastKanbanColumnQuickCreate } = require("@crm/views/forecast_kanban/forecast_kanban_column_quick_create");

const ForecastKanbanRenderer = __exports.ForecastKanbanRenderer = class ForecastKanbanRenderer extends CrmKanbanRenderer {
    static template = "crm.ForecastKanbanRenderer";
    static components = {
        ...CrmKanbanRenderer.components,
        ForecastKanbanColumnQuickCreate,
    };

    setup() {
        super.setup(...arguments);
        this.fillTemporalService = useService("fillTemporalService");
    }
    /**
     * @override
     *
     * Allow creating groups when grouping by forecast_field.
     */
    canCreateGroup() {
        return super.canCreateGroup(...arguments) || this.isGroupedByForecastField();
    }

    isGroupedByForecastField() {
        return (
            this.props.list.context.forecast_field &&
            this.props.list.groupByField.name === this.props.list.context.forecast_field
        );
    }

    isMovableField(field) {
        return super.isMovableField(...arguments) || field.name === "date_deadline";
    }

    async addForecastColumn() {
        const { name, type, granularity } = this.props.list.groupByField;
        this.fillTemporalService
            .getFillTemporalPeriod({
                modelName: this.props.list.resModel,
                field: {
                    name,
                    type,
                },
                granularity: granularity || "month",
            })
            .expand();
        await this.props.list.load();
    }
}

return __exports;
});
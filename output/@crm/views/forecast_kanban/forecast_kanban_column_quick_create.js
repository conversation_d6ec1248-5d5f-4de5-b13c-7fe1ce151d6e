/*******************************************************************************************
*  Filepath: /crm/static/src/views/forecast_kanban/forecast_kanban_column_quick_create.js  *
*  Lines: 30                                                                               *
*******************************************************************************************/
odoo.define('@crm/views/forecast_kanban/forecast_kanban_column_quick_create', ['@web/core/l10n/translation', '@web/search/utils/dates', '@web/views/kanban/kanban_column_quick_create'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { _t } = require("@web/core/l10n/translation");
const { INTERVAL_OPTIONS } = require("@web/search/utils/dates");
const { KanbanColumnQuickCreate } = require("@web/views/kanban/kanban_column_quick_create");

const ForecastKanbanColumnQuickCreate = __exports.ForecastKanbanColumnQuickCreate = class ForecastKanbanColumnQuickCreate extends KanbanColumnQuickCreate {
    /**
     * @override
     */
    get relatedFieldName() {
        const { granularity = "month" } = this.props.groupByField;
        const { description } = INTERVAL_OPTIONS[granularity];
        return _t("Add next %s", description.toLocaleLowerCase());
    }
    /**
     * @override
     *
     * Create column directly upon "unfolding" quick create.
     */
    unfold() {
        this.props.onValidate();
    }
}

return __exports;
});
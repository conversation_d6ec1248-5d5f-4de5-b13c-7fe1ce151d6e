/**********************************************************************************
*  Filepath: /crm/static/src/views/forecast_kanban/forecast_kanban_controller.js  *
*  Lines: 15                                                                      *
**********************************************************************************/
odoo.define('@crm/views/forecast_kanban/forecast_kanban_controller', ['@crm/views/crm_kanban/crm_kanban_view'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { crmKanbanView } = require("@crm/views/crm_kanban/crm_kanban_view");

const ForecastKanbanController = __exports.ForecastKanbanController = class ForecastKanbanController extends crmKanbanView.Controller {
    isQuickCreateField(field) {
        return super.isQuickCreateField(...arguments) || (field && field.name === "date_deadline");
    }
}

return __exports;
});
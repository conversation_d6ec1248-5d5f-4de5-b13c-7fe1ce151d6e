/****************************************************************************
*  Filepath: /crm/static/src/views/forecast_kanban/forecast_kanban_view.js  *
*  Lines: 26                                                                *
****************************************************************************/
odoo.define('@crm/views/forecast_kanban/forecast_kanban_view', ['@crm/views/forecast_kanban/forecast_kanban_controller', '@crm/views/crm_kanban/crm_kanban_arch_parser', '@crm/views/forecast_kanban/forecast_kanban_model', '@crm/views/forecast_kanban/forecast_kanban_renderer', '@crm/views/forecast_search_model', '@web/core/registry', '@web/views/kanban/kanban_view'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { ForecastKanbanController } = require("@crm/views/forecast_kanban/forecast_kanban_controller");
const { CrmKanbanArchParser } = require("@crm/views/crm_kanban/crm_kanban_arch_parser");
const { ForecastKanbanModel } = require("@crm/views/forecast_kanban/forecast_kanban_model");
const { ForecastKanbanRenderer } = require("@crm/views/forecast_kanban/forecast_kanban_renderer");
const { ForecastSearchModel } = require("@crm/views/forecast_search_model");
const { registry } = require("@web/core/registry");
const { kanbanView } = require("@web/views/kanban/kanban_view");

const forecastKanbanView = __exports.forecastKanbanView = {
    ...kanbanView,
    ArchParser: CrmKanbanArchParser,
    Model: ForecastKanbanModel,
    Controller: ForecastKanbanController,
    Renderer: ForecastKanbanRenderer,
    SearchModel: ForecastSearchModel,
};

registry.category("views").add("forecast_kanban", forecastKanbanView);

return __exports;
});
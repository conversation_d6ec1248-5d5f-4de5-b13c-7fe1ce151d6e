/****************************************************************
*  Filepath: /crm/static/src/views/check_rainbowman_message.js  *
*  Lines: 17                                                    *
****************************************************************/
odoo.define('@crm/views/check_rainbowman_message', [], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

__exports.checkRainbowmanMessage = checkRainbowmanMessage; async function checkRainbowmanMessage(orm, effect, recordId) {
    const message = await orm.call("crm.lead", "get_rainbowman_message", [[recordId]]);
    if (message) {
        effect.add({
            message,
            type: "rainbow_man",
        });
    }
}

return __exports;
});
/************************************************************************
*  Filepath: /crm/static/src/views/forecast_list/forecast_list_view.js  *
*  Lines: 18                                                            *
************************************************************************/
odoo.define('@crm/views/forecast_list/forecast_list_view', ['@web/core/registry', '@web/views/list/list_view', '@crm/views/forecast_search_model'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { listView } = require("@web/views/list/list_view");
const { ForecastSearchModel } = require("@crm/views/forecast_search_model");

const forecastListView = __exports.forecastListView = {
    ...listView,
    SearchModel: ForecastSearchModel,
};

registry.category("views").add("forecast_list", forecastListView);

return __exports;
});
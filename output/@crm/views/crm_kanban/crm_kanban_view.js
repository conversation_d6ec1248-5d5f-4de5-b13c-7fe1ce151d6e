/******************************************************************
*  Filepath: /crm/static/src/views/crm_kanban/crm_kanban_view.js  *
*  Lines: 33                                                      *
******************************************************************/
odoo.define('@crm/views/crm_kanban/crm_kanban_view', ['@web/core/registry', '@web/views/kanban/kanban_view', '@crm/views/crm_kanban/crm_kanban_model', '@crm/views/crm_kanban/crm_kanban_arch_parser', '@crm/views/crm_kanban/crm_kanban_renderer'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { registry } = require("@web/core/registry");
const { kanbanView } = require("@web/views/kanban/kanban_view");
const { CrmKanbanModel } = require("@crm/views/crm_kanban/crm_kanban_model");
const { CrmKanbanArchParser } = require("@crm/views/crm_kanban/crm_kanban_arch_parser");
const { CrmKanbanRenderer } = require("@crm/views/crm_kanban/crm_kanban_renderer");

const crmKanbanView = __exports.crmKanbanView = {
    ...kanbanView,
    ArchParser: CrmKanbanArchParser,
    // Makes it easier to patch
    Controller: class extends kanbanView.Controller {
        get progressBarAggregateFields() {
            const res = super.progressBarAggregateFields;
            const progressAttributes = this.props.archInfo.progressAttributes;
            if (progressAttributes && progressAttributes.recurring_revenue_sum_field) {
                res.push(progressAttributes.recurring_revenue_sum_field);
            }
            return res;
        }
    },
    Model: CrmKanbanModel,
    Renderer: CrmKanbanRenderer,
};

registry.category("views").add("crm_kanban", crmKanbanView);

return __exports;
});
/**********************************************************************
*  Filepath: /crm/static/src/views/crm_kanban/crm_kanban_renderer.js  *
*  Lines: 26                                                          *
**********************************************************************/
odoo.define('@crm/views/crm_kanban/crm_kanban_renderer', ['@crm/views/crm_kanban/crm_column_progress', '@web/views/kanban/kanban_renderer', '@web/views/kanban/kanban_header'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { CrmColumnProgress } = require("@crm/views/crm_kanban/crm_column_progress");
const { Kanban<PERSON>enderer } = require("@web/views/kanban/kanban_renderer");
const { KanbanHeader } = require("@web/views/kanban/kanban_header");

class CrmKanbanHeader extends KanbanHeader {
    static template = "crm.CrmKanbanHeader";
    static components = {
        ...KanbanHeader.components,
        ColumnProgress: CrmColumnProgress,
    };
}

const CrmKanbanRenderer = __exports.CrmKanbanRenderer = class CrmKanbanRenderer extends KanbanRenderer {
    static components = {
        ...KanbanRenderer.components,
        KanbanHeader: CrmKanbanHeader,
    };
}

return __exports;
});
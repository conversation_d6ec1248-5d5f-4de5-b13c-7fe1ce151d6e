/*************************************************************************
*  Filepath: /crm/static/src/views/crm_kanban/crm_kanban_arch_parser.js  *
*  Lines: 22                                                             *
*************************************************************************/
odoo.define('@crm/views/crm_kanban/crm_kanban_arch_parser', ['@web/views/kanban/kanban_arch_parser', '@web/core/utils/xml'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { KanbanArchParser } = require("@web/views/kanban/kanban_arch_parser");
const { extractAttributes } = require("@web/core/utils/xml");

const CrmKanbanArchParser = __exports.CrmKanbanArchParser = class CrmKanbanArchParser extends KanbanArchParser {
    /**
     * @override
     */
    parseProgressBar(progressBar, fields) {
        const result = super.parseProgressBar(...arguments);
        const attrs = extractAttributes(progressBar, ["recurring_revenue_sum_field"]);
        result.recurring_revenue_sum_field = fields[attrs.recurring_revenue_sum_field] || false;
        return result;
    }
}

return __exports;
});
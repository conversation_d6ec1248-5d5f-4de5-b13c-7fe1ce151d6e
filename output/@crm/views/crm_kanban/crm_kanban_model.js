/*******************************************************************
*  Filepath: /crm/static/src/views/crm_kanban/crm_kanban_model.js  *
*  Lines: 43                                                       *
*******************************************************************/
odoo.define('@crm/views/crm_kanban/crm_kanban_model', ['@crm/views/check_rainbowman_message', '@web/model/relational_model/relational_model'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const { checkRainbowmanMessage } = require("@crm/views/check_rainbowman_message");
const { RelationalModel } = require("@web/model/relational_model/relational_model");

const CrmKanbanModel = __exports.CrmKanbanModel = class CrmKanbanModel extends RelationalModel {
    setup(params, { effect }) {
        super.setup(...arguments);
        this.effect = effect;
    }
}

const CrmKanbanDynamicGroupList = __exports.CrmKanbanDynamicGroupList = class CrmKanbanDynamicGroupList extends RelationalModel.DynamicGroupList {
    /**
     * @override
     *
     * If the kanban view is grouped by stage_id check if the lead is won and display
     * a rainbowman message if that's the case.
     */
    async moveRecord(dataRecordId, dataGroupId, refId, targetGroupId) {
        await super.moveRecord(...arguments);
        const sourceGroup = this.groups.find((g) => g.id === dataGroupId);
        const targetGroup = this.groups.find((g) => g.id === targetGroupId);
        if (
            dataGroupId !== targetGroupId &&
            sourceGroup &&
            targetGroup &&
            sourceGroup.groupByField.name === "stage_id"
        ) {
            const record = targetGroup.list.records.find((r) => r.id === dataRecordId);
            await checkRainbowmanMessage(this.model.orm, this.model.effect, record.resId);
        }
    }
}

CrmKanbanModel.DynamicGroupList = CrmKanbanDynamicGroupList;
CrmKanbanModel.services = [...RelationalModel.services, "effect"];

return __exports;
});
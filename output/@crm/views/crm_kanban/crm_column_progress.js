/**********************************************************************
*  Filepath: /crm/static/src/views/crm_kanban/crm_column_progress.js  *
*  Lines: 41                                                          *
**********************************************************************/
odoo.define('@crm/views/crm_kanban/crm_column_progress', ['@odoo/owl', '@web/core/user', '@web/views/view_components/column_progress', '@web/session', '@web/core/currency'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { onWillStart } = require("@odoo/owl");
const { user } = require("@web/core/user");
const { ColumnProgress } = require("@web/views/view_components/column_progress");
const { session } = require("@web/session");
const { getCurrency } = require("@web/core/currency");

const CrmColumnProgress = __exports.CrmColumnProgress = class CrmColumnProgress extends ColumnProgress {
    static props = {
        ...ColumnProgress.props,
        progressBarState: { type: Object },
    };
    static template = "crm.ColumnProgress";
    setup() {
        super.setup();
        this.showRecurringRevenue = false;

        onWillStart(async () => {
            if (this.props.progressBarState.progressAttributes.recurring_revenue_sum_field) {
                this.showRecurringRevenue = await user.hasGroup("crm.group_use_recurring_revenues");
            }
        });
    }

    getRecurringRevenueGroupAggregate(group) {
        const rrField = this.props.progressBarState.progressAttributes.recurring_revenue_sum_field;
        const aggregatedValue = this.props.progressBarState.getAggregateValue(group, rrField);
        let currency = false;
        if (aggregatedValue.value && rrField.currency_field) {
            currency = getCurrency(session.company_currency_id);
        }
        return { ...aggregatedValue, currency };
    }
}

return __exports;
});
/**************************************************************************
*  Filepath: /base_import_module/static/src/base_import_list_renderer.js  *
*  Lines: 35                                                              *
**************************************************************************/
odoo.define('@base_import_module/base_import_list_renderer', ['@web/views/list/list_renderer'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */
const { ListRenderer } = require("@web/views/list/list_renderer");

const ImportModuleListRenderer = __exports.ImportModuleListRenderer = class ImportModule<PERSON><PERSON><PERSON><PERSON><PERSON> extends ListRenderer {

    get hasSelectors() {
        return super.hasSelectors && this.props.list.records.every(record => record._values.module_type != 'industries');
    }

    async onCellClicked(record, column, ev) {
        if (record._values.module_type && record._values.module_type !== 'official') {
            const re_action = {
                name: "more_info",
                res_model: "ir.module.module",
                res_id: -1,
                type: "ir.actions.act_window",
                views: [[false, "form"]],
                context: {
                    'module_name': record._values.name,
                    'module_type': record._values.module_type,
                }
            }
            this.env.services.action.doAction(re_action);
        }
        else{
            super.onCellClicked(record, column, ev);
        }
    }
}

return __exports;
});
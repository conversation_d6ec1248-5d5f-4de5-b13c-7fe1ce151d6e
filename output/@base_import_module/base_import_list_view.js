/**********************************************************************
*  Filepath: /base_import_module/static/src/base_import_list_view.js  *
*  Lines: 19                                                          *
**********************************************************************/
odoo.define('@base_import_module/base_import_list_view', ['@web/core/registry', '@web/views/list/list_view', '@base_import_module/base_import_list_renderer'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module */

const { registry } = require("@web/core/registry");
const { listView } = require("@web/views/list/list_view");
const { ImportModuleListRenderer } = require("@base_import_module/base_import_list_renderer");


const ImportModuleListView = __exports.ImportModuleListView = {
    ...listView,
    Renderer: ImportModuleListRenderer,
}

registry.category("views").add("ir_module_module_tree_view", ImportModuleListView);

return __exports;
});
# Base Import List View - 基础导入列表视图

## 概述

`base_import_list_view.js` 是 Odoo 基础导入模块的列表视图组件，专门用于配置和注册模块管理的列表视图。该组件基于Web框架的标准列表视图，集成了自定义的列表渲染器，为基础导入模块系统提供了完整的列表视图支持，是模块管理界面的核心视图组件。

## 文件信息
- **路径**: `/base_import_module/static/src/base_import_list_view.js`
- **行数**: 19
- **模块**: `@base_import_module/base_import_list_view`

## 依赖关系

```javascript
// 核心依赖
'@web/core/registry'                                    // 注册表
'@web/views/list/list_view'                            // Web列表视图
'@base_import_module/base_import_list_renderer'        // 基础导入列表渲染器
```

## 核心功能

### 1. ImportModuleListView 配置

```javascript
const ImportModuleListView = {
    ...listView,
    Renderer: ImportModuleListRenderer,
}
```

**视图配置功能**:
- **继承扩展**: 继承标准列表视图的所有配置
- **渲染器替换**: 使用自定义的模块列表渲染器
- **配置合并**: 保持原有配置的同时添加定制功能
- **组件组合**: 将视图和渲染器进行有机组合

### 2. 视图注册

```javascript
registry.category("views").add("ir_module_module_tree_view", ImportModuleListView);
```

**视图注册功能**:
- **注册表添加**: 将视图添加到视图注册表
- **唯一标识**: 使用"ir_module_module_tree_view"作为视图标识
- **全局可用**: 使视图在整个系统中可用
- **模块绑定**: 专门用于ir.module.module模型的树形视图

## 使用场景

### 1. 基础导入列表视图增强

```javascript
// 基础导入列表视图增强功能
const BaseImportListViewEnhancer = {
    enhanceBaseImportListView: () => {
        // 增强的基础导入列表视图
        const EnhancedImportModuleListView = {
            ...listView,
            Renderer: ImportModuleListRenderer,
            
            // 增强的配置选项
            enhancedConfig: {
                enableAdvancedFiltering: true,
                enableCustomColumns: true,
                enableBulkActions: true,
                enableExportImport: true,
                enableRealTimeUpdates: true,
                enableModuleAnalytics: true,
                enableModuleRecommendations: true,
                enableModuleCategories: true,
                enableModuleSearch: true,
                enableModuleSorting: true,
                enableModuleGrouping: true,
                enableModuleComparison: true
            },
            
            // 自定义控制器
            Controller: class EnhancedImportModuleListController extends listView.Controller {
                setup() {
                    super.setup();
                    
                    // 增强的状态管理
                    this.enhancedState = useState({
                        filterPanelVisible: false,
                        bulkActionMode: false,
                        selectedModules: new Set(),
                        customColumns: [],
                        sortPreferences: {},
                        groupPreferences: {},
                        searchHistory: [],
                        moduleAnalytics: {},
                        recommendations: [],
                        comparisonList: []
                    });
                    
                    // 模块分析器
                    this.moduleAnalyzer = new ModuleAnalyzer();
                    
                    // 推荐引擎
                    this.recommendationEngine = new RecommendationEngine();
                    
                    // 批量操作管理器
                    this.bulkActionManager = new BulkActionManager();
                    
                    // 实时更新管理器
                    this.realTimeUpdateManager = new RealTimeUpdateManager();
                    
                    // 初始化增强功能
                    this.initializeEnhancements();
                }
                
                // 初始化增强功能
                initializeEnhancements() {
                    // 设置实时更新
                    if (this.enhancedConfig.enableRealTimeUpdates) {
                        this.setupRealTimeUpdates();
                    }
                    
                    // 加载模块分析
                    if (this.enhancedConfig.enableModuleAnalytics) {
                        this.loadModuleAnalytics();
                    }
                    
                    // 生成推荐
                    if (this.enhancedConfig.enableModuleRecommendations) {
                        this.generateRecommendations();
                    }
                    
                    // 加载自定义列
                    if (this.enhancedConfig.enableCustomColumns) {
                        this.loadCustomColumns();
                    }
                }
                
                // 设置实时更新
                setupRealTimeUpdates() {
                    this.realTimeUpdateManager.onModuleUpdate((moduleData) => {
                        this.updateModuleInList(moduleData);
                    });
                    
                    this.realTimeUpdateManager.onModuleInstall((moduleName) => {
                        this.refreshModuleStatus(moduleName);
                    });
                    
                    this.realTimeUpdateManager.onModuleUninstall((moduleName) => {
                        this.refreshModuleStatus(moduleName);
                    });
                }
                
                // 加载模块分析
                async loadModuleAnalytics() {
                    try {
                        const analytics = await this.moduleAnalyzer.analyzeModules(
                            this.model.root.records
                        );
                        this.enhancedState.moduleAnalytics = analytics;
                    } catch (error) {
                        console.error('加载模块分析失败:', error);
                    }
                }
                
                // 生成推荐
                async generateRecommendations() {
                    try {
                        const recommendations = await this.recommendationEngine.generateRecommendations(
                            this.model.root.records,
                            this.enhancedState.moduleAnalytics
                        );
                        this.enhancedState.recommendations = recommendations;
                    } catch (error) {
                        console.error('生成推荐失败:', error);
                    }
                }
                
                // 切换过滤面板
                toggleFilterPanel() {
                    this.enhancedState.filterPanelVisible = !this.enhancedState.filterPanelVisible;
                }
                
                // 切换批量操作模式
                toggleBulkActionMode() {
                    this.enhancedState.bulkActionMode = !this.enhancedState.bulkActionMode;
                    if (!this.enhancedState.bulkActionMode) {
                        this.enhancedState.selectedModules.clear();
                    }
                }
                
                // 选择模块
                selectModule(moduleName, selected) {
                    if (selected) {
                        this.enhancedState.selectedModules.add(moduleName);
                    } else {
                        this.enhancedState.selectedModules.delete(moduleName);
                    }
                }
                
                // 全选模块
                selectAllModules() {
                    const allModules = this.model.root.records.map(record => record._values.name);
                    allModules.forEach(moduleName => {
                        this.enhancedState.selectedModules.add(moduleName);
                    });
                }
                
                // 取消全选
                deselectAllModules() {
                    this.enhancedState.selectedModules.clear();
                }
                
                // 执行批量操作
                async executeBulkAction(action) {
                    if (!this.enhancedConfig.enableBulkActions) return;
                    
                    const selectedModules = [...this.enhancedState.selectedModules];
                    if (selectedModules.length === 0) {
                        this.showNotification('请先选择要操作的模块', 'warning');
                        return;
                    }
                    
                    try {
                        await this.bulkActionManager.execute(action, selectedModules);
                        this.showNotification(`批量${action}操作完成`, 'success');
                        this.refreshView();
                    } catch (error) {
                        console.error('批量操作失败:', error);
                        this.showNotification('批量操作失败', 'error');
                    }
                }
                
                // 添加自定义列
                addCustomColumn(columnConfig) {
                    if (!this.enhancedConfig.enableCustomColumns) return;
                    
                    this.enhancedState.customColumns.push(columnConfig);
                    this.saveCustomColumns();
                    this.refreshView();
                }
                
                // 移除自定义列
                removeCustomColumn(columnId) {
                    this.enhancedState.customColumns = this.enhancedState.customColumns.filter(
                        col => col.id !== columnId
                    );
                    this.saveCustomColumns();
                    this.refreshView();
                }
                
                // 导出模块列表
                async exportModuleList(format = 'csv') {
                    if (!this.enhancedConfig.enableExportImport) return;
                    
                    try {
                        const data = this.prepareExportData();
                        const blob = this.formatExportData(data, format);
                        this.downloadFile(blob, `modules.${format}`);
                        this.showNotification('模块列表已导出', 'success');
                    } catch (error) {
                        console.error('导出失败:', error);
                        this.showNotification('导出失败', 'error');
                    }
                }
                
                // 导入模块配置
                async importModuleConfig(file) {
                    if (!this.enhancedConfig.enableExportImport) return;
                    
                    try {
                        const config = await this.parseImportFile(file);
                        await this.applyModuleConfig(config);
                        this.showNotification('模块配置已导入', 'success');
                        this.refreshView();
                    } catch (error) {
                        console.error('导入失败:', error);
                        this.showNotification('导入失败', 'error');
                    }
                }
                
                // 搜索模块
                searchModules(searchTerm) {
                    if (!this.enhancedConfig.enableModuleSearch) return;
                    
                    // 添加到搜索历史
                    if (searchTerm && !this.enhancedState.searchHistory.includes(searchTerm)) {
                        this.enhancedState.searchHistory.unshift(searchTerm);
                        if (this.enhancedState.searchHistory.length > 10) {
                            this.enhancedState.searchHistory = this.enhancedState.searchHistory.slice(0, 10);
                        }
                    }
                    
                    // 执行搜索
                    this.model.load({ domain: this.buildSearchDomain(searchTerm) });
                }
                
                // 构建搜索域
                buildSearchDomain(searchTerm) {
                    if (!searchTerm) return [];
                    
                    return [
                        '|', '|', '|',
                        ['name', 'ilike', searchTerm],
                        ['summary', 'ilike', searchTerm],
                        ['description', 'ilike', searchTerm],
                        ['author', 'ilike', searchTerm]
                    ];
                }
                
                // 应用高级过滤
                applyAdvancedFilter(filterConfig) {
                    if (!this.enhancedConfig.enableAdvancedFiltering) return;
                    
                    const domain = this.buildFilterDomain(filterConfig);
                    this.model.load({ domain });
                }
                
                // 构建过滤域
                buildFilterDomain(filterConfig) {
                    const domain = [];
                    
                    if (filterConfig.category) {
                        domain.push(['category_id', '=', filterConfig.category]);
                    }
                    
                    if (filterConfig.state) {
                        domain.push(['state', '=', filterConfig.state]);
                    }
                    
                    if (filterConfig.author) {
                        domain.push(['author', 'ilike', filterConfig.author]);
                    }
                    
                    if (filterConfig.installed !== undefined) {
                        domain.push(['state', filterConfig.installed ? 'in' : 'not in', ['installed', 'to upgrade']]);
                    }
                    
                    return domain;
                }
                
                // 更新模块状态
                updateModuleInList(moduleData) {
                    const record = this.model.root.records.find(
                        record => record._values.name === moduleData.name
                    );
                    
                    if (record) {
                        Object.assign(record._values, moduleData);
                        this.render();
                    }
                }
                
                // 刷新模块状态
                async refreshModuleStatus(moduleName) {
                    try {
                        const moduleData = await this.orm.call(
                            'ir.module.module',
                            'read',
                            [[moduleName]],
                            { fields: ['name', 'state', 'latest_version'] }
                        );
                        
                        if (moduleData.length > 0) {
                            this.updateModuleInList(moduleData[0]);
                        }
                    } catch (error) {
                        console.error('刷新模块状态失败:', error);
                    }
                }
                
                // 工具方法
                prepareExportData() {
                    return this.model.root.records.map(record => ({
                        name: record._values.name,
                        summary: record._values.summary,
                        state: record._values.state,
                        author: record._values.author,
                        version: record._values.latest_version,
                        category: record._values.category_id?.[1] || ''
                    }));
                }
                
                formatExportData(data, format) {
                    if (format === 'csv') {
                        const headers = ['Name', 'Summary', 'State', 'Author', 'Version', 'Category'];
                        const csvContent = [
                            headers.join(','),
                            ...data.map(row => [
                                row.name, row.summary, row.state, 
                                row.author, row.version, row.category
                            ].map(field => `"${field}"`).join(','))
                        ].join('\n');
                        
                        return new Blob([csvContent], { type: 'text/csv' });
                    } else if (format === 'json') {
                        return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                    }
                }
                
                downloadFile(blob, filename) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    URL.revokeObjectURL(url);
                }
                
                async parseImportFile(file) {
                    const text = await file.text();
                    if (file.name.endsWith('.json')) {
                        return JSON.parse(text);
                    } else if (file.name.endsWith('.csv')) {
                        return this.parseCSV(text);
                    }
                    throw new Error('不支持的文件格式');
                }
                
                parseCSV(csvText) {
                    const lines = csvText.split('\n');
                    const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));
                    const data = [];
                    
                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = lines[i].split(',').map(v => v.replace(/"/g, ''));
                            const row = {};
                            headers.forEach((header, index) => {
                                row[header.toLowerCase()] = values[index] || '';
                            });
                            data.push(row);
                        }
                    }
                    
                    return data;
                }
                
                async applyModuleConfig(config) {
                    // 应用导入的模块配置
                    for (const moduleConfig of config) {
                        if (moduleConfig.action === 'install') {
                            await this.installModule(moduleConfig.name);
                        } else if (moduleConfig.action === 'uninstall') {
                            await this.uninstallModule(moduleConfig.name);
                        }
                    }
                }
                
                loadCustomColumns() {
                    try {
                        const stored = localStorage.getItem('module_custom_columns');
                        if (stored) {
                            this.enhancedState.customColumns = JSON.parse(stored);
                        }
                    } catch (error) {
                        console.error('加载自定义列失败:', error);
                    }
                }
                
                saveCustomColumns() {
                    try {
                        localStorage.setItem('module_custom_columns', 
                            JSON.stringify(this.enhancedState.customColumns));
                    } catch (error) {
                        console.error('保存自定义列失败:', error);
                    }
                }
                
                refreshView() {
                    this.model.load();
                }
                
                showNotification(message, type) {
                    console.log(`[${type}] ${message}`);
                }
            },
            
            // 自定义搜索面板
            SearchPanel: class EnhancedSearchPanel extends listView.SearchPanel {
                // 增强的搜索面板功能
            },
            
            // 自定义控制面板
            ControlPanel: class EnhancedControlPanel extends listView.ControlPanel {
                // 增强的控制面板功能
            }
        };
        
        // 模块分析器
        class ModuleAnalyzer {
            async analyzeModules(records) {
                const analytics = {
                    totalModules: records.length,
                    installedModules: 0,
                    availableModules: 0,
                    categoryDistribution: {},
                    authorDistribution: {},
                    stateDistribution: {}
                };
                
                for (const record of records) {
                    const values = record._values;
                    
                    // 状态分布
                    analytics.stateDistribution[values.state] = 
                        (analytics.stateDistribution[values.state] || 0) + 1;
                    
                    if (values.state === 'installed') {
                        analytics.installedModules++;
                    } else {
                        analytics.availableModules++;
                    }
                    
                    // 分类分布
                    const category = values.category_id?.[1] || 'Other';
                    analytics.categoryDistribution[category] = 
                        (analytics.categoryDistribution[category] || 0) + 1;
                    
                    // 作者分布
                    const author = values.author || 'Unknown';
                    analytics.authorDistribution[author] = 
                        (analytics.authorDistribution[author] || 0) + 1;
                }
                
                return analytics;
            }
        }
        
        // 推荐引擎
        class RecommendationEngine {
            async generateRecommendations(records, analytics) {
                const recommendations = [];
                
                // 基于已安装模块推荐相关模块
                const installedModules = records.filter(
                    record => record._values.state === 'installed'
                );
                
                for (const module of installedModules) {
                    const related = this.findRelatedModules(module, records);
                    recommendations.push(...related);
                }
                
                // 去重并排序
                return [...new Set(recommendations)].slice(0, 10);
            }
            
            findRelatedModules(module, allRecords) {
                // 简单的相关模块查找逻辑
                const category = module._values.category_id?.[1];
                const author = module._values.author;
                
                return allRecords
                    .filter(record => 
                        record._values.state !== 'installed' &&
                        (record._values.category_id?.[1] === category ||
                         record._values.author === author)
                    )
                    .map(record => record._values.name);
            }
        }
        
        // 批量操作管理器
        class BulkActionManager {
            async execute(action, moduleNames) {
                switch (action) {
                    case 'install':
                        return this.bulkInstall(moduleNames);
                    case 'uninstall':
                        return this.bulkUninstall(moduleNames);
                    case 'upgrade':
                        return this.bulkUpgrade(moduleNames);
                    default:
                        throw new Error(`不支持的批量操作: ${action}`);
                }
            }
            
            async bulkInstall(moduleNames) {
                // 实现批量安装逻辑
                console.log('批量安装模块:', moduleNames);
            }
            
            async bulkUninstall(moduleNames) {
                // 实现批量卸载逻辑
                console.log('批量卸载模块:', moduleNames);
            }
            
            async bulkUpgrade(moduleNames) {
                // 实现批量升级逻辑
                console.log('批量升级模块:', moduleNames);
            }
        }
        
        // 实时更新管理器
        class RealTimeUpdateManager {
            constructor() {
                this.listeners = {
                    moduleUpdate: [],
                    moduleInstall: [],
                    moduleUninstall: []
                };
            }
            
            onModuleUpdate(callback) {
                this.listeners.moduleUpdate.push(callback);
            }
            
            onModuleInstall(callback) {
                this.listeners.moduleInstall.push(callback);
            }
            
            onModuleUninstall(callback) {
                this.listeners.moduleUninstall.push(callback);
            }
            
            notifyModuleUpdate(moduleData) {
                this.listeners.moduleUpdate.forEach(callback => callback(moduleData));
            }
            
            notifyModuleInstall(moduleName) {
                this.listeners.moduleInstall.forEach(callback => callback(moduleName));
            }
            
            notifyModuleUninstall(moduleName) {
                this.listeners.moduleUninstall.forEach(callback => callback(moduleName));
            }
        }
        
        // 注册增强的视图
        registry.category("views").add("enhanced_ir_module_module_tree_view", EnhancedImportModuleListView);
        
        // 导出增强的视图
        __exports.EnhancedImportModuleListView = EnhancedImportModuleListView;
    }
};

// 应用基础导入列表视图增强
BaseImportListViewEnhancer.enhanceBaseImportListView();
```

## 技术特点

### 1. 视图配置
- 基于标准列表视图的配置
- 自定义渲染器的集成
- 简洁的配置结构

### 2. 组件组合
- 视图和渲染器的有机结合
- 保持框架的一致性
- 模块化的组件设计

### 3. 注册机制
- 标准的视图注册流程
- 全局的视图可用性
- 清晰的视图标识

### 4. 扩展性
- 易于扩展和定制
- 保持与框架的兼容性
- 支持功能的增量添加

## 设计模式

### 1. 组合模式 (Composite Pattern)
- 视图和渲染器的组合
- 功能的模块化组合

### 2. 注册表模式 (Registry Pattern)
- 视图的注册和管理
- 全局可访问的视图系统

### 3. 配置模式 (Configuration Pattern)
- 通过配置定制视图行为
- 灵活的视图定制选项

## 注意事项

1. **视图标识**: 确保视图标识的唯一性和准确性
2. **组件兼容**: 保持与Web框架的兼容性
3. **性能优化**: 优化视图加载和渲染性能
4. **扩展安全**: 确保扩展不影响核心功能

## 扩展建议

1. **自定义控制器**: 添加自定义的视图控制器
2. **高级过滤**: 实现更复杂的过滤和搜索功能
3. **批量操作**: 支持模块的批量管理操作
4. **实时更新**: 实现模块状态的实时更新
5. **数据导出**: 支持模块列表的导出功能

该基础导入列表视图为模块管理系统提供了重要的视图配置和注册功能，是模块管理界面的核心视图组件。

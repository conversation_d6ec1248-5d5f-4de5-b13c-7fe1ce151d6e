# IAP Notification Service - IAP通知服务

## 概述

`iap_notification_service.js` 是 Odoo IAP Mail 模块的通知服务组件，专门用于处理IAP相关的通知消息。该服务基于Odoo的总线服务和通知系统，集成了消息订阅、通知显示、信用错误处理等核心功能，为IAP Mail系统提供了完整的通知管理支持，是IAP邮件服务通知系统的核心组件。

## 文件信息
- **路径**: `/iap_mail/static/src/js/services/iap_notification_service.js`
- **行数**: 51
- **模块**: `@iap_mail/js/services/iap_notification_service`

## 依赖关系

```javascript
// 核心依赖
'@web/core/l10n/translation'    // 国际化翻译
'@web/core/registry'            // 注册表
'@odoo/owl'                     // OWL框架
```

## 核心功能

### 1. IAP通知服务配置

```javascript
const iapNotificationService = {
    dependencies: ["bus_service", "notification"],
    start(env, { bus_service, notification }) {
        // 服务启动逻辑
    }
};
```

**服务配置功能**:
- **依赖声明**: 声明对总线服务和通知服务的依赖
- **服务启动**: 定义服务的启动和初始化逻辑
- **环境集成**: 集成Odoo环境和相关服务
- **标准结构**: 遵循Odoo服务的标准结构

### 2. 消息订阅

```javascript
bus_service.subscribe("iap_notification", (params) => {
    if (params.type == "no_credit") {
        displayCreditErrorNotification(params);
    } else {
        displayNotification(params);
    }
});
bus_service.start();
```

**消息订阅功能**:
- **总线订阅**: 订阅"iap_notification"频道的消息
- **类型判断**: 根据消息类型执行不同的处理逻辑
- **信用错误**: 特殊处理"no_credit"类型的消息
- **通用通知**: 处理其他类型的通知消息
- **服务启动**: 启动总线服务开始监听

### 3. 通用通知显示

```javascript
function displayNotification(params) {
    notification.add(params.message, {
        title: params.title,
        type: params.type,
    });
}
```

**通用通知功能**:
- **消息显示**: 显示通用的通知消息
- **参数传递**: 传递消息内容、标题和类型
- **类型支持**: 支持多种通知类型（info、warning、success等）
- **标准接口**: 使用标准的通知服务接口

### 4. 信用错误通知

```javascript
function displayCreditErrorNotification(params) {
    const translatedText = _t("Buy more credits");
    const message = markup(`
    <a class='btn btn-link' href='${params.get_credits_url}' target='_blank'>
        <i class='oi oi-arrow-right'></i>
        ${translatedText}
    </a>`);
    notification.add(message, {
        title: params.title,
        type: 'danger',
    });
}
```

**信用错误通知功能**:
- **HTML标记**: 使用markup创建富文本通知内容
- **购买链接**: 提供购买信用的直接链接
- **国际化**: 使用翻译函数支持多语言
- **图标显示**: 包含箭头图标的视觉提示
- **新窗口**: 在新标签页中打开购买页面
- **危险类型**: 使用danger类型突出显示错误

### 5. 服务注册

```javascript
registry.category("services").add("iapNotification", iapNotificationService);
```

**服务注册功能**:
- **注册表添加**: 将服务添加到服务注册表
- **服务标识**: 使用"iapNotification"作为服务标识
- **全局可用**: 使服务在整个系统中可用
- **依赖注入**: 支持依赖注入和服务发现

## 使用场景

### 1. IAP通知服务增强

```javascript
// IAP通知服务增强功能
const IAPNotificationServiceEnhancer = {
    enhanceIAPNotificationService: () => {
        // 增强的IAP通知服务
        const enhancedIapNotificationService = {
            dependencies: ["bus_service", "notification", "orm", "user"],
            
            start(env, { bus_service, notification, orm, user }) {
                // 增强的配置选项
                const enhancedConfig = {
                    enableAdvancedNotifications: true,
                    enableNotificationHistory: true,
                    enableUserPreferences: true,
                    enableNotificationAnalytics: true,
                    enableCustomTemplates: true,
                    enableNotificationQueue: true,
                    enableRealTimeUpdates: true,
                    enableNotificationFiltering: true,
                    maxHistoryItems: 100,
                    notificationTimeout: 5000,
                    retryAttempts: 3,
                    enableSound: true,
                    enableDesktopNotifications: true
                };
                
                // 增强的状态管理
                const enhancedState = {
                    notificationHistory: [],
                    userPreferences: {},
                    notificationQueue: [],
                    analytics: {},
                    customTemplates: new Map(),
                    activeNotifications: new Set(),
                    filters: {},
                    isProcessing: false
                };
                
                // 通知历史管理器
                const notificationHistoryManager = new NotificationHistoryManager();
                
                // 用户偏好管理器
                const userPreferencesManager = new UserPreferencesManager();
                
                // 通知分析器
                const notificationAnalyzer = new NotificationAnalyzer();
                
                // 模板管理器
                const templateManager = new NotificationTemplateManager();
                
                // 队列管理器
                const queueManager = new NotificationQueueManager();
                
                // 初始化增强功能
                initializeEnhancements();
                
                // 订阅IAP通知
                bus_service.subscribe("iap_notification", async (params) => {
                    try {
                        // 记录通知
                        if (enhancedConfig.enableNotificationHistory) {
                            notificationHistoryManager.add(params);
                        }
                        
                        // 分析通知
                        if (enhancedConfig.enableNotificationAnalytics) {
                            notificationAnalyzer.analyze(params);
                        }
                        
                        // 应用过滤器
                        if (enhancedConfig.enableNotificationFiltering) {
                            if (!passesFilters(params)) {
                                return;
                            }
                        }
                        
                        // 检查用户偏好
                        if (enhancedConfig.enableUserPreferences) {
                            const preferences = await userPreferencesManager.getPreferences(user.userId);
                            if (!shouldShowNotification(params, preferences)) {
                                return;
                            }
                        }
                        
                        // 处理不同类型的通知
                        if (params.type === "no_credit") {
                            await displayEnhancedCreditErrorNotification(params);
                        } else {
                            await displayEnhancedNotification(params);
                        }
                        
                    } catch (error) {
                        console.error('处理IAP通知失败:', error);
                        // 降级到基础通知
                        displayBasicNotification(params);
                    }
                });
                
                // 订阅其他相关通知
                bus_service.subscribe("iap_credit_update", (params) => {
                    handleCreditUpdate(params);
                });
                
                bus_service.subscribe("iap_service_status", (params) => {
                    handleServiceStatusUpdate(params);
                });
                
                bus_service.start();
                
                // 初始化增强功能
                async function initializeEnhancements() {
                    try {
                        // 加载用户偏好
                        if (enhancedConfig.enableUserPreferences) {
                            enhancedState.userPreferences = await userPreferencesManager.load(user.userId);
                        }
                        
                        // 加载自定义模板
                        if (enhancedConfig.enableCustomTemplates) {
                            const templates = await templateManager.loadTemplates();
                            enhancedState.customTemplates = templates;
                        }
                        
                        // 加载通知历史
                        if (enhancedConfig.enableNotificationHistory) {
                            enhancedState.notificationHistory = await notificationHistoryManager.load();
                        }
                        
                        // 设置桌面通知权限
                        if (enhancedConfig.enableDesktopNotifications) {
                            await requestDesktopNotificationPermission();
                        }
                        
                    } catch (error) {
                        console.error('初始化增强功能失败:', error);
                    }
                }
                
                // 增强的通用通知显示
                async function displayEnhancedNotification(params) {
                    try {
                        // 检查是否使用自定义模板
                        let message = params.message;
                        if (enhancedConfig.enableCustomTemplates && params.template) {
                            const template = enhancedState.customTemplates.get(params.template);
                            if (template) {
                                message = templateManager.render(template, params);
                            }
                        }
                        
                        // 添加到队列或直接显示
                        if (enhancedConfig.enableNotificationQueue) {
                            queueManager.add({
                                message: message,
                                title: params.title,
                                type: params.type,
                                priority: params.priority || 'normal',
                                timestamp: Date.now()
                            });
                        } else {
                            showNotification(message, params);
                        }
                        
                        // 播放声音
                        if (enhancedConfig.enableSound && enhancedState.userPreferences.enableSound !== false) {
                            playNotificationSound(params.type);
                        }
                        
                        // 显示桌面通知
                        if (enhancedConfig.enableDesktopNotifications && enhancedState.userPreferences.enableDesktop !== false) {
                            showDesktopNotification(params);
                        }
                        
                    } catch (error) {
                        console.error('显示增强通知失败:', error);
                        displayBasicNotification(params);
                    }
                }
                
                // 增强的信用错误通知
                async function displayEnhancedCreditErrorNotification(params) {
                    try {
                        // 获取用户的信用历史
                        const creditHistory = await orm.call(
                            'iap.account',
                            'get_credit_history',
                            [params.service_name]
                        );
                        
                        // 生成个性化建议
                        const suggestions = generateCreditSuggestions(creditHistory, params);
                        
                        // 创建增强的消息内容
                        const translatedText = _t("Buy more credits");
                        const viewHistoryText = _t("View usage history");
                        const optimizeText = _t("Optimize usage");
                        
                        const enhancedMessage = markup(`
                            <div class="iap-credit-error-notification">
                                <div class="alert alert-warning mb-2">
                                    <strong>${params.title}</strong><br>
                                    ${params.message}
                                </div>
                                <div class="btn-group" role="group">
                                    <a class='btn btn-primary btn-sm' href='${params.get_credits_url}' target='_blank'>
                                        <i class='fa fa-credit-card'></i>
                                        ${translatedText}
                                    </a>
                                    <button class='btn btn-info btn-sm' onclick='showCreditHistory("${params.service_name}")'>
                                        <i class='fa fa-history'></i>
                                        ${viewHistoryText}
                                    </button>
                                    <button class='btn btn-success btn-sm' onclick='showOptimizationTips("${params.service_name}")'>
                                        <i class='fa fa-lightbulb-o'></i>
                                        ${optimizeText}
                                    </button>
                                </div>
                                ${suggestions.length > 0 ? `
                                    <div class="mt-2">
                                        <small class="text-muted">
                                            <strong>建议:</strong> ${suggestions.join(', ')}
                                        </small>
                                    </div>
                                ` : ''}
                            </div>
                        `);
                        
                        notification.add(enhancedMessage, {
                            title: params.title,
                            type: 'danger',
                            sticky: true, // 保持显示直到用户关闭
                            className: 'iap-credit-error'
                        });
                        
                        // 记录信用错误事件
                        notificationAnalyzer.recordCreditError(params);
                        
                    } catch (error) {
                        console.error('显示增强信用错误通知失败:', error);
                        // 降级到基础信用错误通知
                        displayBasicCreditErrorNotification(params);
                    }
                }
                
                // 处理信用更新
                function handleCreditUpdate(params) {
                    if (params.service_name && params.new_balance !== undefined) {
                        const message = _t("Credit balance updated: %s credits", params.new_balance);
                        notification.add(message, {
                            title: _t("Credit Update"),
                            type: 'info'
                        });
                    }
                }
                
                // 处理服务状态更新
                function handleServiceStatusUpdate(params) {
                    const statusMessages = {
                        'active': _t("Service is now active"),
                        'inactive': _t("Service is temporarily unavailable"),
                        'maintenance': _t("Service is under maintenance"),
                        'error': _t("Service encountered an error")
                    };
                    
                    const message = statusMessages[params.status] || _t("Service status changed");
                    const type = params.status === 'active' ? 'success' : 
                                params.status === 'error' ? 'danger' : 'warning';
                    
                    notification.add(message, {
                        title: _t("Service Status: %s", params.service_name),
                        type: type
                    });
                }
                
                // 生成信用建议
                function generateCreditSuggestions(creditHistory, params) {
                    const suggestions = [];
                    
                    if (creditHistory.usage_trend === 'increasing') {
                        suggestions.push(_t("Consider purchasing a larger credit package"));
                    }
                    
                    if (creditHistory.peak_usage_hours) {
                        suggestions.push(_t("Schedule operations during off-peak hours"));
                    }
                    
                    if (creditHistory.unused_features) {
                        suggestions.push(_t("Optimize by disabling unused features"));
                    }
                    
                    return suggestions;
                }
                
                // 检查通知是否通过过滤器
                function passesFilters(params) {
                    const filters = enhancedState.filters;
                    
                    // 类型过滤
                    if (filters.types && !filters.types.includes(params.type)) {
                        return false;
                    }
                    
                    // 服务过滤
                    if (filters.services && !filters.services.includes(params.service_name)) {
                        return false;
                    }
                    
                    // 优先级过滤
                    if (filters.minPriority && params.priority < filters.minPriority) {
                        return false;
                    }
                    
                    return true;
                }
                
                // 检查是否应该显示通知
                function shouldShowNotification(params, preferences) {
                    // 检查全局通知开关
                    if (preferences.enableNotifications === false) {
                        return false;
                    }
                    
                    // 检查特定类型的通知开关
                    if (preferences.notificationTypes && 
                        preferences.notificationTypes[params.type] === false) {
                        return false;
                    }
                    
                    // 检查免打扰时间
                    if (preferences.doNotDisturbHours) {
                        const now = new Date();
                        const currentHour = now.getHours();
                        const { start, end } = preferences.doNotDisturbHours;
                        
                        if (start <= end) {
                            if (currentHour >= start && currentHour < end) {
                                return false;
                            }
                        } else {
                            if (currentHour >= start || currentHour < end) {
                                return false;
                            }
                        }
                    }
                    
                    return true;
                }
                
                // 显示基础通知
                function displayBasicNotification(params) {
                    notification.add(params.message, {
                        title: params.title,
                        type: params.type,
                    });
                }
                
                // 显示基础信用错误通知
                function displayBasicCreditErrorNotification(params) {
                    const translatedText = _t("Buy more credits");
                    const message = markup(`
                    <a class='btn btn-link' href='${params.get_credits_url}' target='_blank'>
                        <i class='oi oi-arrow-right'></i>
                        ${translatedText}
                    </a>`);
                    notification.add(message, {
                        title: params.title,
                        type: 'danger',
                    });
                }
                
                // 显示通知
                function showNotification(message, params) {
                    const notificationId = notification.add(message, {
                        title: params.title,
                        type: params.type,
                        sticky: params.sticky,
                        className: params.className
                    });
                    
                    enhancedState.activeNotifications.add(notificationId);
                    
                    // 设置自动关闭
                    if (!params.sticky && enhancedConfig.notificationTimeout > 0) {
                        setTimeout(() => {
                            notification.close(notificationId);
                            enhancedState.activeNotifications.delete(notificationId);
                        }, enhancedConfig.notificationTimeout);
                    }
                }
                
                // 播放通知声音
                function playNotificationSound(type) {
                    try {
                        const soundMap = {
                            'success': '/iap_mail/static/src/sounds/success.mp3',
                            'warning': '/iap_mail/static/src/sounds/warning.mp3',
                            'danger': '/iap_mail/static/src/sounds/error.mp3',
                            'info': '/iap_mail/static/src/sounds/info.mp3'
                        };
                        
                        const soundUrl = soundMap[type] || soundMap['info'];
                        const audio = new Audio(soundUrl);
                        audio.volume = enhancedState.userPreferences.soundVolume || 0.5;
                        audio.play().catch(error => {
                            console.warn('播放通知声音失败:', error);
                        });
                    } catch (error) {
                        console.warn('播放通知声音失败:', error);
                    }
                }
                
                // 显示桌面通知
                function showDesktopNotification(params) {
                    if ('Notification' in window && Notification.permission === 'granted') {
                        try {
                            new Notification(params.title || 'IAP Notification', {
                                body: params.message,
                                icon: '/iap_mail/static/src/img/notification-icon.png',
                                tag: `iap-${params.service_name || 'general'}`,
                                requireInteraction: params.type === 'danger'
                            });
                        } catch (error) {
                            console.warn('显示桌面通知失败:', error);
                        }
                    }
                }
                
                // 请求桌面通知权限
                async function requestDesktopNotificationPermission() {
                    if ('Notification' in window && Notification.permission === 'default') {
                        try {
                            await Notification.requestPermission();
                        } catch (error) {
                            console.warn('请求桌面通知权限失败:', error);
                        }
                    }
                }
                
                // 暴露公共API
                return {
                    // 手动显示通知
                    showNotification: displayEnhancedNotification,
                    
                    // 获取通知历史
                    getNotificationHistory: () => enhancedState.notificationHistory,
                    
                    // 设置用户偏好
                    setUserPreferences: (preferences) => {
                        userPreferencesManager.save(user.userId, preferences);
                        enhancedState.userPreferences = preferences;
                    },
                    
                    // 获取分析数据
                    getAnalytics: () => notificationAnalyzer.getAnalytics(),
                    
                    // 设置过滤器
                    setFilters: (filters) => {
                        enhancedState.filters = filters;
                    },
                    
                    // 清除所有通知
                    clearAllNotifications: () => {
                        for (const notificationId of enhancedState.activeNotifications) {
                            notification.close(notificationId);
                        }
                        enhancedState.activeNotifications.clear();
                    }
                };
            }
        };
        
        // 通知历史管理器
        class NotificationHistoryManager {
            constructor() {
                this.history = [];
                this.maxItems = 100;
            }
            
            add(notification) {
                this.history.unshift({
                    ...notification,
                    timestamp: Date.now(),
                    id: this.generateId()
                });
                
                if (this.history.length > this.maxItems) {
                    this.history = this.history.slice(0, this.maxItems);
                }
                
                this.save();
            }
            
            load() {
                try {
                    const stored = localStorage.getItem('iap_notification_history');
                    if (stored) {
                        this.history = JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载通知历史失败:', error);
                }
                return this.history;
            }
            
            save() {
                try {
                    localStorage.setItem('iap_notification_history', JSON.stringify(this.history));
                } catch (error) {
                    console.error('保存通知历史失败:', error);
                }
            }
            
            generateId() {
                return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            }
        }
        
        // 用户偏好管理器
        class UserPreferencesManager {
            async load(userId) {
                try {
                    const stored = localStorage.getItem(`iap_notification_preferences_${userId}`);
                    if (stored) {
                        return JSON.parse(stored);
                    }
                } catch (error) {
                    console.error('加载用户偏好失败:', error);
                }
                
                return this.getDefaultPreferences();
            }
            
            async save(userId, preferences) {
                try {
                    localStorage.setItem(`iap_notification_preferences_${userId}`, 
                        JSON.stringify(preferences));
                } catch (error) {
                    console.error('保存用户偏好失败:', error);
                }
            }
            
            getDefaultPreferences() {
                return {
                    enableNotifications: true,
                    enableSound: true,
                    enableDesktop: true,
                    soundVolume: 0.5,
                    notificationTypes: {
                        'info': true,
                        'success': true,
                        'warning': true,
                        'danger': true
                    },
                    doNotDisturbHours: null
                };
            }
        }
        
        // 通知分析器
        class NotificationAnalyzer {
            constructor() {
                this.analytics = {
                    totalNotifications: 0,
                    notificationsByType: {},
                    notificationsByService: {},
                    creditErrors: 0,
                    averageResponseTime: 0
                };
            }
            
            analyze(notification) {
                this.analytics.totalNotifications++;
                
                // 按类型统计
                this.analytics.notificationsByType[notification.type] = 
                    (this.analytics.notificationsByType[notification.type] || 0) + 1;
                
                // 按服务统计
                if (notification.service_name) {
                    this.analytics.notificationsByService[notification.service_name] = 
                        (this.analytics.notificationsByService[notification.service_name] || 0) + 1;
                }
            }
            
            recordCreditError(params) {
                this.analytics.creditErrors++;
            }
            
            getAnalytics() {
                return this.analytics;
            }
        }
        
        // 通知模板管理器
        class NotificationTemplateManager {
            constructor() {
                this.templates = new Map();
            }
            
            async loadTemplates() {
                // 加载自定义模板
                return this.templates;
            }
            
            render(template, params) {
                // 渲染模板
                return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
                    return params[key] || match;
                });
            }
        }
        
        // 通知队列管理器
        class NotificationQueueManager {
            constructor() {
                this.queue = [];
                this.isProcessing = false;
            }
            
            add(notification) {
                this.queue.push(notification);
                this.processQueue();
            }
            
            async processQueue() {
                if (this.isProcessing || this.queue.length === 0) {
                    return;
                }
                
                this.isProcessing = true;
                
                while (this.queue.length > 0) {
                    const notification = this.queue.shift();
                    await this.processNotification(notification);
                    await this.delay(500); // 避免通知过于频繁
                }
                
                this.isProcessing = false;
            }
            
            async processNotification(notification) {
                // 处理单个通知
                console.log('处理队列通知:', notification);
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // 注册增强的服务
        registry.category("services").add("enhancedIapNotification", enhancedIapNotificationService);
        
        // 导出增强的服务
        __exports.enhancedIapNotificationService = enhancedIapNotificationService;
    }
};

// 应用IAP通知服务增强
IAPNotificationServiceEnhancer.enhanceIAPNotificationService();
```

## 技术特点

### 1. 服务架构
- 基于Odoo标准服务架构
- 清晰的依赖声明和注入
- 模块化的功能设计

### 2. 消息处理
- 基于总线服务的消息订阅
- 类型驱动的消息分发
- 异步的消息处理机制

### 3. 通知显示
- 支持多种通知类型
- 富文本内容支持
- 国际化的文本处理

### 4. 错误处理
- 专门的信用错误处理
- 用户友好的错误提示
- 直接的购买引导链接

## 设计模式

### 1. 服务模式 (Service Pattern)
- 标准的Odoo服务结构
- 依赖注入和服务发现

### 2. 观察者模式 (Observer Pattern)
- 基于总线的消息订阅
- 事件驱动的通知处理

### 3. 策略模式 (Strategy Pattern)
- 不同类型通知的处理策略
- 可配置的通知行为

## 注意事项

1. **消息安全**: 确保通知消息的安全性和有效性
2. **用户体验**: 提供清晰的通知内容和操作指引
3. **性能优化**: 避免过多的通知影响用户体验
4. **国际化**: 确保所有文本内容的国际化支持

## 扩展建议

1. **通知历史**: 记录和管理通知历史
2. **用户偏好**: 支持用户自定义通知偏好
3. **通知分析**: 分析通知效果和用户行为
4. **模板系统**: 支持自定义通知模板
5. **批量处理**: 支持通知的批量处理和队列管理

该IAP通知服务为IAP Mail系统提供了重要的通知管理功能，是IAP邮件服务用户体验的核心组件。
